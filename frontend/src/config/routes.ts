import React from 'react';

// 懒加载组件
const Dashboard = React.lazy(() => import('../pages/dashboard/Dashboard'));
const UserPage = React.lazy(() => import('../pages/user/UserPage'));
const DepartmentPage = React.lazy(() => import('../pages/department/DepartmentPage'));
const PositionPage = React.lazy(() => import('../pages/position/PositionPage'));
const RolePage = React.lazy(() => import('../pages/role/RolePage'));
const PermissionPage = React.lazy(() => import('../pages/permission/PermissionPage'));
const PermissionGroupPage = React.lazy(() => import('../pages/permission-group/PermissionGroupPage'));
const ApplicationPage = React.lazy(() => import('../pages/application/ApplicationPage'));

const TemplateList = React.lazy(() => import('../pages/email/TemplateList'));
const TemplateCreate = React.lazy(() => import('../pages/email/TemplateCreate'));
const TemplateEdit = React.lazy(() => import('../pages/email/TemplateEdit'));
const TemplateDetail = React.lazy(() => import('../pages/email/TemplateDetail'));

const IdGeneratorPage = React.lazy(() => import('../pages/idgenerator/SequenceList'));
const ResourcePage = React.lazy(() => import('../pages/resource/ResourcePage'));
const ApiManagementPage = React.lazy(() => import('../pages/api-management/ApiManagementPage'));
const TenantPage = React.lazy(() => import('../pages/tenant/TenantPage'));
const AccountManagement = React.lazy(() => import('../pages/email/AccountManagement'));
const VerificationConfigManagement = React.lazy(() => import('../pages/verification/VerificationConfigManagement'));
const AppConfigPage = React.lazy(() => import('../pages/application/AppConfigPage'));

// Email System 相关页面
const ContactManagement = React.lazy(() => import('../pages/email-system/ContactManagement'));
const TagManagement = React.lazy(() => import('../pages/email-system/TagManagement'));

// 文件系统相关页面
const SceneList = React.lazy(() => import('../pages/file-system/config/SceneList'));
const FileList = React.lazy(() => import('../pages/file-system/record/FileList'));
const FileDetail = React.lazy(() => import('../pages/file-system/record/FileDetail'));
const FileUploadPage = React.lazy(() => import('../pages/file-system/upload/FileUploadPage'));


// 路由配置接口
export interface RouteConfig {
    key: string;
    path: string;
    component: React.LazyExoticComponent<React.ComponentType<any>>;
    title: string;
    icon?: React.ReactNode;
    children?: RouteConfig[];
    hidden?: boolean; // 是否在菜单中隐藏
    exact?: boolean;
}

// 菜单项配置接口
export interface MenuConfig {
    key: string;
    label: string;
    icon?: React.ReactNode;
    path?: string;
    children?: MenuConfig[];
    type?: 'divider';
}

// 路由配置
export const routeConfigs: RouteConfig[] = [
    {
        key: 'dashboard',
        path: '/dashboard',
        component: Dashboard,
        title: '仪表盘',
    },
    {
        key: 'user',
        path: '/user',
        component: UserPage,
        title: '用户管理',
    },
    {
        key: 'department',
        path: '/department',
        component: DepartmentPage,
        title: '组织架构',
    },
    {
        key: 'position',
        path: '/position',
        component: PositionPage,
        title: '职位管理',
    },
    {
        key: 'role',
        path: '/role',
        component: RolePage,
        title: '角色管理',
    },
    {
        key: 'permission',
        path: '/permission',
        component: PermissionPage,
        title: '权限管理',
    },
    {
        key: 'permission-group',
        path: '/permission-group',
        component: PermissionGroupPage,
        title: '权限组管理',
    },
    {
        key: 'application',
        path: '/application',
        component: ApplicationPage,
        title: '应用管理',
    },
    {
        key: 'resource',
        path: '/resource',
        component: ResourcePage,
        title: '资源管理',
    },
    {
        key: 'tenant',
        path: '/tenant',
        component: TenantPage,
        title: '租户管理',
    },
    // 新：应用配置页面（按internalAppId）
    {
        key: 'app-config',
        path: '/application/:internalAppId/config',
        component: AppConfigPage,
        title: '应用配置',
        hidden: true,
    },
    {
        key: 'file-system',
        path: '/file-system',
        component: FileList, // 默认显示文件列表
        title: '文件系统',
        children: [
            {
                key: 'file-system/files',
                path: '/file-system/files',
                component: FileList,
                title: '文件管理',
            },
            {
                key: 'file-system/files/:id',
                path: '/file-system/files/:id',
                component: FileDetail,
                title: '文件详情',
                hidden: true,
            },
            {
                key: 'file-system/scenes',
                path: '/file-system/scenes',
                component: SceneList,
                title: '场景配置',
            },
            {
                key: 'file-system/upload',
                path: '/file-system/upload',
                component: FileUploadPage,
                title: '文件上传',
            },
        ],
    },
    {
        key: 'email',
        path: '/email',
        component: AccountManagement,
        title: '邮箱管理',
        children: [
            {
                key: 'email/accounts',
                path: '/email/accounts',
                component: AccountManagement,
                title: '账户管理',
            },
            {
                key: 'email/templates',
                path: '/email/templates',
                component: TemplateList,
                title: '模板列表',
                hidden: true,
            },
            {
                key: 'email/templates/create',
                path: '/email/templates/create',
                component: TemplateCreate,
                title: '创建模板',
                hidden: true,
            },
            {
                key: 'email/templates/edit',
                path: '/email/templates/:id/edit',
                component: TemplateEdit,
                title: '编辑模板',
                hidden: true,
            },
            {
                key: 'email/templates/detail',
                path: '/email/templates/:id',
                component: TemplateDetail,
                title: '模板详情',
                hidden: true,
            },

        ],
    },
    // Email System 路由
    {
        key: 'email-system',
        path: '/email-system',
        component: ContactManagement,
        title: 'Email营销系统',
        children: [
            {
                key: 'email-system/contacts',
                path: '/email-system/contacts',
                component: ContactManagement,
                title: '联系人管理',
            },
            {
                key: 'email-system/tags',
                path: '/email-system/tags',
                component: TagManagement,
                title: '标签管理',
            },
        ],
    },
    {
        key: 'idgenerator',
        path: '/idgenerator/sequences',
        component: IdGeneratorPage,
        title: 'ID生成器',
    },
    {
        key: 'verification',
        path: '/verification',
        title: '验证配置',
        component: VerificationConfigManagement,
        children: [
            {
                key: "verification/configs",
                title: "验证配置管理",
                path: "/verification/configs",
                component: VerificationConfigManagement
            }
        ]
    },
    {
        key: 'api-management',
        path: '/api-management',
        component: ApiManagementPage,
        title: 'API管理',
    },
];

// 菜单配置 - 基于路由配置生成
export const generateMenuConfig = (routes: RouteConfig[]): MenuConfig[] => {
    return [
        {
            key: 'dashboard',
            label: '仪表盘',
            path: '/dashboard',
        },
        {type: 'divider'} as MenuConfig,
        {
            key: 'user',
            label: '用户管理',
            path: '/user',
        },
        {
            key: 'department',
            label: '组织架构',
            path: '/department',
        },
        {
            key: 'position',
            label: '职位管理',
            path: '/position',
        },
        {type: 'divider'} as MenuConfig,
        {
            key: 'role',
            label: '角色管理',
            path: '/role',
        },
        {
            key: 'permission',
            label: '权限管理',
            path: '/permission',
        },
        {
            key: 'application',
            label: '应用管理',
            path: '/application',
        },
        {
            key: 'resource',
            label: '资源管理',
            path: '/resource',
        },
        {
            key: 'tenant',
            label: '租户管理',
            path: '/tenant',
        },
        {type: 'divider'} as MenuConfig,
        {
            key: 'file-system',
            label: '文件系统',
            children: [
                {
                    key: 'file-system/files',
                    label: '文件管理',
                    path: '/file-system/files',
                },
                {
                    key: 'file-system/scenes',
                    label: '场景配置',
                    path: '/file-system/scenes',
                },
                {
                    key: 'file-system/upload',
                    label: '文件上传',
                    path: '/file-system/upload',
                },
            ],
        },
        {
            key: 'email',
            label: '邮箱管理',
            path: '/email',
        },
        {
            key: 'email-templates',
            label: '邮件模板',
            path: '/email/templates',
        },
        {
            key: 'email-system',
            label: 'Email营销系统',
            children: [
                {
                    key: 'email-system/contacts',
                    label: '联系人管理',
                    path: '/email-system/contacts',
                },
                {
                    key: 'email-system/tags',
                    label: '标签管理',
                    path: '/email-system/tags',
                },
            ],
        },
        {type: 'divider'} as MenuConfig,
        {
            key: 'idgenerator',
            label: 'ID生成器',
            path: '/idgenerator/sequences',
        },
        {
            key: 'verification',
            label: '验证策略管理',
            path: '/verification',
        },
        {
            key: 'api-management',
            label: 'API管理',
            path: '/api-management',
        },
    ];
};

// 获取所有路由（包括子路由）
export const getAllRoutes = (routes: RouteConfig[]): RouteConfig[] => {
    const allRoutes: RouteConfig[] = [];

    const traverse = (routeList: RouteConfig[]) => {
        routeList.forEach(route => {
            allRoutes.push(route);
            if (route.children) {
                traverse(route.children);
            }
        });
    };

    traverse(routes);
    return allRoutes;
};

// 根据路径查找路由配置
export const findRouteByPath = (path: string, routes: RouteConfig[] = routeConfigs): RouteConfig | null => {
    const allRoutes = getAllRoutes(routes);
    return allRoutes.find(route => route.path === path) || null;
};

// 根据key查找路由配置
export const findRouteByKey = (key: string, routes: RouteConfig[] = routeConfigs): RouteConfig | null => {
    const allRoutes = getAllRoutes(routes);
    return allRoutes.find(route => route.key === key) || null;
};
