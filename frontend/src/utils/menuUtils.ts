import React from 'react';
import type { MenuProps } from 'antd';
import { Link } from 'react-router-dom';
import { AsyncIcon } from './iconLoader';
import type { MenuTreeNode } from '../types/api';

// 原始菜单项类型定义
export interface MenuItem {
  key: string;
  label?: string;
  path?: string;
  icon?: string;
  type: 'menu' | 'submenu' | 'divider';
  children?: MenuItem[];
}

// 将 icon 字符串转换为异步图标元素
const transformIcon = (iconName?: string): React.ReactNode => {
  if (!iconName) return undefined;
  
  return React.createElement(AsyncIcon, {
    iconName,
    fallback: null,
  });
};

// 创建带有 Link 的标签 - 优化：确保路径正确
const createLinkLabel = (label: string | undefined, path: string | undefined): React.ReactNode => {
  if (!label) return undefined;
  if (!path) return label;

  return React.createElement(Link, { 
    to: path, 
    style: { 
      color: 'inherit', 
      textDecoration: 'none',
      display: 'block',
      width: '100%'
    } 
  }, label);
};

// 递归转换菜单数据为 Ant Design 菜单项 - 优化转换逻辑
export const transformMenuData = (menuItems: MenuItem[]): MenuProps['items'] => {
  return menuItems.map(item => {
    if (item.type === 'divider') {
      return {
        type: 'divider' as const,
        key: item.key,
      };
    }

    if (item.type === 'submenu') {
      return {
        key: item.key,
        label: item.label, // 子菜单标题不需要 Link
        icon: transformIcon(item.icon),
        children: item.children ? transformMenuData(item.children) : undefined,
      };
    }

    // 默认为 menu 类型 - 优化：确保路径正确传递
    return {
      key: item.key,
      label: createLinkLabel(item.label, item.path),
      icon: transformIcon(item.icon),
      // 添加路径信息，便于调试
      ...(item.path && { 'data-path': item.path }),
    };
  });
};

// 测试函数 - 验证转换是否正确
export const testMenuTransformation = () => {
  const testData: MenuItem[] = [
    {
      key: 'dashboard',
      label: '仪表盘',
      path: '/dashboard',
      icon: 'DashboardOutlined',
      type: 'menu'
    },
    {
      key: 'divider-1',
      type: 'divider'
    },
    {
      key: 'user-management',
      label: '用户管理',
      icon: 'TeamOutlined',
      type: 'submenu',
      children: [
        {
          key: 'user',
          label: '用户列表',
          path: '/user',
          icon: 'UserOutlined',
          type: 'menu'
        }
      ]
    }
  ];

  const transformed = transformMenuData(testData);
  return transformed;
};

// 新增：路径匹配工具函数
export const matchPath = (currentPath: string, menuPath: string): boolean => {
  if (currentPath === menuPath) return true;
  
  // 处理动态路由参数
  const currentSegments = currentPath.split('/').filter(Boolean);
  const menuSegments = menuPath.split('/').filter(Boolean);
  
  if (currentSegments.length !== menuSegments.length) return false;
  
  return menuSegments.every((segment, index) => {
    return segment.startsWith(':') || segment === currentSegments[index];
  });
};

// 新增：查找菜单项的工具函数
export const findMenuItemByPath = (path: string, items: MenuItem[]): MenuItem | null => {
  for (const item of items) {
    if (item.path && matchPath(path, item.path)) {
      return item;
    }
    if (item.children) {
      const found = findMenuItemByPath(path, item.children);
      if (found) return found;
    }
  }
  return null;
};

// 新增：将API返回的MenuTreeNode转换为Ant Design菜单项
export const convertMenuTreeToAntdMenuItems = (menuTree: MenuTreeNode[]): MenuProps['items'] => {
 
  if (!Array.isArray(menuTree) || menuTree.length === 0) {
   
    return [];
  }
  
  const result = menuTree
    .filter(node => {
      // 过滤掉无效的节点
      if (!node || typeof node !== 'object') {
        console.warn('Invalid menu node:', node);
        return false;
      }
      // 默认显示，除非明确设置为false
      return node.is_visible !== false;
    })
    .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0)) // 按sort_order排序，提供默认值
    .map(node => convertMenuNodeToAntdItem(node))
    .filter(Boolean); // 过滤掉转换失败的项

  return result;
};

// 递归转换单个菜单节点
const convertMenuNodeToAntdItem = (node: MenuTreeNode): any => {
  try {
    if (!node || typeof node !== 'object') {
      console.warn('Invalid node in convertMenuNodeToAntdItem:', node);
      return null;
    }
    
    const hasChildren = node.children && Array.isArray(node.children) && node.children.length > 0;
    
    // 创建菜单项
    const item: any = {
      key: node.name || `menu-${node.id}`, // 使用name作为key，如果没有name则使用id
      label: hasChildren ? node.display_name : createLinkLabel(node.display_name, node.path),
      icon: transformIcon(node.icon),
      ...(node.path && { 'data-path': node.path }),
    };

    // 如果有子菜单，递归处理
    if (hasChildren) {
      const validChildren = node.children!
        .filter(child => child && typeof child === 'object' && child.is_visible !== false)
        .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0))
        .map(child => convertMenuNodeToAntdItem(child))
        .filter(Boolean); // 过滤掉转换失败的子项
      
      if (validChildren.length > 0) {
        item.children = validChildren;
      }
    }    
    return item;
  } catch (error) {
    console.error('Error converting menu node:', error, 'Node:', node);
    return null;
  }
}; 