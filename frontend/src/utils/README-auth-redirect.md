# 统一登录跳转系统

这个系统提供了统一的登录跳转逻辑，避免在各个组件中重复处理认证相关的路由跳转。

## ⚠️ 重要更新：正确区分401和403错误

系统现在正确区分了401（未授权）和403（权限不足）错误的处理逻辑：

### 🔐 401 未授权错误
- **场景**：用户未登录或Token过期
- **处理方式**：清除认证信息并跳转到登录页面
- **用户体验**：需要重新登录以获得访问权限

### 🚫 403 权限不足错误  
- **场景**：用户已登录但权限不足
- **处理方式**：只显示错误提示，不跳转到登录页面
- **用户体验**：知道自己已登录，但需要联系管理员获得更高权限

## 核心文件

- `src/utils/auth.ts` - 核心登录跳转工具类
- `src/hooks/useAuthRedirect.ts` - React Hook封装
- `src/contexts/AuthContext.tsx` - 已集成统一登录跳转
- `src/components/AuthRedirectExample.tsx` - 使用示例

## 主要功能

### 1. 统一的跳转逻辑
- 自动保存当前页面地址，登录后返回
- 避免登录页面循环重定向
- 支持自定义跳转参数和状态

### 2. 多种认证场景支持
- **未授权访问 (401)**: `handleUnauthorized()` - 跳转到登录页
- **权限不足 (403)**: `handleForbidden()` - 只显示错误提示，不跳转
- **Token过期**: `handleTokenExpired()` - 跳转到登录页
- **要求登录**: `requireLogin()` - 跳转到登录页
- **主动退出**: `handleLogout()` - 跳转到登录页
- **强制重登录**: `forceRelogin()` - 跳转到登录页

### 3. React集成
- 提供便捷的Hook: `useAuthRedirect()`
- 自动集成到AuthContext中
- 支持React Router导航

## 使用方法

### 在组件中使用Hook

```tsx
import { useAuthRedirect } from '../hooks/useAuthRedirect';

export const MyComponent: React.FC = () => {
  const {
    onUnauthorized,    // 401: 跳转登录页
    onForbidden,       // 403: 只显示提示，不跳转
    onTokenExpired,
    onRequireLogin,
    onLogout,
    redirectToLogin
  } = useAuthRedirect();

  const handleProtectedAction = async () => {
    try {
      // 调用需要权限的API
      await protectedApiCall();
    } catch (error: any) {
      if (error.response?.status === 401) {
        // 未授权，需要重新登录
        onUnauthorized({ message: '登录已过期，请重新登录' });
      } else if (error.response?.status === 403) {
        // 权限不足，不跳转登录页
        onForbidden({ 
          message: '您没有权限执行此操作，请联系管理员',
          error: error 
        });
      }
    }
  };

  const handleLoginRequired = () => {
    // 检查是否需要登录
    if (!isAuthenticated) {
      onRequireLogin({ message: '请先登录以访问此功能' });
      return;
    }
    
    // 执行需要登录的操作
    performAction();
  };

  return (
    <div>
      <Button onClick={handleLoginRequired}>
        需要登录的功能
      </Button>
      <Button onClick={handleProtectedAction}>
        需要特定权限的功能
      </Button>
    </div>
  );
};
```

### 在API错误处理中使用

```tsx
// HTTP拦截器中的错误处理（已自动集成）
// 401错误会自动调用 globalUnauthorizedHandler
// 403错误会自动调用 globalForbiddenHandler

// 在业务代码中手动处理特定错误
try {
  await apiCall();
} catch (error: any) {
  if (error.response?.status === 401) {
    // 自动被拦截器处理，也可手动调用
    onUnauthorized(); 
  } else if (error.response?.status === 403) {
    // 自动被拦截器处理，也可手动调用
    onForbidden({ 
      message: '自定义权限不足提示',
      error: error 
    });
  }
}
```

### 直接使用工具类

```tsx
import { 
  LoginRedirect, 
  handleUnauthorized, 
  handleForbidden,
  requireLogin 
} from '../utils/auth';

// 直接跳转到登录页
LoginRedirect.redirect({
  returnUrl: '/dashboard',
  message: '请重新登录',
  reason: 'expired'
});

// 便捷方法
handleUnauthorized();  // 401: 跳转登录页
handleForbidden({ message: '权限不足' }); // 403: 只提示
requireLogin({ message: '需要登录权限' }); // 要求登录
```

## API参考

### LoginRedirectOptions

```typescript
interface LoginRedirectOptions {
  returnUrl?: string;      // 登录成功后的回跳地址
  replace?: boolean;       // 是否替换当前历史记录(默认true)
  state?: any;            // 传递给登录页面的额外状态
  clearAuth?: boolean;     // 是否清除当前认证状态(默认true)
  message?: string;        // 提示消息
  reason?: 'unauthorized' | 'expired' | 'logout' | 'required' | 'force';
}
```

### ForbiddenOptions (新增)

```typescript
interface ForbiddenOptions {
  message?: string;        // 提示消息
  error?: any;            // 错误详情
  showDetails?: boolean;   // 是否显示详细错误信息
}
```

### useAuthRedirect Hook方法

```typescript
const {
  // 通用方法
  redirectToLogin,
  
  // 具体场景方法  
  onUnauthorized,     // 401: 处理未授权，跳转登录页
  onForbidden,        // 403: 处理权限不足，只提示不跳转
  onTokenExpired,     // Token过期，跳转登录页
  onRequireLogin,     // 要求登录，跳转登录页
  onLogout,           // 退出登录，跳转登录页
  onForceRelogin,     // 强制重新登录，跳转登录页
  
  // 工具方法
  shouldRedirectToLogin, // 检查是否应该跳转到登录页
} = useAuthRedirect();
```

## 迁移指南

### 从原有方式迁移

#### 旧方式 ❌
```tsx
const navigate = useNavigate();
const location = useLocation();

const handleUnauthorized = () => {
  clearAuth();
  navigate('/login', {
    replace: true,
    state: { from: location }
  });
};
```

#### 新方式 ✅
```tsx
const { onUnauthorized } = useAuthRedirect();

// 直接调用即可，所有逻辑都已封装
onUnauthorized();
```

### 替换现有组件中的登录跳转

1. 导入Hook: `import { useAuthRedirect } from '../hooks/useAuthRedirect';`
2. 使用Hook: `const { onRequireLogin } = useAuthRedirect();`
3. 替换原有逻辑: `onRequireLogin({ message: '自定义提示' });`

## 特性

### ✅ 已实现
- 统一的跳转逻辑
- 多种认证场景支持
- React Hook封装
- AuthContext集成
- 自动保存返回地址
- 防止循环重定向
- 全局未授权处理

### 🔄 自动化
- API拦截器自动调用
- AuthContext自动初始化
- 错误处理自动集成

### 📱 兼容性
- 支持React Router v6
- 支持多窗口同步
- 降级到window.location

## 最佳实践

1. **在组件中优先使用Hook**: `useAuthRedirect()`提供了最完整的功能
2. **为不同场景使用合适的方法**: 区分401错误和主动要求登录
3. **提供用户友好的消息**: 使用message参数告诉用户为什么需要登录
4. **避免直接操作路由**: 让统一系统处理所有跳转逻辑

## 示例

查看 `src/components/AuthRedirectExample.tsx` 获取完整的使用示例。