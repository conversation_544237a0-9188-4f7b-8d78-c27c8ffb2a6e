/**
 * 统一的登录跳转和认证处理工具
 * 提供统一的登录跳转逻辑，避免在各个组件中重复处理
 */

import { NavigateFunction } from 'react-router-dom';
import { message } from 'antd';

// 登录跳转选项
export interface LoginRedirectOptions {
  /** 登录成功后的回跳地址 */
  returnUrl?: string;
  /** 是否替换当前历史记录 */
  replace?: boolean;
  /** 传递给登录页面的额外状态 */
  state?: any;
  /** 是否清除当前认证状态 */
  clearAuth?: boolean;
  /** 提示消息 */
  message?: string;
  /** 跳转原因 */
  reason?: 'unauthorized' | 'expired' | 'logout' | 'required' | 'force';
}

// 权限不足处理选项
export interface ForbiddenOptions {
  /** 提示消息 */
  message?: string;
  /** 错误详情 */
  error?: any;
  /** 是否显示详细错误信息 */
  showDetails?: boolean;
}

// 登录跳转原因的默认消息
const REASON_MESSAGES = {
  unauthorized: '登录已过期，请重新登录',
  expired: 'Token已过期，请重新登录',
  logout: '已退出登录',
  required: '请先登录',
  force: '需要重新登录',
} as const;

/**
 * 统一的登录跳转类
 */
export class LoginRedirect {
  private static navigate: NavigateFunction | null = null;
  private static clearAuthCallback: (() => void) | null = null;

  /**
   * 设置导航函数
   * 通常在App组件或路由组件中调用
   */
  static setNavigate(navigate: NavigateFunction) {
    this.navigate = navigate;
  }

  /**
   * 设置清除认证的回调函数
   */
  static setClearAuthCallback(callback: () => void) {
    this.clearAuthCallback = callback;
  }

  /**
   * 执行登录跳转
   */
  static redirect(options: LoginRedirectOptions = {}) {
    if (!this.navigate) {
      console.error('LoginRedirect: Navigate function not set. Call LoginRedirect.setNavigate() first.');
      // 降级处理：直接使用window.location
      this.fallbackRedirect(options);
      return;
    }

    const {
      returnUrl,
      replace = true,
      state,
      clearAuth = true,
      message,
      reason = 'required'
    } = options;

    // 清除认证状态
    if (clearAuth && this.clearAuthCallback) {
      this.clearAuthCallback();
    }

    // 确定返回地址
    const finalReturnUrl = returnUrl || this.getCurrentPath();
    
    // 构建跳转状态
    const navigationState = {
      ...state,
      from: finalReturnUrl !== '/login' ? { pathname: finalReturnUrl } : undefined,
      message: message || REASON_MESSAGES[reason],
      reason,
    };

    // 执行跳转
    this.navigate('/login', {
      replace,
      state: navigationState,
    });

    console.log(`LoginRedirect: Redirected to login, reason: ${reason}, returnUrl: ${finalReturnUrl}`);
  }

  /**
   * 处理权限不足错误（403）
   * 不跳转到登录页，只做提示处理
   */
  static handleForbidden(options: ForbiddenOptions = {}) {
    const {
      message: customMessage,
      error,
      showDetails = false
    } = options;

    const defaultMessage = '权限不足，无法访问该资源';
    const displayMessage = customMessage || defaultMessage;

    // 显示错误提示
    message.error(displayMessage);

    // 记录详细错误信息到控制台
    if (error) {
      console.warn('Access forbidden (403):', {
        message: displayMessage,
        error: error,
        timestamp: new Date().toISOString(),
        url: window.location.href
      });

      // 如果需要显示详细信息，可以在这里添加更多处理
      if (showDetails && error.response?.data) {
        console.warn('Error details:', error.response.data);
      }
    }
  }

  /**
   * 降级处理：当React Router不可用时使用
   */
  private static fallbackRedirect(options: LoginRedirectOptions) {
    const { clearAuth = true } = options;
    
    if (clearAuth && this.clearAuthCallback) {
      this.clearAuthCallback();
    }

    const currentPath = window.location.pathname + window.location.search + window.location.hash;
    const returnUrl = options.returnUrl || currentPath;
    
    // 构建登录URL，包含返回地址
    const loginUrl = returnUrl !== '/login' 
      ? `/login?returnUrl=${encodeURIComponent(returnUrl)}`
      : '/login';
    
    window.location.href = loginUrl;
  }

  /**
   * 获取当前路径
   */
  private static getCurrentPath(): string {
    if (typeof window === 'undefined') {
      return '/';
    }
    return window.location.pathname + window.location.search + window.location.hash;
  }

  /**
   * 检查是否需要跳转到登录页面
   */
  static shouldRedirectToLogin(currentPath: string): boolean {
    // 公开路径，不需要登录
    const publicPaths = ['/login', '/register', '/forgot-password', '/reset-password'];
    return !publicPaths.includes(currentPath);
  }
}

/**
 * 便捷方法：处理未授权错误（401）
 */
export const handleUnauthorized = (options?: Omit<LoginRedirectOptions, 'reason'>) => {
  LoginRedirect.redirect({
    ...options,
    reason: 'unauthorized',
    clearAuth: true,
  });
};

/**
 * 便捷方法：处理权限不足错误（403）
 * 不跳转到登录页，只显示错误提示
 */
export const handleForbidden = (options?: ForbiddenOptions) => {
  LoginRedirect.handleForbidden(options);
};

/**
 * 便捷方法：处理Token过期
 */
export const handleTokenExpired = (options?: Omit<LoginRedirectOptions, 'reason'>) => {
  LoginRedirect.redirect({
    ...options,
    reason: 'expired',
    clearAuth: true,
  });
};

/**
 * 便捷方法：强制登录
 */
export const requireLogin = (options?: Omit<LoginRedirectOptions, 'reason'>) => {
  LoginRedirect.redirect({
    ...options,
    reason: 'required',
    clearAuth: false,
  });
};

/**
 * 便捷方法：退出登录
 */
export const handleLogout = (options?: Omit<LoginRedirectOptions, 'reason'>) => {
  LoginRedirect.redirect({
    ...options,
    reason: 'logout',
    clearAuth: true,
    returnUrl: '/login', // 退出登录后直接到登录页，不保存返回地址
  });
};

/**
 * 便捷方法：强制重新登录
 */
export const forceRelogin = (options?: Omit<LoginRedirectOptions, 'reason'>) => {
  LoginRedirect.redirect({
    ...options,
    reason: 'force',
    clearAuth: true,
  });
};

export default LoginRedirect;