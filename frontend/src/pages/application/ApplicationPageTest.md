# 应用管理页面租户选择功能测试

## 功能概述
为应用管理页面添加了租户选择查询条件，允许用户根据租户筛选应用列表。

## 主要修改

### 1. 前端修改
- 添加了租户状态管理：`selectedTenantId`, `tenants`, `loadingTenants`
- 新增 `fetchTenants()` 函数加载租户列表
- 在 `fetchApplications()` 中添加租户ID参数传递
- 在搜索筛选区域添加租户选择器组件
- 在表格中添加租户信息列显示
- 添加重置筛选按钮

### 2. 后端修改
- 修改 `ListApplicationsRequest.TenantID` 验证规则，从 `required,min=1` 改为 `min=0`
- 后端服务层已支持超管根据租户ID查询应用的逻辑

## 测试场景

### 场景1：普通用户
- 租户选择器应该显示所有可用租户
- 选择租户后，只显示该租户下的应用
- 不选择租户时，显示用户自己租户下的应用

### 场景2：超级管理员
- 租户选择器显示所有租户
- 选择租户后，显示该租户下的应用
- 不选择租户时，返回空数据（后端逻辑）

### 场景3：交互体验
- 租户选择器支持搜索功能
- 显示格式：租户名称 (租户代码)
- 支持清除选择
- 重置筛选按钮清除所有筛选条件

## API参数传递
当选择租户时，查询参数包含：
```json
{
  "tenant_id": 123,
  "page": 1,
  "size": 10,
  "keyword": "搜索关键词",
  "status": "active",
  "app_type": "web"
}
```

当未选择租户时，不传递 `tenant_id` 参数。