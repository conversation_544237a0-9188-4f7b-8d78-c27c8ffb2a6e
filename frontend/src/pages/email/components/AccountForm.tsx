import React, { useState, useEffect, useMemo } from 'react';
import {
  Form,
  Input,
  Select,
  Checkbox,
  InputNumber,
  Button,
  Row,
  Col,
  Typography,
  Card,

} from 'antd';
import {
  LockOutlined,
  MailOutlined,
  UserOutlined,
  ApiOutlined,
  GlobalOutlined,
} from '@ant-design/icons';
import {
  EmailAccount,
  EMAIL_ACCOUNT_TYPES,
  EMAIL_PROVIDERS,
  ALIYUN_REGIONS,
} from '../../../types/email';
import { useTheme } from '../../../contexts/ThemeContext';
import { emailAccountService } from '../../../services/email';
import { SUCCESS } from '../../../constants/errorCodes';
  import { showAPIError } from '../../../utils/errorHandler';

// 提供商选项类型（可选图标与预设）
type ProviderOption = {
  value: string;
  label: string;
  icon_url?: string;
  smtp_host?: string;
  smtp_port?: number;
  use_ssl?: boolean;
};

const { Option } = Select;
const { Title, Text } = Typography;

interface AccountFormProps {
  initialValues?: EmailAccount;
  onFinish: (values: any) => void;
  loading?: boolean;
}

/**
 * 邮件账户表单组件
 * 用于创建和编辑邮件账户
 */
const AccountForm: React.FC<AccountFormProps> = ({
  initialValues,
  onFinish,
  loading = false,
}) => {
  const [form] = Form.useForm();
  const { isDarkMode } = useTheme();
  const autoDecision = Form.useWatch(['config', 'auto_decision'], form);
  const [accountType, setAccountType] = useState<number>(
    initialValues?.type || EMAIL_ACCOUNT_TYPES.SMTP
  );
  const [provider, setProvider] = useState<string>(
    initialValues?.provider || EMAIL_PROVIDERS.GMAIL
  );
  const [providers, setProviders] = useState<any[]>([]);
  const isEdit = !!initialValues;
  const [step, setStep] = useState<number>(isEdit ? 2 : 1);
  

  // 当初始值变化时更新表单
  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue({
        ...initialValues,
        // 确保阿里云配置正确设置
        config: initialValues.config || {},
      });
      setAccountType(initialValues.type);
      setProvider(initialValues.provider);
    }
  }, [initialValues, form]);

  // 拉取服务商列表（用于快捷填充）
  useEffect(() => {
    const loadProviders = async () => {
      try {
        const res = await emailAccountService.getProviders();
        if (res && res.code === SUCCESS && Array.isArray(res.data)) {
          setProviders(res.data);
        } else {
          setProviders([]);
        }
      } catch (error) {
        // 服务端不可用时，不让页面崩溃，提示并回退为空列表（仍可选择“自定义”）
        showAPIError('无法获取邮件服务商列表，请手动选择“自定义”');
        setProviders([]);
      }
    };
    loadProviders();
  }, []);

  // 当启用“系统自动决策”时，清空并禁用固定间隔
  useEffect(() => {
    if (autoDecision) {
      const currentConfig = form.getFieldValue('config') || {};
      form.setFieldsValue({
        config: {
          ...currentConfig,
          send_interval_seconds: undefined,
        },
      });
    }
  }, [autoDecision, form]);

  const providerOptions: ProviderOption[] = useMemo(() => {
    const base: ProviderOption[] = providers.map((p: any) => ({
      value: p.name,
      label: p.display_name || p.name,
      icon_url: p.icon_url,
      smtp_host: p.smtp_host,
      smtp_port: p.smtp_port,
      use_ssl: p.use_ssl,
    }));
    // 追加“自定义”
    return [...base, { value: EMAIL_PROVIDERS.CUSTOM, label: '自定义' }];
  }, [providers]);

  // 处理账户类型变化
  const handleTypeChange = (value: number) => {
    setAccountType(value);
    // 根据类型设置默认提供商
    if (value === EMAIL_ACCOUNT_TYPES.API) {
      setProvider(EMAIL_PROVIDERS.ALIYUN);
      form.setFieldsValue({ provider: EMAIL_PROVIDERS.ALIYUN });
    } else if (provider === EMAIL_PROVIDERS.ALIYUN) {
      setProvider(EMAIL_PROVIDERS.GMAIL);
      form.setFieldsValue({ provider: EMAIL_PROVIDERS.GMAIL });
    }
  };

  // 处理提供商变化
  const handleProviderChange = (value: string) => {
    setProvider(value);
    const found = providerOptions.find(p => p.value === value);
    if (found && value !== EMAIL_PROVIDERS.CUSTOM) {
      // 自动填充 SMTP 预设
      form.setFieldsValue({
        host: found.smtp_host,
        port: found.smtp_port,
        is_ssl: !!found.use_ssl,
        provider: value,
      });
    }
  };

  // 选择服务商（第1步）
  const handleSelectProvider = (opt: ProviderOption) => {
    const selectedType = opt.value === 'aliyun' ? EMAIL_ACCOUNT_TYPES.API : EMAIL_ACCOUNT_TYPES.SMTP;
    setAccountType(selectedType);
    setProvider(opt.value);
    form.setFieldsValue({
      type: selectedType,
      provider: opt.value,
      host: opt.smtp_host,
      port: opt.smtp_port,
      is_ssl: opt.use_ssl ?? true,
    });
    setStep(2);
  };

  // 根据账户类型渲染不同的配置表单
  const renderConfigFields = () => {
    if (accountType === EMAIL_ACCOUNT_TYPES.API && provider === EMAIL_PROVIDERS.ALIYUN) {
      return (
        <Card
          title="阿里云配置"
          className="rounded-md shadow-sm"
          size="small"
          bordered={false}
          style={{ marginBottom: 24, background: isDarkMode ? '#1f1f1f' : '#f9f9f9' }}
        >
          <Row gutter={16}>
            <Col xs={24} md={12}>
              <Form.Item
                name={['config', 'access_key_id']}
                label="AccessKey ID"
                rules={[{ required: true, message: '请输入AccessKey ID' }]}
                extra="从阿里云控制台 > 访问密钥管理获取，用于鉴权。示例：LTAI5t************"
              >
                <Input
                  placeholder="请输入阿里云AccessKey ID"
                  prefix={<ApiOutlined />}
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name={['config', 'access_key_secret']}
                label="AccessKey Secret"
                rules={[{ required: true, message: '请输入AccessKey Secret' }]}
                extra="与 AccessKey ID 配对使用，妥善保管，不要泄露。"
              >
                <Input.Password
                  placeholder="请输入阿里云AccessKey Secret"
                  prefix={<LockOutlined />}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col xs={24} md={12}>
              <Form.Item
                name={['config', 'region']}
                label="地域"
                rules={[{ required: true, message: '请选择地域' }]}
                extra="阿里云服务所在地域，示例：cn-hangzhou。"
              >
                <Select placeholder="请选择阿里云地域" showSearch>
                  {ALIYUN_REGIONS.map((region) => (
                    <Option key={region.value} value={region.value}>
                      {region.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name={['config', 'domain']}
                label="发信地址"
                rules={[{ required: true, message: '请输入发信地址' }]}
                tooltip="阿里云邮件推送服务中配置的发信地址"
                extra="阿里云邮件推送的发信域名/地址，例如：mailservice.example.com。"
              >
                <Input
                  placeholder="例如: your-domain.com"
                  prefix={<GlobalOutlined />}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      );
    }

    return (
      <Card
        title="服务器配置"
        className="rounded-md shadow-sm"
        size="small"
        bordered={false}
        style={{ marginBottom: 24, background: isDarkMode ? '#1f1f1f' : '#f9f9f9' }}
      >
        <Row gutter={16}>
          <Col xs={24} md={12}>
            <Form.Item
              name="host"
              label="SMTP服务器"
              rules={[{ required: true, message: '请输入SMTP服务器地址' }]}
              extra="发送邮件使用的SMTP服务器域名。示例：smtp.gmail.com、smtp.qq.com、smtpdm.aliyun.com"
            >
              <Input placeholder="例如: smtp.gmail.com" />
            </Form.Item>
          </Col>
          <Col xs={12} md={6}>
            <Form.Item
              name="port"
              label="端口"
              rules={[{ required: true, message: '请输入端口号' }]}
              extra="SMTP端口号，常见：587(TLS)、465(SSL)、25(非加密，可能被屏蔽)。"
            >
              <InputNumber
                min={1}
                max={65535}
                placeholder="587"
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
          <Col xs={12} md={6}>
            <Form.Item name="is_ssl" label="安全" valuePropName="checked" extra="启用SSL/TLS后，端口通常为465或587，增强链路加密。">
              <Checkbox>SSL/TLS</Checkbox>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} md={12}>
            <Form.Item
              name="username"
              label="用户名"
              rules={[{ required: true, message: '请输入用户名' }]}
              extra="登录SMTP服务器的用户名，通常为完整邮箱地址。示例：<EMAIL>"
            >
              <Input
                placeholder="请输入邮箱用户名"
                prefix={<UserOutlined />}
              />
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              name="password"
              label="密码"
              rules={[{ required: true, message: '请输入密码' }]}
              extra="邮箱密码或应用专用密码（建议使用应用专用密码）。"
            >
              <Input.Password
                placeholder="请输入邮箱密码或应用专用密码"
                prefix={<LockOutlined />}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    );
  };

  // 第一步：服务商选择界面（仅创建时显示）
  if (step === 1) {
    return (
      <div>
        <Card
          title="选择邮件服务商"
          className="rounded-md shadow-sm"
          size="small"
          bordered={false}
          style={{ marginBottom: 16, background: isDarkMode ? '#1f1f1f' : '#f9f9f9' }}
        >
          <Row gutter={[16, 16]}>
            {providerOptions.map((opt) => (
              <Col key={opt.value} xs={12} sm={8} md={6} lg={6}>
                <div
                  onClick={() => handleSelectProvider(opt)}
                  style={{
                    cursor: 'pointer',
                    border: `1px solid ${isDarkMode ? '#303030' : '#e8e8e8'}`,
                    borderRadius: 8,
                    padding: 12,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 8,
                    background: isDarkMode ? '#141414' : '#ffffff',
                  }}
                >
                  {opt.icon_url ? (
                    <img src={opt.icon_url} alt={opt.label} style={{ width: 20, height: 20 }} />
                  ) : (
                    <MailOutlined />
                  )}
                  <span style={{ fontWeight: 500 }}>{opt.label}</span>
                </div>
              </Col>
            ))}
          </Row>
        </Card>
        <div style={{ fontSize: 12, color: isDarkMode ? '#8c8c8c' : '#8c8c8c' }}>
          选择后将自动填充SMTP服务器、端口和SSL设置；选择“自定义”可手动填写。
        </div>
        <div style={{ fontSize: 12, color: isDarkMode ? '#8c8c8c' : '#8c8c8c', marginTop: 4 }}>
          （如果不在列表中，请选择“自定义”手动添加）
        </div>
      </div>
    );
  }

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      initialValues={{
        is_ssl: true,
        is_active: true,
        daily_limit: 100,
        monthly_limit: 3000,
        ...initialValues,
        config: initialValues?.config || {},
      }}
      name="accountForm"
    >
      <Row gutter={24}>
        <Col span={24}>
          <Card
            title="基本信息"
            className="rounded-md shadow-sm"
            size="small"
            bordered={false}
            style={{ marginBottom: 24, background: isDarkMode ? '#1f1f1f' : '#f9f9f9' }}
          >
            <Form.Item
              name="name"
              label="账户名称"
              rules={[{ required: true, message: '请输入账户名称' }]}
            >
              <Input placeholder="请输入账户名称" />
            </Form.Item>

            <Row gutter={16}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="type"
                  label="账户类型"
                  rules={[{ required: true, message: '请选择账户类型' }]}
                  extra="根据服务类型选择连接方式：SMTP/IMAP/POP3/API（阿里云邮件推送）。"
                >
                  <Select onChange={handleTypeChange}>
                    <Option value={EMAIL_ACCOUNT_TYPES.SMTP}>SMTP</Option>
                    <Option value={EMAIL_ACCOUNT_TYPES.IMAP}>IMAP</Option>
                    <Option value={EMAIL_ACCOUNT_TYPES.POP3}>POP3</Option>
                    <Option value={EMAIL_ACCOUNT_TYPES.API}>API (阿里云等)</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  name="provider"
                  label="邮件提供商"
                  rules={[{ required: true, message: '请选择邮件提供商' }]}
                  extra="选择常见服务商将自动填充服务器与端口；未在列表时请选择“自定义”。"
                >
                  <Select onChange={handleProviderChange} showSearch optionFilterProp="label">
                    {providerOptions.map(opt => (
                      <Option key={opt.value} value={opt.value} label={opt.label}>
                        <span style={{ display: 'inline-flex', alignItems: 'center', gap: 8 }}>
                          {opt.icon_url ? (
                            <img src={opt.icon_url} alt={opt.label} style={{ width: 16, height: 16 }} />
                          ) : null}
                          {opt.label}
                        </span>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={24}>
          {renderConfigFields()}
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={24}>
          <Card
            title="发件人设置"
            className="rounded-md shadow-sm"
            size="small"
            bordered={false}
            style={{ marginBottom: 24, background: isDarkMode ? '#1f1f1f' : '#f9f9f9' }}
          >
            <Form.Item
              name="from_address"
              label="发件人地址"
              rules={[
                { required: true, message: '请输入发件人地址' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
              extra="用于展示给收件人的发件邮箱地址。示例：<EMAIL>"
            >
              <Input
                placeholder="请输入发件人邮箱地址"
                prefix={<MailOutlined />}
              />
            </Form.Item>

            <Row gutter={16}>
              <Col xs={24} md={12}>
            <Form.Item
                  name="from_name"
                  label="发件人名称"
              extra="显示在收件人客户端中的发件人名称。示例：示例公司客服"
                >
                  <Input placeholder="请输入发件人显示名称" />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
            <Form.Item
                  name="reply_to_address"
                  label="回复地址"
                  rules={[
                    { type: 'email', message: '请输入有效的邮箱地址' },
                  ]}
              extra="收件人点击“回复”时的接收地址，留空则默认使用发件人地址。"
                >
                  <Input placeholder="请输入回复地址（可选）" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={24}>
          <Card
            title="其他设置"
            className="rounded-md shadow-sm"
            size="small"
            bordered={false}
            style={{ marginBottom: 24, background: isDarkMode ? '#1f1f1f' : '#f9f9f9' }}
          >
            <Row gutter={16}>
              <Col xs={24} md={6}>
                <Form.Item
                  name="daily_limit"
                  label="每日发送限制"
                  rules={[{ required: true, message: '请输入每日发送限制' }]}
                  tooltip="每日最大发送邮件数量"
                  extra="限制该账户每日最大发送量，建议根据邮件服务商限制设置。示例：100"
                >
                  <InputNumber min={1} placeholder="100" style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col xs={24} md={6}>
                <Form.Item
                  name={['config', 'send_interval_seconds']}
                  label="固定间隔（秒）"
                  tooltip="两封邮件发送之间的最小时间间隔，用于控制节奏"
                  extra="示例：2（每封相隔2秒），为0表示不限制固定间隔"
                >
              <InputNumber min={0} placeholder="2" style={{ width: '100%' }} disabled={!!autoDecision} />
                </Form.Item>
              </Col>
              <Col xs={24} md={6}>
                <Form.Item
                  name={['config', 'auto_decision']}
                  label="系统自动决策"
                  valuePropName="checked"
                  tooltip="根据历史表现与服务商限速自动调节发送频率"
                  extra="开启后系统将动态控制速率，必要时降低以提升可达率"
                >
                  <Checkbox>启用</Checkbox>
                </Form.Item>
              </Col>
              {isEdit ? (
                <Col xs={24} md={6}>
                  <Form.Item name="is_active" label="状态" valuePropName="checked" extra="关闭后该账户将不可用于发送邮件。">
                    <Checkbox>启用</Checkbox>
                  </Form.Item>
                </Col>
              ) : null}
            </Row>
          </Card>
        </Col>
      </Row>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading} block>
          {isEdit ? '更新账户' : '创建账户'}
        </Button>
      </Form.Item>
    </Form>
  );
};

export default AccountForm; 