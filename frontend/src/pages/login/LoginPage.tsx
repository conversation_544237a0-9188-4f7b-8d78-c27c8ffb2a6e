import React from 'react';
import { Form, Input, Button, Typography, Card, message, Checkbox, Divider, Space } from 'antd';
import { 
  LockOutlined, 
  UserOutlined, 
  EyeOutlined, 
  EyeInvisibleOutlined,
  SafetyOutlined,
  TeamOutlined,
  SettingOutlined,
  ApartmentOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { LoginRequest } from '../../services/auth';
import {applyFieldErrorsToForm, showAPIError} from '../../utils/errorHandler';
import { showError } from '../../utils/messageManager';
import { VALIDATION_ERROR } from '../../constants/errorCodes';
import './LoginPage.css';

const { Title, Text } = Typography;

const LoginPage: React.FC = () => {
  const { isDarkMode, toggleTheme } = useTheme();
  const { login, loading } = useAuth();
  const [form] = Form.useForm();
  const location = useLocation();
  const navigate = useNavigate();
  
  // 获取登录前要访问的页面路径，避免循环重定向
  const fromPath = location.state?.from?.pathname;
  const from = fromPath && fromPath !== '/login' ? fromPath : '/';

  const onFinish = async (values: any) => {
    const loginRequest: LoginRequest = {
      username: values.username,
      password: values.password
    };
    
    try {
      const res = await login(loginRequest, from); // res: ApiResponse<LoginResponse>
      
      // 处理验证错误
      if (res.code === VALIDATION_ERROR && Array.isArray((res as any).errors)) {
        applyFieldErrorsToForm((res as any).errors, form);
        return;
      }
    
      // 登录成功
      if (res.code === 0 && res.data && res.data.access_token) {
        // 登录成功，存储token和用户信息
        // 跳转已在AuthContext处理
        return;
      }
      
      // 其他错误
      showAPIError(res.message || '登录失败');
    } catch (error: any) {
      // 处理未捕获的错误
      console.error('Login error:', error);
      showError('登录过程中发生未知错误，请稍后重试', 'login-unknown-error');
    }
  };

  const features = [
    { icon: <TeamOutlined />, title: '用户管理', desc: '完整的用户生命周期管理' },
    { icon: <ApartmentOutlined />, title: '组织架构', desc: '灵活的组织结构设计' },
    { icon: <SettingOutlined />, title: '权限控制', desc: '细粒度的权限管理' },
    { icon: <SafetyOutlined />, title: '安全可靠', desc: '企业级安全保障' },
  ];

  return (
    <div className="login-container" style={{ 
      background: isDarkMode 
        ? 'linear-gradient(135deg, #141414 0%, #1f1f1f 50%, #262626 100%)'
        : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
    }}>
      {/* 背景装饰 */}
      <div className="login-bg-decoration">
        <div className="floating-shape shape-1"></div>
        <div className="floating-shape shape-2"></div>
        <div className="floating-shape shape-3"></div>
      </div>

      <div className="login-content">
        <div className="login-left">
          <div className="login-hero">
            <div className="hero-logo">
              <div className="logo-icon">
                <TeamOutlined />
              </div>
              <Title level={1} style={{ 
                margin: '16px 0 8px 0',
                color: isDarkMode ? '#ffffff' : '#000000',
                fontSize: 32,
                fontWeight: 700
              }}>
                用户中心
              </Title>
              <Text style={{ 
                fontSize: 16,
                color: isDarkMode ? '#cccccc' : '#666666',
                marginBottom: 48
              }}>
                企业级用户管理系统
              </Text>
            </div>

            <div className="hero-features">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="feature-item"
                >
                  <div className="feature-icon" style={{
                    background: isDarkMode ? '#262626' : '#ffffff',
                    color: '#1890ff'
                  }}>
                    {feature.icon}
                  </div>
                  <div className="feature-content">
                    <Text strong style={{ 
                      fontSize: 14,
                      color: isDarkMode ? '#ffffff' : '#000000'
                    }}>
                      {feature.title}
                    </Text>
                    <Text style={{ 
                      fontSize: 12,
                      color: isDarkMode ? '#cccccc' : '#666666'
                    }}>
                      {feature.desc}
                    </Text>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="login-right">
          <div className="login-form-container">
            <Card 
              className="login-card shadow-lg"
              style={{ 
                borderRadius: 16,
                border: 'none',
                background: isDarkMode ? '#1f1f1f' : '#ffffff',
                boxShadow: isDarkMode 
                  ? '0 20px 40px rgba(0, 0, 0, 0.3)'
                  : '0 20px 40px rgba(0, 0, 0, 0.1)'
              }}
            >
              <div className="login-header">
                <Title level={2} style={{ 
                  textAlign: 'center', 
                  marginBottom: 8,
                  color: isDarkMode ? '#ffffff' : '#000000',
                  fontSize: 24,
                  fontWeight: 600
                }}>
                  欢迎回来
                </Title>
                <Text style={{ 
                  textAlign: 'center',
                  color: isDarkMode ? '#cccccc' : '#666666',
                  fontSize: 14
                }}>
                  请登录您的账户
                </Text>
              </div>

              {/* 默认登录信息提示 */}
              <Card 
                size="small" 
                style={{ 
                  marginTop: 16,
                  background: isDarkMode ? '#262626' : '#f6f8fa',
                  border: isDarkMode ? '1px solid #434343' : '1px solid #e1e8ed'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <InfoCircleOutlined style={{ color: '#1890ff' }} />
                  <Text style={{ 
                    fontSize: 12,
                    color: isDarkMode ? '#cccccc' : '#666666'
                  }}>
                    默认账号：admin | 密码：Admin123!
                  </Text>
                </div>
              </Card>

              <Form form={form}
                name="login" 
                onFinish={onFinish} 
                autoComplete="off"
                size="large"
                style={{ marginTop: 24 }}
                initialValues={{
                  username: 'admin',
                  password: 'Admin123!'
                }}
              >
                <Form.Item 
                  name="username" 
                  rules={[{ required: true, message: '请输入用户名' }]}
                > 
                  <Input 
                    prefix={<UserOutlined style={{ color: '#1890ff' }} />} 
                    placeholder="用户名" 
                    style={{ 
                      borderRadius: 8,
                      height: 48,
                      background: isDarkMode ? '#262626' : '#fafafa',
                      border: isDarkMode ? '#434343' : '#f0f0f0'
                    }}
                  />
                </Form.Item>
                
                <Form.Item 
                  name="password" 
                  rules={[{ required: true, message: '请输入密码' }]}
                > 
                  <Input.Password 
                    prefix={<LockOutlined style={{ color: '#1890ff' }} />} 
                    placeholder="密码" 
                    style={{ 
                      borderRadius: 8,
                      height: 48,
                      background: isDarkMode ? '#262626' : '#fafafa',
                      border: isDarkMode ? '#434343' : '#f0f0f0'
                    }}
                    iconRender={(visible) => 
                      visible ? 
                        <EyeOutlined style={{ color: '#1890ff' }} /> : 
                        <EyeInvisibleOutlined style={{ color: '#999999' }} />
                    }
                  />
                </Form.Item>

                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  marginBottom: 24
                }}>
                  <Form.Item name="remember" valuePropName="checked" noStyle>
                    <Checkbox style={{ 
                      color: isDarkMode ? '#cccccc' : '#666666'
                    }}>
                      记住我
                    </Checkbox>
                  </Form.Item>
                  <Button 
                    type="link" 
                    style={{ 
                      padding: 0,
                      color: '#1890ff'
                    }}
                    onClick={() => navigate('/forgot-password')}
                  >
                    忘记密码？
                  </Button>
                </div>

                <Form.Item>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    className="login-form-button"
                    loading={loading}
                    style={{ 
                      width: '100%',
                      height: '40px',
                      fontSize: '16px',
                      borderRadius: '8px',
                    }}
                  >
                    {loading ? '登录中...' : '登录'}
                  </Button>
                </Form.Item>

                {/* 注册入口 */}
                <div style={{ 
                  textAlign: 'center',
                  marginTop: 16
                }}>
                  <Text style={{ 
                    color: isDarkMode ? '#cccccc' : '#666666',
                    fontSize: 14
                  }}>
                    还没有账户？
                  </Text>
                  <Button 
                    type="link" 
                    style={{ 
                      padding: '0 8px',
                      color: '#1890ff',
                      fontSize: 14,
                      fontWeight: 500
                    }}
                    onClick={() => navigate('/register')}
                  >
                    立即注册
                  </Button>
                </div>
              </Form>

              <Divider style={{ 
                margin: '24px 0',
                borderColor: isDarkMode ? '#434343' : '#f0f0f0'
              }}>
                <Text style={{ 
                  color: isDarkMode ? '#cccccc' : '#999999',
                  fontSize: 12
                }}>
                  其他登录方式
                </Text>
              </Divider>

              <Space style={{ width: '100%', justifyContent: 'center' }}>
                <Button 
                  shape="circle" 
                  size="large"
                  style={{ 
                    width: 48,
                    height: 48,
                    border: `1px solid ${isDarkMode ? '#434343' : '#f0f0f0'}`,
                    background: isDarkMode ? '#262626' : '#fafafa'
                  }}
                >
                  <TeamOutlined style={{ color: '#1890ff' }} />
                </Button>
                <Button 
                  shape="circle" 
                  size="large"
                  style={{ 
                    width: 48,
                    height: 48,
                    border: `1px solid ${isDarkMode ? '#434343' : '#f0f0f0'}`,
                    background: isDarkMode ? '#262626' : '#fafafa'
                  }}
                >
                  <SafetyOutlined style={{ color: '#52c41a' }} />
                </Button>
                <Button 
                  shape="circle" 
                  size="large"
                  style={{ 
                    width: 48,
                    height: 48,
                    border: `1px solid ${isDarkMode ? '#434343' : '#f0f0f0'}`,
                    background: isDarkMode ? '#262626' : '#fafafa'
                  }}
                >
                  <SettingOutlined style={{ color: '#722ed1' }} />
                </Button>
              </Space>
            </Card>
          </div>
        </div>
      </div>

      {/* 主题切换按钮 */}
      <div className="theme-toggle">
        <Button
          type="text"
          icon={isDarkMode ? <TeamOutlined /> : <LockOutlined />}
          onClick={toggleTheme}
          style={{
            width: 48,
            height: 48,
            borderRadius: '50%',
            background: isDarkMode ? '#262626' : '#ffffff',
            border: `1px solid ${isDarkMode ? '#434343' : '#f0f0f0'}`,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
            color: isDarkMode ? '#ffffff' : '#666666'
          }}
        />
      </div>
    </div>
  );
};

export default LoginPage; 