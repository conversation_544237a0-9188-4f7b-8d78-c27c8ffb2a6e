import React, { useState, useEffect } from 'react';
import { Modal, <PERSON>, Button, Typography, Space, Tag, Row, Col, Card, Divider } from 'antd';
import { 
  SafetyCertificateOutlined, 
  BranchesOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { getPermissions, Permission } from '../../../services/permission';
import { showError } from '../../../utils/messageManager';
import { useTheme } from '../../../contexts/ThemeContext';


const { Text, Title } = Typography;

interface PermissionAssignerProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (selectedPermissions: number[]) => void;
  title?: string;
  initialSelectedPermissions?: number[];
  loading?: boolean;
}

// 转换权限数据为Tree组件需要的格式
const convertPermissionsToTreeData = (permissions: Permission[]): any[] => {
  // 按操作类型分组
  const groupedPermissions: { [key: string]: Permission[] } = {};
  
  permissions.forEach(permission => {
    const actionType = permission.action || 'other';
    if (!groupedPermissions[actionType]) {
      groupedPermissions[actionType] = [];
    }
    groupedPermissions[actionType].push(permission);
  });

  // 转换为树形结构
  return Object.entries(groupedPermissions).map(([actionType, perms]) => ({
    key: `group-${actionType}`,
    title: getActionTypeLabel(actionType),
    icon: <BranchesOutlined />,
    children: perms.map(perm => ({
      key: perm.id,
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <SafetyCertificateOutlined style={{ color: '#1890ff' }} />
          <span>{perm.display_name}</span>
          <Tag color="blue">{perm.scope_display_name || perm.scope}</Tag>
        </div>
      ),
      isLeaf: true,
    })),
  }));
};

// 获取操作类型标签
const getActionTypeLabel = (actionType: string): string => {
  const actionTypeMap: { [key: string]: string } = {
    create: '创建操作',
    read: '查看操作',
    update: '更新操作',
    delete: '删除操作',
    export: '导出操作',
    import: '导入操作',
    approve: '审批操作',
    reject: '拒绝操作',
    assign: '分配操作',
    revoke: '撤销操作',
    other: '其他操作',
  };
  return actionTypeMap[actionType] || actionType;
};

// 获取权限范围标签
const getScopeLabel = (scope: string): string => {
  const scopeMap: { [key: string]: string } = {
    all: '全部权限',
    self: '普通权限',
  };
  return scopeMap[scope] || scope;
};

const PermissionAssigner: React.FC<PermissionAssignerProps> = ({
  visible,
  onCancel,
  onOk,
  title = '分配权限',
  initialSelectedPermissions = [],
  loading = false,
}) => {
  const { isDarkMode } = useTheme();
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [treeData, setTreeData] = useState<any[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>(initialSelectedPermissions);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>(initialSelectedPermissions);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [fetchLoading, setFetchLoading] = useState(false);

  // 加载权限数据
  useEffect(() => {
    if (visible) {
      loadPermissions();
    }
  }, [visible]);

  // 更新初始选中的权限
  useEffect(() => {
    setSelectedKeys(initialSelectedPermissions);
    setCheckedKeys(initialSelectedPermissions);
  }, [initialSelectedPermissions]);

  const loadPermissions = async () => {
    setFetchLoading(true);
    try {
      const permissionsRes = await getPermissions();
      const permissionsData = permissionsRes?.data || [];
      setPermissions(permissionsData);
      setTreeData(convertPermissionsToTreeData(permissionsData));
      
      // 设置默认展开的节点
      const defaultExpandedKeys = Object.keys(convertPermissionsToTreeData(permissionsData)).map(key => `group-${key}`);
      setExpandedKeys(defaultExpandedKeys);
    } catch (error) {
      console.error('Failed to load permissions:', error);
      showError('加载权限数据失败');
    } finally {
      setFetchLoading(false);
    }
  };

  // 处理树节点选择
  const handleTreeSelect = (selectedKeys: React.Key[], info: any) => {
    setSelectedKeys(selectedKeys);
  };

  // 处理树节点勾选
  const handleTreeCheck = (checked: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }, info: any) => {
    const checkedKeys = Array.isArray(checked) ? checked : checked.checked;
    setCheckedKeys(checkedKeys);
  };

  // 处理展开/收起
  const handleTreeExpand = (expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys);
  };

  // 处理确认
  const handleOk = () => {
    // 过滤出叶子节点的权限ID
    const permissionIds = checkedKeys
      .filter(key => typeof key === 'number')
      .map(key => key as number);
    
    onOk(permissionIds);
  };

  // 处理取消
  const handleCancel = () => {
    setSelectedKeys(initialSelectedPermissions);
    setCheckedKeys(initialSelectedPermissions);
    onCancel();
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    const allPermissionIds = permissions.map(p => p.id);
    setCheckedKeys(allPermissionIds);
  };

  const handleUnselectAll = () => {
    setCheckedKeys([]);
  };

  // 获取选中权限的统计信息
  const getSelectedStats = () => {
    const selectedPermissionIds = checkedKeys.filter(key => typeof key === 'number') as number[];
    const selectedPermissions = permissions.filter(p => selectedPermissionIds.includes(p.id));
    
    const actionTypeCount: { [key: string]: number } = {};
    selectedPermissions.forEach(perm => {
      const actionType = perm.action || 'other';
      actionTypeCount[actionType] = (actionTypeCount[actionType] || 0) + 1;
    });

    return {
      total: selectedPermissions.length,
      actionTypes: Object.keys(actionTypeCount).length,
      breakdown: actionTypeCount,
    };
  };

  const selectedStats = getSelectedStats();

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <SafetyCertificateOutlined style={{ color: '#1890ff' }} />
          <span>{title}</span>
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      onOk={handleOk}
      width={800}
      confirmLoading={loading}
      style={{
        borderRadius: 12,
      }}
    >
      <div style={{ marginTop: 24 }}>
        {/* 操作按钮 */}
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button size="small" onClick={handleSelectAll}>
              全选
            </Button>
            <Button size="small" onClick={handleUnselectAll}>
              取消全选
            </Button>
            <Button 
              size="small" 
              icon={<ReloadOutlined />} 
              onClick={loadPermissions}
              loading={fetchLoading}
            >
              刷新
            </Button>
          </Space>
        </div>

        <Row gutter={24}>
          {/* 权限树 */}
          <Col span={16}>
            <Card
              title="权限列表"
              size="small"
              style={{
                background: isDarkMode ? '#1f1f1f' : '#ffffff',
                border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
              }}
            >
              <Tree
                checkable
                selectable={false}
                treeData={treeData}
                checkedKeys={checkedKeys}
                expandedKeys={expandedKeys}
                onCheck={handleTreeCheck}
                onExpand={handleTreeExpand}
                showLine
                showIcon
                height={400}
                style={{
                  background: 'transparent',
                }}
              />
            </Card>
          </Col>

          {/* 统计信息 */}
          <Col span={8}>
            <Card
              title="选择统计"
              size="small"
              style={{
                background: isDarkMode ? '#1f1f1f' : '#ffffff',
                border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
              }}
            >
              <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>已选权限</Text>
                  <Tag color="blue">{selectedStats.total}</Tag>
                </div>
                
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text>操作类型</Text>
                  <Tag color="green">{selectedStats.actionTypes}</Tag>
                </div>

                <Divider />

                <div>
                  <Text strong>按操作类型分布：</Text>
                  <div style={{ marginTop: 8 }}>
                    {Object.entries(selectedStats.breakdown).map(([actionType, count]) => (
                      <div key={actionType} style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        alignItems: 'center',
                        marginBottom: 4 
                      }}>
                        <Text style={{ fontSize: 12 }}>
                          {getActionTypeLabel(actionType)}
                        </Text>
                        {/* Assuming Badge is available, otherwise remove or replace with a placeholder */}
                        {/* <Badge count={count} size="small" /> */}
                      </div>
                    ))}
                  </div>
                </div>

                <Divider />

                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text strong>总计</Text>
                  {/* Assuming Badge is available, otherwise remove or replace with a placeholder */}
                  {/* <Badge count={selectedStats.total} size="small" /> */}
                </div>
              </div>
            </Card>
          </Col>
        </Row>

        {/* 提示信息 */}
        <div style={{ marginTop: 16 }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            提示：勾选权限后，角色将继承所选权限的所有操作权限
          </Text>
        </div>
      </div>
    </Modal>
  );
};

export default PermissionAssigner; 