import React, { useState } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Card,
  Space,
  Typography,
  Divider,
  Switch,
} from 'antd';
import {
  MailOutlined,
  GlobalOutlined,
  UserOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import {
  Contact,
  CreateContactRequest,
  UpdateContactRequest,
  CONTACT_STATUS,
} from '../../../types/email-system';
import { useTheme } from '../../../contexts/ThemeContext';

const { Option } = Select;
const { Title, Text } = Typography;
const { TextArea } = Input;

interface ContactFormProps {
  initialValues?: Contact;
  onFinish: (values: CreateContactRequest | UpdateContactRequest) => void;
  loading?: boolean;
}

/**
 * 联系人表单组件
 */
const ContactForm: React.FC<ContactFormProps> = ({
  initialValues,
  onFinish,
  loading = false,
}) => {
  const [form] = Form.useForm();
  const { isDarkMode } = useTheme();
  const isEdit = !!initialValues;
  const [showAdvanced, setShowAdvanced] = useState(false);

  // 常用语言选项
  const languageOptions = [
    { value: 'zh-CN', label: '简体中文' },
    { value: 'zh-TW', label: '繁体中文' },
    { value: 'en-US', label: 'English (US)' },
    { value: 'en-GB', label: 'English (UK)' },
    { value: 'ja-JP', label: '日本語' },
    { value: 'ko-KR', label: '한국어' },
    { value: 'es-ES', label: 'Español' },
    { value: 'fr-FR', label: 'Français' },
    { value: 'de-DE', label: 'Deutsch' },
    { value: 'ru-RU', label: 'Русский' },
  ];

  // 常用国家代码选项
  const countryOptions = [
    { value: 'CN', label: '中国 (CN)' },
    { value: 'US', label: '美国 (US)' },
    { value: 'JP', label: '日本 (JP)' },
    { value: 'KR', label: '韩国 (KR)' },
    { value: 'GB', label: '英国 (GB)' },
    { value: 'DE', label: '德国 (DE)' },
    { value: 'FR', label: '法国 (FR)' },
    { value: 'CA', label: '加拿大 (CA)' },
    { value: 'AU', label: '澳大利亚 (AU)' },
    { value: 'SG', label: '新加坡 (SG)' },
  ];

  // 常用时区选项
  const timezoneOptions = [
    { value: 'Asia/Shanghai', label: 'Asia/Shanghai (UTC+8)' },
    { value: 'Asia/Tokyo', label: 'Asia/Tokyo (UTC+9)' },
    { value: 'Asia/Seoul', label: 'Asia/Seoul (UTC+9)' },
    { value: 'America/New_York', label: 'America/New_York (UTC-5)' },
    { value: 'America/Los_Angeles', label: 'America/Los_Angeles (UTC-8)' },
    { value: 'Europe/London', label: 'Europe/London (UTC+0)' },
    { value: 'Europe/Paris', label: 'Europe/Paris (UTC+1)' },
    { value: 'Europe/Berlin', label: 'Europe/Berlin (UTC+1)' },
    { value: 'Australia/Sydney', label: 'Australia/Sydney (UTC+10)' },
    { value: 'Asia/Singapore', label: 'Asia/Singapore (UTC+8)' },
  ];

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      initialValues={{
        status: CONTACT_STATUS.ACTIVE,
        ...initialValues,
      }}
    >
      {/* 基本信息 */}
      <Card
        title="基本信息"
        size="small"
        style={{
          marginBottom: 16,
          background: isDarkMode ? '#1f1f1f' : '#f9f9f9',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
        }}
      >
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="email"
              label="邮箱地址"
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' },
              ]}
            >
              <Input
                placeholder="请输入邮箱地址"
                prefix={<MailOutlined />}
                disabled={isEdit} // 编辑时不允许修改邮箱
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col xs={24} md={12}>
            <Form.Item
              name="status"
              label="状态"
              rules={[{ required: true, message: '请选择状态' }]}
            >
              <Select placeholder="请选择状态">
                <Option value={CONTACT_STATUS.ACTIVE}>活跃</Option>
                <Option value={CONTACT_STATUS.SUPPRESSED}>被抑制</Option>
                <Option value={CONTACT_STATUS.UNCONFIRMED}>未确认</Option>
                <Option value={CONTACT_STATUS.BOUNCED}>退回</Option>
                <Option value={CONTACT_STATUS.COMPLAINED}>投诉</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              name="preferred_language"
              label="首选语言"
            >
              <Select
                placeholder="请选择首选语言"
                allowClear
                showSearch
                optionFilterProp="label"
              >
                {languageOptions.map(option => (
                  <Option key={option.value} value={option.value} label={option.label}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
      </Card>

      {/* 地理信息 */}
      <Card
        title="地理信息"
        size="small"
        style={{
          marginBottom: 16,
          background: isDarkMode ? '#1f1f1f' : '#f9f9f9',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
        }}
      >
        <Row gutter={16}>
          <Col xs={24} md={12}>
            <Form.Item
              name="country_code"
              label="国家代码"
              rules={[
                { len: 2, message: '国家代码必须是2个字符' },
              ]}
            >
              <Select
                placeholder="请选择国家"
                allowClear
                showSearch
                optionFilterProp="label"
              >
                {countryOptions.map(option => (
                  <Option key={option.value} value={option.value} label={option.label}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} md={12}>
            <Form.Item
              name="timezone"
              label="时区"
            >
              <Select
                placeholder="请选择时区"
                allowClear
                showSearch
                optionFilterProp="label"
              >
                {timezoneOptions.map(option => (
                  <Option key={option.value} value={option.value} label={option.label}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
      </Card>

      {/* 备注信息 */}
      <Card
        title="备注信息"
        size="small"
        style={{
          marginBottom: 16,
          background: isDarkMode ? '#1f1f1f' : '#f9f9f9',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
        }}
      >
        <Form.Item
          name="notes"
          label="备注"
        >
          <TextArea
            placeholder="请输入备注信息"
            rows={3}
            maxLength={500}
            showCount
          />
        </Form.Item>
      </Card>

      {/* 高级选项 */}
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <span>高级选项</span>
            <Switch
              size="small"
              checked={showAdvanced}
              onChange={setShowAdvanced}
              checkedChildren="显示"
              unCheckedChildren="隐藏"
            />
          </div>
        }
        size="small"
        style={{
          marginBottom: 24,
          background: isDarkMode ? '#1f1f1f' : '#f9f9f9',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
        }}
      >
        {showAdvanced && (
          <>
            <Form.Item
              name="lists"
              label="所属列表"
              tooltip="该联系人将被添加到选中的列表中"
            >
              <Select
                mode="multiple"
                placeholder="请选择列表"
                allowClear
              >
                {/* 这里可以动态加载列表选项 */}
                <Option value={1}>默认列表</Option>
                <Option value={2}>VIP客户</Option>
                <Option value={3}>新用户</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="tags"
              label="标签"
              tooltip="为联系人添加标签，便于分类管理"
            >
              <Select
                mode="tags"
                placeholder="请输入或选择标签"
                allowClear
              >
                {/* 这里可以动态加载标签选项 */}
                <Option value="vip">VIP</Option>
                <Option value="new">新用户</Option>
                <Option value="active">活跃用户</Option>
              </Select>
            </Form.Item>

            <Divider />

            <Form.Item
              name="attributes"
              label="自定义属性"
              tooltip="以JSON格式输入自定义属性"
            >
              <TextArea
                placeholder='例如：{"age": 25, "gender": "male", "city": "Shanghai"}'
                rows={4}
                maxLength={1000}
                showCount
              />
            </Form.Item>
          </>
        )}
      </Card>

      {/* 表单操作按钮 */}
      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit" loading={loading}>
            {isEdit ? '更新联系人' : '创建联系人'}
          </Button>
          <Button>
            取消
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default ContactForm;