import React, { useState } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Card,
  Space,
  Typography,
  Divider,
  Switch,
  ColorPicker,
  Tabs,
  Alert,
} from 'antd';
import {
  TagOutlined,
  BulbOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  CodeOutlined,
} from '@ant-design/icons';
import {
  Tag as TagType,
  CreateTagRequest,
  UpdateTagRequest,
  TAG_TYPE,
  TAG_REFRESH_POLICY,
} from '../../../types/email-system';
import { useTheme } from '../../../contexts/ThemeContext';

const { Option } = Select;
const { Title, Text } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;

interface TagFormProps {
  initialValues?: TagType;
  onFinish: (values: CreateTagRequest | UpdateTagRequest) => void;
  loading?: boolean;
}

/**
 * 标签表单组件
 */
const TagForm: React.FC<TagFormProps> = ({
  initialValues,
  onFinish,
  loading = false,
}) => {
  const [form] = Form.useForm();
  const { isDarkMode } = useTheme();
  const isEdit = !!initialValues;
  const [tagType, setTagType] = useState<string>(initialValues?.type || TAG_TYPE.STATIC_LIST);
  const [showRuleEditor, setShowRuleEditor] = useState(false);

  // 处理标签类型变化
  const handleTypeChange = (value: string) => {
    setTagType(value);
    setShowRuleEditor(value === TAG_TYPE.RULE_BASED);
    
    // 重置规则相关字段
    if (value === TAG_TYPE.STATIC_LIST) {
      form.setFieldsValue({
        rule_tree: undefined,
        refresh_policy: undefined,
      });
    }
  };

  // 预定义的标签颜色
  const presetColors = [
    '#f50', '#2db7f5', '#87d068', '#108ee9',
    '#f04134', '#00a854', '#ffbf00', '#722ed1',
    '#eb2f96', '#52c41a', '#1890ff', '#fa8c16',
    '#13c2c2', '#faad14', '#a0d911', '#ff7875',
  ];

  // 规则模板
  const ruleTemplates = {
    simple: {
      name: '简单条件',
      description: '单个字段的简单比较',
      template: {
        operator: 'and',
        conditions: [
          {
            field: 'email',
            operator: 'contains',
            value: '@gmail.com'
          }
        ]
      }
    },
    complex: {
      name: '复合条件',
      description: '多个字段的复杂组合',
      template: {
        operator: 'and',
        conditions: [
          {
            field: 'status',
            operator: 'equals',
            value: 'active'
          },
          {
            operator: 'or',
            conditions: [
              {
                field: 'country_code',
                operator: 'equals',
                value: 'CN'
              },
              {
                field: 'preferred_language',
                operator: 'equals',
                value: 'zh-CN'
              }
            ]
          }
        ]
      }
    }
  };

  // 插入规则模板
  const insertRuleTemplate = (template: any) => {
    form.setFieldsValue({
      rule_tree: JSON.stringify(template, null, 2)
    });
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onFinish}
      initialValues={{
        type: TAG_TYPE.STATIC_LIST,
        color: '#1890ff',
        refresh_policy: TAG_REFRESH_POLICY.ONCE,
        ...initialValues,
        rule_tree: initialValues?.rule_tree ? JSON.stringify(initialValues.rule_tree, null, 2) : undefined,
      }}
    >
      <Tabs defaultActiveKey="basic" type="card">
        <TabPane tab="基本信息" key="basic">
          {/* 基本信息 */}
          <Card
            title="基本信息"
            size="small"
            style={{
              marginBottom: 16,
              background: isDarkMode ? '#1f1f1f' : '#f9f9f9',
              border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
            }}
          >
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  name="name"
                  label="标签名称"
                  rules={[
                    { required: true, message: '请输入标签名称' },
                    { max: 64, message: '标签名称不能超过64个字符' },
                  ]}
                >
                  <Input
                    placeholder="请输入标签名称"
                    prefix={<TagOutlined />}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="type"
                  label="标签类型"
                  rules={[{ required: true, message: '请选择标签类型' }]}
                >
                  <Select
                    placeholder="请选择标签类型"
                    onChange={handleTypeChange}
                  >
                    <Option value={TAG_TYPE.STATIC_LIST}>
                      <Space>
                        <SettingOutlined />
                        静态标签
                      </Space>
                    </Option>
                    <Option value={TAG_TYPE.RULE_BASED}>
                      <Space>
                        <BulbOutlined />
                        规则标签
                      </Space>
                    </Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  name="color"
                  label="标签颜色"
                >
                  <ColorPicker
                    presets={[
                      {
                        label: '推荐',
                        colors: presetColors,
                      },
                    ]}
                    showText
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="description"
              label="描述"
              rules={[
                { max: 255, message: '描述不能超过255个字符' },
              ]}
            >
              <TextArea
                placeholder="请输入标签描述"
                rows={3}
                maxLength={255}
                showCount
              />
            </Form.Item>

            {tagType === TAG_TYPE.STATIC_LIST && (
              <Alert
                message="静态标签"
                description="静态标签需要手动分配给联系人，适用于固定的分类标准。"
                type="info"
                showIcon
                icon={<SettingOutlined />}
              />
            )}

            {tagType === TAG_TYPE.RULE_BASED && (
              <Alert
                message="规则标签"
                description="规则标签基于条件自动匹配联系人，会根据联系人数据的变化动态更新成员。"
                type="info"
                showIcon
                icon={<BulbOutlined />}
              />
            )}
          </Card>
        </TabPane>

        {tagType === TAG_TYPE.RULE_BASED && (
          <TabPane tab="规则配置" key="rules">
            {/* 规则配置 */}
            <Card
              title="规则定义"
              size="small"
              style={{
                marginBottom: 16,
                background: isDarkMode ? '#1f1f1f' : '#f9f9f9',
                border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
              }}
              extra={
                <Button
                  type="link"
                  size="small"
                  icon={<InfoCircleOutlined />}
                  onClick={() => setShowRuleEditor(!showRuleEditor)}
                >
                  {showRuleEditor ? '隐藏' : '显示'}规则帮助
                </Button>
              }
            >
              {showRuleEditor && (
                <div style={{ marginBottom: 16 }}>
                  <Alert
                    message="规则编写说明"
                    description={
                      <div>
                        <p>规则使用JSON格式定义，支持以下操作符：</p>
                        <ul style={{ marginTop: 8, paddingLeft: 20 }}>
                          <li><code>equals</code>: 等于</li>
                          <li><code>not_equals</code>: 不等于</li>
                          <li><code>contains</code>: 包含</li>
                          <li><code>starts_with</code>: 开始于</li>
                          <li><code>ends_with</code>: 结束于</li>
                          <li><code>greater_than</code>: 大于</li>
                          <li><code>less_than</code>: 小于</li>
                          <li><code>in</code>: 在列表中</li>
                          <li><code>not_in</code>: 不在列表中</li>
                        </ul>
                      </div>
                    }
                    type="info"
                    showIcon
                  />

                  <Divider />

                  <div style={{ marginBottom: 16 }}>
                    <Text strong>规则模板：</Text>
                    <Space style={{ marginTop: 8 }}>
                      {Object.entries(ruleTemplates).map(([key, template]) => (
                        <Button
                          key={key}
                          size="small"
                          onClick={() => insertRuleTemplate(template.template)}
                        >
                          {template.name}
                        </Button>
                      ))}
                    </Space>
                  </div>
                </div>
              )}

              <Form.Item
                name="rule_tree"
                label="规则定义"
                rules={[
                  { required: tagType === TAG_TYPE.RULE_BASED, message: '请输入规则定义' },
                ]}
              >
                <TextArea
                  placeholder="请输入JSON格式的规则定义"
                  rows={10}
                  style={{ fontFamily: 'monospace' }}
                />
              </Form.Item>
            </Card>

            {/* 刷新策略 */}
            <Card
              title="刷新策略"
              size="small"
              style={{
                marginBottom: 16,
                background: isDarkMode ? '#1f1f1f' : '#f9f9f9',
                border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
              }}
            >
              <Form.Item
                name="refresh_policy"
                label="刷新策略"
                tooltip="规则标签的刷新策略，决定何时重新计算标签成员"
              >
                <Select placeholder="请选择刷新策略">
                  <Option value={TAG_REFRESH_POLICY.ONCE}>
                    一次性 - 创建后不再自动刷新
                  </Option>
                  <Option value={TAG_REFRESH_POLICY.SCHEDULE}>
                    定时刷新 - 按计划定期刷新
                  </Option>
                  <Option value={TAG_REFRESH_POLICY.TRIGGER}>
                    触发刷新 - 数据变化时自动刷新
                  </Option>
                </Select>
              </Form.Item>

              <Alert
                message="刷新策略说明"
                description={
                  <ul style={{ marginTop: 8, paddingLeft: 20, marginBottom: 0 }}>
                    <li><strong>一次性</strong>：标签创建后只计算一次，不会自动更新</li>
                    <li><strong>定时刷新</strong>：按设定的时间间隔定期重新计算标签成员</li>
                    <li><strong>触发刷新</strong>：当联系人数据发生变化时自动重新计算</li>
                  </ul>
                }
                type="info"
                showIcon
              />
            </Card>
          </TabPane>
        )}

        <TabPane tab="预览测试" key="preview">
          <Card
            title="规则预览"
            size="small"
            style={{
              background: isDarkMode ? '#1f1f1f' : '#f9f9f9',
              border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
            }}
          >
            <Alert
              message="功能说明"
              description="在此可以预览规则匹配的联系人，验证规则是否正确。保存标签前建议先进行预览测试。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Space>
              <Button
                type="primary"
                icon={<CodeOutlined />}
                disabled={tagType !== TAG_TYPE.RULE_BASED}
              >
                预览规则结果
              </Button>
              <Button
                disabled={tagType !== TAG_TYPE.RULE_BASED}
              >
                验证规则语法
              </Button>
            </Space>

            <div style={{ 
              marginTop: 16, 
              padding: 12, 
              background: isDarkMode ? '#262626' : '#f5f5f5',
              borderRadius: 6,
              minHeight: 200,
            }}>
              <Text type="secondary">预览结果将在此显示...</Text>
            </div>
          </Card>
        </TabPane>
      </Tabs>

      {/* 表单操作按钮 */}
      <div style={{ marginTop: 24, paddingTop: 16, borderTop: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}` }}>
        <Space>
          <Button type="primary" htmlType="submit" loading={loading}>
            {isEdit ? '更新标签' : '创建标签'}
          </Button>
          <Button>
            取消
          </Button>
        </Space>
      </div>
    </Form>
  );
};

export default TagForm;