import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  message,
  Row,
  Col,
  Typography,
  Tooltip,
  Dropdown,
  Switch,
  Form,
  Drawer,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  UserOutlined,
  MoreOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  SearchOutlined,
  FilterOutlined,
} from '@ant-design/icons';
import { useTheme } from '../../contexts/ThemeContext';
import { emailSystemService } from '../../services/email-system';
import {
  Contact,
  CreateContactRequest,
  UpdateContactRequest,
  SearchContactsRequest,
  CONTACT_STATUS,
} from '../../types/email-system';
import ContactForm from './components/ContactForm';
import { showAPIError } from '../../utils/errorHandler';

const { Option } = Select;
const { Title, Text } = Typography;
const { confirm } = Modal;

/**
 * 联系人管理页面
 */
const ContactManagement: React.FC = () => {
  const { isDarkMode } = useTheme();
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState<SearchContactsRequest>({
    page: 1,
    size: 20,
  });

  // 模态框状态
  const [formDrawerVisible, setFormDrawerVisible] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  // 搜索筛选状态
  const [searchForm] = Form.useForm();
  const [advancedSearch, setAdvancedSearch] = useState(false);

  // 统计数据
  const stats = [
    {
      title: '总联系人数',
      value: pagination.total,
      icon: <UserOutlined />,
      color: '#1890ff',
    },
    {
      title: '活跃联系人',
      value: contacts.filter(c => c.status === CONTACT_STATUS.ACTIVE).length,
      icon: <CheckCircleOutlined />,
      color: '#52c41a',
    },
    {
      title: '被抑制',
      value: contacts.filter(c => c.status === CONTACT_STATUS.SUPPRESSED).length,
      icon: <CloseCircleOutlined />,
      color: '#ff4d4f',
    },
    {
      title: '未确认',
      value: contacts.filter(c => c.status === CONTACT_STATUS.UNCONFIRMED).length,
      icon: <ExclamationCircleOutlined />,
      color: '#fa8c16',
    },
  ];

  // 初始化数据
  useEffect(() => {
    fetchContacts();
  }, [pagination.current, pagination.pageSize]);

  // 获取联系人列表
  const fetchContacts = async () => {
    setLoading(true);
    try {
      const params: SearchContactsRequest = {
        page: pagination.current,
        size: pagination.pageSize,
        ...searchParams,
      };
      
      const response = await emailSystemService.contact.searchContacts(params);
      if (response.data) {
        setContacts(response.data.items || []);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination?.total || 0,
        }));
      }
    } catch (error) {
      showAPIError(error);
      setContacts([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = (values: any) => {
    const filters = {
      keyword: values.keyword,
      status: values.status,
      preferred_language: values.preferred_language,
      country_code: values.country_code,
      tags: values.tags,
      lists: values.lists,
    };

    setSearchParams(prev => ({
      ...prev,
      filters,
      page: 1,
    }));
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
    fetchContacts();
  };

  // 重置搜索
  const handleResetSearch = () => {
    searchForm.resetFields();
    setSearchParams({
      page: 1,
      size: pagination.pageSize,
    });
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
    fetchContacts();
  };

  // 处理创建联系人
  const handleCreate = () => {
    setSelectedContact(null);
    setFormDrawerVisible(true);
  };

  // 处理编辑联系人
  const handleEdit = (contact: Contact) => {
    setSelectedContact(contact);
    setFormDrawerVisible(true);
  };

  // 处理删除联系人
  const handleDelete = (contact: Contact) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除联系人 "${contact.email}" 吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await emailSystemService.contact.deleteContact({ id: contact.id });
          message.success('联系人删除成功');
          fetchContacts();
        } catch (error) {
          showAPIError(error);
        }
      },
    });
  };

  // 处理状态切换
  const handleToggleStatus = async (contact: Contact) => {
    const newStatus = contact.status === CONTACT_STATUS.ACTIVE ? CONTACT_STATUS.SUPPRESSED : CONTACT_STATUS.ACTIVE;
    
    try {
      await emailSystemService.contact.updateContactStatus({
        contact_ids: [contact.id],
        status: newStatus,
      });
      message.success(`联系人状态已更新`);
      fetchContacts();
    } catch (error) {
      showAPIError(error);
    }
  };

  // 处理表单提交
  const handleFormSubmit = async (values: CreateContactRequest | UpdateContactRequest) => {
    setFormLoading(true);
    try {
      if (selectedContact) {
        // 更新联系人
        await emailSystemService.contact.updateContact({
          ...values,
          id: selectedContact.id,
        } as UpdateContactRequest);
        message.success('联系人更新成功');
      } else {
        // 创建联系人
        await emailSystemService.contact.createContact(values as CreateContactRequest);
        message.success('联系人创建成功');
      }
      setFormDrawerVisible(false);
      fetchContacts();
    } catch (error) {
      showAPIError(error);
    } finally {
      setFormLoading(false);
    }
  };

  // 渲染联系人状态标签
  const renderStatus = (status: string) => {
    const statusConfig: Record<string, { text: string; color: string }> = {
      [CONTACT_STATUS.ACTIVE]: { text: '活跃', color: 'success' },
      [CONTACT_STATUS.SUPPRESSED]: { text: '被抑制', color: 'error' },
      [CONTACT_STATUS.UNCONFIRMED]: { text: '未确认', color: 'warning' },
      [CONTACT_STATUS.BOUNCED]: { text: '退回', color: 'default' },
      [CONTACT_STATUS.COMPLAINED]: { text: '投诉', color: 'error' },
    };
    const config = statusConfig[status] || { text: status, color: 'default' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: '联系人信息',
      key: 'contact_info',
      render: (record: Contact) => (
        <div>
          <div style={{
            fontSize: 14,
            fontWeight: 500,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {record.email}
          </div>
          {record.preferred_language && (
            <div style={{
              fontSize: 12,
              color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
            }}>
              语言: {record.preferred_language}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (record: Contact) => (
        <div>
          <div style={{
            fontSize: 14,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {renderStatus(record.status)}
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            <Switch
              checked={record.status === CONTACT_STATUS.ACTIVE}
              onChange={() => handleToggleStatus(record)}
              size="small"
            />
          </div>
        </div>
      ),
    },
    {
      title: '地理信息',
      key: 'geo_info',
      render: (record: Contact) => (
        <div>
          {record.country_code && (
            <div style={{
              fontSize: 12,
              color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
            }}>
              国家: {record.country_code}
            </div>
          )}
          {record.timezone && (
            <div style={{
              fontSize: 12,
              color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
            }}>
              时区: {record.timezone}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '创建时间',
      key: 'created_at',
      render: (record: Contact) => (
        <div style={{
          fontSize: 12,
          color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
        }}>
          {new Date(record.created_at).toLocaleString()}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: Contact) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'toggle',
                  icon: record.status === CONTACT_STATUS.ACTIVE ? <CloseCircleOutlined /> : <CheckCircleOutlined />,
                  label: record.status === CONTACT_STATUS.ACTIVE ? '抑制' : '激活',
                  onClick: () => handleToggleStatus(record),
                },
                {
                  key: 'delete',
                  icon: <DeleteOutlined />,
                  label: '删除',
                  danger: true,
                  onClick: () => handleDelete(record),
                },
              ],
            }}
          >
            <Button
              type="text"
              size="small"
              icon={<MoreOutlined />}
            />
          </Dropdown>
        </Space>
      ),
    },
  ];

  return (
    <div className="contact-management-page">
      {/* 统计信息区域 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {/* 统计信息 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 32 }}>
            {stats.map((stat, index) => (
              <div
                key={index}
                style={{ display: 'flex', alignItems: 'center', gap: 12 }}
              >
                <div
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 8,
                    background: `${stat.color}15`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: stat.color,
                    fontSize: 18,
                  }}
                >
                  {stat.icon}
                </div>
                <div>
                  <div style={{
                    fontSize: 24,
                    fontWeight: 600,
                    color: isDarkMode ? '#ffffff' : '#262626',
                    lineHeight: 1,
                  }}>
                    {stat.value.toLocaleString()}
                  </div>
                  <div style={{
                    fontSize: 12,
                    color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                    marginTop: 2,
                  }}>
                    {stat.title}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 操作按钮 */}
          <Space>
            <Button 
              icon={<FilterOutlined />} 
              onClick={() => setAdvancedSearch(!advancedSearch)}
            >
              {advancedSearch ? '简单搜索' : '高级搜索'}
            </Button>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
              新增联系人
            </Button>
          </Space>
        </div>
      </div>

      {/* 搜索和筛选区域 */}
      <Card
        style={{
          borderRadius: 12,
          borderStyle: 'none',
          background: isDarkMode ? '#1f1f1f' : '#ffffff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
          marginBottom: 24,
        }}
        styles={{ body: { padding: 20 } }}
      >
        <Form form={searchForm} onFinish={handleSearch}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="keyword" style={{ margin: 0 }}>
                <Input.Search
                  placeholder="搜索邮箱地址"
                  allowClear
                  prefix={<SearchOutlined />}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="status" style={{ margin: 0 }}>
                <Select
                  placeholder="选择状态"
                  allowClear
                  style={{ width: '100%' }}
                >
                  <Option value={CONTACT_STATUS.ACTIVE}>活跃</Option>
                  <Option value={CONTACT_STATUS.SUPPRESSED}>被抑制</Option>
                  <Option value={CONTACT_STATUS.UNCONFIRMED}>未确认</Option>
                  <Option value={CONTACT_STATUS.BOUNCED}>退回</Option>
                  <Option value={CONTACT_STATUS.COMPLAINED}>投诉</Option>
                </Select>
              </Form.Item>
            </Col>
            {advancedSearch && (
              <>
                <Col xs={24} sm={12} md={6}>
                  <Form.Item name="preferred_language" style={{ margin: 0 }}>
                    <Input placeholder="语言代码" allowClear />
                  </Form.Item>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Form.Item name="country_code" style={{ margin: 0 }}>
                    <Input placeholder="国家代码" allowClear />
                  </Form.Item>
                </Col>
              </>
            )}
            <Col xs={24} sm={12} md={6}>
              <Space>
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                  搜索
                </Button>
                <Button onClick={handleResetSearch}>
                  重置
                </Button>
                <Button icon={<ReloadOutlined />} onClick={fetchContacts}>
                  刷新
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 联系人表格 */}
      <Card
        style={{
          borderRadius: 12,
          borderStyle: 'none',
          background: isDarkMode ? '#1f1f1f' : '#ffffff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
        }}
        styles={{ body: { padding: 0 } }}
      >
        <Table
          columns={columns}
          dataSource={contacts}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination({
                ...pagination,
                current: page,
                pageSize: pageSize || pagination.pageSize,
              });
            },
          }}
          style={{ background: 'transparent' }}
        />
      </Card>

      {/* 创建/编辑联系人抽屉 */}
      <Drawer
        title={selectedContact ? '编辑联系人' : '创建联系人'}
        open={formDrawerVisible}
        onClose={() => setFormDrawerVisible(false)}
        width={600}
        destroyOnClose
      >
        <ContactForm
          initialValues={selectedContact || undefined}
          onFinish={handleFormSubmit}
          loading={formLoading}
        />
      </Drawer>
    </div>
  );
};

export default ContactManagement;