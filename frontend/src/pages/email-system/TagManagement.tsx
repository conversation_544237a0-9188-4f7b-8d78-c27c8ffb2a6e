import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  message,
  Row,
  Col,
  Typography,
  Tooltip,
  Dropdown,
  Switch,
  Form,
  Drawer,
  Badge,
  Progress,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  TagOutlined,
  MoreOutlined,
  FireOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  SearchOutlined,
  SyncOutlined,
  MergeOutlined,
  BulbOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { useTheme } from '../../contexts/ThemeContext';
import { emailSystemService } from '../../services/email-system';
import {
  Tag as TagType,
  CreateTagRequest,
  UpdateTagRequest,
  TAG_TYPE,
  TAG_REFRESH_POLICY,
} from '../../types/email-system';
import TagForm from './components/TagForm';
import { showAPIError } from '../../utils/errorHandler';

const { Option } = Select;
const { Title, Text } = Typography;
const { confirm } = Modal;

/**
 * 标签管理页面
 */
const TagManagement: React.FC = () => {
  const { isDarkMode } = useTheme();
  const [tags, setTags] = useState<TagType[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // 模态框状态
  const [formDrawerVisible, setFormDrawerVisible] = useState(false);
  const [selectedTag, setSelectedTag] = useState<TagType | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  // 搜索状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filterType, setFilterType] = useState<string>('');

  // 热门标签和最近标签
  const [popularTags, setPopularTags] = useState<TagType[]>([]);
  const [recentTags, setRecentTags] = useState<TagType[]>([]);

  // 统计数据
  const stats = [
    {
      title: '总标签数',
      value: pagination.total,
      icon: <TagOutlined />,
      color: '#1890ff',
    },
    {
      title: '规则标签',
      value: tags.filter(t => t.type === TAG_TYPE.RULE_BASED).length,
      icon: <BulbOutlined />,
      color: '#52c41a',
    },
    {
      title: '静态标签',
      value: tags.filter(t => t.type === TAG_TYPE.STATIC_LIST).length,
      icon: <SettingOutlined />,
      color: '#722ed1',
    },
    {
      title: '使用中',
      value: tags.filter(t => t.usage_count > 0).length,
      icon: <FireOutlined />,
      color: '#fa8c16',
    },
  ];

  // 初始化数据
  useEffect(() => {
    fetchTags();
    fetchPopularTags();
    fetchRecentTags();
  }, [pagination.current, pagination.pageSize, searchKeyword, filterType]);

  // 获取标签列表
  const fetchTags = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.current,
        size: pagination.pageSize,
        keyword: searchKeyword || undefined,
      };
      
      const response = await emailSystemService.tag.getTags(params);
      if (response.data) {
        setTags(response.data.items || []);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination?.total || 0,
        }));
      }
    } catch (error) {
      showAPIError(error);
      setTags([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取热门标签
  const fetchPopularTags = async () => {
    try {
      const response = await emailSystemService.tag.getPopularTags({ limit: 10 });
      if (response.data) {
        setPopularTags(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch popular tags:', error);
    }
  };

  // 获取最近标签
  const fetchRecentTags = async () => {
    try {
      const response = await emailSystemService.tag.getRecentTags({ limit: 10 });
      if (response.data) {
        setRecentTags(response.data);
      }
    } catch (error) {
      console.error('Failed to fetch recent tags:', error);
    }
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理类型筛选
  const handleTypeFilter = (value: string) => {
    setFilterType(value);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理创建标签
  const handleCreate = () => {
    setSelectedTag(null);
    setFormDrawerVisible(true);
  };

  // 处理编辑标签
  const handleEdit = (tag: TagType) => {
    setSelectedTag(tag);
    setFormDrawerVisible(true);
  };

  // 处理删除标签
  const handleDelete = (tag: TagType) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除标签 "${tag.name}" 吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await emailSystemService.tag.deleteTag({ id: tag.id });
          message.success('标签删除成功');
          fetchTags();
        } catch (error) {
          showAPIError(error);
        }
      },
    });
  };

  // 处理刷新标签
  const handleRefresh = async (tag: TagType) => {
    try {
      await emailSystemService.tag.refreshTag({ id: tag.id });
      message.success('标签刷新成功');
      fetchTags();
    } catch (error) {
      showAPIError(error);
    }
  };

  // 处理表单提交
  const handleFormSubmit = async (values: CreateTagRequest | UpdateTagRequest) => {
    setFormLoading(true);
    try {
      if (selectedTag) {
        // 更新标签
        await emailSystemService.tag.updateTag({
          ...values,
          id: selectedTag.id,
        } as UpdateTagRequest);
        message.success('标签更新成功');
      } else {
        // 创建标签
        await emailSystemService.tag.createTag(values as CreateTagRequest);
        message.success('标签创建成功');
      }
      setFormDrawerVisible(false);
      fetchTags();
    } catch (error) {
      showAPIError(error);
    } finally {
      setFormLoading(false);
    }
  };

  // 渲染标签类型
  const renderTagType = (type: string) => {
    const typeConfig: Record<string, { text: string; color: string; icon: React.ReactNode }> = {
      [TAG_TYPE.RULE_BASED]: { text: '规则标签', color: 'processing', icon: <BulbOutlined /> },
      [TAG_TYPE.STATIC_LIST]: { text: '静态标签', color: 'success', icon: <SettingOutlined /> },
    };
    const config = typeConfig[type] || { text: type, color: 'default', icon: <TagOutlined /> };
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  // 渲染刷新策略
  const renderRefreshPolicy = (policy?: string) => {
    if (!policy) return null;
    
    const policyConfig: Record<string, { text: string; color: string }> = {
      [TAG_REFRESH_POLICY.SCHEDULE]: { text: '定时刷新', color: 'blue' },
      [TAG_REFRESH_POLICY.TRIGGER]: { text: '触发刷新', color: 'green' },
      [TAG_REFRESH_POLICY.ONCE]: { text: '一次性', color: 'orange' },
    };
    const config = policyConfig[policy] || { text: policy, color: 'default' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: '标签信息',
      key: 'tag_info',
      render: (record: TagType) => (
        <div>
          <div style={{
            fontSize: 14,
            fontWeight: 500,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
            display: 'flex',
            alignItems: 'center',
            gap: 8,
          }}>
            {record.color && (
              <div
                style={{
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  backgroundColor: record.color,
                }}
              />
            )}
            {record.name}
          </div>
          {record.description && (
            <div style={{
              fontSize: 12,
              color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
              marginBottom: 4,
            }}>
              {record.description}
            </div>
          )}
          {renderTagType(record.type)}
        </div>
      ),
    },
    {
      title: '成员统计',
      key: 'member_stats',
      render: (record: TagType) => (
        <div>
          <div style={{
            fontSize: 16,
            fontWeight: 600,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {record.member_count.toLocaleString()}
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            成员数量
          </div>
        </div>
      ),
    },
    {
      title: '使用情况',
      key: 'usage_stats',
      render: (record: TagType) => (
        <div>
          <div style={{
            fontSize: 14,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            <Badge
              count={record.usage_count}
              overflowCount={99}
              style={{ backgroundColor: '#52c41a' }}
            />
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            使用次数
          </div>
        </div>
      ),
    },
    {
      title: '刷新策略',
      key: 'refresh_policy',
      render: (record: TagType) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            {renderRefreshPolicy(record.refresh_policy)}
          </div>
          {record.last_refresh_at && (
            <div style={{
              fontSize: 12,
              color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
            }}>
              上次刷新: {new Date(record.last_refresh_at).toLocaleString()}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '创建时间',
      key: 'created_at',
      render: (record: TagType) => (
        <div style={{
          fontSize: 12,
          color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
        }}>
          {new Date(record.created_at).toLocaleString()}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: TagType) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'refresh',
                  icon: <SyncOutlined />,
                  label: '刷新标签',
                  onClick: () => handleRefresh(record),
                  disabled: record.type !== TAG_TYPE.RULE_BASED,
                },
                {
                  key: 'delete',
                  icon: <DeleteOutlined />,
                  label: '删除',
                  danger: true,
                  onClick: () => handleDelete(record),
                },
              ],
            }}
          >
            <Button
              type="text"
              size="small"
              icon={<MoreOutlined />}
            />
          </Dropdown>
        </Space>
      ),
    },
  ];

  return (
    <div className="tag-management-page">
      {/* 统计信息区域 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {/* 统计信息 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 32 }}>
            {stats.map((stat, index) => (
              <div
                key={index}
                style={{ display: 'flex', alignItems: 'center', gap: 12 }}
              >
                <div
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 8,
                    background: `${stat.color}15`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: stat.color,
                    fontSize: 18,
                  }}
                >
                  {stat.icon}
                </div>
                <div>
                  <div style={{
                    fontSize: 24,
                    fontWeight: 600,
                    color: isDarkMode ? '#ffffff' : '#262626',
                    lineHeight: 1,
                  }}>
                    {stat.value.toLocaleString()}
                  </div>
                  <div style={{
                    fontSize: 12,
                    color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                    marginTop: 2,
                  }}>
                    {stat.title}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 操作按钮 */}
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
              新增标签
            </Button>
          </Space>
        </div>
      </div>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={18}>
          {/* 搜索和筛选区域 */}
          <Card
            style={{
              borderRadius: 12,
              borderStyle: 'none',
              background: isDarkMode ? '#1f1f1f' : '#ffffff',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
              border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
              marginBottom: 24,
            }}
            styles={{ body: { padding: 20 } }}
          >
            <Row gutter={[16, 16]} align="middle">
              <Col xs={24} sm={12} md={8}>
                <Input.Search
                  placeholder="搜索标签名称"
                  onSearch={handleSearch}
                  allowClear
                  prefix={<SearchOutlined />}
                />
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Select
                  placeholder="选择类型"
                  onChange={handleTypeFilter}
                  allowClear
                  style={{ width: '100%' }}
                >
                  <Option value={TAG_TYPE.RULE_BASED}>规则标签</Option>
                  <Option value={TAG_TYPE.STATIC_LIST}>静态标签</Option>
                </Select>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Button icon={<ReloadOutlined />} onClick={fetchTags}>
                  刷新
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 标签表格 */}
          <Card
            style={{
              borderRadius: 12,
              borderStyle: 'none',
              background: isDarkMode ? '#1f1f1f' : '#ffffff',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
              border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
            }}
            styles={{ body: { padding: 0 } }}
          >
            <Table
              columns={columns}
              dataSource={tags}
              rowKey="id"
              loading={loading}
              pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, pageSize) => {
                  setPagination({
                    ...pagination,
                    current: page,
                    pageSize: pageSize || pagination.pageSize,
                  });
                },
              }}
              style={{ background: 'transparent' }}
            />
          </Card>
        </Col>

        <Col xs={24} lg={6}>
          {/* 热门标签 */}
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <FireOutlined style={{ color: '#fa8c16' }} />
                热门标签
              </div>
            }
            size="small"
            style={{
              marginBottom: 16,
              background: isDarkMode ? '#1f1f1f' : '#ffffff',
              border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
            }}
          >
            <Space wrap>
              {popularTags.map(tag => (
                <Tag
                  key={tag.id}
                  color={tag.color || 'default'}
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleEdit(tag)}
                >
                  {tag.name} ({tag.usage_count})
                </Tag>
              ))}
            </Space>
          </Card>

          {/* 最近标签 */}
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <ClockCircleOutlined style={{ color: '#1890ff' }} />
                最近使用
              </div>
            }
            size="small"
            style={{
              background: isDarkMode ? '#1f1f1f' : '#ffffff',
              border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
            }}
          >
            <Space wrap>
              {recentTags.map(tag => (
                <Tag
                  key={tag.id}
                  color={tag.color || 'default'}
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleEdit(tag)}
                >
                  {tag.name}
                </Tag>
              ))}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 创建/编辑标签抽屉 */}
      <Drawer
        title={selectedTag ? '编辑标签' : '创建标签'}
        open={formDrawerVisible}
        onClose={() => setFormDrawerVisible(false)}
        width={700}
        destroyOnClose
      >
        <TagForm
          initialValues={selectedTag || undefined}
          onFinish={handleFormSubmit}
          loading={formLoading}
        />
      </Drawer>
    </div>
  );
};

export default TagManagement;