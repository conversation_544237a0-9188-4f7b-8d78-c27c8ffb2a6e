import React, { useEffect } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { useAuth } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import LoginPage from './pages/login/LoginPage';
import ForgotPasswordPage from './pages/login/ForgotPasswordPage';
import RegisterPage from './pages/login/RegisterPage';
import MainLayout from './layouts/MainLayout';
import { routeConfigs, getAllRoutes } from './config/routes';
import { initializeIconPreloading } from './utils/iconPreloader';
import './styles/global.css';

// A component to handle redirection for authenticated users trying to access login page
const LoginPageWrapper = () => {
  const { isAuthenticated, initialLoading } = useAuth();
  const location = useLocation();

  if (initialLoading) {
    return <div>Loading...</div>; // Or a more sophisticated loader
  }

  if (isAuthenticated) {
    // 避免循环重定向到登录页
    const fromPath = location.state?.from?.pathname;
    const from = fromPath && fromPath !== '/login' ? fromPath : '/';
    return <Navigate to={from} replace />;
  }

  return <LoginPage />;
};

const App: React.FC = () => {
  // 获取所有路由配置
  const allRoutes = getAllRoutes(routeConfigs);

  // 初始化图标预加载
  useEffect(() => {
    initializeIconPreloading();
  }, []);

  return (
    <React.Suspense fallback={
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        fontSize: '16px',
        color: '#666'
      }}>
        Loading...
      </div>
    }>
      <Routes>
        <Route path="/login" element={<LoginPageWrapper />} />
        <Route path="/register" element={<RegisterPage />} />
        <Route path="/forgot-password" element={<ForgotPasswordPage />} />
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <MainLayout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />
          {/* 动态生成路由 */}
          {allRoutes.map(route => (
            <Route
              key={route.key}
              path={route.path.startsWith('/') ? route.path.slice(1) : route.path}
              element={<route.component />}
            />
          ))}
        </Route>
        <Route path="*" element={<Navigate to="/" />} />
      </Routes>
    </React.Suspense>
  );
};

export default App; 