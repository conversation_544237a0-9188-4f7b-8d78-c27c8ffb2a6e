# 前端权限控制使用指南

## 概述

本权限系统基于后端 `/menu-tree` 接口返回的权限数据，实现动态的按钮级别权限控制，避免了硬编码权限和额外的API调用。

## 核心设计原则

1. **不硬编码权限**: 所有权限都来自后端配置，前端不预定义权限编码
2. **不使用mock数据**: 所有权限数据都从真实API获取
3. **页面权限为准**: 当需要按钮级别权限控制时，自动从当前页面权限获取并判断

## 权限数据流

```
后端 users 服务 -> /api/user/menu/menu-tree -> 菜单树(含权限) -> 前端权限校验
```

## 权限控制组件

### PermissionGuard (推荐)

最强大的权限校验组件，支持多种模式和高级功能。

```typescript
interface PermissionGuardProps {
  permission?: string;                    // 单个权限
  permissions?: string[];                 // 多权限数组
  requireAll?: boolean;                   // 是否需要全部权限
  mode?: 'global' | 'current-page' | 'auto'; // 检查模式
  fallback?: ReactNode;                   // 权限不足时的替代内容
  hide?: boolean;                         // 是否完全隐藏
  debug?: boolean;                        // 开发调试模式
  onAccessDenied?: (missing: string[]) => void;   // 权限不足回调
  onAccessGranted?: (permissions: string[]) => void; // 权限通过回调
  children: ReactNode;
}
```

### 基础使用

```tsx
import { PermissionGuard } from '../components/PermissionGuard';

// 基于当前页面权限控制
<PermissionGuard permission="user:create" mode="current-page">
  <Button type="primary">创建用户</Button>
</PermissionGuard>

// 权限不足时显示替代内容
<PermissionGuard 
  permission="user:delete" 
  mode="current-page"
  fallback={<Button disabled>删除(无权限)</Button>}
>
  <Button danger>删除用户</Button>
</PermissionGuard>

// 权限不足时完全隐藏
<PermissionGuard 
  permission="admin:panel" 
  mode="current-page"
  hide={true}
>
  <Button>管理员面板</Button>
</PermissionGuard>
```

### 高级功能

```tsx
// 多权限控制
<PermissionGuard 
  permissions={['user:read', 'user:update']}
  requireAll={false}  // 任一权限即可
  mode="current-page"
>
  <Button>编辑操作</Button>
</PermissionGuard>

// 自动模式 - 智能选择检查模式
<PermissionGuard 
  permission="user:create" 
  mode="auto"  // 自动判断使用页面权限还是全局权限
>
  <Button>智能权限检查</Button>
</PermissionGuard>

// 调试模式
<PermissionGuard 
  permission="user:create" 
  mode="current-page"
  debug={true}  // 开发环境下显示权限检查详情
  onAccessDenied={(missing) => console.log('缺少权限:', missing)}
>
  <Button>调试按钮</Button>
</PermissionGuard>
```

## 权限状态组件

### PermissionStatus

显示权限状态的调试组件，开发时很有用。

```tsx
import { PermissionStatus } from '../components/PermissionGuard';

// 显示当前页面权限状态
<PermissionStatus mode="current-page" />

// 显示全局权限状态
<PermissionStatus mode="global" />

// 紧凑模式
<PermissionStatus mode="current-page" compact={true} />

// 显示指定权限的状态
<PermissionStatus 
  permissions={['user:create', 'user:update', 'user:delete']} 
  mode="current-page" 
/>
```

## 权限Hook

### usePermissions

基础的权限校验Hook。

```typescript
const {
  allPermissions,           // 用户所有权限
  currentPagePermissions,   // 当前页面权限
  hasPermission,            // 全局权限检查
  hasPermissionInCurrentPage, // 页面权限检查
  loading,
  error
} = usePermissions();
```

### usePermissionCheck

便捷的权限检查Hook。

```typescript
import { usePermissionCheck } from '../components/PermissionGuard';

const { hasAccess, checkMode } = usePermissionCheck('user:create', 'auto');

return (
  <Button disabled={!hasAccess}>
    创建用户 {hasAccess ? '✓' : '✗'}
  </Button>
);
```

## 高阶组件

### withPermission

使用HOC方式为组件添加权限控制。

```typescript
import { withPermission } from '../components/PermissionGuard';

const SecureButton = withPermission(Button, {
  permission: 'user:create',
  mode: 'current-page',
  fallback: <Button disabled>权限不足</Button>
});

// 使用
<SecureButton type="primary">安全按钮</SecureButton>
```

## 实际应用场景

### 1. 表格操作列权限控制

```tsx
const columns = [
  // ... 其他列
  {
    title: '操作',
    key: 'action',
    render: (_, record) => (
      <Space>
        <PermissionGuard permission="user:update" mode="current-page">
          <Button type="link" onClick={() => handleEdit(record.id)}>
            编辑
          </Button>
        </PermissionGuard>
        
        <PermissionGuard 
          permission="user:delete" 
          mode="current-page"
          fallback={<Button type="link" disabled>删除</Button>}
        >
          <Button type="link" danger onClick={() => handleDelete(record.id)}>
            删除
          </Button>
        </PermissionGuard>
      </Space>
    ),
  },
];
```

### 2. 页面顶部操作按钮

```tsx
<div style={{ marginBottom: 16 }}>
  <Space>
    <PermissionGuard permission="user:create" mode="current-page">
      <Button type="primary" onClick={handleCreate}>
        新建用户
      </Button>
    </PermissionGuard>
    
    <PermissionGuard 
      permissions={['user:read', 'user:export']} 
      requireAll={true}
      mode="current-page"
      hide={true}
    >
      <Button onClick={handleExport}>导出</Button>
    </PermissionGuard>
  </Space>
</div>
```

### 3. 表单中的字段权限控制

```tsx
<PermissionGuard permission="user:update-sensitive" mode="current-page">
  <Form.Item label="敏感信息" name="sensitive">
    <Input placeholder="只有特定权限才能编辑" />
  </Form.Item>
</PermissionGuard>
```

### 4. 菜单项权限控制

```tsx
const menuItems = [
  {
    key: 'users',
    label: (
      <PermissionGuard permission="user:read" mode="current-page">
        <span>用户管理</span>
      </PermissionGuard>
    ),
  },
];
```

## 权限检查模式

### global (全局权限)
检查用户在整个系统中是否拥有该权限。

### current-page (当前页面权限)
检查用户在当前页面是否拥有该权限。推荐用于页面内的按钮控制。

### auto (自动模式)
智能选择检查模式：
- 如果权限存在于当前页面权限中 → 使用页面权限检查
- 如果权限不存在于当前页面权限中 → 使用全局权限检查

## 开发和调试

### 开启调试模式

```tsx
<PermissionGuard 
  permission="user:create" 
  mode="current-page"
  debug={true}  // 在控制台显示权限检查详情
>
  <Button>调试按钮</Button>
</PermissionGuard>
```

### 权限开发组件

```tsx
// 开发环境显示权限状态
{process.env.NODE_ENV === 'development' && (
  <PermissionStatus mode="current-page" />
)}
```

### 权限检查回调

```tsx
<PermissionGuard 
  permission="user:create" 
  mode="current-page"
  onAccessDenied={(missing) => {
    console.log('用户缺少权限:', missing);
    // 可以发送统计数据或显示提示
  }}
  onAccessGranted={(granted) => {
    console.log('用户拥有权限:', granted);
  }}
>
  <Button>操作按钮</Button>
</PermissionGuard>
```

## 最佳实践

### 1. 优先使用PermissionGuard

```tsx
// 推荐
<PermissionGuard permission="user:create" mode="current-page">
  <Button>创建</Button>
</PermissionGuard>

// 不推荐（功能较少）
<PermissionWrapper permission="user:create" checkMode="current-page">
  <Button>创建</Button>
</PermissionWrapper>
```

### 2. 选择合适的权限检查模式

```tsx
// 页面内按钮推荐使用 current-page 模式
<PermissionGuard permission="user:create" mode="current-page">
  <Button>页面操作</Button>
</PermissionGuard>

// 跨页面的全局操作使用 global 模式
<PermissionGuard permission="admin:system" mode="global">
  <Button>系统设置</Button>
</PermissionGuard>

// 不确定时使用 auto 模式
<PermissionGuard permission="user:create" mode="auto">
  <Button>智能检查</Button>
</PermissionGuard>
```

### 3. 提供友好的降级体验

```tsx
<PermissionGuard 
  permission="user:delete"
  mode="current-page"
  fallback={<Button disabled title="您没有删除权限">删除</Button>}
>
  <Button danger>删除</Button>
</PermissionGuard>
```

### 4. 使用组合权限控制复杂场景

```tsx
<PermissionGuard 
  permissions={['user:read', 'user:export']}
  requireAll={true}
  mode="current-page"
  fallback={<Button disabled>权限不足</Button>}
>
  <Button>高级导出</Button>
</PermissionGuard>
```

## 注意事项

1. **页面权限为空**: 如果页面没有配置权限，所有基于页面权限的检查都会返回false
2. **路径匹配**: 权限检查基于当前路由路径，确保菜单配置的path与实际路由匹配
3. **权限更新**: 权限数据跟随菜单数据自动更新，无需手动刷新
4. **性能考虑**: 权限检查都是内存操作，性能优异，无需担心频繁调用
5. **调试信息**: 生产环境下debug模式自动禁用，不会影响性能

## 升级指南

如果你的项目之前使用其他权限控制方式，可以按以下步骤升级：

### 从PermissionWrapper升级到PermissionGuard

```tsx
// 旧版本
<PermissionWrapper permission="user:create" checkMode="current-page">
  <Button>创建</Button>
</PermissionWrapper>

// 新版本
<PermissionGuard permission="user:create" mode="current-page">
  <Button>创建</Button>
</PermissionGuard>
```

### 从硬编码权限迁移

```tsx
// 旧版本 - 硬编码权限
const PERMISSIONS = {
  USER_CREATE: 'user:create',
  USER_UPDATE: 'user:update',
};

if (hasPermission(PERMISSIONS.USER_CREATE)) {
  // ...
}

// 新版本 - 动态权限
<PermissionGuard permission="user:create" mode="current-page">
  <Button>创建</Button>
</PermissionGuard>
```

这样就完成了向新权限系统的升级！