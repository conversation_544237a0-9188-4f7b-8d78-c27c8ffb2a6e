/**
 * 统一登录跳转的React Hook
 * 提供组件中使用的便捷方法
 */

import { useCallback, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  LoginRedirect, 
  LoginRedirectOptions, 
  ForbiddenOptions,
  handleUnauthorized, 
  handleTokenExpired, 
  requireLogin, 
  handleLogout, 
  forceRelogin,
  handleForbidden
} from '../utils/auth';
import { clearAuth } from '../utils/request';

/**
 * 统一认证跳转Hook
 */
export const useAuthRedirect = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // 初始化登录跳转工具
  useEffect(() => {
    LoginRedirect.setNavigate(navigate);
    LoginRedirect.setClearAuthCallback(clearAuth);
  }, [navigate]);

  // 通用登录跳转方法
  const redirectToLogin = useCallback((options?: LoginRedirectOptions) => {
    LoginRedirect.redirect({
      ...options,
      returnUrl: options?.returnUrl || `${location.pathname}${location.search}${location.hash}`,
    });
  }, [location]);

  // 处理权限不足（403）
  const onForbidden = useCallback((options?: ForbiddenOptions) => {
    handleForbidden(options);
  }, []);

  // 处理未授权（401）
  const onUnauthorized = useCallback((options?: Omit<LoginRedirectOptions, 'reason'>) => {
    handleUnauthorized({
      ...options,
      returnUrl: options?.returnUrl || `${location.pathname}${location.search}${location.hash}`,
    });
  }, [location]);

  // 处理Token过期
  const onTokenExpired = useCallback((options?: Omit<LoginRedirectOptions, 'reason'>) => {
    handleTokenExpired({
      ...options,
      returnUrl: options?.returnUrl || `${location.pathname}${location.search}${location.hash}`,
    });
  }, [location]);

  // 要求登录
  const onRequireLogin = useCallback((options?: Omit<LoginRedirectOptions, 'reason'>) => {
    requireLogin({
      ...options,
      returnUrl: options?.returnUrl || `${location.pathname}${location.search}${location.hash}`,
    });
  }, [location]);

  // 处理退出登录
  const onLogout = useCallback((options?: Omit<LoginRedirectOptions, 'reason'>) => {
    handleLogout(options);
  }, []);

  // 强制重新登录
  const onForceRelogin = useCallback((options?: Omit<LoginRedirectOptions, 'reason'>) => {
    forceRelogin({
      ...options,
      returnUrl: options?.returnUrl || `${location.pathname}${location.search}${location.hash}`,
    });
  }, [location]);

  // 检查是否应该跳转到登录页面
  const shouldRedirectToLogin = useCallback((path?: string) => {
    return LoginRedirect.shouldRedirectToLogin(path || location.pathname);
  }, [location.pathname]);

  return {
    // 通用方法
    redirectToLogin,
    
    // 具体场景方法
    onUnauthorized,
    onForbidden,      // 新增：处理403权限不足
    onTokenExpired,
    onRequireLogin,
    onLogout,
    onForceRelogin,
    
    // 工具方法
    shouldRedirectToLogin,
  };
};

/**
 * 为兼容性提供的简化Hook
 */
export const useLoginRedirect = () => {
  const { redirectToLogin, onUnauthorized, onForbidden } = useAuthRedirect();
  return { redirectToLogin, handleUnauthorized: onUnauthorized, handleForbidden: onForbidden };
};