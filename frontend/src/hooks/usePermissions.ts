import { useLocation } from 'react-router-dom';
import { useMenu } from './useMenu';

/**
 * 权限校验Hook
 * 基于当前页面的权限数据进行动态校验，优先从菜单树的permissions字段获取权限
 * 当permissions包含"*"时，表示拥有所有权限
 */
export const usePermissions = () => {
  const location = useLocation();
  const { 
    allPermissions, 
    hasPermission, 
    hasAnyPermission, 
    hasAllPermissions, 
    getMenuPermissions,
    loading,
    error
  } = useMenu();

  // 获取当前页面的权限
  const currentPagePermissions = getMenuPermissions(location.pathname);

  // 检查是否拥有全部权限（"*" 表示拥有所有权限）
  const hasAllAccess = (): boolean => {
    return currentPagePermissions.includes('*');
  };

  // 增强的权限检查函数 - 当前页面权限检查
  const hasPermissionInCurrentPage = (permission: string): boolean => {
    // 如果当前页面权限包含 "*"，表示拥有所有权限
    if (hasAllAccess()) {
      return true;
    }
    return currentPagePermissions.includes(permission);
  };

  const hasAnyPermissionInCurrentPage = (permissions: string[]): boolean => {
    // 如果当前页面权限包含 "*"，表示拥有所有权限
    if (hasAllAccess()) {
      return true;
    }
    return permissions.some(permission => currentPagePermissions.includes(permission));
  };

  const hasAllPermissionsInCurrentPage = (permissions: string[]): boolean => {
    // 如果当前页面权限包含 "*"，表示拥有所有权限
    if (hasAllAccess()) {
      return true;
    }
    return permissions.every(permission => currentPagePermissions.includes(permission));
  };

  // 增强的全局权限检查函数
  const enhancedHasPermission = (permission: string): boolean => {
    // 如果全局权限包含 "*"，表示拥有所有权限
    if (allPermissions.includes('*')) {
      return true;
    }
    return hasPermission(permission);
  };

  const enhancedHasAnyPermission = (permissions: string[]): boolean => {
    // 如果全局权限包含 "*"，表示拥有所有权限
    if (allPermissions.includes('*')) {
      return true;
    }
    return hasAnyPermission(permissions);
  };

  const enhancedHasAllPermissions = (permissions: string[]): boolean => {
    // 如果全局权限包含 "*"，表示拥有所有权限
    if (allPermissions.includes('*')) {
      return true;
    }
    return hasAllPermissions(permissions);
  };

  return {
    // 权限数据
    allPermissions,
    currentPagePermissions,
    loading,
    error,
    
    // 增强的基础权限校验函数 - 支持 "*" 通配符
    hasPermission: enhancedHasPermission,
    hasAnyPermission: enhancedHasAnyPermission,
    hasAllPermissions: enhancedHasAllPermissions,
    getMenuPermissions,
    
    // 增强的当前页面权限校验 - 支持 "*" 通配符
    hasPermissionInCurrentPage,
    hasAnyPermissionInCurrentPage,
    hasAllPermissionsInCurrentPage,
    
    // 新增功能
    hasAllAccess, // 检查是否拥有全部权限
  };
};

export default usePermissions;
