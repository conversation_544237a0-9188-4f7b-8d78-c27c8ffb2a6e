import { useState, useEffect, useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { getUserMenuTree, MenuTreeNode } from '../services/menu';
import { convertMenuTreeToAntdMenuItems } from '../utils/menuUtils';
import { useAuth } from '../contexts/AuthContext';
import type { MenuProps } from 'antd';

// 菜单项接口
export interface MenuItem {
  key: string;
  label: string;
  path?: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
  type?: 'divider';
}

// 菜单Hook返回值
export interface UseMenuReturn {
  menuItems: MenuProps['items'];
  loading: boolean;
  error: string | null;
  selectedKeys: string[];
  openKeys: string[];
  breadcrumbs: Array<{ title: string; path?: string }>;
  currentPageTitle: string;
  refreshMenu: () => Promise<void>;
  menuTree: MenuTreeNode[]; // 原始菜单树数据
  // 权限相关
  allPermissions: string[]; // 用户拥有的所有权限
  hasPermission: (permission: string) => boolean; // 检查单个权限
  hasAnyPermission: (permissions: string[]) => boolean; // 检查是否有任意一个权限
  hasAllPermissions: (permissions: string[]) => boolean; // 检查是否有全部权限
  getMenuPermissions: (menuPath: string) => string[]; // 获取指定菜单路径的权限
}

/**
 * 菜单管理Hook
 */
export const useMenu = (): UseMenuReturn => {
  const [menuTree, setMenuTree] = useState<MenuTreeNode[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  // 获取菜单数据
  const fetchMenuTree = async () => {
    if (!isAuthenticated) {
      
      setMenuTree([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const tree = await getUserMenuTree();
      setMenuTree(tree);
    } catch (err) {
      console.error('Failed to fetch menu tree:', err);
      setError('获取菜单失败');
      setMenuTree([]);
    } finally {
      setLoading(false);
    }
  };

  // 初始化和认证状态变化时获取菜单
  useEffect(() => {
    fetchMenuTree();
  }, [isAuthenticated]);

  // 将菜单树转换为Ant Design Menu组件需要的格式
  const menuItems = useMemo(() => {
    return convertMenuTreeToAntdMenuItems(menuTree);
  }, [menuTree]);

  // 计算当前选中的菜单项
  const selectedKeys = useMemo(() => {
    const currentPath = location.pathname;
    
    // 递归查找匹配的菜单项
    const findMatchingKey = (nodes: MenuTreeNode[], path: string): string | null => {
      for (const node of nodes) {
        // 精确匹配
        if (node.path === path) {
          return node.name;
        }
        
        // 前缀匹配（用于子路由）
        if (node.path && path.startsWith(node.path + '/')) {
          return node.name;
        }
        
        // 递归查找子菜单
        if (node.children) {
          const childMatch = findMatchingKey(node.children, path);
          if (childMatch) {
            return childMatch;
          }
        }
      }
      return null;
    };

    const matchedKey = findMatchingKey(menuTree, currentPath);
    return matchedKey ? [matchedKey] : [];
  }, [menuTree, location.pathname]);

  // 计算需要展开的菜单项
  const openKeys = useMemo(() => {
    const currentPath = location.pathname;
    const openKeySet = new Set<string>();
    
    // 递归查找包含当前路径的父菜单
    const findParentKeys = (nodes: MenuTreeNode[], path: string): boolean => {
      for (const node of nodes) {
        if (node.children) {
          // 检查子菜单中是否包含当前路径
          const hasMatchingChild = findParentKeys(node.children, path);
          if (hasMatchingChild) {
            openKeySet.add(node.name);
            return true;
          }
        }
        
        // 检查当前项是否匹配
        if (node.path === path || (node.path && path.startsWith(node.path + '/'))) {
          return true;
        }
      }
      return false;
    };

    findParentKeys(menuTree, currentPath);
    return Array.from(openKeySet);
  }, [menuTree, location.pathname]);

  // 计算面包屑导航
  const breadcrumbs = useMemo(() => {
    const currentPath = location.pathname;
    const breadcrumbList: Array<{ title: string; path?: string }> = [];
    
    // 添加首页
    breadcrumbList.push({ title: '首页', path: '/dashboard' });
    
    // 递归查找当前路径的面包屑
    const findBreadcrumbs = (nodes: MenuTreeNode[], path: string, currentBreadcrumbs: Array<{ title: string; path?: string }>): boolean => {
      for (const node of nodes) {
        const newBreadcrumbs = [...currentBreadcrumbs, { title: node.display_name, path: node.path }];
        
        if (node.path === path) {
          breadcrumbList.push(...newBreadcrumbs.slice(1)); // 排除首页重复
          return true;
        }
        
        if (node.path && path.startsWith(node.path + '/')) {
          breadcrumbList.push(...newBreadcrumbs.slice(1));
          return true;
        }
        
        if (node.children && findBreadcrumbs(node.children, path, newBreadcrumbs)) {
          return true;
        }
      }
      return false;
    };

    findBreadcrumbs(menuTree, currentPath, []);
    return breadcrumbList;
  }, [menuTree, location.pathname]);

  // 计算当前页面标题
  const currentPageTitle = useMemo(() => {
    const currentPath = location.pathname;
    
    // 递归查找当前路径对应的标题
    const findTitle = (nodes: MenuTreeNode[]): string | null => {
      for (const node of nodes) {
        if (node.path === currentPath) {
          return node.display_name;
        }
        
        if (node.path && currentPath.startsWith(node.path + '/')) {
          return node.display_name;
        }
        
        if (node.children) {
          const childTitle = findTitle(node.children);
          if (childTitle) {
            return childTitle;
          }
        }
      }
      return null;
    };

    return findTitle(menuTree) || '仪表盘';
  }, [menuTree, location.pathname]);

  // 收集所有权限数据
  const allPermissions = useMemo(() => {
    const permissionSet = new Set<string>();
    
    const collectPermissions = (nodes: MenuTreeNode[]) => {
      nodes.forEach(node => {
        if (node.permissions) {
          node.permissions.forEach(permission => permissionSet.add(permission));
        }
        if (node.children) {
          collectPermissions(node.children);
        }
      });
    };
    
    collectPermissions(menuTree);
    return Array.from(permissionSet);
  }, [menuTree]);

  // 权限校验函数
  const hasPermission = (permission: string): boolean => {
    return allPermissions.includes(permission);
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => allPermissions.includes(permission));
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => allPermissions.includes(permission));
  };

  // 获取指定菜单路径的权限
  const getMenuPermissions = (menuPath: string): string[] => {
    const findMenuPermissions = (nodes: MenuTreeNode[], path: string): string[] => {
      for (const node of nodes) {
        if (node.path === path) {
          return node.permissions || [];
        }
        if (node.children) {
          const childPermissions = findMenuPermissions(node.children, path);
          if (childPermissions.length > 0) {
            return childPermissions;
          }
        }
      }
      return [];
    };
    
    return findMenuPermissions(menuTree, menuPath);
  };
  return {
    menuItems,
    loading,
    error,
    selectedKeys,
    openKeys,
    breadcrumbs,
    currentPageTitle,
    refreshMenu: fetchMenuTree,
    menuTree, // 返回原始菜单树数据
    // 权限相关
    allPermissions,
    hasPermission,
    hasAnyPermission, 
    hasAllPermissions,
    getMenuPermissions,
  };
};

/**
 * 菜单权限Hook - 优化版本，使用菜单树中的权限数据
 */
export const useMenuPermissions = (menuPath?: string) => {
  const { getMenuPermissions, hasPermission, hasAnyPermission, hasAllPermissions, loading: menuLoading } = useMenu();
  
  // 获取指定路径的权限
  const permissions = useMemo(() => {
    return menuPath ? getMenuPermissions(menuPath) : [];
  }, [menuPath, getMenuPermissions]);

  // 权限检查函数
  const hasPermissionForMenu = (permission: string): boolean => {
    return hasPermission(permission);
  };

  const hasAnyPermissionForMenu = (permissionList: string[]): boolean => {
    return hasAnyPermission(permissionList);
  };

  const hasAllPermissionsForMenu = (permissionList: string[]): boolean => {
    return hasAllPermissions(permissionList);
  };

  return {
    permissions,
    loading: menuLoading,
    hasPermission: hasPermissionForMenu,
    hasAnyPermission: hasAnyPermissionForMenu,
    hasAllPermissions: hasAllPermissionsForMenu,
    refreshPermissions: () => {
      // 不再需要单独刷新权限，因为权限跟随菜单数据
      // 可以通过refreshMenu来刷新整个菜单和权限
      console.warn('useMenuPermissions: Use refreshMenu from useMenu hook instead');
    },
  };
};
