/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

/* 深色模式背景 */
body[data-theme='dark'] {
  background-color: #141414;
}

/* 滚动条样式 - 更细更现代 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 2px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 深色模式滚动条 */
body[data-theme='dark'] ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
}

body[data-theme='dark'] ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 卡片样式 - Feishu风格 */
.ant-card {
  border: 1px solid transparent;
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
  transition: box-shadow 0.2s ease;
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
}

/* 按钮样式 - 更现代的设计 */
.ant-btn {
  position: relative;
  overflow: hidden;
  border-radius: 8px !important;
  font-weight: 500;
  transition: all 0.2s ease;
}

.ant-btn-primary {
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.ant-btn-primary:hover {
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transform: translateY(-1px);
}

/* 表格样式优化 */
.ant-table {
  border-radius: 12px !important;
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background: #fafafa !important;
  border-bottom: 1px solid #e8e8e8 !important;
  font-weight: 600;
}

body[data-theme='dark'] .ant-table-thead > tr > th {
  background: #1f1f1f !important;
  border-bottom: 1px solid #303030 !important;
}

.ant-table-tbody > tr:hover > td {
  background: #f8f9fa !important;
}

body[data-theme='dark'] .ant-table-tbody > tr:hover > td {
  background: #1a1a1a !important;
}

/* 紧凑布局优化 */
.ant-layout-sider {
  box-shadow: 2px 0 8px rgba(0,0,0,0.15) !important;
}

/* 菜单项样式优化 - Feishu风格 */
.ant-menu-item {
  margin: 1px 6px !important;
  height: 36px !important;
  line-height: 36px !important;
  border-radius: 8px !important;
  transition: all 0.2s ease !important;
}

.ant-menu-submenu {
  margin: 1px 6px !important;
}

.ant-menu-submenu-title {
  height: 36px !important;
  line-height: 36px !important;
  border-radius: 8px !important;
  margin: 0 !important;
  transition: all 0.2s ease !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .ant-layout-sider-collapsed .ant-menu-item .ant-menu-title-content,
  .ant-layout-sider-collapsed .ant-menu-submenu-title .ant-menu-title-content {
    opacity: 0;
  }
}

/* 页面内容区域优化 - 更简洁的背景 */
.main-content {
  background: transparent;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

/* 深色模式 */
body[data-theme='dark'] .main-content {
  background: transparent;
}

/* 统计卡片样式 */
.statistic-card {
  min-width: 0;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

/* 搜索框样式 - 简洁统一 */
.ant-input-search {
  /* 使用 Ant Design 默认样式，只做必要的主题适配 */
}

/* 分页组件样式 */
.ant-pagination {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

/* 加载动画 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .ant-card {
    margin-bottom: 16px;
  }
  
  .ant-table {
    font-size: 12px;
  }
  
  .ant-statistic-title {
    font-size: 12px;
  }
  
  .ant-statistic-content {
    font-size: 20px;
  }
  .statistic-card {
    margin-bottom: 8px;
  }
  .ant-statistic-content {
    font-size: 20px !important;
  }
  .ant-statistic-title {
    font-size: 12px !important;
  }
}

/* 大屏适配 */
@media (min-width: 1600px) {
  /* .ant-layout-content {
    max-width: 1400px;
    margin: 0 auto;
  } */
  .ant-card {
    border-radius: 12px;
  }
  .ant-table {
    border-radius: 12px;
    overflow: hidden;
  }
}

/* 分隔线样式 */
.divider-custom {
  margin: 24px 0;
  border: none;
  height: 1px;
  background: linear-gradient(90deg, transparent, #d9d9d9, transparent);
}

body[data-theme='dark'] .divider-custom {
  background: linear-gradient(90deg, transparent, #434343, transparent);
}

/* 阴影效果 */
.shadow-sm {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.shadow-md {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.shadow-lg {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

body[data-theme='dark'] .shadow-sm {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

body[data-theme='dark'] .shadow-md {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

body[data-theme='dark'] .shadow-lg {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5);
}

/* 圆角样式 */
.rounded-sm {
  border-radius: 6px;
}

.rounded-md {
  border-radius: 8px;
}

.rounded-lg {
  border-radius: 12px;
}

.rounded-xl {
  border-radius: 16px;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 16px; }
.m-4 { margin: 24px; }
.m-5 { margin: 32px; }

.p-0 { padding: 0; }
.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 16px; }
.p-4 { padding: 24px; }
.p-5 { padding: 32px; }

/* 文本对齐 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* 显示隐藏 */
.hidden { display: none; }
.block { display: block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }

.user-info-dropdown {
  min-width: 0;
  display: flex;
  align-items: center;
  padding: 0 12px;
  height: 48px;
  box-sizing: border-box;
  overflow: hidden;
  flex-shrink: 0;
}
.user-info-dropdown span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}
@media (max-width: 600px) {
  .user-info-dropdown span {
    display: none;
  }
}

/* 强制覆盖 Ant Design message 弹窗所有层级宽度和显示方式 */
.ant-message {
  text-align: center !important;
}
.ant-message-notice,
.ant-message-notice-error,
.ant-message-notice-success,
.ant-message-notice-info,
.ant-message-notice-warning {
  width: auto !important;
  display: inline-block !important;
  margin: 8px auto !important;
  box-sizing: border-box !important;
  text-align: center !important;
}
.ant-message-notice-content {
  max-width: 416px !important;
  min-width: 120px !important;
  display: inline-block !important;
  margin: 0 auto !important;
  box-sizing: border-box !important;
}

/* 只滚动内容区 */
.main-content {
  /* 由JSX内联height控制高度，overflow: auto保证只滚动内容区 */
  /* 可根据需要微调padding/margin等 */
}

.compact-table .ant-table-cell {
  padding-top: 8px !important;
  padding-bottom: 6px !important;
  padding-left: 8px !important;
  padding-right: 8px !important;
}




/* 选择器样式 */
.ant-select-selector {
  border-radius: 8px !important;
  border: 1px solid #e8e8e8 !important;
}

.ant-select-focused .ant-select-selector {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
}

/* 模态框优化 */
.ant-modal {
  border-radius: 16px !important;
}

.ant-modal-content {
  border-radius: 16px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
}

.ant-modal-header {
  border-radius: 16px 16px 0 0 !important;
  padding: 20px 24px 16px !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.ant-modal-body {
  padding: 20px 24px !important;
}

.ant-modal-footer {
  padding: 16px 24px 20px !important;
  border-top: 1px solid #f0f0f0 !important;
  border-radius: 0 0 16px 16px !important;
}

/* 通知样式优化 */
.ant-notification {
  border-radius: 12px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
} 