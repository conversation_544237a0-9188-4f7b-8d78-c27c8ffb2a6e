import React, { ReactNode } from 'react';
import { useLocation } from 'react-router-dom';
import { usePermissions } from '../hooks/usePermissions';

/**
 * 权限校验组件的Props接口
 */
export interface PermissionGuardProps {
  /** 单个权限代码 */
  permission?: string;
  /** 多个权限代码数组 */
  permissions?: string[];
  /** 是否需要所有权限都满足，默认false（满足任一权限即可） */
  requireAll?: boolean;
  /** 权限检查模式：global-全局权限检查，current-page-当前页面权限检查 */
  mode?: 'global' | 'current-page';
  /** 权限校验失败时的替代内容 */
  fallback?: ReactNode;
  /** 权限校验失败时是否完全隐藏，为true时fallback无效 */
  hide?: boolean;
  /** 是否在开发环境显示调试信息 */
  debug?: boolean;
  /** 权限校验失败时的回调函数 */
  onAccessDenied?: (missingPermissions: string[]) => void;
  /** 权限校验成功时的回调函数 */
  onAccessGranted?: (permissions: string[]) => void;
  /** 子元素 */
  children: ReactNode;
}

/**
 * 权限校验组件
 * 用于包装需要权限控制的UI元素，只有通过权限校验才能访问
 */
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permission,
  permissions,
  requireAll = false,
  mode = 'global',
  fallback = null,
  hide = false,
  debug = false,
  onAccessDenied,
  onAccessGranted,
  children,
}) => {
  const location = useLocation();
  const {
    currentPagePermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasPermissionInCurrentPage,
    hasAnyPermissionInCurrentPage,
    hasAllPermissionsInCurrentPage,
    hasAllAccess,
    loading
  } = usePermissions();

  // 如果权限数据还在加载中
  if (loading) {
    return debug ? <div style={{ color: '#999', fontSize: '12px' }}>权限验证中...</div> : null;
  }

  // 确定要检查的权限列表
  const permissionsToCheck = permission ? [permission] : (permissions || []);
  
  if (permissionsToCheck.length === 0) {
    if (debug) {
      console.warn('PermissionGuard: No permissions specified, allowing access');
    }
    return <>{children}</>;
  }

  // 根据模式选择检查函数
  let hasAccess = false;

  if (mode === 'current-page') {
    hasAccess = requireAll 
      ? hasAllPermissionsInCurrentPage(permissionsToCheck)
      : hasAnyPermissionInCurrentPage(permissionsToCheck);
  } else {
    hasAccess = requireAll 
      ? hasAllPermissions(permissionsToCheck)
      : hasAnyPermission(permissionsToCheck);
  }

  // 调试信息
  if (debug && process.env.NODE_ENV === 'development') {
    const debugInfo = {
      path: location.pathname,
      permissions: permissionsToCheck,
      requireAll,
      mode,
      hasAccess,
      currentPagePermissions: currentPagePermissions.length,
      hasAllAccess: mode === 'current-page' ? hasAllAccess() : false,
      hasWildcardPermission: mode === 'current-page' ? currentPagePermissions.includes('*') : false
    };
    console.group(`🔐 PermissionGuard Debug - ${mode} mode`);
    console.table(debugInfo);
    if (mode === 'current-page') {
      console.log('Current page permissions:', currentPagePermissions);
      if (currentPagePermissions.includes('*')) {
        console.log('✨ Found wildcard permission "*" - granting all access');
      }
    }
    console.groupEnd();
  }

  // 权限校验结果处理
  if (hasAccess) {
    onAccessGranted?.(permissionsToCheck);
    return <>{children}</>;
  } else {
    // 找出缺失的权限
    const missingPermissions = permissionsToCheck.filter(p => 
      mode === 'current-page' 
        ? !currentPagePermissions.includes(p)
        : !hasPermission(p)
    );
    
    onAccessDenied?.(missingPermissions);

    if (hide) {
      return null;
    }

    return <>{fallback}</> || null;
  }
};

/**
 * 权限状态显示组件
 * 用于调试和开发时显示权限状态
 */
export interface PermissionStatusProps {
  permissions?: string[];
  mode?: 'global' | 'current-page';
  compact?: boolean;
}

export const PermissionStatus: React.FC<PermissionStatusProps> = ({
  permissions,
  mode = 'current-page',
  compact = false
}) => {
  const { currentPagePermissions, hasPermission, hasPermissionInCurrentPage } = usePermissions();
  const location = useLocation();

  const checkPermissions = permissions || (mode === 'current-page' ? currentPagePermissions : []);
  const checkFunction = mode === 'current-page' ? hasPermissionInCurrentPage : hasPermission;

  if (compact) {
    return (
      <div style={{ 
        fontSize: '12px', 
        color: '#666',
        padding: '4px 8px',
        background: '#f9f9f9',
        borderRadius: '4px',
        display: 'inline-block'
      }}>
        {mode === 'current-page' ? '页面权限' : '全局权限'}: {checkPermissions.length}个
      </div>
    );
  }

  return (
    <div style={{ 
      padding: '12px',
      border: '1px solid #d9d9d9',
      borderRadius: '6px',
      background: '#fafafa',
      fontSize: '12px',
      color: '#666',
      marginBottom: '16px'
    }}>
      <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>
        权限状态 ({location.pathname})
      </div>
      <div>检查模式: {mode === 'current-page' ? '当前页面权限' : '全局权限'}</div>
      <div>权限数量: {checkPermissions.length}</div>
      <div style={{ marginTop: '8px' }}>
        {checkPermissions.map((permission: string) => (
          <span
            key={permission}
            style={{
              display: 'inline-block',
              margin: '2px',
              padding: '2px 6px',
              borderRadius: '3px',
              background: checkFunction(permission) ? '#e6f7ff' : '#fff1f0',
              color: checkFunction(permission) ? '#1890ff' : '#ff4d4f',
              border: `1px solid ${checkFunction(permission) ? '#91d5ff' : '#ffa39e'}`,
              fontSize: '11px'
            }}
          >
            {permission} {checkFunction(permission) ? '✓' : '✗'}
          </span>
        ))}
      </div>
    </div>
  );
};

/**
 * 权限HOC - 高阶组件方式使用权限控制
 */
export function withPermission<T extends object>(
  Component: React.ComponentType<T>,
  options: {
    permission?: string;
    permissions?: string[];
    requireAll?: boolean;
    mode?: 'global' | 'current-page';
    fallback?: ReactNode;
  }
) {
  return React.forwardRef<any, T>((props: any, ref: any) => {
    return (
      <PermissionGuard {...options}>
        <Component {...props} ref={ref} />
      </PermissionGuard>
    );
  });
}

// 便捷的权限检查Hooks
export const usePermissionCheck = (
  permission: string | string[], 
  mode: 'global' | 'current-page' = 'global'
) => {
  const {
    hasPermission,
    hasPermissionInCurrentPage,
    hasAnyPermission,
    hasAnyPermissionInCurrentPage,
  } = usePermissions();

  const permissions = Array.isArray(permission) ? permission : [permission];
  
  const hasAccess = mode === 'current-page' 
    ? (permissions.length === 1 ? hasPermissionInCurrentPage(permissions[0]) : hasAnyPermissionInCurrentPage(permissions))
    : (permissions.length === 1 ? hasPermission(permissions[0]) : hasAnyPermission(permissions));

  return {
    hasAccess,
    checkMode: mode,
    permissions
  };
};

export default PermissionGuard;