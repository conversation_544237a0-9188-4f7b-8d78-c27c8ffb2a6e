/**
 * 使用统一登录跳转的示例组件
 * 展示如何在组件中使用新的认证跳转系统
 */

import React from 'react';
import { Button, Space, Alert } from 'antd';
import { useAuthRedirect } from '../hooks/useAuthRedirect';

/**
 * 认证示例组件
 */
export const AuthRedirectExample: React.FC = () => {
  const {
    redirectToLogin,
    onUnauthorized,
    onForbidden,      // 新增：403权限不足处理
    onTokenExpired,
    onRequireLogin,
    onLogout,
    onForceRelogin,
    shouldRedirectToLogin,
  } = useAuthRedirect();

  return (
    <div style={{ padding: '20px' }}>
      <Alert
        message="统一登录跳转示例"
        description="这些按钮展示了如何在组件中使用统一的登录跳转功能，包括正确区分401和403错误"
        type="info"
        style={{ marginBottom: '20px' }}
      />
      
      <Space direction="vertical" size="middle">
        <Space wrap>
          <Button 
            onClick={() => redirectToLogin()}
            type="default"
          >
            通用登录跳转
          </Button>
          
          <Button 
            onClick={() => onRequireLogin({ message: '请先登录以访问此功能' })}
            type="primary"
          >
            要求登录
          </Button>
          
          <Button 
            onClick={() => onUnauthorized()}
            danger
          >
            处理未授权(401) - 跳转登录
          </Button>
          
          <Button 
            onClick={() => onForbidden({ message: '您没有权限执行此操作' })}
            style={{ backgroundColor: '#ff7875', borderColor: '#ff7875', color: 'white' }}
          >
            处理权限不足(403) - 只提示不跳转
          </Button>
          
          <Button 
            onClick={() => onTokenExpired()}
            danger
          >
            处理Token过期
          </Button>
          
          <Button 
            onClick={() => onForceRelogin({ message: '安全策略要求重新登录' })}
            danger
          >
            强制重新登录
          </Button>
          
          <Button 
            onClick={() => onLogout()}
            type="default"
          >
            退出登录
          </Button>
        </Space>
        
        <div>
          <p><strong>当前页面是否需要登录:</strong> {shouldRedirectToLogin() ? '是' : '否'}</p>
        </div>
        
        <Alert
          message="401 vs 403 错误处理说明"
          description={
            <ul>
              <li><strong>401 未授权:</strong> 用户未登录或token过期，需要跳转到登录页面重新认证</li>
              <li><strong>403 权限不足:</strong> 用户已登录但权限不足，只显示错误提示，不跳转登录页</li>
            </ul>
          }
          type="warning"
          style={{ marginBottom: '10px' }}
        />
        
        <Alert
          message="使用说明"
          description={
            <ul>
              <li><strong>通用登录跳转:</strong> 基本的跳转到登录页面</li>
              <li><strong>要求登录:</strong> 需要登录才能访问某功能时使用</li>
              <li><strong>处理未授权(401):</strong> 收到401响应时调用，会跳转到登录页</li>
              <li><strong>处理权限不足(403):</strong> 收到403响应时调用，只显示提示不跳转</li>
              <li><strong>处理Token过期:</strong> Token过期时调用</li>
              <li><strong>强制重新登录:</strong> 安全策略要求重新认证时使用</li>
              <li><strong>退出登录:</strong> 用户主动退出时调用</li>
            </ul>
          }
          type="info"
        />
      </Space>
    </div>
  );
};

export default AuthRedirectExample;