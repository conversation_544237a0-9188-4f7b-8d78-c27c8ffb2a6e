/**
 * 401/403错误处理测试组件
 * 用于验证系统正确区分401和403错误的处理逻辑
 */

import React, { useState } from 'react';
import { Button, Space, Alert, Card, Typography, Divider } from 'antd';
import { apiService } from '../utils/request';
import { useAuthRedirect } from '../hooks/useAuthRedirect';

const { Title, Paragraph, Text } = Typography;

/**
 * 错误处理测试组件
 */
export const ErrorHandlingTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const { onUnauthorized, onForbidden } = useAuthRedirect();

  // 模拟401错误测试
  const test401Error = async () => {
    try {
      addTestResult('测试401错误：模拟未授权请求...');
      // 模拟一个会返回401的请求
      await apiService.get('/test-endpoint-401');
    } catch (error: any) {
      if (error.response?.status === 401) {
        addTestResult('✅ 401错误被正确拦截，应该跳转到登录页面');
        // 实际的401错误会被拦截器自动处理，但这里我们手动演示
        setTimeout(() => {
          onUnauthorized({ message: '测试：模拟401未授权错误' });
        }, 1000);
      } else {
        addTestResult('❌ 401错误测试失败');
      }
    }
  };

  // 模拟403错误测试
  const test403Error = async () => {
    try {
      addTestResult('测试403错误：模拟权限不足请求...');
      // 模拟一个会返回403的请求
      await apiService.get('/test-endpoint-403');
    } catch (error: any) {
      if (error.response?.status === 403) {
        addTestResult('✅ 403错误被正确拦截，应该显示权限不足提示（不跳转登录）');
        // 实际的403错误会被拦截器自动处理，但这里我们手动演示
        setTimeout(() => {
          onForbidden({ 
            message: '测试：模拟403权限不足错误', 
            error: error,
            showDetails: true 
          });
        }, 1000);
      } else {
        addTestResult('❌ 403错误测试失败');
      }
    }
  };

  // 添加测试结果
  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // 清空测试结果
  const clearResults = () => {
    setTestResults([]);
  };

  // 测试手动触发401处理
  const manualTest401 = () => {
    addTestResult('手动触发401处理：应该跳转到登录页面');
    onUnauthorized({ message: '手动测试401未授权错误' });
  };

  // 测试手动触发403处理
  const manualTest403 = () => {
    addTestResult('手动触发403处理：应该显示权限不足提示（不跳转）');
    onForbidden({ message: '手动测试403权限不足错误' });
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px' }}>
      <Title level={3}>401/403错误处理测试</Title>
      
      <Alert
        message="测试说明"
        description="这个测试组件用于验证系统是否正确区分401和403错误的处理逻辑"
        type="info"
        style={{ marginBottom: '20px' }}
        showIcon
      />

      <Card title="测试场景" style={{ marginBottom: '20px' }}>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div>
            <Title level={5}>自动测试（模拟API请求）</Title>
            <Space>
              <Button onClick={test401Error} type="primary" danger>
                测试401错误处理
              </Button>
              <Button onClick={test403Error} type="default" danger>
                测试403错误处理
              </Button>
            </Space>
            <Paragraph style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
              注意：这些测试会发送真实的API请求，可能会返回404（因为测试端点不存在），但演示了错误处理流程
            </Paragraph>
          </div>
          
          <Divider />
          
          <div>
            <Title level={5}>手动测试（直接触发处理器）</Title>
            <Space>
              <Button onClick={manualTest401} type="primary">
                手动触发401处理
              </Button>
              <Button onClick={manualTest403} type="default">
                手动触发403处理
              </Button>
            </Space>
          </div>
        </Space>
      </Card>

      <Card title="预期行为对比">
        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
          <div>
            <Title level={5} style={{ color: '#ff4d4f' }}>401 未授权错误</Title>
            <ul>
              <li>清除本地认证信息</li>
              <li>跳转到登录页面</li>
              <li>保存当前页面地址用于登录后回跳</li>
              <li>显示"登录已过期"提示</li>
            </ul>
          </div>
          <div>
            <Title level={5} style={{ color: '#faad14' }}>403 权限不足错误</Title>
            <ul>
              <li>不清除认证信息</li>
              <li>不跳转到登录页面</li>
              <li>只显示权限不足的错误提示</li>
              <li>记录错误详情到控制台</li>
            </ul>
          </div>
        </div>
      </Card>

      {testResults.length > 0 && (
        <Card 
          title="测试结果" 
          style={{ marginTop: '20px' }}
          extra={
            <Button size="small" onClick={clearResults}>
              清空结果
            </Button>
          }
        >
          <div style={{ 
            background: '#f6f6f6', 
            padding: '12px', 
            borderRadius: '6px',
            fontFamily: 'monospace',
            fontSize: '12px',
            maxHeight: '300px',
            overflow: 'auto'
          }}>
            {testResults.map((result, index) => (
              <div key={index} style={{ marginBottom: '4px' }}>
                {result}
              </div>
            ))}
          </div>
        </Card>
      )}

      <Alert
        message="重要提示"
        description={
          <div>
            <Text strong>在实际应用中：</Text>
            <ul style={{ marginTop: '8px', marginBottom: '0' }}>
              <li><Text strong>401错误</Text>会被HTTP拦截器自动捕获并触发登录跳转</li>
              <li><Text strong>403错误</Text>会被HTTP拦截器自动捕获但只显示提示，不跳转</li>
              <li>业务代码中可以根据具体需求调用对应的处理器方法</li>
              <li>所有错误处理都会记录到浏览器控制台以便调试</li>
            </ul>
          </div>
        }
        type="warning"
        style={{ marginTop: '20px' }}
      />
    </div>
  );
};

export default ErrorHandlingTest;