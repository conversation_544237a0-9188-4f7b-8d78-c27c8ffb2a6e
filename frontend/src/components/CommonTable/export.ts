// 通用表格组件入口文件
export { default as CommonTable } from './index';
export { default as TableFilters } from './TableFilters';
export { default as FormModal, type FormModalMethods } from './FormModal';
export { 
  TableStats, 
  TableToolbar, 
  TableSearch 
} from './TableComponents';
export { 
  ColumnRenderer, 
  ConfigParser 
} from './ConfigParser';
export { 
  useTableData,
  type UseTableDataOptions,
  type UseTableDataReturn 
} from './useTableData';

// 导出所有类型
export type {
  // 基础类型
  BaseRecord,
  PaginationParams,
  QueryParams,
  
  // 函数类型
  FetchFunction,
  CreateFunction,
  UpdateFunction,
  DeleteFunction,
  BatchDeleteFunction,
  
  // 配置类型
  TableConfig,
  TableColumnConfig,
  ActionButton,
  TableAction,
  FilterConfig,
  StatsConfig,
  ExportConfig,
  CrudModalConfig,
  RefreshConfig,
  
  // 组件类型
  CommonTableProps,
  TableState,
  TableMethods,
  
  // 渲染类型
  CellRenderType
} from './types';

// 导出示例
export { default as TableExample } from './Example';
export { default as BestPracticesExample } from './BestPracticesExample';