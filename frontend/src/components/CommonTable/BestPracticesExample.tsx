import React, { useRef, useState } from 'react';
import { Button, Space, Tag, message, Avatar, Progress } from 'antd';
import {
  UserOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  StopOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { CommonTable, TableConfig, TableMethods, BaseRecord } from './export';

// 用户数据类型定义
interface User extends BaseRecord {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  department: string;
  role: string;
  status: 'active' | 'inactive' | 'pending';
  joinDate: string;
  lastLoginAt?: string;
  permissions: string[];
  progress: number;
  tags: string[];
}

// 模拟API调用
const mockApi = {
  // 获取用户列表
  fetchUsers: async (params: any) => {
    console.log('Fetch users with params:', params);
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const mockUsers: User[] = [
      {
        id: '1',
        name: '张三',
        email: 'zhang<PERSON>@example.com',
        avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=1',
        department: '技术部',
        role: 'developer',
        status: 'active',
        joinDate: '2023-01-15',
        lastLoginAt: '2024-01-20 14:30:00',
        permissions: ['read', 'write'],
        progress: 85,
        tags: ['前端', 'React'],
        created_at: '2023-01-15T08:00:00Z',
        updated_at: '2024-01-20T14:30:00Z'
      },
      {
        id: '2',
        name: '李四',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/miniavs/svg?seed=2',
        department: '产品部',
        role: 'product_manager',
        status: 'pending',
        joinDate: '2023-03-10',
        lastLoginAt: '2024-01-19 09:15:00',
        permissions: ['read'],
        progress: 65,
        tags: ['产品', '设计'],
        created_at: '2023-03-10T08:00:00Z',
        updated_at: '2024-01-19T09:15:00Z'
      },
      {
        id: '3',
        name: '王五',
        email: '<EMAIL>',
        department: '运营部',
        role: 'operations',
        status: 'inactive',
        joinDate: '2022-11-05',
        permissions: ['read'],
        progress: 30,
        tags: ['运营', '数据分析'],
        created_at: '2022-11-05T08:00:00Z',
        updated_at: '2023-12-15T10:20:00Z'
      }
    ];

    // 模拟搜索筛选
    let filteredUsers = mockUsers;
    if (params.search) {
      filteredUsers = filteredUsers.filter(user => 
        user.name.includes(params.search) || 
        user.email.includes(params.search)
      );
    }

    // 模拟状态筛选
    if (params.filters?.status) {
      filteredUsers = filteredUsers.filter(user => user.status === params.filters.status);
    }

    // 模拟部门筛选
    if (params.filters?.department) {
      filteredUsers = filteredUsers.filter(user => user.department === params.filters.department);
    }

    const total = filteredUsers.length;
    const page = params.pagination.current || 1;
    const size = params.pagination.pageSize || 10;
    const pages = Math.ceil(total / size);

    return {
      code: 0,
      message: 'success',
      data: filteredUsers,
      meta: {
        timestamp: Date.now(),
        pagination: {
          page,
          size,
          total,
          pages,
          has_next: page < pages,
          has_prev: page > 1,
          next_cursor: {}
        }
      }
    };
  },

  // 创建用户
  createUser: async (data: Partial<User>) => {
    console.log('Create user:', data);
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const newUser: User = {
      id: Date.now().toString(),
      name: data.name || '',
      email: data.email || '',
      department: data.department || '',
      role: data.role || 'developer',
      status: data.status || 'pending',
      joinDate: new Date().toISOString().split('T')[0],
      permissions: data.permissions || ['read'],
      progress: data.progress || 0,
      tags: data.tags || [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    message.success(`用户 ${newUser.name} 创建成功！`);
    return { code: 0, message: 'success', data: newUser, meta: { timestamp: Date.now() } };
  },

  // 更新用户
  updateUser: async (id: string | number, data: Partial<User>) => {
    console.log('Update user:', id, data);
    await new Promise(resolve => setTimeout(resolve, 600));
    
    message.success(`用户信息更新成功！`);
    return { code: 0, message: 'success', data: { id, ...data }, meta: { timestamp: Date.now() } };
  },

  // 删除用户
  deleteUser: async (id: string | number) => {
    console.log('Delete user:', id);
    await new Promise(resolve => setTimeout(resolve, 400));
    
    message.success(`用户删除成功！`);
    return { code: 0, message: 'success', data: null, meta: { timestamp: Date.now() } };
  },

  // 批量删除用户
  batchDeleteUsers: async (ids: (string | number)[]) => {
    console.log('Batch delete users:', ids);
    await new Promise(resolve => setTimeout(resolve, 800));
    
    message.success(`批量删除 ${ids.length} 个用户成功！`);
    return { code: 0, message: 'success', data: null, meta: { timestamp: Date.now() } };
  }
};

// 最佳实践示例组件
const BestPracticesExample: React.FC = () => {
  const tableRef = useRef<TableMethods<User>>(null);
  const [refreshTrigger, setRefreshTrigger] = useState<number>(0);

  // 表格配置 - 展示完整的最佳实践
  const tableConfig: TableConfig<User> = {
    name: 'user-management-table',
    title: '用户管理系统',
    
    // 数据源配置 - 包含完整的CRUD操作
    dataSource: {
      fetch: mockApi.fetchUsers,
      create: mockApi.createUser,
      update: mockApi.updateUser,
      delete: mockApi.deleteUser,
      batchDelete: mockApi.batchDeleteUsers
    },
    
    // 列配置 - 展示各种渲染类型
    columns: [
      {
        key: 'avatar',
        title: '头像',
        dataIndex: 'avatar',
        width: 80,
        renderType: 'avatar',
        avatarConfig: {
          src: (record) => record.avatar || '',
          fallback: (record) => record.name.charAt(0),
          size: 'default',
          shape: 'circle'
        }
      },
      {
        key: 'name',
        title: '姓名',
        dataIndex: 'name',
        renderType: 'text',
        searchable: true,
        sortable: true,
        copyable: true
      },
      {
        key: 'email',
        title: '邮箱',
        dataIndex: 'email',
        renderType: 'link',
        linkConfig: {
          href: (record) => `mailto:${record.email}`,
          target: '_blank'
        }
      },
      {
        key: 'department',
        title: '部门',
        dataIndex: 'department',
        renderType: 'badge',
        filterable: true
      },
      {
        key: 'role',
        title: '角色',
        dataIndex: 'role',
        renderType: 'text',
        render: (value) => {
          const roleMap: Record<string, string> = {
            developer: '开发工程师',
            product_manager: '产品经理',
            operations: '运营专员'
          };
          return roleMap[value] || value;
        }
      },
      {
        key: 'status',
        title: '状态',
        dataIndex: 'status',
        renderType: 'status',
        statusMap: {
          active: { text: '正常', color: 'success' },
          pending: { text: '待激活', color: 'warning' },
          inactive: { text: '已禁用', color: 'default' }
        },
        filterable: true
      },
      {
        key: 'progress',
        title: '完成度',
        dataIndex: 'progress',
        renderType: 'progress',
        progressConfig: {
          percent: (record) => record.progress,
          status: 'active',
          showInfo: true
        }
      },
      {
        key: 'tags',
        title: '标签',
        dataIndex: 'tags',
        renderType: 'tags',
        tagsConfig: {
          colorMap: {
            '前端': 'blue',
            'React': 'cyan',
            '产品': 'green',
            '设计': 'purple',
            '运营': 'orange',
            '数据分析': 'red'
          },
          maxCount: 2
        }
      },
      {
        key: 'lastLoginAt',
        title: '最后登录',
        dataIndex: 'lastLoginAt',
        renderType: 'datetime',
        format: 'YYYY-MM-DD HH:mm'
      },
      {
        key: 'actions',
        title: '操作',
        renderType: 'actions',
        width: 200,
        fixed: 'right',
        actionsConfig: [
          {
            key: 'detail',
            label: '详情',
            icon: <EyeOutlined />,
            type: 'text',
            onClick: (record) => {
              message.info(`查看用户 ${record.name} 的详情`);
            }
          },
          {
            key: 'edit',
            label: '编辑',
            icon: <EditOutlined />,
            type: 'text',
            visible: (record) => record.status !== 'inactive',
            onClick: (record) => {
              console.log('Edit user:', record);
            }
          },
          {
            key: 'delete',
            label: '删除',
            icon: <DeleteOutlined />,
            type: 'text',
            danger: true,
            visible: (record) => record.status !== 'active',
            onClick: (record) => {
              console.log('Delete user:', record);
            }
          }
        ]
      }
    ],

    // 功能特性配置
    features: {
      // 分页配置
      pagination: {
        defaultPageSize: 10,
        pageSizeOptions: ['10', '20', '50', '100'],
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: true
      },
      
      // 搜索配置
      search: {
        placeholder: '搜索用户姓名或邮箱',
        fields: ['name', 'email'],
        debounceMs: 300
      },
      
      // 筛选配置
      filters: [
        {
          field: 'status',
          label: '状态',
          type: 'select',
          options: [
            { label: '正常', value: 'active', color: 'success' },
            { label: '待激活', value: 'pending', color: 'warning' },
            { label: '已禁用', value: 'inactive', color: 'default' }
          ],
          span: 8
        },
        {
          field: 'department',
          label: '部门',
          type: 'select',
          options: [
            { label: '技术部', value: '技术部' },
            { label: '产品部', value: '产品部' },
            { label: '运营部', value: '运营部' }
          ],
          span: 8
        },
        {
          field: 'joinDateRange',
          label: '入职时间',
          type: 'dateRange',
          dateFormat: 'YYYY-MM-DD',
          span: 8
        }
      ],
      
      // 选择配置
      selection: {
        type: 'checkbox',
        preserveSelectedRowKeys: true
      },
      
      // 统计配置
      stats: [
        {
          title: '总用户数',
          value: (data) => data.length,
          icon: <TeamOutlined />,
          color: '#1890ff'
        },
        {
          title: '正常用户',
          value: (data) => data.filter(user => user.status === 'active').length,
          icon: <CheckCircleOutlined />,
          color: '#52c41a'
        },
        {
          title: '待激活',
          value: (data) => data.filter(user => user.status === 'pending').length,
          icon: <ClockCircleOutlined />,
          color: '#faad14'
        },
        {
          title: '已禁用',
          value: (data) => data.filter(user => user.status === 'inactive').length,
          icon: <StopOutlined />,
          color: '#f5222d'
        }
      ],
      
      // 其他功能
      refresh: true,
      export: {
        filename: '用户列表',
        type: 'excel',
        columns: ['name', 'email', 'department', 'status']
      }
    },

    // 表格级操作
    actions: [
      {
        key: 'add',
        label: '新增用户',
        type: 'primary',
        icon: <UserOutlined />,
        onClick: () => {
          console.log('Add new user clicked');
        }
      },
      {
        key: 'import',
        label: '批量导入',
        icon: <UserOutlined />,
        onClick: () => {
          message.info('导入功能开发中...');
        }
      }
    ],

    // CRUD 模态框配置
    modals: {
      // 新增用户配置
      create: {
        title: '新增用户',
        width: 800,
        formItems: [
          {
            field: 'name',
            label: '姓名',
            type: 'input',
            required: true,
            span: 12,
            rules: [
              { required: true, message: '请输入用户姓名' },
              { min: 2, max: 20, message: '姓名长度在2-20个字符' }
            ]
          },
          {
            field: 'email',
            label: '邮箱',
            type: 'input',
            required: true,
            span: 12,
            rules: [
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入正确的邮箱格式' }
            ]
          },
          {
            field: 'department',
            label: '部门',
            type: 'select',
            required: true,
            span: 12,
            options: [
              { label: '技术部', value: '技术部' },
              { label: '产品部', value: '产品部' },
              { label: '运营部', value: '运营部' },
              { label: '市场部', value: '市场部' }
            ]
          },
          {
            field: 'role',
            label: '角色',
            type: 'select',
            required: true,
            span: 12,
            options: [
              { label: '开发工程师', value: 'developer' },
              { label: '产品经理', value: 'product_manager' },
              { label: '运营专员', value: 'operations' },
              { label: '市场专员', value: 'marketing' }
            ]
          },
          {
            field: 'status',
            label: '状态',
            type: 'select',
            required: true,
            span: 12,
            defaultValue: 'pending',
            options: [
              { label: '正常', value: 'active' },
              { label: '待激活', value: 'pending' },
              { label: '已禁用', value: 'inactive' }
            ]
          },
          {
            field: 'progress',
            label: '完成度',
            type: 'number',
            span: 12,
            min: 0,
            max: 100,
            defaultValue: 0,
            rules: [
              { type: 'number', min: 0, max: 100, message: '完成度范围0-100' }
            ]
          },
          {
            field: 'tags',
            label: '标签',
            type: 'multiSelect',
            span: 24,
            options: [
              { label: '前端', value: '前端' },
              { label: 'React', value: 'React' },
              { label: '产品', value: '产品' },
              { label: '设计', value: '设计' },
              { label: '运营', value: '运营' },
              { label: '数据分析', value: '数据分析' }
            ]
          }
        ],
        onSubmit: async (values: any, isEdit: boolean, record?: User) => {
          await mockApi.createUser(values);
        }
      },

      // 编辑用户配置
      edit: {
        title: (isEdit, record) => `编辑用户 - ${record?.name}`,
        width: 800,
        formItems: [
          {
            field: 'name',
            label: '姓名',
            type: 'input',
            required: true,
            span: 12,
            rules: [
              { required: true, message: '请输入用户姓名' },
              { min: 2, max: 20, message: '姓名长度在2-20个字符' }
            ]
          },
          {
            field: 'email',
            label: '邮箱',
            type: 'input',
            required: true,
            span: 12,
            disabled: true, // 编辑时邮箱不可修改
            rules: [
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入正确的邮箱格式' }
            ]
          },
          {
            field: 'department',
            label: '部门',
            type: 'select',
            required: true,
            span: 12,
            options: [
              { label: '技术部', value: '技术部' },
              { label: '产品部', value: '产品部' },
              { label: '运营部', value: '运营部' },
              { label: '市场部', value: '市场部' }
            ]
          },
          {
            field: 'role',
            label: '角色',
            type: 'select',
            required: true,
            span: 12,
            options: [
              { label: '开发工程师', value: 'developer' },
              { label: '产品经理', value: 'product_manager' },
              { label: '运营专员', value: 'operations' },
              { label: '市场专员', value: 'marketing' }
            ]
          },
          {
            field: 'status',
            label: '状态',
            type: 'select',
            required: true,
            span: 12,
            options: [
              { label: '正常', value: 'active' },
              { label: '待激活', value: 'pending' },
              { label: '已禁用', value: 'inactive' }
            ]
          },
          {
            field: 'progress',
            label: '完成度',
            type: 'number',
            span: 12,
            min: 0,
            max: 100,
            rules: [
              { type: 'number', min: 0, max: 100, message: '完成度范围0-100' }
            ]
          },
          {
            field: 'tags',
            label: '标签',
            type: 'multiSelect',
            span: 24,
            options: [
              { label: '前端', value: '前端' },
              { label: 'React', value: 'React' },
              { label: '产品', value: '产品' },
              { label: '设计', value: '设计' },
              { label: '运营', value: '运营' },
              { label: '数据分析', value: '数据分析' }
            ]
          }
        ],
        onSubmit: async (values: any, isEdit: boolean, record?: User) => {
          if (record?.id) {
            await mockApi.updateUser(record.id, values);
          }
        }
      },

      // 详情查看配置
      detail: {
        title: (record) => `用户详情 - ${record.name}`,
        width: 600,
        render: (record) => (
          <div style={{ padding: '16px 0' }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
              <Avatar 
                size={64} 
                src={record.avatar} 
                style={{ marginRight: 16 }}
              >
                {record.name.charAt(0)}
              </Avatar>
              <div>
                <h3 style={{ margin: 0 }}>{record.name}</h3>
                <p style={{ margin: 0, color: '#666' }}>{record.email}</p>
              </div>
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <strong>部门：</strong>{record.department}
            </div>
            <div style={{ marginBottom: 16 }}>
              <strong>角色：</strong>{record.role === 'developer' ? '开发工程师' : record.role}
            </div>
            <div style={{ marginBottom: 16 }}>
              <strong>状态：</strong>
              <Tag color={
                record.status === 'active' ? 'success' : 
                record.status === 'pending' ? 'warning' : 'default'
              }>
                {record.status === 'active' ? '正常' : 
                 record.status === 'pending' ? '待激活' : '已禁用'}
              </Tag>
            </div>
            <div style={{ marginBottom: 16 }}>
              <strong>完成度：</strong>
              <Progress percent={record.progress} size="small" />
            </div>
            <div style={{ marginBottom: 16 }}>
              <strong>标签：</strong>
              <Space wrap>
                {record.tags?.map(tag => (
                  <Tag key={tag}>{tag}</Tag>
                ))}
              </Space>
            </div>
            <div style={{ marginBottom: 16 }}>
              <strong>入职时间：</strong>{record.joinDate}
            </div>
            {record.lastLoginAt && (
              <div>
                <strong>最后登录：</strong>{record.lastLoginAt}
              </div>
            )}
          </div>
        )
      },

      // 删除确认配置
      delete: {
        title: (record) => `确认删除用户`,
        content: (record) => `确定要删除用户 "${record.name}" 吗？此操作不可恢复。`,
        okText: '确认删除',
        cancelText: '取消'
      }
    },

    // 样式配置
    style: {
      size: 'middle',
      bordered: true,
      showHeader: true,
      scroll: { x: 1200 }
    },

    // 行配置
    rowConfig: {
      key: 'id',
      className: (record, index) => 
        record.status === 'inactive' ? 'table-row-disabled' : '',
      onClick: (record) => {
        console.log('Row clicked:', record);
      }
    },

    // 权限配置
    permissions: {
      add: 'user:create',
      edit: 'user:update',
      delete: 'user:delete',
      export: 'user:export'
    }
  };

  // 外部操作方法
  const handleExternalRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
    message.info('手动刷新表格数据');
  };

  const handleBulkOperation = () => {
    const selectedRows = tableRef.current?.getSelectedRows();
    if (selectedRows && selectedRows.length > 0) {
      message.info(`批量操作 ${selectedRows.length} 条记录`);
    } else {
      message.warning('请先选择要操作的记录');
    }
  };

  const handleExportData = () => {
    tableRef.current?.exportData({
      filename: '用户数据导出',
      type: 'excel'
    });
  };

  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 16 }}>
        <h2>CommonTable 最佳实践示例</h2>
        <p style={{ color: '#666', marginBottom: 24 }}>
          展示完整的表格功能：CRUD操作、外部刷新、筛选搜索、统计显示等
        </p>
        
        {/* 外部操作按钮 */}
        <Space style={{ marginBottom: 16 }}>
          <Button 
            type="primary" 
            onClick={handleExternalRefresh}
          >
            外部刷新
          </Button>
          <Button onClick={handleBulkOperation}>
            批量操作
          </Button>
          <Button onClick={handleExportData}>
            导出数据
          </Button>
          <Button 
            onClick={() => {
              tableRef.current?.search('张');
              message.info('执行搜索：张');
            }}
          >
            执行搜索
          </Button>
          <Button 
            onClick={() => {
              tableRef.current?.filter({ status: 'active' });
              message.info('筛选正常用户');
            }}
          >
            筛选正常用户
          </Button>
        </Space>
      </div>

      {/* 表格组件 */}
      <CommonTable<User>
        config={tableConfig}
        tableRef={tableRef}
        refreshTrigger={refreshTrigger}
        onRefresh={() => {
          console.log('Table refreshed externally');
        }}
        onStateChange={(state) => {
          console.log('Table state changed:', state);
        }}
        onSelectionChange={(keys, rows) => {
          console.log('Selection changed:', keys, rows);
        }}
        onCreateSuccess={(result, data) => {
          console.log('Create success:', result, data);
        }}
        onUpdateSuccess={(result, id, data) => {
          console.log('Update success:', result, id, data);
        }}
        onDeleteSuccess={(result, id) => {
          console.log('Delete success:', result, id);
        }}
      />

      {/* 使用说明 */}
      <div style={{ marginTop: 32, padding: 16, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
        <h4>功能说明：</h4>
        <ul>
          <li>✅ <strong>完整CRUD操作</strong>：支持新增、编辑、删除、批量删除</li>
          <li>✅ <strong>多种列渲染</strong>：头像、状态、进度条、标签、链接等</li>
          <li>✅ <strong>高级筛选</strong>：支持多种筛选器类型和搜索</li>
          <li>✅ <strong>统计展示</strong>：实时统计数据并展示</li>
          <li>✅ <strong>外部刷新</strong>：支持外部触发和依赖刷新</li>
          <li>✅ <strong>权限控制</strong>：基于权限显示/隐藏功能</li>
          <li>✅ <strong>响应式设计</strong>：适配不同屏幕尺寸</li>
          <li>✅ <strong>丰富交互</strong>：行点击、选择、排序等</li>
        </ul>
      </div>
    </div>
  );
};

export default BestPracticesExample;