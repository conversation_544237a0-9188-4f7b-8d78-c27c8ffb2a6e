import { ReactNode, CSSProperties } from 'react';
import type { ApiResponse } from '../../types/api';
import { TableProps, ColumnType } from 'antd/es/table';
import { FormInstance } from 'antd/es/form';
import { FormModalConfig } from './FormModal';

// 基础数据记录类型
export interface BaseRecord {
  id: string | number;
  created_at?: string;
  updated_at?: string;
  [key: string]: any;
}


// 分页参数
export interface PaginationParams {
  current: number;
  pageSize: number;
  total?: number;
}

// 查询参数
export interface QueryParams {
  pagination: PaginationParams;
  filters?: Record<string, any>;
  sorter?: {
    field?: string;
    order?: 'ascend' | 'descend';
  };
  search?: string;
  [key: string]: any;
}

// 数据获取函数类型
export type FetchFunction<T = BaseRecord> = (params: QueryParams) => Promise<ApiResponse<T[]>>;

// CRUD操作函数类型
export type CreateFunction<T = BaseRecord> = (data: Partial<T>) => Promise<ApiResponse>;
export type UpdateFunction<T = BaseRecord> = (id: string | number, data: Partial<T>) => Promise<ApiResponse>;
export type DeleteFunction = (id: string | number) => Promise<ApiResponse>;
export type BatchDeleteFunction = (ids: (string | number)[]) => Promise<ApiResponse>;

// 透出全局 ApiResponse 以便使用方从本模块统一导入
export type { ApiResponse as CommonApiResponse } from '../../types/api';

// 列渲染类型
export type CellRenderType = 
  | 'text' 
  | 'number' 
  | 'date' 
  | 'datetime'
  | 'status' 
  | 'avatar' 
  | 'image'
  | 'link'
  | 'badge'
  | 'progress'
  | 'tags'
  | 'actions'
  | 'custom';

// 列配置
export interface TableColumnConfig<T = BaseRecord> {
  key: string;
  title: string;
  dataIndex?: keyof T | string;
  width?: number | string;
  fixed?: 'left' | 'right';
  align?: 'left' | 'center' | 'right';
  
  // 渲染配置
  renderType?: CellRenderType;
  render?: (value: any, record: T, index: number) => ReactNode;
  
  // 数据格式化
  format?: string; // 用于日期、数字格式化
  
  // 状态列配置
  statusMap?: Record<string, { text: string; color: string }>;
  
  // 头像列配置
  avatarConfig?: {
    src?: string | ((record: T) => string);
    fallback?: string | ((record: T) => string);
    shape?: 'circle' | 'square';
    size?: number | 'small' | 'default' | 'large';
  };
  
  // 标签列配置
  tagsConfig?: {
    colorMap?: Record<string, string>;
    maxCount?: number;
  };
  
  // 操作按钮配置
  actionsConfig?: ActionButton<T>[];
  
  // 交互配置
  sortable?: boolean;
  filterable?: boolean;
  searchable?: boolean;
  
  // 显示控制
  visible?: boolean | ((record: T) => boolean);
  
  // 复制功能
  copyable?: boolean;
  
  // 链接配置
  linkConfig?: {
    href?: string | ((record: T) => string);
    target?: '_blank' | '_self';
  };
  
  // 进度条配置
  progressConfig?: {
    percent: string | ((record: T) => number);
    status?: 'success' | 'exception' | 'active' | 'normal';
    showInfo?: boolean;
  };
}

// 操作按钮
export interface ActionButton<T = BaseRecord> {
  key: string;
  label: string;
  icon?: ReactNode;
  type?: 'primary' | 'default' | 'dashed' | 'text' | 'link';
  danger?: boolean;
  
  // 显示控制
  visible?: boolean | ((record: T) => boolean);
  disabled?: boolean | ((record: T) => boolean);
  
  // 权限控制
  permission?: string;
  
  // 事件处理
  onClick: (record: T, index: number) => void | Promise<void>;
  
  // 确认对话框
  confirm?: {
    title: string;
    content?: string;
    okText?: string;
    cancelText?: string;
  };
  
  // 加载状态
  loading?: boolean | ((record: T) => boolean);
}

// 表格级操作
export interface TableAction {
  key: string;
  label: string;
  icon?: ReactNode;
  type?: 'primary' | 'default' | 'dashed';
  
  // 显示控制
  visible?: boolean;
  disabled?: boolean;
  
  // 权限控制
  permission?: string;
  
  // 事件处理
  onClick: () => void | Promise<void>;
  
  // 加载状态
  loading?: boolean;
}

// 筛选配置
export interface FilterConfig {
  field: string;
  label: string;
  type: 'input' | 'select' | 'multiSelect' | 'dateRange' | 'numberRange';
  
  // 输入框配置
  placeholder?: string;
  
  // 下拉选择配置
  options?: Array<{ label: string; value: any; color?: string }>;
  mode?: 'multiple' | 'tags';
  
  // 日期范围配置
  dateFormat?: string;
  showTime?: boolean;
  
  // 数字范围配置
  numberConfig?: {
    min?: number;
    max?: number;
    step?: number;
    precision?: number;
  };
  
  // 默认值
  defaultValue?: any;
  
  // 宽度
  span?: number;
  
  // 远程数据源
  remote?: {
    url: string;
    valueField: string;
    labelField: string;
    params?: Record<string, any>;
  };
}

// 统计配置
export interface StatsConfig {
  title: string;
  value: number | string | ((data: any[]) => number | string);
  icon?: ReactNode;
  color?: string;
  suffix?: string;
  precision?: number;
}

// 导出配置
export interface ExportConfig {
  filename?: string;
  type?: 'excel' | 'csv';
  columns?: string[]; // 指定导出的列
  beforeExport?: (data: any[]) => any[];
}

// CRUD模态框配置
export interface CrudModalConfig<T = BaseRecord> {
  // 新增配置
  create?: FormModalConfig<T extends BaseRecord ? T : BaseRecord> | boolean;
  
  // 编辑配置
  edit?: FormModalConfig<T extends BaseRecord ? T : BaseRecord> | boolean;
  
  // 详情配置
  detail?: {
    title?: string | ((record: T) => string);
    width?: number;
    render: (record: T) => ReactNode;
  };
  
  // 删除确认配置
  delete?: {
    title?: string | ((record: T) => string);
    content?: string | ((record: T) => string);
    okText?: string;
    cancelText?: string;
  };
}

// 外部刷新配置
export interface RefreshConfig {
  // 刷新触发器
  trigger?: any; // 任何值变化都会触发刷新
  
  // 依赖数组
  deps?: any[];
  
  // 刷新延迟
  delay?: number;
  
  // 刷新回调
  onRefresh?: () => void;
}

// 表格配置主接口
export interface TableConfig<T = BaseRecord> {
  // 基础信息
  name: string;
  title?: string;
  
  // 数据源配置
  dataSource: {
    fetch: FetchFunction<T>;
    create?: CreateFunction<T>;
    update?: UpdateFunction<T>;
    delete?: DeleteFunction;
    batchDelete?: BatchDeleteFunction;
  };
  
  // 列配置
  columns: TableColumnConfig<T>[];
  
  // 功能开关
  features?: {
    // 分页
    pagination?: boolean | {
      defaultPageSize?: number;
      pageSizeOptions?: string[];
      showSizeChanger?: boolean;
      showQuickJumper?: boolean;
      showTotal?: boolean;
      position?: 'top' | 'bottom' | 'both';
    };
    
    // 搜索
    search?: boolean | {
      placeholder?: string;
      fields?: string[];
      debounceMs?: number;
    };
    
    // 筛选
    filters?: FilterConfig[] | boolean;
    
    // 选择
    selection?: boolean | {
      type?: 'checkbox' | 'radio';
      preserveSelectedRowKeys?: boolean;
      onChange?: (selectedRowKeys: any[], selectedRows: T[]) => void;
    };
    
    // 刷新
    refresh?: boolean;
    
    // 导出
    export?: ExportConfig | boolean;
    
    // 统计
    stats?: StatsConfig[] | boolean;
    
    // 密度切换
    density?: boolean;
    
    // 列设置
    columnSettings?: boolean;
  };
  
  // 表格级操作
  actions?: TableAction[];
  
  // CRUD模态框配置
modals?: CrudModalConfig<T>;
  
  // 外部刷新配置
  refresh?: RefreshConfig;
  
  // 样式配置
  style?: {
    size?: 'small' | 'middle' | 'large';
    bordered?: boolean;
    showHeader?: boolean;
    tableLayout?: 'auto' | 'fixed';
    scroll?: { x?: number | string; y?: number | string };
  };
  
  // 行配置
  rowConfig?: {
    key?: string | ((record: T) => string);
    className?: string | ((record: T, index: number) => string);
    onClick?: (record: T, index: number) => void;
    onDoubleClick?: (record: T, index: number) => void;
  };
  
  // 空状态
  emptyConfig?: {
    image?: ReactNode;
    description?: string;
  };
  
  // 权限配置
  permissions?: {
    add?: string;
    edit?: string;
    delete?: string;
    export?: string;
    [key: string]: string | undefined;
  };
}

// 表格状态
export interface TableState<T = BaseRecord> {
  data: T[];
  loading: boolean;
  pagination: PaginationParams;
  filters: Record<string, any>;
  sorter: {
    field?: string;
    order?: 'ascend' | 'descend';
  };
  search: string;
  selectedRowKeys: any[];
  selectedRows: T[];
  stats?: Record<string, any>;
}

// 表格实例方法
export interface TableMethods<T = BaseRecord> {
  // 数据操作
  refresh: () => Promise<void>;
  search: (keyword: string) => void;
  filter: (filters: Record<string, any>) => void;
  resetFilters: () => void;
  getSelectedRows: () => T[];
  clearSelection: () => void;
  exportData: (config?: ExportConfig) => Promise<void>;
  
  // CRUD操作
  create?: (data: Partial<T>) => Promise<void>;
  update?: (id: string | number, data: Partial<T>) => Promise<void>;
  delete?: (id: string | number) => Promise<void>;
  batchDelete?: (ids: (string | number)[]) => Promise<void>;
  
  // 排序分页
  sort: (sorter: { field?: string; order?: 'ascend' | 'descend' }) => void;
  paginate: (pagination: { current: number; pageSize: number }) => void;
  setSelection: (selectedRowKeys: any[], selectedRows: T[]) => void;
  
  // 模态框操作
  openCreateModal?: () => void;
  openEditModal?: (record: T) => void;
  openDetailModal?: (record: T) => void;
}

// 表格组件Props
export interface CommonTableProps<T = BaseRecord> extends Omit<TableProps<T>, 'columns' | 'dataSource' | 'title'> {
  // JSON配置（优先级最高）
  config?: TableConfig<T>;
  
  // Props配置（用于特殊定制）
  title?: string;
  dataSource?: {
    fetch: FetchFunction<T>;
    create?: CreateFunction<T>;
    update?: UpdateFunction<T>;
    delete?: DeleteFunction;
    batchDelete?: BatchDeleteFunction;
  };
  columns?: TableColumnConfig<T>[];
  actions?: TableAction[];
  filters?: FilterConfig[];
  stats?: StatsConfig[];
  
  // CRUD模态框配置
modals?: CrudModalConfig<T extends BaseRecord ? T : BaseRecord>;
  
  // 外部刷新
  refreshTrigger?: any;
  refreshDeps?: any[];
  onRefresh?: () => void;
  
  // 事件回调
  onStateChange?: (state: TableState<T>) => void;
  onSelectionChange?: (selectedRowKeys: any[], selectedRows: T[]) => void;
  onCreateSuccess?: (result: any, data: Partial<T>) => void;
  onUpdateSuccess?: (result: any, id: string | number, data: Partial<T>) => void;
  onDeleteSuccess?: (result: any, id: string | number) => void;
  
  // 表格实例引用
  tableRef?: React.MutableRefObject<TableMethods<T> | null>;
  
  // 自定义渲染
  renderToolbar?: (defaultToolbar: ReactNode) => ReactNode;
  renderFilters?: (defaultFilters: ReactNode) => ReactNode;
  renderStats?: (defaultStats: ReactNode) => ReactNode;
  
  // 样式
  className?: string;
  style?: CSSProperties;
}

// 导出所有类型
export type {
  ColumnType,
  FormInstance,
  TableProps,
  ReactNode,
  CSSProperties,
};