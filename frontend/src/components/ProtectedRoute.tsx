import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuth } from '../contexts/AuthContext';
import { useAuthRedirect } from '../hooks/useAuthRedirect';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, initialLoading } = useAuth();
  const { onRequireLogin } = useAuthRedirect();
  const location = useLocation();

  // 使用useEffect来处理重定向，避免在渲染过程中直接调用
  useEffect(() => {
    if (!initialLoading && !isAuthenticated) {
      onRequireLogin({
        message: '请先登录以访问该页面',
        returnUrl: location.pathname + location.search + location.hash,
      });
    }
  }, [isAuthenticated, initialLoading, onRequireLogin, location]);

  if (initialLoading) {
    return (
      <Spin spinning={true} tip="正在加载应用..." size="large" style={{ minHeight: '100vh' }}>
        <div style={{ height: '100vh', width: '100vw' }} />
      </Spin>
    );
  }

  if (!isAuthenticated) {
    // 返回空内容，实际的重定向由useEffect处理
    return null;
  }

  return <>{children}</>;
};

export default ProtectedRoute; 