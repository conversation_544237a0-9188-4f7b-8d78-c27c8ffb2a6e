import React, { useState, useEffect, Suspense } from 'react';
import {Layout, Avatar, Dropdown, Button, Badge, message, Typography, Spin, Menu} from 'antd';
import {
  UserOutlined,
  BellOutlined,
  SearchOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  <PERSON>Outlined,
  MoonOutlined,
  LogoutOutlined,
  SettingFilled,
  CrownOutlined,
} from '@ant-design/icons';
import { useLocation, Outlet, useNavigate } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';
import { logout, getCurrentUser, UserInfo } from '../services/auth';
import { useAuth } from '../contexts/AuthContext';
import { useAuthRedirect } from '../hooks/useAuthRedirect';
import { useMenu } from '../hooks/useMenu';
import SimpleBreadcrumb from '../components/Breadcrumb/SimpleBreadcrumb';
import { convertMenuTreeToAntdMenuItems } from '../utils/menuUtils';
import '../styles/global.css';
import { showError } from '../utils/messageManager';
import { BreadcrumbItem } from '../hooks/useSimpleMenu';


const { Header, Sider, Content } = Layout;
const { Text, Title } = Typography;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const { isDarkMode, toggleTheme } = useTheme();
  const { user, isAuthenticated } = useAuth();
  const { onLogout } = useAuthRedirect();
  const location = useLocation();
  const navigate = useNavigate();

  // 使用菜单Hook获取真实的菜单数据
  const {
    menuItems,
    loading: menuLoading,
    error: menuError,
    selectedKeys,
    openKeys,
    breadcrumbs,
    currentPageTitle,
    refreshMenu,
    menuTree
  } = useMenu();

  // 处理菜单展开/收起
  const [currentOpenKeys, setCurrentOpenKeys] = useState<string[]>([]);

  // 同步openKeys
  useEffect(() => {
    setCurrentOpenKeys(openKeys);
  }, [openKeys]);

  // 获取用户信息
  useEffect(() => {
    const currentUser = getCurrentUser();
    setUserInfo(currentUser);
  }, []);

  // 处理登出
  const handleLogout = async () => {
    try {
      await logout();
      message.success('已安全退出登录');
      // 使用统一的退出登录处理
      onLogout({
        message: '已安全退出登录'
      });
    } catch (error) {
      console.error('Logout error:', error);
      showError('退出登录失败', 'logout-error');
      // 即使API失败也要跳转到登录页
      onLogout({
        message: '退出登录过程中出现错误，已清除本地认证信息'
      });
    }
  };

  // 处理用户菜单点击
  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'logout':
        handleLogout();
        break;
      case 'profile':
        message.info('个人资料功能开发中');
        break;
      case 'settings':
        message.info('系统设置功能开发中');
        break;
    }
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingFilled />,
      label: '系统设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  // 处理菜单展开/收起
  const handleOpenChange = (keys: string[]) => {
    setCurrentOpenKeys(keys);
  };

  // 处理菜单点击
  const handleMenuClick = (info: any) => {
    const menuNode = findMenuNodeByKey(menuTree, info.key);
    if (menuNode && menuNode.path) {
      navigate(menuNode.path);
    }
  };

  // 辅助函数：根据key查找菜单节点
  const findMenuNodeByKey = (nodes: any[], key: string): any => {
    for (const node of nodes) {
      if (node.name === key) {
        return node;
      }
      if (node.children) {
        const found = findMenuNodeByKey(node.children, key);
        if (found) return found;
      }
    }
    return null;
  };

  // 处理面包屑点击
  const handleBreadcrumbClick = (item: BreadcrumbItem, index: number) => {
    if (item.path) {
      navigate(item.path);
    }
  };

  // 如果菜单加载出错，显示错误并尝试刷新
  useEffect(() => {
    if (menuError) {
      showError(`菜单加载失败: ${menuError}`, 'menu-load-error');
    }
  }, [menuError]);

  return (
    <Layout style={{ minHeight: '100vh', background: isDarkMode ? '#0a0a0a' : '#f5f7fa' }}>
      {/* 侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        collapsedWidth={56}
        breakpoint="lg"
        onBreakpoint={(broken) => {
          if (broken && window.innerWidth <= 768) {
            setCollapsed(true);
          }
        }}
        width={180}
        style={{
          background: isDarkMode ? '#001529' : '#ffffff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
          zIndex: 1000,
          borderRight: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
        }}
        className="sidebar-custom"
      >
        {/* Logo区域 */}
        <div
          className="logo-container"
          style={{
            height: 64,
            display: 'flex',
            alignItems: 'center',
            justifyContent: collapsed ? 'center' : 'flex-start',
            padding: collapsed ? '0 8px' : '0 12px',
            borderBottom: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
            background: isDarkMode ? '#002140' : '#ffffff',
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: 8,
              color: isDarkMode ? '#ffffff' : '#1890ff',
              fontWeight: 600,
              fontSize: collapsed ? 13 : 15,
            }}
          >
            <div
              style={{
                width: 24,
                height: 24,
                borderRadius: 6,
                background: 'linear-gradient(135deg, #1890ff 0%, #722ed1 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#ffffff',
                fontSize: 14,
                fontWeight: 'bold',
              }}
            >
              <CrownOutlined />
            </div>
            {!collapsed && (
              <span style={{ fontSize: 15, fontWeight: 600, color: 'inherit' }}>
                用户中心
              </span>
            )}
          </div>
        </div>

        {/* 菜单区域 - 使用真实的菜单数据 */}
        <div style={{ flex: 1, overflow: 'auto', paddingTop: 8 }}>
          {menuLoading ? (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Spin size="small" />
            </div>
          ) : menuItems && menuItems.length > 0 ? (
            <Menu 
              selectedKeys={selectedKeys}
              openKeys={currentOpenKeys}
              items={menuItems}
              onClick={handleMenuClick}
              onOpenChange={handleOpenChange}
              mode="inline"
              theme={isDarkMode ? 'dark' : 'light'}
            />
          ) : (
            <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
              <div>菜单加载失败</div>
              <Button 
                size="small" 
                type="link" 
                onClick={refreshMenu}
                style={{ marginTop: '8px' }}
              >
                重新加载
              </Button>
            </div>
          )}
        </div>
      </Sider>

      <Layout>
        {/* 顶部导航栏 */}
        <Header
          style={{
            background: isDarkMode ? '#001529' : '#ffffff',
            padding: '0 20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 1px 4px rgba(0, 0, 0, 0.08)',
            borderBottom: `1px solid ${isDarkMode ? '#303030' : '#e8e8e8'}`,
            height: 56,
            zIndex: 999,
          }}
        >
          {/* 左侧区域 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: 16,
                width: 32,
                height: 32,
                borderRadius: 6,
                color: isDarkMode ? '#ffffff' : '#666666',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            />
            
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <SimpleBreadcrumb
                items={breadcrumbs}
                onItemClick={handleBreadcrumbClick}
                isDarkMode={isDarkMode}
                style={{
                  fontSize: 14,
                }}
              />
            </div>
          </div>

          {/* 右侧区域 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
            {/* 搜索框 */}
            <div
              style={{
                position: 'relative',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <input
                type="text"
                placeholder="搜索..."
                style={{
                  width: 200,
                  height: 32,
                  padding: '6px 12px 6px 36px',
                  border: `1px solid ${isDarkMode ? '#434343' : '#d9d9d9'}`,
                  borderRadius: 8,
                  background: isDarkMode ? '#262626' : '#ffffff',
                  color: isDarkMode ? '#ffffff' : '#262626',
                  fontSize: 14,
                  outline: 'none',
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#1890ff';
                  e.target.style.boxShadow = '0 0 0 2px rgba(24, 144, 255, 0.2)';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = isDarkMode ? '#434343' : '#d9d9d9';
                  e.target.style.boxShadow = 'none';
                }}
              />
              <SearchOutlined
                style={{
                  position: 'absolute',
                  left: 10,
                  color: isDarkMode ? '#8c8c8c' : '#bfbfbf',
                  fontSize: 14,
                }}
              />
            </div>

            {/* 主题切换 */}
            <Button
              type="text"
              icon={isDarkMode ? <SunOutlined /> : <MoonOutlined />}
              onClick={toggleTheme}
              style={{
                width: 32,
                height: 32,
                borderRadius: 8,
                color: isDarkMode ? '#ffffff' : '#666666',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            />

            {/* 通知 */}
            <Badge count={3} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{
                  width: 32,
                  height: 32,
                  borderRadius: 8,
                  color: isDarkMode ? '#ffffff' : '#666666',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              />
            </Badge>

            {/* 用户信息 */}
            <Dropdown
              menu={{
                items: userMenuItems,
                onClick: handleUserMenuClick,
              }}
              placement="bottomRight"
              trigger={['click']}
            >
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 8,
                  cursor: 'pointer',
                  padding: '6px 8px',
                  borderRadius: 8,
                  transition: 'background-color 0.2s ease',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = isDarkMode ? '#1f1f1f' : '#f5f5f5';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                <Avatar
                  src={userInfo?.avatar || 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin'}
                  size={28}
                  style={{
                    background: 'linear-gradient(135deg, #1890ff 0%, #722ed1 100%)',
                  }}
                />
                <div style={{ marginLeft: 8 }}>
                  <Text style={{ 
                    fontSize: 14, 
                    fontWeight: 500,
                    color: isDarkMode ? '#ffffff' : '#262626',
                    maxWidth: 80,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}>
                    {userInfo?.real_name || userInfo?.username || '用户'}
                  </Text>
                </div>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* 内容区域 */}
        <Content
          className="main-content"
          style={{
            margin: '12px',
            padding: '20px',
            background: isDarkMode ? '#141414' : '#ffffff',
            borderRadius: 12,
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
            border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
            height: 'calc(100vh - 56px - 24px)', // 56px header + 2*12px margin
            overflow: 'auto',
          }}
        >
          <Suspense fallback={<div style={{ textAlign: 'center', padding: '20px' }}><Spin size="large" /></div>}>
            <Outlet />
          </Suspense>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout; 