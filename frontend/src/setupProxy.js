const {createProxyMiddleware} = require('http-proxy-middleware');

module.exports = function (app) {
    // Users Service 代理 - 处理用户管理、认证、RBAC等
    app.use(
        '/api/user',
        createProxyMiddleware({
            target: 'http://localhost:8084',
            changeOrigin: true,
            pathRewrite: {'^/api/user': '/api/user'},
            logLevel: 'debug', // 开发时显示代理日志
        })
    );

    // Email Service 代理 - 处理邮件服务
    app.use(
        '/api/email',
        createProxyMiddleware({
            target: 'http://localhost:8082',
            changeOrigin: true,
            pathRewrite: {'^/api/email': '/api/email'},
            logLevel: 'debug', // 开发时显示代理日志
        })
    );
    app.use(
        '/api/email-system',
        createProxyMiddleware({
            target: 'http://localhost:8089',
            changeOrigin: true,
            pathRewrite: {'^/api/email-system': '/api/email-system'},
            logLevel: 'debug', // 开发时显示代理日志
        })
    );
}; 