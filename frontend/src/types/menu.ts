/**
 * 菜单相关类型定义
 * 统一管理所有菜单、权限相关的类型
 */

// 重新导出API中的菜单类型
export type {
  MenuType,
  MenuStatus,
  MenuItem,
  MenuTreeNode,
  ButtonPermission,
  UserButtonPermissionsResponse,
  CheckPermissionResponse,
  BatchCheckPermissionRequest,
  BatchCheckPermissionResponse
} from './api';

/**
 * 菜单数据JSON结构（用于menuData.json）
 */
export interface MenuDataItem {
  key: string;
  label: string;
  path?: string;
  icon?: string;
  type: 'menu' | 'submenu' | 'divider';
  children?: MenuDataItem[];
}

/**
 * 菜单数据根结构
 */
export interface MenuData {
  menus: MenuDataItem[];
}

/**
 * 路由菜单（用于路由配置）
 */
export interface RouteMenu {
  id: number;
  path: string;
  component?: string;
  redirect?: string;
  meta: {
    title: string;
    icon?: string;
    cache: boolean;
    affix: boolean;
    hidden: boolean;
    roles?: string[];
    permissions?: string[];
  };
  children?: RouteMenu[];
}

/**
 * 权限相关类型
 */
export interface Permission {
  id: number;
  name: string;
  display_name: string;
  description?: string;
  resource_type: 'menu' | 'button' | 'api';
  action: string;
  menu_code?: string;
  button_code?: string;
  status: 'active' | 'disabled';
}

/**
 * 用户权限
 */
export interface UserPermission {
  menu_permissions: string[];
  button_permissions: string[];
  api_permissions: string[];
}

/**
 * 权限检查工具接口
 */
export interface PermissionChecker {
  hasPermission: (permission: string) => boolean;
  hasMenuPermission: (menuCode: string) => boolean;
  hasButtonPermission: (menuCode: string, buttonCode: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
}

/**
 * 菜单配置选项
 */
export interface MenuConfig {
  collapsed?: boolean;
  theme?: 'light' | 'dark';
  mode?: 'horizontal' | 'vertical' | 'inline';
  selectedKeys?: string[];
  openKeys?: string[];
}

/**
 * 面包屑项
 */
export interface BreadcrumbItem {
  key: string;
  title: string;
  path?: string;
  icon?: string;
}

/**
 * 菜单操作事件
 */
export interface MenuClickEvent {
  key: string;
  keyPath: string[];
  item: MenuDataItem;
  domEvent: Event;
}

/**
 * 菜单展开/收起事件
 */
export interface MenuOpenChangeEvent {
  openKeys: string[];
}
