/**
 * 统一类型导出文件
 * 提供所有类型的统一入口
 */

// API相关类型
export * from './api';

// 通用类型
export * from './common';

// 菜单相关类型
export * from './menu';

// 租户相关类型
export * from './tenant';

// 邮件相关类型
export * from './email';

// Email System 相关类型
export * from './email-system';

// 文件系统相关类型
export * from './file-system';

// ID生成器相关类型
export * from './idgenerator';

// 为了向后兼容，保留一些常用的类型别名
export type {
  ApiResponse as APIResponse,
  PaginationParams as PaginationRequest,
  UserInfo as User
} from './api';
