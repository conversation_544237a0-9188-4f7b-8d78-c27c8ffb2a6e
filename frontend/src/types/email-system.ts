// Email System 相关类型定义

// 联系人状态枚举
export const CONTACT_STATUS = {
  ACTIVE: 'active',
  SUPPRESSED: 'suppressed', 
  UNCONFIRMED: 'unconfirmed',
  BOUNCED: 'bounced',
  COMPLAINED: 'complained',
} as const;

// 标签类型枚举
export const TAG_TYPE = {
  RULE_BASED: 'rule_based',
  STATIC_LIST: 'static_list',
} as const;

// 标签刷新策略枚举
export const TAG_REFRESH_POLICY = {
  SCHEDULE: 'schedule',
  TRIGGER: 'trigger',
  ONCE: 'once',
} as const;

// 发送计划类型枚举
export const SEND_PLAN_TYPE = {
  CAMPAIGN: 'campaign',
  AUTOMATION: 'automation',
  MANUAL: 'manual',
} as const;

// 发送计划状态枚举
export const SEND_PLAN_STATUS = {
  DRAFT: 'draft',
  SCHEDULED: 'scheduled',
  RUNNING: 'running',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  FAILED: 'failed',
} as const;

// 合并策略枚举
export const MERGE_POLICY = {
  KEEP_EXISTING: 'keep_existing',
  NEW_OVERWRITE: 'new_overwrite',
  MOST_RECENT: 'most_recent',
  CONCAT: 'concat',
} as const;

// 通用分页请求
export interface PaginationRequest {
  page?: number;
  size?: number;
}

// 通用分页响应
export interface EmailSystemPaginationResponse {
  page: number;
  size: number;
  total: number;
  pages: number;
  has_next: boolean;
  has_prev: boolean;
  next_cursor?: any;
}

// 时间范围
export interface TimeRange {
  start?: string;
  end?: string;
}

// 排序选项
export interface SortOptions {
  field?: string;
  order?: 'asc' | 'desc';
}

// ========== 联系人相关类型 ==========

// 联系人基础信息
export interface Contact {
  id: number;
  email: string;
  status: string;
  preferred_language?: string;
  country_code?: string;
  timezone?: string;
  notes?: string;
  attributes?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// 创建联系人请求
export interface CreateContactRequest {
  email: string;
  status?: string;
  preferred_language?: string;
  country_code?: string;
  timezone?: string;
  notes?: string;
  attributes?: Record<string, any>;
  lists?: number[];
  tags?: string[];
}

// 更新联系人请求
export interface UpdateContactRequest {
  id: number;
  email?: string;
  status?: string;
  preferred_language?: string;
  country_code?: string;
  timezone?: string;
  notes?: string;
  attributes?: Record<string, any>;
  merge_policy?: string;
}

// 获取联系人请求
export interface GetContactRequest {
  id?: number;
  email?: string;
  fields?: string;
}

// 搜索联系人过滤条件
export interface ContactSearchFilters {
  status?: string;
  email?: string;
  lists?: number[];
  tags?: string[];
  preferred_language?: string;
  country_code?: string;
  attributes?: Record<string, any>;
  created_at_range?: TimeRange;
  last_activity_range?: TimeRange;
  keyword?: string;
  exclude_contact_ids?: number[];
  exclude_tag_ids?: number[];
  exclude_list_ids?: number[];
}

// 搜索联系人请求
export interface SearchContactsRequest {
  filters?: ContactSearchFilters;
  sort?: SortOptions;
  page?: number;
  size?: number;
}

// 批量创建联系人请求
export interface BatchCreateContactsRequest {
  contacts: CreateContactRequest[];
}

// 批量更新联系人请求
export interface BatchUpdateContactsRequest {
  contacts: UpdateContactRequest[];
}

// 更新联系人状态请求
export interface UpdateContactStatusRequest {
  contact_ids: number[];
  status: string;
}

// ========== 标签相关类型 ==========

// 标签基础信息
export interface Tag {
  id: number;
  name: string;
  description?: string;
  type: string;
  color?: string;
  rule_tree?: Record<string, any>;
  refresh_policy?: string;
  last_refresh_at?: string;
  member_count: number;
  usage_count: number;
  created_by: number;
  created_at: string;
  updated_at: string;
}

// 创建标签请求
export interface CreateTagRequest {
  name: string;
  description?: string;
  type: string;
  color?: string;
  rule_tree?: Record<string, any>;
  refresh_policy?: string;
}

// 更新标签请求
export interface UpdateTagRequest {
  id: number;
  name?: string;
  description?: string;
  color?: string;
  rule_tree?: Record<string, any>;
  refresh_policy?: string;
}

// 分配标签请求
export interface AssignTagRequest {
  tag_ids: number[];
  contact_ids: number[];
  operation: 'add' | 'remove';
}

// 合并标签请求
export interface MergeTagRequest {
  source_tag_ids: number[];
  target_tag_id: number;
  merge_policy: string;
}

// 重命名标签请求
export interface RenameTagRequest {
  id: number;
  new_name: string;
}

// ========== 人群圈选相关类型 ==========

// 人群圈选基础信息
export interface Segment {
  id: number;
  name: string;
  description?: string;
  rule_tree?: Record<string, any>;
  status: string;
  member_count: number;
  last_built_at?: string;
  created_by: number;
  created_at: string;
  updated_at: string;
}

// 创建人群圈选请求
export interface CreateSegmentRequest {
  name: string;
  description?: string;
  rule_tree?: Record<string, any>;
}

// 更新人群圈选请求
export interface UpdateSegmentRequest {
  id: number;
  name?: string;
  description?: string;
  rule_tree?: Record<string, any>;
}

// 人群圈选任务信息
export interface SegmentJob {
  job_id: string;
  segment_id: number;
  status: string;
  total_records: number;
  processed_records: number;
  success_records: number;
  error_records: number;
  progress_percentage: number;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
}

// ========== 发送计划相关类型 ==========

// 重试策略
export interface RetryPolicy {
  enabled: boolean;
  max_attempts: number;
  delay_seconds: number;
  backoff_multiplier: number;
}

// 发送计划基础信息
export interface SendPlan {
  plan_id: number;
  tenant_id: number;
  plan_type: string;
  display_name: string;
  template_id: string;
  priority: number;
  schedule_from?: string;
  schedule_to?: string;
  deadline?: string;
  status: string;
  retry_policy?: RetryPolicy;
  created_at: string;
  updated_at: string;
}

// 创建发送计划请求
export interface CreateSendPlanRequest {
  plan_type: string;
  display_name: string;
  template_id: string;
  priority: number;
  schedule_from?: string;
  schedule_to?: string;
  deadline?: string;
  retry_policy?: RetryPolicy;
}

// 更新发送计划请求
export interface UpdateSendPlanRequest {
  plan_id: number;
  display_name?: string;
  template_id?: string;
  priority?: number;
  schedule_from?: string;
  schedule_to?: string;
  deadline?: string;
  retry_policy?: RetryPolicy;
}

// ========== 导入相关类型 ==========

// 导入任务状态
export const IMPORT_JOB_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
} as const;

// 导入任务信息
export interface ImportJob {
  job_id: string;
  tenant_id: number;
  file_path: string;
  file_size: number;
  total_records: number;
  processed_records: number;
  success_records: number;
  error_records: number;
  status: string;
  progress_percentage: number;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  created_by: number;
  created_at: string;
  updated_at: string;
}

// 创建导入任务请求
export interface CreateImportJobRequest {
  file_path: string;
  scenario_code?: string;
  mapping_config?: Record<string, any>;
  validation_rules?: Record<string, any>;
}

// 导入错误信息
export interface ImportError {
  error_id: number;
  job_id: string;
  batch_id?: string;
  record_index: number;
  error_type: string;
  error_message: string;
  error_data?: Record<string, any>;
  created_at: string;
}

// 导入批次信息
export interface ImportBatch {
  batch_id: string;
  job_id: string;
  batch_index: number;
  record_count: number;
  status: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
}

// ========== 规则引擎相关类型 ==========

// 规则操作符
export interface RuleOperator {
  operator: string;
  name: string;
  description: string;
  supported_types: string[];
}

// 字段类型
export interface FieldType {
  type: string;
  name: string;
  description: string;
  supported_operators: string[];
}

// 规则验证请求
export interface ValidateRuleRequest {
  rule_tree: Record<string, any>;
  context?: Record<string, any>;
}

// 规则评估请求
export interface EvaluateRuleRequest {
  rule_tree: Record<string, any>;
  contact_data: Record<string, any>;
}

// ========== 分析相关类型 ==========

// 活动统计
export interface CampaignStats {
  campaign_id: string;
  sent_count: number;
  delivered_count: number;
  open_count: number;
  click_count: number;
  bounce_count: number;
  complaint_count: number;
  unsubscribe_count: number;
  open_rate: number;
  click_rate: number;
  bounce_rate: number;
  complaint_rate: number;
  unsubscribe_rate: number;
  updated_at: string;
}

// 实时统计
export interface RealtimeStats {
  campaign_id: string;
  current_sent: number;
  current_delivered: number;
  current_opens: number;
  current_clicks: number;
  send_rate_per_hour: number;
  timestamp: string;
}

// 链接分析
export interface LinkAnalytics {
  url: string;
  click_count: number;
  unique_click_count: number;
  click_rate: number;
  first_click_at?: string;
  last_click_at?: string;
}

// 用户旅程
export interface UserJourney {
  subscriber_id: number;
  email: string;
  journey_events: JourneyEvent[];
}

// 旅程事件
export interface JourneyEvent {
  event_type: string;
  event_time: string;
  campaign_id?: string;
  template_id?: string;
  url?: string;
  device?: string;
  location?: string;
}

// ========== 追踪相关类型 ==========

// 追踪事件
export interface TrackingEvent {
  event_type: string;
  email: string;
  campaign_id?: string;
  template_id?: string;
  url?: string;
  user_agent?: string;
  ip_address?: string;
  timestamp: string;
}

// 批量追踪事件请求
export interface BatchTrackingEventsRequest {
  events: TrackingEvent[];
}

// ========== 通用响应类型 ==========
// 统一使用全局 ApiResponse<T>，此处不再重复定义

// 分页数据响应
export interface PaginatedResponse<T> {
  items: T[];
  pagination: EmailSystemPaginationResponse;
}

// 操作结果响应
export interface OperationResult {
  success: boolean;
  message?: string;
  affected_count?: number;
  errors?: any[];
}