/**
 * 统一API响应基础类型
 */
export interface ApiResponse<T = any> {
    code: number;
    message: string;
    data: T;
    success?: boolean;
    errors?: ErrorDetail[];
    meta?: {
        request_id?: string;
        timestamp?: number;
        pagination?: PaginationMeta;
    };
}

/**
 * 错误详情
 */
export interface ErrorDetail {
    field: string;
    message: string;
    value?: any;
}


/**
 * 分页参数
 */
export interface PaginationParams {
    page?: number;
    page_size?: number;
    order_by?: string;
    order_desc?: boolean;
}

/**
 * 分页元数据
 */
export interface PaginationMeta {
    page: number;
    total: number;
    size: number,
    pages: number;
    has_next: boolean;
    has_prev: boolean;
    next_cursor: object
}

/**
 * 分页响应
 */
export interface PaginationResponse<T> {
    items: T[];
    pagination: PaginationMeta;
}

/**
 * 批量操作响应
 */
export interface BatchOperationResponse {
    success_count: number;
    failure_count: number;
    errors: string[];
}

/**
 * 统计响应基础类型
 */
export interface StatisticsResponse {
    period?: string;
    start_time?: number;
    end_time?: number;
    data: Record<string, any>;
}

// ============ 用户认证相关类型 ============

/**
 * 登录请求
 */
export interface LoginRequest {
    username: string;
    password: string;
    captcha_id?: string;
    captcha_code?: string;
}

/**
 * 注册请求
 */
export interface RegisterRequest {
    username: string;
    email: string;
    password: string;
    confirm_password?: string;
    tenant_id?: number;
    app_id?: string;
}

/**
 * 注册响应
 */
export interface RegisterResponse {
    user_id: number;
    username: string;
    email: string;
    message: string;
}

/**
 * 用户角色
 */
export interface UserRole {
    id: number;
    name: string;
    display_name?: string;
    code?: string;
}

/**
 * 用户信息
 */
export interface UserInfo {
    id: number;
    username: string;
    email?: string;
    real_name?: string;
    status: string;
    tenant_id: number;
    avatar?: string;
    roles: UserRole[];
}

/**
 * 登录响应
 */
export interface LoginResponse {
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
    user: UserInfo;
    requires_mfa?: boolean;
    mfa_type?: string;
}

/**
 * 刷新令牌请求
 */
export interface RefreshTokenRequest {
    refresh_token: string;
}

// ============ 菜单权限相关类型 ============

/**
 * 菜单类型枚举
 */
export enum MenuType {
    DIRECTORY = 'directory',
    MENU = 'menu',
    BUTTON = 'button'
}

/**
 * 菜单状态枚举
 */
export enum MenuStatus {
    ACTIVE = 'active',
    DISABLED = 'disabled'
}

/**
 * 菜单项基础接口
 */
export interface MenuItem {
    key: string;
    label: string;
    path?: string;
    icon?: string;
    type: 'menu' | 'submenu' | 'divider';
    children?: MenuItem[];
}

/**
 * 菜单树节点
 */
export interface MenuTreeNode {
    id: number;
    parent_id?: number;
    name: string;
    code: string;
    display_name: string;
    path?: string;
    icon?: string;
    type: MenuType;
    sort_order: number;
    is_visible: boolean;
    is_cache?: boolean;
    is_affix?: boolean;
    is_frame?: boolean;
    frame_url?: string;
    status: MenuStatus;
    resource_type?: string;
    children?: MenuTreeNode[];
    permissions?: string[];
    created_at: string;
    updated_at: string;
}

/**
 * 按钮权限
 */
export interface ButtonPermission {
    code: string;
    name: string;
    icon?: string;
    type?: 'primary' | 'success' | 'warning' | 'danger';
}


/**
 * 用户按钮权限响应
 */
export interface UserButtonPermissionsResponse {
    menu_path: string;
    permissions: string[];
}

/**
 * 权限检查响应
 */
export interface CheckPermissionResponse {
    has_permission: boolean;
    code: string;
}

/**
 * 批量权限检查请求
 */
export interface BatchCheckPermissionRequest {
    permissions: Array<{
        code: string;
        key: string;
    }>;
}

/**
 * 批量权限检查响应
 */
export interface BatchCheckPermissionResponse {
    results: Record<string, boolean>;
}



// ============ 模板管理相关类型 ============

/**
 * 模板基础接口
 */
export interface Template {
    id: number;
    tenant_id: number;
    name: string;
    code: string;
    description?: string;
    content: string;
    variables?: Record<string, string>;
    is_active: boolean;
    is_system: boolean;
    created_at: string;
    updated_at: string;
}


/**
 * 模板列表查询参数
 */
export interface TemplateListParams extends PaginationParams {
    keyword?: string;
    is_active?: boolean;
    is_system?: boolean;
}

// ============ 验证相关类型 ============

/**
 * 发送验证请求
 */
export interface SendVerificationRequest {
    target: string;
    target_type: number; // 1: 手机号, 2: 邮箱
    purpose: number; // 验证用途
    token_type?: number; // 令牌类型: 1: 验证码, 2: 链接
    user_id?: number;
    ip_address?: string;
    user_agent?: string;
    extra_data?: Record<string, any>;
}

/**
 * 发送验证响应
 */
export interface SendVerificationResponse {
    token_id: number;
    expires_at: string;
    rate_limit_info?: {
        remaining_today: number;
        next_available_at: string;
        window_reset_at: string;
    };
}

/**
 * 验证令牌请求
 */
export interface VerifyTokenRequest {
    target: string;
    token: string;
    purpose: number;
    ip_address?: string;
    user_agent?: string;
}

/**
 * 验证令牌响应
 */
export interface VerifyTokenResponse {
    valid: boolean;
    target: string;
    target_type: number;
    purpose: number;
    verified_at: string;
    extra_data?: Record<string, any>;
}
