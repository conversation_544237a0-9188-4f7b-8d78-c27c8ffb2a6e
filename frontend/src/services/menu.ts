import { apiService, API_ENDPOINTS } from '../utils/request';

// 导入统一的类型定义
import type {
  ApiResponse,
  MenuTreeNode,
  UserButtonPermissionsResponse,
  CheckPermissionResponse,
  BatchCheckPermissionRequest,
  BatchCheckPermissionResponse,
  MenuItem,
  MenuType,
  MenuStatus
} from '../types';


// 重新导出类型以供其他模块使用
export type { MenuTreeNode, UserButtonPermissionsResponse };

/**
 * 获取用户菜单树
 */
export const getUserMenuTree = async (): Promise<MenuTreeNode[]> => {
  try {
    const response = await apiService.post<ApiResponse<MenuTreeNode[]>>(API_ENDPOINTS.MENU.USER_TREE, {});
    // 检查响应结构 - 使用服务端实际返回的结构
    if (!response.data) {
      return [];
    }
    
    // 使用服务端实际返回的结构：response.data.data
    const menusData = response.data;
    
    if (!menusData) {
      return [];
    }
    
    const result = Array.isArray(menusData) ? menusData : [];
    
    return result;
  } catch (error) {
    console.error('Failed to get user menu tree:', error);
    return [];
  }
};

/**
 * 获取用户按钮权限
 * @deprecated 建议使用 useMenu Hook 中的 getMenuPermissions 方法，避免额外的API调用
 */
export const getUserButtonPermissions = async (menuPath: string): Promise<string[]> => {
  console.warn('getUserButtonPermissions is deprecated. Use useMenu hook instead for better performance.');
  try {
    const response = await apiService.post<UserButtonPermissionsResponse>(
      API_ENDPOINTS.MENU.USER_BUTTONS, 
      { menu_path: menuPath }
    );
    return response.data.permissions || [];
  } catch (error) {
    console.error('Failed to get user button permissions:', error);
    return [];
  }
};

/**
 * 检查单个权限
 * @deprecated 建议使用 useMenu Hook 中的 hasPermission 方法，避免额外的API调用
 */
export const checkPermission = async (permission: string): Promise<boolean> => {
  console.warn('checkPermission is deprecated. Use useMenu hook instead for better performance.');
  try {
    const response = await apiService.post<CheckPermissionResponse>(
      API_ENDPOINTS.MENU.CHECK_PERMISSION, 
      { permission_code: permission }
    );
    return response.data.has_permission;
  } catch (error) {
    console.error('Failed to check permission:', error);
    return false;
  }
};

/**
 * 批量检查权限
 * @deprecated 建议使用 useMenu Hook 中的 hasAnyPermission/hasAllPermissions 方法，避免额外的API调用
 */
export const batchCheckPermissions = async (permissions: Array<{ code: string; key: string }>): Promise<Record<string, boolean>> => {
  console.warn('batchCheckPermissions is deprecated. Use useMenu hook instead for better performance.');
  try {
    const response = await apiService.post<BatchCheckPermissionResponse>(
      API_ENDPOINTS.MENU.BATCH_CHECK_PERMISSION, 
      { permissions }
    );
    return response.data.results || {};
  } catch (error) {
    console.error('Failed to batch check permissions:', error);
    return {};
  }
};

/**
 * 将菜单树转换为面包屑
 */
export const generateBreadcrumbs = (menuTree: MenuTreeNode[], currentPath: string): Array<{ title: string; path: string }> => {
  const breadcrumbs: Array<{ title: string; path: string }> = [];
  
  const findPath = (nodes: MenuTreeNode[], targetPath: string, currentBreadcrumbs: Array<{ title: string; path: string }>): boolean => {
    for (const node of nodes) {
      const newBreadcrumbs = [...currentBreadcrumbs, { title: node.display_name, path: node.path || '' }];
      
      if (node.path === targetPath) {
        breadcrumbs.push(...newBreadcrumbs);
        return true;
      }
      
      if (node.children && findPath(node.children, targetPath, newBreadcrumbs)) {
        return true;
      }
    }
    return false;
  };
  
  findPath(menuTree, currentPath, []);
  return breadcrumbs;
};

/**
 * 将MenuTreeNode转换为MenuItem（用于Ant Design Menu组件）
 */
export const convertToMenuItem = (node: MenuTreeNode): MenuItem => {
  return {
    key: node.code,
    label: node.display_name,
    path: node.path,
    icon: node.icon,
    type: 'menu',
    children: node.children?.map(child => convertToMenuItem(child)),
  };
};

/**
 * 将菜单树转换为MenuItem数组
 */
export const convertMenuTreeToMenuItems = (menuTree: MenuTreeNode[]): MenuItem[] => {
  return menuTree.map(node => convertToMenuItem(node));
};
