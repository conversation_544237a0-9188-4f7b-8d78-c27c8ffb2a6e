import {apiService, API_ENDPOINTS} from '../utils/request';
import { ApiResponse } from '../types/api';

export interface Application {
    internal_app_id: number;
    app_id: string;
    app_name: string;
    app_type: string;
    description?: string;
    app_secret: string;
    tenant_id: number;
    status: string;
    is_system: boolean;
    is_active: boolean;
    callback_urls: string[];
    allowed_origins: string[];
    scopes: string[];
    rate_limit: number;
    logo_url: string;
    homepage_url: string;
    privacy_policy_url: string;
    terms_of_service_url: string;
    contact_email: string;
    config: Record<string, any>;
    created_by: number;
    updated_by: number;
    created_at: string;
    updated_at: string;
}

export interface CreateApplicationRequest {
    app_name: string;
    app_type: string;
    description?: string;
    tenant_id: number;
    status: string;
    is_system: boolean;
    is_active: boolean;
    callback_urls?: string[];
    allowed_origins?: string[];
    scopes?: string[];
    rate_limit?: number;
    logo_url?: string;
    homepage_url?: string;
    privacy_policy_url?: string;
    terms_of_service_url?: string;
    contact_email?: string;
    config?: Record<string, any>;
}

export interface UpdateApplicationRequest {
    internal_app_id: number;
    app_name?: string;
    app_type?: string;
    description?: string;
    status?: string;
    is_active?: boolean;
    callback_urls?: string[];
    allowed_origins?: string[];
    scopes?: string[];
    rate_limit?: number;
    logo_url?: string;
    homepage_url?: string;
    privacy_policy_url?: string;
    terms_of_service_url?: string;
    contact_email?: string;
    config?: Record<string, any>;
}

export interface ListApplicationsResponse {
    list: Application[];
    total: number;
    page: number;
    page_size: number;
}

// 获取应用列表
export async function getApplications(params: any): Promise<ApiResponse<Application[]>> {
    return await apiService.post<Application[]>(API_ENDPOINTS.APPLICATION.LIST, params);
}

// 根据租户ID获取应用列表
export async function getApplicationsByTenantId(tenantId: number, params?: any): Promise<ApiResponse<Application[]>> {
    const requestParams = {
        tenant_id: tenantId,
        page: 1,
        page_size: 100,
        ...params
    };
    return await apiService.post<Application[]>(API_ENDPOINTS.APPLICATION.LIST, requestParams);
}

// 获取当前用户租户下的应用列表
export async function getCurrentTenantApplications(params?: any): Promise<ApiResponse<Application[]>> {
    const requestParams = {
        page: 1,
        page_size: 100,
        ...params
    };
    return await apiService.post<Application[]>(API_ENDPOINTS.APPLICATION.LIST, requestParams);
}

// 创建应用
export async function createApplication(data: CreateApplicationRequest): Promise<ApiResponse<Application>> {
    return await apiService.post<Application>(API_ENDPOINTS.APPLICATION.CREATE, data);
}

// 更新应用
export async function updateApplication(updateData: UpdateApplicationRequest): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.APPLICATION.UPDATE, updateData);
}

// 删除应用
export async function deleteApplication(internalAppId: number): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.APPLICATION.DELETE, {internal_app_id: internalAppId});
}

// 启用应用
export async function enableApplication(internalAppId: number): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.APPLICATION.ENABLE, {internal_app_id: internalAppId});
}

// 禁用应用
export async function disableApplication(internalAppId: number): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.APPLICATION.DISABLE, {internal_app_id: internalAppId});
}

// 获取应用详情
export async function getApplication(internalAppId: number): Promise<ApiResponse<Application>> {
    return await apiService.post<Application>(API_ENDPOINTS.APPLICATION.GET, {internal_app_id: internalAppId});
}

// 根据app_id获取应用
export async function getApplicationByAppId(appId: string): Promise<ApiResponse<Application>> {
    return await apiService.post<Application>(API_ENDPOINTS.APPLICATION.GET_BY_APP_ID, {app_id: appId});
} 