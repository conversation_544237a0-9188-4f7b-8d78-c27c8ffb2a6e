import {apiService} from '../utils/request';
import {handle<PERSON><PERSON>rror, <PERSON>rrorHandleResult} from '../utils/errorHandler';
import {API_ENDPOINTS} from '../utils/request';
import {ApiResponse} from "../types";

export interface Permission {
    id: number;
    name: string;
    display_name: string;
    action: string; // 操作类型：create, read, update, delete等
    scope: string;
    scope_display_name: string;
    description?: string;
    status: string;
    created_at: string;
    updated_at: string;
    is_system?: boolean;
}

export interface CreatePermissionRequest {
    name: string;
    display_name: string;
    action: string; // 操作类型：create, read, update, delete等
    scope: string;
    description?: string;
}

export interface UpdatePermissionRequest {
    id: number;
    name: string;
    display_name: string;
    action: string; // 操作类型：create, read, update, delete等
    scope: string;
    description?: string;
    status?: string;
}

export interface ListPermissionRequest {
    page: number;
    size: number;
    keyword?: string;
    status?: string;
    action?: string; // 操作类型过滤
}

export interface ListPermissionResponse {
    data: Permission[];
    page: number;
    size: number;
    total: number;
}

export interface PermissionStats {
    total_permissions: number;
    active_permissions: number;
    system_permissions: number;
    action_count: number; // 按操作类型统计
}

// 批量创建权限相关接口
export interface CreatePermissionItem {
    name: string;
    code: string;
    display_name: string;
    action: string; // 操作类型：create, read, update, delete等
    scope: string;
    description?: string;
}

export interface BatchCreatePermissionsRequest {
    permissions: CreatePermissionItem[];
    is_system?: boolean;
}

export interface SkippedPermission {
    name: string;
    code: string;
    display_name: string;
    action: string; // 操作类型
    scope: string;
    reason: string;
}

export interface BatchCreatePermissionsResponse {
    created_permissions: Permission[];
    skipped_permissions: SkippedPermission[];
    success_count: number;
    skipped_count: number;
    total_count: number;
}

// 获取权限列表
export async function getPermissions(params?: ListPermissionRequest): Promise<ApiResponse<Permission[]>> {
    return await apiService.post<Permission[]>(API_ENDPOINTS.PERMISSION.LIST, params || {});
}

// 创建权限
export async function createPermission(data: CreatePermissionRequest): Promise<ApiResponse<Permission>> {
    return await apiService.post<Permission>(API_ENDPOINTS.PERMISSION.CREATE, data);
}

// 批量创建权限
export async function batchCreatePermissions(data: BatchCreatePermissionsRequest): Promise<ApiResponse<BatchCreatePermissionsResponse>> {
    return await apiService.post<BatchCreatePermissionsResponse>(API_ENDPOINTS.PERMISSION.BATCH_CREATE, data);
}

// 更新权限
export async function updatePermission(id: number, data: UpdatePermissionRequest): Promise<ApiResponse<null>> {
    const updateData = {...data, id};
    return await apiService.post<null>(API_ENDPOINTS.PERMISSION.UPDATE, updateData);
}

// 删除权限
export async function deletePermission(id: number): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.PERMISSION.DELETE, {id});
}

// 获取权限详情
export async function getPermission(id: number): Promise<ApiResponse<Permission>> {
    return await apiService.post<Permission>(API_ENDPOINTS.PERMISSION.GET, {id});
}

// 获取权限统计
export async function getPermissionStats(): Promise<ApiResponse<PermissionStats>> {
    return await apiService.post<PermissionStats>(API_ENDPOINTS.PERMISSION.STATS, {});
} 