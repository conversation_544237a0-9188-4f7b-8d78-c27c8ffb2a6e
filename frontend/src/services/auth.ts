import { apiService, API_ENDPOINTS } from '../utils/request';
import type {
  LoginRequest,
  LoginResponse,
  UserInfo,
  RefreshTokenRequest, RegisterRequest, RegisterResponse
} from '../types';
import type { ApiResponse } from '../types/api';

// 重新导出类型以供其他模块使用
export type { LoginRequest, LoginResponse, UserInfo, RefreshTokenRequest };

// 安全的token存储类
class SecureTokenStorage {
  private static readonly ACCESS_TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private static readonly USER_INFO_KEY = 'user_info';
  private static readonly TOKEN_EXPIRY_KEY = 'token_expiry';

  // 存储访问令牌 - 修改为使用 localStorage 确保跨窗口共享
  static setAccessToken(token: string, expiresIn: number): void {
    try {
      // 计算过期时间
      const expiryTime = Date.now() + expiresIn * 1000;
      
      // 使用 localStorage 存储访问令牌（跨窗口共享）
      localStorage.setItem(this.ACCESS_TOKEN_KEY, token);
      localStorage.setItem(this.TOKEN_EXPIRY_KEY, expiryTime.toString());
    } catch (error) {
      console.error('Failed to store access token:', error);
    }
  }

  // 获取访问令牌 - 修改为从 localStorage 获取
  static getAccessToken(): string | null {
    try {
      const token = localStorage.getItem(this.ACCESS_TOKEN_KEY);
      const expiryTime = localStorage.getItem(this.TOKEN_EXPIRY_KEY);
      
      if (!token || !expiryTime) {
        return null;
      }

      // 检查是否过期
      if (Date.now() > parseInt(expiryTime)) {
        this.clearTokens();
        return null;
      }

      return token;
    } catch (error) {
      console.error('Failed to get access token:', error);
      return null;
    }
  }

  // 存储刷新令牌
  static setRefreshToken(token: string): void {
    try {
      // 使用 localStorage 存储刷新令牌（持久化）
      localStorage.setItem(this.REFRESH_TOKEN_KEY, token);
    } catch (error) {
      console.error('Failed to store refresh token:', error);
    }
  }

  // 获取刷新令牌
  static getRefreshToken(): string | null {
    try {
      return localStorage.getItem(this.REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to get refresh token:', error);
      return null;
    }
  }

  // 存储用户信息 - 修改为使用 localStorage 确保跨窗口共享
  static setUserInfo(userInfo: UserInfo): void {
    try {
      localStorage.setItem(this.USER_INFO_KEY, JSON.stringify(userInfo));
    } catch (error) {
      console.error('Failed to store user info:', error);
    }
  }

  // 获取用户信息 - 修改为从 localStorage 获取
  static getUserInfo(): UserInfo | null {
    try {
      const userInfo = localStorage.getItem(this.USER_INFO_KEY);
      return userInfo ? JSON.parse(userInfo) : null;
    } catch (error) {
      console.error('Failed to get user info:', error);
      return null;
    }
  }

  // 清除所有令牌 - 修改为清除 localStorage
  static clearTokens(): void {
    try {
      localStorage.removeItem(this.ACCESS_TOKEN_KEY);
      localStorage.removeItem(this.TOKEN_EXPIRY_KEY);
      localStorage.removeItem(this.USER_INFO_KEY);
      localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to clear tokens:', error);
    }
  }

  // 检查是否已登录
  static isLoggedIn(): boolean {
    return this.getAccessToken() !== null;
  }

  // 检查令牌是否即将过期（5分钟内）
  static isTokenExpiringSoon(): boolean {
    try {
      const expiryTime = localStorage.getItem(this.TOKEN_EXPIRY_KEY);
      if (!expiryTime) return true;
      
      const timeUntilExpiry = parseInt(expiryTime) - Date.now();
      return timeUntilExpiry < 5 * 60 * 1000; // 5分钟
    } catch (error) {
      return true;
    }
  }
}

// 登录函数
export async function login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
  try {
    // 移除请求体中的租户代码，因为已经通过全局拦截器处理
    const { ...loginData } = credentials;
    
    return await apiService.post<LoginResponse>(API_ENDPOINTS.AUTH.LOGIN, loginData);
  } catch (error: any) {
    // 特殊处理404错误，提供友好的错误信息
    if (error.response?.status === 404) {
      return {
        code: 404,
        message: '登录服务暂时不可用，请稍后重试或联系管理员',
        data: {} as LoginResponse, // 使用空对象而不是null
        meta: {
          timestamp: Date.now(),
          request_id: error.response?.headers?.['x-request-id'] || ''
        }
      };
    }
    
    // 处理其他网络错误
    if (error.response) {
      return {
        code: error.response.status,
        message: error.response.data?.message || '登录失败，请稍后重试',
        data: {} as LoginResponse, // 使用空对象而不是null
        meta: {
          timestamp: Date.now(),
          request_id: error.response?.headers?.['x-request-id'] || ''
        }
      };
    }
    
    // 处理网络连接错误
    return {
      code: 500,
      message: '网络连接失败，请检查网络设置',
      data: {} as LoginResponse, // 使用空对象而不是null
      meta: {
        timestamp: Date.now()
      }
    };
  }
}

// 登出函数
export async function logout(): Promise<void> {
  try {
    // 调用后端登出接口
    const token = SecureTokenStorage.getAccessToken();
    if (token) {
      await apiService.post(API_ENDPOINTS.AUTH.LOGOUT, {
        all: true // 撤销所有会话
      });
    }
  } catch (error) {
    console.error('Logout error:', error);
  } finally {
    // 清除本地存储的令牌
    SecureTokenStorage.clearTokens();
  }
}

// 刷新令牌
export async function refreshToken(): Promise<LoginResponse | null> {
  try {
    const refreshToken = SecureTokenStorage.getRefreshToken();
    if (!refreshToken) {
      return null;
    }

    const res = await apiService.post(API_ENDPOINTS.AUTH.REFRESH, {
      refresh_token: refreshToken
    });

    // 检查业务状态码
    if (res.data.code !== 0) {
      console.error('Refresh token failed:', res.data.message);
      SecureTokenStorage.clearTokens();
      return null;
    }

    const loginData = res.data.data as LoginResponse;

    // 更新存储的令牌
    if (loginData.access_token) {
      SecureTokenStorage.setAccessToken(loginData.access_token, loginData.expires_in);
      SecureTokenStorage.setRefreshToken(loginData.refresh_token);
      SecureTokenStorage.setUserInfo(loginData.user);
    }

    return loginData;
  } catch (error) {
    console.error('Refresh token error:', error);
    // 刷新失败，清除所有令牌
    SecureTokenStorage.clearTokens();
    return null;
  }
}

// 获取当前用户信息
export function getCurrentUser(): UserInfo | null {
  return SecureTokenStorage.getUserInfo();
}

// 检查是否已登录
export function isLoggedIn(): boolean {
  return SecureTokenStorage.isLoggedIn();
}

// 获取访问令牌
export function getAccessToken(): string | null {
  return SecureTokenStorage.getAccessToken();
}

// 清除认证信息
export function clearAuth(): void {
  SecureTokenStorage.clearTokens();
}

// 导出安全存储类（用于测试）
export { SecureTokenStorage }; 

// 忘记密码请求
export interface ForgotPasswordRequest {
  username: string;
  email?: string;
  phone?: string;
}

// 重置密码请求
export interface ResetPasswordRequest {
  token: string;
  new_password: string;
  confirm_password: string;
}

// 验证重置令牌请求
export interface VerifyResetTokenRequest {
  token: string;
}

// 忘记密码 - 发送重置链接
export async function forgotPassword(data: ForgotPasswordRequest): Promise<ApiResponse<any>> {
  try {
    return await apiService.post(API_ENDPOINTS.AUTH.FORGOT_PASSWORD, data);
  } catch (error: any) {
    // 处理网络错误
    if (error.response) {
      return {
        code: error.response.status,
        message: error.response.data?.message || '发送重置链接失败',
        data: null,
        meta: {
          timestamp: Date.now(),
          request_id: error.response?.headers?.['x-request-id'] || ''
        }
      };
    }
    
    return {
      code: 500,
      message: '网络连接失败，请检查网络设置',
      data: null,
      meta: {
        timestamp: Date.now()
      }
    };
  }
}

// 验证重置令牌
export async function verifyResetToken(token: string): Promise<ApiResponse<any>> {
  try {
    return await apiService.post(API_ENDPOINTS.AUTH.RESET_PASSWORD, {
      token: token
    });
  } catch (error: any) {
    if (error.response) {
      return {
        code: error.response.status,
        message: error.response.data?.message || '验证重置令牌失败',
        data: null,
        meta: {
          timestamp: Date.now(),
          request_id: error.response?.headers?.['x-request-id'] || ''
        }
      };
    }
    
    return {
      code: 500,
      message: '网络连接失败，请检查网络设置',
      data: null,
      meta: {
        timestamp: Date.now()
      }
    };
  }
}

// 重置密码
export async function resetPassword(data: ResetPasswordRequest): Promise<ApiResponse<any>> {
  try {
    return await apiService.post(API_ENDPOINTS.AUTH.RESET_PASSWORD, data);
  } catch (error: any) {
    if (error.response) {
      return {
        code: error.response.status,
        message: error.response.data?.message || '重置密码失败',
        data: null,
        meta: {
          timestamp: Date.now(),
          request_id: error.response?.headers?.['x-request-id'] || ''
        }
      };
    }
    
    return {
      code: 500,
      message: '网络连接失败，请检查网络设置',
      data: null,
      meta: {
        timestamp: Date.now()
      }
    };
  }
}

// 用户注册
export async function register(data: RegisterRequest): Promise<ApiResponse<RegisterResponse | null>> {
  try {
    return await apiService.post(API_ENDPOINTS.AUTH.REGISTER, data);
  } catch (error: any) {
    // 处理网络错误
    if (error.response) {
      return {
        code: error.response.status,
        message: error.response.data?.message || '注册失败',
        data: null,
        meta: {
          timestamp: Date.now(),
          request_id: error.response?.headers?.['x-request-id'] || ''
        }
      };
    }
    
    return {
      code: 500,
      message: '网络连接失败，请检查网络设置',
      data: null,
      meta: {
        timestamp: Date.now()
      }
    };
  }
} 