import { apiService } from '../utils/request';
import { ApiResponse } from '../types/api';
import {
  // 联系人相关类型
  Contact,
  CreateContactRequest,
  UpdateContactRequest,
  SearchContactsRequest,
  BatchCreateContactsRequest,
  BatchUpdateContactsRequest,
  UpdateContactStatusRequest,
  
  // 标签相关类型
  Tag,
  CreateTagRequest,
  UpdateTagRequest,
  AssignTagRequest,
  MergeTagRequest,
  RenameTagRequest,
  
  // 人群圈选相关类型
  Segment,
  CreateSegmentRequest,
  UpdateSegmentRequest,
  SegmentJob,
  
  // 发送计划相关类型
  SendPlan,
  CreateSendPlanRequest,
  UpdateSendPlanRequest,
  
  // 导入相关类型
  ImportJob,
  CreateImportJobRequest,
  ImportError,
  ImportBatch,
  
  // 规则引擎相关类型
  ValidateRuleRequest,
  EvaluateRuleRequest,
  RuleOperator,
  FieldType,
  
  // 分析相关类型
  CampaignStats,
  RealtimeStats,
  LinkAnalytics,
  UserJourney,
  
  // 追踪相关类型
  BatchTrackingEventsRequest,
  
  // 通用类型
  PaginatedResponse,
  OperationResult,
} from '../types/email-system';

const EMAIL_SYSTEM_BASE_URL = '/api/email-system/v1';

// ========== 联系人服务 ==========
export class ContactService {
  // 创建联系人
  static async createContact(data: CreateContactRequest): Promise<ApiResponse<Contact>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/contacts/create`, data);
  }

  // 更新联系人
  static async updateContact(data: UpdateContactRequest): Promise<ApiResponse<Contact>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/contacts/update`, data);
  }

  // 获取联系人详情
  static async getContact(params: { id?: number; email?: string; fields?: string }): Promise<ApiResponse<Contact>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/contacts/detail`, { params });
  }

  // 搜索联系人
  static async searchContacts(data: SearchContactsRequest): Promise<ApiResponse<PaginatedResponse<Contact>>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/contacts/search`, data);
  }

  // 删除联系人
  static async deleteContact(data: { id: number }): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/contacts/delete`, data);
  }

  // 批量创建联系人
  static async batchCreateContacts(data: BatchCreateContactsRequest): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/contacts/batch-create`, data);
  }

  // 批量更新联系人
  static async batchUpdateContacts(data: BatchUpdateContactsRequest): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/contacts/batch-update`, data);
  }

  // 更新联系人状态
  static async updateContactStatus(data: UpdateContactStatusRequest): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/contacts/update-status`, data);
  }
}

// ========== 标签服务 ==========
export class TagService {
  // 创建标签
  static async createTag(data: CreateTagRequest): Promise<ApiResponse<Tag>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/tags/create`, data);
  }

  // 更新标签
  static async updateTag(data: UpdateTagRequest): Promise<ApiResponse<Tag>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/tags/update`, data);
  }

  // 获取标签详情
  static async getTag(params: { id?: number; name?: string }): Promise<ApiResponse<Tag>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/tags/detail`, { params });
  }

  // 获取标签列表
  static async getTags(params?: { page?: number; size?: number; keyword?: string }): Promise<ApiResponse<PaginatedResponse<Tag>>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/tags/list`, { params });
  }

  // 删除标签
  static async deleteTag(data: { id: number }): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/tags/delete`, data);
  }

  // 分配标签
  static async assignTag(data: AssignTagRequest): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/tags/assign`, data);
  }

  // 刷新标签
  static async refreshTag(data: { id: number }): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/tags/refresh`, data);
  }

  // 合并标签
  static async mergeTags(data: MergeTagRequest): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/tags/merge`, data);
  }

  // 重命名标签
  static async renameTag(data: RenameTagRequest): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/tags/rename`, data);
  }

  // 获取热门标签
  static async getPopularTags(params?: { limit?: number }): Promise<ApiResponse<Tag[]>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/tags/popular`, { params });
  }

  // 获取最近使用的标签
  static async getRecentTags(params?: { limit?: number }): Promise<ApiResponse<Tag[]>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/tags/recent`, { params });
  }
}

// ========== 人群圈选服务 ==========
export class SegmentService {
  // 创建人群圈选
  static async createSegment(data: CreateSegmentRequest): Promise<ApiResponse<Segment>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/segments/create`, data);
  }

  // 更新人群圈选
  static async updateSegment(data: UpdateSegmentRequest): Promise<ApiResponse<Segment>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/segments/update`, data);
  }

  // 获取人群圈选详情
  static async getSegment(params: { id: number }): Promise<ApiResponse<Segment>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/segments/detail`, { params });
  }

  // 获取人群圈选列表
  static async getSegments(params?: { page?: number; size?: number; keyword?: string }): Promise<ApiResponse<PaginatedResponse<Segment>>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/segments/list`, { params });
  }

  // 删除人群圈选
  static async deleteSegment(data: { id: number }): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/segments/delete`, data);
  }

  // 预览人群圈选
  static async previewSegment(data: { rule_tree: Record<string, any>; sample_size?: number }): Promise<ApiResponse<Contact[]>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/segments/preview`, data);
  }

  // 重建人群圈选
  static async rebuildSegment(data: { id: number }): Promise<ApiResponse<{ job_id: string }>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/segments/rebuild`, data);
  }

  // 导出人群圈选
  static async exportSegment(params: { id: number; format?: string }): Promise<ApiResponse<{ download_url: string }>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/segments/export`, { params });
  }

  // 获取任务状态
  static async getSegmentJob(params: { job_id: string }): Promise<ApiResponse<SegmentJob>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/segments/job-status`, { params });
  }

  // 取消任务
  static async cancelSegmentJob(data: { job_id: string }): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/segments/cancel-job`, data);
  }
}

// ========== 发送计划服务 ==========
export class SendPlanService {
  // 创建发送计划
  static async createSendPlan(data: CreateSendPlanRequest): Promise<ApiResponse<SendPlan>> {
    return apiService.post('/api/v1/send-plans/create', data);
  }

  // 更新发送计划
  static async updateSendPlan(data: UpdateSendPlanRequest): Promise<ApiResponse<SendPlan>> {
    return apiService.post('/api/v1/send-plans/update', data);
  }

  // 获取发送计划详情
  static async getSendPlan(params: { id: number }): Promise<ApiResponse<SendPlan>> {
    return apiService.get('/api/v1/send-plans/detail', { params });
  }

  // 获取发送计划列表
  static async getSendPlans(params?: { page?: number; size?: number; status?: string }): Promise<ApiResponse<PaginatedResponse<SendPlan>>> {
    return apiService.get('/api/v1/send-plans/list', { params });
  }

  // 删除发送计划
  static async deleteSendPlan(data: { plan_id: number }): Promise<ApiResponse<OperationResult>> {
    return apiService.post('/api/v1/send-plans/delete', data);
  }

  // 开始发送计划
  static async startSendPlan(data: { plan_id: number }): Promise<ApiResponse<OperationResult>> {
    return apiService.post('/api/v1/send-plans/start', data);
  }

  // 暂停发送计划
  static async pauseSendPlan(data: { plan_id: number }): Promise<ApiResponse<OperationResult>> {
    return apiService.post('/api/v1/send-plans/pause', data);
  }

  // 取消发送计划
  static async cancelSendPlan(data: { plan_id: number }): Promise<ApiResponse<OperationResult>> {
    return apiService.post('/api/v1/send-plans/cancel', data);
  }
}

// ========== 导入服务 ==========
export class ImportService {
  // 创建导入任务
  static async createImportJob(data: CreateImportJobRequest): Promise<ApiResponse<ImportJob>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/import/create`, data);
  }

  // 获取导入任务列表
  static async getImportJobs(params?: { page?: number; size?: number; status?: string }): Promise<ApiResponse<PaginatedResponse<ImportJob>>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/import/job-list`, { params });
  }

  // 获取导入任务状态
  static async getImportJobStatus(params: { job_id: string }): Promise<ApiResponse<ImportJob>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/import/job-status`, { params });
  }

  // 开始处理导入任务
  static async processImportJob(data: { job_id: string }): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/import/process-job`, data);
  }

  // 取消导入任务
  static async cancelImportJob(data: { job_id: string }): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/import/cancel-job`, data);
  }

  // 重试导入任务
  static async retryImportJob(data: { job_id: string }): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/import/retry-job`, data);
  }

  // 删除导入任务
  static async deleteImportJob(data: { job_id: string }): Promise<ApiResponse<OperationResult>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/import/delete-job`, data);
  }

  // 获取导入错误列表
  static async getImportErrors(params: { job_id: string; page?: number; size?: number }): Promise<ApiResponse<PaginatedResponse<ImportError>>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/import/job-errors`, { params });
  }

  // 获取导入批次列表
  static async getImportBatches(params: { job_id: string; page?: number; size?: number }): Promise<ApiResponse<PaginatedResponse<ImportBatch>>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/import/job-batches`, { params });
  }
}

// ========== 规则引擎服务 ==========
export class RuleService {
  // 验证规则
  static async validateRule(data: ValidateRuleRequest): Promise<ApiResponse<{ valid: boolean; errors?: string[] }>> {
    return apiService.post('/api/v1/rules/validate', data);
  }

  // 评估规则
  static async evaluateRule(data: EvaluateRuleRequest): Promise<ApiResponse<{ result: boolean; details?: any }>> {
    return apiService.post('/api/v1/rules/evaluate', data);
  }

  // 获取规则模板
  static async getRuleTemplates(): Promise<ApiResponse<any[]>> {
    return apiService.get('/api/v1/rules/templates');
  }

  // 解析规则
  static async parseRule(data: { rule_text: string }): Promise<ApiResponse<Record<string, any>>> {
    return apiService.post('/api/v1/rules/parse', data);
  }

  // 构建联系人规则
  static async buildContactRule(data: { field: string; operator: string; value: any }): Promise<ApiResponse<Record<string, any>>> {
    return apiService.post('/api/v1/rules/build-contact', data);
  }

  // 获取规则操作符
  static async getRuleOperators(): Promise<ApiResponse<RuleOperator[]>> {
    return apiService.get('/api/v1/rules/operators');
  }

  // 获取字段类型
  static async getFieldTypes(): Promise<ApiResponse<FieldType[]>> {
    return apiService.get('/api/v1/rules/field-types');
  }

  // 预览标签规则
  static async previewTagRule(data: { rule_tree: Record<string, any>; sample_size?: number }): Promise<ApiResponse<Contact[]>> {
    return apiService.post('/api/v1/tags/preview-rule', data);
  }

  // 验证标签规则
  static async validateTagRule(data: ValidateRuleRequest): Promise<ApiResponse<{ valid: boolean; errors?: string[] }>> {
    return apiService.post('/api/v1/tags/validate-rule', data);
  }

  // 获取标签规则字段
  static async getTagRuleFields(): Promise<ApiResponse<FieldType[]>> {
    return apiService.get('/api/v1/tags/rule-fields');
  }

  // 预览人群圈选规则
  static async previewSegmentRule(data: { rule_tree: Record<string, any>; sample_size?: number }): Promise<ApiResponse<Contact[]>> {
    return apiService.post('/api/v1/segments/preview-rule', data);
  }

  // 验证人群圈选规则
  static async validateSegmentRule(data: ValidateRuleRequest): Promise<ApiResponse<{ valid: boolean; errors?: string[] }>> {
    return apiService.post('/api/v1/segments/validate-rule', data);
  }
}

// ========== 分析服务 ==========
export class AnalyticsService {
  // 获取活动统计
  static async getCampaignStats(params: { campaign_id: string }): Promise<ApiResponse<CampaignStats>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/analytics/campaign-stats`, { params });
  }

  // 获取实时统计
  static async getRealtimeStats(params: { campaign_id: string }): Promise<ApiResponse<RealtimeStats>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/analytics/campaign-realtime`, { params });
  }

  // 生成报表
  static async generateReport(data: { report_type: string; campaign_ids?: string[]; date_range?: { start: string; end: string } }): Promise<ApiResponse<{ report_id: string }>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/analytics/generate-report`, data);
  }

  // 获取报表详情
  static async getReport(params: { report_id: string }): Promise<ApiResponse<any>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/analytics/report-detail`, { params });
  }

  // 下载报表
  static async downloadReport(params: { report_id: string }): Promise<ApiResponse<{ download_url: string }>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/analytics/download-report`, { params });
  }

  // 创建导出任务
  static async createExport(data: { export_type: string; filters?: Record<string, any> }): Promise<ApiResponse<{ export_id: string }>> {
    return apiService.post(`${EMAIL_SYSTEM_BASE_URL}/analytics/create-export`, data);
  }

  // 获取导出状态
  static async getExportStatus(params: { export_id: string }): Promise<ApiResponse<{ status: string; progress: number; download_url?: string }>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/analytics/export-status`, { params });
  }

  // 下载导出文件
  static async downloadExport(params: { export_id: string }): Promise<ApiResponse<{ download_url: string }>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/analytics/download-export`, { params });
  }

  // 获取链接分析
  static async getLinkAnalytics(params: { campaign_id: string }): Promise<ApiResponse<LinkAnalytics[]>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/analytics/campaign-links`, { params });
  }

  // 获取热力图
  static async getHeatmap(params: { campaign_id: string }): Promise<ApiResponse<any>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/analytics/campaign-heatmap`, { params });
  }

  // 获取用户旅程
  static async getUserJourney(params: { subscriber_id: number }): Promise<ApiResponse<UserJourney>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/analytics/subscriber-journey`, { params });
  }

  // 获取转化漏斗
  static async getConversionFunnel(params: { campaign_id: string }): Promise<ApiResponse<any>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/analytics/conversion-funnel`, { params });
  }

  // 获取实时概览
  static async getRealtimeOverview(): Promise<ApiResponse<any>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/analytics/realtime-overview`);
  }

  // 获取实时事件
  static async getRealtimeEvents(params?: { limit?: number; event_type?: string }): Promise<ApiResponse<any[]>> {
    return apiService.get(`${EMAIL_SYSTEM_BASE_URL}/analytics/realtime-events`, { params });
  }
}

// ========== 追踪服务 ==========
export class TrackingService {
  // 批量处理追踪事件
  static async batchTrackingEvents(data: BatchTrackingEventsRequest): Promise<ApiResponse<OperationResult>> {
    return apiService.post('/api/tracking/batch', data);
  }

  // 处理转化事件
  static async handleConversion(data: { email: string; campaign_id?: string; conversion_type: string; value?: number }): Promise<ApiResponse<OperationResult>> {
    return apiService.post('/api/tracking/conversion', data);
  }

  // 处理入站回复
  static async handleInbound(data: { email: string; campaign_id?: string; reply_content: string }): Promise<ApiResponse<OperationResult>> {
    return apiService.post('/api/tracking/inbound', data);
  }
}

// 导出所有服务
export const emailSystemService = {
  contact: ContactService,
  tag: TagService,
  segment: SegmentService,
  sendPlan: SendPlanService,
  import: ImportService,
  rule: RuleService,
  analytics: AnalyticsService,
  tracking: TrackingService,
};