import {apiService} from '../utils/request';
import type {ApiResponse} from '../types';
import {API_ENDPOINTS} from '../utils/request';

export interface Permission {
    id: number;
    name: string;
    display_name: string;
    action: string; // 操作类型：create, read, update, delete等
    scope: string;
    scope_display_name: string;
    description?: string;
    status: string;
    created_at: string;
    updated_at: string;
}

export interface Role {
    id: number;
    tenant_id: number;
    name: string;
    display_name: string;
    code?: string;
    description?: string;
    status: string;
    is_system: boolean;
    created_at: string;
    updated_at: string;
    user_count: number;
}

export interface CreateRoleRequest {
    name: string;
    code: string;
    display_name: string;
    description?: string;
    is_system?: boolean;
}

export interface UpdateRoleRequest {
    id: number;
    name: string;
    code: string;
    display_name: string;
    description?: string;
    status?: string;
}

export interface ListRoleRequest {
    page?: number;
    size?: number;
    keyword?: string;
    status?: string;
}

export interface AssignPermissionRequest {
    role_id: number;
    permission_ids: number[];
}

export interface AssignRoleRequest {
    role_id: number;
    user_ids: number[];
}

export interface RoleStats {
    total_roles: number;
    active_roles: number;
    system_roles: number;
    total_users: number;
}

// 获取角色列表
export async function getRoles(params?: ListRoleRequest): Promise<ApiResponse<Role[]>> {
    return await apiService.post<Role[]>(API_ENDPOINTS.ROLE.LIST, params || {});
}

// 获取角色详情
export async function getRole(id: number): Promise<ApiResponse<Role>> {
    return await apiService.post<Role>(API_ENDPOINTS.ROLE.GET, {id});
}

// 创建角色
export async function createRole(data: CreateRoleRequest): Promise<ApiResponse<Role>> {
    return await apiService.post<Role>(API_ENDPOINTS.ROLE.CREATE, data);
}

// 更新角色
export async function updateRole(updateData: UpdateRoleRequest): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.ROLE.UPDATE, updateData);
}

// 删除角色
export async function deleteRole(id: number): Promise<ApiResponse<null>> {
    return await apiService.post<null>(API_ENDPOINTS.ROLE.DELETE, {id});
}

// 分配权限给角色
export async function assignPermissions(roleId: number, data: AssignPermissionRequest): Promise<ApiResponse<null>> {
    const requestData = {...data, role_id: roleId};
    return await apiService.post<null>(API_ENDPOINTS.ROLE.ASSIGN_PERMISSIONS, requestData);
}

// 获取角色权限
export async function getRolePermissions(roleId: number): Promise<ApiResponse<Permission[]>> {
    return await apiService.post<Permission[]>(API_ENDPOINTS.ROLE.PERMISSIONS, {role_id: roleId});
}

// 分配角色给用户
export async function assignRole(roleId: number, data: AssignRoleRequest): Promise<ApiResponse<null>> {
    const requestData = {...data, role_id: roleId};
    return await apiService.post<null>(API_ENDPOINTS.ROLE.ASSIGN, requestData);
}

// 获取角色用户
export async function getRoleUsers(roleId: number): Promise<ApiResponse<any[]>> {
    return await apiService.post<any[]>(API_ENDPOINTS.ROLE.USERS, {role_id: roleId});
}

// 获取权限列表
export async function getPermissions(): Promise<ApiResponse<Permission[]>> {
    // 权限列表接口已在 permission 服务中规范
    return await apiService.post<Permission[]>(API_ENDPOINTS.PERMISSION.LIST, {});
}

// 获取角色统计
export async function getRoleStats(): Promise<ApiResponse<RoleStats>> {
    return await apiService.post<RoleStats>(API_ENDPOINTS.ROLE.STATS, {});
} 