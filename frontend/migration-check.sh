#!/bin/bash

# 统一登录跳转系统 - 迁移检查脚本
# 用于识别需要迁移到新系统的组件

echo "🔍 检查需要迁移到统一登录跳转系统的文件..."
echo "=================================================="

# 查找包含旧式登录跳转模式的文件
echo "📁 查找使用navigate('/login')的文件:"
grep -r "navigate.*['\"]\/login['\"]" src/ --include="*.tsx" --include="*.ts" | grep -v node_modules | grep -v "utils/auth.ts" | grep -v "hooks/useAuthRedirect.ts"

echo -e "\n📁 查找手动调用clearAuth的文件:"
grep -r "clearAuth()" src/ --include="*.tsx" --include="*.ts" | grep -v node_modules | grep -v AuthContext.tsx | grep -v "utils/request.ts" | grep -v "utils/auth.ts"

echo -e "\n📁 查找直接使用window.location.href = '/login'的文件:"
grep -r "window\.location\.href.*login" src/ --include="*.tsx" --include="*.ts" | grep -v node_modules

echo -e "\n📁 查找设置location state for redirect的文件:"
grep -r "state.*from.*location" src/ --include="*.tsx" --include="*.ts" | grep -v node_modules | grep -v AuthContext.tsx | grep -v "utils/auth.ts"

echo -e "\n=================================================="
echo "✅ 检查完成！"
echo ""
echo "🔧 迁移建议:"
echo "1. 将 navigate('/login') 替换为 useAuthRedirect().redirectToLogin()"
echo "2. 将 clearAuth() + navigate('/login') 替换为对应的场景方法"
echo "3. 将手动的状态管理替换为统一系统的自动处理"
echo "4. 查看 src/utils/README-auth-redirect.md 获取详细迁移指南"