package adaptor

import (
	"context"

	"gitee.com/heiyee/platforms/ilike-backend/internal/infrastructure/external"
	"gitee.com/heiyee/platforms/pkg/httpmiddleware"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/api/userpb"
)

// UserInfoProviderAdapter 用户信息提供者适配器
type UserInfoProviderAdapter struct {
	UserClient *external.UserServiceClient
}

// GetUserInfo 实现 UserInfoProvider 接口
func (a *UserInfoProviderAdapter) GetUserInfo(ctx context.Context, token string, headers map[string]string) *httpmiddleware.AuthedUser {
	logger := logiface.GetLogger()
	userClient := a.UserClient

	resp, err := userClient.GetUserInfoByToken(ctx, token)
	if err != nil {
		logger.Warn(ctx, "Failed to get user info by token",
			logiface.Error(err),
			logiface.String("token_prefix", token[:min(len(token), 10)]+"..."))
		return nil
	}

	if resp == nil || resp.Code != 0 || resp.Data == nil {
		logger.Warn(ctx, "Invalid or expired token",
			logiface.Int("response_code", int(resp.Code)),
			logiface.String("response_message", resp.Message))
		return nil
	}

	return &httpmiddleware.AuthedUser{
		UserId:   resp.Data.UserId,
		Username: resp.Data.Username,
		RealName: resp.Data.RealName,
		Email:    resp.Data.Email,
		TenantId: resp.Data.TenantId,
	}
}

// AppInfoProviderAdapter 应用信息提供者适配器
type AppInfoProviderAdapter struct {
	UserClient *external.UserServiceClient
}

// GetAppInfo 实现 AppInfoProvider 接口
func (a *AppInfoProviderAdapter) GetAppInfo(ctx context.Context, appId string) *httpmiddleware.AppInfo {
	logger := logiface.GetLogger()

	// 获取底层 gRPC 客户端
	client, err := a.UserClient.GetUserClient(ctx)
	if err != nil {
		logger.Warn(ctx, "Failed to get user service client", logiface.Error(err))
		return nil
	}

	// 通过 appId 拉取应用信息
	req := &userpb.GetAppInfoRequest{InternalAppId: 0, AppId: appId}
	resp, err := client.GetAppInfo(ctx, req)
	if err != nil {
		logger.Warn(ctx, "Failed to get app info",
			logiface.Error(err),
			logiface.String("app_id", appId))
		return nil
	}

	if resp == nil || resp.Code != 0 || resp.Data == nil {
		logger.Warn(ctx, "Invalid app id",
			logiface.Int("response_code", int(resp.GetCode())),
			logiface.String("response_message", resp.GetMessage()),
			logiface.String("app_id", appId))
		return nil
	}

	return &httpmiddleware.AppInfo{
		TenantId:      resp.Data.TenantId,
		AppId:         resp.Data.AppId,
		InternalAppId: resp.Data.InternalAppId,
	}
}
