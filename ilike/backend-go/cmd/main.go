package main

import (
	"context"
	stderrors "errors"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"

	"gitee.com/heiyee/platforms/ilike-backend/internal/domain/errors"
	"gitee.com/heiyee/platforms/ilike-backend/internal/infrastructure/config"
	"gitee.com/heiyee/platforms/ilike-backend/internal/infrastructure/container"
	"gitee.com/heiyee/platforms/ilike-backend/internal/interfaces/http/routes"

	"gitee.com/heiyee/platforms/pkg/grpcregistry"
	"gitee.com/heiyee/platforms/pkg/httpmiddleware"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/otel"
	"gitee.com/heiyee/platforms/users/api/userpb"
)

const ServiceName = "platforms-ilike"

func main() {
	// 创建应用实例
	app := NewApplication()

	// 初始化应用
	if err := app.Initialize(); err != nil {
		log.Fatalf("Failed to initialize application: %v", err)
	}

	// 启动应用
	if err := app.Start(); err != nil {
		log.Fatalf("Failed to start application: %v", err)
	}

	// 等待关闭信号
	app.WaitForShutdown()

	// 优雅关闭
	if err := app.Shutdown(); err != nil {
		log.Printf("Error during shutdown: %v", err)
		os.Exit(1)
	}
}

// Application 应用主结构
type Application struct {
	config       *config.Config
	logger       logiface.Logger
	container    *container.DependencyContainer
	httpServer   *http.Server
	otelShutdown func(context.Context) error
	ctx          context.Context
	cancel       context.CancelFunc
}

// NewApplication 创建应用实例
func NewApplication() *Application {
	ctx, cancel := context.WithCancel(context.Background())
	return &Application{
		ctx:    ctx,
		cancel: cancel,
	}
}

// Initialize 初始化应用
func (app *Application) Initialize() error {
	// 1. 解析命令行参数
	flag.Parse()

	// 2. 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		return errors.NewIntegrationFailedError("配置加载失败")
	}
	app.config = cfg

	// 3. 初始化日志
	if err := app.initLogger(); err != nil {
		return errors.NewIntegrationFailedError("日志初始化失败")
	}

	// 4. 初始化 OpenTelemetry
	if err := app.initOpenTelemetry(); err != nil {
		return errors.NewIntegrationFailedError("OpenTelemetry初始化失败")
	}

	// 5. 启动配置热更新监听
	app.startConfigWatcher()

	// 6. 初始化依赖注入容器
	if err := app.initDependencyContainer(); err != nil {
		return errors.NewIntegrationFailedError("依赖容器初始化失败")
	}

	// 7. 初始化 gRPC 客户端管理器
	if err := app.initGRPCManager(); err != nil {
		return errors.NewIntegrationFailedError("gRPC管理器初始化失败")
	}

	// 8. 构建 HTTP 服务器
	if err := app.buildHTTPServer(); err != nil {
		return errors.NewIntegrationFailedError("HTTP服务器构建失败")
	}

	app.logger.Info(app.ctx, "Application initialized successfully",
		logiface.String("service", ServiceName),
		logiface.String("env", app.config.Server.Env))

	return nil
}

// initLogger 初始化日志系统
func (app *Application) initLogger() error {
	logConfig := logiface.MultiLogConfig{
		App:    app.config.Log["app"],
		Access: app.config.Log["access"],
		Error:  app.config.Log["error"],
	}

	// 开发环境强制控制台输出
	if app.config.Server.Env == "dev" {
		for k, logCfg := range app.config.Log {
			logCfg.Output = "stdout"
			logCfg.Format = "console"
			app.config.Log[k] = logCfg
		}
		logConfig = logiface.MultiLogConfig{
			App:    app.config.Log["app"],
			Access: app.config.Log["access"],
			Error:  app.config.Log["error"],
		}
	}

	logiface.InitLogger(logConfig)
	app.logger = logiface.GetLogger()
	httpmiddleware.SetAccessLogger(app.logger)
	return nil
}

// initOpenTelemetry 初始化 OpenTelemetry
func (app *Application) initOpenTelemetry() error {
	shutdown, err := otel.InitTracerProvider(ServiceName, app.config.Otel.Endpoint)
	if err != nil {
		app.logger.Error(app.ctx, "Failed to init OpenTelemetry", logiface.Error(err))
		return err
	}
	app.otelShutdown = shutdown
	return nil
}

// startConfigWatcher 启动配置监听
func (app *Application) startConfigWatcher() {
	go func() {
		if err := config.ListenNacosConfigChange(ServiceName); err != nil {
			app.logger.Warn(app.ctx, "Failed to start nacos config change listener", logiface.Error(err))
		}
	}()
}

// initDependencyContainer 初始化依赖注入容器
func (app *Application) initDependencyContainer() error {
	app.container = container.NewDependencyContainer(app.config, app.logger)
	return app.container.Initialize(app.ctx)
}

// initGRPCManager 初始化 gRPC 客户端管理器
func (app *Application) initGRPCManager() error {
	// 初始化全局 gRPC 客户端管理器
	grpcregistry.InitGlobalManager(app.logger)

	// 批量订阅 gRPC 服务
	if err := grpcregistry.BatchSubscribeServices(app.config.GRPCSubscriptions, app.logger); err != nil {
		return errors.NewIntegrationFailedError(fmt.Sprintf("gRPC services batch subscription failed: %v", err))
	}

	app.logger.Info(app.ctx, "gRPC client manager initialized successfully")
	return nil
}

// UserInfoProviderAdapter 用户信息提供者适配器
type UserInfoProviderAdapter struct {
	container *container.DependencyContainer
}

// GetUserInfo 实现 UserInfoProvider 接口
func (a *UserInfoProviderAdapter) GetUserInfo(ctx context.Context, token string, headers map[string]string) *httpmiddleware.AuthedUser {
	// 使用 UserClient 获取用户信息
	userInfo, err := a.container.Infrastructure.UsersClient.GetUserInfoByToken(ctx, token)
	if err != nil || userInfo == nil || userInfo.Code != 0 {
		return nil
	}

	return &httpmiddleware.AuthedUser{
		UserId:   userInfo.Data.UserId,
		Username: userInfo.Data.Username,
		RealName: userInfo.Data.RealName,
		Email:    userInfo.Data.Email,
		TenantId: userInfo.Data.TenantId,
	}
}

// AppInfoProviderAdapter 租户信息提供者适配器（对齐 email 的实现：通过 appId 获取 AppInfo）
type AppInfoProviderAdapter struct {
	container *container.DependencyContainer
}

// GetAppInfo 实现 AppInfoProvider 接口
func (a *AppInfoProviderAdapter) GetAppInfo(ctx context.Context, appId string) *httpmiddleware.AppInfo {
	// 直接使用 UsersClient.GetUserClient 获取 gRPC 客户端并调用 GetAppInfo
	client, err := a.container.Infrastructure.UsersClient.GetUserClient(ctx)
	if err != nil {
		return nil
	}
	req := &userpb.GetAppInfoRequest{InternalAppId: 0, AppId: appId}
	resp, err := client.GetAppInfo(ctx, req)
	if err != nil || resp == nil || resp.Code != 0 || resp.Data == nil {
		return nil
	}
	return &httpmiddleware.AppInfo{
		TenantId:      resp.Data.TenantId,
		AppId:         resp.Data.AppId,
		InternalAppId: resp.Data.InternalAppId,
	}
}

// buildHTTPServer 构建 HTTP 服务器
func (app *Application) buildHTTPServer() error {
	// 设置 Gin 模式
	if app.config.Server.Env == "prod" || app.config.Server.Env == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	router := gin.New()
	r := router.Group("")
	// 创建用户和租户信息提供者
	userInfoProvider := &UserInfoProviderAdapter{container: app.container}
	AppInfoProvider := &AppInfoProviderAdapter{container: app.container}

	// 设置统一中间件
	middlewareConfig := &httpmiddleware.MiddlewareConfig{
		ServiceName:           ServiceName,
		EnableAccessLog:       true,
		EnableRequestID:       true,
		EnableSecurityHeaders: true,
		EnableRecovery:        true,
		EnableMetrics:         true,
		EnableRequestSize:     true,
		MaxRequestSize:        10 * 1024 * 1024, // 10MB
		EnableUserInfo:        true,
		UserInfoProvider:      userInfoProvider,
		AppInfoProvider:       AppInfoProvider,
		EnableHealthCheck:     true,
		Logger:                app.logger,
	}
	httpmiddleware.SetupCommonMiddleware(r, middlewareConfig)

	// 设置路由
	routes.SetupRoutes(
		r,
		app.container.Interfaces.RecordHandler,
		app.container.Interfaces.RecordTypeHandler,
		app.container.Interfaces.CategoryHandler,
		app.container.Interfaces.LikeHandler,
		app.container.Interfaces.TagHandler,
		app.container.Interfaces.WishlistHandler,
		app.container.Interfaces.SearchHandler,
		app.container.Interfaces.CommentHandler,
	)

	// 创建 HTTP 服务器
	app.httpServer = &http.Server{
		Addr:         fmt.Sprintf(":%d", app.config.Server.Port),
		Handler:      router,
		ReadTimeout:  app.config.GetServerReadTimeout(),
		WriteTimeout: app.config.GetServerWriteTimeout(),
		IdleTimeout:  app.config.GetServerIdleTimeout(),
	}

	return nil
}

// Start 启动应用
func (app *Application) Start() error {
	// 启动 HTTP 服务器
	go func() {
		app.logger.Info(app.ctx, "Starting HTTP server", logiface.String("address", app.httpServer.Addr))
		if err := app.httpServer.ListenAndServe(); err != nil && !stderrors.Is(err, http.ErrServerClosed) {
			app.logger.Error(app.ctx, "HTTP server failed to start", logiface.Error(err))
			app.cancel() // 触发优雅关闭
		}
	}()

	app.logger.Info(app.ctx, "Application started successfully")
	return nil
}

// WaitForShutdown 等待关闭信号
func (app *Application) WaitForShutdown() {
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	select {
	case <-quit:
		app.logger.Info(app.ctx, "Received shutdown signal")
	case <-app.ctx.Done():
		app.logger.Info(app.ctx, "Application context cancelled")
	}
}

// Shutdown 优雅关闭应用
func (app *Application) Shutdown() error {
	app.logger.Info(app.ctx, "Shutting down application...")

	// 设置关闭超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 1. 关闭 HTTP 服务器
	if app.httpServer != nil {
		if err := app.httpServer.Shutdown(ctx); err != nil {
			app.logger.Error(ctx, "HTTP server shutdown failed", logiface.Error(err))
		} else {
			app.logger.Info(ctx, "HTTP server shutdown completed")
		}
	}

	// 2. 关闭依赖注入容器
	if app.container != nil {
		if err := app.container.Close(); err != nil {
			app.logger.Error(ctx, "Dependency container close failed", logiface.Error(err))
		} else {
			app.logger.Info(ctx, "Dependency container closed")
		}
	}

	// 3. 关闭 gRPC 客户端管理器
	if manager := grpcregistry.GetGlobalManager(); manager != nil {
		manager.Close()
		app.logger.Info(ctx, "gRPC client manager closed")
	}

	// 4. 关闭 OpenTelemetry
	if app.otelShutdown != nil {
		if err := app.otelShutdown(ctx); err != nil {
			app.logger.Error(ctx, "OpenTelemetry shutdown failed", logiface.Error(err))
		} else {
			app.logger.Info(ctx, "OpenTelemetry shutdown completed")
		}
	}

	app.logger.Info(app.ctx, "Application shutdown completed")
	return nil
}
