# 通用可视化自动化编排平台设计方案

**版本**: v1.0.0  
**日期**: 2025-08-13  
**适用范围**: email-system 自动化编排平台  

## 目录

1. [目标与范围](#1-目标与范围)
2. [非功能需求](#2-非功能需求)
3. [架构约束](#3-架构约束)
4. [总体架构设计](#4-总体架构设计)
5. [领域模型与数据契约](#5-领域模型与数据契约)
6. [执行引擎与调度设计](#6-执行引擎与调度设计)
7. [插件体系(SPI)](#7-插件体系spi)
8. [动态表单与人审节点](#8-动态表单与人审节点)
9. [AI智能体编排](#9-ai智能体编排)
10. [人群圈选引擎](#10-人群圈选引擎)
11. [邮件发送与模板](#11-邮件发送与模板)
12. [API与契约](#12-api与契约)
13. [可观测性与治理](#13-可观测性与治理)
14. [版本化与灰度](#14-版本化与灰度)
15. [测试与质量](#15-测试与质量)
16. [前端与交互](#16-前端与交互)
17. [样例与验收](#17-样例与验收)
18. [里程碑与风险](#18-里程碑与风险)

---

## 1. 目标与范围

### 1.1 目标
构建一个插件化、高可观测、可版本化、可灰度的自动化编排平台，支持：
- **复杂业务流程**：多分支、并行、条件、循环、等待
- **营销编排**：邮件营销、用户旅程、A/B测试
- **运营自动化**：审批流程、数据处理、系统集成
- **AI任务流**：智能体编排、工具调用、人机协同
- **人机协同**：人工审核、表单填写、决策支持

### 1.2 范围
- **流程设计器**：可视化拖拽、节点配置、连线校验
- **执行引擎**：状态机、重试补偿、并发控制
- **调度系统**：定时触发、事件驱动、频率控制
- **插件体系**：节点扩展、安全沙箱、版本管理
- **数据/人群引擎**：分群计算、数据刷新、查询优化
- **表单运行时**：动态渲染、校验规则、联动逻辑
- **AI编排**：智能体管理、工具调用、安全护栏
- **渠道发送**：邮件发送、模板渲染、退信处理
- **多租户与权限**：数据隔离、RBAC、审计日志
- **可观测与安全**：链路追踪、指标监控、安全防护
- **运维与治理**：版本管理、灰度发布、故障恢复

---

## 2. 非功能需求

### 2.1 高可用与可扩展
- **高可用**：99.9% SLA，多AZ部署，故障自动切换
- **可扩展**：水平扩展，支持千万级流程实例
- **一致性策略**：
  - 强一致：流程状态、节点执行状态
  - 最终一致：统计数据、分群快照、审计日志

### 2.2 性能指标(SLO)
- **P95时延**：API响应 < 200ms，流程启动 < 500ms
- **成功率**：流程执行成功率 > 99.5%
- **吞吐量**：单节点支持1000 QPS，集群支持10000 QPS

### 2.3 容量预估与扩展路径
- **初期**：10万流程实例/天，100万节点执行/天
- **中期**：100万流程实例/天，1000万节点执行/天  
- **长期**：1000万流程实例/天，1亿节点执行/天
- **扩展路径**：单体 → 微服务 → 分片 → 多集群

### 2.4 安全与合规
- **多租户隔离**：数据库级隔离，网络隔离，资源配额
- **RBAC/ABAC**：基于角色和属性的访问控制
- **PII保护**：敏感数据加密，脱敏处理，访问审计
- **审计与追溯**：操作日志，变更记录，合规报告
- **灰度与回滚**：蓝绿部署，金丝雀发布，快速回滚

### 2.5 可观测性
- **OpenTelemetry**：端到端分布式追踪
- **指标监控**：业务指标、系统指标、自定义指标
- **结构化日志**：统一格式，敏感信息脱敏
- **事件时间线**：流程执行轨迹，可回放调试

---

## 3. 架构约束

### 3.1 技术约束(必须遵守)
- **后端技术**：Go 1.21+、微服务架构、DDD + Clean Architecture
- **依赖注入**：显式构造函数注入，禁止运行时nil检查
- **数据访问**：GORM禁用自动预加载，手控关联查询，避免N+1

### 3.2 传输与接口约束
- **HTTP方法**：仅使用GET/POST，无路径参数
- **统一响应体**：
```go
type Response struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
    Meta    *Meta       `json:"meta,omitempty"`
}
```
- **错误处理**：不向用户暴露系统错误细节，以code判断业务成功与否

### 3.3 邮件发送约束
- **不使用消息队列**：以数据库表作为请求与执行隔离的载体
- **表驱动模式**：请求表 → 任务表 → 状态表
- **幂等与补偿**：支持重复请求，失败自动补偿

### 3.4 可观测性约束
- **OpenTelemetry**：端到端使用，统一中间件
- **日志规范**：携带Trace/Request ID，结构化格式
- **API必要性**：新增接口需显式"必要性分析"

---

## 4. 总体架构设计

### 4.1 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        A[流程设计器] --> B[运行监控台]
        B --> C[分群设计器]
        C --> D[表单设计器]
    end

    subgraph "网关层"
        E[API网关] --> F[认证授权]
        F --> G[限流熔断]
        G --> H[链路追踪]
    end

    subgraph "应用服务层"
        I[工作流服务] --> J[执行引擎服务]
        J --> K[调度服务]
        K --> L[分群服务]
        L --> M[表单服务]
        M --> N[AI服务]
        N --> O[邮件服务]
    end

    subgraph "领域服务层"
        P[流程领域] --> Q[节点领域]
        Q --> R[执行领域]
        R --> S[分群领域]
        S --> T[表单领域]
        T --> U[AI领域]
    end

    subgraph "基础设施层"
        V[MySQL集群] --> W[Redis集群]
        W --> X[对象存储]
        X --> Y[外部服务]
    end

    A --> E
    E --> I
    I --> P
    P --> V
```

### 4.2 数据流架构

```mermaid
sequenceDiagram
    participant U as 用户
    participant D as 设计器
    participant E as 执行引擎
    participant S as 调度器
    participant N as 节点插件
    participant DB as 数据库

    U->>D: 设计流程
    D->>DB: 保存流程定义
    U->>E: 启动流程
    E->>DB: 创建流程实例
    E->>S: 注册调度任务
    S->>E: 触发节点执行
    E->>N: 调用节点插件
    N->>E: 返回执行结果
    E->>DB: 更新执行状态
    E->>U: 推送执行结果
```

---

## 5. 领域模型与数据契约

### 5.1 核心聚合与关系图

```mermaid
erDiagram
    WORKFLOW ||--o{ WORKFLOW_VERSION : has
    WORKFLOW_VERSION ||--o{ NODE_INSTANCE : contains
    WORKFLOW_VERSION ||--o{ EDGE : connects
    WORKFLOW ||--o{ WORKFLOW_RUN : executes
    WORKFLOW_RUN ||--o{ WORKFLOW_RUN_STEP : contains
    NODE_REGISTRY ||--o{ NODE_INSTANCE : implements
    SEGMENT ||--o{ SEGMENT_SNAPSHOT : snapshots
    FORM_SCHEMA ||--o{ FORM_SUBMISSION : submits
    AI_CONVERSATION ||--o{ AI_TOOL_CALL : calls
    EMAIL_TEMPLATE ||--o{ EMAIL_MESSAGE : renders
    TENANT ||--o{ WORKFLOW : owns
    USER ||--o{ WORKFLOW_RUN : triggers
```

### 5.2 核心实体定义

#### 5.2.1 工作流聚合
- **Workflow（工作流）**：流程定义的根聚合
  - 职责：管理流程元信息、版本控制、发布状态
  - 状态机：draft → published → deprecated
  - 不变量：同一租户下名称唯一、至少有一个版本

- **WorkflowVersion（工作流版本）**：具体的流程实现
  - 职责：存储节点配置、连线关系、执行策略
  - 状态机：editing → validating → active → archived
  - 不变量：版本号递增、激活版本唯一

- **WorkflowRun（工作流运行实例）**：流程执行的根聚合
  - 职责：管理执行状态、上下文数据、错误处理
  - 状态机：pending → running → completed/failed/cancelled
  - 不变量：同一版本可并发执行、状态转换单向

#### 5.2.2 节点聚合
- **NodeRegistry（节点注册表）**：插件节点的声明
  - 职责：节点类型定义、配置Schema、版本管理
  - 不变量：节点类型全局唯一、配置Schema向后兼容

- **NodeInstance（节点实例）**：流程中的具体节点
  - 职责：存储节点配置、输入输出定义、执行策略
  - 不变量：配置符合Schema、输入输出类型匹配

#### 5.2.3 分群聚合
- **Segment（分群）**：用户群体定义
  - 职责：分群规则、刷新策略、快照管理
  - 状态机：building → ready → refreshing → error
  - 不变量：规则DSL语法正确、快照数据一致

### 5.3 数据库表结构设计

#### 5.3.1 工作流核心表

```sql
-- 工作流定义表
CREATE TABLE workflows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    name VARCHAR(128) NOT NULL COMMENT '流程名称',
    description TEXT COMMENT '流程描述',
    category VARCHAR(64) COMMENT '分类',
    tags JSON COMMENT '标签',
    status VARCHAR(32) NOT NULL DEFAULT 'draft' COMMENT '状态：draft/published/deprecated',
    current_version_id BIGINT COMMENT '当前版本ID',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_tenant_name (tenant_id, name),
    KEY idx_tenant_status (tenant_id, status),
    KEY idx_created_by (created_by),
    KEY idx_category (tenant_id, category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流定义表';

-- 工作流版本表
CREATE TABLE workflow_versions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workflow_id BIGINT NOT NULL COMMENT '工作流ID',
    version_number INT NOT NULL COMMENT '版本号',
    definition JSON NOT NULL COMMENT '流程定义(节点+连线)',
    config JSON COMMENT '执行配置',
    status VARCHAR(32) NOT NULL DEFAULT 'editing' COMMENT '状态：editing/validating/active/archived',
    validation_errors JSON COMMENT '校验错误',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    activated_at TIMESTAMP NULL COMMENT '激活时间',

    UNIQUE KEY uk_workflow_version (workflow_id, version_number),
    KEY idx_status (status),
    KEY idx_created_at (created_at),
    FOREIGN KEY (workflow_id) REFERENCES workflows(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流版本表';

-- 工作流运行实例表
CREATE TABLE workflow_runs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    workflow_id BIGINT NOT NULL COMMENT '工作流ID',
    version_id BIGINT NOT NULL COMMENT '版本ID',
    run_name VARCHAR(128) COMMENT '运行名称',
    trigger_type VARCHAR(32) NOT NULL COMMENT '触发类型：manual/scheduled/event',
    trigger_data JSON COMMENT '触发数据',
    input_data JSON COMMENT '输入数据',
    context_data JSON COMMENT '上下文数据',
    status VARCHAR(32) NOT NULL DEFAULT 'pending' COMMENT '状态：pending/running/completed/failed/cancelled',
    current_step_id BIGINT COMMENT '当前步骤ID',
    progress_percent INT DEFAULT 0 COMMENT '进度百分比',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    created_by BIGINT COMMENT '创建人',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    KEY idx_tenant_status (tenant_id, status),
    KEY idx_workflow_status (workflow_id, status),
    KEY idx_created_at (created_at),
    KEY idx_trigger_type (trigger_type),
    FOREIGN KEY (workflow_id) REFERENCES workflows(id),
    FOREIGN KEY (version_id) REFERENCES workflow_versions(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流运行实例表';

-- 工作流运行步骤表
CREATE TABLE workflow_run_steps (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    run_id BIGINT NOT NULL COMMENT '运行实例ID',
    node_id VARCHAR(64) NOT NULL COMMENT '节点ID',
    node_type VARCHAR(64) NOT NULL COMMENT '节点类型',
    step_name VARCHAR(128) COMMENT '步骤名称',
    step_sequence INT NOT NULL COMMENT '步骤序号',
    input_data JSON COMMENT '输入数据',
    output_data JSON COMMENT '输出数据',
    config_data JSON COMMENT '配置数据',
    status VARCHAR(32) NOT NULL DEFAULT 'pending' COMMENT '状态：pending/running/completed/failed/skipped',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    duration_ms INT COMMENT '执行时长(毫秒)',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    UNIQUE KEY uk_run_sequence (run_id, step_sequence),
    KEY idx_run_status (run_id, status),
    KEY idx_node_type (node_type),
    KEY idx_started_at (started_at),
    FOREIGN KEY (run_id) REFERENCES workflow_runs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流运行步骤表';
```

#### 5.3.2 节点插件表

```sql
-- 节点注册表
CREATE TABLE node_registry (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    node_type VARCHAR(64) NOT NULL COMMENT '节点类型',
    name VARCHAR(128) NOT NULL COMMENT '节点名称',
    description TEXT COMMENT '节点描述',
    category VARCHAR(64) COMMENT '分类',
    version VARCHAR(32) NOT NULL COMMENT '版本号',
    config_schema JSON NOT NULL COMMENT '配置Schema',
    input_schema JSON COMMENT '输入Schema',
    output_schema JSON COMMENT '输出Schema',
    icon_url VARCHAR(512) COMMENT '图标URL',
    is_system TINYINT(1) DEFAULT 0 COMMENT '是否系统节点',
    is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_type_version (node_type, version),
    KEY idx_category (category),
    KEY idx_enabled (is_enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='节点注册表';

-- 节点实例表
CREATE TABLE node_instances (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    version_id BIGINT NOT NULL COMMENT '工作流版本ID',
    node_id VARCHAR(64) NOT NULL COMMENT '节点ID(流程内唯一)',
    node_type VARCHAR(64) NOT NULL COMMENT '节点类型',
    name VARCHAR(128) NOT NULL COMMENT '节点名称',
    description TEXT COMMENT '节点描述',
    config JSON NOT NULL COMMENT '节点配置',
    position_x INT COMMENT 'X坐标',
    position_y INT COMMENT 'Y坐标',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    UNIQUE KEY uk_version_node (version_id, node_id),
    KEY idx_node_type (node_type),
    FOREIGN KEY (version_id) REFERENCES workflow_versions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='节点实例表';

-- 连线表
CREATE TABLE edges (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    version_id BIGINT NOT NULL COMMENT '工作流版本ID',
    source_node_id VARCHAR(64) NOT NULL COMMENT '源节点ID',
    target_node_id VARCHAR(64) NOT NULL COMMENT '目标节点ID',
    condition_expr TEXT COMMENT '条件表达式',
    label VARCHAR(128) COMMENT '连线标签',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    KEY idx_version_source (version_id, source_node_id),
    KEY idx_version_target (version_id, target_node_id),
    FOREIGN KEY (version_id) REFERENCES workflow_versions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='连线表';
```

#### 5.3.3 分群相关表

```sql
-- 分群定义表
CREATE TABLE segments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    name VARCHAR(128) NOT NULL COMMENT '分群名称',
    description TEXT COMMENT '分群描述',
    rule_dsl JSON NOT NULL COMMENT '分群规则DSL',
    is_dynamic TINYINT(1) DEFAULT 1 COMMENT '是否动态分群',
    refresh_policy VARCHAR(32) DEFAULT 'manual' COMMENT '刷新策略：manual/hourly/daily',
    last_built_at TIMESTAMP NULL COMMENT '最后构建时间',
    size_snapshot BIGINT DEFAULT 0 COMMENT '快照大小',
    status VARCHAR(32) NOT NULL DEFAULT 'draft' COMMENT '状态：draft/building/ready/error',
    error_message TEXT COMMENT '错误信息',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_tenant_name (tenant_id, name),
    KEY idx_tenant_status (tenant_id, status),
    KEY idx_refresh_policy (refresh_policy),
    KEY idx_last_built (last_built_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分群定义表';

-- 分群快照表
CREATE TABLE segment_snapshots (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    segment_id BIGINT NOT NULL COMMENT '分群ID',
    snapshot_time TIMESTAMP NOT NULL COMMENT '快照时间',
    total_count BIGINT NOT NULL DEFAULT 0 COMMENT '总数量',
    data_hash VARCHAR(64) COMMENT '数据哈希',
    storage_path VARCHAR(512) COMMENT '存储路径',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    KEY idx_segment_time (segment_id, snapshot_time),
    KEY idx_snapshot_time (snapshot_time),
    FOREIGN KEY (segment_id) REFERENCES segments(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分群快照表';

-- 分群计算任务表
CREATE TABLE segment_jobs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    segment_id BIGINT NOT NULL COMMENT '分群ID',
    job_type VARCHAR(32) NOT NULL COMMENT '任务类型：full/incremental',
    status VARCHAR(32) NOT NULL DEFAULT 'pending' COMMENT '状态：pending/running/completed/failed',
    progress_percent INT DEFAULT 0 COMMENT '进度百分比',
    processed_count BIGINT DEFAULT 0 COMMENT '已处理数量',
    total_count BIGINT DEFAULT 0 COMMENT '总数量',
    result_count BIGINT DEFAULT 0 COMMENT '结果数量',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    KEY idx_segment_status (segment_id, status),
    KEY idx_status_created (status, created_at),
    FOREIGN KEY (segment_id) REFERENCES segments(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分群计算任务表';
```

#### 5.3.4 表单相关表

```sql
-- 表单Schema表
CREATE TABLE form_schemas (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    name VARCHAR(128) NOT NULL COMMENT '表单名称',
    description TEXT COMMENT '表单描述',
    schema_json JSON NOT NULL COMMENT '表单Schema',
    ui_schema JSON COMMENT 'UI Schema',
    validation_rules JSON COMMENT '校验规则',
    version VARCHAR(32) NOT NULL DEFAULT '1.0.0' COMMENT '版本号',
    status VARCHAR(32) NOT NULL DEFAULT 'draft' COMMENT '状态：draft/published/archived',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_tenant_name_version (tenant_id, name, version),
    KEY idx_tenant_status (tenant_id, status),
    KEY idx_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表单Schema表';

-- 表单提交表
CREATE TABLE form_submissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    schema_id BIGINT NOT NULL COMMENT '表单Schema ID',
    run_id BIGINT COMMENT '关联的工作流运行ID',
    step_id BIGINT COMMENT '关联的步骤ID',
    form_data JSON NOT NULL COMMENT '表单数据',
    submitter_id BIGINT COMMENT '提交人ID',
    submitter_ip VARCHAR(45) COMMENT '提交人IP',
    status VARCHAR(32) NOT NULL DEFAULT 'submitted' COMMENT '状态：submitted/approved/rejected',
    reviewed_by BIGINT COMMENT '审核人ID',
    reviewed_at TIMESTAMP NULL COMMENT '审核时间',
    review_comment TEXT COMMENT '审核意见',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    KEY idx_tenant_status (tenant_id, status),
    KEY idx_schema_created (schema_id, created_at),
    KEY idx_run_step (run_id, step_id),
    KEY idx_submitter (submitter_id),
    FOREIGN KEY (schema_id) REFERENCES form_schemas(id),
    FOREIGN KEY (run_id) REFERENCES workflow_runs(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表单提交表';

-- 表单草稿表
CREATE TABLE form_drafts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    schema_id BIGINT NOT NULL COMMENT '表单Schema ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    draft_data JSON NOT NULL COMMENT '草稿数据',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_schema_user (schema_id, user_id),
    KEY idx_tenant_user (tenant_id, user_id),
    FOREIGN KEY (schema_id) REFERENCES form_schemas(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表单草稿表';
```

#### 5.3.5 AI相关表

```sql
-- AI对话表
CREATE TABLE ai_conversations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    run_id BIGINT COMMENT '关联的工作流运行ID',
    step_id BIGINT COMMENT '关联的步骤ID',
    agent_type VARCHAR(64) NOT NULL COMMENT 'Agent类型',
    conversation_id VARCHAR(128) NOT NULL COMMENT '对话ID',
    context_data JSON COMMENT '上下文数据',
    status VARCHAR(32) NOT NULL DEFAULT 'active' COMMENT '状态：active/completed/failed',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL COMMENT '完成时间',

    UNIQUE KEY uk_conversation_id (conversation_id),
    KEY idx_tenant_status (tenant_id, status),
    KEY idx_run_step (run_id, step_id),
    FOREIGN KEY (run_id) REFERENCES workflow_runs(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话表';

-- AI工具调用表
CREATE TABLE ai_tool_calls (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    conversation_id BIGINT NOT NULL COMMENT '对话ID',
    tool_name VARCHAR(128) NOT NULL COMMENT '工具名称',
    tool_args JSON NOT NULL COMMENT '工具参数',
    tool_result JSON COMMENT '工具结果',
    status VARCHAR(32) NOT NULL DEFAULT 'pending' COMMENT '状态：pending/running/completed/failed',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    KEY idx_conversation_status (conversation_id, status),
    KEY idx_tool_name (tool_name),
    KEY idx_created_at (created_at),
    FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI工具调用表';

-- AI护栏事件表
CREATE TABLE ai_guardrail_events (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    conversation_id BIGINT COMMENT '对话ID',
    event_type VARCHAR(64) NOT NULL COMMENT '事件类型',
    rule_name VARCHAR(128) NOT NULL COMMENT '规则名称',
    input_content TEXT COMMENT '输入内容',
    output_content TEXT COMMENT '输出内容',
    risk_level VARCHAR(32) NOT NULL COMMENT '风险等级：low/medium/high',
    action_taken VARCHAR(64) NOT NULL COMMENT '采取的行动',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    KEY idx_tenant_risk (tenant_id, risk_level),
    KEY idx_conversation (conversation_id),
    KEY idx_event_type (event_type),
    KEY idx_created_at (created_at),
    FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI护栏事件表';
```

#### 5.3.6 邮件相关表

```sql
-- 邮件模板表(继承email模块设计)
CREATE TABLE email_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    name VARCHAR(128) NOT NULL COMMENT '模板名称',
    subject VARCHAR(512) NOT NULL COMMENT '邮件主题',
    html_content MEDIUMTEXT COMMENT 'HTML内容',
    text_content TEXT COMMENT '纯文本内容',
    variables JSON COMMENT '变量定义',
    version VARCHAR(32) NOT NULL DEFAULT '1.0.0' COMMENT '版本号',
    status VARCHAR(32) NOT NULL DEFAULT 'draft' COMMENT '状态：draft/published/archived',
    account_id BIGINT COMMENT '关联的发件账户ID',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_tenant_name_version (tenant_id, name, version),
    KEY idx_tenant_status (tenant_id, status),
    KEY idx_account_id (account_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件模板表';

-- 邮件发送请求表
CREATE TABLE email_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    run_id BIGINT COMMENT '关联的工作流运行ID',
    step_id BIGINT COMMENT '关联的步骤ID',
    template_id BIGINT NOT NULL COMMENT '模板ID',
    to_email VARCHAR(255) NOT NULL COMMENT '收件人邮箱',
    to_name VARCHAR(128) COMMENT '收件人姓名',
    from_email VARCHAR(255) NOT NULL COMMENT '发件人邮箱',
    from_name VARCHAR(128) COMMENT '发件人姓名',
    subject VARCHAR(512) NOT NULL COMMENT '邮件主题',
    variables JSON COMMENT '模板变量',
    headers JSON COMMENT '自定义头部',
    priority INT NOT NULL DEFAULT 100 COMMENT '优先级',
    scheduled_at TIMESTAMP NULL COMMENT '计划发送时间',
    status VARCHAR(32) NOT NULL DEFAULT 'queued' COMMENT '状态：queued/sending/sent/failed/skipped',
    sent_at TIMESTAMP NULL COMMENT '发送时间',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    idempotency_key VARCHAR(128) COMMENT '幂等键',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    UNIQUE KEY uk_idempotency (idempotency_key),
    KEY idx_tenant_status (tenant_id, status),
    KEY idx_run_step (run_id, step_id),
    KEY idx_scheduled_at (scheduled_at),
    KEY idx_to_email (to_email),
    FOREIGN KEY (template_id) REFERENCES email_templates(id),
    FOREIGN KEY (run_id) REFERENCES workflow_runs(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件发送请求表';

-- 邮件发送任务表
CREATE TABLE email_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    message_id BIGINT NOT NULL COMMENT '邮件消息ID',
    task_type VARCHAR(32) NOT NULL DEFAULT 'send' COMMENT '任务类型：send/retry',
    status VARCHAR(32) NOT NULL DEFAULT 'pending' COMMENT '状态：pending/processing/completed/failed',
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    error_message TEXT COMMENT '错误信息',
    provider_response JSON COMMENT '服务商响应',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    KEY idx_message_status (message_id, status),
    KEY idx_status_created (status, created_at),
    FOREIGN KEY (message_id) REFERENCES email_messages(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件发送任务表';

-- 邮件退信表
CREATE TABLE email_bounces (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    message_id BIGINT COMMENT '邮件消息ID',
    email VARCHAR(255) NOT NULL COMMENT '邮箱地址',
    bounce_type VARCHAR(32) NOT NULL COMMENT '退信类型：hard/soft',
    bounce_reason VARCHAR(128) COMMENT '退信原因',
    bounce_code VARCHAR(16) COMMENT '退信代码',
    raw_message TEXT COMMENT '原始消息',
    occurred_at TIMESTAMP NOT NULL COMMENT '发生时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    KEY idx_tenant_email (tenant_id, email),
    KEY idx_message_id (message_id),
    KEY idx_bounce_type (bounce_type),
    KEY idx_occurred_at (occurred_at),
    FOREIGN KEY (message_id) REFERENCES email_messages(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='邮件退信表';
```

#### 5.3.7 系统管理表

```sql
-- 租户表
CREATE TABLE tenants (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(128) NOT NULL COMMENT '租户名称',
    code VARCHAR(64) NOT NULL COMMENT '租户代码',
    status VARCHAR(32) NOT NULL DEFAULT 'active' COMMENT '状态：active/suspended/deleted',
    plan VARCHAR(32) NOT NULL DEFAULT 'basic' COMMENT '套餐：basic/pro/enterprise',
    settings JSON COMMENT '租户设置',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_code (code),
    KEY idx_status (status),
    KEY idx_plan (plan)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户表';

-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    username VARCHAR(64) NOT NULL COMMENT '用户名',
    email VARCHAR(255) NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) COMMENT '密码哈希',
    full_name VARCHAR(128) COMMENT '全名',
    avatar_url VARCHAR(512) COMMENT '头像URL',
    status VARCHAR(32) NOT NULL DEFAULT 'active' COMMENT '状态：active/inactive/locked',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_tenant_username (tenant_id, username),
    UNIQUE KEY uk_tenant_email (tenant_id, email),
    KEY idx_status (status),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 角色表
CREATE TABLE roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    name VARCHAR(64) NOT NULL COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    permissions JSON NOT NULL COMMENT '权限列表',
    is_system TINYINT(1) DEFAULT 0 COMMENT '是否系统角色',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_tenant_name (tenant_id, name),
    KEY idx_is_system (is_system),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 用户角色关联表
CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    UNIQUE KEY uk_user_role (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 审计日志表
CREATE TABLE audit_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    user_id BIGINT COMMENT '操作用户ID',
    action VARCHAR(64) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(64) NOT NULL COMMENT '资源类型',
    resource_id VARCHAR(128) COMMENT '资源ID',
    old_values JSON COMMENT '变更前值',
    new_values JSON COMMENT '变更后值',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT 'User Agent',
    request_id VARCHAR(128) COMMENT '请求ID',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    KEY idx_tenant_created (tenant_id, created_at),
    KEY idx_user_created (user_id, created_at),
    KEY idx_resource (resource_type, resource_id),
    KEY idx_action (action),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审计日志表';
```

#### 5.3.8 配额与限流表

```sql
-- 配额策略表
CREATE TABLE quota_policies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    resource_type VARCHAR(64) NOT NULL COMMENT '资源类型：workflow_run/email_send/api_call',
    subject_type VARCHAR(32) NOT NULL COMMENT '主体类型：tenant/user/workflow',
    subject_key VARCHAR(128) COMMENT '主体键值',
    metric VARCHAR(32) NOT NULL COMMENT '指标：count/size/duration',
    period_type VARCHAR(16) NOT NULL COMMENT '周期类型：hour/day/month',
    limit_value BIGINT NOT NULL COMMENT '限制值',
    status VARCHAR(32) NOT NULL DEFAULT 'active' COMMENT '状态：active/inactive',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_quota_policy (tenant_id, resource_type, subject_type, subject_key, metric, period_type),
    KEY idx_tenant_resource (tenant_id, resource_type),
    KEY idx_status (status),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='配额策略表';

-- 用量计数表
CREATE TABLE usage_counters (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    resource_type VARCHAR(64) NOT NULL COMMENT '资源类型',
    subject_type VARCHAR(32) NOT NULL COMMENT '主体类型',
    subject_key VARCHAR(128) COMMENT '主体键值',
    metric VARCHAR(32) NOT NULL COMMENT '指标',
    period_type VARCHAR(16) NOT NULL COMMENT '周期类型',
    period_start TIMESTAMP NOT NULL COMMENT '周期开始时间',
    used_value BIGINT NOT NULL DEFAULT 0 COMMENT '已使用值',
    last_updated TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_usage_counter (tenant_id, resource_type, subject_type, subject_key, metric, period_type, period_start),
    KEY idx_tenant_resource (tenant_id, resource_type),
    KEY idx_period_start (period_start),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用量计数表';

-- 限流策略表
CREATE TABLE rate_limit_policies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    resource_type VARCHAR(64) NOT NULL COMMENT '资源类型',
    rate_per_second INT NOT NULL COMMENT '每秒速率',
    burst_size INT NOT NULL COMMENT '突发大小',
    window_size INT NOT NULL DEFAULT 60 COMMENT '窗口大小(秒)',
    status VARCHAR(32) NOT NULL DEFAULT 'active' COMMENT '状态：active/inactive',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_rate_limit (tenant_id, resource_type),
    KEY idx_status (status),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='限流策略表';

-- 调度任务表
CREATE TABLE schedules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    workflow_id BIGINT NOT NULL COMMENT '工作流ID',
    name VARCHAR(128) NOT NULL COMMENT '调度名称',
    cron_expression VARCHAR(128) NOT NULL COMMENT 'Cron表达式',
    timezone VARCHAR(64) DEFAULT 'UTC' COMMENT '时区',
    input_data JSON COMMENT '输入数据',
    status VARCHAR(32) NOT NULL DEFAULT 'active' COMMENT '状态：active/paused/stopped',
    next_run_time TIMESTAMP NULL COMMENT '下次运行时间',
    last_run_time TIMESTAMP NULL COMMENT '上次运行时间',
    last_run_status VARCHAR(32) COMMENT '上次运行状态',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    KEY idx_tenant_status (tenant_id, status),
    KEY idx_workflow_id (workflow_id),
    KEY idx_next_run_time (next_run_time),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (workflow_id) REFERENCES workflows(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调度任务表';
```

---

## 6. 执行引擎与调度设计

### 6.1 流程模型设计

#### 6.1.1 支持的节点类型
- **控制节点**：开始、结束、条件分支、并行网关、循环、等待
- **业务节点**：邮件发送、数据查询、API调用、文件处理
- **人工节点**：表单填写、审批、决策
- **AI节点**：智能体对话、工具调用、内容生成
- **集成节点**：Webhook、数据库操作、第三方服务

#### 6.1.2 数据流规范(DataEnvelope)
```go
type DataEnvelope struct {
    Headers    map[string]string `json:"headers"`    // 元数据头
    Meta       map[string]any    `json:"meta"`       // 元信息
    Payload    any               `json:"payload"`    // 业务数据
    Errors     []ExecutionError  `json:"errors"`     // 错误信息
    TraceID    string           `json:"trace_id"`   // 追踪ID
    Timestamp  time.Time        `json:"timestamp"`  // 时间戳
}

type ExecutionError struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Field   string `json:"field,omitempty"`
    Details any    `json:"details,omitempty"`
}
```

### 6.2 执行语义设计

#### 6.2.1 重试与补偿策略
```go
type RetryPolicy struct {
    MaxRetries      int           `json:"max_retries"`      // 最大重试次数
    InitialDelay    time.Duration `json:"initial_delay"`    // 初始延迟
    MaxDelay        time.Duration `json:"max_delay"`        // 最大延迟
    BackoffFactor   float64       `json:"backoff_factor"`   // 退避因子
    RetryableErrors []string      `json:"retryable_errors"` // 可重试错误码
}

type CompensationPolicy struct {
    Enabled         bool          `json:"enabled"`          // 是否启用补偿
    CompensateOn    []string      `json:"compensate_on"`    // 触发补偿的条件
    CompensateDelay time.Duration `json:"compensate_delay"` // 补偿延迟
    MaxCompensation int           `json:"max_compensation"` // 最大补偿次数
}
```

#### 6.2.2 幂等性策略
```go
type IdempotencyPolicy struct {
    Enabled    bool          `json:"enabled"`     // 是否启用幂等
    KeyFields  []string      `json:"key_fields"`  // 幂等键字段
    TTL        time.Duration `json:"ttl"`         // 幂等键TTL
    Scope      string        `json:"scope"`       // 幂等范围：global/tenant/user
}
```

#### 6.2.3 失败处理策略
```go
type FailurePolicy struct {
    OnFailure   string `json:"on_failure"`   // 失败处理：stop/continue/skip/retry
    SkipErrors  []string `json:"skip_errors"` // 可跳过的错误码
    Fallback    *NodeConfig `json:"fallback"`   // 降级节点配置
    Notification *NotificationConfig `json:"notification"` // 通知配置
}
```

### 6.3 调度系统设计

#### 6.3.1 调度器架构
```go
type Scheduler interface {
    // 注册调度任务
    Schedule(ctx context.Context, task *ScheduleTask) error

    // 取消调度任务
    Cancel(ctx context.Context, taskID string) error

    // 暂停/恢复调度任务
    Pause(ctx context.Context, taskID string) error
    Resume(ctx context.Context, taskID string) error

    // 获取下次执行时间
    NextRunTime(ctx context.Context, taskID string) (time.Time, error)
}

type ScheduleTask struct {
    ID           string                 `json:"id"`
    TenantID     int64                  `json:"tenant_id"`
    WorkflowID   int64                  `json:"workflow_id"`
    CronExpr     string                 `json:"cron_expr"`
    Timezone     string                 `json:"timezone"`
    InputData    map[string]interface{} `json:"input_data"`
    RetryPolicy  *RetryPolicy          `json:"retry_policy"`
    Enabled      bool                   `json:"enabled"`
}
```

#### 6.3.2 频率控制设计
```go
type RateLimiter interface {
    // 检查是否允许执行
    Allow(ctx context.Context, key string, resource string) (bool, error)

    // 获取剩余配额
    Remaining(ctx context.Context, key string, resource string) (int64, error)

    // 重置配额
    Reset(ctx context.Context, key string, resource string) error
}

type RateLimitConfig struct {
    Resource     string        `json:"resource"`      // 资源类型
    Rate         int           `json:"rate"`          // 速率限制
    Burst        int           `json:"burst"`         // 突发限制
    Window       time.Duration `json:"window"`        // 时间窗口
    Algorithm    string        `json:"algorithm"`     // 算法：token_bucket/sliding_window
}
```

#### 6.3.3 并发控制设计
```go
type ConcurrencyController interface {
    // 获取执行许可
    Acquire(ctx context.Context, resource string, permits int) error

    // 释放执行许可
    Release(ctx context.Context, resource string, permits int) error

    // 获取当前并发数
    Current(ctx context.Context, resource string) (int, error)
}

type ConcurrencyConfig struct {
    MaxConcurrent int    `json:"max_concurrent"` // 最大并发数
    QueueSize     int    `json:"queue_size"`     // 队列大小
    Timeout       time.Duration `json:"timeout"` // 超时时间
    Strategy      string `json:"strategy"`       // 策略：block/reject/queue
}
```

### 6.4 状态机设计

#### 6.4.1 工作流状态机
```go
type WorkflowRunStatus string

const (
    StatusPending   WorkflowRunStatus = "pending"   // 等待执行
    StatusRunning   WorkflowRunStatus = "running"   // 执行中
    StatusCompleted WorkflowRunStatus = "completed" // 已完成
    StatusFailed    WorkflowRunStatus = "failed"    // 执行失败
    StatusCancelled WorkflowRunStatus = "cancelled" // 已取消
    StatusPaused    WorkflowRunStatus = "paused"    // 已暂停
)

type WorkflowRunStateMachine struct {
    transitions map[WorkflowRunStatus][]WorkflowRunStatus
}

func NewWorkflowRunStateMachine() *WorkflowRunStateMachine {
    return &WorkflowRunStateMachine{
        transitions: map[WorkflowRunStatus][]WorkflowRunStatus{
            StatusPending:   {StatusRunning, StatusCancelled},
            StatusRunning:   {StatusCompleted, StatusFailed, StatusCancelled, StatusPaused},
            StatusPaused:    {StatusRunning, StatusCancelled},
            StatusCompleted: {},
            StatusFailed:    {},
            StatusCancelled: {},
        },
    }
}
```

#### 6.4.2 节点状态机
```go
type NodeExecutionStatus string

const (
    NodeStatusPending   NodeExecutionStatus = "pending"   // 等待执行
    NodeStatusRunning   NodeExecutionStatus = "running"   // 执行中
    NodeStatusCompleted NodeExecutionStatus = "completed" // 已完成
    NodeStatusFailed    NodeExecutionStatus = "failed"    // 执行失败
    NodeStatusSkipped   NodeExecutionStatus = "skipped"   // 已跳过
    NodeStatusRetrying  NodeExecutionStatus = "retrying"  // 重试中
)
```

### 6.5 长跑任务与心跳机制

#### 6.5.1 心跳设计
```go
type HeartbeatManager interface {
    // 开始心跳
    StartHeartbeat(ctx context.Context, runID int64, interval time.Duration) error

    // 停止心跳
    StopHeartbeat(ctx context.Context, runID int64) error

    // 检查心跳
    CheckHeartbeat(ctx context.Context, runID int64, timeout time.Duration) (bool, error)

    // 处理心跳超时
    HandleTimeout(ctx context.Context, runID int64) error
}

type HeartbeatRecord struct {
    RunID       int64     `json:"run_id"`
    LastBeat    time.Time `json:"last_beat"`
    Status      string    `json:"status"`
    WorkerID    string    `json:"worker_id"`
    ProcessID   int       `json:"process_id"`
}
```

#### 6.5.2 死信处理
```go
type DeadLetterHandler interface {
    // 发送到死信队列
    SendToDeadLetter(ctx context.Context, task *ExecutionTask, reason string) error

    // 从死信队列恢复
    RecoverFromDeadLetter(ctx context.Context, taskID string) error

    // 获取死信列表
    ListDeadLetters(ctx context.Context, filter *DeadLetterFilter) ([]*DeadLetterTask, error)
}

type DeadLetterTask struct {
    ID          string    `json:"id"`
    OriginalTask *ExecutionTask `json:"original_task"`
    Reason      string    `json:"reason"`
    RetryCount  int       `json:"retry_count"`
    CreatedAt   time.Time `json:"created_at"`
    LastRetryAt time.Time `json:"last_retry_at"`
}
```

---

## 7. 插件体系(SPI)

### 7.1 节点插件接口定义

#### 7.1.1 核心插件接口
```go
// NodePlugin 节点插件核心接口
type NodePlugin interface {
    // 插件初始化
    Init(ctx context.Context, config *PluginConfig) error

    // 配置校验
    Validate(ctx context.Context, config map[string]interface{}) error

    // 执行节点逻辑
    Execute(ctx context.Context, input *DataEnvelope) (*DataEnvelope, ExecutionStatus, error)

    // 回滚操作
    Rollback(ctx context.Context, input *DataEnvelope) error

    // 获取插件信息
    GetInfo() *PluginInfo

    // 健康检查
    HealthCheck(ctx context.Context) error

    // 资源清理
    Cleanup(ctx context.Context) error
}

type PluginInfo struct {
    Name        string            `json:"name"`
    Version     string            `json:"version"`
    Description string            `json:"description"`
    Author      string            `json:"author"`
    Category    string            `json:"category"`
    Tags        []string          `json:"tags"`
    ConfigSchema map[string]interface{} `json:"config_schema"`
    InputSchema  map[string]interface{} `json:"input_schema"`
    OutputSchema map[string]interface{} `json:"output_schema"`
    IconURL     string            `json:"icon_url"`
}

type ExecutionStatus string

const (
    StatusSuccess    ExecutionStatus = "success"
    StatusFailure    ExecutionStatus = "failure"
    StatusRetry      ExecutionStatus = "retry"
    StatusSkip       ExecutionStatus = "skip"
    StatusWaiting    ExecutionStatus = "waiting"
)
```

#### 7.1.2 插件生命周期管理
```go
type PluginManager interface {
    // 注册插件
    Register(ctx context.Context, plugin NodePlugin) error

    // 注销插件
    Unregister(ctx context.Context, pluginName string) error

    // 获取插件
    GetPlugin(ctx context.Context, pluginName string) (NodePlugin, error)

    // 列出所有插件
    ListPlugins(ctx context.Context) ([]*PluginInfo, error)

    // 启用/禁用插件
    Enable(ctx context.Context, pluginName string) error
    Disable(ctx context.Context, pluginName string) error

    // 热重载插件
    Reload(ctx context.Context, pluginName string) error
}
```

### 7.2 安全沙箱设计

#### 7.2.1 资源配额控制
```go
type ResourceQuota struct {
    MaxMemory     int64         `json:"max_memory"`     // 最大内存(字节)
    MaxCPU        float64       `json:"max_cpu"`        // 最大CPU使用率
    MaxDuration   time.Duration `json:"max_duration"`   // 最大执行时间
    MaxFileSize   int64         `json:"max_file_size"`  // 最大文件大小
    MaxNetworkIO  int64         `json:"max_network_io"` // 最大网络IO
    MaxDiskIO     int64         `json:"max_disk_io"`    // 最大磁盘IO
}

type SandboxConfig struct {
    Enabled       bool           `json:"enabled"`
    ResourceQuota *ResourceQuota `json:"resource_quota"`
    AllowedHosts  []string       `json:"allowed_hosts"`  // 允许访问的主机
    BlockedPorts  []int          `json:"blocked_ports"`  // 禁止访问的端口
    TempDir       string         `json:"temp_dir"`       // 临时目录
    ReadOnlyPaths []string       `json:"readonly_paths"` // 只读路径
}
```

#### 7.2.2 安全检查器
```go
type SecurityChecker interface {
    // 检查插件安全性
    CheckPlugin(ctx context.Context, plugin NodePlugin) (*SecurityReport, error)

    // 检查配置安全性
    CheckConfig(ctx context.Context, config map[string]interface{}) error

    // 检查输入数据安全性
    CheckInput(ctx context.Context, input *DataEnvelope) error

    // 检查输出数据安全性
    CheckOutput(ctx context.Context, output *DataEnvelope) error
}

type SecurityReport struct {
    PluginName   string             `json:"plugin_name"`
    RiskLevel    string             `json:"risk_level"`    // low/medium/high
    Issues       []SecurityIssue    `json:"issues"`
    Recommendations []string        `json:"recommendations"`
    Approved     bool               `json:"approved"`
}

type SecurityIssue struct {
    Type        string `json:"type"`
    Severity    string `json:"severity"`
    Description string `json:"description"`
    Location    string `json:"location"`
}
```

### 7.3 版本管理与兼容性

#### 7.3.1 版本策略
```go
type VersionManager interface {
    // 注册插件版本
    RegisterVersion(ctx context.Context, plugin NodePlugin, version string) error

    // 获取指定版本插件
    GetPluginVersion(ctx context.Context, pluginName, version string) (NodePlugin, error)

    // 获取最新版本
    GetLatestVersion(ctx context.Context, pluginName string) (string, error)

    // 检查版本兼容性
    CheckCompatibility(ctx context.Context, pluginName, version string) (*CompatibilityReport, error)

    // 迁移插件版本
    MigrateVersion(ctx context.Context, pluginName, fromVersion, toVersion string) error
}

type CompatibilityReport struct {
    Compatible      bool     `json:"compatible"`
    BreakingChanges []string `json:"breaking_changes"`
    Warnings        []string `json:"warnings"`
    MigrationPath   string   `json:"migration_path"`
}
```

### 7.4 内置插件实现

#### 7.4.1 邮件发送插件
```go
type EmailSendPlugin struct {
    emailService EmailService
    logger       Logger
}

func (p *EmailSendPlugin) Execute(ctx context.Context, input *DataEnvelope) (*DataEnvelope, ExecutionStatus, error) {
    // 解析配置
    config, err := p.parseConfig(input.Meta["config"])
    if err != nil {
        return nil, StatusFailure, err
    }

    // 构建邮件请求
    emailReq := &EmailRequest{
        TemplateID: config.TemplateID,
        ToEmail:    input.Payload.(map[string]interface{})["email"].(string),
        Variables:  input.Payload.(map[string]interface{})["variables"],
    }

    // 发送邮件
    result, err := p.emailService.SendEmail(ctx, emailReq)
    if err != nil {
        return nil, StatusRetry, err
    }

    // 构建输出
    output := &DataEnvelope{
        Headers:   input.Headers,
        Meta:      map[string]any{"message_id": result.MessageID},
        Payload:   result,
        TraceID:   input.TraceID,
        Timestamp: time.Now(),
    }

    return output, StatusSuccess, nil
}
```

#### 7.4.2 条件分支插件
```go
type ConditionPlugin struct {
    expressionEngine ExpressionEngine
}

func (p *ConditionPlugin) Execute(ctx context.Context, input *DataEnvelope) (*DataEnvelope, ExecutionStatus, error) {
    config := input.Meta["config"].(map[string]interface{})
    expression := config["expression"].(string)

    // 评估条件表达式
    result, err := p.expressionEngine.Evaluate(expression, input.Payload)
    if err != nil {
        return nil, StatusFailure, err
    }

    // 构建输出
    output := &DataEnvelope{
        Headers:   input.Headers,
        Meta:      map[string]any{"condition_result": result},
        Payload:   input.Payload,
        TraceID:   input.TraceID,
        Timestamp: time.Now(),
    }

    return output, StatusSuccess, nil
}
```

---

## 8. 动态表单与人审节点

### 8.1 表单Schema标准

#### 8.1.1 JSON Schema定义
```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "title": {"type": "string"},
    "description": {"type": "string"},
    "version": {"type": "string"},
    "fields": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "id": {"type": "string"},
          "type": {"enum": ["text", "email", "number", "select", "checkbox", "radio", "textarea", "date", "file"]},
          "label": {"type": "string"},
          "placeholder": {"type": "string"},
          "required": {"type": "boolean"},
          "validation": {
            "type": "object",
            "properties": {
              "min": {"type": "number"},
              "max": {"type": "number"},
              "pattern": {"type": "string"},
              "custom": {"type": "string"}
            }
          },
          "options": {
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "label": {"type": "string"},
                "value": {"type": "string"}
              }
            }
          },
          "conditional": {
            "type": "object",
            "properties": {
              "field": {"type": "string"},
              "operator": {"enum": ["eq", "ne", "gt", "lt", "in", "nin"]},
              "value": {}
            }
          }
        }
      }
    },
    "layout": {
      "type": "object",
      "properties": {
        "columns": {"type": "number"},
        "sections": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "title": {"type": "string"},
              "fields": {"type": "array", "items": {"type": "string"}}
            }
          }
        }
      }
    }
  }
}
```

#### 8.1.2 表单运行时引擎
```go
type FormEngine interface {
    // 渲染表单
    RenderForm(ctx context.Context, schemaID int64, context map[string]interface{}) (*RenderedForm, error)

    // 校验表单数据
    ValidateForm(ctx context.Context, schemaID int64, data map[string]interface{}) (*ValidationResult, error)

    // 提交表单
    SubmitForm(ctx context.Context, req *FormSubmissionRequest) (*FormSubmissionResponse, error)

    // 获取表单状态
    GetFormStatus(ctx context.Context, submissionID int64) (*FormStatus, error)
}

type RenderedForm struct {
    Schema    *FormSchema            `json:"schema"`
    Fields    []*RenderedField       `json:"fields"`
    Layout    *FormLayout            `json:"layout"`
    Context   map[string]interface{} `json:"context"`
    Readonly  bool                   `json:"readonly"`
}

type RenderedField struct {
    ID          string                 `json:"id"`
    Type        string                 `json:"type"`
    Label       string                 `json:"label"`
    Value       interface{}            `json:"value"`
    Options     []*FieldOption         `json:"options"`
    Validation  *FieldValidation       `json:"validation"`
    Visible     bool                   `json:"visible"`
    Disabled    bool                   `json:"disabled"`
    Properties  map[string]interface{} `json:"properties"`
}

type ValidationResult struct {
    Valid   bool                    `json:"valid"`
    Errors  map[string][]string     `json:"errors"`
    Warnings map[string][]string    `json:"warnings"`
}
```

### 8.2 人工任务流程设计

#### 8.2.1 人工任务节点
```go
type HumanTaskPlugin struct {
    formEngine    FormEngine
    taskService   HumanTaskService
    notification  NotificationService
}

func (p *HumanTaskPlugin) Execute(ctx context.Context, input *DataEnvelope) (*DataEnvelope, ExecutionStatus, error) {
    config := input.Meta["config"].(map[string]interface{})

    // 创建人工任务
    task := &HumanTask{
        RunID:       input.Meta["run_id"].(int64),
        StepID:      input.Meta["step_id"].(int64),
        FormSchemaID: config["form_schema_id"].(int64),
        AssigneeID:  config["assignee_id"].(int64),
        Title:       config["title"].(string),
        Description: config["description"].(string),
        Priority:    config["priority"].(string),
        DueDate:     parseTime(config["due_date"]),
        InputData:   input.Payload,
    }

    // 保存任务
    taskID, err := p.taskService.CreateTask(ctx, task)
    if err != nil {
        return nil, StatusFailure, err
    }

    // 发送通知
    err = p.notification.NotifyTaskAssigned(ctx, taskID)
    if err != nil {
        // 通知失败不影响任务创建
        log.Warn("Failed to send task notification", "task_id", taskID, "error", err)
    }

    // 返回等待状态
    output := &DataEnvelope{
        Headers:   input.Headers,
        Meta:      map[string]any{"task_id": taskID, "status": "waiting"},
        Payload:   input.Payload,
        TraceID:   input.TraceID,
        Timestamp: time.Now(),
    }

    return output, StatusWaiting, nil
}
```

#### 8.2.2 人工任务服务
```go
type HumanTaskService interface {
    // 创建任务
    CreateTask(ctx context.Context, task *HumanTask) (int64, error)

    // 分配任务
    AssignTask(ctx context.Context, taskID int64, assigneeID int64) error

    // 完成任务
    CompleteTask(ctx context.Context, taskID int64, result *TaskResult) error

    // 转派任务
    DelegateTask(ctx context.Context, taskID int64, fromUserID, toUserID int64, comment string) error

    // 撤回任务
    RecallTask(ctx context.Context, taskID int64, reason string) error

    // 获取任务列表
    ListTasks(ctx context.Context, filter *TaskFilter) ([]*HumanTask, error)

    // 批量处理任务
    BatchProcess(ctx context.Context, taskIDs []int64, action string, params map[string]interface{}) error
}

type HumanTask struct {
    ID           int64                  `json:"id"`
    RunID        int64                  `json:"run_id"`
    StepID       int64                  `json:"step_id"`
    FormSchemaID int64                  `json:"form_schema_id"`
    Title        string                 `json:"title"`
    Description  string                 `json:"description"`
    AssigneeID   int64                  `json:"assignee_id"`
    Priority     string                 `json:"priority"`
    Status       string                 `json:"status"`
    InputData    map[string]interface{} `json:"input_data"`
    OutputData   map[string]interface{} `json:"output_data"`
    DueDate      time.Time              `json:"due_date"`
    CreatedAt    time.Time              `json:"created_at"`
    CompletedAt  *time.Time             `json:"completed_at"`
}

type TaskResult struct {
    Action     string                 `json:"action"`     // approve/reject/delegate
    FormData   map[string]interface{} `json:"form_data"`
    Comment    string                 `json:"comment"`
    Attachments []string              `json:"attachments"`
}
```

### 8.3 SLA与催办机制

#### 8.3.1 SLA管理
```go
type SLAManager interface {
    // 设置SLA规则
    SetSLARule(ctx context.Context, rule *SLARule) error

    // 检查SLA违规
    CheckSLAViolation(ctx context.Context, taskID int64) (*SLAStatus, error)

    // 处理SLA违规
    HandleSLAViolation(ctx context.Context, taskID int64) error

    // 获取SLA报告
    GetSLAReport(ctx context.Context, filter *SLAReportFilter) (*SLAReport, error)
}

type SLARule struct {
    ID          int64         `json:"id"`
    TaskType    string        `json:"task_type"`
    Priority    string        `json:"priority"`
    ResponseTime time.Duration `json:"response_time"` // 响应时间
    ResolutionTime time.Duration `json:"resolution_time"` // 解决时间
    EscalationLevels []EscalationLevel `json:"escalation_levels"`
}

type EscalationLevel struct {
    Level       int           `json:"level"`
    Delay       time.Duration `json:"delay"`
    AssigneeIDs []int64       `json:"assignee_ids"`
    Actions     []string      `json:"actions"` // notify/reassign/escalate
}
```

#### 8.3.2 催办服务
```go
type ReminderService interface {
    // 创建催办
    CreateReminder(ctx context.Context, reminder *Reminder) error

    // 发送催办通知
    SendReminder(ctx context.Context, taskID int64) error

    // 自动催办检查
    CheckOverdueTasks(ctx context.Context) error

    // 升级处理
    EscalateTask(ctx context.Context, taskID int64, level int) error
}

type Reminder struct {
    TaskID      int64     `json:"task_id"`
    Type        string    `json:"type"`        // due_soon/overdue/escalation
    Recipients  []int64   `json:"recipients"`
    Message     string    `json:"message"`
    ScheduledAt time.Time `json:"scheduled_at"`
    SentAt      *time.Time `json:"sent_at"`
}
```

---

## 9. AI智能体编排

### 9.1 Agent结构设计

#### 9.1.1 AI Agent接口
```go
type AIAgent interface {
    // 初始化Agent
    Init(ctx context.Context, config *AgentConfig) error

    // 开始对话
    StartConversation(ctx context.Context, input *ConversationInput) (*Conversation, error)

    // 发送消息
    SendMessage(ctx context.Context, conversationID string, message *Message) (*Response, error)

    // 调用工具
    CallTool(ctx context.Context, conversationID string, toolCall *ToolCall) (*ToolResult, error)

    // 结束对话
    EndConversation(ctx context.Context, conversationID string) error

    // 获取对话历史
    GetConversationHistory(ctx context.Context, conversationID string) ([]*Message, error)
}

type AgentConfig struct {
    Name            string                 `json:"name"`
    Model           string                 `json:"model"`           // gpt-4/claude-3/etc
    Provider        string                 `json:"provider"`        // openai/anthropic/etc
    SystemPrompt    string                 `json:"system_prompt"`
    Temperature     float64                `json:"temperature"`
    MaxTokens       int                    `json:"max_tokens"`
    Tools           []*ToolDefinition      `json:"tools"`
    Memory          *MemoryConfig          `json:"memory"`
    Guardrails      []*GuardrailRule       `json:"guardrails"`
    Context         map[string]interface{} `json:"context"`
}

type ConversationInput struct {
    UserID      int64                  `json:"user_id"`
    SessionID   string                 `json:"session_id"`
    InitialMessage string              `json:"initial_message"`
    Context     map[string]interface{} `json:"context"`
    Metadata    map[string]interface{} `json:"metadata"`
}
```

#### 9.1.2 工具调用协议
```go
type ToolDefinition struct {
    Name        string                 `json:"name"`
    Description string                 `json:"description"`
    Parameters  map[string]interface{} `json:"parameters"` // JSON Schema
    Handler     ToolHandler            `json:"-"`
}

type ToolHandler interface {
    // 执行工具
    Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error)

    // 校验参数
    ValidateArgs(ctx context.Context, args map[string]interface{}) error

    // 获取工具信息
    GetInfo() *ToolInfo
}

type ToolCall struct {
    ID       string                 `json:"id"`
    Name     string                 `json:"name"`
    Args     map[string]interface{} `json:"args"`
    Timeout  time.Duration          `json:"timeout"`
}

type ToolResult struct {
    Success bool                   `json:"success"`
    Data    map[string]interface{} `json:"data"`
    Error   string                 `json:"error"`
    Metadata map[string]interface{} `json:"metadata"`
}
```

### 9.2 内存与上下文管理

#### 9.2.1 对话内存
```go
type ConversationMemory interface {
    // 保存消息
    SaveMessage(ctx context.Context, conversationID string, message *Message) error

    // 获取消息历史
    GetMessages(ctx context.Context, conversationID string, limit int) ([]*Message, error)

    // 更新上下文
    UpdateContext(ctx context.Context, conversationID string, context map[string]interface{}) error

    // 获取上下文
    GetContext(ctx context.Context, conversationID string) (map[string]interface{}, error)

    // 清理过期对话
    CleanupExpiredConversations(ctx context.Context, ttl time.Duration) error
}

type Message struct {
    ID            string                 `json:"id"`
    ConversationID string                `json:"conversation_id"`
    Role          string                 `json:"role"`          // user/assistant/system/tool
    Content       string                 `json:"content"`
    ToolCalls     []*ToolCall           `json:"tool_calls"`
    Metadata      map[string]interface{} `json:"metadata"`
    Timestamp     time.Time              `json:"timestamp"`
}

type MemoryConfig struct {
    Type        string        `json:"type"`         // short_term/long_term/episodic
    MaxMessages int           `json:"max_messages"` // 最大消息数
    TTL         time.Duration `json:"ttl"`          // 生存时间
    Compression bool          `json:"compression"`  // 是否压缩历史
}
```

### 9.3 安全护栏设计

#### 9.3.1 护栏规则引擎
```go
type GuardrailEngine interface {
    // 检查输入
    CheckInput(ctx context.Context, input string, rules []*GuardrailRule) (*GuardrailResult, error)

    // 检查输出
    CheckOutput(ctx context.Context, output string, rules []*GuardrailRule) (*GuardrailResult, error)

    // 检查工具调用
    CheckToolCall(ctx context.Context, toolCall *ToolCall, rules []*GuardrailRule) (*GuardrailResult, error)

    // 添加规则
    AddRule(ctx context.Context, rule *GuardrailRule) error

    // 更新规则
    UpdateRule(ctx context.Context, ruleID string, rule *GuardrailRule) error
}

type GuardrailRule struct {
    ID          string                 `json:"id"`
    Name        string                 `json:"name"`
    Type        string                 `json:"type"`        // content/behavior/privacy/safety
    Pattern     string                 `json:"pattern"`     // 正则表达式或规则表达式
    Action      string                 `json:"action"`      // block/warn/log/modify
    Severity    string                 `json:"severity"`    // low/medium/high/critical
    Message     string                 `json:"message"`
    Enabled     bool                   `json:"enabled"`
    Config      map[string]interface{} `json:"config"`
}

type GuardrailResult struct {
    Allowed     bool                   `json:"allowed"`
    Violations  []*GuardrailViolation  `json:"violations"`
    ModifiedContent string             `json:"modified_content"`
    Confidence  float64                `json:"confidence"`
}

type GuardrailViolation struct {
    RuleID      string  `json:"rule_id"`
    RuleName    string  `json:"rule_name"`
    Severity    string  `json:"severity"`
    Message     string  `json:"message"`
    Confidence  float64 `json:"confidence"`
    Position    int     `json:"position"`
    Length      int     `json:"length"`
}
```

### 9.4 模型与供应商抽象

#### 9.4.1 模型提供商接口
```go
type ModelProvider interface {
    // 发送聊天请求
    ChatCompletion(ctx context.Context, req *ChatRequest) (*ChatResponse, error)

    // 流式聊天
    ChatCompletionStream(ctx context.Context, req *ChatRequest) (<-chan *ChatStreamResponse, error)

    // 嵌入向量
    Embeddings(ctx context.Context, texts []string) ([][]float64, error)

    // 获取模型信息
    GetModelInfo(ctx context.Context, modelName string) (*ModelInfo, error)

    // 健康检查
    HealthCheck(ctx context.Context) error
}

type ChatRequest struct {
    Model       string                 `json:"model"`
    Messages    []*Message             `json:"messages"`
    Temperature float64                `json:"temperature"`
    MaxTokens   int                    `json:"max_tokens"`
    Tools       []*ToolDefinition      `json:"tools"`
    Stream      bool                   `json:"stream"`
    Metadata    map[string]interface{} `json:"metadata"`
}

type ChatResponse struct {
    ID      string    `json:"id"`
    Model   string    `json:"model"`
    Message *Message  `json:"message"`
    Usage   *Usage    `json:"usage"`
    Metadata map[string]interface{} `json:"metadata"`
}

type Usage struct {
    PromptTokens     int `json:"prompt_tokens"`
    CompletionTokens int `json:"completion_tokens"`
    TotalTokens      int `json:"total_tokens"`
}
```

### 9.5 内置AI工具

#### 9.5.1 数据查询工具
```go
type DataQueryTool struct {
    dataService DataService
}

func (t *DataQueryTool) Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error) {
    query := args["query"].(string)
    table := args["table"].(string)
    filters := args["filters"].(map[string]interface{})

    // 安全检查
    if err := t.validateQuery(query); err != nil {
        return &ToolResult{
            Success: false,
            Error:   fmt.Sprintf("Invalid query: %v", err),
        }, nil
    }

    // 执行查询
    result, err := t.dataService.Query(ctx, &QueryRequest{
        Query:   query,
        Table:   table,
        Filters: filters,
    })
    if err != nil {
        return &ToolResult{
            Success: false,
            Error:   err.Error(),
        }, nil
    }

    return &ToolResult{
        Success: true,
        Data: map[string]interface{}{
            "rows":  result.Rows,
            "count": result.Count,
        },
    }, nil
}
```

#### 9.5.2 邮件发送工具
```go
type EmailSendTool struct {
    emailService EmailService
}

func (t *EmailSendTool) Execute(ctx context.Context, args map[string]interface{}) (*ToolResult, error) {
    templateID := int64(args["template_id"].(float64))
    toEmail := args["to_email"].(string)
    variables := args["variables"].(map[string]interface{})

    // 发送邮件
    result, err := t.emailService.SendEmail(ctx, &EmailRequest{
        TemplateID: templateID,
        ToEmail:    toEmail,
        Variables:  variables,
    })
    if err != nil {
        return &ToolResult{
            Success: false,
            Error:   err.Error(),
        }, nil
    }

    return &ToolResult{
        Success: true,
        Data: map[string]interface{}{
            "message_id": result.MessageID,
            "status":     result.Status,
        },
    }, nil
}
```

---

## 10. 人群圈选引擎

### 10.1 分群DSL设计

#### 10.1.1 DSL语法定义
```json
{
  "type": "and",
  "conditions": [
    {
      "type": "attribute",
      "field": "age",
      "operator": "gte",
      "value": 18
    },
    {
      "type": "attribute",
      "field": "city",
      "operator": "in",
      "value": ["北京", "上海", "深圳"]
    },
    {
      "type": "behavior",
      "event": "email_open",
      "timeWindow": {
        "start": "2024-01-01",
        "end": "2024-12-31"
      },
      "frequency": {
        "operator": "gte",
        "value": 5
      }
    },
    {
      "type": "sequence",
      "events": [
        {
          "event": "page_view",
          "properties": {"page": "/product"}
        },
        {
          "event": "add_to_cart",
          "within": "1h"
        }
      ]
    }
  ]
}
```

#### 10.1.2 分群引擎接口
```go
type SegmentEngine interface {
    // 创建分群
    CreateSegment(ctx context.Context, segment *Segment) (int64, error)

    // 更新分群规则
    UpdateSegmentRule(ctx context.Context, segmentID int64, rule *SegmentRule) error

    // 计算分群
    ComputeSegment(ctx context.Context, segmentID int64, options *ComputeOptions) (*ComputeJob, error)

    // 获取分群快照
    GetSegmentSnapshot(ctx context.Context, segmentID int64, snapshotTime time.Time) (*SegmentSnapshot, error)

    // 查询分群成员
    QuerySegmentMembers(ctx context.Context, segmentID int64, filter *MemberFilter) ([]*SegmentMember, error)

    // 分群交并补运算
    CombineSegments(ctx context.Context, operation string, segmentIDs []int64) (*SegmentSnapshot, error)
}

type SegmentRule struct {
    Type       string                 `json:"type"`       // and/or/not
    Conditions []*Condition           `json:"conditions"`
    TimeWindow *TimeWindow            `json:"time_window"`
    Limit      int                    `json:"limit"`
    OrderBy    string                 `json:"order_by"`
}

type Condition struct {
    Type       string                 `json:"type"`       // attribute/behavior/sequence/funnel
    Field      string                 `json:"field"`
    Operator   string                 `json:"operator"`   // eq/ne/gt/lt/gte/lte/in/nin/contains/regex
    Value      interface{}            `json:"value"`
    Properties map[string]interface{} `json:"properties"`
    TimeWindow *TimeWindow            `json:"time_window"`
    Frequency  *FrequencyCondition    `json:"frequency"`
}

type TimeWindow struct {
    Start    time.Time `json:"start"`
    End      time.Time `json:"end"`
    Relative string    `json:"relative"` // last_7d/last_30d/this_month
}

type FrequencyCondition struct {
    Operator string `json:"operator"` // eq/ne/gt/lt/gte/lte
    Value    int    `json:"value"`
}
```

### 10.2 数据刷新策略

#### 10.2.1 刷新调度器
```go
type SegmentRefreshScheduler interface {
    // 调度刷新任务
    ScheduleRefresh(ctx context.Context, segmentID int64, policy *RefreshPolicy) error

    // 立即刷新
    RefreshNow(ctx context.Context, segmentID int64) (*ComputeJob, error)

    // 增量刷新
    IncrementalRefresh(ctx context.Context, segmentID int64, since time.Time) (*ComputeJob, error)

    // 获取刷新状态
    GetRefreshStatus(ctx context.Context, segmentID int64) (*RefreshStatus, error)

    // 暂停/恢复刷新
    PauseRefresh(ctx context.Context, segmentID int64) error
    ResumeRefresh(ctx context.Context, segmentID int64) error
}

type RefreshPolicy struct {
    Type        string        `json:"type"`         // manual/scheduled/realtime
    Schedule    string        `json:"schedule"`     // cron表达式
    Incremental bool          `json:"incremental"`  // 是否增量刷新
    MaxDuration time.Duration `json:"max_duration"` // 最大执行时间
    Retry       *RetryPolicy  `json:"retry"`        // 重试策略
}

type RefreshStatus struct {
    SegmentID      int64     `json:"segment_id"`
    Status         string    `json:"status"`         // idle/running/completed/failed
    LastRefreshAt  time.Time `json:"last_refresh_at"`
    NextRefreshAt  time.Time `json:"next_refresh_at"`
    Progress       int       `json:"progress"`       // 0-100
    ProcessedCount int64     `json:"processed_count"`
    TotalCount     int64     `json:"total_count"`
    ErrorMessage   string    `json:"error_message"`
}
```

### 10.3 性能优化设计

#### 10.3.1 索引策略
```sql
-- 用户属性索引
CREATE INDEX idx_users_age ON users(tenant_id, age);
CREATE INDEX idx_users_city ON users(tenant_id, city);
CREATE INDEX idx_users_created_at ON users(tenant_id, created_at);

-- 事件索引
CREATE INDEX idx_events_user_type_time ON events(tenant_id, user_id, event_type, occurred_at);
CREATE INDEX idx_events_type_time ON events(tenant_id, event_type, occurred_at);
CREATE INDEX idx_events_properties ON events(tenant_id, (JSON_EXTRACT(properties, '$.page')));

-- 分群快照索引
CREATE INDEX idx_segment_snapshots_time ON segment_snapshots(segment_id, snapshot_time);
CREATE INDEX idx_segment_members ON segment_members(segment_id, user_id);
```

#### 10.3.2 缓存策略
```go
type SegmentCache interface {
    // 缓存分群快照
    CacheSnapshot(ctx context.Context, segmentID int64, snapshot *SegmentSnapshot) error

    // 获取缓存快照
    GetCachedSnapshot(ctx context.Context, segmentID int64) (*SegmentSnapshot, error)

    // 缓存查询结果
    CacheQueryResult(ctx context.Context, queryKey string, result interface{}, ttl time.Duration) error

    // 获取缓存查询结果
    GetCachedQueryResult(ctx context.Context, queryKey string) (interface{}, error)

    // 失效缓存
    InvalidateCache(ctx context.Context, segmentID int64) error
}

type CacheConfig struct {
    Enabled     bool          `json:"enabled"`
    TTL         time.Duration `json:"ttl"`
    MaxSize     int64         `json:"max_size"`
    Compression bool          `json:"compression"`
    Sharding    int           `json:"sharding"`
}
```

---

## 11. 邮件发送与模板

### 11.1 邮件模板系统

#### 11.1.1 模板引擎接口
```go
type TemplateEngine interface {
    // 渲染模板
    RenderTemplate(ctx context.Context, templateID int64, variables map[string]interface{}) (*RenderedEmail, error)

    // 预览模板
    PreviewTemplate(ctx context.Context, templateID int64, variables map[string]interface{}) (*EmailPreview, error)

    // 校验模板
    ValidateTemplate(ctx context.Context, template *EmailTemplate) (*ValidationResult, error)

    // 编译模板
    CompileTemplate(ctx context.Context, template *EmailTemplate) (*CompiledTemplate, error)

    // 获取模板变量
    GetTemplateVariables(ctx context.Context, templateID int64) ([]string, error)
}

type RenderedEmail struct {
    Subject     string            `json:"subject"`
    HTMLContent string            `json:"html_content"`
    TextContent string            `json:"text_content"`
    Headers     map[string]string `json:"headers"`
    Attachments []*Attachment     `json:"attachments"`
}

type EmailPreview struct {
    Subject     string `json:"subject"`
    HTMLContent string `json:"html_content"`
    TextContent string `json:"text_content"`
    PreviewText string `json:"preview_text"`
}

type CompiledTemplate struct {
    ID          int64     `json:"id"`
    TemplateID  int64     `json:"template_id"`
    CompiledAt  time.Time `json:"compiled_at"`
    Variables   []string  `json:"variables"`
    Checksum    string    `json:"checksum"`
}
```

### 11.2 发送流水设计

#### 11.2.1 邮件发送服务
```go
type EmailSendService interface {
    // 发送单封邮件
    SendEmail(ctx context.Context, req *EmailRequest) (*EmailResponse, error)

    // 批量发送邮件
    BatchSendEmail(ctx context.Context, requests []*EmailRequest) ([]*EmailResponse, error)

    // 获取发送状态
    GetSendStatus(ctx context.Context, messageID int64) (*SendStatus, error)

    // 重试发送
    RetrySend(ctx context.Context, messageID int64) error

    // 取消发送
    CancelSend(ctx context.Context, messageID int64) error
}

type EmailRequest struct {
    TemplateID    int64                  `json:"template_id"`
    ToEmail       string                 `json:"to_email"`
    ToName        string                 `json:"to_name"`
    FromEmail     string                 `json:"from_email"`
    FromName      string                 `json:"from_name"`
    Subject       string                 `json:"subject"`
    Variables     map[string]interface{} `json:"variables"`
    Headers       map[string]string      `json:"headers"`
    Priority      int                    `json:"priority"`
    ScheduledAt   *time.Time             `json:"scheduled_at"`
    IdempotencyKey string                `json:"idempotency_key"`
}

type EmailResponse struct {
    MessageID   int64     `json:"message_id"`
    Status      string    `json:"status"`
    QueuedAt    time.Time `json:"queued_at"`
    EstimatedSendTime *time.Time `json:"estimated_send_time"`
}
```

#### 11.2.2 发送队列处理器
```go
type EmailQueueProcessor interface {
    // 启动处理器
    Start(ctx context.Context) error

    // 停止处理器
    Stop(ctx context.Context) error

    // 处理队列消息
    ProcessQueue(ctx context.Context) error

    // 获取队列状态
    GetQueueStatus(ctx context.Context) (*QueueStatus, error)
}

type QueueStatus struct {
    QueuedCount    int64 `json:"queued_count"`
    ProcessingCount int64 `json:"processing_count"`
    CompletedCount int64 `json:"completed_count"`
    FailedCount    int64 `json:"failed_count"`
    ThroughputPerSecond float64 `json:"throughput_per_second"`
}
```

### 11.3 速率限制与频控

#### 11.3.1 频控策略
```go
type RateLimitStrategy interface {
    // 检查发送限制
    CheckSendLimit(ctx context.Context, req *SendLimitRequest) (*SendLimitResult, error)

    // 更新发送计数
    UpdateSendCount(ctx context.Context, key string, count int) error

    // 获取剩余配额
    GetRemainingQuota(ctx context.Context, key string) (int64, error)

    // 重置配额
    ResetQuota(ctx context.Context, key string) error
}

type SendLimitRequest struct {
    TenantID    int64  `json:"tenant_id"`
    UserID      int64  `json:"user_id"`
    ToEmail     string `json:"to_email"`
    ToDomain    string `json:"to_domain"`
    AccountID   int64  `json:"account_id"`
    MessageType string `json:"message_type"`
}

type SendLimitResult struct {
    Allowed       bool          `json:"allowed"`
    Reason        string        `json:"reason"`
    RetryAfter    time.Duration `json:"retry_after"`
    RemainingQuota int64        `json:"remaining_quota"`
    ResetTime     time.Time     `json:"reset_time"`
}
```

### 11.4 退信处理

#### 11.4.1 退信处理器
```go
type BounceHandler interface {
    // 处理退信
    HandleBounce(ctx context.Context, bounce *BounceEvent) error

    // 处理投诉
    HandleComplaint(ctx context.Context, complaint *ComplaintEvent) error

    // 更新抑制列表
    UpdateSuppressionList(ctx context.Context, email string, reason string) error

    // 检查抑制状态
    CheckSuppression(ctx context.Context, email string) (*SuppressionStatus, error)
}

type BounceEvent struct {
    MessageID    int64     `json:"message_id"`
    Email        string    `json:"email"`
    BounceType   string    `json:"bounce_type"`   // hard/soft
    BounceReason string    `json:"bounce_reason"`
    BounceCode   string    `json:"bounce_code"`
    RawMessage   string    `json:"raw_message"`
    OccurredAt   time.Time `json:"occurred_at"`
}

type SuppressionStatus struct {
    Email      string    `json:"email"`
    Suppressed bool      `json:"suppressed"`
    Reason     string    `json:"reason"`
    AddedAt    time.Time `json:"added_at"`
    ExpiresAt  *time.Time `json:"expires_at"`
}
```

---

## 12. API与契约

### 12.1 核心API清单

#### 12.1.1 工作流管理API
```http
# 创建工作流
POST /api/workflows/create
Content-Type: application/json

{
  "name": "用户欢迎流程",
  "description": "新用户注册后的欢迎邮件流程",
  "category": "marketing",
  "tags": ["welcome", "email"]
}

# 响应
{
  "code": 0,
  "message": "success",
  "data": {
    "workflow_id": 12345,
    "name": "用户欢迎流程",
    "status": "draft",
    "created_at": "2024-01-01T00:00:00Z"
  },
  "meta": {
    "request_id": "req_123456789",
    "trace_id": "trace_987654321"
  }
}

# 获取工作流列表
POST /api/workflows/list
Content-Type: application/json

{
  "page": 1,
  "size": 20,
  "status": "published",
  "category": "marketing"
}

# 启动工作流
POST /api/workflows/run
Content-Type: application/json

{
  "workflow_id": 12345,
  "input_data": {
    "user_id": 67890,
    "email": "<EMAIL>",
    "name": "张三"
  },
  "trigger_type": "manual"
}
```

#### 12.1.2 节点管理API
```http
# 注册节点插件
POST /api/nodes/register
Content-Type: application/json

{
  "node_type": "email_send",
  "name": "邮件发送节点",
  "version": "1.0.0",
  "config_schema": {
    "type": "object",
    "properties": {
      "template_id": {"type": "integer"},
      "priority": {"type": "integer", "default": 100}
    }
  }
}

# 获取节点列表
POST /api/nodes/list
Content-Type: application/json

{
  "category": "communication",
  "enabled": true
}
```

#### 12.1.3 分群管理API
```http
# 创建分群
POST /api/segments/create
Content-Type: application/json

{
  "name": "活跃用户",
  "description": "最近30天有登录的用户",
  "rule_dsl": {
    "type": "and",
    "conditions": [
      {
        "type": "behavior",
        "event": "user_login",
        "time_window": {
          "relative": "last_30d"
        },
        "frequency": {
          "operator": "gte",
          "value": 1
        }
      }
    ]
  },
  "refresh_policy": "daily"
}

# 计算分群
POST /api/segments/compute
Content-Type: application/json

{
  "segment_id": 12345,
  "type": "full"
}

# 查询分群成员
POST /api/segments/members
Content-Type: application/json

{
  "segment_id": 12345,
  "page": 1,
  "size": 100
}
```

### 12.2 统一响应结构

#### 12.2.1 成功响应
```json
{
  "code": 0,
  "message": "success",
  "data": {
    // 业务数据
  },
  "meta": {
    "request_id": "req_123456789",
    "trace_id": "trace_987654321",
    "timestamp": 1640995200,
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100
    }
  }
}
```

#### 12.2.2 错误响应
```json
{
  "code": 10001,
  "message": "参数验证失败",
  "errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确"
    },
    {
      "field": "name",
      "message": "名称不能为空"
    }
  ],
  "meta": {
    "request_id": "req_123456789",
    "trace_id": "trace_987654321",
    "timestamp": 1640995200
  }
}
```

### 12.3 错误码约定

| 错误码 | 说明 | HTTP状态码 |
|--------|------|------------|
| 0 | 成功 | 200 |
| 10001 | 参数验证失败 | 200 |
| 10002 | 资源不存在 | 200 |
| 10003 | 权限不足 | 200 |
| 10004 | 操作失败 | 200 |
| 10005 | 频率限制 | 200 |
| 20001 | 工作流不存在 | 200 |
| 20002 | 工作流状态错误 | 200 |
| 20003 | 节点配置错误 | 200 |
| 30001 | 分群不存在 | 200 |
| 30002 | 分群计算失败 | 200 |
| 40001 | 邮件发送失败 | 200 |
| 40002 | 模板不存在 | 200 |
| 50000 | 系统内部错误 | 200 |

---

## 13. 可观测性与治理

### 13.1 追踪体系设计

#### 13.1.1 Trace/Span命名规范
```go
// 工作流执行追踪
span := tracer.Start(ctx, "workflow.execute",
    trace.WithAttributes(
        attribute.Int64("workflow.id", workflowID),
        attribute.String("workflow.name", workflowName),
        attribute.String("workflow.version", version),
        attribute.Int64("tenant.id", tenantID),
    ),
)

// 节点执行追踪
nodeSpan := tracer.Start(ctx, "node.execute",
    trace.WithAttributes(
        attribute.String("node.type", nodeType),
        attribute.String("node.id", nodeID),
        attribute.Int64("run.id", runID),
        attribute.Int("step.sequence", stepSequence),
    ),
)

// 分群计算追踪
segmentSpan := tracer.Start(ctx, "segment.compute",
    trace.WithAttributes(
        attribute.Int64("segment.id", segmentID),
        attribute.String("compute.type", computeType),
        attribute.Int64("processed.count", processedCount),
    ),
)
```

#### 13.1.2 业务指标定义
```go
var (
    // 工作流指标
    workflowRunsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "workflow_runs_total",
            Help: "Total number of workflow runs",
        },
        []string{"tenant_id", "workflow_id", "status"},
    )

    workflowRunDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "workflow_run_duration_seconds",
            Help: "Workflow run duration in seconds",
            Buckets: prometheus.ExponentialBuckets(0.1, 2, 10),
        },
        []string{"tenant_id", "workflow_id"},
    )

    // 节点执行指标
    nodeExecutionsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "node_executions_total",
            Help: "Total number of node executions",
        },
        []string{"tenant_id", "node_type", "status"},
    )

    // 邮件发送指标
    emailsSentTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "emails_sent_total",
            Help: "Total number of emails sent",
        },
        []string{"tenant_id", "template_id", "status"},
    )

    emailDeliveryRate = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "email_delivery_rate",
            Help: "Email delivery rate",
        },
        []string{"tenant_id"},
    )
)
```

### 13.2 日志规范

#### 13.2.1 结构化日志格式
```go
type LogEntry struct {
    Timestamp   time.Time              `json:"timestamp"`
    Level       string                 `json:"level"`
    Message     string                 `json:"message"`
    TraceID     string                 `json:"trace_id"`
    SpanID      string                 `json:"span_id"`
    RequestID   string                 `json:"request_id"`
    TenantID    int64                  `json:"tenant_id"`
    UserID      int64                  `json:"user_id,omitempty"`
    Component   string                 `json:"component"`
    Operation   string                 `json:"operation"`
    Duration    int64                  `json:"duration_ms,omitempty"`
    Error       string                 `json:"error,omitempty"`
    Fields      map[string]interface{} `json:"fields,omitempty"`
}

// 使用示例
logger.Info(ctx, "Workflow execution started",
    logiface.Int64("workflow_id", workflowID),
    logiface.String("workflow_name", workflowName),
    logiface.Int64("run_id", runID),
    logiface.String("trigger_type", triggerType),
)

logger.Error(ctx, "Node execution failed",
    logiface.Int64("run_id", runID),
    logiface.String("node_id", nodeID),
    logiface.String("node_type", nodeType),
    logiface.Error(err),
    logiface.Int("retry_count", retryCount),
)
```

### 13.3 监控告警

#### 13.3.1 告警规则
```yaml
groups:
  - name: workflow_alerts
    rules:
      - alert: WorkflowFailureRateHigh
        expr: rate(workflow_runs_total{status="failed"}[5m]) / rate(workflow_runs_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "工作流失败率过高"
          description: "租户 {{ $labels.tenant_id }} 的工作流失败率超过10%"

      - alert: WorkflowExecutionSlow
        expr: histogram_quantile(0.95, workflow_run_duration_seconds) > 300
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "工作流执行缓慢"
          description: "95%的工作流执行时间超过5分钟"

      - alert: EmailDeliveryRateLow
        expr: email_delivery_rate < 0.9
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: "邮件投递率过低"
          description: "租户 {{ $labels.tenant_id }} 的邮件投递率低于90%"
```

---

## 14. 版本化与灰度

### 14.1 版本策略

#### 14.1.1 工作流版本管理
```go
type WorkflowVersionManager interface {
    // 创建新版本
    CreateVersion(ctx context.Context, workflowID int64, definition *WorkflowDefinition) (*WorkflowVersion, error)

    // 发布版本
    PublishVersion(ctx context.Context, versionID int64) error

    // 回滚版本
    RollbackVersion(ctx context.Context, workflowID int64, targetVersionID int64) error

    // 获取版本差异
    GetVersionDiff(ctx context.Context, fromVersionID, toVersionID int64) (*VersionDiff, error)

    // 迁移运行实例
    MigrateRunningInstances(ctx context.Context, fromVersionID, toVersionID int64) error
}

type VersionDiff struct {
    AddedNodes    []*NodeInstance `json:"added_nodes"`
    RemovedNodes  []*NodeInstance `json:"removed_nodes"`
    ModifiedNodes []*NodeDiff     `json:"modified_nodes"`
    AddedEdges    []*Edge         `json:"added_edges"`
    RemovedEdges  []*Edge         `json:"removed_edges"`
}
```

### 14.2 灰度发布

#### 14.2.2 灰度策略
```go
type GrayReleaseManager interface {
    // 创建灰度发布
    CreateGrayRelease(ctx context.Context, release *GrayRelease) error

    // 更新灰度比例
    UpdateGrayRatio(ctx context.Context, releaseID int64, ratio float64) error

    // 获取灰度状态
    GetGrayStatus(ctx context.Context, releaseID int64) (*GrayStatus, error)

    // 完成灰度发布
    CompleteGrayRelease(ctx context.Context, releaseID int64) error

    // 回滚灰度发布
    RollbackGrayRelease(ctx context.Context, releaseID int64) error
}

type GrayRelease struct {
    ID              int64                  `json:"id"`
    WorkflowID      int64                  `json:"workflow_id"`
    FromVersionID   int64                  `json:"from_version_id"`
    ToVersionID     int64                  `json:"to_version_id"`
    Strategy        string                 `json:"strategy"`        // user_based/percentage/feature_flag
    Ratio           float64                `json:"ratio"`           // 0.0-1.0
    Conditions      map[string]interface{} `json:"conditions"`      // 灰度条件
    Status          string                 `json:"status"`          // preparing/running/completed/rollback
    StartTime       time.Time              `json:"start_time"`
    EndTime         *time.Time             `json:"end_time"`
    Metrics         *GrayMetrics           `json:"metrics"`
}

type GrayMetrics struct {
    TotalRuns       int64   `json:"total_runs"`
    GrayRuns        int64   `json:"gray_runs"`
    SuccessRate     float64 `json:"success_rate"`
    GraySuccessRate float64 `json:"gray_success_rate"`
    AvgDuration     float64 `json:"avg_duration"`
    GrayAvgDuration float64 `json:"gray_avg_duration"`
}
```

---

## 15. 测试与质量

### 15.1 测试策略

#### 15.1.1 单元测试
```go
func TestWorkflowExecution(t *testing.T) {
    tests := []struct {
        name           string
        workflowDef    *WorkflowDefinition
        inputData      map[string]interface{}
        expectedStatus WorkflowRunStatus
        expectedOutput map[string]interface{}
    }{
        {
            name: "简单邮件发送流程",
            workflowDef: &WorkflowDefinition{
                Nodes: []*NodeInstance{
                    {ID: "start", Type: "start"},
                    {ID: "email", Type: "email_send", Config: map[string]interface{}{
                        "template_id": 123,
                    }},
                    {ID: "end", Type: "end"},
                },
                Edges: []*Edge{
                    {SourceNodeID: "start", TargetNodeID: "email"},
                    {SourceNodeID: "email", TargetNodeID: "end"},
                },
            },
            inputData: map[string]interface{}{
                "email": "<EMAIL>",
                "name":  "测试用户",
            },
            expectedStatus: StatusCompleted,
            expectedOutput: map[string]interface{}{
                "message_id": int64(456),
                "status":     "sent",
            },
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 并行执行测试
            t.Parallel()

            // 创建测试上下文
            ctx := context.Background()

            // 执行工作流
            result, err := workflowEngine.Execute(ctx, tt.workflowDef, tt.inputData)

            // 断言结果
            assert.NoError(t, err)
            assert.Equal(t, tt.expectedStatus, result.Status)
            assert.Equal(t, tt.expectedOutput, result.Output)
        })
    }
}
```

#### 15.1.2 集成测试
```go
func TestWorkflowIntegration(t *testing.T) {
    // 设置测试环境
    testDB := setupTestDatabase(t)
    defer testDB.Close()

    testRedis := setupTestRedis(t)
    defer testRedis.Close()

    // 创建测试容器
    container := setupTestContainer(t, testDB, testRedis)

    // 测试完整流程
    t.Run("用户注册欢迎流程", func(t *testing.T) {
        // 1. 创建工作流
        workflow, err := container.WorkflowService.CreateWorkflow(ctx, &CreateWorkflowRequest{
            Name:        "用户注册欢迎流程",
            Definition:  loadWorkflowDefinition("welcome_flow.json"),
        })
        require.NoError(t, err)

        // 2. 发布工作流
        err = container.WorkflowService.PublishWorkflow(ctx, workflow.ID)
        require.NoError(t, err)

        // 3. 启动工作流
        run, err := container.WorkflowService.StartWorkflow(ctx, &StartWorkflowRequest{
            WorkflowID: workflow.ID,
            InputData: map[string]interface{}{
                "user_id": 12345,
                "email":   "<EMAIL>",
                "name":    "新用户",
            },
        })
        require.NoError(t, err)

        // 4. 等待执行完成
        err = waitForWorkflowCompletion(ctx, container, run.ID, 30*time.Second)
        require.NoError(t, err)

        // 5. 验证结果
        finalRun, err := container.WorkflowService.GetWorkflowRun(ctx, run.ID)
        require.NoError(t, err)
        assert.Equal(t, StatusCompleted, finalRun.Status)

        // 6. 验证邮件发送
        messages, err := container.EmailService.GetMessagesByRun(ctx, run.ID)
        require.NoError(t, err)
        assert.Len(t, messages, 1)
        assert.Equal(t, "sent", messages[0].Status)
    })
}
```

### 15.2 性能测试

#### 15.2.1 压力测试
```go
func BenchmarkWorkflowExecution(b *testing.B) {
    // 设置基准测试
    container := setupBenchmarkContainer(b)

    // 创建测试工作流
    workflow := createBenchmarkWorkflow(b, container)

    b.ResetTimer()
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            // 启动工作流
            run, err := container.WorkflowService.StartWorkflow(context.Background(), &StartWorkflowRequest{
                WorkflowID: workflow.ID,
                InputData: generateRandomInputData(),
            })
            if err != nil {
                b.Fatal(err)
            }

            // 等待完成
            err = waitForWorkflowCompletion(context.Background(), container, run.ID, 10*time.Second)
            if err != nil {
                b.Fatal(err)
            }
        }
    })
}

func TestConcurrentWorkflowExecution(t *testing.T) {
    container := setupTestContainer(t)
    workflow := createTestWorkflow(t, container)

    // 并发执行1000个工作流
    concurrency := 100
    totalRuns := 1000

    var wg sync.WaitGroup
    semaphore := make(chan struct{}, concurrency)
    results := make(chan *WorkflowRun, totalRuns)

    for i := 0; i < totalRuns; i++ {
        wg.Add(1)
        go func(i int) {
            defer wg.Done()
            semaphore <- struct{}{}
            defer func() { <-semaphore }()

            run, err := container.WorkflowService.StartWorkflow(context.Background(), &StartWorkflowRequest{
                WorkflowID: workflow.ID,
                InputData: map[string]interface{}{
                    "index": i,
                    "email": fmt.Sprintf("<EMAIL>", i),
                },
            })

            if err != nil {
                t.Errorf("Failed to start workflow %d: %v", i, err)
                return
            }

            results <- run
        }(i)
    }

    wg.Wait()
    close(results)

    // 验证结果
    successCount := 0
    for run := range results {
        if run != nil {
            successCount++
        }
    }

    assert.Equal(t, totalRuns, successCount)
}
```

---

## 16. 前端与交互

### 16.1 流程设计器

#### 16.1.1 画布组件设计
```typescript
interface WorkflowCanvas {
  // 节点管理
  addNode(nodeType: string, position: Position): Node;
  removeNode(nodeId: string): void;
  updateNode(nodeId: string, config: NodeConfig): void;

  // 连线管理
  addEdge(sourceId: string, targetId: string): Edge;
  removeEdge(edgeId: string): void;
  updateEdge(edgeId: string, condition: string): void;

  // 布局管理
  autoLayout(): void;
  zoomToFit(): void;
  centerView(): void;

  // 校验
  validateWorkflow(): ValidationResult;

  // 序列化
  serialize(): WorkflowDefinition;
  deserialize(definition: WorkflowDefinition): void;
}

interface Node {
  id: string;
  type: string;
  name: string;
  position: Position;
  config: NodeConfig;
  status?: NodeStatus;
}

interface Edge {
  id: string;
  sourceId: string;
  targetId: string;
  condition?: string;
  label?: string;
}
```

### 16.2 运行监控台

#### 16.2.1 实时监控组件
```typescript
interface WorkflowMonitor {
  // 获取运行列表
  getRunList(filter: RunFilter): Promise<WorkflowRun[]>;

  // 获取运行详情
  getRunDetail(runId: number): Promise<WorkflowRunDetail>;

  // 获取执行时间线
  getExecutionTimeline(runId: number): Promise<ExecutionEvent[]>;

  // 实时状态更新
  subscribeToUpdates(runId: number, callback: (event: ExecutionEvent) => void): void;

  // 操作控制
  pauseRun(runId: number): Promise<void>;
  resumeRun(runId: number): Promise<void>;
  cancelRun(runId: number): Promise<void>;
}

interface ExecutionEvent {
  id: string;
  runId: number;
  nodeId: string;
  eventType: 'started' | 'completed' | 'failed' | 'skipped';
  timestamp: Date;
  duration?: number;
  error?: string;
  data?: any;
}
```

---

## 17. 样例与验收

### 17.1 完整样例流程

#### 17.1.1 新用户引导流程
```json
{
  "name": "新用户引导流程",
  "description": "新用户注册后的自动化引导流程",
  "nodes": [
    {
      "id": "start",
      "type": "start",
      "name": "开始",
      "position": {"x": 100, "y": 100}
    },
    {
      "id": "welcome_email",
      "type": "email_send",
      "name": "发送欢迎邮件",
      "position": {"x": 300, "y": 100},
      "config": {
        "template_id": 1001,
        "priority": 100,
        "delay": "0s"
      }
    },
    {
      "id": "wait_24h",
      "type": "delay",
      "name": "等待24小时",
      "position": {"x": 500, "y": 100},
      "config": {
        "duration": "24h"
      }
    },
    {
      "id": "check_activation",
      "type": "condition",
      "name": "检查激活状态",
      "position": {"x": 700, "y": 100},
      "config": {
        "expression": "user.activated == true"
      }
    },
    {
      "id": "activation_email",
      "type": "email_send",
      "name": "发送激活提醒",
      "position": {"x": 700, "y": 300},
      "config": {
        "template_id": 1002,
        "priority": 90
      }
    },
    {
      "id": "guide_email",
      "type": "email_send",
      "name": "发送使用指南",
      "position": {"x": 900, "y": 100},
      "config": {
        "template_id": 1003,
        "priority": 80
      }
    },
    {
      "id": "end",
      "type": "end",
      "name": "结束",
      "position": {"x": 1100, "y": 200}
    }
  ],
  "edges": [
    {"sourceId": "start", "targetId": "welcome_email"},
    {"sourceId": "welcome_email", "targetId": "wait_24h"},
    {"sourceId": "wait_24h", "targetId": "check_activation"},
    {"sourceId": "check_activation", "targetId": "activation_email", "condition": "user.activated == false"},
    {"sourceId": "check_activation", "targetId": "guide_email", "condition": "user.activated == true"},
    {"sourceId": "activation_email", "targetId": "end"},
    {"sourceId": "guide_email", "targetId": "end"}
  ]
}
```

**输入数据样例**：
```json
{
  "user_id": 12345,
  "email": "<EMAIL>",
  "name": "张三",
  "registration_time": "2024-01-01T10:00:00Z",
  "source": "website"
}
```

**期望输出**：
- 立即发送欢迎邮件
- 24小时后检查用户激活状态
- 根据激活状态发送不同邮件
- 记录完整执行轨迹

#### 17.1.2 多触点营销流程
```json
{
  "name": "产品推广营销流程",
  "description": "基于用户行为的个性化产品推广",
  "nodes": [
    {
      "id": "start",
      "type": "start",
      "name": "开始"
    },
    {
      "id": "segment_users",
      "type": "segment_query",
      "name": "查询目标用户",
      "config": {
        "segment_id": 2001,
        "max_users": 10000
      }
    },
    {
      "id": "ab_split",
      "type": "ab_test",
      "name": "A/B测试分组",
      "config": {
        "variants": [
          {"name": "A", "ratio": 0.5, "template_id": 3001},
          {"name": "B", "ratio": 0.5, "template_id": 3002}
        ]
      }
    },
    {
      "id": "send_email",
      "type": "email_send",
      "name": "发送推广邮件",
      "config": {
        "template_id": "{{variant.template_id}}",
        "priority": 70
      }
    },
    {
      "id": "wait_response",
      "type": "wait_event",
      "name": "等待用户响应",
      "config": {
        "events": ["email_open", "email_click"],
        "timeout": "72h"
      }
    },
    {
      "id": "check_engagement",
      "type": "condition",
      "name": "检查用户参与度",
      "config": {
        "expression": "event.type in ['email_open', 'email_click']"
      }
    },
    {
      "id": "follow_up_email",
      "type": "email_send",
      "name": "发送跟进邮件",
      "config": {
        "template_id": 3003,
        "delay": "24h"
      }
    },
    {
      "id": "end",
      "type": "end",
      "name": "结束"
    }
  ],
  "edges": [
    {"sourceId": "start", "targetId": "segment_users"},
    {"sourceId": "segment_users", "targetId": "ab_split"},
    {"sourceId": "ab_split", "targetId": "send_email"},
    {"sourceId": "send_email", "targetId": "wait_response"},
    {"sourceId": "wait_response", "targetId": "check_engagement"},
    {"sourceId": "check_engagement", "targetId": "follow_up_email", "condition": "event.type == 'email_open'"},
    {"sourceId": "check_engagement", "targetId": "end", "condition": "event.type == 'email_click'"},
    {"sourceId": "wait_response", "targetId": "end", "condition": "timeout"},
    {"sourceId": "follow_up_email", "targetId": "end"}
  ]
}
```

### 17.2 验收清单

#### 17.2.1 功能验收
- [ ] **工作流管理**
  - [ ] 创建、编辑、删除工作流
  - [ ] 版本管理和发布
  - [ ] 工作流导入导出
  - [ ] 权限控制和共享

- [ ] **节点插件**
  - [ ] 内置节点正常工作
  - [ ] 自定义节点注册和使用
  - [ ] 节点配置校验
  - [ ] 节点版本兼容性

- [ ] **执行引擎**
  - [ ] 串行和并行执行
  - [ ] 条件分支和循环
  - [ ] 错误处理和重试
  - [ ] 暂停和恢复

- [ ] **分群功能**
  - [ ] 分群规则配置
  - [ ] 实时和定时刷新
  - [ ] 分群交并补运算
  - [ ] 性能优化

- [ ] **表单系统**
  - [ ] 动态表单渲染
  - [ ] 表单校验和提交
  - [ ] 人工审批流程
  - [ ] 表单版本管理

- [ ] **AI功能**
  - [ ] 智能体对话
  - [ ] 工具调用
  - [ ] 安全护栏
  - [ ] 上下文管理

- [ ] **邮件发送**
  - [ ] 模板渲染
  - [ ] 批量发送
  - [ ] 退信处理
  - [ ] 发送统计

#### 17.2.2 稳定性验收
- [ ] **高可用**
  - [ ] 服务故障自动切换
  - [ ] 数据库主从切换
  - [ ] 缓存故障降级
  - [ ] 负载均衡

- [ ] **性能**
  - [ ] API响应时间 < 200ms (P95)
  - [ ] 工作流启动时间 < 500ms
  - [ ] 支持1000并发执行
  - [ ] 内存使用稳定

- [ ] **容错**
  - [ ] 网络异常恢复
  - [ ] 服务重启恢复
  - [ ] 数据一致性保证
  - [ ] 死锁检测和恢复

#### 17.2.3 安全验收
- [ ] **认证授权**
  - [ ] JWT令牌验证
  - [ ] 权限控制
  - [ ] 多租户隔离
  - [ ] API访问控制

- [ ] **数据安全**
  - [ ] 敏感数据加密
  - [ ] 数据脱敏
  - [ ] 访问审计
  - [ ] 数据备份

- [ ] **输入验证**
  - [ ] 参数校验
  - [ ] SQL注入防护
  - [ ] XSS防护
  - [ ] 文件上传安全

#### 17.2.4 可观测性验收
- [ ] **监控**
  - [ ] 业务指标监控
  - [ ] 系统指标监控
  - [ ] 告警规则配置
  - [ ] 监控面板

- [ ] **日志**
  - [ ] 结构化日志
  - [ ] 日志聚合
  - [ ] 错误日志告警
  - [ ] 日志检索

- [ ] **追踪**
  - [ ] 分布式追踪
  - [ ] 链路分析
  - [ ] 性能分析
  - [ ] 错误定位

---

## 18. 里程碑与风险

### 18.1 里程碑拆解

#### 18.1.1 第一阶段：基础平台 (8周)
**目标**：建立核心架构和基础功能

**里程碑**：
- [ ] **Week 1-2**: 架构设计和技术选型
  - 完成详细设计文档
  - 搭建开发环境
  - 建立CI/CD流水线
  - 数据库表结构设计

- [ ] **Week 3-4**: 核心领域模型
  - 工作流聚合实现
  - 节点插件框架
  - 执行引擎基础版
  - 基础API接口

- [ ] **Week 5-6**: 基础功能实现
  - 工作流CRUD操作
  - 简单节点插件(开始、结束、条件)
  - 基础执行引擎
  - 前端设计器原型

- [ ] **Week 7-8**: 集成测试和优化
  - 端到端测试
  - 性能优化
  - 文档完善
  - 部署脚本

**交付物**：
- 可运行的基础平台
- 简单工作流创建和执行
- 基础监控和日志
- 部署文档

#### 18.1.2 第二阶段：核心功能 (10周)
**目标**：实现主要业务功能

**里程碑**：
- [ ] **Week 9-10**: 邮件发送功能
  - 邮件模板系统
  - 发送队列处理
  - 退信处理
  - 发送统计

- [ ] **Week 11-12**: 分群引擎
  - 分群DSL解析
  - 分群计算引擎
  - 快照管理
  - 性能优化

- [ ] **Week 13-14**: 表单系统
  - 动态表单引擎
  - 表单渲染器
  - 人工任务流程
  - 审批功能

- [ ] **Week 15-16**: 调度系统
  - 定时调度器
  - 事件触发器
  - 频率控制
  - 并发管理

- [ ] **Week 17-18**: 前端完善
  - 流程设计器
  - 运行监控台
  - 分群设计器
  - 表单设计器

**交付物**：
- 完整的工作流平台
- 邮件营销功能
- 用户分群功能
- 可视化界面

#### 18.1.3 第三阶段：高级功能 (8周)
**目标**：实现AI和高级特性

**里程碑**：
- [ ] **Week 19-20**: AI智能体
  - AI Agent框架
  - 工具调用系统
  - 安全护栏
  - 对话管理

- [ ] **Week 21-22**: 高级执行特性
  - 复杂重试策略
  - 补偿机制
  - 长跑任务支持
  - 死信处理

- [ ] **Week 23-24**: 版本管理
  - 工作流版本控制
  - 灰度发布
  - 回滚机制
  - 兼容性管理

- [ ] **Week 25-26**: 可观测性
  - 完整监控体系
  - 分布式追踪
  - 告警系统
  - 性能分析

**交付物**：
- AI增强的工作流平台
- 企业级可靠性特性
- 完整的可观测性
- 生产就绪系统

#### 18.1.4 第四阶段：优化和扩展 (6周)
**目标**：性能优化和生态扩展

**里程碑**：
- [ ] **Week 27-28**: 性能优化
  - 数据库优化
  - 缓存策略
  - 并发优化
  - 资源管理

- [ ] **Week 29-30**: 生态扩展
  - 更多节点插件
  - 第三方集成
  - API生态
  - 插件市场

- [ ] **Week 31-32**: 文档和培训
  - 用户文档
  - 开发文档
  - 培训材料
  - 最佳实践

**交付物**：
- 高性能生产系统
- 丰富的插件生态
- 完整的文档体系
- 培训和支持材料

### 18.2 风险清单与对策

#### 18.2.1 技术风险

| 风险 | 概率 | 影响 | 对策 |
|------|------|------|------|
| 性能不达标 | 中 | 高 | 早期性能测试，架构预留扩展性 |
| 数据一致性问题 | 中 | 高 | 严格的事务设计，完善的测试 |
| 插件安全漏洞 | 中 | 高 | 安全沙箱，代码审查，安全测试 |
| 分布式系统复杂性 | 高 | 中 | 渐进式架构，充分的集成测试 |
| 第三方依赖风险 | 中 | 中 | 多供应商策略，降级方案 |

#### 18.2.2 业务风险

| 风险 | 概率 | 影响 | 对策 |
|------|------|------|------|
| 需求变更频繁 | 高 | 中 | 敏捷开发，模块化设计 |
| 用户接受度低 | 中 | 高 | 用户调研，原型验证，迭代改进 |
| 竞品压力 | 中 | 中 | 差异化功能，快速迭代 |
| 合规要求变化 | 低 | 高 | 关注法规变化，预留合规接口 |

#### 18.2.3 资源风险

| 风险 | 概率 | 影响 | 对策 |
|------|------|------|------|
| 人员流失 | 中 | 高 | 知识文档化，代码规范，备份人员 |
| 开发进度延期 | 中 | 中 | 缓冲时间，关键路径管理 |
| 基础设施不足 | 低 | 中 | 容量规划，弹性扩展 |
| 预算超支 | 低 | 中 | 成本控制，分阶段投入 |

#### 18.2.4 运营风险

| 风险 | 概率 | 影响 | 对策 |
|------|------|------|------|
| 系统故障 | 中 | 高 | 高可用设计，故障演练，快速恢复 |
| 数据丢失 | 低 | 高 | 多重备份，灾备方案 |
| 安全攻击 | 中 | 高 | 安全防护，渗透测试，应急响应 |
| 运维复杂度高 | 中 | 中 | 自动化运维，监控告警，文档完善 |

### 18.3 成功标准

#### 18.3.1 技术指标
- **性能**: API响应时间P95 < 200ms，支持1000并发
- **可用性**: 系统可用性 > 99.9%，故障恢复时间 < 5分钟
- **扩展性**: 支持水平扩展，单集群支持10万工作流实例/天
- **安全性**: 通过安全测试，零安全漏洞

#### 18.3.2 业务指标
- **功能完整性**: 覆盖90%的业务场景
- **用户体验**: 用户满意度 > 4.0/5.0
- **生态丰富度**: 内置节点 > 20个，第三方插件 > 10个
- **文档完善度**: 文档覆盖率 > 95%

#### 18.3.3 运营指标
- **部署效率**: 从代码到生产 < 30分钟
- **故障处理**: 平均故障解决时间 < 2小时
- **监控覆盖**: 监控指标覆盖率 > 90%
- **成本控制**: 运营成本在预算范围内

---

## 总结

本设计方案提供了一套完整的通用可视化自动化编排平台解决方案，严格遵循了email-system项目的架构约束和技术规范。方案涵盖了从架构设计到实施落地的各个方面，具有以下特点：

1. **技术先进性**：采用DDD+Clean Architecture，支持插件化扩展，具备完整的可观测性
2. **业务完整性**：覆盖工作流编排、AI智能体、人群圈选、邮件发送等核心功能
3. **工程可落地**：提供详细的数据模型、API设计、测试策略和部署方案
4. **风险可控性**：识别关键风险点，提供相应的缓解策略

该方案可以作为email-system自动化编排平台的技术蓝图，支持分阶段实施和持续演进。
```
```
```
```

