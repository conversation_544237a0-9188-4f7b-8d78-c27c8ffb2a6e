# 资源权限系统设计需求提示词

## 背景描述

我正在设计一个多租户系统的资源权限管理模块，需要基于现有的表结构进行优化升级，而不是完全重新设计。现有系统已经运行了一段时间，包含用户、角色、权限、资源等核心表。

## 现有表结构

### 1. resource表（资源表）
- 包含所有资源类型：menu（菜单）、page（页面）、button（按钮）、api（接口）
- 字段包括：id, tenant_id, internal_app_id, name, display_name, description, resource_type, service_name, request_type, response_type, api_method, content_type, parent_id, path, icon, sort_order, is_system, is_public, public_level, assignable等
- 目前API相关字段（service_name, request_type, response_type, api_method, content_type）与页面/菜单/按钮字段混合在一起

### 2. permissions表（权限表）
- 按租户隔离的权限表
- 字段包括：id, tenant_id, internal_app_id, name, display_name, description, action, status, is_system, code, scope等
- 支持多租户，每个租户可以定制，也可以使用系统维护好的权限体系

### 3. resource_app_assignments表（资源应用关联表）
- 控制租户对资源的访问权限
- 字段包括：id, tenant_id, resource_id, internal_app_id, is_active, assigned_by, assigned_at, expires_at等

## 核心需求

### 1. 设计目标
- **基于现有表结构进行优化升级**，不破坏现有数据结构
- 实现**精确的资源权限控制**
- 支持**多租户隔离**
- 提供**灵活的权限分配机制**
- 确保**系统安全性和可扩展性**

### 2. 具体需求

#### 2.1 资源表优化
- 将API相关字段从resource表中分离出来
- 创建专门的api_resources表来管理API资源
- 为resource表添加permission_id字段，直接关联权限
- 保持现有数据不丢失，通过迁移脚本处理

#### 2.2 权限关联设计
- resource表和api_resources表都增加permission_id字段
- 每个资源可以直接关联到具体的权限
- 支持权限继承机制（子资源可以继承父资源的权限）
- 利用现有的resource_app_assignments表控制租户访问权限

#### 2.3 多租户支持
- 所有核心表都按租户隔离
- 权限表按租户设计，每个租户有独立的权限体系
- 通过resource_app_assignments表控制租户对资源的访问权限
- 支持不同租户对相同资源使用不同权限控制

#### 2.4 权限检查机制
- 实现精确的权限检查逻辑
- 支持API权限检查（基于路径和HTTP方法）
- 支持页面/菜单/按钮权限检查
- 支持权限继承和默认权限

## 技术要求

### 1. 数据库设计
- 使用MySQL数据库
- 支持外键约束保证数据一致性
- 合理的索引设计提升查询性能
- 支持软删除机制

### 2. 应用架构
- 使用Go语言开发
- 遵循Clean Architecture架构
- 使用GORM作为ORM框架
- 支持OpenTelemetry分布式追踪

### 3. 权限检查
- 实现中间件进行权限拦截
- 支持基于用户角色的权限检查
- 支持细粒度的数据权限控制
- 提供权限检查的API接口

## 输出要求

### 1. 设计文档
- 详细的数据库表结构设计
- 完整的升级方案和迁移脚本
- 权限检查逻辑的代码实现
- 升级步骤和验证方案

### 2. 技术实现
- SQL DDL语句（表结构修改、新建表）
- 数据迁移脚本
- Go代码实现（权限检查服务、中间件等）
- 配置和部署说明

### 3. 文档格式
- 使用Markdown格式
- 包含完整的SQL代码示例
- 包含Go代码示例
- 提供数据示例和测试用例

## 约束条件

### 1. 兼容性要求
- 不能删除现有表
- 不能破坏现有数据结构
- 必须支持现有数据的迁移
- 保持现有API接口的兼容性

### 2. 性能要求
- 权限检查响应时间 < 100ms
- 支持高并发访问
- 数据库查询优化
- 合理的缓存策略

### 3. 安全要求
- 严格的权限控制
- 防止权限提升攻击
- 支持审计日志
- 数据隔离和加密

## 期望输出

请基于以上需求，设计一个完整的资源权限系统升级方案，包括：

1. **数据库设计**：详细的表结构修改和新表设计
2. **升级方案**：分步骤的升级脚本和迁移方案
3. **代码实现**：权限检查服务和中间件的Go代码
4. **部署指南**：升级步骤和验证方案
5. **测试用例**：功能测试和性能测试方案

要求方案具有：
- **实用性**：能够直接在生产环境使用
- **可维护性**：代码结构清晰，易于理解和维护
- **可扩展性**：支持未来功能扩展
- **安全性**：满足企业级安全要求
- **性能**：满足高并发和大数据量要求
