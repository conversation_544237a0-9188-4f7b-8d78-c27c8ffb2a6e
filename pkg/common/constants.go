package common

// 系统级别通用常量，供各模块统一引用
const (
	// Authorization 认证头部字段
	HeaderAuthorization = "Authorization"
	// RequestID 全链路请求ID
	HeaderRequestID = "X-Request-Id"
	// TenantID 租户ID（仅限服务间传递，禁止对外暴露）
	ContextTenantID = "X-Tenant-Id"
	// AppId 应用ID
	HeaderAppId = "X-App-Id"
	// UserID 用户ID（如有必要）
	HeaderUserID = "X-User-Id"

	// BearerTokenPrefix Bearer Token 前缀
	BearerTokenPrefix = "Bearer "

	ContextKeyMetadata = "metadata"
)

// 安全校验需要传递的 Header 字段配置
var SecurityValidationHeaders = []string{
	"User-Agent",
	"X-Forwarded-For",
	"X-Real-IP",
	"X-Forwarded-Proto",
	"X-Forwarded-Host",
	"X-Original-URI",
	"Accept",
	"Accept-Language",
	"Accept-Encoding",
	"Referer",
	"Origin",
	HeaderAppId,
}

// 系统租户相关常量
const (
	// SystemTenantID 系统租户ID
	SystemTenantID = 1
	SystemAppId    = 1
	// SystemTenantCode 系统租户编码
	SystemTenantCode = "system"
	// SystemTenantName 系统租户名称
	SystemTenantName = "系统租户"
)
