package common_response

import (
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

// 错误码常量定义 - 避免循环依赖
const (
	CodeSuccess = 0 // 操作成功

	// 通用业务错误 (10001-19999)
	CodeValidationError    = 10001 // 参数验证失败
	CodeGeneralError       = 10002 // 通用错误
	CodeOperationFailed    = 10003 // 操作失败
	CodeInvalidRequest     = 10004 // 无效请求
	CodeMissingParam       = 10005 // 缺少必要参数
	CodeParamFormatError   = 10006 // 参数格式错误
	CodeParamValueError    = 10007 // 参数值错误
	CodeParamTypeError     = 10008 // 参数类型错误
	CodeParamLengthError   = 10009 // 参数长度错误
	CodeEmptyRequestBody   = 10010 // 请求体为空
	CodeInvalidJSON        = 10011 // JSON格式无效
	CodeInvalidXML         = 10012 // XML格式无效
	CodeInvalidFormat      = 10013 // 格式无效
	CodeBusinessLogicError = 10014 // 业务逻辑错误
	CodeWorkflowError      = 10015 // 工作流错误
	CodeStateError         = 10016 // 状态错误
	CodeVersionError       = 10017 // 版本错误
	CodeCompatibilityError = 10018 // 兼容性错误
	CodeDependencyError    = 10019 // 依赖错误

	// 认证授权错误 (20000-29999)
	CodeAuthError          = 20000 // 认证错误
	CodeUnauthenticated    = 20001 // 未认证
	CodeTokenExpired       = 20002 // 登录已过期
	CodeTokenInvalid       = 20003 // Token无效
	CodeTokenMalformed     = 20004 // Token格式错误
	CodeTokenNotFound      = 20005 // Token不存在
	CodeTokenRevoked       = 20006 // Token已撤销
	CodeLoginAbnormal      = 20007 // 登录状态异常
	CodeLoginFailed        = 20008 // 登录失败
	CodeLoginBlocked       = 20009 // 登录被阻止
	CodePermissionDenied   = 20010 // 权限不足
	CodeAccessForbidden    = 20011 // 访问被禁止
	CodeAccountLocked      = 20012 // 账户被锁定
	CodeAccountDisabled    = 20013 // 账户被禁用
	CodeAccountExpired     = 20014 // 账户已过期
	CodeAccountSuspended   = 20015 // 账户已暂停
	CodeInvalidCredentials = 20016 // 凭证无效
	CodeCredentialsExpired = 20017 // 凭证已过期
	CodeMFARequired        = 20018 // 需要多因素认证
	CodeMFAInvalid         = 20019 // 多因素认证失败
	CodeMFAExpired         = 20020 // 多因素认证已过期
	CodeSessionExpired     = 20021 // 会话已过期
	CodeSessionInvalid     = 20022 // 会话无效
	CodeSessionNotFound    = 20023 // 会话不存在
	CodeIPRestricted       = 20024 // IP受限
	CodeDeviceNotTrusted   = 20025 // 设备不受信任
	CodeLocationRestricted = 20026 // 地理位置受限
	CodeTimeRestricted     = 20027 // 时间受限
	CodeConcurrentLogin    = 20028 // 并发登录限制
	CodePasswordExpired    = 20029 // 密码已过期
	CodePasswordWeak       = 20030 // 密码强度不足

	// 数据验证错误 (30000-39999)
	CodeDataValidationError     = 30000 // 数据验证失败
	CodeEmailFormatError        = 30001 // 邮箱格式错误
	CodePhoneFormatError        = 30002 // 手机号格式错误
	CodePasswordFormatError     = 30003 // 密码格式错误
	CodeUsernameFormatError     = 30004 // 用户名格式错误
	CodeDateFormatError         = 30005 // 日期格式错误
	CodeTimeFormatError         = 30006 // 时间格式错误
	CodeURLFormatError          = 30007 // URL格式错误
	CodeIPFormatError           = 30008 // IP格式错误
	CodeRegexValidationError    = 30009 // 正则验证失败
	CodeRangeValidationError    = 30010 // 范围验证失败
	CodeLengthValidationError   = 30011 // 长度验证失败
	CodeUniqueValidationError   = 30012 // 唯一性验证失败
	CodeRequiredValidationError = 30013 // 必填验证失败
	CodeCustomValidationError   = 30014 // 自定义验证失败
	CodeChecksumError           = 30015 // 校验和错误
	CodeHashValidationError     = 30016 // 哈希验证失败
	CodeSignatureError          = 30017 // 签名错误
	CodeParsingError            = 30024 // 解析错误

	// 资源操作错误 (40000-49999)
	CodeResourceNotFound         = 40001 // 资源不存在
	CodeResourceExists           = 40002 // 资源已存在
	CodeResourceConflict         = 40003 // 资源冲突
	CodeResourceLocked           = 40004 // 资源被锁定
	CodeResourceInUse            = 40006 // 资源正在使用中
	CodeResourceExpired          = 40007 // 资源已过期
	CodeResourceUnavailable      = 40008 // 资源不可用
	CodeResourceFull             = 40009 // 资源已满
	CodeOperationNotAllowed      = 40011 // 操作不被允许
	CodeOperationTimeout         = 40012 // 操作超时
	CodeRateLimitExceeded        = 40018 // 速率限制超限
	CodeConcurrencyLimitExceeded = 40019 // 并发限制超限
	CodeSizeLimitExceeded        = 40020 // 大小限制超限
	CodeCountLimitExceeded       = 40021 // 数量限制超限
	CodeTimeLimitExceeded        = 40022 // 时间限制超限

	// 系统错误 (50000-59999)
	CodeSystemError              = 50000 // 系统错误
	CodeInternalError            = 50001 // 服务器内部错误
	CodeDatabaseError            = 50002 // 数据库错误
	CodeDatabaseConnectionError  = 50003 // 数据库连接错误
	CodeDatabaseQueryError       = 50004 // 数据库查询错误
	CodeDatabaseTransactionError = 50005 // 数据库事务错误
	CodeDatabaseDeadlock         = 50006 // 数据库死锁
	CodeDatabaseTimeout          = 50007 // 数据库超时
	CodeCacheError               = 50008 // 缓存错误
	CodeServiceUnavailable       = 50018 // 服务不可用
	CodeServiceTimeout           = 50019 // 服务超时
	CodeServiceOverload          = 50020 // 服务过载
	CodeGatewayTimeout           = 50021 // 网关超时
	CodeGatewayError             = 50022 // 网关错误
	CodeThirdPartyError          = 50015 // 第三方服务错误
	CodeThirdPartyTimeout        = 50016 // 第三方服务超时
	CodeThirdPartyUnavailable    = 50017 // 第三方服务不可用
)

// ErrorMessages 错误码对应的中文消息映射
var ErrorMessages = map[int]string{
	// 成功码
	CodeSuccess: "操作成功",

	// 通用业务错误 (10001-19999)
	CodeValidationError:    "参数验证失败",
	CodeGeneralError:       "通用错误",
	CodeOperationFailed:    "操作失败",
	CodeInvalidRequest:     "无效请求",
	CodeMissingParam:       "缺少必要参数",
	CodeParamFormatError:   "参数格式错误",
	CodeParamValueError:    "参数值错误",
	CodeParamTypeError:     "参数类型错误",
	CodeParamLengthError:   "参数长度错误",
	CodeEmptyRequestBody:   "请求体为空",
	CodeInvalidJSON:        "JSON格式无效",
	CodeInvalidXML:         "XML格式无效",
	CodeInvalidFormat:      "格式无效",
	CodeBusinessLogicError: "业务逻辑错误",
	CodeWorkflowError:      "工作流错误",
	CodeStateError:         "状态错误",
	CodeVersionError:       "版本错误",
	CodeCompatibilityError: "兼容性错误",
	CodeDependencyError:    "依赖错误",

	// 认证授权错误 (20000-29999)
	CodeAuthError:          "认证错误",
	CodeUnauthenticated:    "未认证",
	CodeTokenExpired:       "登录已过期",
	CodeTokenInvalid:       "Token无效",
	CodeTokenMalformed:     "Token格式错误",
	CodeTokenNotFound:      "Token不存在",
	CodeTokenRevoked:       "Token已撤销",
	CodeLoginAbnormal:      "登录状态异常",
	CodeLoginFailed:        "登录失败",
	CodeLoginBlocked:       "登录被阻止",
	CodePermissionDenied:   "权限不足",
	CodeAccessForbidden:    "访问被禁止",
	CodeAccountLocked:      "账户被锁定",
	CodeAccountDisabled:    "账户被禁用",
	CodeAccountExpired:     "账户已过期",
	CodeAccountSuspended:   "账户已暂停",
	CodeInvalidCredentials: "凭证无效",
	CodeCredentialsExpired: "凭证已过期",
	CodeMFARequired:        "需要多因素认证",
	CodeMFAInvalid:         "多因素认证失败",
	CodeMFAExpired:         "多因素认证已过期",
	CodeSessionExpired:     "会话已过期",
	CodeSessionInvalid:     "会话无效",
	CodeSessionNotFound:    "会话不存在",
	CodeIPRestricted:       "IP受限",
	CodeDeviceNotTrusted:   "设备不受信任",
	CodeLocationRestricted: "地理位置受限",
	CodeTimeRestricted:     "时间受限",
	CodeConcurrentLogin:    "并发登录限制",
	CodePasswordExpired:    "密码已过期",
	CodePasswordWeak:       "密码强度不足",

	// 数据验证错误 (30000-39999)
	CodeDataValidationError:     "数据验证失败",
	CodeEmailFormatError:        "邮箱格式错误",
	CodePhoneFormatError:        "手机号格式错误",
	CodePasswordFormatError:     "密码格式错误",
	CodeUsernameFormatError:     "用户名格式错误",
	CodeDateFormatError:         "日期格式错误",
	CodeTimeFormatError:         "时间格式错误",
	CodeURLFormatError:          "URL格式错误",
	CodeIPFormatError:           "IP格式错误",
	CodeRegexValidationError:    "正则验证失败",
	CodeRangeValidationError:    "范围验证失败",
	CodeLengthValidationError:   "长度验证失败",
	CodeUniqueValidationError:   "唯一性验证失败",
	CodeRequiredValidationError: "必填验证失败",
	CodeCustomValidationError:   "自定义验证失败",
	CodeChecksumError:           "校验和错误",
	CodeHashValidationError:     "哈希验证失败",
	CodeSignatureError:          "签名错误",
	CodeParsingError:            "解析错误",

	// 资源操作错误 (40000-49999)
	CodeResourceNotFound:         "资源不存在",
	CodeResourceExists:           "资源已存在",
	CodeResourceConflict:         "资源冲突",
	CodeResourceLocked:           "资源被锁定",
	CodeResourceInUse:            "资源正在使用中",
	CodeResourceExpired:          "资源已过期",
	CodeResourceUnavailable:      "资源不可用",
	CodeResourceFull:             "资源已满",
	CodeOperationNotAllowed:      "操作不被允许",
	CodeOperationTimeout:         "操作超时",
	CodeRateLimitExceeded:        "速率限制超限",
	CodeConcurrencyLimitExceeded: "并发限制超限",
	CodeSizeLimitExceeded:        "大小限制超限",
	CodeCountLimitExceeded:       "数量限制超限",
	CodeTimeLimitExceeded:        "时间限制超限",

	// 系统错误 (50000-59999)
	CodeSystemError:              "系统错误",
	CodeInternalError:            "服务器内部错误",
	CodeDatabaseError:            "数据库错误",
	CodeDatabaseConnectionError:  "数据库连接错误",
	CodeDatabaseQueryError:       "数据库查询错误",
	CodeDatabaseTransactionError: "数据库事务错误",
	CodeDatabaseDeadlock:         "数据库死锁",
	CodeDatabaseTimeout:          "数据库超时",
	CodeCacheError:               "缓存错误",
	CodeServiceUnavailable:       "服务不可用",
	CodeServiceTimeout:           "服务超时",
	CodeServiceOverload:          "服务过载",
	CodeGatewayTimeout:           "网关超时",
	CodeGatewayError:             "网关错误",
	CodeThirdPartyError:          "第三方服务错误",
	CodeThirdPartyTimeout:        "第三方服务超时",
	CodeThirdPartyUnavailable:    "第三方服务不可用",
}

// GetErrorMessage 根据错误码获取对应的中文错误消息
func GetErrorMessage(code int) string {
	if message, exists := ErrorMessages[code]; exists {
		return message
	}
	return "未知错误"
}

// Response 统一响应结构
type Response struct {
	Code       int         `json:"code"`                 // 业务状态码
	Message    string      `json:"message"`              // 响应消息
	Data       interface{} `json:"data,omitempty"`       // 响应数据
	Meta       *Meta       `json:"meta,omitempty"`       // 元数据信息
	Validation interface{} `json:"validation,omitempty"` // 安全校验信息（如验证码等）
}

// Meta 元数据信息
type Meta struct {
	RequestID  string                 `json:"request_id,omitempty"` // 请求ID
	Timestamp  int64                  `json:"timestamp"`            // 时间戳
	Pagination *Pagination            `json:"pagination,omitempty"` // 分页信息
	Extra      map[string]interface{} `json:"extra,omitempty"`      // 额外信息
}

// Pagination 分页信息
type Pagination struct {
	Page       int         `json:"page"`                  // 当前页码
	Size       int         `json:"size"`                  // 每页大小
	Total      int64       `json:"total"`                 // 总记录数
	Pages      int         `json:"pages"`                 // 总页数
	HasNext    bool        `json:"has_next"`              // 是否有下一页
	HasPrev    bool        `json:"has_prev"`              // 是否有上一页
	NextCursor interface{} `json:"next_cursor,omitempty"` // 下一页游标
}

// ValidationError 验证错误详情
type ValidationError struct {
	Field   string      `json:"field"`   // 字段名
	Message string      `json:"message"` // 错误消息
	Value   interface{} `json:"value"`   // 字段值
}

// ErrorDetail 错误详情结构
type ErrorDetail struct {
	Code    int               `json:"code"`
	Message string            `json:"message"`
	Data    interface{}       `json:"data,omitempty"`
	Errors  []ValidationError `json:"errors,omitempty"`
	Meta    *Meta             `json:"meta,omitempty"`
}

// HTTPStatusMapping HTTP状态码映射
var HTTPStatusMapping = map[int]int{
	// 成功
	CodeSuccess: http.StatusOK,

	// 认证授权错误 (20000-29999) -> 401/403
	CodeAuthError:          http.StatusUnauthorized,
	CodeUnauthenticated:    http.StatusUnauthorized,
	CodeTokenExpired:       http.StatusUnauthorized,
	CodeTokenInvalid:       http.StatusUnauthorized,
	CodeTokenMalformed:     http.StatusUnauthorized,
	CodeTokenNotFound:      http.StatusUnauthorized,
	CodeTokenRevoked:       http.StatusUnauthorized,
	CodeLoginAbnormal:      http.StatusUnauthorized,
	CodeLoginFailed:        http.StatusUnauthorized,
	CodeLoginBlocked:       http.StatusUnauthorized,
	CodePermissionDenied:   http.StatusForbidden,
	CodeAccessForbidden:    http.StatusForbidden,
	CodeAccountLocked:      http.StatusForbidden,
	CodeAccountDisabled:    http.StatusForbidden,
	CodeAccountExpired:     http.StatusForbidden,
	CodeAccountSuspended:   http.StatusForbidden,
	CodeInvalidCredentials: http.StatusUnauthorized,
	CodeCredentialsExpired: http.StatusUnauthorized,
	CodeMFARequired:        http.StatusUnauthorized,
	CodeMFAInvalid:         http.StatusUnauthorized,
	CodeMFAExpired:         http.StatusUnauthorized,
	CodeSessionExpired:     http.StatusUnauthorized,
	CodeSessionInvalid:     http.StatusUnauthorized,
	CodeSessionNotFound:    http.StatusUnauthorized,
	CodeIPRestricted:       http.StatusForbidden,
	CodeDeviceNotTrusted:   http.StatusForbidden,
	CodeLocationRestricted: http.StatusForbidden,
	CodeTimeRestricted:     http.StatusForbidden,
	CodeConcurrentLogin:    http.StatusForbidden,
	CodePasswordExpired:    http.StatusUnauthorized,
}

// GetHTTPStatus 根据错误码获取对应的HTTP状态码
func GetHTTPStatus(code int) int {
	if status, exists := HTTPStatusMapping[code]; exists {
		return status
	}
	// 默认返回200
	return http.StatusOK
}

// buildMeta 构建元数据
func buildMeta(c *gin.Context) *Meta {
	meta := &Meta{
		Timestamp: time.Now().Unix(),
	}

	// 获取请求ID
	if requestID := c.GetHeader("X-Request-ID"); requestID != "" {
		meta.RequestID = requestID
	} else if requestID := c.GetString("request_id"); requestID != "" {
		meta.RequestID = requestID
	}

	return meta
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	response := Response{
		Code:    CodeSuccess,
		Message: GetErrorMessage(CodeSuccess),
		Data:    data,
		Meta:    buildMeta(c),
	}
	c.JSON(http.StatusOK, response)
}

// Created 创建成功响应
func Created(c *gin.Context, data interface{}) {
	response := Response{
		Code:    CodeSuccess,
		Message: "创建成功",
		Data:    data,
		Meta:    buildMeta(c),
	}
	c.JSON(http.StatusCreated, response)
}

// Updated 更新成功响应
func Updated(c *gin.Context, data interface{}) {
	response := Response{
		Code:    CodeSuccess,
		Message: "更新成功",
		Data:    data,
		Meta:    buildMeta(c),
	}
	c.JSON(http.StatusOK, response)
}

// Deleted 删除成功响应
func Deleted(c *gin.Context) {
	response := Response{
		Code:    CodeSuccess,
		Message: "删除成功",
		Meta:    buildMeta(c),
	}
	c.JSON(http.StatusOK, response)
}

// Paginated 分页响应
func Paginated(c *gin.Context, data interface{}, page, size int, total int64) {
	// 防止除零错误，设置默认值
	if size <= 0 {
		size = 20 // 默认每页20条
	}
	if page <= 0 {
		page = 1 // 默认第1页
	}

	pages := int((total + int64(size) - 1) / int64(size))

	pagination := &Pagination{
		Page:    page,
		Size:    size,
		Total:   total,
		Pages:   pages,
		HasNext: page < pages,
		HasPrev: page > 1,
	}

	meta := buildMeta(c)
	meta.Pagination = pagination

	response := Response{
		Code:    CodeSuccess,
		Message: GetErrorMessage(CodeSuccess),
		Data:    data,
		Meta:    meta,
	}
	c.JSON(http.StatusOK, response)
}

// Error 通用错误响应
func Error(c *gin.Context, code int, message ...string) {
	if c.IsAborted() {
		return
	}
	errorMsg := GetErrorMessage(code)
	if len(message) > 0 && message[0] != "" {
		errorMsg = message[0]
	}

	response := Response{
		Code:    code,
		Message: errorMsg,
		Meta:    buildMeta(c),
	}

	httpStatus := GetHTTPStatus(code)
	c.JSON(httpStatus, response)
}

// ErrorWithData 带数据的错误响应
func ErrorWithData(c *gin.Context, code int, data interface{}, message ...string) {
	if c.IsAborted() {
		return
	}
	errorMsg := GetErrorMessage(code)
	if len(message) > 0 && message[0] != "" {
		errorMsg = message[0]
	}

	response := Response{
		Code:    code,
		Message: errorMsg,
		Data:    data,
		Meta:    buildMeta(c),
	}
	c.JSON(http.StatusOK, response)
}

// ValidationErrorResponse 验证错误响应
func ValidationErrorResponse(c *gin.Context, errors []ValidationError, message ...string) {
	if c.IsAborted() {
		return
	}
	errorMsg := GetErrorMessage(CodeValidationError)
	if len(message) > 0 && message[0] != "" {
		errorMsg = message[0]
	}

	response := ErrorDetail{
		Code:    CodeValidationError,
		Message: errorMsg,
		Errors:  errors,
		Meta:    buildMeta(c),
	}
	// DO NOT MODIFY
	c.JSON(http.StatusOK, response)
}

// InternalError 内部错误响应（隐藏系统详情）
func InternalError(c *gin.Context, err error) {
	if c.IsAborted() {
		return
	}
	// 记录详细错误到日志（这里可以集成日志系统）
	// logger.Error("Internal error", "error", err, "request_id", c.GetString("request_id"))
	// 处理字段验证错误（如唯一性约束）
	var vErr *FieldValidationError
	if err != nil && errors.As(err, &vErr) { // 兼容验证错误
		FieldError(c, vErr.Field, vErr.Message)
		return
	}
	// 返回用户友好的错误消息
	response := Response{
		Code:    CodeInternalError,
		Message: GetErrorMessage(CodeInternalError),
		Meta:    buildMeta(c),
	}

	c.JSON(http.StatusOK, response)
}

func TimeoutError(c *gin.Context) {
	if c.IsAborted() {
		return
	}
	// 返回用户友好的错误消息
	response := Response{
		Code:    CodeGatewayTimeout,
		Message: GetErrorMessage(CodeGatewayTimeout),
		Meta:    buildMeta(c),
	}
	c.JSON(http.StatusGatewayTimeout, response)
}

// 便捷方法

// BadRequest 400错误
func BadRequest(c *gin.Context, message ...string) {
	Error(c, CodeInvalidRequest, message...)
}

// Unauthorized 401错误
func Unauthorized(c *gin.Context, message ...string) {
	if c.IsAborted() {
		return
	}
	errorMsg := GetErrorMessage(CodeInvalidCredentials)
	if len(message) > 0 && message[0] != "" {
		errorMsg = message[0]
	}
	response := Response{
		Code:    CodeInvalidCredentials,
		Message: errorMsg,
		Meta:    buildMeta(c),
	}
	if c.IsAborted() {
		return
	}
	c.JSON(http.StatusUnauthorized, response)
}

// Forbidden 403错误
func Forbidden(c *gin.Context, message ...string) {
	if c.IsAborted() {
		return
	}
	errorMsg := GetErrorMessage(CodePermissionDenied)
	if len(message) > 0 && message[0] != "" {
		errorMsg = message[0]
	}
	response := Response{
		Code:    CodePermissionDenied,
		Message: errorMsg,
		Meta:    buildMeta(c),
	}
	if c.IsAborted() {
		return
	}
	c.JSON(http.StatusForbidden, response)
}

// NotFound 404错误
func NotFound(c *gin.Context, resource ...string) {
	message := GetErrorMessage(CodeResourceNotFound)
	if len(resource) > 0 {
		message = fmt.Sprintf("%s不存在", resource[0])
	}
	Error(c, CodeResourceNotFound, message)
}

// Conflict 409错误
func Conflict(c *gin.Context, message ...string) {
	Error(c, CodeResourceConflict, message...)
}

// TooManyRequests 429错误
func TooManyRequests(c *gin.Context, retryAfter int) {
	c.Header("Retry-After", fmt.Sprintf("%d", retryAfter))
	Error(c, CodeRateLimitExceeded)
}

// ServiceUnavailable 503错误
func ServiceUnavailable(c *gin.Context, message ...string) {
	Error(c, CodeServiceUnavailable, message...)
}

// BuildMeta 构建元数据 - 导出版本，供其他模块使用
func BuildMeta(c *gin.Context) *Meta {
	return buildMeta(c)
}

// FieldValidationError 字段验证错误 - 兼容旧版本
type FieldValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// Error 实现error接口
func (e *FieldValidationError) Error() string {
	return fmt.Sprintf("%s: %s", e.Field, e.Message)
}

// GinValidationError 将gin验证错误转换为系统统一错误结构
// 直接从gin错误信息中提取JSON字段名和错误消息
func GinValidationError(c *gin.Context, err error) {
	if c.IsAborted() {
		return
	}
	var validationErrors []ValidationError

	// 处理go-playground/validator的验证错误
	var validatorErrors validator.ValidationErrors
	if errors.As(err, &validatorErrors) {
		for _, fieldError := range validatorErrors {
			validationErrors = append(validationErrors, ValidationError{
				Field:   getJSONFieldNameFromTag(fieldError),
				Message: getValidationMessage(fieldError),
			})
		}
	}

	// 处理JSON绑定错误（如数值溢出、格式错误等）
	if len(validationErrors) == 0 {
		validationErrors = parseGinBindingError(err.Error())
	}

	// 返回标准验证错误响应
	ValidationErrorResponse(c, validationErrors)
}

// getJSONFieldNameFromTag 从validator.FieldError中提取JSON字段名
func getJSONFieldNameFromTag(fieldError validator.FieldError) string {
	// 优先使用JSON标签名
	if jsonTag := fieldError.Tag(); jsonTag != "" {
		// 从namespace中提取字段名，格式如: "CreateUserRequest.Username"
		namespace := fieldError.Namespace()
		if dotIndex := strings.LastIndex(namespace, "."); dotIndex != -1 {
			fieldName := namespace[dotIndex+1:]
			// 转换为snake_case格式（常见的JSON命名约定）
			return toSnakeCase(fieldName)
		}
	}

	// 降级处理：使用字段名并转换为snake_case
	return toSnakeCase(fieldError.Field())
}

// getValidationMessage 根据validator.FieldError生成用户友好的错误消息
func getValidationMessage(fieldError validator.FieldError) string {
	tag := fieldError.Tag()
	param := fieldError.Param()

	switch tag {
	case "required":
		return "此字段为必填项"
	case "min":
		return fmt.Sprintf("字段长度不能少于%s个字符", param)
	case "max":
		return fmt.Sprintf("字段长度不能超过%s个字符", param)
	case "len":
		return fmt.Sprintf("字段长度必须为%s个字符", param)
	case "email":
		return "请输入有效的邮箱地址"
	case "url":
		return "请输入有效的URL地址"
	case "numeric":
		return "此字段必须为数字"
	case "alpha":
		return "此字段只能包含字母"
	case "alphanum":
		return "此字段只能包含字母和数字"
	case "oneof":
		return fmt.Sprintf("此字段的值必须是以下之一: %s", param)
	case "gte":
		return fmt.Sprintf("此字段的值必须大于等于%s", param)
	case "lte":
		return fmt.Sprintf("此字段的值必须小于等于%s", param)
	case "gt":
		return fmt.Sprintf("此字段的值必须大于%s", param)
	case "lt":
		return fmt.Sprintf("此字段的值必须小于%s", param)
	case "unique":
		return "此字段的值必须唯一"
	default:
		return "字段验证失败"
	}
}

// parseGinBindingError 解析gin绑定错误（如JSON格式错误）
func parseGinBindingError(errMsg string) []ValidationError {
	var errors []ValidationError

	// 处理数值溢出错误
	if strings.Contains(errMsg, "cannot unmarshal number") && strings.Contains(errMsg, "into Go struct field") {
		// 提取字段名，格式如: "cannot unmarshal number 9223372036854776000 into Go struct field UpdateSequenceWithID.maxValue of type int64"
		fieldName := extractFieldNameFromUnmarshalError(errMsg)
		errors = append(errors, ValidationError{
			Field:   fieldName,
			Message: "数值超出允许范围，请输入有效的数值",
		})
	} else if strings.Contains(errMsg, "invalid character") ||
		strings.Contains(errMsg, "unexpected end of JSON input") ||
		strings.Contains(errMsg, "cannot unmarshal") {
		errors = append(errors, ValidationError{
			Field:   "request",
			Message: "请求数据格式错误",
		})
	} else {
		// 其他未知错误
		errors = append(errors, ValidationError{
			Field:   "request",
			Message: "请求参数格式错误",
		})
	}

	return errors
}

// extractFieldNameFromUnmarshalError 从unmarshal错误中提取字段名
func extractFieldNameFromUnmarshalError(errMsg string) string {
	// 错误格式: "cannot unmarshal number 9223372036854776000 into Go struct field UpdateSequenceWithID.maxValue of type int64"
	// 提取 "UpdateSequenceWithID.maxValue" 中的 "maxValue"

	// 查找 "into Go struct field" 后面的部分
	startIndex := strings.Index(errMsg, "into Go struct field ")
	if startIndex == -1 {
		return "request"
	}

	startIndex += len("into Go struct field ")
	endIndex := strings.Index(errMsg[startIndex:], " of type")
	if endIndex == -1 {
		return "request"
	}

	fieldPath := errMsg[startIndex : startIndex+endIndex]

	// 提取最后一个点后面的字段名
	lastDotIndex := strings.LastIndex(fieldPath, ".")
	if lastDotIndex == -1 {
		return fieldPath
	}

	fieldName := fieldPath[lastDotIndex+1:]
	return fieldName
}

// toSnakeCase 将驼峰命名转换为蛇形命名
func toSnakeCase(str string) string {
	// 使用正则表达式在大写字母前插入下划线
	re := regexp.MustCompile("([a-z0-9])([A-Z])")
	snake := re.ReplaceAllString(str, "${1}_${2}")
	return strings.ToLower(snake)
}

// FieldError 单个字段错误响应
func FieldError(c *gin.Context, field, message string, value ...interface{}) {
	var val interface{}
	if len(value) > 0 {
		val = value[0]
	}

	errors := []ValidationError{
		{
			Field:   field,
			Message: message,
			Value:   val,
		},
	}
	ValidationErrorResponse(c, errors, "参数验证失败")
}

// UniqueConstraintError 唯一性约束错误响应
func UniqueConstraintError(c *gin.Context, field string, value interface{}) {
	errors := []ValidationError{
		{
			Field:   field,
			Message: field + "已存在",
			Value:   value,
		},
	}
	ValidationErrorResponse(c, errors, "数据验证失败")
}

// BusinessError 业务错误响应
func BusinessError(c *gin.Context, code int, message string, data ...interface{}) {
	if c.IsAborted() {
		return
	}
	response := Response{
		Code:    code,
		Message: message,
		Meta:    buildMeta(c),
	}

	if len(data) > 0 {
		response.Data = data[0]
	}

	httpStatus := GetHTTPStatus(code)
	c.JSON(httpStatus, response)
}

// ValidationRequired 返回需要安全校验（如验证码）时的响应
func ValidationRequired(c *gin.Context, code int, message string, validation interface{}) {
	if c.IsAborted() {
		return
	}
	resp := Response{
		Code:       code,
		Message:    message,
		Data:       nil,
		Meta:       buildMeta(c),
		Validation: validation,
	}
	c.JSON(http.StatusOK, resp)
}
