package resource

import (
	"context"
	"net/http"
)

// Resource 资源接口 - 共享接口，避免直接依赖internal包
type Resource interface {
	// GetID 获取资源ID
	GetID() int64
	
	// GetName 获取资源名称
	GetName() string
	
	// GetDisplayName 获取显示名称
	GetDisplayName() string
	
	// GetResourceType 获取资源类型
	GetResourceType() string
	
	// GetPath 获取路径
	GetPath() string
	
	// GetAPIMethod 获取API方法
	GetAPIMethod() string
	
	// GetTenantID 获取租户ID
	GetTenantID() int64
	
	// GetInternalAppID 获取内部应用ID
	GetInternalAppID() int64
	
	// IsAPI 是否为API资源
	IsAPI() bool
	
	// IsPublicResource 是否为公开资源
	IsPublicResource() bool
	
	// CheckPublicAccess 检查公开访问权限
	CheckPublicAccess(ctx context.Context, req *http.Request) bool
}