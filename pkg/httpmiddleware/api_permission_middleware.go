package httpmiddleware

import (
	"github.com/gin-gonic/gin"
	commonResource "gitee.com/heiyee/platforms/pkg/common/resource"
	commonResponse "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/httpmiddleware/permission"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
)

// APIPermissionConfig API权限拦截配置
type APIPermissionConfig struct {
	// 启用权限检查
	Enabled bool
	
	// 跳过权限检查的路径列表
	SkipPaths []string
	
	// 跳过权限检查的路径前缀列表
	SkipPrefixes []string
	
	// 资源未找到时的处理策略
	ResourceNotFoundStrategy string // "allow", "deny", "log_and_allow"
	
	// 权限不足时的处理策略
	InsufficientPermissionStrategy string // "deny", "log_and_deny"
	
	// 记录权限检查详情
	LogPermissionDetails bool
	
	// 调试模式
	DebugMode bool
	
	// 组件依赖
	APIResourceMatcher   permission.APIResourceMatcher
	PermissionChecker   permission.PermissionChecker
	Logger              logiface.Logger
}

// DefaultAPIPermissionConfig 默认配置
func DefaultAPIPermissionConfig() *APIPermissionConfig {
	return &APIPermissionConfig{
		Enabled:                       true,
		SkipPaths:                     []string{"/health", "/metrics", "/favicon.ico"},
		SkipPrefixes:                  []string{"/static/", "/assets/", "/public/"},
		ResourceNotFoundStrategy:      "log_and_allow", // 默认允许未配置的API访问
		InsufficientPermissionStrategy: "deny",          // 默认拒绝权限不足的访问
		LogPermissionDetails:          true,
		DebugMode:                     false,
	}
}

// shouldSkipPermissionCheck 检查是否应跳过权限检查
func shouldSkipPermissionCheck(c *gin.Context, config *APIPermissionConfig) bool {
	path := c.Request.URL.Path
	
	// 检查精确路径匹配
	for _, skipPath := range config.SkipPaths {
		if path == skipPath {
			return true
		}
	}
	
	// 检查路径前缀匹配
	for _, prefix := range config.SkipPrefixes {
		if len(path) >= len(prefix) && path[:len(prefix)] == prefix {
			return true
		}
	}
	
	return false
}

// handleResourceNotFound 处理资源未找到
func handleResourceNotFound(c *gin.Context, config *APIPermissionConfig) {
	ctx := c.Request.Context()
	
	switch config.ResourceNotFoundStrategy {
	case "allow":
		config.Logger.Debug(ctx, "API资源未找到，允许访问",
			logiface.String("path", c.Request.URL.Path),
			logiface.String("method", c.Request.Method))
		c.Next()
		
	case "deny":
		config.Logger.Warn(ctx, "API资源未找到，拒绝访问",
			logiface.String("path", c.Request.URL.Path),
			logiface.String("method", c.Request.Method))
		commonResponse.NotFound(c, "API资源未找到")
		
	case "log_and_allow":
		fallthrough
	default:
		config.Logger.Info(ctx, "API资源未配置，允许访问",
			logiface.String("path", c.Request.URL.Path),
			logiface.String("method", c.Request.Method))
		c.Next()
	}
}

// handleInsufficientPermission 处理权限不足
func handleInsufficientPermission(c *gin.Context, config *APIPermissionConfig, reason string) {
	ctx := c.Request.Context()
	
	switch config.InsufficientPermissionStrategy {
	case "log_and_deny":
		fallthrough
	case "deny":
		fallthrough
	default:
		if config.LogPermissionDetails {
			config.Logger.Warn(ctx, "API权限检查失败",
				logiface.String("path", c.Request.URL.Path),
				logiface.String("method", c.Request.Method),
				logiface.String("reason", reason))
		}
		
		commonResponse.Forbidden(c, "权限不足")
	}
}

// APIPermissionInterceptor 基于URL的权限拦截中间件
func APIPermissionInterceptor(config *APIPermissionConfig) gin.HandlerFunc {
	if config == nil {
		config = DefaultAPIPermissionConfig()
	}
	
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		
		// 1. 检查是否启用权限检查
		if !config.Enabled {
			c.Next()
			return
		}
		
		// 2. 检查是否需要跳过权限检查
		if shouldSkipPermissionCheck(c, config) {
			if config.DebugMode {
				config.Logger.Debug(ctx, "跳过权限检查",
					logiface.String("path", c.Request.URL.Path),
					logiface.String("method", c.Request.Method))
			}
			c.Next()
			return
		}
		
		// 3. 获取用户和应用信息
		userInfo, hasUser := usercontext.GetUserInfo(ctx)
		appInfo, hasApp := usercontext.GetAppInfo(ctx)
		
		if !hasApp || appInfo == nil {
			config.Logger.Warn(ctx, "应用上下文信息缺失",
				logiface.String("path", c.Request.URL.Path),
				logiface.String("method", c.Request.Method))
			commonResponse.BadRequest(c, "应用上下文信息缺失")
			return
		}
		
		// 4. 匹配 API 资源
		apiResource, err := config.APIResourceMatcher.MatchAPIResource(
			ctx, c.Request.URL.Path, c.Request.Method,
			appInfo.TenantID, appInfo.InternalAppId)
		
		if err != nil {
			config.Logger.Error(ctx, "API资源匹配失败",
				logiface.Error(err),
				logiface.String("path", c.Request.URL.Path),
				logiface.String("method", c.Request.Method))
			commonResponse.InternalError(c, nil)
			return
		}
		
		if apiResource == nil {
			handleResourceNotFound(c, config)
			return
		}
		
		// 5. 公开访问检查
		if apiResource.IsPublicResource() && apiResource.CheckPublicAccess(ctx, c.Request) {
			if config.DebugMode {
				config.Logger.Debug(ctx, "API资源为公开访问",
					logiface.Int64("api_resource_id", apiResource.GetID()),
					logiface.String("api_name", apiResource.GetName()))
			}
			c.Set("api_resource", apiResource)
			c.Next()
			return
		}
		
		// 6. 认证检查
		if !hasUser || userInfo == nil {
			config.Logger.Info(ctx, "用户未认证",
				logiface.String("path", c.Request.URL.Path),
				logiface.String("method", c.Request.Method))
			commonResponse.Unauthorized(c, "需要登录")
			return
		}
		
		// 7. 权限检查 - 简化版本，直接基于资源进行检查
		hasPermission, err := config.PermissionChecker.CheckResourceAccess(
			ctx, userInfo.UserID, apiResource)
		
		if err != nil {
			config.Logger.Error(ctx, "权限检查过程失败",
				logiface.Error(err),
				logiface.Int64("user_id", userInfo.UserID),
				logiface.Int64("api_resource_id", apiResource.GetID()))
			commonResponse.InternalError(c, nil)
			return
		}
		
		if !hasPermission {
			handleInsufficientPermission(c, config, "用户无权限访问此资源")
			return
		}
		
		// 8. 权限检查通过，记录成功日志
		if config.LogPermissionDetails {
			config.Logger.Info(ctx, "API权限检查通过",
				logiface.Int64("user_id", userInfo.UserID),
				logiface.Int64("api_resource_id", apiResource.GetID()),
				logiface.String("api_name", apiResource.GetName()),
				logiface.String("path", c.Request.URL.Path),
				logiface.String("method", c.Request.Method))
		}
		
		// 9. 将资源信息传递给后续处理器
		c.Set("api_resource", apiResource)
		
		c.Next()
	}
}

// PermissionMiddlewareConfig 权限中间件配置
type PermissionMiddlewareConfig struct {
	// API权限配置
	APIPermissionConfig *APIPermissionConfig
	
	// 是否启用权限中间件
	Enabled bool
}

// SetupPermissionMiddleware 设置权限中间件
func SetupPermissionMiddleware(engine *gin.RouterGroup, config *PermissionMiddlewareConfig) {
	if config == nil || !config.Enabled {
		return
	}
	
	if config.APIPermissionConfig != nil {
		engine.Use(APIPermissionInterceptor(config.APIPermissionConfig))
	}
}

// 辅助函数：从Gin上下文获取权限相关信息

// GetAPIResourceFromContext 从上下文获取API资源
func GetAPIResourceFromContext(c *gin.Context) (commonResource.Resource, bool) {
	if resource, exists := c.Get("api_resource"); exists {
		if apiResource, ok := resource.(commonResource.Resource); ok {
			return apiResource, true
		}
	}
	return nil, false
}