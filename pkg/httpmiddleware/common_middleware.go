package httpmiddleware

import (
	"context"
	"net/http"
	"os"
	"regexp"
	"strings"
	"time"

	"gitee.com/heiyee/platforms/pkg/common"
	commonResponse "gitee.com/heiyee/platforms/pkg/common/response"

	"go.opentelemetry.io/otel/trace"

	"gitee.com/heiyee/platforms/pkg/logiface"

	"gitee.com/heiyee/platforms/pkg/usercontext"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// RequestID 请求ID中间件
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取或生成请求ID
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = uuid.New().String()
		}
		// 设置请求ID到上下文和响应头
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)

		// 设置请求信息到context
		requestInfo := &usercontext.RequestInfo{
			RequestID: requestID,
			TraceID:   c.<PERSON>eader("X-Trace-ID"), // 从header获取trace_id
			UserAgent: c.<PERSON>Header("User-Agent"),
			ClientIP:  c.ClientIP(),
			Method:    c.Request.Method,
			Path:      c.Request.URL.Path,
			StartTime: time.Now(),
		}

		ctx := usercontext.SetRequestInfo(c.Request.Context(), requestInfo)
		c.Request = c.Request.WithContext(ctx)

		c.Next()
	}
}

// SecurityHeaders 安全头中间件
func SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置安全相关的HTTP头
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		c.Next()
	}
}

// Timeout 超时中间件
func Timeout(timeout time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置请求超时
		ctx, cancel := context.WithTimeout(c.Request.Context(), timeout)
		defer cancel()
		c.Request = c.Request.WithContext(ctx)
		// 监听超时
		done := make(chan struct{})
		go func() {
			c.Next()
			close(done)
		}()
		select {
		case <-done:
			// 请求正常完成
		case <-ctx.Done():
			// 请求超时
			commonResponse.TimeoutError(c)
			c.Abort()
		}
	}
}

// Recovery 恢复中间件（自定义版本）
func Recovery(logger logiface.Logger) gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		// 记录panic日志
		logger.Error(c, "Panic recovered",
			logiface.Any("panic", recovered),
			logiface.String("request_id", c.GetString("request_id")),
			logiface.String("method", c.Request.Method),
			logiface.String("path", c.Request.URL.Path),
			logiface.String("ip", c.ClientIP()))
		commonResponse.InternalError(c, nil)
	})
}

// Metrics 指标收集中间件
func Metrics() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		c.Next()
		// 计算请求耗时
		duration := time.Since(start)
		// 这里可以将指标发送到监控系统
		// 例如：Prometheus、InfluxDB等
		// 简化实现：记录到日志
		if duration > time.Second {
			// 只记录慢请求
			logger := logiface.GetLogger()
			logger.Warn(c, "Slow request detected",
				logiface.String("method", c.Request.Method),
				logiface.String("path", c.Request.URL.Path),
				logiface.Int("status", c.Writer.Status()),
				logiface.Duration("duration", duration),
				logiface.String("request_id", c.GetString("request_id")))
		}
	}
}

// RequestSize 请求大小限制中间件
func RequestSize(maxSize int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.ContentLength > maxSize {
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{
				"success":    false,
				"code":       "REQUEST_TOO_LARGE",
				"message":    "Request entity too large",
				"max_size":   maxSize,
				"request_id": c.GetString("request_id"),
				"timestamp":  time.Now().Unix(),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// IPWhitelist IP白名单中间件
func IPWhitelist(whitelist []string) gin.HandlerFunc {
	whitelistMap := make(map[string]bool)
	for _, ip := range whitelist {
		whitelistMap[ip] = true
	}

	return func(c *gin.Context) {
		clientIP := c.ClientIP()

		if !whitelistMap[clientIP] {
			c.JSON(http.StatusForbidden, gin.H{
				"success":    false,
				"code":       "IP_NOT_ALLOWED",
				"message":    "IP address not in whitelist",
				"ip":         clientIP,
				"request_id": c.GetString("request_id"),
				"timestamp":  time.Now().Unix(),
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// UserAgent 用户代理检查中间件
func UserAgent(required bool, allowedPatterns []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userAgent := c.GetHeader("User-Agent")
		if required && userAgent == "" {
			logger := logiface.GetLogger()
			logger.Warn(c, "User-Agent header missing",
				logiface.String("method", c.Request.Method),
				logiface.String("path", c.Request.URL.Path),
				logiface.String("ip", c.ClientIP()))
			commonResponse.BadRequest(c, "bad request")
			return
		}
		// 检查用户代理模式（如果提供了模式列表）
		if len(allowedPatterns) > 0 {
			allowed := false
			for _, pattern := range allowedPatterns {
				if matched, _ := regexp.MatchString(pattern, userAgent); matched {
					allowed = true
					break
				}
			}
			if !allowed {
				logger := logiface.GetLogger()
				logger.Warn(c, "User-Agent not allowed",
					logiface.String("method", c.Request.Method),
					logiface.String("path", c.Request.URL.Path),
					logiface.String("ip", c.ClientIP()),
					logiface.String("user_agent", userAgent))
				commonResponse.Forbidden(c, "not allowed")
				return
			}
		}
		c.Next()
	}
}

// APIVersion API版本中间件
func APIVersion() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从头部获取API版本
		version := c.GetHeader("X-API-Version")
		if version == "" {
			version = "v1" // 默认版本
		}
		// 设置到上下文
		c.Set("api_version", version)

		// 设置响应头
		c.Header("X-API-Version", version)
		c.Next()
	}
}

// Maintenance 维护模式中间件
func Maintenance(enabled bool, message string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if enabled {
			commonResponse.ServiceUnavailable(c, message)
			return
		}
		c.Next()
	}
}

// HealthCheck 健康检查中间件
func HealthCheck(healthCheckPath string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.URL.Path == healthCheckPath {
			c.JSON(http.StatusOK, gin.H{
				"status":    "ok",
				"timestamp": time.Now().Unix(),
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// TraceIDMiddleware 自动将OpenTelemetry trace_id注入gin.Context和c.Request.Context()
// 推荐所有日志、service、GORM等都通过context获取trace_id
func TraceIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		span := trace.SpanFromContext(c.Request.Context())
		var traceID string
		if span != nil && span.SpanContext().IsValid() {
			traceID = span.SpanContext().TraceID().String()
		}

		// 如果没有从OpenTelemetry获取到，尝试从header获取
		if traceID == "" {
			traceID = c.GetHeader("X-Trace-ID")
		}
		if traceID != "" {
			// 兼容gin handler通过c.Get("trace_id")获取
			c.Set("trace_id", traceID)
			// 设置到usercontext的请求信息中，但不破坏OpenTelemetry context
			if requestInfo, ok := usercontext.GetRequestInfo(c.Request.Context()); ok {
				requestInfo.TraceID = traceID
				// 使用usercontext.SetRequestInfo更新，避免破坏OpenTelemetry context
				ctx := usercontext.SetRequestInfo(c.Request.Context(), requestInfo)
				c.Request = c.Request.WithContext(ctx)
			}
		}
		c.Next()
	}
}

type AuthedUser struct {
	UserId        int64  `json:"user_id"`
	Username      string `json:"username"`
	RealName      string `json:"real_name"`
	Email         string `json:"email"`
	TenantId      int64  `json:"tenant_id"`
	InternalAppID int64  `json:"internal_app_id"` // 内部应用ID
	AppId         string `json:"app_id"`          // 外部应用ID
}

// AppInfo App信息结构体
type AppInfo struct {
	TenantId      int64  `json:"tenant_id"`
	AppId         string `json:"app_id"`
	InternalAppId int64  `json:"internal_app_id"`
}

type UserInfoProvider interface {
	GetUserInfo(ctx context.Context, token string, headers map[string]string) *AuthedUser
}

// AppInfoProvider 租户信息提供者接口
type AppInfoProvider interface {
	GetAppInfo(ctx context.Context, appId string) *AppInfo
}

// UserAndAppInfoMiddleware 用户和应用信息中间件（推荐方案）
func UserAndAppInfoMiddleware(
	getUserInfo func(ctx context.Context, token string, headers map[string]string) *usercontext.UserInfo,
	getAppInfo func(ctx context.Context, appId string) *usercontext.AppInfo,
) gin.HandlerFunc {
	return func(c *gin.Context) {
		var userInfo *usercontext.UserInfo
		var appInfo *usercontext.AppInfo

		// 开发环境测试用户功能：仅当 X-User-ID: 1 时启用
		if isDevEnvironment() {
			if mockUserID := c.GetHeader("X-User-ID"); mockUserID == "1" {
				// 使用固定的测试用户信息
				userInfo = &usercontext.UserInfo{
					UserID:        1,
					Username:      "test_user",
					RealName:      "测试用户",
					Email:         "<EMAIL>",
					TenantID:      1,
					AppId:         "test-app-id",
					InternalAppID: 1,
				}

				// 对应的测试应用信息
				appInfo = &usercontext.AppInfo{
					TenantID:      1,
					AppId:         "test-app-id",
					InternalAppId: 1,
				}

				// 注入用户和应用信息
				usercontext.InjectUserAndApp(c, userInfo, appInfo)

				// 设置测试标识
				c.Set("is_test_user", true)
				c.Set("test_user_id", int64(1))

				// 在响应头中添加测试标识（便于调试）
				c.Header("X-Test-User", "true")
				c.Header("X-Test-User-ID", "1")

				c.Next()
				return
			}
		}

		// 获取 appId
		appId := c.GetHeader(common.HeaderAppId)

		// 将 appId 注入到 context 中，供后续使用
		if appId != "" {
			ctx := context.WithValue(c.Request.Context(), common.HeaderAppId, appId)
			c.Request = c.Request.WithContext(ctx)
		}

		// 获取用户信息（含应用信息）
		authHeader := c.GetHeader(common.HeaderAuthorization)
		if authHeader != "" && strings.HasPrefix(authHeader, common.BearerTokenPrefix) {
			token := strings.TrimPrefix(authHeader, common.BearerTokenPrefix)
			if token != "" {
				// 收集安全校验所需的 header 信息
				headers := make(map[string]string)
				for _, headerName := range common.SecurityValidationHeaders {
					if headerValue := c.GetHeader(headerName); headerValue != "" {
						headers[headerName] = headerValue
					}
				}
				userInfo = getUserInfo(c.Request.Context(), token, headers)
			}
		}

		if userInfo != nil {
			// 用户已登录，直接注入用户和应用信息
			if userInfo.TenantID != 0 || userInfo.AppId != "" {
				appInfo = &usercontext.AppInfo{
					TenantID:      userInfo.TenantID,
					AppId:         userInfo.AppId,
					InternalAppId: userInfo.InternalAppID,
				}
				usercontext.InjectUserAndApp(c, userInfo, appInfo)
				c.Next()
				return
			}
			// 用户信息无效
			commonResponse.Unauthorized(c, "invalid request")
			c.Abort()
			return
		}
		if appId != "" {
			appInfo = getAppInfo(c.Request.Context(), appId)
			if appInfo != nil {
				usercontext.InjectUserAndApp(c, nil, appInfo)
				c.Next()
				return
			}
		}
		// 既没有有效用户信息，也没有有效应用信息
		commonResponse.Unauthorized(c, "invalid request")
		c.Abort()
		return
	}
}

// RequireAuthedMiddleware 强制认证中间件,需要先调用UserInfoMiddleware
func RequireAuthedMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否为测试用户（开发环境）
		if isTestUser, exists := c.Get("is_test_user"); exists && isTestUser.(bool) {
			// 测试用户自动通过认证
			c.Next()
			return
		}

		// 获取用户上下文
		authedUser, _ := usercontext.GetUserInfo(c.Request.Context())
		if authedUser == nil {
			commonResponse.Unauthorized(c, "invalid token: ")
			c.Abort()
			return
		}
		c.Next()
	}
}

// RequireAuthedMiddleware 强制认证中间件,需要先调用UserInfoMiddleware
func RequireAppMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户上下文
		appInfo, _ := usercontext.GetAppInfo(c.Request.Context())
		if appInfo == nil {
			commonResponse.Unauthorized(c, "invalid token: ")
			c.Abort()
			return
		}
		c.Next()
	}
}

// MiddlewareConfig 中间件配置结构体
type MiddlewareConfig struct {
	ServiceName string
	// 请求日志
	EnableAccessLog bool
	// 基础中间件
	EnableRequestID       bool
	EnableSecurityHeaders bool
	EnableRecovery        bool
	EnableMetrics         bool

	// 安全中间件
	EnableRequestSize bool
	MaxRequestSize    int64
	EnableIPWhitelist bool
	IPWhitelist       []string
	EnableUserAgent   bool
	UserAgentRequired bool
	AllowedUserAgents []string

	// 功能中间件
	EnableAPIVersion   bool
	EnableTimeout      bool
	RequestTimeout     time.Duration
	EnableMaintenance  bool
	MaintenanceMessage string
	EnableHealthCheck  bool
	HealthCheckPath    string
	EnableTraceID      bool

	// 认证中间件
	EnableUserInfo   bool
	UserInfoProvider UserInfoProvider
	AppInfoProvider  AppInfoProvider

	// 日志配置
	Logger logiface.Logger
}

// DefaultMiddlewareConfig 返回默认的中间件配置
func DefaultMiddlewareConfig() *MiddlewareConfig {
	return &MiddlewareConfig{
		EnableRequestID:       true,
		EnableSecurityHeaders: true,
		EnableRecovery:        true,
		EnableMetrics:         true,
		EnableRequestSize:     true,
		MaxRequestSize:        10 << 20, // 10MB
		EnableAPIVersion:      true,
		EnableTimeout:         true,
		RequestTimeout:        30 * time.Second,
		EnableTraceID:         true,
		Logger:                logiface.GetLogger(),
	}
}

// SetupCommonMiddleware 配置gin的公共拦截器
func SetupCommonMiddleware(engine *gin.RouterGroup, config *MiddlewareConfig) {
	if config == nil {
		config = DefaultMiddlewareConfig()
	}
	// 添加中间件
	engine.Use(GinOtelMiddleware(config.ServiceName))
	// 基础中间件（按执行顺序）
	if config.EnableRequestID {
		engine.Use(RequestID())
	}
	if config.EnableTraceID {
		engine.Use(TraceIDMiddleware())
	}
	if config.EnableSecurityHeaders {
		engine.Use(SecurityHeaders())
	}
	if config.EnableRequestSize {
		engine.Use(RequestSize(config.MaxRequestSize))
	}
	if config.EnableAccessLog {
		engine.Use(AccessLogMiddleware())
	}
	if config.EnableTimeout {
		engine.Use(Timeout(config.RequestTimeout))
	}

	if config.EnableUserAgent {
		engine.Use(UserAgent(config.UserAgentRequired, config.AllowedUserAgents))
	}
	if config.EnableIPWhitelist && len(config.IPWhitelist) > 0 {
		engine.Use(IPWhitelist(config.IPWhitelist))
	}
	if config.EnableAPIVersion {
		engine.Use(APIVersion())
	}
	if config.EnableHealthCheck {
		if config.HealthCheckPath == "" {
			config.HealthCheckPath = "/health"
		}
		engine.Use(HealthCheck(config.HealthCheckPath))
	}
	if config.EnableRecovery {
		engine.Use(Recovery(config.Logger))
	}
	// 集成统一用户和应用信息中间件
	if config.EnableUserInfo && config.UserInfoProvider != nil {
		engine.Use(UserAndAppInfoMiddleware(
			func(ctx context.Context, token string, headers map[string]string) *usercontext.UserInfo {
				u := config.UserInfoProvider.GetUserInfo(ctx, token, headers)
				if u == nil {
					return nil
				}
				// 适配 AuthedUser -> usercontext.UserInfo
				return &usercontext.UserInfo{
					UserID:        u.UserId,
					Username:      u.Username,
					RealName:      u.RealName,
					Email:         u.Email,
					TenantID:      u.TenantId,
					AppId:         u.AppId,         // 可扩展
					InternalAppID: u.InternalAppID, // 可扩展
				}
			},
			func(ctx context.Context, appId string) *usercontext.AppInfo {
				if config.AppInfoProvider != nil {
					t := config.AppInfoProvider.GetAppInfo(ctx, appId)
					if t != nil {
						return &usercontext.AppInfo{
							TenantID:      t.TenantId,
							AppId:         t.AppId,
							InternalAppId: t.InternalAppId,
						}
					}
				}
				return nil
			},
		))
	}
}

// isDevEnvironment 检查是否为开发环境
func isDevEnvironment() bool {
	env := os.Getenv("APP_ENV")
	return env == "dev" || env == "development" || env == "local"
}
