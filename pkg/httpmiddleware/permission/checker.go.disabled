package permission

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// PermissionResult 权限检查结果
type PermissionResult struct {
	HasAccess      bool                      // 是否有访问权限
	CheckedPerms   []*PermissionRequirement  // 已检查的权限需求
	GrantedPerms   []string                  // 已授予的权限
	MissingPerms   []string                  // 缺失的权限
	Reason         string                    // 决策原因
}

// IsAccessAllowed 是否允许访问
func (pr *PermissionResult) IsAccessAllowed() bool {
	return pr.HasAccess
}

// GetMissingPermissions 获取缺失的权限列表
func (pr *PermissionResult) GetMissingPermissions() []string {
	return pr.MissingPerms
}

// GetAccessReason 获取访问决策原因
func (pr *PermissionResult) GetAccessReason() string {
	if pr.Reason != "" {
		return pr.Reason
	}
	if pr.HasAccess {
		return "权限检查通过"
	}
	return "权限不足"
}

// PermissionChecker 权限检查器接口
type PermissionChecker interface {
	// CheckAPIAccess 检查API访问权限（基于页面上下文）
	CheckAPIAccess(ctx context.Context, userID int64, apiResource *entity.Resource, pageContext *PageContext) (*PermissionResult, error)
	
	// CheckResourceAccess 检查资源访问权限（基础检查）
	CheckResourceAccess(ctx context.Context, userID int64, resource *entity.Resource) (*PermissionResult, error)
	
	// CheckPermissions 检查具体权限列表
	CheckPermissions(ctx context.Context, userID int64, permissions []*PermissionRequirement, tenantID, internalAppID int64) (*PermissionResult, error)
	
	// CheckPermissionCode 检查单个权限编码
	CheckPermissionCode(ctx context.Context, userID int64, permissionCode string, tenantID, internalAppID int64) (bool, error)
}

// UserPermissionRepository 用户权限仓库接口
type UserPermissionRepository interface {
	// GetUserPermissions 获取用户权限列表
	GetUserPermissions(ctx context.Context, userID, tenantID, internalAppID int64) ([]string, error)
	
	// HasPermission 检查用户是否有指定权限
	HasPermission(ctx context.Context, userID int64, permissionCode string, tenantID, internalAppID int64) (bool, error)
	
	// GetUserRoles 获取用户角色列表
	GetUserRoles(ctx context.Context, userID, tenantID, internalAppID int64) ([]*entity.Role, error)
}

// defaultPermissionChecker 默认权限检查器实现
type defaultPermissionChecker struct {
	userPermRepo   UserPermissionRepository
	relationResolver ResourceRelationResolver
	logger         logiface.Logger
}

// NewPermissionChecker 创建权限检查器
func NewPermissionChecker(
	userPermRepo UserPermissionRepository,
	relationResolver ResourceRelationResolver,
	logger logiface.Logger,
) PermissionChecker {
	return &defaultPermissionChecker{
		userPermRepo:     userPermRepo,
		relationResolver: relationResolver,
		logger:           logger,
	}
}

// CheckAPIAccess 检查API访问权限（基于页面上下文）
func (c *defaultPermissionChecker) CheckAPIAccess(ctx context.Context, userID int64, apiResource *entity.Resource, pageContext *PageContext) (*PermissionResult, error) {
	// 1. 解析权限需求
	permissions, err := c.relationResolver.ResolveAPIPermissions(ctx, apiResource, pageContext)
	if err != nil {
		return nil, fmt.Errorf("解析API权限需求失败: %v", err)
	}
	
	c.logger.Debug(ctx, "开始API权限检查",
		logiface.Int64("user_id", userID),
		logiface.Int64("api_resource_id", apiResource.ID),
		logiface.String("api_name", apiResource.Name),
		logiface.Int("permission_count", len(permissions)))
	
	// 2. 检查权限
	result, err := c.CheckPermissions(ctx, userID, permissions, apiResource.TenantID, apiResource.InternalAppID)
	if err != nil {
		return nil, fmt.Errorf("权限检查失败: %v", err)
	}
	
	// 3. 记录检查结果
	c.logPermissionCheck(ctx, userID, apiResource, pageContext, result)
	
	return result, nil
}

// CheckResourceAccess 检查资源访问权限（基础检查）
func (c *defaultPermissionChecker) CheckResourceAccess(ctx context.Context, userID int64, resource *entity.Resource) (*PermissionResult, error) {
	// 1. 检查资源是否为公开资源
	if resource.IsPublicResource() {
		c.logger.Debug(ctx, "资源为公开资源，允许访问",
			logiface.Int64("user_id", userID),
			logiface.Int64("resource_id", resource.ID),
			logiface.String("resource_name", resource.Name))
		
		return &PermissionResult{
			HasAccess: true,
			Reason:    "公开资源",
		}, nil
	}
	
	// 2. 获取默认权限需求
	permissions, err := c.relationResolver.GetDefaultPermissions(ctx, resource)
	if err != nil {
		return nil, fmt.Errorf("获取默认权限需求失败: %v", err)
	}
	
	// 3. 检查权限
	result, err := c.CheckPermissions(ctx, userID, permissions, resource.TenantID, resource.InternalAppID)
	if err != nil {
		return nil, fmt.Errorf("权限检查失败: %v", err)
	}
	
	c.logger.Debug(ctx, "基础资源权限检查完成",
		logiface.Int64("user_id", userID),
		logiface.Int64("resource_id", resource.ID),
		logiface.String("resource_name", resource.Name),
		logiface.Bool("has_access", result.HasAccess))
	
	return result, nil
}

// CheckPermissions 检查具体权限列表
func (c *defaultPermissionChecker) CheckPermissions(ctx context.Context, userID int64, permissions []*PermissionRequirement, tenantID, internalAppID int64) (*PermissionResult, error) {
	result := &PermissionResult{
		CheckedPerms: permissions,
		GrantedPerms: make([]string, 0),
		MissingPerms: make([]string, 0),
	}
	
	// 如果没有权限需求，直接允许访问
	if len(permissions) == 0 {
		result.HasAccess = true
		result.Reason = "无权限需求"
		return result, nil
	}
	
	// 获取用户权限列表
	userPermissions, err := c.userPermRepo.GetUserPermissions(ctx, userID, tenantID, internalAppID)
	if err != nil {
		return nil, fmt.Errorf("获取用户权限失败: %v", err)
	}
	
	// 创建权限映射
	userPermMap := make(map[string]bool)
	for _, perm := range userPermissions {
		userPermMap[perm] = true
	}
	
	// 检查每个权限需求
	hasRequiredPerms := true
	hasAnyPerm := false
	
	for _, perm := range permissions {
		if perm.PermissionCode == "" {
			continue
		}
		
		hasThisPerm := userPermMap[perm.PermissionCode]
		if hasThisPerm {
			result.GrantedPerms = append(result.GrantedPerms, perm.PermissionCode)
			hasAnyPerm = true
		} else {
			result.MissingPerms = append(result.MissingPerms, perm.PermissionCode)
			if perm.IsRequired {
				hasRequiredPerms = false
			}
		}
		
		c.logger.Debug(ctx, "权限检查详情",
			logiface.Int64("user_id", userID),
			logiface.String("permission_code", perm.PermissionCode),
			logiface.Bool("is_required", perm.IsRequired),
			logiface.Bool("has_permission", hasThisPerm),
			logiface.String("source", perm.Source))
	}
	
	// 决策逻辑：
	// 1. 如果有必需权限缺失，拒绝访问
	// 2. 如果所有权限都是非必需的，且用户有任意一个权限，允许访问
	// 3. 如果所有权限都是非必需的，且用户没有任何权限，拒绝访问
	
	if !hasRequiredPerms {
		result.HasAccess = false
		result.Reason = fmt.Sprintf("缺少必需权限: %v", result.MissingPerms)
	} else if len(result.MissingPerms) == 0 {
		result.HasAccess = true
		result.Reason = "所有权限检查通过"
	} else if hasAnyPerm {
		result.HasAccess = true
		result.Reason = "部分权限检查通过"
	} else {
		result.HasAccess = false
		result.Reason = "无任何有效权限"
	}
	
	c.logger.Info(ctx, "权限检查结果",
		logiface.Int64("user_id", userID),
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("internal_app_id", internalAppID),
		logiface.Bool("has_access", result.HasAccess),
		logiface.Strings("granted_perms", result.GrantedPerms),
		logiface.Strings("missing_perms", result.MissingPerms),
		logiface.String("reason", result.Reason))
	
	return result, nil
}

// CheckPermissionCode 检查单个权限编码
func (c *defaultPermissionChecker) CheckPermissionCode(ctx context.Context, userID int64, permissionCode string, tenantID, internalAppID int64) (bool, error) {
	hasPermission, err := c.userPermRepo.HasPermission(ctx, userID, permissionCode, tenantID, internalAppID)
	if err != nil {
		return false, fmt.Errorf("检查权限失败: %v", err)
	}
	
	c.logger.Debug(ctx, "单个权限检查",
		logiface.Int64("user_id", userID),
		logiface.String("permission_code", permissionCode),
		logiface.Bool("has_permission", hasPermission))
	
	return hasPermission, nil
}

// logPermissionCheck 记录权限检查日志
func (c *defaultPermissionChecker) logPermissionCheck(ctx context.Context, userID int64, apiResource *entity.Resource, pageContext *PageContext, result *PermissionResult) {
	fields := []logiface.Field{
		logiface.Int64("user_id", userID),
		logiface.Int64("api_resource_id", apiResource.ID),
		logiface.String("api_name", apiResource.Name),
		logiface.String("api_path", apiResource.Path),
		logiface.String("api_method", apiResource.APIMethod),
		logiface.Bool("has_access", result.HasAccess),
		logiface.String("reason", result.Reason),
		logiface.Int("checked_perm_count", len(result.CheckedPerms)),
		logiface.Int("granted_perm_count", len(result.GrantedPerms)),
		logiface.Int("missing_perm_count", len(result.MissingPerms)),
	}
	
	if pageContext != nil {
		fields = append(fields,
			logiface.Int64("page_resource_id", pageContext.PageResourceID),
			logiface.String("page_path", pageContext.PagePath),
			logiface.String("page_name", pageContext.PageName))
	}
	
	if result.HasAccess {
		c.logger.Info(ctx, "API权限检查通过", fields...)
	} else {
		c.logger.Warn(ctx, "API权限检查失败", fields...)
	}
}