package permission

import (
	"context"
	"fmt"
	"strings"

	commonResource "gitee.com/heiyee/platforms/pkg/common/resource"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// APIResourceMatcher API资源匹配器接口
type APIResourceMatcher interface {
	// MatchAPIResource 匹配API资源
	MatchAPIResource(ctx context.Context, path, method string, tenantID, internalAppID int64) (commonResource.Resource, error)
	
	// RefreshCache 刷新缓存
	RefreshCache(ctx context.Context, tenantID, internalAppID int64) error
}

// APIResourceCache API资源缓存项
type APIResourceCache struct {
	Resources []commonResource.Resource // 资源列表
}

// defaultAPIResourceMatcher 默认API资源匹配器实现
type defaultAPIResourceMatcher struct {
	resourceRepo ResourceRepository    // 资源仓库
	cache        map[string]*APIResourceCache // 缓存：tenantID_appID -> cache
	logger       logiface.Logger
}

// ResourceRepository 资源仓库接口
type ResourceRepository interface {
	// FindAPIResources 查找API资源
	FindAPIResources(ctx context.Context, tenantID, internalAppID int64) ([]commonResource.Resource, error)
}

// NewAPIResourceMatcher 创建API资源匹配器
func NewAPIResourceMatcher(resourceRepo ResourceRepository, logger logiface.Logger) APIResourceMatcher {
	return &defaultAPIResourceMatcher{
		resourceRepo: resourceRepo,
		cache:        make(map[string]*APIResourceCache),
		logger:       logger,
	}
}

// MatchAPIResource 匹配API资源
func (m *defaultAPIResourceMatcher) MatchAPIResource(ctx context.Context, path, method string, tenantID, internalAppID int64) (commonResource.Resource, error) {
	// 标准化方法名
	method = strings.ToUpper(method)
	
	// 获取缓存
	cache, err := m.getOrLoadCache(ctx, tenantID, internalAppID)
	if err != nil {
		return nil, fmt.Errorf("获取资源缓存失败: %v", err)
	}
	
	// 遍历资源进行匹配
	for _, resource := range cache.Resources {
		if m.matchResource(resource, path, method) {
			m.logger.Debug(ctx, "API资源匹配成功",
				logiface.String("path", path),
				logiface.String("method", method),
				logiface.Int64("resource_id", resource.GetID()),
				logiface.String("resource_name", resource.GetName()))
			return resource, nil
		}
	}
	
	m.logger.Debug(ctx, "未找到匹配的API资源",
		logiface.String("path", path),
		logiface.String("method", method),
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("internal_app_id", internalAppID))
	
	return nil, nil // 未找到匹配的资源
}

// matchResource 匹配单个资源 - 简化版本，只做URL和方法匹配
func (m *defaultAPIResourceMatcher) matchResource(resource commonResource.Resource, path, method string) bool {
	// 检查资源类型
	if !resource.IsAPI() {
		return false
	}
	
	// 检查HTTP方法
	if resource.GetAPIMethod() != "" && strings.ToUpper(resource.GetAPIMethod()) != method {
		return false
	}
	
	// 检查路径匹配 - 简化为精确匹配
	if resource.GetPath() == "" {
		return false
	}
	
	// 精确匹配路径
	return resource.GetPath() == path
}

// getOrLoadCache 获取或加载缓存
func (m *defaultAPIResourceMatcher) getOrLoadCache(ctx context.Context, tenantID, internalAppID int64) (*APIResourceCache, error) {
	cacheKey := fmt.Sprintf("%d_%d", tenantID, internalAppID)
	
	// 检查缓存
	if cache, exists := m.cache[cacheKey]; exists {
		return cache, nil
	}
	
	// 加载资源
	resources, err := m.resourceRepo.FindAPIResources(ctx, tenantID, internalAppID)
	if err != nil {
		return nil, fmt.Errorf("查询API资源失败: %v", err)
	}
	
	// 创建缓存
	cache := &APIResourceCache{
		Resources: resources,
	}
	
	// 存储缓存
	m.cache[cacheKey] = cache
	
	m.logger.Info(ctx, "API资源缓存已加载",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("internal_app_id", internalAppID),
		logiface.Int("resource_count", len(resources)))
	
	return cache, nil
}

// RefreshCache 刷新缓存
func (m *defaultAPIResourceMatcher) RefreshCache(ctx context.Context, tenantID, internalAppID int64) error {
	cacheKey := fmt.Sprintf("%d_%d", tenantID, internalAppID)
	delete(m.cache, cacheKey)
	
	m.logger.Info(ctx, "API资源缓存已清除",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("internal_app_id", internalAppID))
	
	// 重新加载缓存
	_, err := m.getOrLoadCache(ctx, tenantID, internalAppID)
	return err
}