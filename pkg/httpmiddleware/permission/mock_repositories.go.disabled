package permission

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
)

// MockResourceRepository 模拟资源仓库（用于测试）
type MockResourceRepository struct {
	resources map[string][]*entity.Resource
}

// NewMockResourceRepository 创建模拟资源仓库
func NewMockResourceRepository() *MockResourceRepository {
	return &MockResourceRepository{
		resources: make(map[string][]*entity.Resource),
	}
}

// FindAPIResources 查找API资源
func (m *MockResourceRepository) FindAPIResources(ctx context.Context, tenantID, internalAppID int64) ([]*entity.Resource, error) {
	key := fmt.Sprintf("%d_%d", tenantID, internalAppID)
	if resources, exists := m.resources[key]; exists {
		return resources, nil
	}
	return []*entity.Resource{}, nil
}

// AddAPIResource 添加API资源（测试用）
func (m *MockResourceRepository) AddAPIResource(tenantID, internalAppID int64, resource *entity.Resource) {
	key := fmt.Sprintf("%d_%d", tenantID, internalAppID)
	m.resources[key] = append(m.resources[key], resource)
}

// MockResourceRelationRepository 模拟资源关系仓库（用于测试）
type MockResourceRelationRepository struct {
	relations map[string][]*entity.ResourceRelation
}

// NewMockResourceRelationRepository 创建模拟资源关系仓库
func NewMockResourceRelationRepository() *MockResourceRelationRepository {
	return &MockResourceRelationRepository{
		relations: make(map[string][]*entity.ResourceRelation),
	}
}

// FindRelationsByAPI 根据API资源查找关系
func (m *MockResourceRelationRepository) FindRelationsByAPI(ctx context.Context, apiResourceID, tenantID, internalAppID int64) ([]*entity.ResourceRelation, error) {
	key := fmt.Sprintf("api_%d_%d_%d", apiResourceID, tenantID, internalAppID)
	if relations, exists := m.relations[key]; exists {
		return relations, nil
	}
	return []*entity.ResourceRelation{}, nil
}

// FindRelationsByPageAndAPI 根据页面和API资源查找关系
func (m *MockResourceRelationRepository) FindRelationsByPageAndAPI(ctx context.Context, pageResourceID, apiResourceID, tenantID, internalAppID int64) ([]*entity.ResourceRelation, error) {
	key := fmt.Sprintf("page_api_%d_%d_%d_%d", pageResourceID, apiResourceID, tenantID, internalAppID)
	if relations, exists := m.relations[key]; exists {
		return relations, nil
	}
	return []*entity.ResourceRelation{}, nil
}

// FindActiveRelations 查找激活的关系
func (m *MockResourceRelationRepository) FindActiveRelations(ctx context.Context, tenantID, internalAppID int64) ([]*entity.ResourceRelation, error) {
	key := fmt.Sprintf("active_%d_%d", tenantID, internalAppID)
	if relations, exists := m.relations[key]; exists {
		return relations, nil
	}
	return []*entity.ResourceRelation{}, nil
}

// AddRelation 添加关系（测试用）
func (m *MockResourceRelationRepository) AddRelation(key string, relation *entity.ResourceRelation) {
	m.relations[key] = append(m.relations[key], relation)
}

// MockUserPermissionRepository 模拟用户权限仓库（用于测试）
type MockUserPermissionRepository struct {
	permissions map[string][]string
	roles       map[string][]*entity.Role
}

// NewMockUserPermissionRepository 创建模拟用户权限仓库
func NewMockUserPermissionRepository() *MockUserPermissionRepository {
	return &MockUserPermissionRepository{
		permissions: make(map[string][]string),
		roles:       make(map[string][]*entity.Role),
	}
}

// GetUserPermissions 获取用户权限列表
func (m *MockUserPermissionRepository) GetUserPermissions(ctx context.Context, userID, tenantID, internalAppID int64) ([]string, error) {
	key := fmt.Sprintf("%d_%d_%d", userID, tenantID, internalAppID)
	if permissions, exists := m.permissions[key]; exists {
		return permissions, nil
	}
	return []string{}, nil
}

// HasPermission 检查用户是否有指定权限
func (m *MockUserPermissionRepository) HasPermission(ctx context.Context, userID int64, permissionCode string, tenantID, internalAppID int64) (bool, error) {
	permissions, err := m.GetUserPermissions(ctx, userID, tenantID, internalAppID)
	if err != nil {
		return false, err
	}
	
	for _, perm := range permissions {
		if perm == permissionCode {
			return true, nil
		}
	}
	
	return false, nil
}

// GetUserRoles 获取用户角色列表
func (m *MockUserPermissionRepository) GetUserRoles(ctx context.Context, userID, tenantID, internalAppID int64) ([]*entity.Role, error) {
	key := fmt.Sprintf("%d_%d_%d", userID, tenantID, internalAppID)
	if roles, exists := m.roles[key]; exists {
		return roles, nil
	}
	return []*entity.Role{}, nil
}

// AddUserPermission 添加用户权限（测试用）
func (m *MockUserPermissionRepository) AddUserPermission(userID, tenantID, internalAppID int64, permission string) {
	key := fmt.Sprintf("%d_%d_%d", userID, tenantID, internalAppID)
	m.permissions[key] = append(m.permissions[key], permission)
}

// AddUserRole 添加用户角色（测试用）
func (m *MockUserPermissionRepository) AddUserRole(userID, tenantID, internalAppID int64, role *entity.Role) {
	key := fmt.Sprintf("%d_%d_%d", userID, tenantID, internalAppID)
	m.roles[key] = append(m.roles[key], role)
}