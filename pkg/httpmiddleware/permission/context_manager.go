package permission

import (
	"context"
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// PageContext 页面上下文信息
type PageContext struct {
	PageResourceID   int64             // 页面资源ID
	PagePath         string            // 页面路径
	PageName         string            // 页面名称
	RefererURL       string            // 来源页面URL
	Headers          map[string]string // 相关头信息
	QueryParams      map[string]string // 查询参数
	TenantID         int64             // 租户ID
	InternalAppID    int64             // 应用ID
}

// IsValid 检查页面上下文是否有效
func (pc *PageContext) IsValid() bool {
	return pc.PageResourceID > 0 && pc.TenantID > 0 && pc.InternalAppID > 0
}

// GetDisplayInfo 获取显示信息
func (pc *PageContext) GetDisplayInfo() string {
	if pc.PageName != "" {
		return pc.PageName
	}
	if pc.PagePath != "" {
		return pc.PagePath
	}
	return fmt.Sprintf("页面资源ID: %d", pc.PageResourceID)
}

// PageContextManager 页面上下文管理器接口
type PageContextManager interface {
	// GetPageContext 获取页面上下文
	GetPageContext(c *gin.Context) (*PageContext, error)
	
	// ExtractPageInfo 从请求中提取页面信息
	ExtractPageInfo(c *gin.Context) (*PageContext, error)
	
	// ValidatePageAccess 验证页面访问权限
	ValidatePageAccess(ctx context.Context, userID int64, pageContext *PageContext) (bool, error)
}

// defaultPageContextManager 默认页面上下文管理器实现
type defaultPageContextManager struct {
	resourceRepo ResourceRepository
	logger       logiface.Logger
}

// NewPageContextManager 创建页面上下文管理器
func NewPageContextManager(resourceRepo ResourceRepository, logger logiface.Logger) PageContextManager {
	return &defaultPageContextManager{
		resourceRepo: resourceRepo,
		logger:       logger,
	}
}

// GetPageContext 获取页面上下文
func (m *defaultPageContextManager) GetPageContext(c *gin.Context) (*PageContext, error) {
	ctx := c.Request.Context()
	
	// 1. 尝试从请求头获取页面信息
	pageContext, err := m.extractFromHeaders(c)
	if err == nil && pageContext != nil && pageContext.IsValid() {
		m.logger.Debug(ctx, "从请求头获取页面上下文",
			logiface.Int64("page_resource_id", pageContext.PageResourceID),
			logiface.String("page_path", pageContext.PagePath))
		return pageContext, nil
	}
	
	// 2. 尝试从Referer获取页面信息
	pageContext, err = m.extractFromReferer(c)
	if err == nil && pageContext != nil && pageContext.IsValid() {
		m.logger.Debug(ctx, "从Referer获取页面上下文",
			logiface.Int64("page_resource_id", pageContext.PageResourceID),
			logiface.String("referer_url", pageContext.RefererURL))
		return pageContext, nil
	}
	
	// 3. 尝试从查询参数获取页面信息
	pageContext, err = m.extractFromQuery(c)
	if err == nil && pageContext != nil && pageContext.IsValid() {
		m.logger.Debug(ctx, "从查询参数获取页面上下文",
			logiface.Int64("page_resource_id", pageContext.PageResourceID))
		return pageContext, nil
	}
	
	// 4. 返回空上下文（不是错误，只是没有页面上下文）
	m.logger.Debug(ctx, "未能获取页面上下文",
		logiface.String("path", c.Request.URL.Path),
		logiface.String("method", c.Request.Method))
	
	return nil, nil
}

// extractFromHeaders 从请求头提取页面信息
func (m *defaultPageContextManager) extractFromHeaders(c *gin.Context) (*PageContext, error) {
	// 检查自定义头部
	pageIDStr := c.GetHeader("X-Page-Resource-ID")
	pagePath := c.GetHeader("X-Page-Path")
	pageName := c.GetHeader("X-Page-Name")
	
	if pageIDStr == "" && pagePath == "" {
		return nil, fmt.Errorf("请求头中未找到页面信息")
	}
	
	var pageResourceID int64
	if pageIDStr != "" {
		var err error
		pageResourceID, err = strconv.ParseInt(pageIDStr, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("无效的页面资源ID: %s", pageIDStr)
		}
	}
	
	// 获取应用信息
	tenantID, internalAppID, err := m.extractAppInfo(c)
	if err != nil {
		return nil, err
	}
	
	pageContext := &PageContext{
		PageResourceID: pageResourceID,
		PagePath:       pagePath,
		PageName:       pageName,
		Headers:        make(map[string]string),
		TenantID:       tenantID,
		InternalAppID:  internalAppID,
	}
	
	// 收集相关头信息
	pageContext.Headers["User-Agent"] = c.GetHeader("User-Agent")
	pageContext.Headers["Origin"] = c.GetHeader("Origin")
	pageContext.Headers["Referer"] = c.GetHeader("Referer")
	
	return pageContext, nil
}

// extractFromReferer 从Referer提取页面信息
func (m *defaultPageContextManager) extractFromReferer(c *gin.Context) (*PageContext, error) {
	referer := c.GetHeader("Referer")
	if referer == "" {
		return nil, fmt.Errorf("请求头中未找到Referer")
	}
	
	// 这里可以实现URL到页面资源的映射逻辑
	// 暂时返回基础信息
	tenantID, internalAppID, err := m.extractAppInfo(c)
	if err != nil {
		return nil, err
	}
	
	pageContext := &PageContext{
		RefererURL:    referer,
		TenantID:      tenantID,
		InternalAppID: internalAppID,
		Headers:       make(map[string]string),
	}
	
	pageContext.Headers["Referer"] = referer
	
	return pageContext, nil
}

// extractFromQuery 从查询参数提取页面信息
func (m *defaultPageContextManager) extractFromQuery(c *gin.Context) (*PageContext, error) {
	pageIDStr := c.Query("page_resource_id")
	pagePath := c.Query("page_path")
	
	if pageIDStr == "" && pagePath == "" {
		return nil, fmt.Errorf("查询参数中未找到页面信息")
	}
	
	var pageResourceID int64
	if pageIDStr != "" {
		var err error
		pageResourceID, err = strconv.ParseInt(pageIDStr, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("无效的页面资源ID: %s", pageIDStr)
		}
	}
	
	// 获取应用信息
	tenantID, internalAppID, err := m.extractAppInfo(c)
	if err != nil {
		return nil, err
	}
	
	pageContext := &PageContext{
		PageResourceID: pageResourceID,
		PagePath:       pagePath,
		QueryParams:    make(map[string]string),
		TenantID:       tenantID,
		InternalAppID:  internalAppID,
	}
	
	// 收集相关查询参数
	for key, values := range c.Request.URL.Query() {
		if len(values) > 0 {
			pageContext.QueryParams[key] = values[0]
		}
	}
	
	return pageContext, nil
}

// extractAppInfo 提取应用信息
func (m *defaultPageContextManager) extractAppInfo(c *gin.Context) (int64, int64, error) {
	// 从用户上下文获取应用信息
	ctx := c.Request.Context()
	
	// 这里需要根据实际的用户上下文实现来获取租户ID和应用ID
	// 暂时从请求头获取
	tenantIDStr := c.GetHeader("X-Tenant-ID")
	appIDStr := c.GetHeader("X-Internal-App-ID")
	
	var tenantID, internalAppID int64
	var err error
	
	if tenantIDStr != "" {
		tenantID, err = strconv.ParseInt(tenantIDStr, 10, 64)
		if err != nil {
			return 0, 0, fmt.Errorf("无效的租户ID: %s", tenantIDStr)
		}
	}
	
	if appIDStr != "" {
		internalAppID, err = strconv.ParseInt(appIDStr, 10, 64)
		if err != nil {
			return 0, 0, fmt.Errorf("无效的应用ID: %s", appIDStr)
		}
	}
	
	// 如果头部没有，尝试从上下文获取
	if tenantID == 0 || internalAppID == 0 {
		// TODO: 从usercontext获取应用信息
		m.logger.Debug(ctx, "未能从请求头获取完整的应用信息",
			logiface.Int64("tenant_id", tenantID),
			logiface.Int64("internal_app_id", internalAppID))
	}
	
	return tenantID, internalAppID, nil
}

// ExtractPageInfo 从请求中提取页面信息
func (m *defaultPageContextManager) ExtractPageInfo(c *gin.Context) (*PageContext, error) {
	return m.GetPageContext(c)
}

// ValidatePageAccess 验证页面访问权限
func (m *defaultPageContextManager) ValidatePageAccess(ctx context.Context, userID int64, pageContext *PageContext) (bool, error) {
	if pageContext == nil || !pageContext.IsValid() {
		// 没有页面上下文时，不进行页面权限检查
		return true, nil
	}
	
	// TODO: 实现页面访问权限检查逻辑
	// 这里应该检查用户是否有访问指定页面的权限
	
	m.logger.Debug(ctx, "页面访问权限检查",
		logiface.Int64("user_id", userID),
		logiface.Int64("page_resource_id", pageContext.PageResourceID),
		logiface.String("page_path", pageContext.PagePath))
	
	// 暂时返回true，实际实现时需要查询用户权限
	return true, nil
}