package permission

import (
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// PermissionFactory 权限组件工厂
type PermissionFactory struct {
	logger logiface.Logger
}

// NewPermissionFactory 创建权限组件工厂
func NewPermissionFactory(logger logiface.Logger) *PermissionFactory {
	return &PermissionFactory{
		logger: logger,
	}
}

// CreatePermissionComponents 创建权限相关组件
func (f *PermissionFactory) CreatePermissionComponents(
	resourceRepo ResourceRepository,
	relationRepo ResourceRelationRepository,
	userPermRepo UserPermissionRepository,
) (*PermissionComponents, error) {
	
	// 创建缓存管理器
	cacheManager := NewPermissionCacheManager(DefaultCacheConfig(), f.logger)
	
	// 创建API资源匹配器
	apiMatcher := NewAPIResourceMatcher(resourceRepo, f.logger)
	
	// 创建页面上下文管理器
	contextManager := NewPageContextManager(resourceRepo, f.logger)
	
	// 创建资源关系权限解析器
	relationResolver := NewResourceRelationResolver(relationRepo, f.logger)
	
	// 创建权限检查器
	permissionChecker := NewPermissionChecker(userPermRepo, relationResolver, f.logger)
	
	return &PermissionComponents{
		APIResourceMatcher:       apiMatcher,
		PageContextManager:      contextManager,
		ResourceRelationResolver: relationResolver,
		PermissionChecker:       permissionChecker,
		CacheManager:           cacheManager,
		Logger:                 f.logger,
	}, nil
}

// PermissionComponents 权限组件集合
type PermissionComponents struct {
	APIResourceMatcher       APIResourceMatcher
	PageContextManager      PageContextManager
	ResourceRelationResolver ResourceRelationResolver
	PermissionChecker       PermissionChecker
	CacheManager           PermissionCacheManager
	Logger                 logiface.Logger
}

// Start 启动权限组件
func (pc *PermissionComponents) Start() error {
	if pc.CacheManager != nil {
		return pc.CacheManager.Start()
	}
	return nil
}

// Stop 停止权限组件
func (pc *PermissionComponents) Stop() error {
	if pc.CacheManager != nil {
		return pc.CacheManager.Stop()
	}
	return nil
}

// GetCacheStats 获取缓存统计信息
func (pc *PermissionComponents) GetCacheStats() *CacheStats {
	if pc.CacheManager != nil {
		return pc.CacheManager.GetStats()
	}
	return nil
}

// InvalidateUserCache 失效用户缓存
func (pc *PermissionComponents) InvalidateUserCache(userID, tenantID, internalAppID int64) {
	if pc.CacheManager != nil {
		pc.CacheManager.InvalidateUser(userID, tenantID, internalAppID)
	}
}

// InvalidateAppCache 失效应用缓存
func (pc *PermissionComponents) InvalidateAppCache(tenantID, internalAppID int64) {
	if pc.CacheManager != nil {
		pc.CacheManager.InvalidateApp(tenantID, internalAppID)
	}
}

// RefreshAPIResourceCache 刷新API资源缓存
func (pc *PermissionComponents) RefreshAPIResourceCache(tenantID, internalAppID int64) error {
	if pc.APIResourceMatcher != nil {
		return pc.APIResourceMatcher.RefreshCache(nil, tenantID, internalAppID)
	}
	return nil
}