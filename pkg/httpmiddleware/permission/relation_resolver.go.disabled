package permission

import (
	"context"
	"fmt"
	"sort"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// PermissionRequirement 权限需求
type PermissionRequirement struct {
	PermissionCode string // 权限编码
	IsRequired     bool   // 是否必需
	Priority       int    // 优先级
	Source         string // 来源描述
}

// ResourceRelationResolver 资源关系权限解析器接口
type ResourceRelationResolver interface {
	// ResolveAPIPermissions 解析API权限需求
	ResolveAPIPermissions(ctx context.Context, apiResource *entity.Resource, pageContext *PageContext) ([]*PermissionRequirement, error)
	
	// ResolvePermissionsByRelation 通过关系解析权限需求
	ResolvePermissionsByRelation(ctx context.Context, apiResourceID, pageResourceID, tenantID, internalAppID int64) ([]*PermissionRequirement, error)
	
	// GetDefaultPermissions 获取API的默认权限需求
	GetDefaultPermissions(ctx context.Context, apiResource *entity.Resource) ([]*PermissionRequirement, error)
}

// defaultResourceRelationResolver 默认资源关系权限解析器实现
type defaultResourceRelationResolver struct {
	relationRepo ResourceRelationRepository
	logger       logiface.Logger
}

// ResourceRelationRepository 资源关系仓库接口
type ResourceRelationRepository interface {
	// FindRelationsByAPI 根据API资源查找关系
	FindRelationsByAPI(ctx context.Context, apiResourceID, tenantID, internalAppID int64) ([]*entity.ResourceRelation, error)
	
	// FindRelationsByPageAndAPI 根据页面和API资源查找关系
	FindRelationsByPageAndAPI(ctx context.Context, pageResourceID, apiResourceID, tenantID, internalAppID int64) ([]*entity.ResourceRelation, error)
	
	// FindActiveRelations 查找激活的关系
	FindActiveRelations(ctx context.Context, tenantID, internalAppID int64) ([]*entity.ResourceRelation, error)
}

// NewResourceRelationResolver 创建资源关系权限解析器
func NewResourceRelationResolver(relationRepo ResourceRelationRepository, logger logiface.Logger) ResourceRelationResolver {
	return &defaultResourceRelationResolver{
		relationRepo: relationRepo,
		logger:       logger,
	}
}

// ResolveAPIPermissions 解析API权限需求
func (r *defaultResourceRelationResolver) ResolveAPIPermissions(ctx context.Context, apiResource *entity.Resource, pageContext *PageContext) ([]*PermissionRequirement, error) {
	if apiResource == nil {
		return nil, fmt.Errorf("API资源不能为空")
	}
	
	var permissions []*PermissionRequirement
	
	// 1. 如果有页面上下文，优先使用页面-API关系
	if pageContext != nil && pageContext.IsValid() {
		pagePermissions, err := r.ResolvePermissionsByRelation(ctx, 
			apiResource.ID, pageContext.PageResourceID, 
			pageContext.TenantID, pageContext.InternalAppID)
		if err != nil {
			r.logger.Warn(ctx, "解析页面-API关系权限失败",
				logiface.Error(err),
				logiface.Int64("api_resource_id", apiResource.ID),
				logiface.Int64("page_resource_id", pageContext.PageResourceID))
		} else if len(pagePermissions) > 0 {
			permissions = append(permissions, pagePermissions...)
			r.logger.Debug(ctx, "使用页面-API关系权限",
				logiface.Int64("api_resource_id", apiResource.ID),
				logiface.Int64("page_resource_id", pageContext.PageResourceID),
				logiface.Int("permission_count", len(pagePermissions)))
		}
	}
	
	// 2. 如果没有找到页面关系权限，查找API的通用关系权限
	if len(permissions) == 0 {
		apiPermissions, err := r.getAPIRelationPermissions(ctx, apiResource)
		if err != nil {
			r.logger.Warn(ctx, "解析API关系权限失败",
				logiface.Error(err),
				logiface.Int64("api_resource_id", apiResource.ID))
		} else {
			permissions = append(permissions, apiPermissions...)
		}
	}
	
	// 3. 如果仍然没有权限，使用默认权限
	if len(permissions) == 0 {
		defaultPermissions, err := r.GetDefaultPermissions(ctx, apiResource)
		if err != nil {
			return nil, fmt.Errorf("获取默认权限失败: %v", err)
		}
		permissions = append(permissions, defaultPermissions...)
	}
	
	// 4. 去重和排序
	permissions = r.deduplicateAndSort(permissions)
	
	r.logger.Debug(ctx, "API权限解析完成",
		logiface.Int64("api_resource_id", apiResource.ID),
		logiface.String("api_name", apiResource.Name),
		logiface.Int("final_permission_count", len(permissions)))
	
	return permissions, nil
}

// getAPIRelationPermissions 获取API的关系权限
func (r *defaultResourceRelationResolver) getAPIRelationPermissions(ctx context.Context, apiResource *entity.Resource) ([]*PermissionRequirement, error) {
	relations, err := r.relationRepo.FindRelationsByAPI(ctx, apiResource.ID, apiResource.TenantID, apiResource.InternalAppID)
	if err != nil {
		return nil, fmt.Errorf("查询API关系失败: %v", err)
	}
	
	var permissions []*PermissionRequirement
	for _, relation := range relations {
		if !relation.IsActive() {
			continue
		}
		
		perm := &PermissionRequirement{
			PermissionCode: relation.PermissionCode,
			IsRequired:     relation.IsRequired,
			Priority:       relation.Priority,
			Source:         fmt.Sprintf("API关系[%d]", relation.ID),
		}
		
		permissions = append(permissions, perm)
	}
	
	return permissions, nil
}

// ResolvePermissionsByRelation 通过关系解析权限需求
func (r *defaultResourceRelationResolver) ResolvePermissionsByRelation(ctx context.Context, apiResourceID, pageResourceID, tenantID, internalAppID int64) ([]*PermissionRequirement, error) {
	relations, err := r.relationRepo.FindRelationsByPageAndAPI(ctx, pageResourceID, apiResourceID, tenantID, internalAppID)
	if err != nil {
		return nil, fmt.Errorf("查询页面-API关系失败: %v", err)
	}
	
	var permissions []*PermissionRequirement
	
	for _, relation := range relations {
		if !relation.IsActive() {
			continue
		}
		
		perm := &PermissionRequirement{
			PermissionCode: relation.PermissionCode,
			IsRequired:     relation.IsRequired,
			Priority:       relation.Priority,
			Source:         fmt.Sprintf("页面-API关系[%d]", relation.ID),
		}
		
		permissions = append(permissions, perm)
		
		r.logger.Debug(ctx, "找到页面-API权限关系",
			logiface.Int64("relation_id", relation.ID),
			logiface.String("permission_code", relation.PermissionCode),
			logiface.Bool("is_required", relation.IsRequired),
			logiface.Int("priority", relation.Priority))
	}
	
	return permissions, nil
}

// GetDefaultPermissions 获取API的默认权限需求
func (r *defaultResourceRelationResolver) GetDefaultPermissions(ctx context.Context, apiResource *entity.Resource) ([]*PermissionRequirement, error) {
	// 如果API资源本身就是公开的，不需要权限
	if apiResource.IsPublicResource() {
		r.logger.Debug(ctx, "API资源为公开资源，无需权限",
			logiface.Int64("api_resource_id", apiResource.ID),
			logiface.String("api_name", apiResource.Name))
		return []*PermissionRequirement{}, nil
	}
	
	// 默认需要访问该API的基础权限
	defaultPermCode := r.generateDefaultPermissionCode(apiResource)
	
	defaultPerm := &PermissionRequirement{
		PermissionCode: defaultPermCode,
		IsRequired:     true,
		Priority:       0,
		Source:         "默认权限",
	}
	
	r.logger.Debug(ctx, "使用API默认权限",
		logiface.Int64("api_resource_id", apiResource.ID),
		logiface.String("api_name", apiResource.Name),
		logiface.String("permission_code", defaultPermCode))
	
	return []*PermissionRequirement{defaultPerm}, nil
}

// generateDefaultPermissionCode 生成默认权限编码
func (r *defaultResourceRelationResolver) generateDefaultPermissionCode(apiResource *entity.Resource) string {
	// 根据API路径和方法生成权限编码
	if apiResource.Name != "" {
		return fmt.Sprintf("api:%s", apiResource.Name)
	}
	
	if apiResource.Path != "" && apiResource.APIMethod != "" {
		return fmt.Sprintf("api:%s:%s", apiResource.APIMethod, apiResource.Path)
	}
	
	return fmt.Sprintf("api:resource:%d", apiResource.ID)
}

// deduplicateAndSort 去重和排序权限
func (r *defaultResourceRelationResolver) deduplicateAndSort(permissions []*PermissionRequirement) []*PermissionRequirement {
	if len(permissions) <= 1 {
		return permissions
	}
	
	// 使用map去重，以权限编码为key
	permMap := make(map[string]*PermissionRequirement)
	
	for _, perm := range permissions {
		key := perm.PermissionCode
		if existing, exists := permMap[key]; exists {
			// 如果已存在，选择优先级更高的
			if perm.Priority > existing.Priority {
				permMap[key] = perm
			} else if perm.Priority == existing.Priority && perm.IsRequired && !existing.IsRequired {
				// 相同优先级时，选择必需的权限
				permMap[key] = perm
			}
		} else {
			permMap[key] = perm
		}
	}
	
	// 转换为切片
	result := make([]*PermissionRequirement, 0, len(permMap))
	for _, perm := range permMap {
		result = append(result, perm)
	}
	
	// 按优先级排序（降序）
	sort.Slice(result, func(i, j int) bool {
		if result[i].Priority != result[j].Priority {
			return result[i].Priority > result[j].Priority
		}
		// 优先级相同时，必需权限排在前面
		if result[i].IsRequired != result[j].IsRequired {
			return result[i].IsRequired
		}
		// 最后按权限编码排序
		return result[i].PermissionCode < result[j].PermissionCode
	})
	
	return result
}