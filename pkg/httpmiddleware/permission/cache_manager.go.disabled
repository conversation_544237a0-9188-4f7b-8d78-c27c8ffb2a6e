package permission

import (
	"context"
	"fmt"
	"sync"
	"time"

	commonResource "gitee.com/heiyee/platforms/pkg/common/resource"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// CacheConfig 缓存配置
type CacheConfig struct {
	// 资源缓存TTL
	ResourceTTL time.Duration
	// 权限缓存TTL
	PermissionTTL time.Duration
	// 关系缓存TTL
	RelationTTL time.Duration
	// 缓存清理间隔
	CleanupInterval time.Duration
	// 最大缓存条目数
	MaxEntries int
}

// DefaultCacheConfig 默认缓存配置
func DefaultCacheConfig() *CacheConfig {
	return &CacheConfig{
		ResourceTTL:     5 * time.Minute,
		PermissionTTL:   3 * time.Minute,
		RelationTTL:     5 * time.Minute,
		CleanupInterval: 1 * time.Minute,
		MaxEntries:      10000,
	}
}

// CacheItem 缓存项
type CacheItem struct {
	Value     interface{}
	ExpiresAt time.Time
	AccessAt  time.Time
}

// IsExpired 检查是否过期
func (item *CacheItem) IsExpired() bool {
	return time.Now().After(item.ExpiresAt)
}

// UpdateAccess 更新访问时间
func (item *CacheItem) UpdateAccess() {
	item.AccessAt = time.Now()
}

// PermissionCacheManager 权限缓存管理器接口
type PermissionCacheManager interface {
	// 资源缓存
	GetAPIResources(tenantID, internalAppID int64) ([]*entity.Resource, bool)
	SetAPIResources(tenantID, internalAppID int64, resources []*entity.Resource)
	
	// 权限缓存
	GetUserPermissions(userID, tenantID, internalAppID int64) ([]string, bool)
	SetUserPermissions(userID, tenantID, internalAppID int64, permissions []string)
	
	// 关系缓存
	GetResourceRelations(tenantID, internalAppID int64) ([]*entity.ResourceRelation, bool)
	SetResourceRelations(tenantID, internalAppID int64, relations []*entity.ResourceRelation)
	
	// 页面-API关系缓存
	GetPageAPIRelations(pageResourceID, apiResourceID, tenantID, internalAppID int64) ([]*entity.ResourceRelation, bool)
	SetPageAPIRelations(pageResourceID, apiResourceID, tenantID, internalAppID int64, relations []*entity.ResourceRelation)
	
	// 缓存管理
	InvalidateUser(userID, tenantID, internalAppID int64)
	InvalidateApp(tenantID, internalAppID int64)
	InvalidateAll()
	GetStats() *CacheStats
	
	// 生命周期
	Start() error
	Stop() error
}

// CacheStats 缓存统计信息
type CacheStats struct {
	TotalEntries    int
	ResourceEntries int
	PermissionEntries int
	RelationEntries int
	HitCount        int64
	MissCount       int64
	HitRate         float64
}

// defaultPermissionCacheManager 默认权限缓存管理器实现
type defaultPermissionCacheManager struct {
	config    *CacheConfig
	cache     map[string]*CacheItem
	mutex     sync.RWMutex
	logger    logiface.Logger
	stopCh    chan struct{}
	hitCount  int64
	missCount int64
}

// NewPermissionCacheManager 创建权限缓存管理器
func NewPermissionCacheManager(config *CacheConfig, logger logiface.Logger) PermissionCacheManager {
	if config == nil {
		config = DefaultCacheConfig()
	}
	
	return &defaultPermissionCacheManager{
		config: config,
		cache:  make(map[string]*CacheItem),
		logger: logger,
		stopCh: make(chan struct{}),
	}
}

// Start 启动缓存管理器
func (m *defaultPermissionCacheManager) Start() error {
	go m.cleanupLoop()
	m.logger.Info(context.Background(), "权限缓存管理器已启动",
		logiface.Duration("resource_ttl", m.config.ResourceTTL),
		logiface.Duration("permission_ttl", m.config.PermissionTTL),
		logiface.Duration("relation_ttl", m.config.RelationTTL))
	return nil
}

// Stop 停止缓存管理器
func (m *defaultPermissionCacheManager) Stop() error {
	close(m.stopCh)
	m.logger.Info(context.Background(), "权限缓存管理器已停止")
	return nil
}

// cleanupLoop 清理循环
func (m *defaultPermissionCacheManager) cleanupLoop() {
	ticker := time.NewTicker(m.config.CleanupInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			m.cleanup()
		case <-m.stopCh:
			return
		}
	}
}

// cleanup 清理过期缓存
func (m *defaultPermissionCacheManager) cleanup() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	now := time.Now()
	expiredKeys := make([]string, 0)
	
	for key, item := range m.cache {
		if item.IsExpired() {
			expiredKeys = append(expiredKeys, key)
		}
	}
	
	for _, key := range expiredKeys {
		delete(m.cache, key)
	}
	
	if len(expiredKeys) > 0 {
		m.logger.Debug(context.Background(), "清理过期缓存",
			logiface.Int("expired_count", len(expiredKeys)),
			logiface.Int("total_entries", len(m.cache)))
	}
	
	// 如果缓存条目过多，清理最旧的访问记录
	if len(m.cache) > m.config.MaxEntries {
		m.evictOldest(len(m.cache) - m.config.MaxEntries)
	}
}

// evictOldest 驱逐最旧的缓存条目
func (m *defaultPermissionCacheManager) evictOldest(count int) {
	if count <= 0 {
		return
	}
	
	type keyTime struct {
		key      string
		accessAt time.Time
	}
	
	keyTimes := make([]keyTime, 0, len(m.cache))
	for key, item := range m.cache {
		keyTimes = append(keyTimes, keyTime{key: key, accessAt: item.AccessAt})
	}
	
	// 按访问时间排序
	for i := 0; i < len(keyTimes)-1; i++ {
		for j := i + 1; j < len(keyTimes); j++ {
			if keyTimes[i].accessAt.After(keyTimes[j].accessAt) {
				keyTimes[i], keyTimes[j] = keyTimes[j], keyTimes[i]
			}
		}
	}
	
	// 删除最旧的条目
	for i := 0; i < count && i < len(keyTimes); i++ {
		delete(m.cache, keyTimes[i].key)
	}
	
	m.logger.Debug(context.Background(), "驱逐最旧缓存条目",
		logiface.Int("evicted_count", count),
		logiface.Int("remaining_entries", len(m.cache)))
}

// set 设置缓存项
func (m *defaultPermissionCacheManager) set(key string, value interface{}, ttl time.Duration) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	m.cache[key] = &CacheItem{
		Value:     value,
		ExpiresAt: time.Now().Add(ttl),
		AccessAt:  time.Now(),
	}
}

// get 获取缓存项
func (m *defaultPermissionCacheManager) get(key string) (interface{}, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	item, exists := m.cache[key]
	if !exists {
		m.missCount++
		return nil, false
	}
	
	if item.IsExpired() {
		m.missCount++
		return nil, false
	}
	
	item.UpdateAccess()
	m.hitCount++
	return item.Value, true
}

// delete 删除缓存项
func (m *defaultPermissionCacheManager) delete(key string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	delete(m.cache, key)
}

// deleteByPrefix 删除指定前缀的缓存项
func (m *defaultPermissionCacheManager) deleteByPrefix(prefix string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	keysToDelete := make([]string, 0)
	for key := range m.cache {
		if len(key) >= len(prefix) && key[:len(prefix)] == prefix {
			keysToDelete = append(keysToDelete, key)
		}
	}
	
	for _, key := range keysToDelete {
		delete(m.cache, key)
	}
}

// GetAPIResources 获取API资源缓存
func (m *defaultPermissionCacheManager) GetAPIResources(tenantID, internalAppID int64) ([]*entity.Resource, bool) {
	key := fmt.Sprintf("resources:%d:%d", tenantID, internalAppID)
	if value, ok := m.get(key); ok {
		if resources, ok := value.([]*entity.Resource); ok {
			return resources, true
		}
	}
	return nil, false
}

// SetAPIResources 设置API资源缓存
func (m *defaultPermissionCacheManager) SetAPIResources(tenantID, internalAppID int64, resources []*entity.Resource) {
	key := fmt.Sprintf("resources:%d:%d", tenantID, internalAppID)
	m.set(key, resources, m.config.ResourceTTL)
}

// GetUserPermissions 获取用户权限缓存
func (m *defaultPermissionCacheManager) GetUserPermissions(userID, tenantID, internalAppID int64) ([]string, bool) {
	key := fmt.Sprintf("permissions:%d:%d:%d", userID, tenantID, internalAppID)
	if value, ok := m.get(key); ok {
		if permissions, ok := value.([]string); ok {
			return permissions, true
		}
	}
	return nil, false
}

// SetUserPermissions 设置用户权限缓存
func (m *defaultPermissionCacheManager) SetUserPermissions(userID, tenantID, internalAppID int64, permissions []string) {
	key := fmt.Sprintf("permissions:%d:%d:%d", userID, tenantID, internalAppID)
	m.set(key, permissions, m.config.PermissionTTL)
}

// GetResourceRelations 获取资源关系缓存
func (m *defaultPermissionCacheManager) GetResourceRelations(tenantID, internalAppID int64) ([]*entity.ResourceRelation, bool) {
	key := fmt.Sprintf("relations:%d:%d", tenantID, internalAppID)
	if value, ok := m.get(key); ok {
		if relations, ok := value.([]*entity.ResourceRelation); ok {
			return relations, true
		}
	}
	return nil, false
}

// SetResourceRelations 设置资源关系缓存
func (m *defaultPermissionCacheManager) SetResourceRelations(tenantID, internalAppID int64, relations []*entity.ResourceRelation) {
	key := fmt.Sprintf("relations:%d:%d", tenantID, internalAppID)
	m.set(key, relations, m.config.RelationTTL)
}

// GetPageAPIRelations 获取页面-API关系缓存
func (m *defaultPermissionCacheManager) GetPageAPIRelations(pageResourceID, apiResourceID, tenantID, internalAppID int64) ([]*entity.ResourceRelation, bool) {
	key := fmt.Sprintf("page_api_relations:%d:%d:%d:%d", pageResourceID, apiResourceID, tenantID, internalAppID)
	if value, ok := m.get(key); ok {
		if relations, ok := value.([]*entity.ResourceRelation); ok {
			return relations, true
		}
	}
	return nil, false
}

// SetPageAPIRelations 设置页面-API关系缓存
func (m *defaultPermissionCacheManager) SetPageAPIRelations(pageResourceID, apiResourceID, tenantID, internalAppID int64, relations []*entity.ResourceRelation) {
	key := fmt.Sprintf("page_api_relations:%d:%d:%d:%d", pageResourceID, apiResourceID, tenantID, internalAppID)
	m.set(key, relations, m.config.RelationTTL)
}

// InvalidateUser 失效用户相关缓存
func (m *defaultPermissionCacheManager) InvalidateUser(userID, tenantID, internalAppID int64) {
	prefix := fmt.Sprintf("permissions:%d:%d:%d", userID, tenantID, internalAppID)
	m.delete(prefix)
	
	m.logger.Debug(context.Background(), "用户权限缓存已失效",
		logiface.Int64("user_id", userID),
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("internal_app_id", internalAppID))
}

// InvalidateApp 失效应用相关缓存
func (m *defaultPermissionCacheManager) InvalidateApp(tenantID, internalAppID int64) {
	prefixes := []string{
		fmt.Sprintf("resources:%d:%d", tenantID, internalAppID),
		fmt.Sprintf("relations:%d:%d", tenantID, internalAppID),
		fmt.Sprintf("permissions:%d:%d", tenantID, internalAppID),
		fmt.Sprintf("page_api_relations:%d:%d", tenantID, internalAppID),
	}
	
	for _, prefix := range prefixes {
		m.deleteByPrefix(prefix)
	}
	
	m.logger.Info(context.Background(), "应用相关缓存已失效",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("internal_app_id", internalAppID))
}

// InvalidateAll 失效所有缓存
func (m *defaultPermissionCacheManager) InvalidateAll() {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	m.cache = make(map[string]*CacheItem)
	m.hitCount = 0
	m.missCount = 0
	
	m.logger.Info(context.Background(), "所有权限缓存已清空")
}

// GetStats 获取缓存统计信息
func (m *defaultPermissionCacheManager) GetStats() *CacheStats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	stats := &CacheStats{
		TotalEntries: len(m.cache),
		HitCount:     m.hitCount,
		MissCount:    m.missCount,
	}
	
	// 计算命中率
	totalRequests := stats.HitCount + stats.MissCount
	if totalRequests > 0 {
		stats.HitRate = float64(stats.HitCount) / float64(totalRequests)
	}
	
	// 统计不同类型的缓存条目
	for key := range m.cache {
		if len(key) >= 9 && key[:9] == "resources" {
			stats.ResourceEntries++
		} else if len(key) >= 11 && key[:11] == "permissions" {
			stats.PermissionEntries++
		} else if len(key) >= 9 && key[:9] == "relations" {
			stats.RelationEntries++
		}
	}
	
	return stats
}