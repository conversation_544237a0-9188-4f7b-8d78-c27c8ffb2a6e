package grpcmiddleware

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"

	"gitee.com/heiyee/platforms/pkg/common"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
)

// ClientContextInterceptor 客户端上下文拦截器
type ClientContextInterceptor struct {
	logger logiface.Logger
}

// NewClientContextInterceptor 创建客户端上下文拦截器
func NewClientContextInterceptor(logger logiface.Logger) *ClientContextInterceptor {
	return &ClientContextInterceptor{
		logger: logger,
	}
}

// UnaryClientInterceptor 一元调用客户端拦截器
func (i *ClientContextInterceptor) UnaryClientInterceptor() grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		// 1. 提取上下文信息
		ctx = i.injectContext(ctx)

		// 2. 记录请求开始
		start := time.Now()
		requestID, _ := usercontext.GetRequestID(ctx)

		i.logger.Info(ctx, "gRPC client request started",
			logiface.String("method", method),
			logiface.String("request_id", requestID),
		)

		// 3. 执行调用
		err := invoker(ctx, method, req, reply, cc, opts...)

		// 4. 记录请求结果
		duration := time.Since(start)
		if err != nil {
			st, _ := status.FromError(err)
			i.logger.Error(ctx, "gRPC client request failed",
				logiface.String("method", method),
				logiface.String("request_id", requestID),
				logiface.Duration("duration", duration),
				logiface.Error(err),
				logiface.Int("code", int(st.Code())),
			)
		} else {
			i.logger.Info(ctx, "gRPC client request completed",
				logiface.String("method", method),
				logiface.String("request_id", requestID),
				logiface.Duration("duration", duration),
			)
		}

		return err
	}
}

// StreamClientInterceptor 流式调用客户端拦截器
func (i *ClientContextInterceptor) StreamClientInterceptor() grpc.StreamClientInterceptor {
	return func(ctx context.Context, desc *grpc.StreamDesc, cc *grpc.ClientConn, method string, streamer grpc.Streamer, opts ...grpc.CallOption) (grpc.ClientStream, error) {
		// 1. 提取上下文信息
		ctx = i.injectContext(ctx)

		// 2. 记录流请求开始
		requestID, _ := usercontext.GetRequestID(ctx)
		i.logger.Info(ctx, "gRPC client stream started",
			logiface.String("method", method),
			logiface.String("request_id", requestID),
		)

		// 3. 执行流调用
		stream, err := streamer(ctx, desc, cc, method, opts...)
		if err != nil {
			st, _ := status.FromError(err)
			i.logger.Error(ctx, "gRPC client stream failed",
				logiface.String("method", method),
				logiface.String("request_id", requestID),
				logiface.Error(err),
				logiface.Int("code", int(st.Code())),
			)
		}

		return stream, err
	}
}

// injectContext 注入上下文信息到gRPC元数据
func (i *ClientContextInterceptor) injectContext(ctx context.Context) context.Context {
	// 1. 获取用户信息（从HTTP中间件保存的上下文中获取）
	userInfo, hasUser := usercontext.GetUserInfo(ctx)
	appInfo, hasApp := usercontext.GetAppInfo(ctx)
	requestID, hasRequestID := usercontext.GetRequestID(ctx)
	traceID, hasTraceID := usercontext.GetTraceID(ctx)

	if !hasUser && !hasApp && !hasRequestID && !hasTraceID {
		return ctx
	}

	// 2. 构建元数据
	md := metadata.New(map[string]string{
		"x-timestamp": fmt.Sprintf("%d", time.Now().Unix()),
	})

	// 添加用户信息
	if hasUser && userInfo != nil {
		md.Set(common.HeaderUserID, fmt.Sprintf("%d", userInfo.UserID))
		if userInfo.Username != "" {
			md.Set("x-username", userInfo.Username)
		}
		if userInfo.RealName != "" {
			// URL编码real-name字段，避免gRPC请求头包含非ASCII字符
			encodedRealName := url.QueryEscape(userInfo.RealName)
			md.Set("x-real-name", encodedRealName)
		}
		if userInfo.Email != "" {
			md.Set("x-email", userInfo.Email)
		}
	}

	// 添加应用信息（补全所有字段）
	if hasApp && appInfo != nil {
		// 租户ID（服务间头，禁止对外暴露）
		if appInfo.TenantID != 0 {
			md.Set(common.ContextTenantID, fmt.Sprintf("%d", appInfo.TenantID))
		}
		// 外部应用ID
		if appInfo.AppId != "" {
			md.Set(common.HeaderAppId, appInfo.AppId)
		}
		// 内部应用ID（用于资源隔离）
		if appInfo.InternalAppId != 0 {
			md.Set("internal-app-id", fmt.Sprintf("%d", appInfo.InternalAppId))
		}
	}

	// 添加请求信息
	if hasRequestID {
		md.Set(common.HeaderRequestID, requestID)
	}

	// 添加追踪信息
	if hasTraceID {
		md.Set("x-trace-id", traceID)
	}

	// 3. 合并现有元数据
	if existingMD, ok := metadata.FromOutgoingContext(ctx); ok {
		md = metadata.Join(existingMD, md)
	}

	return metadata.NewOutgoingContext(ctx, md)
}
