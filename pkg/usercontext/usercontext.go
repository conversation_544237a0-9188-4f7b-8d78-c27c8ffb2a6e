package usercontext

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

// 错误常量
var (
	ErrUserNotAuthenticated = errors.New("user not authenticated")
	ErrTenantNotFound       = errors.New("tenant not found")
	ErrUserNotFound         = errors.New("user not found")
	ErrInvalidUserInfo      = errors.New("invalid user info")
	ErrInvalidTenantInfo    = errors.New("invalid tenant info")
)

// ContextKey 类型安全的上下文键
type ContextKey string

const (
	// UserInfoKey 用户信息上下文键
	UserInfoKey ContextKey = "user_info"
	// AppInfoKey 应用信息上下文键
	AppInfoKey ContextKey = "app_info"
	// RequestIDKey 请求ID上下文键
	RequestIDKey ContextKey = "request_id"
	// TraceIDKey 链路追踪ID上下文键
	TraceIDKey ContextKey = "trace_id"
)

// 注意：角色常量已移除，因为角色检查功能已移除

// UserInfo 用户信息结构体（与 gRPC 接口保持一致）
type UserInfo struct {
	UserID        int64  `json:"user_id"`
	Username      string `json:"username"`
	RealName      string `json:"real_name"`
	Email         string `json:"email"`
	TenantID      int64  `json:"tenant_id"`
	InternalAppID int64  `json:"internal_app_id"` // 内部应用ID
	AppId         string `json:"app_id"`          // 外部应用ID
}

// AppInfo 租户信息结构体
type AppInfo struct {
	TenantID      int64  `json:"tenant_id"`
	AppId         string `json:"app_id"`
	InternalAppId int64  `json:"internal_app_id"`
}

// RequestInfo 请求信息结构体
type RequestInfo struct {
	RequestID string    `json:"request_id"`
	TraceID   string    `json:"trace_id"`
	UserAgent string    `json:"user_agent,omitempty"`
	ClientIP  string    `json:"client_ip,omitempty"`
	Method    string    `json:"method,omitempty"`
	Path      string    `json:"path,omitempty"`
	StartTime time.Time `json:"start_time,omitempty"`
}

// SetUserInfo 设置用户信息到上下文
func SetUserInfo(ctx context.Context, userInfo *UserInfo) context.Context {
	if userInfo == nil {
		return ctx
	}
	return context.WithValue(ctx, UserInfoKey, userInfo)
}

// GetUserInfo 从上下文获取用户信息
func GetUserInfo(ctx context.Context) (*UserInfo, bool) {
	if ctx == nil {
		return nil, false
	}
	userInfo, ok := ctx.Value(UserInfoKey).(*UserInfo)
	return userInfo, ok
}

// MustGetUserInfo 从上下文获取用户信息，如果不存在则panic
func MustGetUserInfo(ctx context.Context) *UserInfo {
	userInfo, ok := GetUserInfo(ctx)
	if !ok {
		panic("user info not found in context")
	}
	return userInfo
}

// SetAppInfo 设置租户信息到上下文
func SetAppInfo(ctx context.Context, appInfo *AppInfo) context.Context {
	if appInfo == nil {
		return ctx
	}
	return context.WithValue(ctx, AppInfoKey, appInfo)
}

// GetAppInfo 从上下文获取应用信息
func GetAppInfo(ctx context.Context) (*AppInfo, bool) {
	if ctx == nil {
		return nil, false
	}
	appInfo, ok := ctx.Value(AppInfoKey).(*AppInfo)
	return appInfo, ok
}

// SetRequestInfo 设置请求信息到上下文
func SetRequestInfo(ctx context.Context, requestInfo *RequestInfo) context.Context {
	if requestInfo == nil {
		return ctx
	}
	return context.WithValue(ctx, RequestIDKey, requestInfo)
}

// GetRequestInfo 从上下文获取请求信息
func GetRequestInfo(ctx context.Context) (*RequestInfo, bool) {
	if ctx == nil {
		return nil, false
	}
	requestInfo, ok := ctx.Value(RequestIDKey).(*RequestInfo)
	return requestInfo, ok
}

// MustGetRequestInfo 从上下文获取请求信息，如果不存在则panic
func MustGetRequestInfo(ctx context.Context) *RequestInfo {
	requestInfo, ok := GetRequestInfo(ctx)
	if !ok {
		panic("request info not found in context")
	}
	return requestInfo
}

// SetRequestID 设置请求ID到上下文
func SetRequestID(ctx context.Context, requestID string) context.Context {
	if requestID == "" {
		return ctx
	}
	return context.WithValue(ctx, RequestIDKey, requestID)
}

// GetRequestID 从上下文获取请求ID
func GetRequestID(ctx context.Context) (string, bool) {
	if ctx == nil {
		return "", false
	}
	requestID, ok := ctx.Value(RequestIDKey).(string)
	return requestID, ok
}

// MustGetRequestID 从上下文获取请求ID，如果不存在则panic
func MustGetRequestID(ctx context.Context) string {
	requestID, ok := GetRequestID(ctx)
	if !ok {
		panic("request id not found in context")
	}
	return requestID
}

// SetTraceID 设置链路追踪ID到上下文
func SetTraceID(ctx context.Context, traceID string) context.Context {
	if traceID == "" {
		return ctx
	}
	return context.WithValue(ctx, TraceIDKey, traceID)
}

// GetTraceID 从上下文获取链路追踪ID
func GetTraceID(ctx context.Context) (string, bool) {
	if ctx == nil {
		return "", false
	}
	traceID, ok := ctx.Value(TraceIDKey).(string)
	return traceID, ok
}

// MustGetTraceID 从上下文获取链路追踪ID，如果不存在则panic
func MustGetTraceID(ctx context.Context) string {
	traceID, ok := GetTraceID(ctx)
	if !ok {
		panic("trace id not found in context")
	}
	return traceID
}

// RequireAuth 要求用户已认证，如果未认证则返回错误
func RequireAuth(ctx context.Context) (*UserInfo, error) {
	userInfo, ok := GetUserInfo(ctx)
	if !ok {
		return nil, fmt.Errorf("user not authenticated")
	}
	if userInfo == nil {
		return nil, fmt.Errorf("user info is nil")
	}
	return userInfo, nil
}

// IsSameTenant 检查用户是否属于指定租户
func IsSameTenant(ctx context.Context, tenantID int64) bool {
	userInfo, ok := GetUserInfo(ctx)
	if !ok || userInfo == nil {
		return false
	}
	return userInfo.TenantID == tenantID
}

// IsSameUser 检查用户ID是否匹配
func IsSameUser(ctx context.Context, userID int64) bool {
	userInfo, ok := GetUserInfo(ctx)
	if !ok || userInfo == nil {
		return false
	}
	return userInfo.UserID == userID
}

// GetUserID 获取用户ID
func GetUserID(ctx context.Context) (int64, bool) {
	userInfo, ok := GetUserInfo(ctx)
	if !ok || userInfo == nil {
		return 0, false
	}
	return userInfo.UserID, true
}

// MustGetUserID 获取用户ID，如果不存在则panic
func MustGetUserID(ctx context.Context) int64 {
	userInfo := MustGetUserInfo(ctx)
	return userInfo.UserID
}

// GetTenantID 获取租户ID
func GetTenantID(ctx context.Context) (int64, bool) {
	appInfo, ok := GetAppInfo(ctx)
	if !ok || appInfo == nil {
		return 0, false
	}
	return appInfo.TenantID, true
}

// MustGetTenantID 获取租户ID，如果不存在则panic
func MustGetTenantID(ctx context.Context) int64 {
	appInfo := MustGetUserInfo(ctx)
	return appInfo.TenantID
}

// GetInternalAppID 获取内部应用ID
func GetInternalAppID(ctx context.Context) (int64, bool) {
	appInfo, ok := getAppInfoCached(ctx)
	if !ok || appInfo == nil {
		return 0, false
	}
	return appInfo.InternalAppId, true
}

// MustGetInternalAppID 获取内部应用ID，如果不存在则panic
func MustGetInternalAppID(ctx context.Context) int64 {
	appInfo, _ := getAppInfoCached(ctx)
	return appInfo.InternalAppId
}

// GetAppId 获取外部应用ID
func GetAppId(ctx context.Context) (string, bool) {
	appInfo, ok := getAppInfoCached(ctx)
	if !ok || appInfo == nil {
		return "", false
	}
	return appInfo.AppId, true
}

// MustGetAppId 获取外部应用ID，如果不存在则panic
func MustGetAppId(ctx context.Context) string {
	appInfo, _ := getAppInfoCached(ctx)
	return appInfo.AppId
}

// GetUsername 获取用户名
func GetUsername(ctx context.Context) (string, bool) {
	userInfo, ok := GetUserInfo(ctx)
	if !ok || userInfo == nil {
		return "", false
	}
	return userInfo.Username, true
}

// MustGetUsername 获取用户名，如果不存在则panic
func MustGetUsername(ctx context.Context) string {
	userInfo := MustGetUserInfo(ctx)
	return userInfo.Username
}

// 注意：以下管理员检查功能已移除，因为角色检查功能已移除
// IsAdmin, IsSuperAdmin, IsTenantAdmin

// 内部优化函数：获取用户信息并缓存结果
func getUserInfoCached(ctx context.Context) (*UserInfo, bool) {
	if ctx == nil {
		return nil, false
	}
	userInfo, ok := ctx.Value(UserInfoKey).(*UserInfo)
	return userInfo, ok
}

// 内部优化函数：获取应用信息并缓存结果
func getAppInfoCached(ctx context.Context) (*AppInfo, bool) {
	if ctx == nil {
		return nil, false
	}
	appInfo, ok := ctx.Value(AppInfoKey).(*AppInfo)
	return appInfo, ok
}

// 内部优化函数：检查字符串是否在切片中
func containsString(slice []string, target string) bool {
	for _, item := range slice {
		if item == target {
			return true
		}
	}
	return false
}

// 内部优化函数：检查是否包含任意一个字符串
func containsAnyString(slice []string, targets ...string) bool {
	for _, target := range targets {
		if containsString(slice, target) {
			return true
		}
	}
	return false
}

// 内部优化函数：检查是否包含所有字符串
func containsAllStrings(slice []string, targets ...string) bool {
	for _, target := range targets {
		if !containsString(slice, target) {
			return false
		}
	}
	return true
}

// SetAllInfo 批量设置所有信息到上下文
func SetAllInfo(ctx context.Context, userInfo *UserInfo, appInfo *AppInfo, requestID, traceID string) context.Context {
	if userInfo != nil {
		ctx = SetUserInfo(ctx, userInfo)
	}
	if appInfo != nil {
		ctx = SetAppInfo(ctx, appInfo)
	}
	if requestID != "" {
		ctx = SetRequestID(ctx, requestID)
	}
	if traceID != "" {
		ctx = SetTraceID(ctx, traceID)
	}
	return ctx
}

// SetUserAndAppInfo 设置用户和应用信息
func SetUserAndAppInfo(ctx context.Context, userInfo *UserInfo, appInfo *AppInfo) context.Context {
	if userInfo != nil {
		ctx = SetUserInfo(ctx, userInfo)
	}
	if appInfo != nil {
		ctx = SetAppInfo(ctx, appInfo)
	}
	return ctx
}

// SetRequestAndTraceInfo 设置请求和链路追踪信息
func SetRequestAndTraceInfo(ctx context.Context, requestID, traceID string) context.Context {
	if requestID != "" {
		ctx = SetRequestID(ctx, requestID)
	}
	if traceID != "" {
		ctx = SetTraceID(ctx, traceID)
	}
	return ctx
}

// GetUserAndAppInfo 获取用户和应用信息
func GetUserAndAppInfo(ctx context.Context) (*UserInfo, *AppInfo) {
	userInfo, _ := GetUserInfo(ctx)
	appInfo, _ := GetAppInfo(ctx)
	return userInfo, appInfo
}

// GetRequestAndTraceInfo 获取请求和链路追踪信息
func GetRequestAndTraceInfo(ctx context.Context) (requestID, traceID string) {
	requestID, _ = GetRequestID(ctx)
	traceID, _ = GetTraceID(ctx)
	return requestID, traceID
}

// WithUserInfo 装饰器模式：为函数添加用户信息上下文
func WithUserInfo(userInfo *UserInfo) func(context.Context) context.Context {
	return func(ctx context.Context) context.Context {
		return SetUserInfo(ctx, userInfo)
	}
}

// WithAppInfo 装饰器模式：为函数添加应用信息上下文
func WithAppInfo(appInfo *AppInfo) func(context.Context) context.Context {
	return func(ctx context.Context) context.Context {
		return SetAppInfo(ctx, appInfo)
	}
}

// WithRequestInfo 装饰器模式：为函数添加请求信息上下文
func WithRequestInfo(requestID, traceID string) func(context.Context) context.Context {
	return func(ctx context.Context) context.Context {
		ctx = SetRequestID(ctx, requestID)
		ctx = SetTraceID(ctx, traceID)
		return ctx
	}
}

// WithAllInfo 装饰器模式：为函数添加所有信息上下文
func WithAllInfo(userInfo *UserInfo, appInfo *AppInfo, requestID, traceID string) func(context.Context) context.Context {
	return func(ctx context.Context) context.Context {
		return SetAllInfo(ctx, userInfo, appInfo, requestID, traceID)
	}
}

// ApplyDecorators 应用多个装饰器到上下文
func ApplyDecorators(ctx context.Context, decorators ...func(context.Context) context.Context) context.Context {
	for _, decorator := range decorators {
		ctx = decorator(ctx)
	}
	return ctx
}

// 双重注入支持函数

// InjectUserAndApp 双重注入：同时注入到gin.Context和context.Context
func InjectUserAndApp(c *gin.Context, userInfo *UserInfo, appInfo *AppInfo) {
	// 注入到gin.Context
	c.Set(string(UserInfoKey), userInfo)
	c.Set(string(AppInfoKey), appInfo)

	// 注入到context.Context
	ctx := SetUserAndAppInfo(c.Request.Context(), userInfo, appInfo)
	c.Request = c.Request.WithContext(ctx)
}

// GetUserInfoFromGinContext 从gin.Context获取用户信息
func GetUserInfoFromGinContext(c *gin.Context) (*UserInfo, bool) {
	user, exists := c.Get(string(UserInfoKey))
	if !exists {
		return nil, false
	}
	userInfo, ok := user.(*UserInfo)
	return userInfo, ok
}

// GetAppInfoFromGinContext 从gin.Context获取应用信息
func GetAppInfoFromGinContext(c *gin.Context) (*AppInfo, bool) {
	app, exists := c.Get(string(AppInfoKey))
	if !exists {
		return nil, false
	}
	appInfo, ok := app.(*AppInfo)
	return appInfo, ok
}

// GetUserInfoFromGinRequestContext 从gin.Context获取context.Context中的用户信息
func GetUserInfoFromGinRequestContext(c *gin.Context) (*UserInfo, bool) {
	return GetUserInfo(c.Request.Context())
}

// GetAppInfoFromGinRequestContext 从gin.Context获取context.Context中的应用信息
func GetAppInfoFromGinRequestContext(c *gin.Context) (*AppInfo, bool) {
	return GetAppInfo(c.Request.Context())
}

// SetUserInfoToGinContext 将用户信息注入到gin.Context
func SetUserInfoToGinContext(c *gin.Context, userInfo *UserInfo) {
	c.Set(string(UserInfoKey), userInfo)
}

// SetAppInfoToGinContext 将应用信息注入到gin.Context
func SetAppInfoToGinContext(c *gin.Context, appInfo *AppInfo) {
	c.Set(string(AppInfoKey), appInfo)
}

// SetUserAndAppInfoToGinContext 将用户和应用信息同时注入到gin.Context
func SetUserAndAppInfoToGinContext(c *gin.Context, userInfo *UserInfo, appInfo *AppInfo) {
	SetUserInfoToGinContext(c, userInfo)
	SetAppInfoToGinContext(c, appInfo)
}

// ==================== gRPC 相关功能 ====================

// UserContextUnaryInterceptor gRPC一元调用用户上下文拦截器
// 从gRPC元数据中提取用户信息并注入到context，确保与HTTP获取的context完全一致
func UserContextUnaryInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// 从元数据中提取用户信息
		userInfo := extractUserInfoFromMetadata(ctx)
		if userInfo != nil {
			ctx = SetUserInfo(ctx, userInfo)
		}

		// 从元数据中提取应用信息
		appInfo := extractAppInfoFromMetadata(ctx)
		if appInfo != nil {
			ctx = SetAppInfo(ctx, appInfo)
		}

		// 从元数据中提取请求ID和追踪ID
		requestID, traceID := extractRequestInfoFromMetadata(ctx)
		if requestID != "" {
			ctx = SetRequestID(ctx, requestID)
		}
		if traceID != "" {
			ctx = SetTraceID(ctx, traceID)
		}

		return handler(ctx, req)
	}
}

// UserContextStreamInterceptor gRPC流调用用户上下文拦截器
func UserContextStreamInterceptor() grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		ctx := extractUserAndAppFromMetadata(ss.Context())
		wrappedStream := &wrappedServerStream{
			ServerStream: ss,
			ctx:          ctx,
		}
		return handler(srv, wrappedStream)
	}
}

// UserContextUnaryClientInterceptor gRPC客户端一元调用用户上下文拦截器
// 将用户和租户信息注入到metadata中，确保与HTTP发送的context完全一致
func UserContextUnaryClientInterceptor() grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		// 将用户和租户信息注入到metadata中
		ctx = injectUserAndTenantToMetadata(ctx)
		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

// UserContextStreamClientInterceptor gRPC客户端流调用用户上下文拦截器
func UserContextStreamClientInterceptor() grpc.StreamClientInterceptor {
	return func(ctx context.Context, desc *grpc.StreamDesc, cc *grpc.ClientConn, method string, streamer grpc.Streamer, opts ...grpc.CallOption) (grpc.ClientStream, error) {
		// 将用户和租户信息注入到metadata中
		ctx = injectUserAndTenantToMetadata(ctx)
		return streamer(ctx, desc, cc, method, opts...)
	}
}

// WithUserContext 客户端拦截器：将用户信息添加到gRPC元数据
func WithUserContext(userInfo *UserInfo) grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		if userInfo != nil {
			// URL编码real-name字段，避免gRPC请求头包含非ASCII字符
			encodedRealName := url.QueryEscape(userInfo.RealName)

			md := metadata.New(map[string]string{
				"user-id":         fmt.Sprintf("%d", userInfo.UserID),
				"username":        userInfo.Username,
				"real-name":       encodedRealName,
				"email":           userInfo.Email,
				"tenant-id":       fmt.Sprintf("%d", userInfo.TenantID),
				"app-id":          userInfo.AppId,
				"internal-app-id": fmt.Sprintf("%d", userInfo.InternalAppID),
			})

			// 注意：角色和权限信息已从UserInfo中移除

			ctx = metadata.NewOutgoingContext(ctx, md)
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

// WithAppContext 客户端拦截器：将应用信息添加到gRPC元数据
func WithAppContext(appInfo *AppInfo) grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {

		if appInfo != nil {
			md := metadata.New(map[string]string{
				"tenant-id": fmt.Sprintf("%d", appInfo.TenantID),
			})

			ctx = metadata.NewOutgoingContext(ctx, md)
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

// WithRequestContext 客户端拦截器：将请求信息添加到gRPC元数据
func WithRequestContext(requestID, traceID string) grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		md := metadata.New(map[string]string{})

		if requestID != "" {
			md.Set("request-id", requestID)
		}
		if traceID != "" {
			md.Set("trace-id", traceID)
		}

		if len(md) > 0 {
			ctx = metadata.NewOutgoingContext(ctx, md)
		}

		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

// 内部辅助函数

// extractUserAndAppFromMetadata 从metadata中提取用户和应用信息
func extractUserAndAppFromMetadata(ctx context.Context) context.Context {
	// 从元数据中获取用户信息
	userInfo := extractUserInfoFromMetadata(ctx)
	if userInfo != nil {
		ctx = SetUserInfo(ctx, userInfo)
	}

	// 从元数据中获取应用信息
	appInfo := extractAppInfoFromMetadata(ctx)
	if appInfo != nil {
		ctx = SetAppInfo(ctx, appInfo)
	}

	// 从元数据中获取请求ID和追踪ID
	requestID, traceID := extractRequestInfoFromMetadata(ctx)
	if requestID != "" {
		ctx = SetRequestID(ctx, requestID)
	}
	if traceID != "" {
		ctx = SetTraceID(ctx, traceID)
	}

	return ctx
}

// extractUserInfoFromMetadata 从gRPC元数据中提取用户信息
func extractUserInfoFromMetadata(ctx context.Context) *UserInfo {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil
	}

	// 从元数据中获取用户信息
	userIDStr := getFirstValue(md, "user-id")
	username := getFirstValue(md, "username")
	encodedRealName := getFirstValue(md, "real-name")
	email := getFirstValue(md, "email")
	tenantIDStr := getFirstValue(md, "tenant-id")
	internalAppIDStr := getFirstValue(md, "internal-app-id")

	// URL解码real-name字段
	realName := ""
	if encodedRealName != "" {
		if decoded, err := url.QueryUnescape(encodedRealName); err == nil {
			realName = decoded
		} else {
			// 如果解码失败，使用原始值
			realName = encodedRealName
		}
	}
	// 注意：角色和权限信息已从UserInfo中移除

	// 解析用户ID
	userID := int64(0)
	if userIDStr != "" {
		if id, err := parseInt64(userIDStr); err == nil {
			userID = id
		}
	}

	// 解析租户ID
	tenantID := int64(0)
	if tenantIDStr != "" {
		if id, err := parseInt64(tenantIDStr); err == nil {
			tenantID = id
		}
	}

	// 解析内部应用ID
	internalAppID := int64(0)
	if internalAppIDStr != "" {
		if id, err := parseInt64(internalAppIDStr); err == nil {
			internalAppID = id
		}
	}

	// 如果没有有效的用户ID，返回nil
	if userID == 0 {
		return nil
	}

	return &UserInfo{
		UserID:        userID,
		Username:      username,
		RealName:      realName,
		Email:         email,
		TenantID:      tenantID,
		AppId:         getFirstValue(md, "app-id"),
		InternalAppID: internalAppID,
	}
}

// extractAppInfoFromMetadata 从gRPC元数据中提取应用信息
func extractAppInfoFromMetadata(ctx context.Context) *AppInfo {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil
	}

	tenantIDStr := getFirstValue(md, "tenant-id")
	appID := getFirstValue(md, "app-id")
	internalAppIDStr := getFirstValue(md, "internal-app-id")

	// 解析租户ID
	tenantID := int64(0)
	if tenantIDStr != "" {
		if id, err := parseInt64(tenantIDStr); err == nil {
			tenantID = id
		}
	}

	// 解析内部应用ID
	internalAppID := int64(0)
	if internalAppIDStr != "" {
		if id, err := parseInt64(internalAppIDStr); err == nil {
			internalAppID = id
		}
	}

	// 三者皆空/零则返回nil
	if tenantID == 0 && appID == "" && internalAppID == 0 {
		return nil
	}

	return &AppInfo{
		TenantID:      tenantID,
		AppId:         appID,
		InternalAppId: internalAppID,
	}
}

// extractRequestInfoFromMetadata 从gRPC元数据中提取请求信息
func extractRequestInfoFromMetadata(ctx context.Context) (requestID, traceID string) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return "", ""
	}

	requestID = getFirstValue(md, "request-id")
	traceID = getFirstValue(md, "trace-id")

	return requestID, traceID
}

// injectUserAndTenantToMetadata 将用户和租户信息注入到metadata中
func injectUserAndTenantToMetadata(ctx context.Context) context.Context {
	md, ok := metadata.FromOutgoingContext(ctx)
	if !ok {
		md = metadata.New(nil)
	}

	// 注入用户信息
	if userInfo, ok := GetUserInfo(ctx); ok && userInfo != nil {
		md.Set("user-id", fmt.Sprintf("%d", userInfo.UserID))
		md.Set("username", userInfo.Username)
		// URL编码real-name字段，避免gRPC请求头包含非ASCII字符
		if userInfo.RealName != "" {
			encodedRealName := url.QueryEscape(userInfo.RealName)
			md.Set("real-name", encodedRealName)
		}
		md.Set("email", userInfo.Email)
		md.Set("tenant-id", fmt.Sprintf("%d", userInfo.TenantID))
		md.Set("app-id", userInfo.AppId)
		md.Set("internal-app-id", fmt.Sprintf("%d", userInfo.InternalAppID))

	}

	// 注入应用信息（tenant-id / app-id / internal-app-id）
	if appInfo, ok := GetAppInfo(ctx); ok && appInfo != nil {
		if appInfo.TenantID != 0 {
			md.Set("tenant-id", fmt.Sprintf("%d", appInfo.TenantID))
		}
		if appInfo.AppId != "" {
			md.Set("app-id", appInfo.AppId)
		}
		if appInfo.InternalAppId != 0 {
			md.Set("internal-app-id", fmt.Sprintf("%d", appInfo.InternalAppId))
		}
	}

	// 注入请求ID和追踪ID
	if requestID, ok := GetRequestID(ctx); ok && requestID != "" {
		md.Set("request-id", requestID)
	}
	if traceID, ok := GetTraceID(ctx); ok && traceID != "" {
		md.Set("trace-id", traceID)
	}

	return metadata.NewOutgoingContext(ctx, md)
}

// getFirstValue 获取元数据中的第一个值
func getFirstValue(md metadata.MD, key string) string {
	values := md.Get(key)
	if len(values) == 0 {
		return ""
	}
	return values[0]
}

// getValues 获取元数据中的所有值
func getValues(md metadata.MD, key string) []string {
	return md.Get(key)
}

// parseInt64 解析字符串为int64
func parseInt64(s string) (int64, error) {
	// 移除可能的空格
	s = strings.TrimSpace(s)
	if s == "" {
		return 0, nil
	}

	// 使用 strconv.ParseInt 进行严格的解析
	return strconv.ParseInt(s, 10, 64)
}

// wrappedServerStream 包装的服务器流，用于传递修改后的上下文
type wrappedServerStream struct {
	grpc.ServerStream
	ctx context.Context
}

func (w *wrappedServerStream) Context() context.Context {
	return w.ctx
}
