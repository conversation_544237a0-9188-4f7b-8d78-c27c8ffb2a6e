# 资源权限系统升级设计文档

## 1. 设计概述

### 1.1 设计目标
- 基于现有表结构进行优化升级
- 实现精确的资源权限控制
- 支持多租户隔离
- 提供灵活的权限分配机制
- 确保系统安全性和可扩展性

### 1.2 核心设计思路
1. **保持现有表结构**：不删除现有表，通过修改和扩展实现
2. **resource 表增加 permission_id**：直接关联权限
3. **创建 api_resources 表**：将API相关字段从resource表分离
4. **利用现有的 resource_app_assignments 表**：控制租户对资源的访问权限
5. **权限表按租户隔离**：保持现有的多租户设计

## 2. 升级方案

### 2.1 第一步：修改resource表

```sql
-- 1. 为resource表添加permission_id字段
ALTER TABLE `resource` 
ADD COLUMN `permission_id` bigint DEFAULT NULL COMMENT '关联的权限ID' AFTER `resource_type`,
ADD KEY `idx_resource_permission_id` (`permission_id`),
ADD CONSTRAINT `fk_resource_permission_id` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE SET NULL;

-- 2. 修改resource_type约束，移除api类型
ALTER TABLE `resource` 
DROP CONSTRAINT `chk_resource_type`,
ADD CONSTRAINT `chk_resource_type` CHECK (`resource_type` IN ('menu', 'page', 'button'));

-- 3. 移除API相关字段（保留但不使用）
-- 注意：不删除字段，避免影响现有数据
-- service_name, request_type, response_type, api_method, content_type 字段保留
```

### 2.2 第二步：创建api_resources表

```sql
-- 创建API资源扩展表
CREATE TABLE `api_resources` (
  `id` bigint NOT NULL COMMENT '分布式ID',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL DEFAULT '0' COMMENT '内部应用ID',
  `resource_id` bigint NOT NULL COMMENT '关联的资源ID',
  `permission_id` bigint DEFAULT NULL COMMENT '关联的权限ID',
  `service_name` varchar(100) NOT NULL COMMENT '应用服务名称',
  `api_method` varchar(10) NOT NULL COMMENT 'HTTP方法: GET, POST, PUT, DELETE, PATCH',
  `path` varchar(255) NOT NULL COMMENT 'API路径',
  `request_type` varchar(50) DEFAULT 'json' COMMENT '请求数据类型: json, form, file, text, stream, xml, binary',
  `response_type` varchar(50) DEFAULT 'json' COMMENT '响应数据类型: json, html, xml, stream, file, text, binary',
  `content_type` varchar(100) DEFAULT NULL COMMENT 'Content-Type',
  `timeout` int DEFAULT 30000 COMMENT '超时时间(毫秒)',
  `rate_limit` int DEFAULT NULL COMMENT '限流次数(次/分钟)',
  `is_public` tinyint(1) DEFAULT '0' COMMENT '是否公开API',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态：active-活跃，disabled-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uq_api_resource_tenant_path` (`tenant_id`,`service_name`,`api_method`,`path`),
  UNIQUE KEY `uq_api_resource_id` (`resource_id`),
  KEY `idx_api_resource_tenant_id` (`tenant_id`),
  KEY `idx_api_resource_service` (`service_name`),
  KEY `idx_api_resource_method` (`api_method`),
  KEY `idx_api_resource_status` (`status`),
  KEY `idx_api_resource_permission_id` (`permission_id`),
  KEY `idx_api_resource_internal_app_id` (`internal_app_id`),
  KEY `idx_api_resource_tenant_internal_app` (`tenant_id`,`internal_app_id`),
  CONSTRAINT `fk_api_resource_resource_id` FOREIGN KEY (`resource_id`) REFERENCES `resource` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_api_resource_permission_id` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE SET NULL,
  CONSTRAINT `chk_api_method` CHECK (`api_method` IN ('GET', 'POST', 'PUT', 'DELETE', 'PATCH')),
  CONSTRAINT `chk_api_status` CHECK (`status` IN ('active', 'disabled'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API资源表';
```

### 2.3 第三步：为permissions表添加resource_type字段

```sql
-- 为permissions表添加resource_type字段
ALTER TABLE `permissions` 
ADD COLUMN `resource_type` varchar(50) DEFAULT NULL COMMENT '资源类型：menu, page, button, api' AFTER `action`,
ADD KEY `idx_permission_resource_type` (`resource_type`),
ADD CONSTRAINT `chk_permission_resource_type` CHECK (`resource_type` IN ('menu', 'page', 'button', 'api'));
```

### 2.4 第四步：数据迁移

```sql
-- 1. 迁移现有API资源到新表
INSERT INTO api_resources (
    id, tenant_id, internal_app_id, resource_id,
    service_name, api_method, path, request_type, response_type, 
    content_type, is_public, status, created_at, updated_at
)
SELECT 
    id, tenant_id, internal_app_id, id as resource_id,
    service_name, api_method, path, request_type, response_type,
    content_type, is_public, 
    CASE WHEN status = 'active' THEN 'active' ELSE 'disabled' END as status,
    created_at, updated_at
FROM resource 
WHERE resource_type = 'api';

-- 2. 为API资源创建对应的权限（如果不存在）
INSERT INTO permissions (tenant_id, internal_app_id, name, code, action, resource_type, is_system)
SELECT 
    r.tenant_id,
    r.internal_app_id,
    CONCAT(r.name, ':', 'access') as name,
    CONCAT(UPPER(REPLACE(r.name, '_', '_')), '_ACCESS') as code,
    'read' as action,
    'api' as resource_type,
    1 as is_system
FROM resource r
WHERE r.resource_type = 'api'
AND NOT EXISTS (
    SELECT 1 FROM permissions p 
    WHERE p.tenant_id = r.tenant_id 
    AND p.code = CONCAT(UPPER(REPLACE(r.name, '_', '_')), '_ACCESS')
);

-- 3. 更新API资源的permission_id
UPDATE api_resources ar
JOIN permissions p ON ar.tenant_id = p.tenant_id 
    AND p.code = CONCAT(UPPER(REPLACE(ar.service_name, '-', '_')), '_', UPPER(REPLACE(ar.path, '/', '_')), '_ACCESS')
SET ar.permission_id = p.id
WHERE ar.permission_id IS NULL;

-- 4. 为页面资源创建对应的权限（如果不存在）
INSERT INTO permissions (tenant_id, internal_app_id, name, code, action, resource_type, is_system)
SELECT 
    r.tenant_id,
    r.internal_app_id,
    CONCAT(r.name, ':', 'access') as name,
    CONCAT(UPPER(REPLACE(r.name, '-', '_')), '_ACCESS') as code,
    'read' as action,
    'page' as resource_type,
    1 as is_system
FROM resource r
WHERE r.resource_type = 'page'
AND NOT EXISTS (
    SELECT 1 FROM permissions p 
    WHERE p.tenant_id = r.tenant_id 
    AND p.code = CONCAT(UPPER(REPLACE(r.name, '-', '_')), '_ACCESS')
);

-- 5. 更新页面资源的permission_id
UPDATE resource r
JOIN permissions p ON r.tenant_id = p.tenant_id 
    AND p.code = CONCAT(UPPER(REPLACE(r.name, '-', '_')), '_ACCESS')
SET r.permission_id = p.id
WHERE r.resource_type = 'page' AND r.permission_id IS NULL;
```

## 3. 权限检查逻辑

### 3.1 权限检查服务

```go
// 权限检查服务
type PermissionService struct {
    permissionRepo repository.PermissionRepository
    resourceRepo   repository.ResourceRepository
    apiResourceRepo repository.APIResourceRepository
    assignmentRepo repository.ResourceAppAssignmentRepository
    roleRepo       repository.RoleRepository
    rolePermissionRepo repository.RolePermissionRepository
}

// 检查用户对资源的权限
func (s *PermissionService) CheckUserResourcePermission(ctx context.Context, userID int64, resourceID int64) (bool, error) {
    // 1. 检查资源是否分配给当前应用
    appInfo, _ := usercontext.GetAppInfo(ctx)
    hasAssignment, err := s.assignmentRepo.Exists(ctx, resourceID, appInfo.InternalAppId)
    if err != nil {
        return false, err
    }
    if !hasAssignment {
        return false, nil // 资源未分配给当前应用
    }
    
    // 2. 获取资源信息
    resource, err := s.resourceRepo.FindByID(ctx, resourceID)
    if err != nil {
        return false, err
    }
    if resource == nil {
        return false, nil
    }
    
    // 3. 检查资源权限
    if resource.PermissionID != nil {
        return s.checkUserPermission(ctx, userID, *resource.PermissionID)
    }
    
    // 4. 如果没有直接权限，检查父级资源权限
    if resource.ParentID != nil && *resource.ParentID > 0 {
        return s.CheckUserResourcePermission(ctx, userID, *resource.ParentID)
    }
    
    return true, nil // 默认允许访问
}

// 检查用户对API的权限
func (s *PermissionService) CheckUserAPIPermission(ctx context.Context, userID int64, apiPath, method string) (bool, error) {
    // 1. 根据路径和方法查找API资源
    apiResource, err := s.apiResourceRepo.FindByPathAndMethod(ctx, apiPath, method)
    if err != nil {
        return false, err
    }
    if apiResource == nil {
        return false, nil // API资源不存在
    }
    
    // 2. 检查API资源是否分配给当前应用
    appInfo, _ := usercontext.GetAppInfo(ctx)
    hasAssignment, err := s.assignmentRepo.Exists(ctx, apiResource.ResourceID, appInfo.InternalAppId)
    if err != nil {
        return false, err
    }
    if !hasAssignment {
        return false, nil // API资源未分配给当前应用
    }
    
    // 3. 检查API权限
    if apiResource.PermissionID != nil {
        return s.checkUserPermission(ctx, userID, *apiResource.PermissionID)
    }
    
    // 4. 如果没有直接权限，检查关联的基础资源权限
    return s.CheckUserResourcePermission(ctx, userID, apiResource.ResourceID)
}
```

## 4. 升级优势

### 4.1 设计优势

1. **保持兼容性**：不破坏现有数据结构
2. **渐进式升级**：可以分步骤进行升级
3. **权限粒度精确**：每个资源直接关联权限，控制精确
4. **租户隔离清晰**：通过 `resource_app_assignments` 控制租户访问
5. **继承机制**：支持父级资源权限继承
6. **灵活性高**：支持不同资源类型的不同权限控制

### 4.2 技术优势

1. **数据库设计合理**：索引设计优化，查询性能好
2. **外键约束完整**：保证数据一致性
3. **软删除支持**：支持数据恢复
4. **多租户隔离**：租户数据完全隔离
5. **应用级隔离**：支持多应用场景

## 5. 升级步骤

### 5.1 升级准备

1. **备份数据**：升级前完整备份数据库
2. **测试环境验证**：在测试环境验证升级脚本
3. **制定回滚方案**：准备回滚脚本

### 5.2 升级执行

1. **执行表结构修改**
2. **执行数据迁移**
3. **验证数据完整性**
4. **更新应用代码**
5. **功能测试**

### 5.3 升级验证

1. **数据完整性检查**
2. **权限功能测试**
3. **性能测试**
4. **兼容性测试**

## 6. 总结

这个升级方案具有以下特点：

1. **兼容性**：保持现有表结构，通过修改和扩展实现
2. **渐进式**：可以分步骤进行升级，降低风险
3. **精确控制**：每个资源都可以精确控制权限
4. **多租户支持**：完全支持多租户隔离
5. **灵活扩展**：易于添加新的资源类型和权限类型
6. **性能优化**：查询效率高，支持大规模数据
7. **易于维护**：权限关系清晰，便于理解和维护

该升级方案完全满足企业级应用的权限管理需求，是一个成熟、可靠的权限系统升级解决方案。
