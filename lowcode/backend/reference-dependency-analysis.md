# 引用分析与依赖图构建系统

## 概述

引用分析与依赖图构建系统是AI-DSL平台的静态分析核心，通过深度分析DSL代码结构，构建完整的引用关系图和依赖图，为AI提供准确的代码理解和影响分析能力。

## 核心架构

### 1. 引用分析引擎

```typescript
// 引用分析引擎
class ReferenceAnalysisEngine {
  private symbolTable: SymbolTable;
  private referenceGraph: ReferenceGraph;
  private dependencyResolver: DependencyResolver;
  
  // 分析项目引用关系
  analyzeProject(project: ProjectStructure): AnalysisResult {
    // 1. 构建符号表
    this.buildSymbolTable(project);
    
    // 2. 解析引用关系
    this.resolveReferences(project);
    
    // 3. 构建依赖图
    const dependencyGraph = this.buildDependencyGraph();
    
    // 4. 分析循环依赖
    const circularDependencies = this.detectCircularDependencies(dependencyGraph);
    
    // 5. 计算影响范围
    const impactAnalysis = this.calculateImpactScope(dependencyGraph);
    
    return {
      symbolTable: this.symbolTable,
      referenceGraph: this.referenceGraph,
      dependencyGraph,
      circularDependencies,
      impactAnalysis
    };
  }
  
  // 构建符号表
  private buildSymbolTable(project: ProjectStructure): void {
    this.symbolTable = new SymbolTable();
    
    // 注册实体符号
    for (const entity of project.entities) {
      this.symbolTable.registerSymbol({
        name: entity.name,
        type: 'entity',
        scope: 'global',
        definition: entity,
        location: {
          module: entity.module,
          file: entity.file,
          line: entity.line
        },
        metadata: {
          fields: entity.fields.map(f => f.name),
          relations: entity.relations.map(r => r.name),
          indexes: entity.indexes.map(i => i.name)
        }
      });
      
      // 注册字段符号
      for (const field of entity.fields) {
        this.symbolTable.registerSymbol({
          name: `${entity.name}.${field.name}`,
          type: 'field',
          scope: entity.name,
          definition: field,
          parent: entity.name,
          location: {
            module: entity.module,
            file: entity.file,
            line: field.line
          }
        });
      }
    }
    
    // 注册服务符号
    for (const service of project.services) {
      this.symbolTable.registerSymbol({
        name: service.name,
        type: 'service',
        scope: 'global',
        definition: service,
        location: {
          module: service.module,
          file: service.file,
          line: service.line
        }
      });
      
      // 注册方法符号
      for (const method of service.methods) {
        this.symbolTable.registerSymbol({
          name: `${service.name}.${method.name}`,
          type: 'method',
          scope: service.name,
          definition: method,
          parent: service.name,
          location: {
            module: service.module,
            file: service.file,
            line: method.line
          }
        });
      }
    }
  }
}
```

### 2. 依赖解析器

```typescript
// 依赖解析器
class DependencyResolver {
  // 解析实体依赖
  resolveEntityDependencies(entity: EntityDefinition): EntityDependency[] {
    const dependencies: EntityDependency[] = [];
    
    // 解析关系依赖
    for (const relation of entity.relations) {
      dependencies.push({
        type: 'relation',
        source: entity.name,
        target: relation.target,
        relationship: relation.type,
        metadata: {
          cardinality: relation.cardinality,
          cascade: relation.cascade,
          foreignKey: relation.foreignKey
        }
      });
    }
    
    // 解析字段类型依赖
    for (const field of entity.fields) {
      if (this.isCustomType(field.type)) {
        dependencies.push({
          type: 'field_type',
          source: entity.name,
          target: field.type,
          relationship: 'uses_type',
          metadata: {
            fieldName: field.name,
            required: field.required,
            constraints: field.constraints
          }
        });
      }
    }
    
    // 解析继承依赖
    if (entity.extends) {
      dependencies.push({
        type: 'inheritance',
        source: entity.name,
        target: entity.extends,
        relationship: 'extends',
        metadata: {
          overrides: entity.overrides || []
        }
      });
    }
    
    return dependencies;
  }
  
  // 解析服务依赖
  resolveServiceDependencies(service: ServiceDefinition): ServiceDependency[] {
    const dependencies: ServiceDependency[] = [];
    
    // 解析方法级依赖
    for (const method of service.methods) {
      // 实体依赖
      if (method.dependencies?.entities) {
        for (const entityDep of method.dependencies.entities) {
          dependencies.push({
            type: 'entity_usage',
            source: `${service.name}.${method.name}`,
            target: entityDep,
            relationship: 'uses_entity',
            metadata: {
              operations: this.extractEntityOperations(method, entityDep)
            }
          });
        }
      }
      
      // 服务依赖
      if (method.dependencies?.services) {
        for (const serviceDep of method.dependencies.services) {
          dependencies.push({
            type: 'service_call',
            source: `${service.name}.${method.name}`,
            target: serviceDep.name,
            relationship: 'calls_service',
            metadata: {
              method: serviceDep.method,
              async: serviceDep.async,
              parameters: serviceDep.parameters
            }
          });
        }
      }
      
      // 规则依赖
      if (method.dependencies?.rules) {
        for (const ruleDep of method.dependencies.rules) {
          dependencies.push({
            type: 'rule_usage',
            source: `${service.name}.${method.name}`,
            target: ruleDep,
            relationship: 'applies_rule',
            metadata: {
              stage: this.getRuleStage(method, ruleDep)
            }
          });
        }
      }
    }
    
    return dependencies;
  }
}
```

### 3. 依赖图构建

```yaml
# 依赖图构建配置
dependency_graph_config:
  graph_types:
    - name: entity_dependency_graph
      description: "实体间依赖关系图"
      node_types: [entity, custom_type]
      edge_types: [relation, field_type, inheritance]
      analysis:
        - circular_dependency_detection
        - dependency_depth_analysis
        - coupling_metrics
    
    - name: service_dependency_graph
      description: "服务间依赖关系图"
      node_types: [service, method]
      edge_types: [service_call, entity_usage, rule_usage]
      analysis:
        - call_path_analysis
        - service_coupling_analysis
        - performance_impact_analysis
    
    - name: module_dependency_graph
      description: "模块间依赖关系图"
      node_types: [module]
      edge_types: [module_import, module_export]
      analysis:
        - module_cohesion_analysis
        - deployment_order_analysis
        - change_impact_analysis
  
  # 图构建算法
  algorithms:
    topological_sort:
      use_for: [build_order, deployment_order]
      cycle_handling: error
    
    strongly_connected_components:
      use_for: [circular_dependency_detection]
      algorithm: tarjan
    
    shortest_path:
      use_for: [impact_analysis, change_propagation]
      algorithm: dijkstra
      weight_function: dependency_strength
```

### 4. 循环依赖检测

```typescript
// 循环依赖检测器
class CircularDependencyDetector {
  // 检测循环依赖
  detectCircularDependencies(graph: DependencyGraph): CircularDependency[] {
    const circularDeps: CircularDependency[] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    const path: string[] = [];
    
    for (const node of graph.getAllNodes()) {
      if (!visited.has(node.id)) {
        const cycles = this.dfsDetectCycles(
          graph, 
          node.id, 
          visited, 
          recursionStack, 
          path
        );
        circularDeps.push(...cycles);
      }
    }
    
    return this.analyzeCycles(circularDeps);
  }
  
  // DFS检测循环
  private dfsDetectCycles(
    graph: DependencyGraph,
    nodeId: string,
    visited: Set<string>,
    recursionStack: Set<string>,
    path: string[]
  ): CircularDependency[] {
    const cycles: CircularDependency[] = [];
    
    visited.add(nodeId);
    recursionStack.add(nodeId);
    path.push(nodeId);
    
    for (const edge of graph.getOutgoingEdges(nodeId)) {
      const targetId = edge.target;
      
      if (!visited.has(targetId)) {
        // 递归检测
        const childCycles = this.dfsDetectCycles(
          graph, 
          targetId, 
          visited, 
          recursionStack, 
          path
        );
        cycles.push(...childCycles);
      } else if (recursionStack.has(targetId)) {
        // 发现循环
        const cycleStartIndex = path.indexOf(targetId);
        const cycle = path.slice(cycleStartIndex).concat([targetId]);
        
        cycles.push({
          nodes: cycle,
          edges: this.extractCycleEdges(graph, cycle),
          type: this.determineCycleType(graph, cycle),
          severity: this.calculateCycleSeverity(graph, cycle),
          suggestions: this.generateCycleBreakingSuggestions(graph, cycle)
        });
      }
    }
    
    recursionStack.delete(nodeId);
    path.pop();
    
    return cycles;
  }
  
  // 生成循环打破建议
  private generateCycleBreakingSuggestions(
    graph: DependencyGraph,
    cycle: string[]
  ): CycleBreakingSuggestion[] {
    const suggestions: CycleBreakingSuggestion[] = [];
    
    // 分析每条边的强度
    const edges = this.extractCycleEdges(graph, cycle);
    const edgeStrengths = edges.map(edge => ({
      edge,
      strength: this.calculateEdgeStrength(graph, edge)
    }));
    
    // 按强度排序，优先建议删除弱依赖
    edgeStrengths.sort((a, b) => a.strength - b.strength);
    
    for (const { edge, strength } of edgeStrengths.slice(0, 3)) {
      suggestions.push({
        type: 'remove_dependency',
        edge,
        impact: strength,
        alternatives: this.findAlternatives(graph, edge),
        description: `移除 ${edge.source} -> ${edge.target} 的依赖关系`,
        implementation: this.generateImplementationGuidance(edge)
      });
    }
    
    // 建议引入中介模块
    suggestions.push({
      type: 'introduce_mediator',
      description: '引入中介模块打破直接依赖',
      implementation: {
        steps: [
          '创建共享接口模块',
          '将相互依赖的部分抽取到接口中',
          '通过依赖注入或事件机制解耦'
        ]
      }
    });
    
    return suggestions;
  }
}
```

## 影响分析算法

### 1. 变更影响分析

```typescript
// 变更影响分析器
class ChangeImpactAnalyzer {
  // 分析变更影响
  analyzeChangeImpact(
    graph: DependencyGraph, 
    changes: Change[]
  ): ImpactAnalysis {
    const impactAnalysis = new ImpactAnalysis();
    
    for (const change of changes) {
      const directImpacts = this.calculateDirectImpacts(graph, change);
      const indirectImpacts = this.calculateIndirectImpacts(graph, change);
      const riskAssessment = this.assessRisks(graph, change);
      
      impactAnalysis.addChangeImpact({
        change,
        directImpacts,
        indirectImpacts,
        riskAssessment,
        recommendations: this.generateRecommendations(change, directImpacts, indirectImpacts)
      });
    }
    
    return impactAnalysis;
  }
  
  // 计算直接影响
  private calculateDirectImpacts(
    graph: DependencyGraph, 
    change: Change
  ): DirectImpact[] {
    const impacts: DirectImpact[] = [];
    const changedNode = graph.getNode(change.target);
    
    if (!changedNode) return impacts;
    
    // 分析直接依赖者
    for (const incomingEdge of graph.getIncomingEdges(change.target)) {
      const dependent = graph.getNode(incomingEdge.source);
      
      impacts.push({
        target: dependent.id,
        type: this.determineImpactType(change, incomingEdge),
        severity: this.calculateImpactSeverity(change, incomingEdge),
        description: this.generateImpactDescription(change, dependent, incomingEdge),
        requiredActions: this.determineRequiredActions(change, incomingEdge)
      });
    }
    
    // 分析直接依赖项
    for (const outgoingEdge of graph.getOutgoingEdges(change.target)) {
      const dependency = graph.getNode(outgoingEdge.target);
      
      if (change.type === 'delete') {
        impacts.push({
          target: dependency.id,
          type: 'orphaned_dependency',
          severity: 'medium',
          description: `${dependency.id} 将失去来自 ${change.target} 的引用`,
          requiredActions: ['review_usage', 'consider_cleanup']
        });
      }
    }
    
    return impacts;
  }
  
  // 计算间接影响
  private calculateIndirectImpacts(
    graph: DependencyGraph, 
    change: Change
  ): IndirectImpact[] {
    const impacts: IndirectImpact[] = [];
    const visited = new Set<string>();
    const queue = [{ nodeId: change.target, distance: 0, path: [change.target] }];
    
    while (queue.length > 0) {
      const { nodeId, distance, path } = queue.shift()!;
      
      if (visited.has(nodeId) || distance > 5) continue; // 限制传播深度
      visited.add(nodeId);
      
      for (const incomingEdge of graph.getIncomingEdges(nodeId)) {
        const dependentId = incomingEdge.source;
        const newPath = [dependentId, ...path];
        const propagationStrength = this.calculatePropagationStrength(path, incomingEdge);
        
        if (propagationStrength > 0.1) { // 传播强度阈值
          impacts.push({
            target: dependentId,
            distance: distance + 1,
            path: newPath,
            propagationStrength,
            estimatedImpact: this.estimateIndirectImpact(change, newPath, propagationStrength),
            description: this.generateIndirectImpactDescription(change, newPath)
          });
          
          queue.push({ 
            nodeId: dependentId, 
            distance: distance + 1, 
            path: newPath 
          });
        }
      }
    }
    
    return impacts.sort((a, b) => b.estimatedImpact - a.estimatedImpact);
  }
  
  // 评估风险
  private assessRisks(graph: DependencyGraph, change: Change): RiskAssessment {
    const risks: Risk[] = [];
    
    // 评估破坏性变更风险
    if (this.isBreakingChange(change)) {
      risks.push({
        type: 'breaking_change',
        severity: 'high',
        probability: 0.9,
        description: '此变更可能导致现有功能出现错误',
        mitigations: [
          '实施全面的回归测试',
          '准备回滚计划',
          '分阶段发布'
        ]
      });
    }
    
    // 评估性能影响风险
    const performanceRisk = this.assessPerformanceRisk(graph, change);
    if (performanceRisk.severity !== 'low') {
      risks.push(performanceRisk);
    }
    
    // 评估数据一致性风险
    const dataConsistencyRisk = this.assessDataConsistencyRisk(graph, change);
    if (dataConsistencyRisk.severity !== 'low') {
      risks.push(dataConsistencyRisk);
    }
    
    return {
      overallRisk: this.calculateOverallRisk(risks),
      risks,
      recommendations: this.generateRiskMitigationRecommendations(risks)
    };
  }
}
```

### 2. 引用查找算法

```typescript
// 引用查找器
class ReferenceFinder {
  // 查找所有引用
  findAllReferences(
    graph: ReferenceGraph, 
    target: string,
    options: FindOptions = {}
  ): Reference[] {
    const references: Reference[] = [];
    
    // 直接引用
    const directRefs = this.findDirectReferences(graph, target);
    references.push(...directRefs);
    
    // 间接引用（可选）
    if (options.includeIndirect) {
      const indirectRefs = this.findIndirectReferences(graph, target, options.maxDepth || 3);
      references.push(...indirectRefs);
    }
    
    // 类型引用（可选）
    if (options.includeTypeReferences) {
      const typeRefs = this.findTypeReferences(graph, target);
      references.push(...typeRefs);
    }
    
    // 运行时引用（可选）
    if (options.includeRuntimeReferences) {
      const runtimeRefs = this.findRuntimeReferences(graph, target);
      references.push(...runtimeRefs);
    }
    
    return this.deduplicate(references);
  }
  
  // 查找直接引用
  private findDirectReferences(graph: ReferenceGraph, target: string): Reference[] {
    const references: Reference[] = [];
    
    for (const edge of graph.getIncomingEdges(target)) {
      references.push({
        source: edge.source,
        target: edge.target,
        type: edge.type,
        location: edge.metadata?.location,
        context: edge.metadata?.context,
        confidence: 1.0
      });
    }
    
    return references;
  }
  
  // 查找间接引用
  private findIndirectReferences(
    graph: ReferenceGraph, 
    target: string, 
    maxDepth: number
  ): Reference[] {
    const references: Reference[] = [];
    const visited = new Set<string>();
    const queue = [{ nodeId: target, depth: 0, path: [target] }];
    
    while (queue.length > 0) {
      const { nodeId, depth, path } = queue.shift()!;
      
      if (depth >= maxDepth || visited.has(nodeId)) continue;
      visited.add(nodeId);
      
      for (const edge of graph.getIncomingEdges(nodeId)) {
        const sourceId = edge.source;
        const newPath = [sourceId, ...path];
        const confidence = this.calculateIndirectReferenceConfidence(newPath, edge);
        
        if (confidence > 0.3) { // 置信度阈值
          references.push({
            source: sourceId,
            target,
            type: 'indirect',
            path: newPath,
            distance: depth + 1,
            confidence,
            context: this.buildIndirectContext(newPath, edge)
          });
          
          queue.push({ 
            nodeId: sourceId, 
            depth: depth + 1, 
            path: newPath 
          });
        }
      }
    }
    
    return references;
  }
}
```

## 可视化与报告

### 1. 依赖图可视化

```yaml
# 依赖图可视化配置
dependency_visualization:
  layout_algorithms:
    - name: hierarchical
      description: "分层布局，适用于树状依赖"
      parameters:
        direction: top_to_bottom
        layer_spacing: 100
        node_spacing: 80
        
    - name: force_directed
      description: "力导向布局，适用于网状依赖"
      parameters:
        spring_length: 120
        spring_strength: 0.8
        repulsion_strength: 1000
        
    - name: circular
      description: "圆形布局，适用于模块间依赖"
      parameters:
        radius: 200
        start_angle: 0
        
  node_styling:
    entity:
      shape: rectangle
      color: "#4CAF50"
      size: based_on_field_count
      border_width: 2
      
    service:
      shape: rounded_rectangle
      color: "#2196F3"
      size: based_on_method_count
      border_width: 2
      
    module:
      shape: hexagon
      color: "#FF9800"
      size: based_on_content_count
      border_width: 3
      
  edge_styling:
    relation:
      style: solid
      color: "#666666"
      width: 2
      arrow: true
      
    inheritance:
      style: solid
      color: "#9C27B0"
      width: 3
      arrow: true
      
    service_call:
      style: dashed
      color: "#FF5722"
      width: based_on_call_frequency
      arrow: true
      
    circular_dependency:
      style: solid
      color: "#F44336"
      width: 4
      arrow: both
      highlight: true
  
  interaction_features:
    zoom: { min: 0.1, max: 5.0 }
    pan: enabled
    select: highlight_connected
    filter: by_node_type, by_edge_type, by_module
    search: fuzzy_search
    export: svg, png, pdf
```

### 2. 分析报告生成

```typescript
// 分析报告生成器
class AnalysisReportGenerator {
  // 生成完整分析报告
  generateReport(analysis: AnalysisResult): AnalysisReport {
    return {
      summary: this.generateSummary(analysis),
      dependencyAnalysis: this.generateDependencyAnalysis(analysis),
      circularDependencies: this.generateCircularDependencyReport(analysis),
      impactAnalysis: this.generateImpactAnalysisReport(analysis),
      recommendations: this.generateRecommendations(analysis),
      metrics: this.generateMetrics(analysis),
      visualizations: this.generateVisualizations(analysis)
    };
  }
  
  // 生成摘要
  private generateSummary(analysis: AnalysisResult): ReportSummary {
    const stats = this.calculateStatistics(analysis);
    
    return {
      projectOverview: {
        totalEntities: stats.entityCount,
        totalServices: stats.serviceCount,
        totalModules: stats.moduleCount,
        totalDependencies: stats.dependencyCount
      },
      healthMetrics: {
        couplingIndex: stats.couplingIndex,
        cohesionIndex: stats.cohesionIndex,
        complexityScore: stats.complexityScore,
        maintainabilityIndex: stats.maintainabilityIndex
      },
      issues: {
        circularDependencies: stats.circularDependencyCount,
        highCouplingModules: stats.highCouplingModuleCount,
        unusedEntities: stats.unusedEntityCount,
        potentialRefactorTargets: stats.refactorTargetCount
      },
      recommendations: {
        criticalIssues: stats.criticalIssueCount,
        suggestedOptimizations: stats.optimizationSuggestionCount,
        estimatedEffort: stats.totalEstimatedEffort
      }
    };
  }
  
  // 生成依赖分析报告
  private generateDependencyAnalysis(analysis: AnalysisResult): DependencyAnalysisReport {
    const graph = analysis.dependencyGraph;
    
    return {
      topLevelStatistics: {
        totalNodes: graph.getNodeCount(),
        totalEdges: graph.getEdgeCount(),
        averageFanIn: graph.calculateAverageFanIn(),
        averageFanOut: graph.calculateAverageFanOut(),
        maxDependencyDepth: graph.calculateMaxDepth()
      },
      dependencyHotspots: this.identifyDependencyHotspots(graph),
      isolatedComponents: this.findIsolatedComponents(graph),
      dependencyChains: this.findLongestDependencyChains(graph),
      moduleAnalysis: this.analyzeModuleDependencies(graph),
      recommendations: this.generateDependencyRecommendations(graph)
    };
  }
  
  // 识别依赖热点
  private identifyDependencyHotspots(graph: DependencyGraph): DependencyHotspot[] {
    const hotspots: DependencyHotspot[] = [];
    
    for (const node of graph.getAllNodes()) {
      const fanIn = graph.getFanIn(node.id);
      const fanOut = graph.getFanOut(node.id);
      const centralityScore = this.calculateCentralityScore(graph, node.id);
      
      // 高扇入节点（被很多其他节点依赖）
      if (fanIn > 10) {
        hotspots.push({
          nodeId: node.id,
          type: 'high_fan_in',
          score: fanIn,
          description: `${node.id} 被 ${fanIn} 个组件依赖，是系统的核心组件`,
          risks: [
            '修改此组件影响面大',
            '可能成为性能瓶颈',
            '测试复杂度高'
          ],
          recommendations: [
            '考虑拆分功能以降低耦合',
            '确保充分的测试覆盖',
            '建立清晰的接口契约'
          ]
        });
      }
      
      // 高扇出节点（依赖很多其他节点）
      if (fanOut > 15) {
        hotspots.push({
          nodeId: node.id,
          type: 'high_fan_out',
          score: fanOut,
          description: `${node.id} 依赖 ${fanOut} 个组件，职责可能过于分散`,
          risks: [
            '违反单一职责原则',
            '维护成本高',
            '变更影响不可预测'
          ],
          recommendations: [
            '重构以减少依赖数量',
            '使用依赖注入降低耦合',
            '考虑应用门面模式'
          ]
        });
      }
      
      // 高中心性节点（在依赖网络中处于关键位置）
      if (centralityScore > 0.8) {
        hotspots.push({
          nodeId: node.id,
          type: 'high_centrality',
          score: centralityScore,
          description: `${node.id} 在依赖网络中处于关键位置`,
          risks: [
            '系统稳定性的关键点',
            '故障影响范围大',
            '难以重构和替换'
          ],
          recommendations: [
            '提高组件的健壮性',
            '建立监控和告警机制',
            '准备备用方案'
          ]
        });
      }
    }
    
    return hotspots.sort((a, b) => b.score - a.score);
  }
}
```

## 性能优化

### 1. 分析性能优化

```yaml
# 性能优化配置
analysis_performance:
  caching:
    symbol_table:
      enabled: true
      ttl: 3600s  # 1小时
      max_size: 100MB
      invalidation: on_file_change
      
    dependency_graph:
      enabled: true
      ttl: 1800s  # 30分钟
      max_size: 200MB
      invalidation: on_dependency_change
      
    analysis_results:
      enabled: true
      ttl: 600s   # 10分钟
      max_size: 50MB
      invalidation: on_structure_change
  
  incremental_analysis:
    enabled: true
    change_detection: file_watcher
    partial_rebuild: dependency_affected
    batch_processing: true
    batch_size: 100
    batch_timeout: 5s
  
  parallel_processing:
    enabled: true
    max_workers: cpu_count
    work_stealing: true
    load_balancing: dynamic
    
  memory_optimization:
    lazy_loading: true
    garbage_collection: aggressive
    memory_mapping: large_files
    streaming_processing: enabled
```

### 2. 增量分析算法

```typescript
// 增量分析器
class IncrementalAnalyzer {
  private changeTracker: ChangeTracker;
  private analysisCache: AnalysisCache;
  
  // 执行增量分析
  performIncrementalAnalysis(changes: FileChange[]): AnalysisUpdate {
    const affectedNodes = this.calculateAffectedNodes(changes);
    const invalidatedAnalysis = this.invalidateAnalysis(affectedNodes);
    
    // 并行重新分析受影响的部分
    const updatedAnalysis = this.reanalyzeAffectedParts(affectedNodes);
    
    // 合并分析结果
    const mergedAnalysis = this.mergeAnalysisResults(
      this.analysisCache.getCachedAnalysis(),
      updatedAnalysis
    );
    
    // 更新缓存
    this.analysisCache.updateCache(mergedAnalysis);
    
    return {
      affectedNodes,
      updatedAnalysis: mergedAnalysis,
      changeSet: changes,
      analysisTime: Date.now() - this.startTime
    };
  }
  
  // 计算受影响的节点
  private calculateAffectedNodes(changes: FileChange[]): AffectedNode[] {
    const affectedNodes: AffectedNode[] = [];
    
    for (const change of changes) {
      // 直接受影响的节点
      const directlyAffected = this.getNodesInFile(change.filePath);
      
      // 依赖受影响节点的其他节点
      const indirectlyAffected = this.getDependentNodes(directlyAffected);
      
      affectedNodes.push(...directlyAffected);
      affectedNodes.push(...indirectlyAffected);
    }
    
    return this.deduplicateAffectedNodes(affectedNodes);
  }
}
```

通过这个引用分析与依赖图构建系统，AI能够深度理解代码结构，准确分析变更影响，为开发者提供精准的重构建议和风险评估。