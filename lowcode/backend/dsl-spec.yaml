# 服务端逻辑 DSL 完整规范
# Version: 1.0.0
# Author: DSL Architect Team

---
# ============================================================================
# 1️⃣ 语义覆盖范围 - 完整元模型定义
# ============================================================================

# DSL 根节点结构
service:
  # 服务基本信息
  meta:
    name: string           # 服务名称
    version: string        # 服务版本
    description: string    # 服务描述
    namespace: string      # 命名空间
    tags: string[]         # 服务标签
  
  # 参数化配置支持
  parameters:
    environments:          # 环境变量参数化
      - name: string
        type: string|number|boolean
        default: any
        description: string
    feature_flags:         # 功能开关参数化
      - name: string
        enabled: boolean
        environments: string[]
    tenant_variables:      # 租户级变量参数化
      - name: string
        type: string
        tenant_scoped: boolean

  # 导入其他DSL文件
  imports:
    - path: string         # 文件路径
      namespace: string    # 导入命名空间
      version: string      # 版本约束

# ============================================================================
# ① 领域模型定义
# ============================================================================
domain:
  # 实体定义
  entities:
    - name: string                    # 实体名称
      table: string                   # 数据库表名
      description: string             # 实体描述
      
      # 字段定义
      fields:
        - name: string                # 字段名
          type: string                # 字段类型
          nullable: boolean           # 是否可空
          default: any                # 默认值
          description: string         # 字段描述
          constraints:                # 约束条件
            min: number
            max: number
            pattern: string
            unique: boolean
            references: string        # 外键引用
          annotations:                # 字段注解
            sensitive: boolean        # 敏感字段标记
            encrypted: boolean        # 加密字段标记
            audit: boolean            # 审计字段标记
            searchable: boolean       # 可搜索标记
      
      # 索引定义
      indexes:
        - name: string                # 索引名称
          type: btree|hash|gin|gist   # 索引类型
          fields: string[]            # 索引字段
          unique: boolean             # 唯一索引
          partial: string             # 部分索引条件
          include: string[]           # 包含字段
          
      # 关联关系
      relations:
        - name: string                # 关联名称
          type: hasOne|hasMany|belongsTo|manyToMany
          target: string              # 目标实体
          foreign_key: string         # 外键字段
          through: string             # 中间表（多对多）
          cascade: boolean            # 级联操作
          
      # 继承关系
      inheritance:
        strategy: table_per_class|single_table|joined_table
        parent: string                # 父类实体
        discriminator: string         # 区分字段
        
      # 分区策略
      partitioning:
        strategy: range|hash|list     # 分区策略
        key: string                   # 分区键
        partitions:                   # 分区定义
          - name: string
            condition: string
            
      # 多租户支持
      tenant:
        enabled: boolean              # 启用多租户
        field: string                 # 租户字段
        strategy: shared|separate     # 隔离策略

  # 枚举定义
  enums:
    - name: string                    # 枚举名称
      values:                         # 枚举值
        - key: string
          value: string|number
          description: string
          
  # 联合类型定义
  unions:
    - name: string                    # 联合类型名称
      types: string[]                 # 包含的类型
      discriminator: string           # 区分字段

  # 值对象定义
  value_objects:
    - name: string                    # 值对象名称
      fields:                         # 字段定义（同entity）
        - name: string
          type: string
          constraints: object

# ============================================================================
# ② REST 端点定义
# ============================================================================
api:
  # 全局配置
  global:
    base_path: string                 # API基础路径
    version: string                   # API版本
    cors: object                      # CORS配置
    rate_limiting:                    # 全局限流
      requests_per_minute: number
      burst_size: number
    
  # 端点定义
  endpoints:
    - path: string                    # 端点路径
      method: GET|POST|PUT|DELETE|PATCH
      name: string                    # 端点名称
      description: string             # 端点描述
      
      # 输入定义
      input:
        params:                       # 路径参数
          - name: string
            type: string
            required: boolean
            description: string
        query:                        # 查询参数
          - name: string
            type: string
            required: boolean
            default: any
            description: string
        body:                         # 请求体
          type: string                # 引用的类型
          required: boolean
          schema: object              # 内联schema
        headers:                      # 请求头
          - name: string
            type: string
            required: boolean
            
      # 输出定义
      output:
        success:                      # 成功响应
          status_code: number
          type: string
          schema: object
          headers: object
        errors:                       # 错误响应映射
          - status_code: number
            type: string
            message: string
            schema: object
            
      # 校验规则
      validation:
        - field: string               # 校验字段
          rules:                      # 校验规则
            - type: required|min|max|pattern|custom
              value: any
              message: string
              
      # 中间件配置
      middleware:
        - name: string                # 中间件名称
          config: object              # 中间件配置
          order: number               # 执行顺序
          
      # 安全配置
      security:
        authentication:               # 认证要求
          required: boolean
          schemes: string[]           # 认证方案
        authorization:                # 授权要求
          required: boolean
          permissions: string[]       # 所需权限
          roles: string[]             # 所需角色
        rate_limiting:                # 端点级限流
          requests_per_minute: number
          
      # 缓存配置
      cache:
        enabled: boolean              # 启用缓存
        ttl: number                   # 缓存时长
        key_pattern: string           # 缓存键模式
        invalidation: string[]        # 失效条件

# ============================================================================
# ③ 长事务 / Saga / 补偿流程
# ============================================================================
sagas:
  - name: string                      # Saga名称
    description: string               # Saga描述
    trigger:                          # 触发条件
      event: string                   # 触发事件
      condition: string               # 触发条件表达式
      
    # 步骤定义
    steps:
      - name: string                  # 步骤名称
        type: action|compensation     # 步骤类型
        service: string               # 目标服务
        operation: string             # 操作名称
        
        # 输入输出
        input:                        # 输入映射
          from: string                # 数据来源
          mapping: object             # 字段映射
        output:                       # 输出映射
          to: string                  # 数据目标
          mapping: object             # 字段映射
          
        # 重试策略
        retry:
          max_attempts: number        # 最大重试次数
          backoff: linear|exponential|fixed
          delay: number               # 延迟时间
          timeout: number             # 超时时间
          
        # 补偿操作
        compensation:
          service: string             # 补偿服务
          operation: string           # 补偿操作
          input: object               # 补偿输入
          
        # 并行分支
        parallel:
          branches:                   # 并行分支
            - steps: string[]         # 分支步骤
              join_policy: all|any|majority
              
    # 全局配置
    config:
      timeout: number                 # 全局超时
      isolation_level: string         # 隔离级别
      consistency: eventual|strong    # 一致性级别

# ============================================================================
# ④ 事件驱动
# ============================================================================
events:
  # 事件定义
  definitions:
    - name: string                    # 事件名称
      version: string                 # 事件版本
      schema:                         # 事件schema
        type: object
        properties: object
      metadata:                       # 事件元数据
        source: string                # 事件源
        subject: string               # 事件主题
        time: boolean                 # 时间戳
        correlation_id: boolean       # 关联ID
        
  # 发布配置
  publishers:
    - event: string                   # 发布的事件
      trigger:                        # 发布触发条件
        on: create|update|delete      # 触发时机
        entity: string                # 关联实体
        condition: string             # 发布条件
      channel: string                 # 发布通道
      format: json|avro|protobuf      # 消息格式
      
  # 订阅配置
  subscribers:
    - event: string                   # 订阅的事件
      handler: string                 # 处理器名称
      channel: string                 # 订阅通道
      filter: string                  # 过滤条件
      batch:                          # 批处理配置
        enabled: boolean
        size: number
        timeout: number
      retry:                          # 重试配置
        max_attempts: number
        backoff: string
        dead_letter: boolean
        
  # 事件溯源
  event_sourcing:
    enabled: boolean                  # 启用事件溯源
    entities: string[]                # 应用实体
    snapshot:                         # 快照配置
      enabled: boolean
      frequency: number
      storage: string
      
  # CDC配置
  change_data_capture:
    enabled: boolean                  # 启用CDC
    tables: string[]                  # 监听表
    format: debezium|custom           # CDC格式
    destination: string               # 输出目标

# ============================================================================
# ⑤ 多租户 / 行级权限 / RBAC / ABAC
# ============================================================================
security:
  # 多租户配置
  multi_tenant:
    enabled: boolean                  # 启用多租户
    strategy: shared_db|separate_db|schema_per_tenant
    tenant_field: string              # 租户标识字段
    resolution: header|subdomain|path # 租户解析方式
    
  # RBAC配置
  rbac:
    enabled: boolean                  # 启用RBAC
    roles:                            # 角色定义
      - name: string
        description: string
        permissions: string[]
        inherits_from: string[]
    permissions:                      # 权限定义
      - name: string
        description: string
        resource: string
        actions: string[]
        
  # ABAC配置
  abac:
    enabled: boolean                  # 启用ABAC
    policies:                         # 策略定义
      - name: string
        description: string
        subject: object               # 主体属性
        resource: object              # 资源属性
        action: string                # 动作
        condition: string             # 条件表达式
        effect: allow|deny            # 策略效果
        
  # 行级安全
  row_level_security:
    enabled: boolean                  # 启用行级安全
    policies:                         # 行级策略
      - entity: string
        policy: string                # 策略表达式
        roles: string[]               # 应用角色
        
  # 字段级安全
  field_level_security:
    enabled: boolean                  # 启用字段级安全
    policies:                         # 字段策略
      - entity: string
        field: string
        access: read|write|none
        condition: string             # 访问条件

# ============================================================================
# ⑥ 缓存策略
# ============================================================================
cache:
  # 全局缓存配置
  global:
    default_ttl: number               # 默认TTL
    key_prefix: string                # 键前缀
    serialization: json|msgpack       # 序列化格式
    
  # 本地缓存
  local:
    enabled: boolean                  # 启用本地缓存
    type: lru|lfu|fifo                # 缓存算法
    max_size: number                  # 最大大小
    ttl: number                       # 生存时间
    
  # Redis缓存
  redis:
    enabled: boolean                  # 启用Redis缓存
    cluster: boolean                  # 集群模式
    connection: object                # 连接配置
    
  # 分布式缓存一致性
  consistency:
    strategy: write_through|write_back|write_around
    invalidation:                     # 失效策略
      - events: string[]              # 触发事件
        keys: string[]                # 失效键模式
      
  # 缓存策略
  strategies:
    - name: string                    # 策略名称
      entities: string[]              # 应用实体
      operations: string[]            # 应用操作
      ttl: number                     # 缓存时长
      warm_up: boolean                # 预热策略
      tags: string[]                  # 缓存标签

# ============================================================================
# ⑦ 性能调优
# ============================================================================
performance:
  # 数据库优化
  database:
    # 索引提示
    index_hints:
      - query: string                 # 查询模式
        hint: string                  # 索引提示
        
    # 读写分离
    read_write_separation:
      enabled: boolean                # 启用读写分离
      read_replicas: string[]         # 读副本配置
      write_master: string            # 写主库配置
      routing_rules:                  # 路由规则
        - operation: read|write
          condition: string
          target: string
          
    # 连接池
    connection_pool:
      min_size: number                # 最小连接数
      max_size: number                # 最大连接数
      acquire_timeout: number         # 获取超时
      idle_timeout: number            # 空闲超时
      
  # 批处理优化
  batch_processing:
    enabled: boolean                  # 启用批处理
    batch_size: number                # 批次大小
    timeout: number                   # 批处理超时
    operations: string[]              # 应用操作
    
  # 分页优化
  pagination:
    default_size: number              # 默认页大小
    max_size: number                  # 最大页大小
    cursor_based: boolean             # 游标分页
    count_optimization: boolean       # 计数优化
    
  # 查询优化
  query_optimization:
    # N+1查询检测
    n_plus_1_detection: boolean
    # 慢查询监控
    slow_query_threshold: number
    # 查询计划缓存
    plan_cache: boolean

# ============================================================================
# ⑧ 外部集成
# ============================================================================
integrations:
  # HTTP客户端
  http:
    - name: string                    # 客户端名称
      base_url: string                # 基础URL
      timeout: number                 # 超时时间
      retry:                          # 重试策略
        max_attempts: number
        backoff: string
      auth:                           # 认证配置
        type: basic|bearer|oauth2
        config: object
      headers: object                 # 默认请求头
      
  # gRPC客户端
  grpc:
    - name: string                    # 客户端名称
      proto_file: string              # Proto文件路径
      service: string                 # 服务名称
      address: string                 # 服务地址
      options:                        # gRPC选项
        timeout: number
        max_receive_message_length: number
        
  # GraphQL客户端
  graphql:
    - name: string                    # 客户端名称
      endpoint: string                # GraphQL端点
      auth: object                    # 认证配置
      schema_file: string             # Schema文件
      
  # WebSocket
  websocket:
    - name: string                    # WebSocket名称
      path: string                    # WebSocket路径
      auth: boolean                   # 认证要求
      events:                         # 事件处理
        - event: string
          handler: string
          
  # 消息队列
  message_queues:
    - name: string                    # 队列名称
      type: rabbitmq|kafka|redis      # 队列类型
      connection: object              # 连接配置
      topics:                         # 主题配置
        - name: string
          partitions: number
          replication: number
          
  # 文件存储
  file_storage:
    - name: string                    # 存储名称
      type: s3|azure_blob|gcs|local   # 存储类型
      config: object                  # 存储配置
      access_control:                 # 访问控制
        public_read: boolean
        signed_urls: boolean

# ============================================================================
# ⑨ 脚本扩展
# ============================================================================
scripts:
  # 脚本引擎配置
  engines:
    javascript:                       # JavaScript引擎
      enabled: boolean
      version: es2020                 # ECMAScript版本
      sandbox:                        # 沙箱限制
        memory_limit: 128mb
        cpu_time_limit: 5s
        allowed_modules: string[]
        
    python:                           # Python引擎
      enabled: boolean
      version: "3.9"                  # Python版本
      sandbox:
        memory_limit: 256mb
        cpu_time_limit: 10s
        allowed_packages: string[]
        
    lua:                              # Lua引擎
      enabled: boolean
      version: "5.4"
      sandbox:
        memory_limit: 64mb
        cpu_time_limit: 3s
        
  # 脚本钩子
  hooks:
    # 实体生命周期钩子
    entity_hooks:
      - entity: string                # 实体名称
        event: before_create|after_create|before_update|after_update|before_delete|after_delete
        script: |                     # 内嵌脚本
          // JavaScript/Python/Lua code
        file: string                  # 外部脚本文件
        
    # API请求钩子
    api_hooks:
      - endpoint: string              # API端点
        event: before_request|after_request|on_error
        script: string
        
    # 业务逻辑钩子
    business_hooks:
      - name: string                  # 钩子名称
        trigger: string               # 触发条件
        script: string                # 脚本内容

# ============================================================================
# 2️⃣ 语法设计原则实现
# ============================================================================

# 模板参数化示例
templates:
  - name: crud_template               # 模板名称
    parameters:                       # 模板参数
      - name: entity_name
        type: string
        required: true
      - name: enable_soft_delete
        type: boolean
        default: false
    content: |                        # 模板内容
      api:
        endpoints:
          - path: "/api/{{entity_name | lowercase}}"
            method: GET
            name: "list_{{entity_name}}"
            {{#if enable_soft_delete}}
            query:
              - name: include_deleted
                type: boolean
                default: false
            {{/if}}

# 版本演进策略
migration:
  version: "1.0.0"                    # 当前版本
  from_version: "0.9.0"               # 源版本
  changes:                            # 变更列表
    - type: add_field                 # 变更类型
      entity: User
      field: email_verified
      default: false
    - type: rename_field              # 重命名字段
      entity: Order
      from: total_price
      to: total_amount
  backward_compatible: true           # 向后兼容性
  auto_migration: true                # 自动迁移

# ============================================================================
# 3️⃣ 类型系统
# ============================================================================
types:
  # 基础类型
  primitives:
    - string: { min_length: 0, max_length: 65535, pattern: "string" }
    - number: { min: -Infinity, max: Infinity, precision: 15 }
    - integer: { min: -2147483648, max: 2147483647 }
    - boolean: { values: [true, false] }
    - date: { format: "YYYY-MM-DD" }
    - datetime: { format: "ISO8601" }
    - uuid: { version: 4, format: "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx" }
    
  # 自定义标量类型
  custom_scalars:
    money:                            # 货币类型
      base: decimal
      precision: 10
      scale: 2
      currency: required
      validation: positive
      
    email:                            # 邮箱类型
      base: string
      pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
      max_length: 254
      normalization: lowercase
      
    phone:                            # 电话类型
      base: string
      pattern: "^\\+?[1-9]\\d{1,14}$"
      normalization: e164
      
    jsonb:                            # JSONB类型
      base: object
      validation: json_schema
      indexable: true
      
  # 联合类型
  union_types:
    - name: PaymentMethod
      types: [CreditCard, BankTransfer, PayPal]
      discriminator: type
      
  # 可选链和默认值
  optional_chaining:
    syntax: "field?.nested?.value"    # 可选链语法
    null_coalescing: "field ?? default"  # 空合并语法
    
  # 校验函数
  validation_functions:
    - name: validate_credit_card      # 自定义校验函数
      language: javascript
      code: |
        function validate(value) {
          // Luhn算法验证信用卡号
          return luhnCheck(value);
        }
        
  # 自定义序列化
  serialization:
    - type: money
      format: json
      serializer: |
        {
          "amount": value.amount,
          "currency": value.currency
        }

# ============================================================================
# 4️⃣ 执行模型
# ============================================================================
execution:
  # 同步/异步模型
  concurrency:
    default_mode: async               # 默认执行模式
    sync_operations: string[]         # 同步操作列表
    async_operations: string[]        # 异步操作列表
    
  # 事务边界
  transactions:
    default_isolation: read_committed  # 默认隔离级别
    boundaries:                       # 事务边界定义
      - name: order_processing
        operations: string[]          # 事务内操作
        isolation: serializable
        timeout: 30s
        rollback_on: string[]         # 回滚条件
        
  # 幂等性
  idempotency:
    enabled: boolean                  # 启用幂等性
    key_generation: uuid|hash|custom  # 幂等键生成策略
    ttl: 24h                          # 幂等键TTL
    storage: redis|database           # 存储方式
    
  # 分布式锁
  distributed_locks:
    enabled: boolean                  # 启用分布式锁
    backend: redis|etcd|zookeeper     # 锁后端
    timeout: 10s                      # 锁超时
    retry: 3                          # 重试次数
    
  # 并发控制
  concurrency_control:
    model: sequential|eventually_consistent
    conflict_resolution: last_write_wins|merge|error
    vector_clocks: boolean            # 向量时钟

# ============================================================================
# 5️⃣ 安全与治理
# ============================================================================
governance:
  # 敏感字段处理
  data_privacy:
    sensitive_fields:                 # 敏感字段标记
      - field: "*.password"
        treatment: hash_with_salt
      - field: "*.ssn"
        treatment: encrypt_with_key
      - field: "*.email"
        treatment: mask_partial
        
    # 数据脱敏
    anonymization:
      - field: user.name
        method: pseudonymize
        algorithm: k_anonymity
        k_value: 5
        
  # 审计日志
  audit:
    enabled: boolean                  # 启用审计
    events: string[]                  # 审计事件
    storage: database|elasticsearch   # 存储方式
    retention: 7y                     # 保留期限
    fields:                           # 审计字段
      - user_id: required
      - action: required
      - resource: required
      - timestamp: required
      - ip_address: optional
      
  # 加密字段
  encryption:
    algorithm: aes256                 # 加密算法
    key_management: vault|kms         # 密钥管理
    fields:                           # 加密字段
      - entity: User
        field: ssn
        key_id: user_data_key
        
  # SQL注入防护
  sql_injection_prevention:
    parameterized_queries: enforce    # 强制参数化查询
    input_sanitization: enabled      # 输入清理
    query_validation: enabled        # 查询验证
    
  # 部署治理
  deployment:
    # 灰度发布
    canary:
      enabled: boolean                # 启用灰度发布
      traffic_split: 5%               # 流量分配
      success_criteria:               # 成功标准
        error_rate: <1%
        latency_p99: <500ms
      duration: 30m                   # 灰度时长
      
    # 熔断器
    circuit_breaker:
      enabled: boolean                # 启用熔断器
      failure_threshold: 50%          # 失败阈值
      timeout: 60s                    # 熔断超时
      half_open_requests: 10          # 半开请求数
      
    # 限流
    rate_limiting:
      global: 1000/min                # 全局限流
      per_user: 100/min               # 用户限流
      per_ip: 60/min                  # IP限流
      
    # 超时控制
    timeouts:
      http_client: 30s                # HTTP客户端超时
      database: 10s                   # 数据库超时
      cache: 1s                       # 缓存超时

# ============================================================================
# 6️⃣ 可观测性
# ============================================================================
observability:
  # 指标收集
  metrics:
    enabled: boolean                  # 启用指标
    collection_interval: 15s          # 收集间隔
    custom_metrics:                   # 自定义指标
      - name: order_processing_time
        type: histogram
        buckets: [0.1, 0.5, 1, 2, 5]
        labels: [tenant_id, order_type]
        
  # 分布式追踪
  tracing:
    enabled: boolean                  # 启用追踪
    sampler: probabilistic            # 采样策略
    sample_rate: 0.1                  # 采样率
    exporters:                        # 导出器
      - jaeger
      - zipkin
      
  # 日志记录
  logging:
    level: info                       # 日志级别
    format: json                      # 日志格式
    structured: true                  # 结构化日志
    correlation_id: true              # 关联ID
    sensitive_data_filtering: true    # 敏感数据过滤
    
  # 告警配置
  alerting:
    rules:                            # 告警规则
      - name: high_error_rate
        condition: error_rate > 5%
        duration: 5m
        severity: critical
        channels: [email, slack]
        
  # 健康检查
  health_checks:
    - name: database
      type: database_connection
      interval: 30s
      timeout: 5s
      
    - name: external_service
      type: http_endpoint
      url: https://api.example.com/health
      interval: 60s

# ============================================================================
# 7️⃣ 工具链与生态 - JSON Schema 元模型
# ============================================================================
json_schema:
  "$schema": "https://json-schema.org/draft/2020-12/schema"
  "$id": "https://dsl.example.com/schema/v1"
  "title": "Server Logic DSL Schema"
  "type": "object"
  "properties":
    service:
      type: object
      required: [meta, domain]
      properties:
        meta:
          type: object
          required: [name, version]
          properties:
            name: { type: string, pattern: "^[a-zA-Z][a-zA-Z0-9_-]*$" }
            version: { type: string, pattern: "^\\d+\\.\\d+\\.\\d+$" }
            description: { type: string }
            namespace: { type: string }
            tags: { type: array, items: { type: string } }
        domain:
          type: object
          properties:
            entities:
              type: array
              items:
                type: object
                required: [name, fields]
                properties:
                  name: { type: string }
                  table: { type: string }
                  fields:
                    type: array
                    items:
                      type: object
                      required: [name, type]
                      properties:
                        name: { type: string }
                        type: { 
                          type: string, 
                          enum: [string, number, integer, boolean, date, datetime, uuid, money, email, phone, jsonb]
                        }
                        nullable: { type: boolean, default: false }
                        default: true
                        constraints:
                          type: object
                          properties:
                            min: { type: number }
                            max: { type: number }
                            pattern: { type: string }
                            unique: { type: boolean }
                            references: { type: string }
  "additionalProperties": false