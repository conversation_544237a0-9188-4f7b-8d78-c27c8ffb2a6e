# ============================================================================
# 8️⃣ 复杂场景示例：电商订单跨库Saga + 库存扣减 + 优惠券 + 多租户 + 灰度
# ============================================================================

# 服务基本信息
service:
  meta:
    name: "ecommerce-order-service"        # 电商订单服务
    version: "2.1.0"                      # 服务版本
    description: "电商订单处理微服务，支持跨库事务、库存管理、优惠券系统"
    namespace: "ecommerce"                 # 业务命名空间
    tags: ["order", "saga", "multi-tenant", "canary"]  # 服务标签

  # 参数化配置 - 支持环境差异化部署
  parameters:
    environments:
      - name: "ENABLE_INVENTORY_SAGA"      # 是否启用库存Saga
        type: boolean
        default: true
        description: "启用分布式库存扣减事务"
      - name: "COUPON_SERVICE_TIMEOUT"     # 优惠券服务超时
        type: number
        default: 3000
        description: "优惠券服务调用超时时间(ms)"
    
    feature_flags:
      - name: "new_coupon_engine"          # 新优惠券引擎开关
        enabled: false
        environments: ["staging", "production"]
      - name: "inventory_reservation"      # 库存预留功能
        enabled: true
        environments: ["production"]

# ============================================================================
# 领域模型定义 - 支持多租户的电商实体
# ============================================================================
domain:
  entities:
    # 订单主实体 - 分布式事务的聚合根
    - name: "Order"
      table: "orders"
      description: "订单聚合根，管理整个订单生命周期"
      
      fields:
        - name: "id"                       # 订单主键
          type: "uuid"
          nullable: false
          description: "订单唯一标识符"
          
        - name: "tenant_id"                # 多租户标识
          type: "uuid"
          nullable: false
          description: "租户标识，实现数据隔离"
          
        - name: "customer_id"              # 客户标识
          type: "uuid"
          nullable: false
          description: "下单客户ID"
          
        - name: "order_number"             # 订单号
          type: "string"
          nullable: false
          constraints:
            pattern: "^ORD-\\d{8}-\\d{6}$"
            unique: true
          description: "业务订单号，格式：ORD-YYYYMMDD-HHMMSS"
          
        - name: "status"                   # 订单状态
          type: "string"
          nullable: false
          default: "pending"
          description: "订单状态：pending/confirmed/shipped/delivered/cancelled"
          
        - name: "total_amount"             # 订单总额
          type: "money"
          nullable: false
          constraints:
            min: 0
          description: "订单总金额，包含优惠后价格"
          
        - name: "original_amount"          # 原始金额
          type: "money"
          nullable: false
          description: "优惠前原始金额"
          
        - name: "discount_amount"          # 折扣金额
          type: "money"
          nullable: false
          default: 0
          description: "优惠券等折扣总额"
          
        - name: "saga_id"                  # Saga标识
          type: "uuid"
          nullable: true
          description: "关联的Saga事务ID"
          
        - name: "created_at"              # 创建时间
          type: "datetime"
          nullable: false
          default: "NOW()"
          
        - name: "updated_at"              # 更新时间
          type: "datetime"
          nullable: false
          default: "NOW()"
          annotations:
            audit: true                    # 审计字段
      
      # 数据库索引优化
      indexes:
        - name: "idx_tenant_customer"      # 多租户+客户复合索引
          fields: ["tenant_id", "customer_id"]
          type: "btree"
          
        - name: "idx_order_number"         # 订单号唯一索引
          fields: ["order_number"]
          unique: true
          
        - name: "idx_status_created"       # 状态+时间索引，优化列表查询
          fields: ["tenant_id", "status", "created_at"]
          
        - name: "idx_saga_id"              # Saga关联索引
          fields: ["saga_id"]
          partial: "saga_id IS NOT NULL"   # 部分索引，只索引非空值
      
      # 实体关联关系
      relations:
        - name: "order_items"              # 一对多：订单商品
          type: "hasMany"
          target: "OrderItem"
          foreign_key: "order_id"
          cascade: true
          
        - name: "coupon_usages"            # 一对多：优惠券使用记录
          type: "hasMany"
          target: "CouponUsage"
          foreign_key: "order_id"
          
        - name: "payments"                 # 一对多：支付记录
          type: "hasMany"
          target: "Payment"
          foreign_key: "order_id"
      
      # 多租户配置
      tenant:
        enabled: true                      # 启用多租户
        field: "tenant_id"                 # 租户字段
        strategy: "shared"                 # 共享数据库策略

    # 订单商品项
    - name: "OrderItem"
      table: "order_items"
      description: "订单商品明细，记录每个商品的购买信息"
      
      fields:
        - name: "id"
          type: "uuid"
          nullable: false
          
        - name: "tenant_id"                # 继承租户标识
          type: "uuid"
          nullable: false
          
        - name: "order_id"                 # 所属订单
          type: "uuid"
          nullable: false
          constraints:
            references: "Order.id"
            
        - name: "product_id"               # 商品标识
          type: "uuid"
          nullable: false
          
        - name: "product_name"             # 商品名称快照
          type: "string"
          nullable: false
          constraints:
            max_length: 200
          description: "下单时商品名称快照，避免商品信息变更影响历史订单"
          
        - name: "quantity"                 # 购买数量
          type: "integer"
          nullable: false
          constraints:
            min: 1
            
        - name: "unit_price"               # 单价
          type: "money"
          nullable: false
          
        - name: "total_price"              # 小计
          type: "money"
          nullable: false
          description: "quantity * unit_price"
          
        - name: "inventory_reserved"       # 库存预留状态
          type: "boolean"
          nullable: false
          default: false
          description: "库存是否已预留"
          
        - name: "reservation_id"           # 库存预留ID
          type: "uuid"
          nullable: true
          description: "库存系统返回的预留标识"
      
      indexes:
        - name: "idx_order_tenant"
          fields: ["order_id", "tenant_id"]
          
        - name: "idx_product_reservation"
          fields: ["product_id", "reservation_id"]
          partial: "reservation_id IS NOT NULL"
      
      relations:
        - name: "order"
          type: "belongsTo"
          target: "Order"
          foreign_key: "order_id"
      
      tenant:
        enabled: true
        field: "tenant_id"
        strategy: "shared"

    # 优惠券使用记录
    - name: "CouponUsage"
      table: "coupon_usages"
      description: "优惠券使用记录，支持一单多券"
      
      fields:
        - name: "id"
          type: "uuid"
          nullable: false
          
        - name: "tenant_id"
          type: "uuid"
          nullable: false
          
        - name: "order_id"
          type: "uuid"
          nullable: false
          constraints:
            references: "Order.id"
            
        - name: "coupon_code"              # 优惠券码
          type: "string"
          nullable: false
          constraints:
            max_length: 50
            
        - name: "discount_amount"          # 折扣金额
          type: "money"
          nullable: false
          
        - name: "used_at"                  # 使用时间
          type: "datetime"
          nullable: false
          default: "NOW()"
      
      indexes:
        - name: "idx_coupon_usage"
          fields: ["tenant_id", "coupon_code", "used_at"]
          
        - name: "idx_order_coupon"
          fields: ["order_id"]
      
      tenant:
        enabled: true
        field: "tenant_id"
        strategy: "shared"

# ============================================================================
# API端点定义 - RESTful订单接口
# ============================================================================
api:
  global:
    base_path: "/api/v1"                  # API基础路径
    version: "1.0"                        # API版本
    cors:                                 # CORS配置
      origin: "*"
      methods: ["GET", "POST", "PUT", "DELETE"]
      credentials: true
    rate_limiting:                        # 全局限流
      requests_per_minute: 1000
      burst_size: 100

  endpoints:
    # 创建订单接口 - 触发分布式Saga事务
    - path: "/orders"
      method: "POST"
      name: "create_order"
      description: "创建新订单，触发库存扣减和优惠券使用的分布式事务"
      
      input:
        body:                             # 请求体定义
          type: "CreateOrderRequest"
          required: true
          schema:
            type: "object"
            required: ["customer_id", "items"]
            properties:
              customer_id:                # 客户ID
                type: "string"
                format: "uuid"
                description: "下单客户标识"
              items:                      # 订单商品列表
                type: "array"
                minItems: 1
                maxItems: 50              # 限制单次最多50个商品
                items:
                  type: "object"
                  required: ["product_id", "quantity"]
                  properties:
                    product_id:
                      type: "string"
                      format: "uuid"
                    quantity:
                      type: "integer"
                      minimum: 1
                      maximum: 999
                    unit_price:           # 可选，前端传入的单价用于验证
                      type: "number"
                      format: "money"
              coupon_codes:               # 优惠券码列表
                type: "array"
                maxItems: 5               # 最多5个优惠券
                items:
                  type: "string"
                  maxLength: 50
              shipping_address:           # 收货地址
                type: "object"
                required: ["province", "city", "detail"]
                properties:
                  province:
                    type: "string"
                    maxLength: 50
                  city:
                    type: "string"
                    maxLength: 50
                  detail:
                    type: "string"
                    maxLength: 200
        
        headers:                          # 请求头
          - name: "X-Tenant-ID"           # 租户标识头
            type: "string"
            required: true
            description: "多租户标识"
            
          - name: "X-User-ID"             # 用户标识头
            type: "string"
            required: true
            description: "操作用户标识，用于审计"
      
      output:
        success:                          # 成功响应
          status_code: 201
          type: "CreateOrderResponse"
          schema:
            type: "object"
            properties:
              order_id:
                type: "string"
                format: "uuid"
                description: "创建的订单ID"
              order_number:
                type: "string"
                description: "订单编号"
              saga_id:
                type: "string"
                format: "uuid"
                description: "Saga事务ID，用于跟踪分布式事务状态"
              total_amount:
                type: "number"
                format: "money"
                description: "订单总金额"
              status:
                type: "string"
                enum: ["pending", "processing"]
                description: "订单初始状态"
        
        errors:                           # 错误响应映射
          - status_code: 400
            type: "ValidationError"
            message: "请求参数验证失败"
            schema:
              type: "object"
              properties:
                code:
                  type: "string"
                  example: "VALIDATION_FAILED"
                message:
                  type: "string"
                  example: "商品数量必须大于0"
                details:
                  type: "array"
                  items:
                    type: "object"
                    properties:
                      field:
                        type: "string"
                      error:
                        type: "string"
                        
          - status_code: 409
            type: "BusinessError"
            message: "业务规则冲突"
            schema:
              type: "object"
              properties:
                code:
                  type: "string"
                  enum: ["INSUFFICIENT_INVENTORY", "COUPON_EXPIRED", "COUPON_USED"]
                message:
                  type: "string"
                saga_id:
                  type: "string"
                  format: "uuid"
                  description: "失败的Saga事务ID"
      
      # 输入验证规则
      validation:
        - field: "items"                  # 商品列表验证
          rules:
            - type: "custom"              # 自定义验证规则
              value: "validate_order_items"
              message: "商品信息验证失败"
              
        - field: "coupon_codes"           # 优惠券验证
          rules:
            - type: "custom"
              value: "validate_coupon_codes"
              message: "优惠券码格式或状态错误"
      
      # 中间件配置
      middleware:
        - name: "tenant_context"          # 租户上下文中间件
          config:
            header: "X-Tenant-ID"
            required: true
          order: 1
          
        - name: "user_context"            # 用户上下文中间件
          config:
            header: "X-User-ID"
            required: true
          order: 2
          
        - name: "request_logging"         # 请求日志中间件
          config:
            include_body: true
            mask_fields: ["payment_info"]
          order: 3
          
        - name: "idempotency"             # 幂等性中间件
          config:
            key_source: "header"          # 从请求头获取幂等键
            header_name: "Idempotency-Key"
            ttl: 3600                     # 1小时幂等窗口
          order: 4
      
      # 安全配置
      security:
        authentication:
          required: true
          schemes: ["bearer_token"]       # JWT Bearer认证
        authorization:
          required: true
          permissions: ["order:create"]   # 需要订单创建权限
        rate_limiting:
          requests_per_minute: 100        # 每分钟100个请求
          per_user: true                  # 按用户限流
      
      # 缓存配置（创建操作通常不缓存）
      cache:
        enabled: false

    # 订单状态查询接口 - 支持Saga状态追踪
    - path: "/orders/{order_id}"
      method: "GET"
      name: "get_order"
      description: "获取订单详情，包含Saga事务状态"
      
      input:
        params:
          - name: "order_id"              # 路径参数
            type: "string"
            format: "uuid"
            required: true
            description: "订单ID"
        
        query:
          - name: "include_saga_status"   # 是否包含Saga状态
            type: "boolean"
            default: false
            description: "是否返回详细的Saga执行状态"
            
          - name: "include_items"         # 是否包含商品详情
            type: "boolean"
            default: true
            description: "是否包含订单商品明细"
        
        headers:
          - name: "X-Tenant-ID"
            type: "string"
            required: true
      
      output:
        success:
          status_code: 200
          type: "OrderDetailResponse"
          schema:
            type: "object"
            properties:
              id:
                type: "string"
                format: "uuid"
              order_number:
                type: "string"
              status:
                type: "string"
                enum: ["pending", "confirmed", "shipped", "delivered", "cancelled"]
              total_amount:
                type: "number"
                format: "money"
              items:                      # 可选包含的商品列表
                type: "array"
                items:
                  type: "object"
                  properties:
                    product_id:
                      type: "string"
                      format: "uuid"
                    product_name:
                      type: "string"
                    quantity:
                      type: "integer"
                    unit_price:
                      type: "number"
                      format: "money"
                    inventory_status:
                      type: "string"
                      enum: ["reserved", "allocated", "insufficient"]
              saga_status:                # 可选包含的Saga状态
                type: "object"
                properties:
                  saga_id:
                    type: "string"
                    format: "uuid"
                  status:
                    type: "string"
                    enum: ["running", "completed", "failed", "compensating"]
                  current_step:
                    type: "string"
                  steps:
                    type: "array"
                    items:
                      type: "object"
                      properties:
                        name:
                          type: "string"
                        status:
                          type: "string"
                          enum: ["pending", "running", "completed", "failed", "compensated"]
                        started_at:
                          type: "string"
                          format: "date-time"
                        completed_at:
                          type: "string"
                          format: "date-time"
                        error:
                          type: "string"
              created_at:
                type: "string"
                format: "date-time"
              updated_at:
                type: "string"
                format: "date-time"
        
        errors:
          - status_code: 404
            type: "NotFoundError"
            message: "订单不存在或无权访问"
      
      middleware:
        - name: "tenant_context"
          order: 1
        - name: "cache_response"           # 响应缓存中间件
          config:
            ttl: 300                      # 5分钟缓存
            vary_by: ["tenant_id", "order_id", "include_saga_status"]
          order: 2
      
      security:
        authentication:
          required: true
        authorization:
          required: true
          permissions: ["order:read"]
        rate_limiting:
          requests_per_minute: 500
      
      cache:
        enabled: true
        ttl: 300                          # 5分钟缓存
        key_pattern: "order:{tenant_id}:{order_id}:{include_saga_status}"
        invalidation: ["order_updated"]   # 订单更新时失效

# ============================================================================
# 分布式Saga事务定义 - 订单处理的核心业务流程
# ============================================================================
sagas:
  # 订单处理Saga - 协调库存、优惠券、支付等多个服务
  - name: "order_processing_saga"
    description: "订单处理分布式事务，协调库存扣减、优惠券使用、支付等步骤"
    trigger:
      event: "order_created"              # 订单创建事件触发
      condition: "order.status == 'pending'"  # 仅处理待处理状态的订单
    
    steps:
      # 步骤1：验证订单基本信息
      - name: "validate_order"
        type: "action"
        service: "order_service"          # 本服务内部操作
        operation: "validate_order_info"
        description: "验证订单基本信息：客户状态、商品有效性等"
        
        input:                            # 输入数据映射
          from: "saga_context"            # 数据来源：Saga上下文
          mapping:
            order_id: "order.id"
            tenant_id: "order.tenant_id"
            customer_id: "order.customer_id"
            items: "order.items"
        
        output:                           # 输出数据映射
          to: "saga_context"
          mapping:
            validated_items: "validated_items"    # 验证后的商品列表
            customer_level: "customer.level"     # 客户等级
        
        retry:                            # 重试策略
          max_attempts: 3
          backoff: "exponential"          # 指数退避
          delay: 1000                     # 初始延迟1秒
          timeout: 10000                  # 10秒超时
        
        compensation:                     # 补偿操作
          service: "order_service"
          operation: "mark_validation_failed"
          input:
            order_id: "{{saga_context.order.id}}"
            reason: "validation_failed"

      # 步骤2：检查并预留库存（并行执行）
      - name: "reserve_inventory"
        type: "action"
        service: "inventory_service"      # 外部库存服务
        operation: "reserve_items"
        description: "为订单商品预留库存，支持并发安全"
        
        input:
          from: "saga_context"
          mapping:
            tenant_id: "order.tenant_id"
            items: "validated_items"      # 使用验证后的商品列表
            reservation_id: "saga_id"     # 使用Saga ID作为预留标识
            expiry_minutes: 30            # 预留30分钟过期
        
        output:
          to: "saga_context"
          mapping:
            reservations: "reservations"  # 预留结果列表
            total_reserved_value: "total_value"
        
        retry:
          max_attempts: 3
          backoff: "linear"
          delay: 2000
          timeout: 15000                  # 库存服务可能较慢
        
        compensation:                     # 库存释放补偿
          service: "inventory_service"
          operation: "release_reservation"
          input:
            reservation_id: "{{saga_context.saga_id}}"
            tenant_id: "{{saga_context.order.tenant_id}}"

      # 步骤3：验证和锁定优惠券（与库存预留并行）
      - name: "validate_coupons"
        type: "action"
        service: "coupon_service"         # 外部优惠券服务
        operation: "validate_and_lock"
        description: "验证优惠券有效性并临时锁定"
        
        parallel:                         # 与库存预留并行执行
          with: ["reserve_inventory"]
          join_policy: "all"              # 两个步骤都成功才继续
        
        input:
          from: "saga_context"
          mapping:
            tenant_id: "order.tenant_id"
            customer_id: "order.customer_id"
            coupon_codes: "order.coupon_codes"
            order_amount: "order.original_amount"
            customer_level: "customer_level"
        
        output:
          to: "saga_context"
          mapping:
            validated_coupons: "valid_coupons"
            total_discount: "total_discount_amount"
            final_amount: "final_order_amount"
        
        retry:
          max_attempts: 2                 # 优惠券服务重试次数较少
          backoff: "fixed"
          delay: 1000
          timeout: 5000
        
        compensation:                     # 优惠券解锁补偿
          service: "coupon_service"
          operation: "unlock_coupons"
          input:
            lock_id: "{{saga_context.saga_id}}"
            coupon_codes: "{{saga_context.order.coupon_codes}}"

      # 步骤4：更新订单金额和状态
      - name: "update_order_amount"
        type: "action"
        service: "order_service"
        operation: "update_pricing"
        description: "根据优惠券计算结果更新订单最终金额"
        
        input:
          from: "saga_context"
          mapping:
            order_id: "order.id"
            final_amount: "final_amount"
            discount_amount: "total_discount"
            coupon_details: "validated_coupons"
        
        output:
          to: "saga_context"
          mapping:
            updated_order: "order"
        
        retry:
          max_attempts: 5                 # 本地操作，可以多重试
          backoff: "exponential"
          delay: 500
          timeout: 5000
        
        compensation:
          service: "order_service"
          operation: "revert_pricing"
          input:
            order_id: "{{saga_context.order.id}}"
            original_amount: "{{saga_context.order.original_amount}}"

      # 步骤5：创建支付请求
      - name: "create_payment"
        type: "action"
        service: "payment_service"        # 外部支付服务
        operation: "create_payment_intent"
        description: "创建支付意图，准备支付流程"
        
        input:
          from: "saga_context"
          mapping:
            tenant_id: "order.tenant_id"
            order_id: "order.id"
            customer_id: "order.customer_id"
            amount: "final_amount"
            currency: "CNY"
            description: "订单支付"
        
        output:
          to: "saga_context"
          mapping:
            payment_intent_id: "payment_intent.id"
            payment_status: "payment_intent.status"
        
        retry:
          max_attempts: 3
          backoff: "exponential"
          delay: 2000
          timeout: 20000                  # 支付服务可能需要更长时间
        
        compensation:
          service: "payment_service"
          operation: "cancel_payment_intent"
          input:
            payment_intent_id: "{{saga_context.payment_intent_id}}"

      # 步骤6：确认订单（最终提交）
      - name: "confirm_order"
        type: "action"
        service: "order_service"
        operation: "confirm_order"
        description: "确认订单，将状态更新为已确认"
        
        input:
          from: "saga_context"
          mapping:
            order_id: "order.id"
            saga_id: "saga_id"
            payment_intent_id: "payment_intent_id"
            reservations: "reservations"
            coupon_usages: "validated_coupons"
        
        output:
          to: "saga_context"
          mapping:
            confirmed_order: "order"
            confirmation_time: "confirmed_at"
        
        retry:
          max_attempts: 5
          backoff: "exponential"
          delay: 1000
          timeout: 10000
        
        compensation:
          service: "order_service"
          operation: "cancel_order"
          input:
            order_id: "{{saga_context.order.id}}"
            reason: "confirmation_failed"

      # 步骤7：发送订单确认通知（异步，不影响主流程）
      - name: "send_confirmation"
        type: "action"
        service: "notification_service"   # 通知服务
        operation: "send_order_confirmation"
        description: "发送订单确认通知给客户"
        
        input:
          from: "saga_context"
          mapping:
            tenant_id: "order.tenant_id"
            customer_id: "order.customer_id"
            order_id: "order.id"
            order_number: "order.order_number"
            amount: "final_amount"
        
        retry:
          max_attempts: 3
          backoff: "exponential"
          delay: 2000
          timeout: 10000
        
        # 通知失败不需要补偿，不影响订单状态
        compensation:
          service: "notification_service"
          operation: "log_notification_failure"
          input:
            order_id: "{{saga_context.order.id}}"
            notification_type: "order_confirmation"
    
    # Saga全局配置
    config:
      timeout: 300000                     # 5分钟总超时
      isolation_level: "read_committed"   # 读已提交隔离级别
      consistency: "eventual"             # 最终一致性
      max_parallel_steps: 3               # 最大并行步骤数
      retry_failed_saga: true             # 失败后是否重试整个Saga
      compensation_timeout: 60000         # 补偿操作超时1分钟

# ============================================================================
# 事件驱动配置 - 订单状态变更的事件发布订阅
# ============================================================================
events:
  definitions:
    # 订单创建事件
    - name: "order_created"
      version: "v1"
      schema:
        type: "object"
        required: ["order_id", "tenant_id", "customer_id", "items", "total_amount"]
        properties:
          order_id:
            type: "string"
            format: "uuid"
          tenant_id:
            type: "string"
            format: "uuid"
          customer_id:
            type: "string"
            format: "uuid"
          order_number:
            type: "string"
          items:
            type: "array"
            items:
              type: "object"
              properties:
                product_id:
                  type: "string"
                  format: "uuid"
                quantity:
                  type: "integer"
                unit_price:
                  type: "number"
          total_amount:
            type: "number"
          coupon_codes:
            type: "array"
            items:
              type: "string"
      metadata:
        source: "order_service"
        subject: "order"
        time: true                        # 包含事件时间戳
        correlation_id: true              # 包含关联ID

    # 订单确认事件
    - name: "order_confirmed"
      version: "v1"
      schema:
        type: "object"
        required: ["order_id", "tenant_id", "saga_id", "confirmed_at"]
        properties:
          order_id:
            type: "string"
            format: "uuid"
          tenant_id:
            type: "string"
            format: "uuid"
          saga_id:
            type: "string"
            format: "uuid"
          order_number:
            type: "string"
          final_amount:
            type: "number"
          confirmed_at:
            type: "string"
            format: "date-time"
      metadata:
        source: "order_service"
        subject: "order"

    # Saga步骤完成事件
    - name: "saga_step_completed"
      version: "v1"
      schema:
        type: "object"
        required: ["saga_id", "step_name", "status", "completed_at"]
        properties:
          saga_id:
            type: "string"
            format: "uuid"
          step_name:
            type: "string"
          status:
            type: "string"
            enum: ["completed", "failed", "compensated"]
          completed_at:
            type: "string"
            format: "date-time"
          result:
            type: "object"
          error:
            type: "string"

  # 事件发布配置
  publishers:
    # 订单创建时发布事件
    - event: "order_created"
      trigger:
        on: "create"                      # 创建时触发
        entity: "Order"                   # 监听Order实体
        condition: "order.status == 'pending'"  # 发布条件
      channel: "order_events"             # 发布到订单事件通道
      format: "json"                      # JSON格式
      
    # 订单确认时发布事件
    - event: "order_confirmed"
      trigger:
        on: "update"                      # 更新时触发
        entity: "Order"
        condition: "order.status == 'confirmed' AND previous.status != 'confirmed'"
      channel: "order_events"
      format: "json"

  # 事件订阅配置
  subscribers:
    # 订阅订单创建事件，触发Saga
    - event: "order_created"
      handler: "start_order_processing_saga"
      channel: "order_events"
      filter: "tenant_id IN ('{{allowed_tenants}}')"  # 租户过滤
      batch:                              # 批处理配置
        enabled: false                    # Saga触发不使用批处理
      retry:
        max_attempts: 5
        backoff: "exponential"
        dead_letter: true                 # 失败消息进入死信队列
        
    # 订阅Saga步骤事件，更新订单状态
    - event: "saga_step_completed"
      handler: "update_order_saga_status"
      channel: "saga_events"
      batch:
        enabled: true                     # 状态更新可以批处理
        size: 10
        timeout: 5000
      retry:
        max_attempts: 3
        backoff: "linear"

  # 事件溯源配置（可选）
  event_sourcing:
    enabled: true                         # 启用事件溯源
    entities: ["Order", "OrderItem"]      # 对订单实体启用事件溯源
    snapshot:
      enabled: true
      frequency: 50                       # 每50个事件创建一个快照
      storage: "postgresql"               # 快照存储

# ============================================================================
# 多租户与权限配置 - 数据隔离和访问控制
# ============================================================================
security:
  # 多租户配置
  multi_tenant:
    enabled: true                         # 启用多租户
    strategy: "shared_db"                 # 共享数据库策略
    tenant_field: "tenant_id"             # 租户字段名
    resolution: "header"                  # 从请求头解析租户ID

  # RBAC角色权限配置
  rbac:
    enabled: true
    roles:
      # 租户管理员角色
      - name: "tenant_admin"
        description: "租户管理员，拥有租户内所有权限"
        permissions: 
          - "order:*"                     # 订单所有权限
          - "coupon:*"                    # 优惠券所有权限
          - "inventory:read"              # 库存读权限
          - "customer:*"                  # 客户所有权限
        
      # 订单管理员角色
      - name: "order_manager"
        description: "订单管理员，管理订单相关操作"
        permissions:
          - "order:create"
          - "order:read"
          - "order:update"
          - "order:cancel"
          - "coupon:use"
          
      # 普通客服角色
      - name: "customer_service"
        description: "客服人员，只能查看和处理订单"
        permissions:
          - "order:read"
          - "order:update_status"
          - "customer:read"
          
    permissions:
      # 订单权限定义
      - name: "order:create"
        description: "创建订单"
        resource: "order"
        actions: ["create"]
        
      - name: "order:read"
        description: "查看订单"
        resource: "order"
        actions: ["read"]
        
      - name: "order:update"
        description: "更新订单"
        resource: "order"
        actions: ["update"]
        
      - name: "order:cancel"
        description: "取消订单"
        resource: "order"
        actions: ["cancel"]

  # 行级安全策略
  row_level_security:
    enabled: true
    policies:
      # 订单行级安全：只能访问同租户的订单
      - entity: "Order"
        policy: "tenant_id = current_setting('app.current_tenant_id')"
        roles: ["tenant_admin", "order_manager", "customer_service"]
        
      # 客户只能看自己的订单
      - entity: "Order"
        policy: "customer_id = current_setting('app.current_user_id')"
        roles: ["customer"]

  # 字段级安全：敏感字段访问控制
  field_level_security:
    enabled: true
    policies:
      # 支付信息只有管理员可见
      - entity: "Order"
        field: "payment_info"
        access: "read"
        condition: "has_permission('order:view_payment')"
        
      # 折扣金额只有相关角色可见
      - entity: "Order"
        field: "discount_amount"
        access: "read"
        condition: "has_any_role('tenant_admin', 'order_manager')"

# ============================================================================
# 缓存策略配置 - 多层缓存优化性能
# ============================================================================
cache:
  global:
    default_ttl: 300                      # 默认5分钟TTL
    key_prefix: "ecommerce_order"          # 缓存键前缀
    serialization: "json"                 # JSON序列化

  # 本地缓存配置
  local:
    enabled: true
    type: "lru"                           # LRU淘汰算法
    max_size: 1000                        # 最大1000个条目
    ttl: 60                               # 本地缓存1分钟TTL

  # Redis分布式缓存
  redis:
    enabled: true
    cluster: true                         # 使用Redis集群
    connection:
      nodes:
        - "redis-node-1:6379"
        - "redis-node-2:6379"
        - "redis-node-3:6379"
      pool_size: 10

  # 缓存一致性策略
  consistency:
    strategy: "write_through"             # 写透策略
    invalidation:
      # 订单更新时失效相关缓存
      - events: ["order_updated", "order_status_changed"]
        keys: ["order:*:{order_id}:*", "customer_orders:{customer_id}:*"]
        
      # Saga状态变更时失效Saga缓存
      - events: ["saga_step_completed", "saga_completed"]
        keys: ["saga_status:{saga_id}:*"]

  # 具体缓存策略
  strategies:
    # 订单详情缓存策略
    - name: "order_details_cache"
      entities: ["Order"]
      operations: ["get_order", "get_order_with_items"]
      ttl: 300                            # 5分钟
      warm_up: false                      # 不预热
      tags: ["order_cache"]
      
    # 用户订单列表缓存
    - name: "customer_orders_cache"
      entities: ["Order"]
      operations: ["list_customer_orders"]
      ttl: 180                            # 3分钟
      warm_up: true                       # 预热热点用户
      tags: ["order_list_cache"]
      
    # Saga状态缓存
    - name: "saga_status_cache"
      entities: ["Saga"]
      operations: ["get_saga_status"]
      ttl: 60                             # 1分钟，状态变化较频繁
      tags: ["saga_cache"]

# ============================================================================
# 性能调优配置 - 数据库和查询优化
# ============================================================================
performance:
  database:
    # 数据库索引提示
    index_hints:
      # 订单列表查询优化
      - query: "SELECT * FROM orders WHERE tenant_id = ? AND customer_id = ? ORDER BY created_at DESC"
        hint: "USE INDEX (idx_tenant_customer, idx_status_created)"
        
      # 订单状态查询优化
      - query: "SELECT * FROM orders WHERE tenant_id = ? AND status = ?"
        hint: "USE INDEX (idx_status_created)"

    # 读写分离配置
    read_write_separation:
      enabled: true
      read_replicas:
        - "db-read-replica-1"
        - "db-read-replica-2"
      write_master: "db-write-master"
      routing_rules:
        # 查询操作路由到读副本
        - operation: "read"
          condition: "operation_type IN ('SELECT', 'COUNT', 'EXISTS')"
          target: "read_replicas"
          
        # 写操作路由到主库
        - operation: "write"
          condition: "operation_type IN ('INSERT', 'UPDATE', 'DELETE')"
          target: "write_master"

    # 连接池配置
    connection_pool:
      min_size: 5                         # 最小连接数
      max_size: 20                        # 最大连接数
      acquire_timeout: 10000              # 10秒获取超时
      idle_timeout: 300000                # 5分钟空闲超时

  # 批处理优化
  batch_processing:
    enabled: true
    batch_size: 100                       # 批次大小100
    timeout: 5000                         # 5秒批处理超时
    operations: 
      - "create_order_items"              # 批量创建订单商品
      - "update_inventory_reservations"   # 批量更新库存预留

  # 分页优化
  pagination:
    default_size: 20                      # 默认页大小
    max_size: 100                         # 最大页大小100
    cursor_based: true                    # 使用游标分页
    count_optimization: true              # 优化总数统计

  # 查询优化配置
  query_optimization:
    n_plus_1_detection: true              # N+1查询检测
    slow_query_threshold: 1000            # 慢查询阈值1秒
    plan_cache: true                      # 查询计划缓存

# ============================================================================
# 外部服务集成配置 - 库存、优惠券、支付等服务
# ============================================================================
integrations:
  # HTTP客户端配置
  http:
    # 库存服务客户端
    - name: "inventory_service"
      base_url: "http://inventory-service:8080"
      timeout: 15000                      # 15秒超时
      retry:
        max_attempts: 3
        backoff: "exponential"
      auth:
        type: "bearer"
        token: "${INVENTORY_SERVICE_TOKEN}"
      headers:
        "Content-Type": "application/json"
        "X-Service-Name": "order-service"
        
    # 优惠券服务客户端
    - name: "coupon_service"
      base_url: "http://coupon-service:8080"
      timeout: 5000                       # 5秒超时，优惠券服务要求快速响应
      retry:
        max_attempts: 2
        backoff: "linear"
      auth:
        type: "bearer"
        token: "${COUPON_SERVICE_TOKEN}"
        
    # 支付服务客户端
    - name: "payment_service"
      base_url: "https://payment-gateway.example.com"
      timeout: 30000                      # 30秒超时，支付可能较慢
      retry:
        max_attempts: 3
        backoff: "exponential"
      auth:
        type: "oauth2"
        config:
          client_id: "${PAYMENT_CLIENT_ID}"
          client_secret: "${PAYMENT_CLIENT_SECRET}"
          token_url: "https://payment-gateway.example.com/oauth/token"

  # 消息队列集成
  message_queues:
    # 主要事件队列
    - name: "order_events"
      type: "kafka"
      connection:
        brokers: 
          - "kafka-1:9092"
          - "kafka-2:9092"
          - "kafka-3:9092"
      topics:
        - name: "order-events"
          partitions: 6                   # 6个分区支持并发
          replication: 3                  # 3副本保证可用性
          
        - name: "saga-events"
          partitions: 3
          replication: 3
          
    # 通知队列
    - name: "notifications"
      type: "rabbitmq"
      connection:
        host: "rabbitmq-cluster"
        port: 5672
        username: "${RABBITMQ_USER}"
        password: "${RABBITMQ_PASSWORD}"

# ============================================================================
# 治理与可观测性配置 - 灰度发布、监控、告警
# ============================================================================
governance:
  # 灰度发布配置
  deployment:
    canary:
      enabled: true                       # 启用灰度发布
      traffic_split: 10                   # 10%流量到新版本
      success_criteria:                   # 成功标准
        error_rate: "<2%"                 # 错误率低于2%
        latency_p99: "<1000ms"            # P99延迟低于1秒
        saga_success_rate: ">95%"         # Saga成功率高于95%
      duration: 1800                      # 30分钟灰度时长
      auto_promote: true                  # 满足条件自动推广
      rollback_criteria:                  # 回滚标准
        error_rate: ">5%"
        latency_p99: ">2000ms"
        
    # 熔断器配置
    circuit_breaker:
      enabled: true
      failure_threshold: 50               # 50%失败率触发熔断
      timeout: 60                         # 60秒熔断时间
      half_open_requests: 5               # 半开状态5个请求
      
    # 限流配置
    rate_limiting:
      global: "2000/min"                  # 全局每分钟2000请求
      per_tenant: "500/min"               # 每租户每分钟500请求
      per_user: "100/min"                 # 每用户每分钟100请求
      
    # 超时控制
    timeouts:
      http_client: 30000                  # HTTP客户端30秒
      database: 10000                     # 数据库10秒
      cache: 2000                         # 缓存2秒
      saga_step: 60000                    # Saga步骤60秒

# ============================================================================
# 可观测性配置 - 指标、链路追踪、日志、告警
# ============================================================================
observability:
  # 指标收集
  metrics:
    enabled: true
    collection_interval: 15               # 15秒收集间隔
    custom_metrics:
      # 订单处理时间指标
      - name: "order_processing_duration"
        type: "histogram"
        description: "订单处理总耗时"
        buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60]  # 秒
        labels: ["tenant_id", "order_type", "payment_method"]
        
      # Saga成功率指标
      - name: "saga_success_rate"
        type: "gauge"
        description: "Saga事务成功率"
        labels: ["saga_name", "tenant_id"]
        
      # 库存预留成功率
      - name: "inventory_reservation_success_rate"
        type: "gauge"
        description: "库存预留成功率"
        labels: ["tenant_id"]
        
      # 优惠券使用统计
      - name: "coupon_usage_count"
        type: "counter"
        description: "优惠券使用次数"
        labels: ["tenant_id", "coupon_type"]

  # 分布式追踪
  tracing:
    enabled: true
    sampler: "probabilistic"              # 概率采样
    sample_rate: 0.1                      # 10%采样率
    exporters: ["jaeger", "zipkin"]       # 导出到Jaeger和Zipkin
    trace_saga_steps: true                # 追踪Saga步骤
    
  # 结构化日志
  logging:
    level: "info"                         # 信息级别
    format: "json"                        # JSON格式
    structured: true                      # 结构化日志
    correlation_id: true                  # 包含关联ID
    sensitive_data_filtering: true        # 过滤敏感数据
    fields:
      - tenant_id: "always"               # 总是记录租户ID
      - user_id: "always"                 # 总是记录用户ID
      - saga_id: "when_present"           # Saga ID存在时记录
      - order_id: "always"                # 总是记录订单ID

  # 告警规则
  alerting:
    rules:
      # 高错误率告警
      - name: "high_error_rate"
        condition: "error_rate > 5"        # 错误率超过5%
        duration: 300                      # 持续5分钟
        severity: "critical"
        channels: ["slack", "email"]
        message: "订单服务错误率过高: {{$value}}%"
        
      # Saga失败率告警
      - name: "saga_failure_rate_high"
        condition: "saga_failure_rate > 10"  # Saga失败率超过10%
        duration: 300
        severity: "warning"
        channels: ["slack"]
        message: "Saga事务失败率过高: {{$value}}%"
        
      # 响应时间告警
      - name: "high_latency"
        condition: "response_time_p99 > 2000"  # P99延迟超过2秒
        duration: 600                          # 持续10分钟
        severity: "warning"
        channels: ["slack"]
        
      # 库存预留失败告警
      - name: "inventory_reservation_failure"
        condition: "inventory_reservation_failure_rate > 20"
        duration: 180                      # 持续3分钟
        severity: "critical"
        channels: ["slack", "email", "pagerduty"]
        message: "库存预留失败率过高，可能影响订单处理"

  # 健康检查
  health_checks:
    # 数据库健康检查
    - name: "database"
      type: "database_connection"
      interval: 30                        # 30秒检查一次
      timeout: 5                          # 5秒超时
      
    # 缓存健康检查
    - name: "redis_cache"
      type: "redis_ping"
      interval: 60
      timeout: 3
      
    # 外部服务健康检查
    - name: "inventory_service"
      type: "http_endpoint"
      url: "http://inventory-service:8080/health"
      interval: 60
      timeout: 10
      
    - name: "coupon_service"
      type: "http_endpoint"
      url: "http://coupon-service:8080/health"
      interval: 60
      timeout: 5
      
    - name: "payment_service"
      type: "http_endpoint"
      url: "https://payment-gateway.example.com/health"
      interval: 120                       # 外部服务检查间隔更长
      timeout: 15

# ============================================================================
# 版本演进与迁移配置
# ============================================================================
migration:
  version: "2.1.0"                       # 当前版本
  from_version: "2.0.0"                  # 源版本
  changes:                                # 版本变更
    - type: "add_field"                   # 添加字段
      entity: "Order"
      field: "saga_id"
      sql: "ALTER TABLE orders ADD COLUMN saga_id UUID"
      
    - type: "add_index"                   # 添加索引
      entity: "Order"
      index: "idx_saga_id"
      sql: "CREATE INDEX idx_saga_id ON orders(saga_id) WHERE saga_id IS NOT NULL"
      
    - type: "add_table"                   # 添加表
      entity: "CouponUsage"
      sql: |
        CREATE TABLE coupon_usages (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tenant_id UUID NOT NULL,
          order_id UUID NOT NULL REFERENCES orders(id),
          coupon_code VARCHAR(50) NOT NULL,
          discount_amount DECIMAL(10,2) NOT NULL,
          used_at TIMESTAMP NOT NULL DEFAULT NOW()
        );
        CREATE INDEX idx_coupon_usage ON coupon_usages(tenant_id, coupon_code, used_at);
        
  backward_compatible: true               # 向后兼容
  auto_migration: true                    # 自动执行迁移
  rollback_plan:                          # 回滚方案
    - "DROP INDEX IF EXISTS idx_saga_id"
    - "ALTER TABLE orders DROP COLUMN IF EXISTS saga_id"
    - "DROP TABLE IF EXISTS coupon_usages"

# ============================================================================
# 环境配置模板
# ============================================================================
environments:
  # 开发环境配置
  development:
    database:
      host: "localhost"
      port: 5432
      name: "ecommerce_dev"
    redis:
      host: "localhost"
      port: 6379
    services:
      inventory_service: "http://localhost:8081"
      coupon_service: "http://localhost:8082"
      payment_service: "http://localhost:8083"
    features:
      enable_saga: true
      enable_caching: false               # 开发环境关闭缓存
      log_level: "debug"
      
  # 生产环境配置
  production:
    database:
      host: "${DB_HOST}"                  # 从环境变量获取
      port: "${DB_PORT}"
      name: "${DB_NAME}"
      ssl: true
      connection_pool:
        min_size: 10
        max_size: 50
    redis:
      cluster: true
      nodes: "${REDIS_NODES}"             # 集群节点列表
    services:
      inventory_service: "${INVENTORY_SERVICE_URL}"
      coupon_service: "${COUPON_SERVICE_URL}"
      payment_service: "${PAYMENT_SERVICE_URL}"
    features:
      enable_saga: true
      enable_caching: true
      log_level: "info"
      monitoring: true
      alerting: true