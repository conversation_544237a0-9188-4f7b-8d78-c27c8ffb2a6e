# 核心DSL语法设计

## 设计原则

### 极简表达式原则
为了支持快速调用链路分析和引用分析，DSL设计遵循以下原则：
- **声明式优先**: 避免复杂表达式，使用声明式定义
- **静态引用**: 所有引用在编译时确定，避免动态计算
- **结构化依赖**: 清晰的层次结构，便于依赖图构建
- **原子操作**: 将复杂逻辑分解为原子操作的组合

## 核心语法结构

### 1. 实体定义语法

```yaml
# 极简实体定义
entity: User
table: users
fields:
  - id: { type: ID, primary: true }
  - username: { type: String, unique: true, length: 50 }
  - email: { type: Email, unique: true }
  - status: { type: Enum, values: [active, inactive, deleted] }
  - created_at: { type: DateTime, auto: true }

# 索引定义（静态分析友好）
indexes:
  - name: idx_username
    fields: [username]
    unique: true
  - name: idx_email  
    fields: [email]
    unique: true
  - name: idx_status
    fields: [status]

# 关系定义（明确的引用关系）
relations:
  profile:
    type: hasOne
    target: UserProfile
    foreign_key: user_id
    cascade: delete
  
  orders:
    type: hasMany
    target: Order
    foreign_key: user_id
```

### 2. 引用系统设计

```yaml
# 静态引用定义
references:
  # 直接字段引用
  field_refs:
    - source: User.id
      targets: [UserProfile.user_id, Order.user_id]
    - source: User.email
      targets: [LoginLog.email, Notification.recipient]
  
  # API引用关系
  api_refs:
    - endpoint: /api/users/{id}
      entity: User
      field: id
      operations: [read, update, delete]
    - endpoint: /api/users/{id}/orders
      entity: User
      field: id
      related: Order
      operations: [read]

# 调用链定义
call_chains:
  user_registration:
    steps:
      - validate: User.email, User.username
      - create: User
      - trigger: send_welcome_email
      - create: UserProfile
    dependencies: [EmailService]
  
  user_login:
    steps:
      - validate: User.email
      - check: User.status == active
      - create: LoginLog
      - update: User.last_login_at
    dependencies: [AuthService]
```

### 3. 业务规则简化语法

```yaml
# 原子化规则定义
rules:
  validate_email_format:
    type: format_validation
    target: User.email
    pattern: email
    message: "Invalid email format"
  
  check_username_unique:
    type: uniqueness_check
    target: User.username
    scope: global
    message: "Username already exists"
  
  ensure_user_active:
    type: field_check
    target: User.status
    condition: equals
    value: active
    message: "User is not active"

# 规则链（避免复杂表达式）
rule_chains:
  user_creation_rules:
    sequence: [validate_email_format, check_username_unique]
    failure_action: abort
  
  user_login_rules:
    sequence: [validate_email_format, ensure_user_active]
    failure_action: return_error
```

### 4. 服务定义语法

```yaml
# 服务接口定义
service: UserService
namespace: user.management
version: v1

# 方法定义（明确的依赖关系）
methods:
  create_user:
    inputs:
      - username: String
      - email: Email
      - password: Password
    outputs:
      - user: User
      - profile: UserProfile
    dependencies:
      entities: [User, UserProfile]
      services: [EmailService, ValidationService]
      rules: [user_creation_rules]
    
  get_user_by_id:
    inputs:
      - id: ID
    outputs:
      - user: User
    dependencies:
      entities: [User]
    
  update_user:
    inputs:
      - id: ID
      - data: UserUpdateData
    outputs:
      - user: User
    dependencies:
      entities: [User]
      rules: [validate_email_format, check_username_unique]

# API端点映射
endpoints:
  - path: /api/users
    method: POST
    handler: create_user
    auth_required: false
  
  - path: /api/users/{id}
    method: GET
    handler: get_user_by_id
    auth_required: true
  
  - path: /api/users/{id}
    method: PUT
    handler: update_user
    auth_required: true
```

### 5. 数据流定义

```yaml
# 数据流图（用于调用链分析）
data_flows:
  user_registration_flow:
    inputs: [username, email, password]
    stages:
      - stage: validation
        processor: ValidationService
        inputs: [username, email, password]
        outputs: [validation_result]
      
      - stage: user_creation
        processor: UserService.create_user
        inputs: [username, email, password]
        outputs: [user_id]
        dependencies: [validation]
      
      - stage: profile_creation
        processor: ProfileService.create_profile
        inputs: [user_id]
        outputs: [profile_id]
        dependencies: [user_creation]
      
      - stage: notification
        processor: NotificationService.send_welcome
        inputs: [user_id, email]
        outputs: []
        dependencies: [user_creation]
        async: true
```

### 6. 模块依赖定义

```yaml
# 模块定义
module: UserManagement
version: 1.0.0

# 明确的依赖声明
dependencies:
  internal:
    - CommonTypes: ^1.0.0
    - ValidationUtils: ^2.1.0
    - DatabaseUtils: ^3.0.0
  
  external:
    - EmailService: ^1.5.0
    - LoggingService: ^2.0.0

# 提供的接口
provides:
  entities: [User, UserProfile]
  services: [UserService, ProfileService]
  endpoints: [/api/users/*, /api/profiles/*]

# 消费的接口
consumes:
  services: [EmailService.send, LoggingService.log]
  events: [user.created, user.updated, user.deleted]
```

## 分析友好的特性

### 1. 静态分析支持

```yaml
# 分析元数据
analysis_metadata:
  complexity_metrics:
    - cyclomatic_complexity: low
    - coupling_factor: minimal
    - cohesion_level: high
  
  reference_patterns:
    - direct_references: explicit
    - indirect_references: traceable
    - circular_dependencies: detected
  
  performance_hints:
    - index_usage: optimized
    - query_patterns: efficient
    - caching_strategy: applicable
```

### 2. 调用图生成支持

```yaml
# 调用图元数据
call_graph_metadata:
  nodes:
    - type: entity
      complexity: O(1)
    - type: service_method  
      complexity: O(log n)
    - type: rule_chain
      complexity: O(n)
  
  edges:
    - type: direct_call
      weight: 1
    - type: dependency
      weight: 2  
    - type: data_flow
      weight: 1
```

### 3. 影响分析支持

```yaml
# 变更影响分析
impact_analysis:
  change_propagation:
    - field_change: [dependent_fields, dependent_services, dependent_apis]
    - entity_change: [related_entities, consuming_services, client_apis]
    - service_change: [calling_services, dependent_endpoints, client_apps]
  
  test_coverage:
    - unit_tests: auto_generated
    - integration_tests: dependency_based
    - end_to_end_tests: flow_based
```

## 编译时优化

### 1. 依赖图预构建

```yaml
# 编译时依赖图
dependency_graph:
  build_order:
    1. [CommonTypes, ValidationUtils]
    2. [User, UserProfile]  
    3. [UserService, ProfileService]
    4. [UserController]
  
  circular_dependency_check: enabled
  unused_dependency_detection: enabled
  optimization_suggestions: enabled
```

### 2. 静态分析缓存

```yaml
# 分析结果缓存
static_analysis_cache:
  entity_metadata: persistent
  service_dependencies: persistent  
  api_mappings: persistent
  rule_validations: persistent
  
  cache_invalidation:
    - on_entity_change: [related_entities, consuming_services]
    - on_service_change: [dependent_services, client_apis]
    - on_rule_change: [affected_entities, related_services]
```

## 运行时分析支持

### 1. 调用跟踪

```yaml
# 运行时调用跟踪
call_tracing:
  trace_points:
    - service_entry: { method: all, capture: [inputs, context] }
    - entity_access: { operations: [create, read, update, delete], capture: [entity_id, fields] }
    - rule_execution: { rules: all, capture: [inputs, results, duration] }
  
  trace_storage:
    - local_buffer: 1000_entries
    - persistent_storage: elasticsearch
    - retention_policy: 30_days
```

### 2. 性能监控

```yaml
# 性能监控配置
performance_monitoring:
  metrics:
    - service_response_time: percentiles
    - entity_operation_time: average
    - rule_execution_time: histogram
    - dependency_call_time: distribution
  
  alerting:
    - slow_queries: threshold_5s
    - high_error_rate: threshold_5_percent
    - memory_usage: threshold_80_percent
```

## MCP集成准备

### 1. 数据访问抽象

```yaml
# MCP数据访问接口定义
mcp_data_access:
  read_operations:
    - get_entity: { entity_type, entity_id }
    - query_entities: { entity_type, filters, pagination }
    - get_relationships: { entity_id, relation_type }
    - analyze_dependencies: { target_entity, depth }
  
  write_operations:
    - create_entity: { entity_type, data }
    - update_entity: { entity_id, changes }
    - delete_entity: { entity_id }
    - batch_operations: { operations_list }
  
  analysis_operations:
    - trace_call_path: { start_point, end_point }
    - find_references: { target, reference_type }
    - impact_analysis: { change_target, analysis_depth }
    - dependency_graph: { root_entities, max_depth }
```

### 2. AI分析接口

```yaml
# AI分析能力接口
ai_analysis_interface:
  code_understanding:
    - parse_dsl_structure: { dsl_content }
    - extract_dependencies: { module_path }
    - identify_patterns: { code_segment }
    - suggest_optimizations: { analysis_target }
  
  impact_prediction:
    - predict_change_impact: { proposed_changes }
    - estimate_migration_effort: { from_version, to_version }
    - identify_breaking_changes: { version_diff }
    - suggest_test_scenarios: { changed_components }
  
  optimization_recommendations:
    - performance_optimization: { bottleneck_analysis }
    - architecture_improvement: { current_structure }
    - security_enhancement: { vulnerability_scan }
    - maintainability_improvement: { complexity_analysis }
```

这种简化的DSL设计通过最小化表达式复杂度，最大化静态分析友好性，为快速调用链路分析和引用分析奠定了基础。