# AI-DSL类型系统详细设计

## 概述

AI-DSL类型系统采用静态强类型设计，优化表达式复杂度，支持快速的类型推导和验证。系统设计原则是尽可能减少运行时类型检查，将类型信息在编译时确定，为调用链路分析和引用分析提供坚实基础。

## 核心类型定义

### 1. 基础标量类型

```yaml
# 基础类型定义
basic_types:
  # 数值类型
  Int:
    size: 32bit
    range: [-2147483648, 2147483647]
    default: 0
    validation: integer_check
    storage: fixed_4_bytes
    
  Long:
    size: 64bit
    range: [-9223372036854775808, 9223372036854775807]
    default: 0
    validation: long_integer_check
    storage: fixed_8_bytes
    
  Decimal:
    precision: 18
    scale: 2
    max_digits: 18
    default: 0.00
    validation: decimal_format_check
    storage: bcd_encoding
    
  # 文本类型
  String:
    encoding: utf8
    max_length: 65535
    default: ""
    validation: utf8_check
    storage: variable_length
    
  ShortString:
    encoding: utf8
    max_length: 255
    default: ""
    validation: utf8_check
    storage: variable_length_optimized
    
  # 时间类型
  DateTime:
    format: iso8601
    timezone_aware: true
    precision: millisecond
    default: null
    validation: datetime_format_check
    storage: unix_timestamp_ms
    
  Date:
    format: "YYYY-MM-DD"
    timezone_aware: false
    default: null
    validation: date_format_check
    storage: days_since_epoch
    
  # 布尔类型
  Boolean:
    values: [true, false]
    default: false
    validation: boolean_check
    storage: single_bit
```

### 2. 业务类型系统

```yaml
# 业务类型定义
business_types:
  # 标识符类型
  ID:
    base_type: Long
    constraints:
      positive: true
      unique: global
    generation: auto_increment
    validation: positive_integer_check
    
  UUID:
    base_type: String
    format: "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx"
    constraints:
      length: 36
      pattern: uuid_v4_pattern
    generation: random_uuid
    validation: uuid_format_check
    
  # 通信类型
  Email:
    base_type: String
    constraints:
      max_length: 254
      pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    validation: email_format_check
    normalization: lowercase_domain
    
  Phone:
    base_type: String
    constraints:
      pattern: "^\\+?[1-9]\\d{1,14}$"
    validation: phone_number_check
    normalization: e164_format
    
  # 金融类型
  Money:
    base_type: Decimal
    precision: 10
    scale: 2
    constraints:
      min_value: 0
    currency: required
    validation: monetary_amount_check
    
  # 状态类型
  Status:
    base_type: Enum
    values: ["active", "inactive", "pending", "deleted"]
    default: "pending"
    transitions:
      pending: [active, inactive, deleted]
      active: [inactive, deleted]
      inactive: [active, deleted]
      deleted: []
    validation: status_transition_check
```

### 3. 集合类型

```yaml
# 集合类型定义
collection_types:
  Array:
    element_type: generic_T
    constraints:
      min_length: 0
      max_length: 1000
      unique_elements: false
    operations: [add, remove, get, contains, size, isEmpty]
    validation: element_type_check
    storage: dynamic_array
    
  Set:
    element_type: generic_T
    constraints:
      min_size: 0
      max_size: 1000
      unique_elements: true
    operations: [add, remove, contains, size, isEmpty, union, intersection]
    validation: uniqueness_check
    storage: hash_set
    
  Map:
    key_type: generic_K
    value_type: generic_V
    constraints:
      max_size: 1000
      unique_keys: true
    operations: [put, get, remove, containsKey, size, isEmpty]
    validation: key_uniqueness_check
    storage: hash_map
```

## 类型推导系统

### 1. 静态类型推导

```typescript
// 类型推导引擎
class TypeInferenceEngine {
  private typeRegistry: TypeRegistry;
  private constraintSolver: ConstraintSolver;
  
  // 推导表达式类型
  inferExpressionType(expression: Expression, context: TypeContext): TypeInfo {
    switch (expression.type) {
      case 'literal':
        return this.inferLiteralType(expression as LiteralExpression);
      
      case 'field_reference':
        return this.inferFieldReferenceType(expression as FieldReference, context);
      
      case 'function_call':
        return this.inferFunctionCallType(expression as FunctionCall, context);
      
      case 'binary_operation':
        return this.inferBinaryOperationType(expression as BinaryOperation, context);
      
      default:
        throw new TypeInferenceError(`Unknown expression type: ${expression.type}`);
    }
  }
  
  // 推导字面量类型
  private inferLiteralType(literal: LiteralExpression): TypeInfo {
    const value = literal.value;
    
    // 数值类型推导
    if (typeof value === 'number') {
      if (Number.isInteger(value)) {
        // 根据值的大小选择合适的整数类型
        if (value >= -2147483648 && value <= 2147483647) {
          return { type: 'Int', constraints: { value } };
        } else {
          return { type: 'Long', constraints: { value } };
        }
      } else {
        return { type: 'Decimal', constraints: { value } };
      }
    }
    
    // 字符串类型推导
    if (typeof value === 'string') {
      // 检查是否是特殊格式
      if (this.isEmailFormat(value)) {
        return { type: 'Email', constraints: { value } };
      } else if (this.isPhoneFormat(value)) {
        return { type: 'Phone', constraints: { value } };
      } else if (this.isUUIDFormat(value)) {
        return { type: 'UUID', constraints: { value } };
      } else {
        return { 
          type: value.length <= 255 ? 'ShortString' : 'String', 
          constraints: { maxLength: value.length, value } 
        };
      }
    }
    
    // 布尔类型推导
    if (typeof value === 'boolean') {
      return { type: 'Boolean', constraints: { value } };
    }
    
    throw new TypeInferenceError(`Cannot infer type for literal: ${value}`);
  }
  
  // 推导字段引用类型
  private inferFieldReferenceType(
    fieldRef: FieldReference, 
    context: TypeContext
  ): TypeInfo {
    const [entityName, fieldName] = fieldRef.path.split('.');
    
    // 从上下文中查找实体定义
    const entityDef = context.getEntityDefinition(entityName);
    if (!entityDef) {
      throw new TypeInferenceError(`Entity not found: ${entityName}`);
    }
    
    // 查找字段定义
    const fieldDef = entityDef.fields.find(f => f.name === fieldName);
    if (!fieldDef) {
      throw new TypeInferenceError(`Field not found: ${fieldName} in entity ${entityName}`);
    }
    
    return {
      type: fieldDef.type,
      constraints: fieldDef.constraints,
      nullable: !fieldDef.required,
      source: `${entityName}.${fieldName}`
    };
  }
}
```

### 2. 约束求解器

```typescript
// 类型约束求解器
class ConstraintSolver {
  // 解决类型约束
  solveConstraints(constraints: TypeConstraint[]): ConstraintSolution {
    const solution = new ConstraintSolution();
    
    // 按优先级排序约束
    const sortedConstraints = this.sortConstraintsByPriority(constraints);
    
    for (const constraint of sortedConstraints) {
      try {
        this.solveConstraint(constraint, solution);
      } catch (error) {
        solution.addError(constraint, error);
      }
    }
    
    // 验证解决方案的一致性
    this.validateSolutionConsistency(solution);
    
    return solution;
  }
  
  // 解决单个约束
  private solveConstraint(constraint: TypeConstraint, solution: ConstraintSolution): void {
    switch (constraint.type) {
      case 'type_equality':
        this.solveTypeEquality(constraint as TypeEqualityConstraint, solution);
        break;
        
      case 'subtype_relation':
        this.solveSubtypeRelation(constraint as SubtypeConstraint, solution);
        break;
        
      case 'field_access':
        this.solveFieldAccess(constraint as FieldAccessConstraint, solution);
        break;
        
      case 'function_call':
        this.solveFunctionCall(constraint as FunctionCallConstraint, solution);
        break;
        
      default:
        throw new ConstraintSolverError(`Unknown constraint type: ${constraint.type}`);
    }
  }
  
  // 解决类型相等约束
  private solveTypeEquality(
    constraint: TypeEqualityConstraint, 
    solution: ConstraintSolution
  ): void {
    const leftType = solution.getType(constraint.left);
    const rightType = solution.getType(constraint.right);
    
    if (leftType && rightType) {
      // 两个类型都已知，检查是否兼容
      if (!this.areTypesCompatible(leftType, rightType)) {
        throw new TypeMismatchError(
          `Type mismatch: ${leftType.type} is not compatible with ${rightType.type}`
        );
      }
      
      // 统一类型为更具体的类型
      const unifiedType = this.unifyTypes(leftType, rightType);
      solution.setType(constraint.left, unifiedType);
      solution.setType(constraint.right, unifiedType);
    } else if (leftType) {
      // 只有左侧类型已知，传播到右侧
      solution.setType(constraint.right, leftType);
    } else if (rightType) {
      // 只有右侧类型已知，传播到左侧
      solution.setType(constraint.left, rightType);
    }
    // 如果都未知，等待其他约束提供信息
  }
}
```

## 类型验证系统

### 1. 编译时验证

```yaml
# 编译时类型验证配置
compile_time_validation:
  type_checking:
    strictness: strict
    null_safety: enabled
    type_inference: enabled
    generic_validation: enabled
    
  validation_rules:
    - name: required_field_check
      description: "检查必填字段不为空"
      trigger: field_assignment
      condition: field.required == true
      action: validate_not_null
      
    - name: type_compatibility_check
      description: "检查类型兼容性"
      trigger: assignment_operation
      condition: always
      action: validate_type_compatibility
      
    - name: constraint_satisfaction_check
      description: "检查约束条件满足"
      trigger: value_assignment
      condition: field.constraints != empty
      action: validate_constraints
      
    - name: reference_validity_check
      description: "检查引用有效性"
      trigger: field_reference
      condition: always
      action: validate_reference_exists
  
  error_reporting:
    format: structured
    include_suggestions: true
    max_errors: 100
    continue_on_error: true
```

### 2. 运行时验证

```typescript
// 运行时类型验证器
class RuntimeTypeValidator {
  // 验证实体数据
  validateEntityData(entityType: string, data: any): ValidationResult {
    const entityDef = this.typeRegistry.getEntityDefinition(entityType);
    const result = new ValidationResult();
    
    // 验证必填字段
    this.validateRequiredFields(entityDef, data, result);
    
    // 验证字段类型
    this.validateFieldTypes(entityDef, data, result);
    
    // 验证约束条件
    this.validateConstraints(entityDef, data, result);
    
    // 验证业务规则
    this.validateBusinessRules(entityDef, data, result);
    
    return result;
  }
  
  // 验证字段类型
  private validateFieldTypes(
    entityDef: EntityDefinition, 
    data: any, 
    result: ValidationResult
  ): void {
    for (const field of entityDef.fields) {
      const value = data[field.name];
      
      if (value !== null && value !== undefined) {
        const typeValid = this.validateFieldType(field, value);
        if (!typeValid.isValid) {
          result.addError({
            field: field.name,
            type: 'type_mismatch',
            message: `Expected ${field.type}, got ${typeof value}`,
            value,
            expectedType: field.type
          });
        }
      }
    }
  }
  
  // 验证单个字段类型
  private validateFieldType(field: FieldDefinition, value: any): TypeValidationResult {
    const validator = this.getTypeValidator(field.type);
    
    try {
      const isValid = validator.validate(value, field.constraints);
      return {
        isValid,
        normalizedValue: isValid ? validator.normalize(value) : value,
        errors: isValid ? [] : validator.getErrors(value)
      };
    } catch (error) {
      return {
        isValid: false,
        normalizedValue: value,
        errors: [{ message: error.message, code: 'validation_error' }]
      };
    }
  }
}
```

## 类型安全特性

### 1. 空安全

```yaml
# 空安全配置
null_safety:
  default_behavior: non_nullable
  nullable_annotation: "?"
  null_coalescing: "??"
  safe_navigation: "?."
  
  nullable_types:
    syntax: "Type?"
    examples:
      - "String?"
      - "User?"
      - "Array<String>?"
  
  null_checking_rules:
    - name: null_assignment_check
      rule: "不能将null赋值给非空类型"
      error_code: "E001"
      
    - name: null_dereference_check
      rule: "访问可能为null的对象前必须进行null检查"
      error_code: "E002"
      
    - name: null_propagation_check
      rule: "null值在运算中的传播必须明确处理"
      error_code: "E003"
```

### 2. 泛型类型系统

```yaml
# 泛型类型系统
generic_types:
  syntax:
    definition: "Type<T>"
    constraints: "T extends BaseType"
    multiple_params: "Type<T, U>"
    
  built_in_generics:
    Array:
      definition: "Array<T>"
      constraints: "T extends Any"
      operations: [get, set, add, remove]
      
    Map:
      definition: "Map<K, V>"
      constraints: 
        - "K extends Comparable"
        - "V extends Any"
      operations: [get, put, remove, containsKey]
      
    Result:
      definition: "Result<T, E>"
      constraints:
        - "T extends Any"
        - "E extends Error"
      operations: [isSuccess, isError, getValue, getError]
  
  type_variance:
    covariance: "out T"     # Array<out T>
    contravariance: "in T"  # Consumer<in T>
    invariance: "T"         # MutableArray<T>
```

## 类型转换与兼容性

### 1. 隐式类型转换

```yaml
# 隐式转换规则
implicit_conversions:
  numeric_widening:
    - from: Int
      to: Long
      safe: true
      
    - from: Int
      to: Decimal
      safe: true
      
    - from: Long
      to: Decimal
      safe: true
  
  string_conversions:
    - from: ShortString
      to: String
      safe: true
      
    - from: Any
      to: String
      safe: false  # 需要显式转换
      method: toString()
  
  nullable_conversions:
    - from: "Type"
      to: "Type?"
      safe: true
      
    - from: "Type?"
      to: "Type"
      safe: false  # 需要null检查
```

### 2. 显式类型转换

```typescript
// 类型转换器
class TypeConverter {
  // 执行类型转换
  convert(value: any, fromType: TypeInfo, toType: TypeInfo): ConversionResult {
    // 检查是否需要转换
    if (this.areTypesEqual(fromType, toType)) {
      return { success: true, value, errors: [] };
    }
    
    // 查找转换路径
    const conversionPath = this.findConversionPath(fromType, toType);
    if (!conversionPath) {
      return {
        success: false,
        value: null,
        errors: [{ 
          message: `No conversion path from ${fromType.type} to ${toType.type}`,
          code: 'NO_CONVERSION_PATH'
        }]
      };
    }
    
    // 执行转换链
    let currentValue = value;
    let currentType = fromType;
    
    for (const step of conversionPath.steps) {
      const stepResult = this.executeConversionStep(currentValue, currentType, step);
      if (!stepResult.success) {
        return stepResult;
      }
      
      currentValue = stepResult.value;
      currentType = step.targetType;
    }
    
    return {
      success: true,
      value: currentValue,
      errors: [],
      conversionPath: conversionPath.steps.map(s => s.method)
    };
  }
  
  // 查找转换路径
  private findConversionPath(fromType: TypeInfo, toType: TypeInfo): ConversionPath | null {
    // 使用广度优先搜索找到最短转换路径
    const queue = [{ type: fromType, path: [] }];
    const visited = new Set([fromType.type]);
    
    while (queue.length > 0) {
      const { type: currentType, path } = queue.shift()!;
      
      // 查找从当前类型可以进行的转换
      const availableConversions = this.getAvailableConversions(currentType);
      
      for (const conversion of availableConversions) {
        const targetType = conversion.targetType;
        
        if (this.areTypesEqual(targetType, toType)) {
          // 找到目标类型
          return {
            steps: [...path, conversion],
            totalCost: path.length + 1
          };
        }
        
        if (!visited.has(targetType.type)) {
          visited.add(targetType.type);
          queue.push({
            type: targetType,
            path: [...path, conversion]
          });
        }
      }
    }
    
    return null; // 未找到转换路径
  }
}
```

## 性能优化

### 1. 类型检查优化

```yaml
# 类型检查优化配置
type_checking_optimization:
  caching:
    type_resolution:
      enabled: true
      cache_size: 10000
      ttl: 3600s
      
    validation_results:
      enabled: true
      cache_size: 5000
      ttl: 1800s
      
    constraint_solutions:
      enabled: true
      cache_size: 2000
      ttl: 600s
  
  lazy_evaluation:
    type_inference: true
    constraint_solving: true
    validation_execution: true
    
  batch_processing:
    validation_batch_size: 100
    inference_batch_size: 50
    parallel_processing: true
    max_threads: 4
```

### 2. 内存优化

```typescript
// 类型信息池化
class TypeInfoPool {
  private typeInfoPool = new Map<string, TypeInfo>();
  private constraintPool = new Map<string, TypeConstraint[]>();
  
  // 获取类型信息（池化）
  getTypeInfo(typeName: string, constraints?: TypeConstraint[]): TypeInfo {
    const key = this.createTypeKey(typeName, constraints);
    
    if (this.typeInfoPool.has(key)) {
      return this.typeInfoPool.get(key)!;
    }
    
    const typeInfo = new TypeInfo(typeName, constraints);
    this.typeInfoPool.set(key, typeInfo);
    
    return typeInfo;
  }
  
  // 创建类型键
  private createTypeKey(typeName: string, constraints?: TypeConstraint[]): string {
    if (!constraints || constraints.length === 0) {
      return typeName;
    }
    
    const constraintHash = this.hashConstraints(constraints);
    return `${typeName}#${constraintHash}`;
  }
  
  // 约束哈希计算
  private hashConstraints(constraints: TypeConstraint[]): string {
    const sorted = constraints
      .map(c => JSON.stringify(c))
      .sort()
      .join('|');
      
    return this.simpleHash(sorted);
  }
  
  // 清理未使用的类型信息
  cleanup(): void {
    const currentTime = Date.now();
    const maxAge = 5 * 60 * 1000; // 5分钟
    
    for (const [key, typeInfo] of this.typeInfoPool) {
      if (currentTime - typeInfo.lastAccessed > maxAge) {
        this.typeInfoPool.delete(key);
      }
    }
  }
}
```

这个类型系统设计通过简化表达式、优化类型推导和强化静态检查，为AI-DSL平台提供了坚实的类型安全基础，同时支持快速的静态分析和依赖追踪。