# 追踪配置与默认策略（模板级极简配置）

目标：最大程度由系统自动完成追踪配置与安全处理，避免打扰用户；仅在确属营销策略选择或合规需要时暴露少量可选项。默认启用“安全可靠”的最佳实践。

关联文档：
- `prd/10_追踪与分析与报表/PRD.md`
- `prd/10_追踪与分析与报表/追踪实现V2_技术方案与接口规范.md`
- `prd/10_追踪与分析与报表/打开率追踪_技术细节.md`

---

## 1. 模板级最小化开关

在模板编辑页仅暴露一个核心开关：
- 开启追踪（Recommended）：打开后系统自动注入打开/点击/转化追踪，不需要用户再配置。

当开关开启后，系统自动启用以下能力（默认策略见 §2）：
- 打开追踪：动态像素 + Beacon（视兼容）+ AMP（如模板为 AMP）
- 链接追踪：跳转网关 + 目的地址签名/加密 + 反缓存参数
- 指标口径：报表展示 Gross 与 Estimated Human 双口径
- 可观测与限流：默认开启

可选（仅营销/合规必要时出现的二级选项）：
- UTM 追加策略：默认开启；用户可选择自定义 `utm_campaign`/`utm_content`，或关闭追加
- 转化归因口径：默认“点击后 24 小时内”有效；用户可调整窗口（12h/24h/7d）
- 特殊域名白名单：默认允许站点域名；用户可添加业务可信域
- 追踪域自定义：默认使用系统分配域；需要品牌一致性或提升送达率时可更换（需 DNS 接入）

---

## 2. 默认策略（系统自动处理）

- 自定义追踪域：系统为租户分配 `track.{tenant_sub}.example.com`，证书自动签发与续期
- 参数签名：系统为租户管理 `tracking_secret`，所有追踪 URL 自动 HMAC-SHA256 签名
- 反缓存：统一附加 `ts/nonce` 与 `Cache-Control: no-store, max-age=0`
- 识别与口径：自动识别 Apple MPP/Gmail Proxy/扫描器，报表默认展示双口径与代理占比
- 去重与幂等：打开 30 分钟独立去重；点击首次/总次数；转化按 `order_id` 幂等
- 安全：点击目的地址签名/加密封装 + 域白名单；接口统一限流与告警
- UTM：默认追加 `utm_source=newsletter&utm_medium=email&utm_campaign={cid}`；如已有 UTM 则不覆盖
- 隐私：`sid` 使用邮箱哈希；支持 IP 掩码；合规说明内置于系统隐私策略

---

## 3. 模板字段与接口

模板实体新增字段（JSON）：`tracking_options`
```json
{
  "enabled": true,
  "utm": {"enabled": true, "defaults": {"source": "newsletter", "medium": "email"}},
  "conversion_window_hours": 24,
  "custom_tracking_domain": null,
  "link_domain_whitelist": []
}
```

后端读取逻辑：
- enabled=false：不做任何追踪注入/改写
- enabled=true：按默认策略自动执行；若存在可选项则按用户选择覆盖默认

---

## 4. UI 行为

- 默认仅显示一个“开启追踪”开关（带说明：提高指标可观测性与转化归因，合规可控）
- 展开“高级设置”（可选）：UTM、转化窗口、域名白名单、自定义追踪域（需要运营介入时）
- 预览：提供“包含追踪元素预览”按钮，便于校验链接改写与像素注入是否生效

---

## 5. 回退与降级

- 若链接改写失败，回退至直链并记录告警，不阻断发送
- 若签名校验失败，先告警（灰度），成熟后切换为强校验
- 若 AMP 不被客户端支持，仅保留像素与 Beacon，不影响发送

---

## 6. 合规与提示

- 系统在模板预览处提示：启用追踪后，报表将展示双口径；某些客户端可能预取图片导致“非人类打开”计入 Gross 口径
- 用户可在全局设置处查看隐私与合规说明；模板仅保留“开启追踪”的产品化开关，避免复杂技术选项


