# 技术方案设计｜追踪与分析与报表

> 相关技术细节：
> - 《打开率追踪_技术细节》：`prd/10_追踪与分析与报表/打开率追踪_技术细节.md`
> - 《发件渠道时间表与A/B测试_技术细节》：`prd/10_追踪与分析与报表/发件渠道时间表与A_B测试_技术细节.md`
> - 《追踪实现V2_技术方案与接口规范》：`prd/10_追踪与分析与报表/追踪实现V2_技术方案与接口规范.md`

## HLD
- 组件：追踪域重写器、像素服务、事件总线、聚合器、报表 API、导出器
- 数据：`events_*`（send/deliver/open/click/...）、`aggregations`

## 时序
```mermaid
sequenceDiagram
participant User
participant TRK as Tracker
participant BUS as EventBus
participant AGG as Aggregator
participant REP as ReportAPI
User->>TRK: 打开/点击
TRK->>BUS: 事件
BUS->>AGG: 消费聚合
REP-->>User: 查询报表
```

## LLD
- 幂等：事件去重 key（message_id+type+ts）
- 导出：分片+压缩；Webhook 订阅

---

## 1. 目标与范围
- 目标：事件延迟 P95<2s；报表分钟级聚合；导出与订阅稳定。
- 范围：事件采集（像素/链接重写/JS/后端）、报表（活动/旅程/联系人/域名）、导出与订阅。

## 2. 数据与接口
- 表：`events`、`message_links`、`metrics_hourly`；
- API：POST `/api/events/ingest`；POST `/api/reports/export|subscribe`，`/api/campaigns/report`。

## 3. 规则与算法
- 链接重写失败回退直链；像素被拦截时以点击替代打开（可配）。
- 打开口径：Gross（像素命中）与 Estimated Human（估算人类）并行，见《打开率追踪_技术细节》第5-6节。
- 代理识别与误报处理：MPP/Gmail Proxy/扫描器识别、回填与估算，见细节文档第7节。
- 指标口径：到达率、唯一打开/点击、CTOR、退订/硬退/投诉、转化率（统一定义）。

## 4. 可靠性
- 幂等去重；
- 写入失败补偿与死信（导出订阅）。

## 5. 安全与合规
- 链接签名与过期；
- 导出权限与一次性 URL。

## 6. 可观测与性能
- 指标：事件吞吐、延迟、聚合耗时、导出/订阅成功率；
- 告警：延迟/失败异常。

## 7. 测试与验收
- 事件类型全覆盖；
- 指标口径验证；
- 导出/订阅稳定性。

## 8. 发布与回滚
- 指标DSL与报表口径灰度；
- 链接重写策略回滚。

## 9. 风险与对策
- 反爬与拦截：多域/混淆/降级策略；
- 大规模导出：分片限流与断点续传。

## 10. Checklist
- 统一响应；
- 口径一致；
- 指标与告警上线。
