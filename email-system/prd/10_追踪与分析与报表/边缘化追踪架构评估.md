# 边缘化（Serverless/Edge）追踪采集架构评估

## 1. 背景
- 目标：降低全球用户点击/打开的网络时延、提升 302/204 首包速度、减少跨洲 RTT 与抖动，避免对核心服务造成直接压力。
- 约束：仅 GET/POST、无路径参数、统一响应；合规（GDPR/CCPA/数据驻留）；HMAC 验签与目的地址保护；可观测与限流。

## 2. 方案对比

| 方案 | 时延 | 冷启动 | 可用性 | 成本 | 复杂度 | 备注 |
|---|---|---|---|---|---|---|
| 中心化追踪服务（单/多区） | 中等/偏高（跨洲） | 无 | 高 | 中 | 低 | 架构简单，全球体验不均衡 |
| CDN 直回源 | 中等 | 无 | 高 | 低 | 低 | 仍需跨洲 RTT，缓存需严格禁止 |
| 边缘函数（Cloudflare Workers/Fastly/Edge Runtime） | 低（就近 PoP） | 极低 | 高 | 低/中 | 中 | 适合 204/302 小逻辑与队列上报 |
| Lambda@Edge/CloudFront Functions | 低 | 低/中 | 高 | 中 | 中 | 需与 SQS/Kinesis 组合；部署流程更重 |

结论：边缘函数 + 队列上报 + 核心追踪服务的“边缘采集/中心处理”混合架构最优。

## 3. 推荐架构（Edge Collector + MQ + Core Tracking）

- 路由：`track.{tenant}.domain` CNAME 到边缘平台（Anycast / 就近 PoP）。
- Edge Collector（Workers/Compute@Edge）：
  - 校验签名、基础限流与反滥用；
  - 解析 `cid/sid/mid/lid/ts/nonce/sig[/dest]`；
  - 生成轻量事件并上报 MQ（Cloudflare Queues / SQS / Pub/Sub / Kafka Gateway）；
  - open/beacon/amp → 204 立刻返回；redirect → 记录后 302 到真实 URL；
  - 失败回退：若队列短暂不可用，降级为直连 origin 的 `/api/tracking/*`；
  - 不做重逻辑（识别/幂等/估算放在中心）。
- MQ：区域就近接入 → 跨区汇聚（或区域归档以满足数据驻留）。
- Core Tracking Service（中心）：
  - 幂等去重、MPP/代理识别、口径计算、聚合与报表、外部回传；
  - 统一可观测（OTel）、统一响应，不直接承接外部巨大瞬时流量。

时延收益：点击跳转仅需到最近 PoP（通常 10–30ms），相较直连中心（100–250ms）显著下降；打开像素 204 首包更快，提升“感知速度”和可达性。

## 4. 域名与路由策略
- 每租户自定义追踪域名：`track.tenant.com` → CNAME 边缘平台；
- 可选 GeoDNS，但 Anycast/CDN 本身具备就近调度；
- 严格响应头：`Cache-Control: no-store, max-age=0`；URL 带 `ts/nonce`；
- 目的地址签名/加密封装，点击时在边缘先验签与域白名单。

## 5. 事件上报与幂等
- 事件携带幂等键：`type|cid|sid|mid|lid|ts|nonce`；
- Edge 仅将事件投递 MQ；中心服务按幂等键去重；
- 投递语义：至少一次（At-Least-Once），中心去重保障最终一致；
- 失败重试：队列侧重试 + 中心消费端幂等。

## 6. 安全与隐私
- HMAC-SHA256 验签；`dest` 签名/加密封装；
- 域白名单；速率限制（per IP/tenant/route）；
- `sid` 使用邮箱哈希；IP 可做掩码；
- 数据驻留：
  - EU 用户请求在 EU PoP 处理并投递到 EU 区域队列；
  - 中心处理可采用“区域性处理 + 汇总指标跨区复制”（避免 PII 跨境）。

## 7. 可观测与告警
- Edge：
  - 计数/延迟/失败率/排队时长；
  - 302/204 首包时间分布；
  - 队列投递失败与降级比例；
- Core：
  - 消费延迟/堆积、去重率、MPP/代理占比、估算占比；
  - 签名失败异常、域名白名单命中失败。

## 8. 冷启动与性能
- Cloudflare Workers/Fastly Compute@Edge：冷启动极低，适合高 QPS 短逻辑；
- Lambda@Edge：冷启动可接受，但需关注并发与成本（可用预置并发）；
- 返回体积最小化（204/302），避免图片体积；
- 连接复用：边缘平台自动优化；无需自管。

## 9. 成本与运维
- 边缘平台计费以请求数/CPU 时间/队列吞吐为主；
- 中心服务压力下降，DB/聚合层更稳定；
- 需要 DevOps 维护多环境边缘脚本与 IaC；
- 证书自动化（ACME）与 CNAME 接入自助配置。

## 10. 迁移路径
1) 现网中心服务保持不变；
2) 部署 Edge Collector（open/redirect/beacon/amp 四条路由）到一个区域，压测验证功能与签名；
3) 接入 MQ 并在中心实现消费端幂等；
4) 按地区逐步开启 Anycast/PoP 部署；
5) 启用故障降级：队列不可用 → 回源；
6) 启用数据驻留策略与合规模块；
7) 全量切换与成本回归分析。

## 11. 伪代码（Edge）
```js
// Cloudflare Workers 示例
export default {
  async fetch(req, env, ctx) {
    const url = new URL(req.url)
    const p = Object.fromEntries(url.searchParams)
    if (!verifyHmac(p, env.SECRET)) return new Response(null, { status: 204 })

    const evt = minimalEvent(req, p) // 摘取必要字段
    ctx.waitUntil(env.QUEUE.send(evt))

    if (url.pathname.endsWith('/redirect')) {
      const real = unwrapAndVerifyDest(p.dest, env.SECRET)
      return Response.redirect(real, 302)
    }
    return new Response(null, { status: 204, headers: { 'Cache-Control': 'no-store, max-age=0' } })
  }
}
```

## 12. 结论
- 边缘采集 + 队列 + 中心处理能最大化降低时延、提升点击/打开的首包体验，并隔离核心服务压力；
- 建议采用 Cloudflare Workers/Fastly Compute@Edge 或 Lambda@Edge（如偏 AWS 生态），并结合队列（Cloudflare Queues/SQS/PubSub/Kafka）实现；
- 对于 EU/敏感地区，启用区域化处理与数据驻留策略；
- 与现有“模板级极简开关”不冲突，用户无需关心边缘部署细节。


