# 追踪实现 V2｜技术方案与接口规范

本文件与《PRD｜追踪与分析与报表》《技术方案设计》《打开率追踪_技术细节》共同构成“追踪与分析与报表”的实施说明。本文聚焦端到端追踪实现（打开/点击/回复/转化）、反缓存、签名、防篡改、MPP/代理识别、数据建模、Clean Architecture 接入与可观测性，统一接口与口径，补齐 V2 新增能力。

关联文档：
 - `prd/10_追踪与分析与报表/PRD.md`
 - `prd/10_追踪与分析与报表/技术方案设计.md`
 - `prd/10_追踪与分析与报表/打开率追踪_技术细节.md`
 - `prd/10_追踪与分析与报表/追踪配置与默认策略.md`

---

## 1. 设计原则

- 仅使用 GET/POST；不使用路径参数（所有字段通过查询参数或 JSON 体传递）。
- 自定义追踪域名（每租户/每应用）：隔离信誉、降低“热门资源”缓存概率。
- 所有追踪 URL 唯一化（`ts`+`nonce`）+ 反缓存头（`Cache-Control: no-store, max-age=0`）。
- HMAC-SHA256 参数签名，`dest` 目的地址签名/加密封装并校验。
- 机器人/代理/预取识别与分层口径：Gross 与 Estimated Human 并行。
- 幂等去重与窗口控制；全链路 OTel 可观测；统一响应结构。

核心查询/体字段：
- `cid` 活动 ID、`sid` 订阅者 ID/哈希、`mid` 邮件消息 ID、`lid` 链接 ID
- `ts` 时间戳、`nonce` 随机数、`sig` HMAC 签名、`dest` 目的地址（点击用）

---

## 2. 接口规范（仅 GET/POST）

说明：以下接口部署在追踪域（如 `track.tenant.com`）或网关之下，服务端根据 `Host` 解析租户并加载对应密钥。

### 2.1 打开（像素）
- 方法：GET `/api/tracking/open`
- 入参（Query）：`cid,sid,mid,ts,nonce,sig`
- 响应：204（推荐）或 200 返回 1x1 GIF（极小体积）
- 头：`Cache-Control: no-store, max-age=0`
- 说明：动态 URL + 自定义域名 → 首次加载直达我方；记录 open 事件并做 MPP/代理识别。

### 2.2 打开（Beacon，无图片）
- 方法：GET `/api/tracking/beacon`
- 入参（Query）：`cid,sid,mid,ts,nonce,sig`
- 响应：204
- 说明：用于降低“图片缓存”语义影响，部分客户端不加载 `<object>/<iframe>`，视兼容性可灰度。

### 2.3 打开（AMP）
- 方法：GET `/api/tracking/amp/open`
- 入参（Query）：`cid,sid,mid,ts,nonce,sig`
- 响应：204
- 说明：在支持 AMP 的客户端使用 `<amp-pixel src="...">`。

### 2.4 点击（跳转网关）
- 方法：GET `/api/tracking/redirect`
- 入参（Query）：`cid,sid,mid,lid,dest,ts,nonce,sig`
- 响应：302 → 解封装/验签后的真实 URL
- 头：`Cache-Control: no-store, max-age=0`
- 说明：严格校验签名与目的域白名单；记录点击事件（首次+总次数），可同步/异步回传 GA4/sGTM/Segment。

### 2.5 转化（后端/前端回传）
- 方法：POST `/api/tracking/conversion`
- 请求体（JSON）：
  ```json
  {
    "cid": "...",
    "sid": "...",
    "mid": "...",
    "event": "purchase|signup|custom",
    "order_id": "...",
    "value": 123.45,
    "currency": "USD",
    "ts": **********,
    "nonce": "...",
    "sig": "..."
  }
  ```
- 响应：200（统一响应结构）
- 说明：落地页通过跳转 URL 带下来的 `sid/cid/mid`（cookie/localStorage）回传转化。

### 2.6 入站回复（Inbound）
- 通道：MX → Inbound 网关（如 SES/SendGrid）→ 我方 HTTP
- 我方方法：POST `/api/tracking/inbound`（受限来源）
- 体（JSON）：包含邮件头、`to/from/message_id`、`text/html` 等；`To:` 使用 `reply+{cid}-{sid}-{mid}@inbound.tenant.com`
- 说明：解析 local-part 获取 `cid/sid/mid`，判定自动回复并入库 reply 事件。

### 2.7 Edge 路由与部署示例（可选，边缘采集）
- DNS：`track.{tenant}.domain` CNAME 到边缘平台（如 Cloudflare/ Fastly/ Lambda@Edge）。
- 路由映射（示例）：
  - `/api/tracking/open` → Edge Worker（返回 204）
  - `/api/tracking/beacon` → Edge Worker（返回 204）
  - `/api/tracking/amp/open` → Edge Worker（返回 204）
  - `/api/tracking/redirect` → Edge Worker（记录后 302 到真实 URL）
- 边缘职责：
  - 校验签名（弱校验可灰度）、附加 `Cache-Control: no-store, max-age=0`；
  - 解析查询参数（`cid/sid/mid/lid/ts/nonce/sig[/dest]`）并写入 MQ；
  - 失败时降级回源到中心 `/api/tracking/*`；
  - 不做复杂识别/幂等（由中心处理）。
- Workers 伪代码：
```js
export default {
  async fetch(req, env, ctx) {
    const url = new URL(req.url)
    const p = Object.fromEntries(url.searchParams)
    const evt = buildEvent(req, url, p)
    // 可选：verifyHmac(p, env.SECRET) 失败则打标后继续投递（灰度期）
    ctx.waitUntil(env.QUEUE.send(evt))
    if (url.pathname.endsWith('/redirect')) {
      const real = unwrapAndVerifyDest(p.dest, env.SECRET)
      return Response.redirect(real, 302)
    }
    return new Response(null, { status: 204, headers: { 'Cache-Control': 'no-store, max-age=0' } })
  }
}
```

### 2.8 MQ 事件 Schema（Edge→Core）
- 统一 JSON Schema（示例，核心字段）：
```json
{
  "v": 1,
  "type": "open|beacon|amp_open|click|conversion",
  "host": "track.tenant.com",
  "path": "/api/tracking/open",
  "cid": "campaign_id",
  "sid": "subscriber_hash",
  "mid": "message_id",
  "lid": "link_id (for click)",
  "ts": **********,
  "nonce": "random",
  "sig": "base64url(hmac)",
  "sig_valid": true,
  "dest": "signed_or_encrypted_url (for click)",
  "dest_host": "www.example.com (for click)",
  "ua": "User-Agent",
  "ip": "*******",
  "headers": {"cf-connecting-ip": "...", "accept-language": "..."},
  "edge": {"provider": "cloudflare", "pop": "SIN"},
  "received_at": **********,
  "request_id": "uuid"
}
```
- 幂等键（Core 去重建议）：`type|cid|sid|mid|lid|ts|nonce`。
- 字段映射：
  - `cid/sid/mid/lid/ts/nonce/sig/dest` ← 查询参数；
  - `host/path/headers/ip/ua` ← 边缘请求上下文；
  - `sig_valid` ← 边缘快速验签结果（可选，中心仍二次验证）；
  - `edge.pop/provider` ← 边缘平台注入；
  - `received_at/request_id` ← 边缘生成（单调时间与可追踪 ID）。

---

## 3. 安全与反缓存

- 反缓存：唯一 URL（`ts+nonce`）+ `Cache-Control: no-store, max-age=0`；
- 参数签名：`sig = HMAC-SHA256(BaseString, tracking_secret)`，BaseString 字段顺序固定；
- 目的地址保护：`dest` 使用签名封装或 AES-GCM + HMAC；点击时验签与域白名单校验；
- 速率限制与防刷：对 `open/redirect` 启用基本限流，异常模式告警并分层处理（不直接报错给用户）。

---

## 4. 识别与口径

- 识别：
  - Apple MPP：UA/IP/并发与时间模式；
  - Gmail 代理：代理域与头部；
  - 安全扫描器：数据中心 ASN、极短时间高并发、无 Referer；
- 分层字段：`open_class = human|prefetch|mpp|proxy|scanner`
- 双口径：
  - Gross Opens：全部像素命中（可去重/不去重）；
  - Estimated Human Opens：基于识别 + 点击回填 + 估算模型的“人类打开”。

---

## 5. 去重与估算

- 去重窗口：
  - 打开：同 `(cid,sid,mid)` N 分钟（建议 30）计一次独立；
  - 点击：同 `(cid,sid,mid,lid)` 计首次与总次数；
  - 转化：`(sid,cid,mid,order_id)` 幂等；
- 估算补全（Probabilistic Open）：
  - 基于非代理人群计算基准 OR_nc（分行业/ISP/设备/活动类型）；
  - 规则：有点击→human_open=1；direct/gmail_non_scanner→1；mpp/scanner-only→OR_nc×α（灰度范围 0.7~0.9）。

---

## 6. 数据模型（建议）

- `email_events_open`：`cid,sid,mid,ua,ip,occurred_at,open_class,source,is_unique,sig_valid`
- `email_events_click`：`cid,sid,mid,lid,dest_host,ua,ip,occurred_at,is_first`
- `email_events_reply`：`cid,sid,mid,message_id,from_addr,occurred_at,is_auto_reply`
- `email_events_conversion`：`cid,sid,mid,event,order_id,revenue,currency,occurred_at`
- 聚合：按活动/旅程/联系人/域名/ISP/设备/时间窗口的物化视图或分区表。

---

## 7. Clean Architecture 接入

- 接口层：HTTP Handler（仅 GET/POST），统一中间件、统一响应；
- 应用层：`TrackingApplicationService`（验签、识别、去重、入库、回传）；
- 领域层：`OpenClassifier`、`SignatureService`、`AttributionService`；
- 基础设施层：GORM 仓储（禁止自动预加载）、外部回传客户端（GA4/Segment）、Inbound 适配器。

---

## 8. 可观测与合规

- OTel：每个请求创建 span，携带 `cid/sid/mid/lid/ua/ip/host/open_class`；
- 指标：QPS/延迟/错误率、MPP/扫描器占比、估算占比与偏差；
- 日志：JSON 化并注入 trace/request ID；
- 合规：GDPR/CCPA 同意与披露、最小化采集（`sid` 哈希、IP 掩码可选）、关闭开关。

---

## 9. 迁移与灰度

1) 先接入点击网关与动态像素；
2) 灰度 Beacon 与 AMP；
3) 启用签名验签（初期仅告警，后期强校验）；
4) 增加 MPP/扫描器识别，报表展示双口径；
5) 引入估算补全，分租户/分活动灰度；
6) 打通外部回传（GA4/sGTM/Segment）；
7) 完成迁移并沉淀运营指引与 FAQ。


