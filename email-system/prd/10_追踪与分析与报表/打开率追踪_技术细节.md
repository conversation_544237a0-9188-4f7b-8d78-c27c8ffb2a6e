# 技术细节｜打开率追踪（Open Rate Tracking）

- 版本：v1.0
- 更新时间：2025-08-11
- 状态：Draft
- 关联文档：`prd/10_追踪与分析与报表/技术方案设计.md`，`prd/10_追踪与分析与报表/PRD.md`，`prd/10_追踪与分析与报表/追踪实现V2_技术方案与接口规范.md`

## 1. 概述与目标
- 目标：在隐私代理（Apple MPP、Gmail Image Proxy 等）与安全扫描器存在的现实下，提供透明、可解释且可运营的打开率度量：Gross（像素命中）与 Estimated Human（估算人类）双口径，并支持估算回填与模型校准。
- 成功标准：
  - 事件延迟 P95 < 2s；
  - 指标透明（口径说明、代理占比、估算占比与置信区间）；
  - 估算模型可灰度、可回滚；
  - 反缓存与反误报策略稳健。

## 2. 实现原理
- 1×1 透明像素：在 HTML 邮件中注入唯一 URL（携带 message_id、recipient_id、campaign_id、tenant_id、sig 等），被请求即记一次 pixel_hit。
- 链接重写：所有链接指向追踪域，点击事件作为“强信号”用于回填与口径校准。
- 代理环境：
  - Apple MPP：邮件投递后由代理批量预取图片并缓存，掩蔽 IP/UA，非真实用户打开；
  - Gmail Image Proxy：首次打开通常触发一次像素请求，后续复用缓存；
  - 安全扫描器/沙箱：可能在投递后短时间访问像素与链接，产生“假打开/假点击”。

## 3. 架构与流程
```mermaid
sequenceDiagram
participant M as Mail Client/Proxy
participant CDN as CDN/Edge
participant TRK as Tracker(API)
participant BUS as EventBus
participant AGG as Aggregator
M->>CDN: GET /pixel/{tenant}/{msg}/{rcpt}?sig=...
CDN->>TRK: Forward with headers (UA/IP)
TRK->>BUS: emit open(pixel_hit)
BUS->>AGG: consume and aggregate
Note over TRK,AGG: 去重/归因/分层统计（Gross/Estimated Human）
```

## 4. 事件与数据模型（核心字段）
- `events_open`
  - `id, tenant_id, campaign_id, message_id, recipient_id, occurred_at`
  - `source`（direct|apple_mpp|gmail_proxy|scanner|unknown）
  - `ua, ip, geo, is_first_hit, is_unique_candidate`
  - `sig_valid, cache_control_hint`
- `events_click`: 用于强信号回填与 CTR/CTOR。
- `metrics_hourly`：按维度聚合（campaign/list/domain/isp/device）。

## 5. 口径定义
- Gross Opens：所有像素命中（含代理/扫描器/直连），可 Total/Unique。
- Estimated Human Opens：估算“人类打开”，包含：
  - 直连像素命中（非代理/非扫描器）；
  - Gmail Proxy 首次命中（排除扫描器段）；
  - 有点击的收件人（点击强信号）→ 回填为人类打开；
  - Apple MPP/Scanner-only 场景走估算模型（见 §6）。

## 6. 估算模型（可灰度）
- 分组基准：在“非代理人群”计算 OR_nc（无点击人群的打开率基准），按行业/ISP/设备/名单来源/活动类型等维度分层，每周滚动。
- 估算规则：
  - 若 recipient 有点击 → human_open=1；
  - 否则，若像素来源为 direct/gmail_proxy_non_scanner → human_open=1；
  - 否则，若来源为 apple_mpp/scanner_only → human_open=OR_nc(segment)×α（α∈[0.7,0.9]，灰度与回放验证）；
  - 否则 → human_open=0。
- 活动口径：EstimatedHumanOpens = Σ human_open(recipient)。
- 置信：对各 segment 输出估算占比与±区间（根据历史偏差与样本量）。

## 7. 误报与拦截处理
- 代理识别：UA/IP 段库（Apple/Microsoft/Yahoo 代理、主流安全网关），模式识别（批量并发、无 Referer 等）。
- 反缓存：
  - 像素 URL 唯一路径+查询签名；
  - Cache-Control: no-store,max-age=0（注意：对 MPP/Gmail 代理并不可靠，主要防企业/中间缓存）。
- 反扫描：
  - 速率与节律阈值；
  - 链接与像素交叉验证；
  - 特征命中进入 scanner-only 分层，不计入人类口径。

## 8. 去重与归因
- Unique 去重：`(message_id, recipient_id)` 维度；
- 归因：open 事件与 deliver、click 事件按 `message_id/recipient_id` 联合归因。

## 9. API 概要
- `POST /api/events/ingest`（像素/点击统一入口，或像素走边缘代理内部上报）
- `POST /api/campaigns/report`（返回 Gross/Estimated Human、代理占比、分布等）

## 10. 错误码（节选）
| code | message | 说明 |
|---|---|---|
| 410001 | pixel_blocked | 像素被拦截（仅告警口径） |
| 410002 | invalid_signature | 像素签名校验失败 |
| 410003 | proxy_detected | 识别为代理/缓存命中（信息标签） |

## 11. 监控与告警
- 指标：像素吞吐、延迟、代理占比、scanner-only 占比、估算占比、估算偏差（与点击/回填对照）。
- 告警：代理占比异常飙升、scanner-only 异常、延迟/失败率异常、估算偏差超阈。

## 12. 安全与合规
- 链接签名（HMAC）与过期；
- 仅采集必要头信息，隐私最小化；
- 支持租户级关闭打开追踪；
- 口径说明在报表 UI 显示，避免误解“打开=人类阅读”。

## 13. 默认参数（建议）
- 估算 α：0.8（新租户默认，可灰度）
- 周期：OR_nc 每周滚动；小样本回退到全局/行业均值。
- 去重窗口：unique 7 天（活动级可配）。

## 14. 局限与说明
- 代理/缓存不可完全绕开；
- 文本邮件与禁图无法产生像素；
- 转发打开归因仍指向原收件人；
- 因此提供双口径并配合点击/转化作为更强信号。
