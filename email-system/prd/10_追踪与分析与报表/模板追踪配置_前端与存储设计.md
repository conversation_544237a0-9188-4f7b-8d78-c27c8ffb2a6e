# 模板追踪配置｜前端与存储设计

本文定义在“模板编辑页”新增“追踪与归因”的前端配置，以及模板级 `tracking_options` 的存储模型、默认策略与读写流程。

- 目标：极简开关、默认安全、可观测、可回滚
- 存储：模板表新增 `tracking_options JSON`（NULL 表示按系统默认）
- 渲染：开启时注入像素/Beacon/AMP与链接改写；关闭保持原样

## tracking_options 示例
```json
{
  "enabled": true,
  "utm": { "autoAppend": true, "campaign": "", "content": "" },
  "attribution": { "windowHours": 24 },
  "pixel": { "enableAmp": true, "enableBeacon": true },
  "redirect": { "whitelist": [] },
  "domain": { "trackingDomain": "track.tenant.example.com", "status": "verified" }
}
```

## DDL（示意）
```sql
ALTER TABLE email_templates
  ADD COLUMN tracking_options JSON NULL;
```

## GORM 模型读写（示例）
```go
// TemplateModel 示例
 type TemplateModel struct {
     ID              int64          `gorm:"primaryKey"`
     Name            string         `gorm:"size:128;not null"`
     TrackingOptions datatypes.JSON `gorm:"type:JSON"`
 }

 type TrackingOptions struct {
     Enabled     bool             `json:"enabled"`
     UTM         *UTMOptions      `json:"utm,omitempty"`
     Attribution *AttributionOpts `json:"attribution,omitempty"`
     Pixel       *PixelOpts       `json:"pixel,omitempty"`
     Redirect    *RedirectOpts    `json:"redirect,omitempty"`
 }

 func (m *TemplateModel) SetTrackingOptions(opts *TrackingOptions) error {
     if opts == nil { m.TrackingOptions = nil; return nil }
     b, err := json.Marshal(opts)
     if err != nil { return fmt.Errorf("marshal tracking_options: %w", err) }
     m.TrackingOptions = datatypes.JSON(b)
     return nil
 }

 func (m *TemplateModel) GetTrackingOptions() *TrackingOptions {
     if len(m.TrackingOptions) == 0 { return nil }
     var o TrackingOptions
     if err := json.Unmarshal(m.TrackingOptions, &o); err != nil { return nil }
     return &o
 }
```

> 详细方案与默认策略参考：`追踪实现V2_技术方案与接口规范.md`、`追踪配置与默认策略.md`
