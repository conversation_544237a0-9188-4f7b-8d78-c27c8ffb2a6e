# 技术方案设计｜活动 与 A/B（统一发送流程）

## HLD
- 组件：活动编排器、统一发送引擎、实验管理器、优胜判定器、报告回流、gRPC 调用 Email 模块
- 数据：`campaigns`、`sending_tasks`、`sending_batches`、`email_messages`、`ab_experiments`、`ab_variants`

## 时序
```mermaid
sequenceDiagram
participant UI
participant C as CampaignSvc
participant S as SendingEngine
participant E as ExpSvc
participant Email as EmailModule
UI->>C: 创建/调度活动
C->>S: 创建发送任务
S->>S: 受众解析与渲染
S->>Email: gRPC 发送请求
Email-->>S: 投递结果
S-->>C: 事件回流
C-->>UI: 报告
UI->>E: 创建/配置实验
E->>C: 绑定实验到活动/对象
Email-->>E: 事件回流（按variant打点）
E-->>UI: 实验报告/优胜
```

## LLD
- 幂等键：`message_key=campaign+variant+contact`
- 重试：网络/临时错误指数退避；永久错误记录并抑制
- 并发实验分层：根据实验要素构造 `layer_key`（如 `subject`, `sender`, `send_time`, `content:block_id`），同一 `layer_key` 的活跃实验需合并为同一实验或串行执行。
- 受众分配：在总样本比例内按变体流量进行随机分桶（sticky hashing：`hash(contact_id, campaign_id, layer_key)`），剩余人群按优胜策略发送。

---

## 1. 目标与范围
- 目标：发送错误率<0.1%；并发实验自动冲突检测与优胜；预检覆盖抑制/域名/额度。
- 范围：活动（受众/内容/追踪/预检/调度/执行/报告）、实验（对象/要素/样本/流量/指标/观察期/并发）。

## 2. 数据与接口
- 表：
  - `campaigns(campaign_id, name, ... )`
  - `campaign_batches(batch_id, campaign_id, ... )`
  - `email_messages(message_id, campaign_id, variant_id, contact_id, dedupe_key, ... )`
  - `ab_experiments(exp_id, campaign_id, layer_key, sample_pct, alloc_method, metric, observe_hours, win_strategy, status, ... )`
  - `ab_variants(variant_id, exp_id, name, traffic_pct, payload, status, ... )`
- 事件打点：`message_events(message_id, event_type, variant_id, exp_id, ts, ... )`
- API：
  - 活动：`/api/campaigns/create|update|schedule|pause|resume|cancel|preview|send_test|report`
  - 实验：`/api/experiments/create|update|start|pause|list|detail|attach|detach|apply_winner|report`

## 3. 规则与算法
- 预检：抑制/黑名单、域名身份/限速、额度；
- 并发实验：同一 `layer_key` 冲突需合并或串行；跨 `layer_key` 可并发，采用独立分层哈希避免干扰；
- 变体分配：平均/贝叶斯多臂赌博机（根据 Beta 分布实时更新）/手动；
- 优胜：观察期结束或达到置信标准提前胜出；
- 去重：同一联系人不重复接收（包含排除集合）。

## 4. 可靠性
- 队列化：`email_messages` 表驱动；锁、重试、死信；
- 幂等：`dedupe_key` 与 `(campaign, variant, contact)`；
- 失败批次仅失败重发；
- 实验一致性：实验配置变更基于版本号，消息入队时写入 `exp_id`/`variant_id` 快照。

## 5. 安全与合规
- 审计：预检结果与变更；
- 内容合规：退订/公司信息。

## 6. 可观测与性能
- 指标：批次吞吐、失败率、AB 优胜耗时、预检失败分布、各 `layer_key` 的样本覆盖与泄露率；
- 告警：失败率/退信率/投诉率阈值、实验分配偏斜/样本不足预警。

## 7. 测试与验收
- 正常/失败/重试/仅失败重发；A/B 优胜；
- 预检命中与拒绝路径。

## 8. 发布与回滚
- 调度器开关与灰度；
- 预检规则版本化与回滚。

## 9. 风险与对策
- 受众超大：分片 + 限速；
- 预检漏检：规则防逃逸与全链路审计。

## 10. Checklist
- 统一响应；
- 队列化与幂等；
- 报告与审计闭环。
