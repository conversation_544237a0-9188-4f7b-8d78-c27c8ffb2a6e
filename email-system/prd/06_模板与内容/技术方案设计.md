# 技术方案设计 | 模板与内容（多语言映射）

## 1. 聚合与数据归属
- 模板聚合由外部 `templates`（Email 模块维护）与内部 `template_locales`（Email-System 维护）组成，二者形成模板的完整可渲染实体。
- `template_locales` 为 Email-System 内部表：每条记录对应一个外部 template_id + locale 的语言化内容（主题/预览文本/正文与资源覆盖）。
- Email-System 内实现多语言内容管理、预览与渲染；发送通过 gRPC 调用 Email 内核执行投递与可达性治理。

## 2. 表结构（DDL 摘要）
```sql
-- 注：templates 表由 Email 模块维护，Email-System 不创建
-- Email-System 仅维护多语言映射表
CREATE TABLE IF NOT EXISTS template_locales (
  id                 BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  template_id        BIGINT NOT NULL COMMENT '模板ID（Email 模块模板ID）',
  locale             VARCHAR(16) NOT NULL COMMENT '语言代码（如 zh-CN/en-US）',
  is_default         TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否默认语言',
  subject            VARCHAR(512) NOT NULL COMMENT '主题',
  preheader          VARCHAR(256) NULL COMMENT '预览文本',
  html_content       MEDIUMTEXT NOT NULL COMMENT 'HTML 内容',
  mjml_content       MEDIUMTEXT NULL COMMENT 'MJML 源码（可选）',
  text_content       TEXT NULL COMMENT '纯文本版本（可选）',
  variables_json     JSON NULL COMMENT '语言特定变量/默认值',
  assets_overrides   JSON NULL COMMENT '资源覆盖（按语言差异化）',
  created_at         DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at         DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY uk_tpl_locale (template_id, locale),
  KEY idx_tpl_locale (template_id, locale)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板语言变体表（多语言内容）';
```

## 3. 读写流程
- 预览/渲染：通过 template_id 获取外部模板配置，读取匹配的 `template_locales`（按联系人 `preferred_language` 或活动语言策略）；缺失时回退 `default_locale`。
- 发送：在 Email-System 内完成渲染后，通过 gRPC 调用 Email 模块执行投递。

## 4. 迁移策略
- 若历史存在 `template_content`：按模板 `default_locale` 迁移至 `template_locales`，并补齐 `is_default` 标识；详见 SQL 文件中的迁移示例。
- 模板主表迁移：将现有模板数据迁移至 Email 模块，Email-System 保留 template_id 引用关系。

## 5. 接口与边界
- 内部接口仅 GET/POST，统一响应；跨服务通过 gRPC 调用 Email 内核。
- Email-System 负责多语言内容的 CRUD 操作，Email 模块负责模板主体的管理。

## 6. 发送侧协作
- `email_messages` 不再落地完整正文；发送时根据外部模板与内部语言变体动态渲染，持久化必要的主题与变量快照。
- 渲染完成后通过 gRPC 将完整内容传递给 Email 模块执行投递。
# 技术方案设计｜模板与内容

## HLD
- 组件：编辑器前端、渲染与校验服务、资产存储、审批流服务、Diff 服务
- 数据：`templates`、`template_versions`、`assets`、`approvals`

## 时序
```mermaid
sequenceDiagram
participant UI
participant VAL as Validator
participant APP as Approval
participant DB
UI->>VAL: 校验模板
VAL-->>UI: 校验报告
UI->>APP: 提交审批
APP->>DB: 状态变更
```

## LLD
- 语法：Handlebars 子集 + 片段；
- 校验：变量必填、退订/公司信息、链接追踪；
- 渲染：MJML→HTML 预编译缓存。

### 多语言渲染与回退（新增）
- 数据模型：`template_locales` 存储语言变体，链接 `template_versions`
- 选择策略：`preferred_locale` → 语言泛化（en-US→en）→ 默认语言
- 渲染缓存键：`template_id:version:locale`
- 校验扩展：各语言的 subject/preheader/content_html 必填校验；缺失提示
- 预览：前端切换 locale 参数传给预览 API，返回对应内容

---

## 1. 目标与范围
- 目标：多语言内容渲染预览 <1s；语言变体管理；变量缺失阻止发布。
- 范围：多语言内容管理、预览/渲染、变量/片段校验、语言回退策略。

## 2. 数据与接口
- 表：`template_locales`（Email-System 维护）；`templates`、`template_versions`、`template_approvals`（Email 模块维护）；
- API：POST `/api/templates/locales/add|update|delete`，GET `/api/templates/locales/list|get`；
  - 预览：POST `/api/templates/preview`（基于外部模板 + 内部语言内容）。

## 3. 规则与算法
- 变量治理：必填校验、默认值、类型检查；
- 合规模型：退订/公司信息/垃圾词；
- 链接重写与 UTM 自动注入（可选开关）。
 - 多语言回退：语言-地区拆解回退，按 `zh-Hans-CN→zh-CN→zh→默认`。

## 4. 可靠性
- 审批版本不可变；
- 渲染与校验服务熔断与兜底提示。

## 5. 安全与合规
- 模板内容存储与访问鉴权；
- 发布前执行 `compliance_validation_logs` 校验并存档结果。

## 6. 可观测与性能
- 指标：渲染/校验耗时、发布通过率、回滚率；
- 告警：渲染 P95 异常、校验失败突增。

## 7. 测试与验收
- 变量缺失、退订校验、版本 diff、审批流、回滚；
- 性能：批量预览与大模板渲染。

## 8. 发布与回滚
- 版本化发布；审批策略可配置，回滚到上一个发布版本。

## 9. 风险与对策
- 大模板/外链资源导致渲染慢：缓存与占位提示；
- 变量与数据口径不一致：强校验 + 示例渲染。

## 10. Checklist
- 统一响应；
- 审批/审计闭环；
- 校验与发布门禁启用。
