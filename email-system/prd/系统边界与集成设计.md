# 系统边界与集成设计（Users / Email / Email-System）

本文明确 Users、Email、Email-System 三个上下文的功能边界、数据归属、接口契约与运行职责，避免职责重叠与后期难以维护。

## 0. 参与方与统一约束
- 参与方：
  - Users：账户/租户/组织/权限（认证与鉴权中枢）
  - Email：事务型邮件内核（账户/服务商/投递执行/可达性）
  - Email-System：营销域（活动/受众/模板与多语言内容/渠道扩展/分析/偏好抑制/集成）
- 接口约束（所有服务）：仅 GET/POST；无路径参数；统一响应 { code, message, data, meta }；统一 Trace/Log；错误不暴露内部细节。
- 应用标识约定：Email 模块以 (tenant_id, internal_app_id) 作为隔离与归属键。Email-System 使用“固定 internal_app_id”对接 Email 模块，不在本系统内做 appId 选择；仅在跨服务调用时透传固定 internal_app_id（可按需记录审计）。

## 1. 职责矩阵（按功能维度划分）
| 功能/域 | 归属 | 说明 |
|---|---|---|
| 认证（JWT/OIDC） | Users | 登录、令牌签发、刷新、会话与登出；全链路携带 user_id/tenant_id（app_id 对 Email-System 固定、可忽略） |
| 租户/组织/角色/RBAC | Users | 多租户与组织、角色/权限、API 授权（网关注入上下文）|
| 发件账户凭证存储（SMTP/API） | Email | 表：email_accounts（host/port/username/password/is_ssl 等）；包含启用状态、测试状态；隔离键 (tenant_id, internal_app_id) |
| 服务商注册/图标/端口预设/排序 | Email | 表：email_providers；字段：icon_url、smtp/imap/pop3、sort_order 升序返回 |
| 模板与多语言内容 | 混合 | 模板/版本由 Email 模块维护；Email-System 仅维护多语言映射 `template_locales` 与渲染变量；预览/渲染在 Email-System 内完成 |
| 发送记录（营销域） | Email-System | 表：email_messages、email_delivery_events；以表驱动的请求/执行隔离；通过 gRPC 调用 Email 执行投递与决策 |
| 投递执行与可达性 | Email | 采集并产生事件；限流/预热策略的最终裁决与执行 |
| 投递与可达性（退信/投诉/延迟/信誉） | Email | 采集并产生事件；限流/预热策略的最终裁决与执行 |
| 发送触发（活动/自动化/手动任务） | Email-System | 活动、自动化旅程、手动发送任务统一通过模板关联发件账户；支持接入自动化流程作为执行策略 |
| 活动（Campaigns）/A\B 测试 | Email-System | 表：campaigns/experiments/variants；活动状态与发布/暂停/恢复/取消 |
| 受众（Segments/Tags） | Email-System | 表：segments/tags；临时圈选草稿、快照、预估；写入活动绑定 |
| 抑制列表/同意与偏好 | Email-System | 表：suppression/consent/preferences；公开退订/偏好中心接口 |
| 追踪域/回调 Webhooks | Email-System | 表：tracking_domains/webhooks；签名/重试/死信与告警 |
| 分析与报表/导出 | Email-System | 表：analytics_* 与 export_jobs；聚合、下载链接 |
| 用户/租户上下文注入 | Users | 中间件写入 request 上下文；header 透传（X-Request-Id、X-Tenant-Id；X-App-Id 固定、可不参与业务）|

> 关键点：敏感凭证仅在 Email（email_accounts）存储；Email-System 不落地密钥，仅保存引用（account_id）与营销策略元数据。

## 2. 数据模型归属（表级明确）
- Users：users、tenants、orgs、roles、permissions、sessions、audit_logs...
- Email：
  - email_accounts（账户/凭证/启用/测试）
  - email_providers（服务商/图标/预设/sort_order）
  - email_templates（渲染实体）
  - email_messages（消息/状态/错误/时间戳）
- Email-System：
  - template_locales（多语言映射表）
  - campaigns/experiments/variants（活动、A/B 测试）
  - journeys（自动化旅程）
  - manual_send_tasks（手动发送任务）
  - segments/tags（分群/标签）、audience_drafts（圈选草稿）
  - suppression/consent/preferences（抑制/同意/偏好）
  - tracking_domains/webhooks（追踪域/回调）
  - analytics_*（聚合指标）、export_jobs（导出任务）

## 3. 策略：配置与执行分离
- 策略生成（Email-System）：
  - 固定间隔：config.send_interval_seconds（整数，秒）
  - 自动决策：config.auto_decision（布尔）
  - 时间表：schedule_json（时区、窗口、工作日）
  - 预热：warmup_json（起始速率、增量、上限）
- 最终执行（Email）：
  - 统一限流/预热/退避；遇到信誉/退信/投诉升高时可降档；记录决策与原因
  - 对来自 Email-System 的策略进行有效性校验与必要的保护性调整

## 4. 接口契约（跨服务，仅列要点）
-- Email-System → Email（均需透传 fixed internal_app_id）：
  - POST /api/email/accounts/check（校验 account_id 可用/可达；headers/body 中包含 tenant_id 与 internal_app_id）
  - GET  /api/email/providers/list（获取服务商及排序；可按 internal_app_id 过滤）
  - POST /api/email/send（提交发送任务：tenant_id、internal_app_id、account_id、message 构造/策略期望；正文由 Email-System 基于模板渲染生成）
- Email → Email-System：
  - POST /api/events/email（投递事件回调：delivered/opened/clicked/bounced/complained/unsubscribed 等，带签名，重试幂等）
- Users 提供：
  - 认证/授权：签发 JWT；Email/Email-System 网关统一校验
- 上下文注入：tenant_id、user_id；Email-System 在调用 Email 时注入固定 internal_app_id；审计记录

所有接口仅 GET/POST；不使用路径参数；响应：
```json
{ "code": 0, "message": "success", "data": {}, "meta": {} }
```

## 4.1 gRPC 接口契约（Email-System → Email）

### 4.1.1 发送接口
```protobuf
service EmailService {
  rpc SendEmail(SendEmailRequest) returns (SendEmailResponse);
  rpc GetSendingStats(GetSendingStatsRequest) returns (GetSendingStatsResponse);
  rpc CancelSending(CancelSendingRequest) returns (CancelSendingResponse);
}

message SendEmailRequest {
  int64 tenant_id = 1;
  int64 template_id = 2;
  string to_email = 3;
  string from_email = 4;
  string from_name = 5;
  string subject = 6;
  string html_content = 7;
  string text_content = 8;
  map<string, string> headers = 9;
  map<string, string> variables = 10;
  string locale_used = 11;
  bool track_open = 12;
  bool track_click = 13;
  int32 priority = 14;
  string dedupe_key = 15;
}

message SendEmailResponse {
  string message_id = 1;
  string status = 2;
  string error_message = 3;
}
```

### 4.1.2 统计接口
支持按照 Email-System 模块的不同统计区间获取发送数量、成功率、退信率等指标。

### 4.1.3 撤回/取消接口
支持撤回未发送的邮件或取消正在发送的批次。

## 5. 端到端流程（示例）
1) 营销人员在 Email-System 创建活动/自动化旅程/手动任务，选择 Template（模板关联发件账户）、Segment，配置发送策略。
2) Email-System 在发送时渲染模板内容（基于 template_locales 多语言映射），生成受众快照与发送计划。
3) Email-System 通过 gRPC 调用 Email 模块的发送接口，传递渲染后的内容和发送参数。
4) Email 模块执行投递与限流；遇到风险自动降档；产生事件（投递/打开/点击/退信/投诉）。
5) 事件回流至 Email-System，进入分析与看板；A/B 测试可根据指标自动选优。

## 6. 安全与合规
- 凭证隔离：凭证仅在 Email；Email-System 不落地密钥。
- 数据最小化：跨服务只传必要 ID 与安全元数据；模板渲染变量做白名单校验与体积限制。
- 公开端点（退订/偏好）签名与过期机制；防刷与速率限制；审计日志。

## 7. 观测与稳定性
- Tracing：跨服务 trace-id 贯通（渲染/排队/投递/回调/汇总）。
- Metrics：投递成功率、退信率、投诉率、打开/点击、QPS/延迟、队列滞后、导出耗时。
- 日志：结构化日志，携带 request_id/trace_id/tenant_id/account_id/campaign_id。

## 8. 发布与伸缩（SLO 建议）
- Email：SLO 99.95%；关键路径（投递）低延迟与高并发；按带宽/并发扩容。
- Email-System：SLO 99.9%；活动/圈选/报表可异步化；按 CPU/存储扩容。
- 同仓多服务（monorepo）+ 独立部署：共享 `pkg/`，统一规范。

## 9. 迁移与治理清单
- [ ] 所有营销通道均引用 account_id，不复制凭证
- [ ] 模板统一走 Email 渲染内核；营销侧仅做版本/审批/变体
- [ ] 策略（固定间隔/自动决策/时间表/预热）在 Email-System 产出，Email 执行并可保护性降档
- [ ] 事件统一回流 Email-System 做分析与报表
- [ ] Users 统一认证/授权/租户上下文（app_id 固定、可忽略）；跨服务审计贯通
- [ ] 所有跨服务接口遵循 GET/POST + 统一响应 + 错误隐藏 + Trace 贯通

> 本文与 `email-system/api/*.md` 接口文档配套。后续新域（自动化旅程、计费/额度、内容合规）按本边界模式扩展，新增“功能-表-接口-运行职责”四元组条目。
