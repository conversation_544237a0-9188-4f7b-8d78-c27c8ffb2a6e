## 邮件发送调度与 Email 服务交互设计

### 背景与目标
为满足活动群发、自动化旅程与手动任务三类场景，同时支持优先级、取消/撤回、套餐与容量查询、批量入队并获得可追踪的 message_id、活动 deadline 以及失败重试策略，制定 email-system 与 email 服务的清晰分工与 gRPC 交互契约。在不依赖 MQ 的前提下，采用数据库队列隔离请求与执行[[偏好说明：以 email 服务的 `email_messages` 表作为执行队列，email-system 保持业务级发送记录与编排]]。

## 职责边界（细化）
- email-system（业务编排层）
  - 圈选/标签、细分、受众快照物化与频控去重
  - 计划与批次：SendPlan → SendBatch 切分、优先级、deadline、暂停/恢复/取消
  - 依据邮箱账户能力/配额动态决定批次大小与预下发节奏
  - 通过 gRPC 批量下发到 email 服务，携带 send_at、priority、retry 策略、idempotency_key
  - 维护业务级发送记录与进度（与 email 消息一一关联），以及活动/旅程维度看板
  - 对未执行的批次和消息进行取消；对已执行仅能“停止后续/压制”，不承诺真实撤回

- email 服务（执行层）
  - 接收批量写入执行队列 `email_messages`，返回稳定的 `message_id`
  - 基于 DB 租约的 worker 执行：并发/速率/连接池、退避重试、失败标注、可见性超时
  - 按 send_at 与 deadline 约束执行，超时自动丢弃未开始消息
  - 事件与状态回传（Outbox + 拉取或回调接口）：delivered/bounce/complaint/open/click 等
  - 提供账户能力/配额/发送量统计的查询能力（多维度）

说明：模板与模板版本归属 Email 模块（外部系统），email-system 不维护模板，仅持引用。

## 核心数据模型（与 SQL 设计）

### email-system 侧（业务编排与记录）
1) 发送计划表 `send_plans`
```sql
CREATE TABLE IF NOT EXISTS send_plans (
  plan_id          BIGINT PRIMARY KEY,
  tenant_id        BIGINT NOT NULL,
  plan_type        VARCHAR(32) NOT NULL,              -- campaign | automation | manual
  display_name     VARCHAR(255) NOT NULL,
  template_id      VARCHAR(128) NOT NULL,
  priority         INT NOT NULL DEFAULT 5,            -- 1(最低)-10(最高)
  schedule_from    TIMESTAMP NULL,                    -- 可选，计划起始时间窗口
  schedule_to      TIMESTAMP NULL,                    -- 可选，计划结束窗口
  deadline         TIMESTAMP NULL,                    -- 截止时间，过期未发不再发送
  status           VARCHAR(32) NOT NULL,              -- draft|running|paused|completed|canceled|expired
  retry_policy     JSON NULL,                         -- 见下方策略定义
  created_at       TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at       TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

2) 批次表 `send_batches`
```sql
CREATE TABLE IF NOT EXISTS send_batches (
  batch_id         BIGINT PRIMARY KEY,
  plan_id          BIGINT NOT NULL,
  tenant_id        BIGINT NOT NULL,
  expected_size    INT NOT NULL,
  send_at          TIMESTAMP NOT NULL,                -- 该批理想发送时间（可提前下发）
  priority         INT NOT NULL,
  status           VARCHAR(32) NOT NULL,              -- pending|enqueued|running|paused|completed|canceled|expired|failed
  throttle_key     VARCHAR(256) NULL,                 -- 速率/配额维度键，如 tenant:provider:pool
  cancelable_until TIMESTAMP NULL,                    -- 允许取消截止（通常 <= send_at 或 deadline）
  created_at       TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at       TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_plan (plan_id),
  INDEX idx_status (status)
);
```

3) 受众快照表 `audience_snapshots`
```sql
CREATE TABLE IF NOT EXISTS audience_snapshots (
  snapshot_id      BIGINT PRIMARY KEY,
  plan_id          BIGINT NOT NULL,
  tenant_id        BIGINT NOT NULL,
  segment_query    JSON NOT NULL,                      -- 圈选条件
  materialized_at  TIMESTAMP NOT NULL,
  total_count      INT NOT NULL,
  created_at       TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_plan (plan_id)
);
```

4) 发送记录表 `send_records`（业务侧追踪，映射 email 服务 message_id）
```sql
CREATE TABLE IF NOT EXISTS send_records (
  record_id        BIGINT PRIMARY KEY,
  plan_id          BIGINT NOT NULL,
  batch_id         BIGINT NOT NULL,
  tenant_id        BIGINT NOT NULL,
  recipient        VARCHAR(320) NOT NULL,
  personalization  JSON NULL,
  template_id      VARCHAR(128) NOT NULL,
  client_token     VARCHAR(128) NOT NULL,              -- 幂等键（由 email-system 生成）
  email_message_id BIGINT NULL,                        -- email 服务返回 message_id
  status           VARCHAR(32) NOT NULL,               -- pending|enqueued|sent|delivered|bounced|complained|failed|canceled|skipped|expired
  error_code       VARCHAR(64) NULL,
  error_message    VARCHAR(512) NULL,
  send_at          TIMESTAMP NOT NULL,
  deadline         TIMESTAMP NULL,
  retry_policy     JSON NULL,
  created_at       TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at       TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uniq_client_token (tenant_id, client_token),
  INDEX idx_email_msg (email_message_id),
  INDEX idx_plan_batch_status (plan_id, batch_id, status)
);
```

备注：email-system 侧保存业务记录，便于活动/旅程统计与跨渠道分析；执行细节由 email 服务维护。

### email 服务侧（执行队列与事件）
仅列关键字段，完整设计在 email 服务仓库维护，已有功能，不做修改：
```sql
C-- 0008_recreate_email_messages_strict.sql
-- 严格按给定设计：先删除表再创建；所有字段一次性在 CREATE TABLE 中定义（不使用 ALTER）
-- 说明：
--  - 地址列为逗号分隔字符串（varchar），发送内容与标题通过模板可以还原，不在表内持久化
--  - 取消使用 status 维护，无需单独取消标记列

SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS `email_messages`;
SET FOREIGN_KEY_CHECKS = 1;

CREATE TABLE `email_messages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `email_id` bigint NOT NULL COMMENT '业务侧请求ID/去重ID（唯一）',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用id',
  `template_id` bigint DEFAULT NULL COMMENT '模板ID（外部/邮件模块）',
  `account_id` bigint NOT NULL DEFAULT '0' COMMENT '冗余的发件账号ID',
  `from_address` text NOT NULL COMMENT '发件人地址',
  `to_addresses` text NOT NULL COMMENT '收件人地址，逗号分隔',
  `cc_addresses` text DEFAULT NULL COMMENT '抄送地址，逗号分隔',
  `bcc_addresses` text DEFAULT NULL COMMENT '密送地址，逗号分隔',
  `variables` json DEFAULT NULL COMMENT '模板变量快照（渲染所需）',
  `status` varchar(20) NOT NULL COMMENT '状态：queued|running|sent|delivered|bounced|failed|canceled|expired 等',
  `priority` bigint DEFAULT '2' COMMENT '优先级，数值越大越高',
  `retry_count` bigint DEFAULT '0' COMMENT '已重试次数',
  `last_retry_at` datetime(3) DEFAULT NULL COMMENT '上次重试时间',
  `max_retries` bigint DEFAULT '3' COMMENT '最大重试次数',
  `retry_policy` json DEFAULT NULL COMMENT '重试策略：{mode,max_attempts,initial_backoff_ms,max_backoff_ms,retry_on}',
  `scheduled_at` datetime(3) DEFAULT NULL COMMENT '计划发送时间',
  `sent_at` datetime(3) DEFAULT NULL COMMENT '实际发送时间',
  `no_later_than` datetime(3) DEFAULT NULL COMMENT '截止时间（超过不再发送）',
  `batch_id` bigint DEFAULT NULL COMMENT '发送批次ID',
  `idempotency_key` varchar(128) DEFAULT NULL COMMENT '幂等键（可选）',
  `canceled_at` datetime(3) DEFAULT NULL COMMENT '取消时间（取消由status维护）',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_email_messages_email_id` (`email_id`),
  KEY `idx_email_messages_tenant_id` (`tenant_id`),
  KEY `idx_email_messages_status` (`status`),
  KEY `idx_email_messages_deleted_at` (`deleted_at`),
  KEY `idx_tenant_status_created` (`tenant_id`,`status`,`created_at`),
  KEY `idx_tenant_sent_at` (`tenant_id`,`sent_at`)
) ENGINE=InnoDB AUTO_INCREMENT=5100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='发送内容与标题通过模板可还原，不落库';

-- 备注：发送内容与标题通过模板可以还原，不在表内持久化



```

## 优先级设计
- 默认映射（可配置覆盖）：
  - automation（旅程/事件）= 8
  - campaign（活动）= 5
  - manual（手动）= 4
- 执行层以数值越大优先级越高；同优先级内按 `send_at` 优先，随后按 `id`。
- 结合 `throttle_key`（如 `tenant:provider:pool`）做配额与并发隔离。

## deadline 规则
- Plan 级：超过 `deadline` 的未开始批次标记为 `expired`，不再下发。
- Message 级：`no_later_than`（email 侧）作为最终兜底，worker 在领取时若 `now > no_later_than`，直接标记 `expired`。

## 取消与撤回
- 未执行：
  - email-system 将 `send_batches`/`send_records` 标记 `canceled`；
  - 同步调用 email 服务 `CancelMessages`/`CancelBatch` 撤销队列中尚未领取的消息；
  - 若已被 worker 领取但未实际发送，租约过期或检查到取消标记后跳过。
- 已执行：
  - 邮件不可强制撤回；实现“停止后续发送/压制”与“抑制列表添加”。

## 失败重试策略（策略载荷）
```json
{
  "mode": "RETRY_EXPONENTIAL",    // NONE|RETRY_LINEAR|RETRY_EXPONENTIAL
  "max_attempts": 3,
  "initial_backoff_ms": 10000,
  "max_backoff_ms": 600000,
  "retry_on": ["TEMPORARY_FAILURE", "RATE_LIMIT", "NETWORK"]
}
```
email-system 在下发时附带策略；email 服务严格执行并在状态回传中携带最终尝试结果。对于“失败忽略”，将 `mode=NONE` 或 `max_attempts=1`。

## gRPC 交互设计

### 服务与消息（IDL 草案）
```proto
syntax = "proto3";
package emailpb;

// 账户能力与容量
service EmailCapacityService {
  rpc GetAccountCapacity(GetAccountCapacityRequest) returns (GetAccountCapacityResponse);
  rpc GetQuotaStatus(GetQuotaStatusRequest) returns (GetQuotaStatusResponse);
  rpc GetSendStats(GetSendStatsRequest) returns (GetSendStatsResponse);
}

// 批量入队与控制
service EmailEnqueueService {
  rpc EnqueueMessages(BulkEnqueueRequest) returns (BulkEnqueueResponse);
  rpc CancelMessages(CancelMessagesRequest) returns (CancelMessagesResponse);
}

// 事件回传（拉取式）
service EmailEventsService {
  rpc PullEvents(PullEventsRequest) returns (PullEventsResponse);
}

message GetAccountCapacityRequest {
  int64 tenant_id = 1;
  string account_id = 2;          // 邮件账户/发信域/池
}

message GetAccountCapacityResponse {
  int32 supported_batch_size = 1;  // 建议的单批上限
  string next_send_time = 2;       // RFC3339
  map<string,int32> throttle_limits = 3; // 维度->每分钟上限 e.g. provider_ses=10000
}

message GetQuotaStatusRequest { int64 tenant_id = 1; string account_id = 2; }
message GetQuotaStatusResponse {
  int64 quota_total = 1; int64 quota_used = 2; int64 quota_remaining = 3; string reset_time = 4;
}

message GetSendStatsRequest {
  int64 tenant_id = 1; string granularity = 2; // minute|hour|day
  string from = 3; string to = 4;              // RFC3339
  repeated string group_by = 5;                // e.g. ["provider","domain","ip_pool"]
}
message GetSendStatsResponse { repeated StatBucket buckets = 1; }
message StatBucket { string window_start = 1; map<string,string> dims = 2; int64 sent = 3; int64 delivered = 4; int64 bounced = 5; int64 complaints = 6; }

message BulkEnqueueRequest {
  int64 tenant_id = 1; string plan_id = 2; string batch_id = 3; int32 priority = 4;
  string throttle_key = 5; string send_at = 6; string no_later_than = 7; RetryPolicy retry = 8;
  repeated EnqueueItem messages = 9;
}
message EnqueueItem {
  string client_token = 1;  // 幂等键
  string to = 2; string template_id = 3; string body_ref = 4; string personalization_json = 5;
}
message RetryPolicy { string mode = 1; int32 max_attempts = 2; int32 initial_backoff_ms = 3; int32 max_backoff_ms = 4; repeated string retry_on = 5; }

message BulkEnqueueResponse { repeated MessageAck acks = 1; }
message MessageAck { string client_token = 1; int64 message_id = 2; string status = 3; string error = 4; }

message CancelMessagesRequest { int64 tenant_id = 1; repeated int64 message_ids = 2; string reason = 3; }
message CancelMessagesResponse { int32 requested = 1; int32 canceled = 2; }

message PullEventsRequest { int64 tenant_id = 1; string cursor = 2; int32 limit = 3; }
message PullEventsResponse { repeated DeliveryEvent events = 1; string next_cursor = 2; }
message DeliveryEvent { int64 message_id = 1; string type = 2; string time = 3; string provider_code = 4; string info = 5; }
```

要点：
- BulkEnqueue 返回 `message_id`，email-system 将其写回 `send_records.email_message_id`，实现全链路追踪；
- `client_token` 在 email 服务侧具备幂等保障；
- 通过 `GetAccountCapacity` 与 `GetQuotaStatus` 决策批次大小与发送节奏；通过 `GetSendStats` 做运营看板与策略自适应。

## 批次与调度策略（细化）
- 批次大小：
  - automation：100–1000/批（低延迟）
  - campaign/manual：5k–20k/批（根据容量动态调整）
- 预下发：默认 T-15min 将未来 15–30 分钟窗口内批次写入 email 队列，设置 `send_at` 与 `no_later_than`；
- 动态整形：
  - 周期性查询 `GetAccountCapacity`/`GetQuotaStatus`，根据余量与速率平滑切批；
  - `throttle_key` 维度限速，防止账户/域/池超限；
- 冲突与背压：
  - 当容量不足，优先保证高优先级（automation）批次；
  - 低优先级批次可以延后 send_at，但不得越过 deadline；
  - 越过 deadline 的剩余量标记 `expired`。

## 三类发送场景流程
- 活动（campaign）
  1) 圈选 → 快照 → 切批（带 priority、send_at、deadline）
  2) 动态容量评估 → 批量入队（拿回 `message_id`）
  3) 监控进度与事件回传；deadline 到达未发部分过期
  4) 可在 cancelable 窗口内取消

- 自动化（automation）
  1) 事件触发微批物化（频控/去重），即时或短延时的 `send_at`
  2) 入队返回 `message_id`，高优先级执行
  3) 重试策略通常保守（低延迟、少次尝试）

- 手动任务（manual）
  - 与活动一致但规模较小，支持即时暂停/恢复/取消

## 失败与回传（细化）
- 状态机（email 服务）：queued → leased/running → sent → delivered|bounced|complained；任一阶段可能进入 failed/expired/canceled
- 重试：按策略退避；达到上限标记 `failed_max_attempts`
- 回传：
  - 事件拉取 `PullEvents`（推荐至少一次投递保证）；email-system 以游标拉取并幂等落库，更新 `send_records`
  - 聚合看板与 SLA 指标基于事件流构建

## 风险与治理
- 时钟偏移：统一 UTC；email 服务在领取时校验 `send_at/no_later_than`
- 幂等与重复：`client_token` 保证幂等，DB 唯一键约束；入队失败需可重试
- 取消竞态：队列项加“取消标记”并在领取与发送前二次检查
- 配额一致性：容量为近似值，策略需保守，避免冲击配额
- 合规与退订：入队前校验黑名单/退订；投诉回传自动进入抑制
- 观测：全链路 trace（plan_id/batch_id/message_id）、错误码分层、速率与延迟指标

## 与邮件账户能力的交互
- 通过 `GetAccountCapacity` 获取：建议批次上限、下一可用窗口、维度限速
- 通过 `GetQuotaStatus` 获取：套餐总量、已用、剩余、重置时间
- 通过 `GetSendStats` 获取：按 provider/domain/ip_pool/tenant 等维度聚合的发送量与效果

## 编排层接口与策略落地
- 入队时附带：`priority`、`send_at`、`no_later_than`、`throttle_key`、`retry_policy` 与 `client_token`
- 取消：优先按 `message_id` 精确取消；批次/计划级联取消需批量查询 `message_id` 后调用 `CancelMessages`
- 失败处理：email-system 根据事件回传决定是否触发补发（新 `client_token`）或放弃

## 实施步骤
1) 在 email-system 新增：`send_plans/send_batches/audience_snapshots/send_records` 表与用例服务
2) 集成 email gRPC：
   - 周期性容量/配额拉取
   - 批量入队并回填 `message_id`
   - 取消接口接入
   - 事件拉取与幂等入库
3) 策略配置化：优先级/批次大小/退避参数/deadline 规则
4) 观测：打通 OpenTelemetry trace、指标与结构化日志

## 示例：批量入队请求载荷（逻辑示例）
```json
{
  "tenant_id": 42,
  "plan_id": "p_20250813_001",
  "batch_id": "b_0001",
  "priority": 5,
  "throttle_key": "tenant42:ses:poolA",
  "send_at": "2025-08-13T06:00:00Z",
  "no_later_than": "2025-08-13T07:00:00Z",
  "retry": {"mode":"RETRY_EXPONENTIAL","max_attempts":3,"initial_backoff_ms":10000},
  "messages": [
    {"client_token":"t42:pX:u10001","to":"<EMAIL>","template_id":"tpl_abc","personalization_json":"{\"name\":\"Alice\"}"}
  ]
}
```

## 默认优先级建议
- automation: 8（可被提升至 9–10 用于关键事务类）
- campaign: 5（高峰期可临时降至 4）
- manual: 4（小规模即时，可根据容量提升至 6）

## 兼容性与扩展
- 同一编排与回传框架可扩展至短信/推送渠道
- `throttle_key` 与重试策略保持通用抽象，便于跨渠道共用


