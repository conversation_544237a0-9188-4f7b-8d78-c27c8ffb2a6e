# 统一发送流程设计 | Email-System

## 1. 设计目标

### 1.1 核心原则
- **统一执行引擎**：活动、自动化旅程、手动任务使用统一的发送执行引擎
- **模板关联发件账户**：去除发件渠道概念，发件账户信息通过模板关联获取
- **自动化流程集成**：所有发送类型都可接入自动化流程作为执行策略
- **gRPC 调用 Email 模块**：发送时通过 gRPC 调用 Email 模块执行投递

### 1.2 边界调整
- **Email-System**：负责发送策略、受众管理、内容渲染、发送调度
- **Email 模块**：负责模板管理、发件账户管理、实际投递执行

## 2. 统一发送模型

### 2.1 发送任务抽象
所有发送都抽象为 `SendingTask`，包含：
- **任务类型**：campaign（活动）、journey（自动化旅程）、manual（手动任务）
- **模板信息**：template_id（关联发件账户）
- **受众配置**：audience_json（标签选择）
- **发送设置**：locale_strategy、schedule_time、timezone
- **自动化配置**：automation_config（可选接入自动化流程）

### 2.2 执行流程统一
```mermaid
flowchart TD
    A[发送任务创建] --> B[受众解析]
    B --> C[模板渲染]
    C --> D[批次分片]
    D --> E[入队 email_messages]
    E --> F[执行引擎调度]
    F --> G[gRPC 调用 Email 模块]
    G --> H[投递执行]
    H --> I[事件回流]
    I --> J[状态更新]
```

## 3. 数据模型设计

### 3.1 统一发送任务表
```sql
-- 统一发送任务抽象表
CREATE TABLE sending_tasks (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  tenant_id       BIGINT NOT NULL COMMENT '租户ID',
  task_type       VARCHAR(32) NOT NULL COMMENT '任务类型（campaign/journey/manual）',
  source_id       BIGINT NOT NULL COMMENT '源对象ID（campaign_id/journey_id/manual_task_id）',
  name            VARCHAR(128) NOT NULL COMMENT '任务名称',
  template_id     BIGINT NOT NULL COMMENT '模板ID（关联发件账户）',
  audience_json   JSON NOT NULL COMMENT '受众配置',
  settings_json   JSON NOT NULL COMMENT '发送设置',
  locale_strategy VARCHAR(32) NOT NULL DEFAULT 'auto' COMMENT '多语言策略',
  force_locale    VARCHAR(16) NULL COMMENT '强制语言',
  schedule_time   DATETIME NULL COMMENT '计划时间',
  timezone        VARCHAR(64) NULL COMMENT '时区',
  automation_config JSON NULL COMMENT '自动化配置',
  status          VARCHAR(32) NOT NULL DEFAULT 'draft' COMMENT '状态',
  created_by      BIGINT NULL COMMENT '创建人',
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY idx_task_type_source (tenant_id, task_type, source_id),
  KEY idx_task_status (tenant_id, status, schedule_time),
  KEY idx_task_template (tenant_id, template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统一发送任务表';
```

### 3.2 发送批次表
```sql
-- 发送批次表（统一管理）
CREATE TABLE sending_batches (
  id              BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  tenant_id       BIGINT NOT NULL COMMENT '租户ID',
  task_id         BIGINT NOT NULL COMMENT '发送任务ID',
  batch_no        INT NOT NULL COMMENT '批次号',
  size_planned    INT NOT NULL COMMENT '计划发送数量',
  size_sent       INT NOT NULL DEFAULT 0 COMMENT '实际发送数量',
  status          VARCHAR(32) NOT NULL DEFAULT 'pending' COMMENT '状态',
  scheduled_at    DATETIME NULL COMMENT '计划执行时间',
  started_at      DATETIME NULL COMMENT '开始时间',
  finished_at     DATETIME NULL COMMENT '完成时间',
  error_message   VARCHAR(1024) NULL COMMENT '错误信息',
  created_at      DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_batch_task (tenant_id, task_id, batch_no),
  KEY idx_batch_status (tenant_id, status, scheduled_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发送批次表';
```

## 4. 执行引擎设计

### 4.1 统一调度器
- **任务扫描**：定期扫描 sending_tasks 表，识别待执行任务
- **受众解析**：根据 audience_json 解析目标受众
- **批次分片**：按配置将受众分片为多个批次
- **队列入队**：将消息写入 email_messages 表

### 4.2 渲染引擎
- **模板获取**：通过 template_id 获取 Email 模块的模板信息
- **多语言渲染**：基于 template_locales 进行语言匹配和渲染
- **变量替换**：根据联系人信息和设置进行变量替换
- **内容生成**：生成最终的邮件内容

### 4.3 发送执行器
- **消息处理**：从 email_messages 表获取待发送消息
- **gRPC 调用**：调用 Email 模块的发送接口
- **状态管理**：更新发送状态和重试逻辑
- **事件处理**：处理投递事件回流

## 5. 自动化流程集成

### 5.1 自动化策略
- **触发条件**：时间触发、事件触发、条件触发
- **执行策略**：立即执行、延迟执行、条件执行
- **流程控制**：串行、并行、分支、循环

### 5.2 配置示例
```json
{
  "automation_enabled": true,
  "trigger_type": "schedule",
  "trigger_config": {
    "schedule_time": "2024-01-01 10:00:00",
    "timezone": "Asia/Shanghai"
  },
  "execution_strategy": {
    "type": "batch",
    "batch_size": 1000,
    "interval_seconds": 60
  },
  "conditions": [
    {
      "type": "audience_filter",
      "config": {
        "tags": ["active_user"],
        "exclude_tags": ["unsubscribed"]
      }
    }
  ]
}
```

## 6. gRPC 接口设计

### 6.1 发送接口
```protobuf
service EmailSendingService {
  rpc SendEmail(SendEmailRequest) returns (SendEmailResponse);
  rpc BatchSendEmail(BatchSendEmailRequest) returns (BatchSendEmailResponse);
  rpc GetSendingStatus(GetSendingStatusRequest) returns (GetSendingStatusResponse);
  rpc CancelSending(CancelSendingRequest) returns (CancelSendingResponse);
}
```

### 6.2 统计接口
```protobuf
service EmailStatsService {
  rpc GetSendingStats(GetSendingStatsRequest) returns (GetSendingStatsResponse);
  rpc GetDeliveryStats(GetDeliveryStatsRequest) returns (GetDeliveryStatsResponse);
}
```

## 7. 迁移策略

### 7.1 现有数据迁移
- **活动数据**：campaigns 表数据迁移到 sending_tasks
- **旅程数据**：journeys 表关联 sending_tasks
- **渠道数据**：channel_id 映射到 template_id

### 7.2 功能迁移
- **渠道选择**：改为模板选择（模板关联发件账户）
- **发送配置**：从渠道配置迁移到任务配置
- **时间调度**：统一到 sending_tasks 的调度逻辑

## 8. 监控与观测

### 8.1 关键指标
- **任务执行成功率**：按任务类型统计
- **发送吞吐量**：每分钟处理的消息数
- **渲染性能**：模板渲染耗时
- **gRPC 调用性能**：调用 Email 模块的延迟和成功率

### 8.2 告警机制
- **任务执行失败**：连续失败超过阈值
- **队列积压**：email_messages 表积压超过阈值
- **gRPC 调用异常**：调用 Email 模块失败率超过阈值
