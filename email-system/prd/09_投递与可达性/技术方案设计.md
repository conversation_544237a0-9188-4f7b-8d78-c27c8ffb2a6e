# 技术方案设计｜投递与可达性

> 详见技术细节补充文档：《自动验证与预热_技术细节》：`prd/09_投递与可达性/自动验证与预热_技术细节.md`

## HLD
- 组件：渠道管理器、DNS 校验器、预热管理器、限速控制器、FBL/退信接收器、健康看板、邮箱验证器（Verifier）
- 数据：`sending_channels`、`channel_permissions`、`domains`、`throttle_policies`、`reputation_metrics`、`bounces`、`complaints`、`verification_jobs`、`verification_results`

## 时序
```mermaid
sequenceDiagram
participant UI
participant CM as ChannelMgr
participant DNS
participant W as Warming
participant T as Throttle
participant V as Verifier
UI->>CM: 创建渠道
CM->>DNS: 分配域名
UI->>DNS: 校验
UI->>W: 开启预热
UI->>V: 创建验证任务
V-->>UI: 任务状态/结果
T->>MTA: 速率控制
MTA-->>T: 反馈
```

## LLD
- 渠道隔离：域名/IP池/限速策略按渠道隔离；权限控制基于渠道维度。
- 限速令牌桶；ISP 维度路由；反馈触发自动调参。详见《自动验证与预热_技术细节》的并发与限流章节。

---

## 1. 目标与范围
- 目标：到达率≥98%；投诉率≤0.08%；硬退率≤0.6%；DNS 一键校验；预热自动化。邮箱验证高吞吐与高可用。渠道间资源隔离率100%。更多细节参见技术细节文档。
- 范围：发件渠道管理、域名接入/身份校验、预热与限速、反馈与治理、健康看板与告警、邮箱验证。

## 2. 数据与接口
- 表：`sending_channels`、`channel_permissions`、`sending_domains`、`throttle_policies`、`bounce_logs`、`fbl_complaints`、`deliverability_metrics_hourly`、`verification_jobs`、`verification_results`；
- API：POST `/api/channels/create|update|delete|permissions/set`；POST `/api/domains/connect|verify|delete|throttle`；POST `/api/verification/jobs/create|pause|resume`；GET `/api/verification/jobs/list|status|results|download`。

## 3. 规则与算法
- 渠道管理：渠道类型（marketing/transactional/system）、资源隔离、权限控制；
- 校验：SPF/DKIM/DMARC/BIMI；
- 预热：自动曲线、失败降速、域名/IP/ISP 多级速率；
- 验证：Syntax→MX→SMTP 渐进验证；退避算法与熔断。详见技术细节文档 5.x 章节。

## 4. 可靠性
- 渠道故障隔离；校验/预热/验证断点续跑；限速与执行隔离；任务幂等等，配合技术细节文档中的容错与降级策略。

## 5. 安全与合规
- 渠道权限控制；密钥安全存储；域名变更审计与二次确认；验证数据最小化与加密。更多说明见技术细节文档第12节。

## 6. 可观测与性能
- 指标与告警详见技术细节文档第10节，包含渠道维度监控、unknown 占比、域/MX TopN 等。

## 7. 测试与验收
- 渠道隔离测试、多 ISP 场景校验、预热曲线与自动降速、Verification 原因码映射等；细节与用例参见技术细节文档。

## 8. 发布与回滚
- 渠道功能灰度发布；预热策略配置灰度；验证集群滚动发布；限速策略版本化；任务格式前向兼容。

## 9. 风险与对策
- 渠道资源不足、DNS 传播延迟、ISP 反馈缺失、SMTP 探测受限等风险，处置见技术细节文档第9与第11节。

## 10. Checklist
- 渠道隔离验证；统一响应；审计合规；看板告警齐备；验证数据加密；结果导出与抑制联动。

---

## 11. 参考资料与行业最佳实践（已采纳）

### 11.1 Geeksend：邮箱预热技巧与策略（采纳点）
- 来源：`Geeksend｜解锁高效邮箱预热的技巧和策略`（见参考链接）
- 关键建议与我方采纳：
  - 预热阶段：在暂停或低发送期也保持预热；将预热排程在常规发送时间以保持“自然性”。
  - 邮箱账户活动：持续预热可在非营销日维持账户活跃度，稳定发件人声誉。
  - 邮件发送量（单邮箱账户级别的保守上限）：
    - 每邮箱账户每日发送量建议不超过约 50 封（含预热与营销总和，视 ESP/邮箱供应商限制而定）。
    - 预热邮件计入 ESP 的总体发送限制，应纳入配额统筹。
    - 营销:预热的平衡示例：50/50 作为起点；效果良好可提高营销比例；当打开/回复下降时，临时提高预热比例（如 35/15、45/5 等）。
    - 避免突增突降；非营销日用预热量进行“填充”，维持发送曲线平滑。

### 11.2 平台设计落地点
- 配置中心：
  - 增加“单邮箱账户日上限（含预热）”与“营销:预热配比”参数（可按渠道/发件账户级配置）。
  - 将“预热占比动态调节”作为策略开关，允许按互动指标（打开/回复）自动调整比例。
- 调度与节流：
  - 发送编排器识别“非营销日”，自动注入预热任务，保持日度稳定输出（与 ISP/域/IP 级限流并行约束）。
  - 在队列层引入“自然性约束”：限制短时间的急剧增幅。
- 可观测性：
  - 看板新增“营销:预热占比”维度与“单邮箱账户日量热力图”，便于发现异常突变。
  - 告警在比例偏离或单账户日上限逼近/超限时触发。

参考链接：
- [Geeksend｜解锁高效邮箱预热的技巧和策略](https://www.geeksend.com/knowledge/use_email_warm_up_tips_forbest_results.html)

### 11.3 邮箱轮换策略（采纳点）
- 概念：对同一渠道/域名下配置多账户，按“单账户日上限（含预热）”与“营销:预热配比”进行轮换，分散风险与保持自然性。
- 策略：
  - 轮换优先级：优先选择“预热完成且近期互动健康”的账户；对“预热中/历史投诉高”的账户降低权重或仅用于预热流量。
  - 上限约束：每账户每日不超过配置上限（建议参考 50/日/账户起步，结合 ISP 与套餐调整）。
  - 失败回退：若某账户短时 4xx/5xx 异常增多，自动降权并切换下一个健康账户。
- 系统实现：队列调度层增加“发件账户轮换器”；与 ISP/域/IP 限速与预热状态联动。
- 来源参考：见上文 Geeksend 预热技巧与策略。

### 11.4 时间表与“自然性”约束（采纳点）
- 原则：将预热与营销任务安排在常规工作时间窗口；非营销日自动注入预热以保持活跃；避免短时间大幅波动。
- 实现：
  - 发送编排器加入“工作日/时段配置”与“自然性斜率限制（每小时增长上限）”。
  - 非营销日的容量空洞由预热任务按比例填充（遵循 ISP/域/IP/账户多级限流）。
- 来源参考：见上文 Geeksend 预热技巧与策略。

### 11.5 跟踪域名与发信域名 DNS（采纳点）
- 跟踪域名：
  - 支持自定义 tracking domain（CNAME 到平台），建议启用 HTTPS/SSL；避免使用公共追踪域降低收件箱概率。
  - 与 DMARC 对齐无直接冲突，但需确保链接域信誉独立、证书有效。
- 发信域名 DNS：
  - SPF/DKIM/DMARC/BIMI 完整配置；PTR/rDNS 正确；遵循各 ESP 建议 TTL。
- 平台实现：域名接入向导新增 tracking domain 校验与证书上传；DNS 健康检查纳入投递前置检查清单。
- 来源参考：见上文 Geeksend 预热技巧与策略。

### 11.6 退订与合规（采纳点）
- 强制要求营销邮件包含退订链接与 List-Unsubscribe 头；一键退订与偏好中心联动。
- 预热/营销均计入 ESP 发送限制，配额管控需统一计量；告警在接近上限时触发。
- 平台实现：模板渲染校验退订占位符；投递前 Preflight 检查无退订则拒发；抑制名单强制拦截。
- 来源参考：见上文 Geeksend 预热技巧与策略。

### 11.7 参与指标与看板（采纳点）
- 指标：打开、点击、回复、退订、投诉、软/硬退、队列延迟；支持按“营销:预热占比”“账户/域/ISP”切片。
- A/B：支持主题/内容/发送时间小流量实验，选优后全量；预热阶段建议 10% 以内实验流量。
- 平台实现：看板增加“账户日量热力图”与“占比曲线”；异常抖动自动告警。
- 来源参考：见上文 Geeksend 预热技巧与策略。

### 11.8 抑制与名单健康（采纳点）
- 抑制范围：退订、投诉、硬退（域/账户/全租户级）即时入库；预热/营销统一生效。
- 名单健康：对近 90 天无互动用户延迟发送（如 24h），优先高互动分层；结合邮箱验证结果排除 invalid。
- 平台实现：发送选择器支持“排除 risky/unknown”与“延迟层”；与验证模块联动。
- 来源参考：见上文 Geeksend 预热技巧与策略。

### 11.9 连接器矩阵与接入合规（采纳点）
- 支持多种 SMTP/ESP 连接：Gmail、QQ/163、阿里云、Zoho、Amazon SES、Amazon WorkMail、Outlook、Mailgun、SendGrid、Namecheap（Private Email）、Titan、GoDaddy、IONOS、AOL、Hostinger 等。
- 接入要求：
  - 鉴权：优先 OAuth/应用专用密码；安全地保存凭证；最小权限原则。
  - 校验：接入向导对 From/Envelope-From/Reply-To 合法性校验，DMARC 对齐提示。
  - 配额：同步账户级配额（Daily/TPS），与平台限流合并取最小值。
- 平台实现：`渠道管理器`新增通用 SMTP/ESP 连接器与分供应商配置模板；接入向导分步校验与可达性检查。
- 参考链接（目录来源）：[Geeksend 帮助中心 - 账户/连接/域名/DNS/自动化 等文档目录](https://www.geeksend.com/knowledge/use_email_warm_up_tips_forbest_results.html)

### 11.10 免发送列表与“免打扰”策略（采纳点）
- 术语统一：免发送列表 = 抑制名单（suppression list）；支持租户级、域级、账户级分层。
- 策略：
  - 发送期统一校验（预热/营销/事务类均生效）；
  - 提供“永久免打扰”与“活动期免打扰（到期自动恢复）”两种模式；
  - 导入支持 CSV/API；与退订/投诉/硬退回来源自动合并。
- 平台实现：在发送前置 Preflight 阶段进行合并判定；策略与 UI 已在抑制模块体现。
- 参考链接（目录来源）：[Geeksend 帮助中心 - 免发送邮箱列表/退订与合规/自动化](https://www.geeksend.com/knowledge/use_email_warm_up_tips_forbest_results.html)

### 11.11 列表导入与字段映射（采纳点）
- 导入：支持 CSV/URL/API；字段映射与类型校验（邮箱、姓名、公司、标签等）。
- 可达性前置检查：
  - 对导入名单触发邮箱验证（批/在线）；
  - 自动排除 invalid，标注 risky/unknown；
  - 对来源不明或近期无互动名单，建议仅进入延迟层或预热流量。
- 参考链接（目录来源）：[Geeksend 帮助中心 - 列表导入/自定义字段/标签管理/邮箱验证](https://www.geeksend.com/knowledge/use_email_warm_up_tips_forbest_results.html)

### 11.12 发件身份与别名（采纳点）
- From/Envelope-From/Reply-To 支持别名；统一校验显示名与域对齐，避免 DMARC 失配。
- 支持“按活动/渠道设置发件身份模板”，并在活动级覆盖；审计所有改动。
- 参考链接（目录来源）：[Geeksend 帮助中心 - 别名/发信域名/DNS 配置](https://www.geeksend.com/knowledge/use_email_warm_up_tips_forbest_results.html)

### 11.13 BCC 合规与审计（采纳点）
- 可选开启合规 BCC 到内部审计邮箱；对敏感活动（合规/财务类）强制开启；脱敏策略可配置。
- 参考链接（目录来源）：[Geeksend 帮助中心 - BCC/合规模块相关条目](https://www.geeksend.com/knowledge/use_email_warm_up_tips_forbest_results.html)

---

## 12. 限流与配额控制实现（结合数据库表）

### 12.1 目标
- 控制“速率”（QPS/RPM/并发）与“总量”（账户/渠道/域/ISP/租户在 hour/day/week/month/year 窗口内的配额），在分布式环境下保持近似一致，避免明显超额。

### 12.2 相关数据表
- 限速策略：`throttle_policies(tenant_id, channel_id, isp, rate_per_sec, burst, max_connections, thresholds_json, safe_cap_isp)`
- 配额策略：`quota_policies(tenant_id, channel_id, subject_type, subject_key, metric, period_type, limit_value)`
- 用量快照：`usage_counters(tenant_id, channel_id, subject_type, subject_key, metric, period_type, period_start, used_value)`
- 队列与事件：`email_messages`、`email_delivery_events`、`bounce_logs`、`fbl_complaints`
- 账户与域：`connector_accounts(daily_cap, ratio_marketing, health_score, status)`、`sending_domains(warmup_status, reputation_score)`

### 12.3 控制层级
- 速率层：
  - 全局/租户 → `rate_limit_global`（进程内配置）；
  - ISP/域/通道 → `throttle_policies`（令牌桶 + AIMD）；
  - IP/连接 → MTA 连接与批量参数；
- 总量层：
  - 账户/渠道/域/ISP/租户 × 周期（hour/day/...）→ `deliverability_quota_policies` + `deliverability_usage_*`；

### 12.4 算法概述
- 令牌桶（必须）：`rate_per_sec` + `burst` 控制平滑吞吐与瞬时峰值。
- AIMD 自适应（建议）：健康窗口合格 → +10~20%；触发阈值（投诉/退信/deferral）→ ×0.5 降档 + 冷却。
- 阈值回退：结合 `thresholds_json` 与实时指标（deferral/4xx, hard_bounce, complaint）。
- 配额校验：仅基于 `usage_counters.used_value` 快照做发送前校验；不做预留/在途精算。

### 12.5 执行流程（高层时序）
1) Preflight：
   - 读取 `quota_policies` → 计算各维度窗口（period_start）。
   - 读取 `usage_counters.used_value` 快照 → `remaining = limit - used_value`；若不足直接拒绝（不创建任务）。
   - 读取 `throttle_policies` → 初始化/更新令牌桶与并发上限。
2) 入队：
   - 通过配额校验后，创建任务/入队 `email_messages`（不做配额预留）。
3) 发送执行（速率控制）：
   - 进程内令牌桶按 ISP/域/通道发放令牌；队列取件时校验冷却/阈值状态。
4) 刷新（主动触发）：
   - 撤回/取消/回滚/导入补数/人工刷新 → 对目标周期做精确重算，将成功事件计数写回 `usage_counters.used_value`。
5) 自适应调节：
   - 每 1 分钟读取健康窗口指标（`email_delivery_events`, `bounce_logs`, `fbl_complaints` 汇总）更新速率；
   - 触发阈值 → 冷却/降档，并记录 `warmup_events`/运维告警。

### 12.6 关键 SQL 模式（示意）
```sql
-- 1) 计算窗口起点（示意：DAY）
SET @period_start = DATE_FORMAT(UTC_TIMESTAMP(), '%Y-%m-%d 00:00:00');

-- 2) 读取剩余额度（FOR UPDATE）
SELECT used_value, reserved_value
FROM deliverability_usage_counters
WHERE tenant_id=? AND resource_type='connector_account' AND resource_key=?
  AND metric='sent_total' AND period_type='day' AND period_start=@period_start
FOR UPDATE;

-- 3) 写入预留（幂等）与累加 reserved
-- 预留由 Redis 负责，MySQL 仅保持 `usage_counters` 聚合：

INSERT INTO deliverability_usage_counters
  (tenant_id, resource_type, resource_key, metric, period_type, period_start, used_value, reserved_value)
VALUES (?, 'connector_account', ?, 'sent_total', 'day', @period_start, 0, ?)
ON DUPLICATE KEY UPDATE reserved_value = reserved_value + VALUES(reserved_value);

-- 4) 入队成功后：转已用并减少预留
UPDATE deliverability_usage_counters
SET used_value = used_value + ?, reserved_value = reserved_value - ?
WHERE tenant_id=? AND resource_type='connector_account' AND resource_key=?
  AND metric='sent_total' AND period_type='day' AND period_start=@period_start;

UPDATE deliverability_usage_reservations
SET status='consumed'
WHERE tenant_id=? AND resource_type='connector_account' AND resource_key=?
  AND metric='sent_total' AND period_type='day' AND period_start=@period_start AND reservation_key=?;
```

### 12.7 发送时令牌桶与 AIMD（伪代码）
```pseudo
every 1s per ISP/channel:
  tokens += rate_per_sec(isp)
  while tokens >= cost and not cooling(isp):
    msg = pick_next_message(isp)
    if msg is None: break
    send(msg); tokens -= cost

every 1m per ISP:
  health = read_health_window(5-15m)
  if health.complaint > 0.0008 or health.hard_bounce > 0.015 or health.deferral > 0.03:
    rate_per_sec(isp) *= 0.5; cooldown(isp, 5-15m)
  else if stable >= 2 windows:
    rate_per_sec(isp) = min(rate_per_sec(isp) * 1.1, safe_cap_isp)
```

### 12.8 分布式与近似一致
- 预留-入队-消耗模型允许在高并发下出现轻微超额，随后批次通过切片与速率/冷却调节抵消；
- 通过 `reservation_key` 实现幂等重放；通过 `expires_at` 扫描回收“幽灵预留”。

### 12.9 设计取舍与决策（不忘清单）
- 一致性目标：近似一致（不强卡死总量），避免“明显超额”。
- 热路径用 Redis 的原因：
  - 高并发原子授予（Lua）和秒级滑动窗口统计，延迟低、吞吐高；
  - 允许部分授予（grant < requested），剩余流量交由轮换/延后，避免拥塞；
  - TTL 与键过期可简化短期状态管理（窗口内计数、预留明细）。
- 冷路径用 MySQL 的原因：
  - 配额策略与聚合计数持久化（`quota_policies`/`usage_counters`），可报表、可审计、可对账；
  - 与发送事件、投递指标联动，形成治理闭环（阈值、冷却、回退）。
- 移除 DB 预留表（usage_reservations）的原因：
  - 热路径改为 Redis 承担预留/回收；DB 仅保存聚合计数（used/reserved），降低写放大与锁竞争；
  - 预留明细不作为长期审计口径（长期以 `send_records`/事件聚合为准）。
- 部分授予策略：
  - grant = min(requested, remaining_rate, remaining_quota)；
  - grant==0 → 排队或切换其他账户/维度；grant<requested → 将余量拆分为待重试项。
- 速率与配额的关系：
  - 速率（QPS/RPM）控制“瞬时”，配额（hour/day/…）控制“周期总量”；两者同时收敛，取更严格者执行。

### 12.10 刷新机制（规范）
- 不做周期性定时刷新；仅在“主动触发”时重算目标周期（撤回/取消/回滚/导入补数/人工刷新）。
- 重算逻辑以“成功事件”口径聚合：`used_value = COUNT(*) FROM email_delivery_events WHERE event_type IN ('delivered','accepted') AND occurred_at ∈ [period_start, period_end)`。
- 可选：提供“刷新用量”API/后台操作，限流保护；UI 展示“快照更新时间”说明可能非实时。

### 12.11 失败场景与恢复
- Redis 故障/重启：
  - 热计数丢失 → 短期放宽；后台以 DB 聚合与最近发送事件重建近似状态；
  - 恢复后令牌桶按保守速率启动（safe_cap_isp 的 70%）。
- 同步作业失败：
  - 重试队列；下次同步做增量补偿；定期全量对账（小时/天）。
- 明显超配：
  - 触发“冷却+降档”与“配额回退”（下一窗口减少初始预算），并记录 `warmup_events`/运维告警。

### 12.12 实施清单（落地必做）
- Redis Lua：授予脚本（滑窗+部分授予+reserved++）、消耗脚本（reserved→used）、回收脚本（过期预留）。
- 后台同步：Redis→`usage_counters` UPSERT；小时/日对账；偏差纠偏。
- 阈值联动：投诉/硬退/deferral 命中 → 限速器进入冷却；预热期记录 `warmup_events`。
- 可观测性：
  - 看板：速率（滑窗）、配额（remaining/used/reserved）、grant 命中率、部分授予比例；
  - 告警：配额穿透、窗口超配、同步失败、对账偏差超阈值。
