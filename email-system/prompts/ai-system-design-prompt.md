### AI 系统设计提示词：Email-System 多语言模板与边界调整

- 角色：你是一名资深后端/系统架构师 AI，精通 Go、微服务、DDD 与 Clean Architecture。
- 背景：Email 模块拥有模板与版本；Email-System 负责营销域（活动/受众/渠道/分析等），并在本次迭代中仅维护多语言映射表以支撑模板的语言化渲染。发送记录在 Email-System 内通过数据库队列表实现，并通过 gRPC 调用 Email 内核执行投递。

## 目标
- 去除 Email-System 内 `templates`、`template_versions`、`template_approvals` 的维护。
- 新增 多语言映射表 `template_locales`（Email-System 持有），用以维护外部 `template_id` 与 `locale` 的语言化内容。
- email_messages在email模块；Email-System与 Email 内核通过 gRPC 接口交互。发送时将templateId和模版参数传递，调用email系统，并新增统计接口，支持按照Email-System模块的不同统计区间获取数量，支持撤回 取消
- 同步更新 数据库设计、边界与集成设计、模板模块 PRD/技术方案设计、可执行 SQL。

## 约束与规范
- 接口：仅 GET/POST，无路径参数；统一响应结构 { code, message, data, meta }；隐藏系统错误。
- 架构：Clean Architecture + DDD；分层清晰（interfaces/internal/application/domain/infrastructure）；依赖注入构造注入；禁止注入依赖的运行时 nil 检查。
- 数据：MySQL 8.0，utf8mb4，InnoDB；时间 UTC；主键 BIGINT 自增；必要二级索引；不建立外键到外部系统。
- 发送实现：不使用消息队列；使用数据库表 `email_messages` 做发送请求与执行隔离；重试/幂等等通过表字段与锁实现。
- 可观测：全链路 OpenTelemetry Tracing；结构化日志；关键参数入 Span 属性。

## 需要产出
1) 文档更新
- `email-system/prd/数据库设计.md`：
  - 边界声明：模板与版本归属 Email 模块；Email-System 仅维护 `template_locales`。
  - 移除/标注不再维护的 `templates`/`template_versions`/`template_approvals`。
  - 增加 `template_locales` DDL 与字段说明、唯一键与索引。
  - 在发送核心章节，说明正文按模板+语言在发送时渲染，`email_messages` 不落地完整正文。
- `email-system/prd/系统边界与集成设计.md`：
  - 职责矩阵：模板/版本（Email 模块），多语言映射 `template_locales`（Email-System）；发送由 Email 执行。
  - 接口契约：Email-System 通过 gRPC 向 Email 下发发送任务；正文在 Email-System 渲染完成后再调用发送。
- `email-system/prd/06_模板与内容/PRD.md`：
  - 申明 `TemplateLocale` 为 Email-System 内部表，记录外部 `template_id` 与 `locale` 及语言化内容；
  - 预览/渲染基于 `templates + template_locales`；发送通过 gRPC 调用 Email 内核。
- `email-system/prd/06_模板与内容/技术方案设计.md`：
  - 聚合定义（仅语言映射由 Email-System 持有）；渲染/回退策略；迁移策略（如历史 `template_content`）。

2) SQL 输出
- 文件：`email-system/platforms-email-system.sql`
  - 移除（或以注释说明）`templates`/`template_versions`/`template_approvals` 表；
  - 保留/新增：`template_locales` 表（IF NOT EXISTS），含唯一键 `UNIQUE (template_id, locale)` 与 `KEY (template_id, locale)`；
  - 附带迁移注释：若历史存在 `template_content`，示例性 INSERT 迁移到 `template_locales`。

## 关键表设计（Email-System）
- `template_locales`：
  只保留 template_id 和locale关联，Email-System系统在使用多语言的情况下，需要记录template_locales的id字段

- `email_messages`（已存在，保留）：
  - 队列/状态机字段；渲染使用的 `locale_used`、`locale_fallback`；不保存完整正文。
- 去除Email-System中对模版的存储设计，Email-System直接调用email系统来维护模版和变量
## 渲染与回退策略
- 匹配顺序：`contact.preferred_language` → 活动强制语言 → 模板默认语言；
- 变体缺失阻断发布或降级到默认语言（按配置）；
- 记录 `locale_used` 与是否回退。

## 任务分解（具体执行步骤）
1. 更新文档四处（数据库设计/边界与集成/PRD/技术方案），按“需要产出”章节落地。
2. 更新/生成 `platforms-email-system.sql`：仅包含 `template_locales` DDL 与迁移注释；去除模板/版本/审批。
3. 在发送核心文档中，明确 gRPC 交互流程与渲染位置。
4. 完成自查：索引、唯一键、字段注释、命名一致性。

## 验收标准（Checklist）
- [ ] `platforms-email-system.sql` 中不存在 `templates`/`template_versions`/`template_approvals` 的建表语句
- [ ] 存在 `template_locales` 的 DDL，且含 `UNIQUE (template_id, locale)`
- [ ] `数据库设计.md` 模板章节只描述 `template_locales`，并明确模板与版本由 Email 模块维护
- [ ] `系统边界与集成设计.md` 职责矩阵与接口契约已更新为“混合”所有权与 gRPC 调用
- [ ] `06_模板与内容/PRD.md` 说明 `TemplateLocale` 为 Email-System 内部表，渲染在 Email-System 完成
- [ ] 发送核心章节明确 `email_messages` 队列化与不落地正文的设计

## 输出风格
- 变更以“编辑”方式呈现，保持现有缩进与格式；仅改动相关文件与段落。
- SQL 使用 MySQL 方言，包含注释与索引；避免外键。
- 中文为主，必要处给出英文术语。

## 发送的设计
邮件发送时支持 通过 活动/自动化/手动任务触发，需要关联一个模版，模版关联了发件账户，去掉 Email-System目前的发件渠道的设计，允许 活动/手动任务 接入自动化流程，及自动化实际上是一个策略，也可以是一个完整的流程，重新设计下系统的相关设计文档和数据库设计

以上设计有任何不确定场景需要与我核实