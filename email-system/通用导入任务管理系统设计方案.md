# 通用导入任务管理系统设计方案

## 1. 系统概述

### 1.1 设计目标
基于系统现有的导入任务管理架构，设计一个通用的导入任务管理系统，支持多种业务场景：
- **联系人导入**：导入联系人到联系人库
- **一次性邮件发送**：导入邮件列表直接发送，不保存联系人
- **标签成员导入**：导入标签成员
- **列表成员导入**：导入列表成员
- **其他业务场景**：可扩展的业务场景支持

### 1.2 核心特性
- 基于现有 `import_jobs` 表架构，扩展业务场景支持
- 通过场景参数控制数据处理逻辑和存储目标
- 支持多种文件格式（CSV、Excel、JSON等）
- 实现稳定落标，避免瞬时QPS过高
- 提供统一的进度监控和错误处理
- 支持大容量数据导入（百万级）

## 2. 系统架构设计

### 2.1 整体架构
```
用户上传文件 → 文件解析服务 → 数据验证 → 场景路由器 → 业务处理器 → 数据存储
                ↓
            通用导入任务管理 → 根据场景分发到不同业务模块
```

### 2.2 核心组件
1. **通用导入任务管理器**：基于现有 `import_jobs` 表，扩展业务场景支持
2. **场景路由器**：根据场景参数决定数据处理流程和存储目标
3. **业务处理器**：针对不同场景的专门处理逻辑
4. **数据验证引擎**：统一的验证规则和错误处理
5. **分批处理引擎**：控制处理速度和资源使用
6. **进度监控服务**：统一的进度跟踪和状态管理

## 3. 数据库设计

### 3.1 扩展现有导入任务表

#### 3.1.1 扩展 import_jobs 表
```sql
-- 在现有 import_jobs 表基础上添加业务场景字段
ALTER TABLE import_jobs 
ADD COLUMN business_scenario VARCHAR(32) NOT NULL DEFAULT 'contacts' COMMENT '业务场景（contacts/one_time_email/tag_members/list_members/custom）',
ADD COLUMN scenario_config JSON NULL COMMENT '场景特定配置',
ADD COLUMN target_entity_type VARCHAR(32) NULL COMMENT '目标实体类型（contacts/tags/lists/emails）',
ADD COLUMN target_entity_id BIGINT NULL COMMENT '目标实体ID（如标签ID、列表ID）',
ADD COLUMN business_metadata JSON NULL COMMENT '业务元数据';

-- 添加索引
ALTER TABLE import_jobs 
ADD KEY idx_business_scenario (tenant_id, business_scenario),
ADD KEY idx_target_entity (tenant_id, target_entity_type, target_entity_id);
```

#### 3.1.2 新增业务场景配置表
```sql
CREATE TABLE import_scenario_configs (
  id                    BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  tenant_id             BIGINT NOT NULL COMMENT '租户ID',
  scenario_code         VARCHAR(32) NOT NULL COMMENT '场景代码',
  scenario_name         VARCHAR(64) NOT NULL COMMENT '场景名称',
  description           VARCHAR(255) NULL COMMENT '场景描述',
  target_entity_type    VARCHAR(32) NOT NULL COMMENT '目标实体类型',
  required_fields       JSON NOT NULL COMMENT '必填字段列表',
  optional_fields       JSON NULL COMMENT '可选字段列表',
  validation_rules      JSON NULL COMMENT '验证规则配置',
  default_mapping       JSON NULL COMMENT '默认字段映射',
  post_process_config   JSON NULL COMMENT '后处理配置',
  status                VARCHAR(16) NOT NULL DEFAULT 'active' COMMENT '状态（active/inactive）',
  created_by            BIGINT NULL COMMENT '创建人',
  created_at            DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at            DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  UNIQUE KEY uk_scenario_code (tenant_id, scenario_code),
  KEY idx_scenario_status (tenant_id, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导入场景配置表';
```

#### 3.1.3 新增业务场景错误记录表
```sql
CREATE TABLE import_scenario_errors (
  id                    BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  tenant_id             BIGINT NOT NULL COMMENT '租户ID',
  job_id                BIGINT NOT NULL COMMENT '导入任务ID',
  batch_no              INT NOT NULL COMMENT '批次号',
  row_number            BIGINT NOT NULL COMMENT '原始行号',
  raw_data              JSON NOT NULL COMMENT '原始行数据（仅保存失败行）',
  error_message         VARCHAR(1024) NOT NULL COMMENT '错误描述',
  retry_count           INT NOT NULL DEFAULT 0 COMMENT '重试次数',
  created_at            DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at            DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY idx_job_batch (tenant_id, job_id, batch_no),
  KEY idx_job_errors (tenant_id, job_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导入场景错误记录表（仅保存失败行）';
```

### 3.2 场景配置示例

#### 3.2.1 联系人导入场景
```json
{
  "scenario_code": "contacts",
  "scenario_name": "联系人导入",
  "target_entity_type": "contacts",
  "required_fields": ["email"],
  "optional_fields": ["first_name", "last_name", "company", "phone", "country_code", "preferred_language", "timezone", "notes"],
  "validation_rules": {
    "email": {
      "required": true,
      "format": "email",
      "max_length": 255
    },
    "duplicate_check": {
      "enabled": true,
      "scope": "tenant_global"
    }
  },
  "default_mapping": {
    "邮箱": "email",
    "姓名": "first_name",
    "公司": "company"
  },
  "post_process_config": {
    "create_contact": true,
    "update_existing": true,
    "assign_tags": [],
    "add_to_lists": []
  }
}
```

#### 3.2.2 一次性邮件发送场景
```json
{
  "scenario_code": "one_time_email",
  "scenario_name": "一次性邮件发送",
  "target_entity_type": "emails",
  "required_fields": ["email"],
  "optional_fields": ["first_name", "last_name", "company", "custom_fields"],
  "validation_rules": {
    "email": {
      "required": true,
      "format": "email"
    },
    "duplicate_check": {
      "enabled": true,
      "scope": "job_only"
    }
  },
  "default_mapping": {
    "邮箱": "email",
    "姓名": "first_name"
  },
  "post_process_config": {
    "send_email": true,
    "template_id": null,
    "subject": "",
    "from_name": "",
    "from_email": "",
    "track_opens": true,
    "track_clicks": true
  }
}
```

#### 3.2.3 标签成员导入场景
```json
{
  "scenario_code": "tag_members",
  "scenario_name": "标签成员导入",
  "target_entity_type": "tags",
  "required_fields": ["email"],
  "optional_fields": ["first_name", "last_name", "company"],
  "validation_rules": {
    "email": {
      "required": true,
      "format": "email"
    },
    "tag_exists": {
      "required": true
    }
  },
  "default_mapping": {
    "邮箱": "email",
    "姓名": "first_name"
  },
  "post_process_config": {
    "add_to_tag": true,
    "create_contact_if_not_exists": true,
    "update_existing_contact": true
  }
}
```

## 4. 核心业务逻辑设计

### 4.1 场景路由器
```go
type ScenarioRouter struct {
    scenarioHandlers map[string]ScenarioHandler
    errorRecorder    ErrorRecorder
}

// 验证结果结构
type ValidationResult struct {
    RowNumber    int64
    RawData      map[string]interface{}
    Valid        bool
    ErrorMessage string
}

// 处理结果结构
type ProcessResult struct {
    RowNumber    int64
    RawData      map[string]interface{}
    Success      bool
    Data         interface{}
    ErrorMessage string
}

type ScenarioHandler interface {
    ValidateData(data []map[string]interface{}) ([]ValidationResult, []map[string]interface{})
    ProcessData(validData []map[string]interface{}, config map[string]interface{}) []ProcessResult
    StoreData(results []ProcessResult) error
}

type ErrorRecorder interface {
    RecordErrors(jobID int64, batchNo int, errors []ImportError) error
}

type ImportError struct {
    RowNumber    int64
    RawData      map[string]interface{}
    ErrorMessage string
}

func (sr *ScenarioRouter) Route(scenario string, data []map[string]interface{}, config map[string]interface{}) error {
    handler, exists := sr.scenarioHandlers[scenario]
    if !exists {
        return fmt.Errorf("unsupported scenario: %s", scenario)
    }
    
    // 验证数据，返回有效数据和错误记录
    validationResults, validData := handler.ValidateData(data)
    
    // 记录验证错误
    var validationErrors []ImportError
    for _, result := range validationResults {
        if !result.Valid {
            validationErrors = append(validationErrors, ImportError{
                RowNumber:    result.RowNumber,
                RawData:      result.RawData,
                ErrorMessage: result.ErrorMessage,
            })
        }
    }
    
    // 处理有效数据
    processResults := handler.ProcessData(validData, config)
    
    // 记录业务处理错误
    var businessErrors []ImportError
    for _, result := range processResults {
        if !result.Success {
            businessErrors = append(businessErrors, ImportError{
                RowNumber:    result.RowNumber,
                RawData:      result.RawData,
                ErrorMessage: result.ErrorMessage,
            })
        }
    }
    
    // 存储错误记录
    if len(validationErrors) > 0 || len(businessErrors) > 0 {
        allErrors := append(validationErrors, businessErrors...)
        sr.errorRecorder.RecordErrors(config["job_id"].(int64), config["batch_no"].(int), allErrors)
    }
    
    // 存储业务数据
    return handler.StoreData(processResults)
}
```

### 4.2 联系人导入处理器
```go
type ContactsImportHandler struct {
    contactService ContactService
    tagService     TagService
    listService    ListService
}

func (h *ContactsImportHandler) ValidateData(data []map[string]interface{}) ([]ValidationResult, []map[string]interface{}) {
    var validationResults []ValidationResult
    var validData []map[string]interface{}
    
    for i, row := range data {
        result := ValidationResult{
            RowNumber: int64(i + 1),
            RawData:   row,
            Valid:     true,
        }
        
        // 验证必填字段
        if email, ok := row["email"].(string); !ok || email == "" {
            result.Valid = false
            result.ErrorMessage = "邮箱地址不能为空"
            validationResults = append(validationResults, result)
            continue
        }
        
        // 验证邮箱格式
        if !h.isValidEmail(row["email"].(string)) {
            result.Valid = false
            result.ErrorMessage = "邮箱格式不正确"
            validationResults = append(validationResults, result)
            continue
        }
        
        // 验证通过，添加到有效数据
        validData = append(validData, row)
        validationResults = append(validationResults, result)
    }
    
    return validationResults, validData
}

func (h *ContactsImportHandler) ProcessData(validData []map[string]interface{}, config map[string]interface{}) []ProcessResult {
    var results []ProcessResult
    
    for i, row := range validData {
        result := ProcessResult{
            RowNumber: int64(i + 1),
            RawData:   row,
            Success:   true,
        }
        
        // 检查联系人是否存在
        existingContact, err := h.contactService.GetByEmail(row["email"].(string))
        
        if err == nil && existingContact != nil {
            // 更新现有联系人
            if config["update_existing"].(bool) {
                updatedContact, err := h.updateContact(existingContact, row, config)
                if err != nil {
                    result.Success = false
                    result.ErrorMessage = err.Error()
                } else {
                    result.Data = updatedContact
                }
            }
        } else {
            // 创建新联系人
            newContact, err := h.createContact(row, config)
            if err != nil {
                result.Success = false
                result.ErrorMessage = err.Error()
            } else {
                result.Data = newContact
            }
        }
        
        results = append(results, result)
    }
    
    return results
}

func (h *ContactsImportHandler) StoreData(results []ProcessResult) error {
    // 批量保存联系人
    contacts := make([]Contact, 0)
    for _, result := range results {
        if result.Success {
            contacts = append(contacts, result.Data.(Contact))
        }
    }
    
    if len(contacts) > 0 {
        return h.contactService.BatchCreate(contacts)
    }
    
    return nil
}
```

### 4.3 一次性邮件发送处理器
```go
type OneTimeEmailHandler struct {
    emailService EmailService
    templateService TemplateService
}

func (h *OneTimeEmailHandler) ValidateData(data []map[string]interface{}) ([]ValidationResult, []map[string]interface{}) {
    var validationResults []ValidationResult
    var validData []map[string]interface{}
    
    for i, row := range data {
        result := ValidationResult{
            RowNumber: int64(i + 1),
            RawData:   row,
            Valid:     true,
        }
        
        // 验证必填字段
        if email, ok := row["email"].(string); !ok || email == "" {
            result.Valid = false
            result.ErrorMessage = "邮箱地址不能为空"
            validationResults = append(validationResults, result)
            continue
        }
        
        // 验证邮箱格式
        if !h.isValidEmail(row["email"].(string)) {
            result.Valid = false
            result.ErrorMessage = "邮箱格式不正确"
            validationResults = append(validationResults, result)
            continue
        }
        
        // 验证通过，添加到有效数据
        validData = append(validData, row)
        validationResults = append(validationResults, result)
    }
    
    return validationResults, validData
}

func (h *OneTimeEmailHandler) ProcessData(validData []map[string]interface{}, config map[string]interface{}) []ProcessResult {
    var results []ProcessResult
    
    // 获取邮件模板
    template, err := h.templateService.GetByID(config["template_id"].(int64))
    if err != nil {
        return []ProcessResult{{
            RowNumber:    0,
            Success:      false,
            ErrorMessage: "邮件模板不存在",
        }}
    }
    
    for i, row := range validData {
        result := ProcessResult{
            RowNumber: int64(i + 1),
            RawData:   row,
            Success:   true,
        }
        
        // 构建邮件内容
        emailContent, err := h.buildEmailContent(template, row, config)
        if err != nil {
            result.Success = false
            result.ErrorMessage = err.Error()
            results = append(results, result)
            continue
        }
        
        // 创建邮件发送任务
        emailTask, err := h.createEmailTask(emailContent, config)
        if err != nil {
            result.Success = false
            result.ErrorMessage = err.Error()
        } else {
            result.Data = emailTask
        }
        
        results = append(results, result)
    }
    
    return results
}

func (h *OneTimeEmailHandler) StoreData(results []ProcessResult) error {
    // 将邮件任务添加到发送队列
    emailTasks := make([]EmailTask, 0)
    for _, result := range results {
        if result.Success {
            emailTasks = append(emailTasks, result.Data.(EmailTask))
        }
    }
    
    if len(emailTasks) > 0 {
        return h.emailService.BatchAddToQueue(emailTasks)
    }
    
    return nil
}
```

### 4.4 标签成员导入处理器
```go
type TagMembersHandler struct {
    tagService     TagService
    contactService ContactService
}

func (h *TagMembersHandler) ValidateData(data []map[string]interface{}) ([]ValidationResult, []map[string]interface{}) {
    var validationResults []ValidationResult
    var validData []map[string]interface{}
    
    for i, row := range data {
        result := ValidationResult{
            RowNumber: int64(i + 1),
            RawData:   row,
            Valid:     true,
        }
        
        // 验证必填字段
        if email, ok := row["email"].(string); !ok || email == "" {
            result.Valid = false
            result.ErrorMessage = "邮箱地址不能为空"
            validationResults = append(validationResults, result)
            continue
        }
        
        // 验证邮箱格式
        if !h.isValidEmail(row["email"].(string)) {
            result.Valid = false
            result.ErrorMessage = "邮箱格式不正确"
            validationResults = append(validationResults, result)
            continue
        }
        
        // 验证通过，添加到有效数据
        validData = append(validData, row)
        validationResults = append(validationResults, result)
    }
    
    return validationResults, validData
}

func (h *TagMembersHandler) ProcessData(validData []map[string]interface{}, config map[string]interface{}) []ProcessResult {
    var results []ProcessResult
    
    tagID := config["tag_id"].(int64)
    
    for i, row := range validData {
        result := ProcessResult{
            RowNumber: int64(i + 1),
            RawData:   row,
            Success:   true,
        }
        
        email := row["email"].(string)
        
        // 检查联系人是否存在
        contact, err := h.contactService.GetByEmail(email)
        
        if err != nil || contact == nil {
            // 创建新联系人
            if config["create_contact_if_not_exists"].(bool) {
                contact, err = h.createContact(row)
                if err != nil {
                    result.Success = false
                    result.ErrorMessage = err.Error()
                    results = append(results, result)
                    continue
                }
            } else {
                result.Success = false
                result.ErrorMessage = "联系人不存在且不允许创建"
                results = append(results, result)
                continue
            }
        }
        
        // 添加到标签
        err = h.addToTag(contact.ID, tagID)
        if err != nil {
            result.Success = false
            result.ErrorMessage = err.Error()
        } else {
            result.Data = map[string]interface{}{
                "contact_id": contact.ID,
                "tag_id":     tagID,
            }
        }
        
        results = append(results, result)
    }
    
    return results
}

func (h *TagMembersHandler) StoreData(results []ProcessResult) error {
    // 标签成员关系已经在ProcessData中处理完成
    // 这里不需要额外的存储操作
    return nil
}
```

## 5. API接口设计

### 5.1 创建导入任务
**接口**: `POST /api/import/create`

**请求参数**:
```json
{
  "business_scenario": "contacts",
  "file_url": "https://example.com/contacts.csv",
  "file_type": "csv",
  "mapping_config": {
    "mapping": {
      "邮箱": "email",
      "姓名": "first_name",
      "公司": "company"
    }
  },
  "scenario_config": {
    "update_existing": true,
    "assign_tags": [1, 2],
    "add_to_lists": [1]
  },
  "validation_rules": {
    "email_validation": {
      "required": true,
      "format": "email"
    }
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "job_id": 12345,
    "business_scenario": "contacts",
    "status": "queued",
    "estimated_duration": "5-10分钟"
  }
}
```

### 5.2 获取任务状态
**接口**: `GET /api/import/status/{job_id}`

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "job_id": 12345,
    "business_scenario": "contacts",
    "status": "processing",
    "progress": 65,
    "current_phase": "storing_data",
    "total_rows": 10000,
    "valid_rows": 9800,
    "invalid_rows": 150,
    "duplicate_rows": 50,
    "processed_count": 6370,
    "failed_count": 23,
    "estimated_completion": "2024-01-01T12:30:00Z",
    "error_summary": {
      "invalid_email": 100,
      "duplicate_email": 50
    }
  }
}
```

### 5.3 获取场景配置
**接口**: `GET /api/import/scenarios`

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "scenarios": [
      {
        "scenario_code": "contacts",
        "scenario_name": "联系人导入",
        "description": "导入联系人到联系人库",
        "target_entity_type": "contacts",
        "required_fields": ["email"],
        "optional_fields": ["first_name", "last_name", "company"]
      },
      {
        "scenario_code": "one_time_email",
        "scenario_name": "一次性邮件发送",
        "description": "导入邮件列表直接发送",
        "target_entity_type": "emails",
        "required_fields": ["email"],
        "optional_fields": ["first_name", "last_name"]
      }
    ]
  }
}
```

## 6. 性能优化策略

### 6.1 分批处理
- **批次大小**：根据业务场景动态调整批次大小
- **并发控制**：限制并发批次数量，避免资源耗尽
- **速率限制**：实现令牌桶算法，控制处理速度

### 6.2 数据库优化
- **批量操作**：使用批量INSERT和UPDATE减少数据库交互
- **索引优化**：为常用查询字段建立复合索引
- **连接池**：合理配置数据库连接池大小

### 6.3 内存优化
- **流式处理**：大文件采用流式读取，避免内存溢出
- **对象池**：复用验证和处理对象，减少GC压力
- **缓存策略**：缓存场景配置和验证规则

## 7. 错误处理与监控

### 7.1 错误分类
1. **文件错误**：文件格式错误、文件损坏、文件过大
2. **数据错误**：必填字段缺失、格式错误、数据重复
3. **业务错误**：目标实体不存在、权限不足、配额超限
4. **系统错误**：数据库连接失败、网络超时、资源不足

### 7.2 错误处理策略
- **重试机制**：网络错误自动重试，业务错误不重试
- **降级策略**：部分功能失败时，继续处理其他数据
- **错误聚合**：相同类型错误聚合，减少日志量
- **用户通知**：关键错误及时通知用户

### 7.3 监控指标
- **性能指标**：处理速度、成功率、错误率
- **资源指标**：CPU使用率、内存使用率、数据库连接数
- **业务指标**：各场景任务完成率、平均处理时间

## 8. 部署与运维

### 8.1 配置管理
- **场景配置**：支持运行时配置更新，无需重启服务
- **环境配置**：开发、测试、生产环境配置分离
- **配置验证**：启动时验证配置有效性

### 8.2 日志管理
- **结构化日志**：使用JSON格式，便于日志分析
- **日志分级**：DEBUG、INFO、WARN、ERROR、FATAL
- **日志轮转**：按大小和时间自动轮转日志文件

## 9. 扩展性设计

### 9.1 新场景支持
- **插件机制**：通过实现 `ScenarioHandler` 接口添加新场景
- **配置驱动**：通过配置文件定义新场景，无需代码修改
- **热更新**：支持运行时添加新场景配置

### 9.2 业务逻辑扩展
- **验证规则扩展**：支持自定义验证规则
- **后处理扩展**：支持自定义后处理逻辑
- **数据转换扩展**：支持自定义数据转换规则

## 10. 实施计划

### 10.1 第一阶段（2周）
- 扩展现有 `import_jobs` 表结构
- 实现场景路由器核心逻辑
- 开发联系人导入场景处理器

### 10.2 第二阶段（2周）
- 实现一次性邮件发送场景处理器
- 实现标签成员导入场景处理器
- 开发统一的进度监控和错误处理

### 10.3 第三阶段（1周）
- 性能优化和测试
- 文档编写和部署配置
- 用户培训和上线

## 11. 总结

本设计方案基于系统现有的导入任务管理架构，通过以下方式实现通用性：

1. **架构兼容**：基于现有 `import_jobs` 表，最小化对现有系统的影响
2. **场景扩展**：通过场景参数控制处理逻辑，支持多种业务场景
3. **统一管理**：提供统一的进度监控、错误处理和状态管理
4. **性能保障**：实现分批处理和速率控制，确保系统稳定性
5. **扩展性强**：支持新场景的快速添加和业务逻辑的灵活扩展
6. **数据优化**：只保存失败行的错误记录，避免数据量翻倍，提高系统效率

该设计既保持了与现有系统的兼容性，又提供了强大的扩展能力，可以满足各种导入场景的需求。通过简化的错误处理机制，系统能够高效地处理大量数据导入任务，同时保持错误追踪的完整性。
