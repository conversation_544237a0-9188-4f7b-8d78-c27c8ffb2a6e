# Email System 配置文件

server:
  port: 8089
  mode: debug  # debug, release

database:
  driver: mysql
  dsn: "root:12345678@tcp(localhost:3306)/email-system?charset=utf8mb4&parseTime=True&loc=Local"
  max_open_conns: 100
  max_idle_conns: 10
  conn_max_lifetime: 3600  # 秒
  conn_max_idle_time: 600   # 秒

redis:
  addr: "localhost:6379"
  password: ""
  db: 0

log:
  level: info      # debug, info, warn, error
  format: json     # json, text
  output: stdout   # stdout, stderr, file path
  time_format: "2006-01-02T15:04:05.000Z07:00"

otel:
  endpoint: ""     # OpenTelemetry Collector endpoint, 留空则不启用
  service_name: "email-system"

grpc:
  port: 0          # gRPC端口，0表示不启用gRPC服务

registry:
  enabled: false   # 是否启用服务注册
  type: "nacos"    # 注册中心类型
  address: "localhost:8848"

id_generator:
  worker_id: 1
  datacenter_id: 1

email:
  module_endpoint: "http://localhost:8084"  # email模块的API端点
  internal_app_id: 1000                     # 固定的内部应用ID
  timeout: 30                               # 超时时间（秒）

storage:
  type: "local"    # local, s3
  local_path: "./uploads"
  s3:
    region: ""
    bucket: ""
    access_key: ""
    secret_key: ""
    endpoint: ""
  options: {}
