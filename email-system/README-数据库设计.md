# Email System 数据库设计说明

## 概述

Email System 使用 `platforms-email-system.sql` 作为**唯一的数据库设计文件**，该文件包含了MVP版本所需的所有表结构、索引、约束和注释。

## 数据库设计文件

### 主文件
- **文件名**: `platforms-email-system.sql`
- **位置**: 项目根目录
- **用途**: 完整的数据库设计，包含所有表结构
- **状态**: ✅ 当前使用的唯一数据库设计文件

### 已删除的文件
- ~~`migrations/0001_create_tracking_tables.sql`~~ - 已删除，内容已合并到主文件

## 数据库模块结构

`platforms-email-system.sql` 包含以下完整模块：

### 1. 联系人管理模块
- `contacts` - 联系人主表
- 支持多租户、自定义属性、退订管理、同意管理

### 2. 标签管理模块
- `tags` - 标签表
- `contact_tags` - 联系人标签关联表
- 支持规则标签和静态标签

### 3. 人群圈选模块
- `segments` - 人群圈选表
- 支持动态和静态人群圈选

### 4. 模板管理模块
- `template_locales` - 模板本地化表
- 支持多语言模板管理

### 5. 发送管理模块
- `send_plans` - 发送计划表
- `send_batches` - 发送批次表
- `send_records` - 发送记录表
- `audience_snapshots` - 受众快照表

### 6. 追踪分析模块
- `tracking_events` - 追踪事件表
- `analytics_metrics` - 分析指标表
- 支持邮件打开、点击、转化等事件追踪

## 数据库特性

### 1. 多租户支持
- 所有表都包含 `tenant_id` 字段
- 通过租户ID进行数据隔离
- 支持索引优化

### 2. 数据完整性
- 完整的唯一键约束
- 外键关系设计
- 状态字段枚举值

### 3. 性能优化
- 合理的索引策略
- 复合索引设计
- 查询性能优化

### 4. 扩展性
- JSON字段支持自定义属性
- 灵活的规则配置
- 可扩展的事件追踪

## 使用方法

### 1. 创建数据库
```sql
-- 创建数据库
CREATE DATABASE email_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE email_system;

-- 执行SQL文件
SOURCE platforms-email-system.sql;
```

### 2. 开发环境
```bash
# 使用MySQL命令行
mysql -u root -p < platforms-email-system.sql

# 或使用MySQL Workbench等工具导入
```

### 3. 生产环境
```bash
# 备份现有数据
mysqldump -u root -p email_system > backup.sql

# 执行新的数据库结构
mysql -u root -p email_system < platforms-email-system.sql
```

## 注意事项

### 1. 文件唯一性
- **只使用** `platforms-email-system.sql` 作为数据库设计文件
- 不要创建或使用其他SQL文件
- 所有数据库变更都应该更新主文件

### 2. 版本控制
- 数据库设计变更应该提交到版本控制系统
- 保持主文件与代码的同步
- 记录重要的数据库变更

### 3. 数据迁移
- 生产环境的数据迁移需要谨慎处理
- 建议先在测试环境验证
- 保留数据备份

## 表结构概览

| 模块 | 表名 | 主要功能 | 记录数预估 |
|------|------|----------|------------|
| 联系人管理 | contacts | 联系人基本信息 | 100万+ |
| 标签管理 | tags | 标签定义 | 1000+ |
| 人群圈选 | segments | 人群圈选规则 | 100+ |
| 模板管理 | template_locales | 模板本地化 | 1000+ |
| 发送管理 | send_plans | 发送计划 | 1000+ |
| 发送管理 | send_records | 发送记录 | 1000万+ |
| 追踪分析 | tracking_events | 追踪事件 | 1000万+ |
| 追踪分析 | analytics_metrics | 分析指标 | 100万+ |

## 性能考虑

### 1. 分区策略
- `tracking_events` 表建议按月分区
- 历史数据定期归档
- 热点数据缓存

### 2. 索引优化
- 复合索引覆盖常用查询
- 避免过度索引
- 定期分析索引使用情况

### 3. 查询优化
- 使用分页查询避免大量数据返回
- 合理使用筛选条件
- 避免N+1查询问题

## 维护建议

### 1. 定期维护
- 分析表统计信息
- 优化慢查询
- 清理过期数据

### 2. 监控指标
- 表大小增长趋势
- 查询性能指标
- 存储空间使用

### 3. 备份策略
- 定期全量备份
- 增量备份策略
- 异地备份存储

## 总结

`platforms-email-system.sql` 是Email System的完整数据库设计文件，包含了MVP版本所需的所有功能模块。通过统一使用这个文件，可以确保：

1. **设计一致性**: 所有表结构在一个文件中定义
2. **维护便利性**: 避免多个文件造成的混乱
3. **版本同步**: 数据库设计与代码版本保持一致
4. **部署简单**: 只需要执行一个SQL文件即可完成数据库初始化

请确保所有开发、测试和生产环境都使用这个统一的数据库设计文件。
