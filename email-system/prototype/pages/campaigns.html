<div class="toolbar">
  <button class="btn" onclick="showCreateCampaign()">📧 新建活动</button>
  <button class="btn secondary">📝 草稿箱</button>
  <button class="btn secondary">📊 活动报告</button>
  <button class="btn ghost">🧪 A/B 测试</button>
</div>

<!-- 活动列表 -->
<div class="card">
  <div class="mini">📋 活动列表</div>
  <table class="table" id="campaigns-table">
    <thead>
      <tr>
        <th>活动名称</th>
        <th>状态</th>
        <th>目标受众</th>
        <th>发送数量</th>
        <th>送达数量</th>
        <th>打开数量</th>
        <th>点击数量</th>
        <th>创建时间</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <!-- 数据将通过 JavaScript 动态加载 -->
    </tbody>
  </table>
</div>

<!-- 创建活动向导 -->
<div id="create-campaign" class="card" style="display:none;">
  <div class="mini">活动向导</div>
  
  <!-- 步骤1：基础配置 -->
  <div id="step-1" class="campaign-step">
    <div class="grid cols-2">
      <div>
        <label>活动名称 *</label>
        <input id="campaign-name" placeholder="如：双11预热活动" />
        
        <label>发件渠道 *</label>
        <select id="campaign-channel" onchange="loadChannelInfo()">
          <option value="">请选择发件渠道</option>
          <option value="1">营销主渠道 (marketing-main)</option>
          <option value="2">交易通知 (transactional)</option>
          <option value="3">系统告警 (system-alerts)</option>
        </select>
        <div class="hint">选择用于发送此活动的发件渠道，影响域名/IP池/限速策略</div>
        
        <label>模板 *</label>
        <select id="campaign-template" onchange="loadTemplateInfo()">
          <option value="">请选择模板</option>
          <option value="1">简约促销 (支持多语言)</option>
          <option value="2">节日祝福 (支持多语言)</option>
          <option value="3">周报模板 (仅中文)</option>
        </select>
        <div class="hint">选择邮件模板，支持多语言模板将显示语言选项</div>
        
        <div id="multilingual-options" style="display:none;">
          <label>多语言配置</label>
          <div class="card" style="background: var(--panel-2);">
            <div class="row">
              <div><input type="checkbox" checked/> 中文 (zh-CN)</div>
              <div><input type="checkbox" checked/> English (en-US)</div>
              <div><input type="checkbox"/> Español (es-ES)</div>
            </div>
            <div class="hint">系统将根据用户语言偏好自动选择对应版本</div>
          </div>
        </div>
      </div>
      
      <div>
        <label>受众 *</label>
        <select>
          <option value="">请选择受众</option>
          <option>列表：双11活动</option>
          <option>细分：近30天活跃VIP</option>
          <option>细分：新注册用户</option>
        </select>
        
        <div id="channel-info" class="card" style="background: var(--panel-2); display:none;">
          <div class="mini">渠道信息</div>
          <div class="row">
            <div><strong>默认域名：</strong><span id="channel-domain">—</span></div>
            <div><strong>默认IP池：</strong><span id="channel-ip-pool">—</span></div>
            <div><strong>日配额：</strong><span id="channel-quota">—</span></div>
            <div><strong>限速策略：</strong><span id="channel-rate-limit">—</span></div>
          </div>
          
          <div class="divider"></div>
          <div class="mini">时间表配置</div>
          <div id="channel-schedule-info">
            <div class="row">
              <div><strong>时间表类型：</strong><span id="schedule-type">—</span></div>
              <div><strong>发送时间：</strong><span id="schedule-time">—</span></div>
            </div>
            <div id="timezone-info" style="display:none;">
              <div><strong>时区匹配：</strong><span id="timezone-strategy">—</span></div>
              <div><strong>数据来源：</strong><span id="timezone-source">—</span></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="toolbar">
      <button class="btn" onclick="nextStep(3)">下一步：发送配置</button>
      <button class="btn secondary" onclick="nextStep(2)">可选：实验配置</button>
      <button class="btn secondary" onclick="hideCreateCampaign()">取消</button>
    </div>
  </div>
  
  <!-- 步骤2：实验配置 -->
  <div id="step-2" class="campaign-step" style="display:none;">
    <div class="mini">实验配置</div>
    
    <div>
      <label>是否启用A/B测试</label>
      <div>
        <input type="checkbox" id="enable-ab-test" onchange="toggleABTestConfig()"/> 启用A/B测试
        <div class="hint">通过A/B测试优化活动效果</div>
      </div>
    </div>
    
    <div id="ab-test-config" style="display:none;">
      <div class="grid cols-2">
        <div>
          <label>选择实验</label>
          <select id="ab-test-select">
            <option value="">请选择实验</option>
            <option value="1">主题行测试-双11</option>
            <option value="2">发送时间测试-圣诞</option>
            <option value="3">内容块测试-促销</option>
          </select>
          <div class="hint">选择已有的实验策略，或创建新实验</div>
          
          <button class="btn secondary" onclick="showCreateABTest()">创建新实验</button>
        </div>
        
        <div>
          <label>流量分配策略</label>
          <select id="traffic-strategy">
            <option value="equal">平均分配 (50%/50%)</option>
            <option value="weighted">加权分配</option>
            <option value="bayesian">贝叶斯优化</option>
            <option value="manual">手动分配</option>
          </select>
          
          <div id="manual-traffic" style="display:none;">
            <label>手动分配比例</label>
            <div class="row">
              <div>
                <label>变体A</label>
                <input type="number" value="50" min="0" max="100" />%
              </div>
              <div>
                <label>变体B</label>
                <input type="number" value="50" min="0" max="100" />%
              </div>
            </div>
          </div>
          
          <label>实验目标指标</label>
          <select>
            <option value="open_rate">打开率</option>
            <option value="click_rate">点击率</option>
            <option value="conversion_rate">转化率</option>
            <option value="revenue">收入</option>
          </select>
        </div>
      </div>
      
      <div class="divider"></div>
      <div class="mini">实验详情</div>
      <div id="experiment-details" class="card" style="background: var(--panel-2);">
        <div class="row">
          <div><strong>实验类型：</strong><span id="exp-type">—</span></div>
          <div><strong>测试要素：</strong><span id="exp-element">—</span></div>
          <div><strong>样本比例：</strong><span id="exp-sample">—</span></div>
          <div><strong>观察期：</strong><span id="exp-duration">—</span></div>
        </div>
      </div>
    </div>
    
    <div class="toolbar">
      <button class="btn secondary" onclick="prevStep(1)">上一步</button>
      <button class="btn" onclick="nextStep(3)">下一步：发送配置</button>
    </div>
  </div>
  
  <!-- 步骤3：发送配置 -->
  <div id="step-3" class="campaign-step" style="display:none;">
    <div class="mini">发送配置</div>
    
    <div class="grid cols-2">
      <div>
        <label>发送时间</label>
        <select id="send-time-type" onchange="toggleSendTimeConfig()">
          <option value="immediate">立即发送</option>
          <option value="scheduled">定时发送</option>
          <option value="timezone">时区优化</option>
        </select>
        
        <div id="scheduled-time-config" style="display:none;">
          <label>发送时间</label>
          <input type="datetime-local" />
        </div>
        
        <div id="timezone-optimize-config" style="display:none;">
          <label>时区优化策略</label>
          <select>
            <option value="channel-default">使用渠道默认设置</option>
            <option value="custom">自定义时区规则</option>
          </select>
        </div>
        
        <label>节流控制</label>
        <input type="number" value="10000" placeholder="每小时发送限制" />
        <div class="hint">控制发送速率，避免触发限制</div>
      </div>
      
      <div>
        <label>追踪设置</label>
        <div><input type="checkbox" checked/> 打开追踪（像素）</div>
        <div><input type="checkbox" checked/> 点击追踪（链接替换）</div>
        <div><input type="checkbox"/> UTM 参数</div>
        <div><input type="checkbox"/> 转化追踪</div>
        
        <div class="divider"></div>
        <label>重试策略</label>
        <select>
          <option value="exponential">指数退避</option>
          <option value="fixed">固定间隔</option>
          <option value="immediate">立即重试</option>
        </select>
        
        <label>预检设置</label>
        <div><input type="checkbox" checked/> 抑制列表检查</div>
        <div><input type="checkbox" checked/> 域名身份验证</div>
        <div><input type="checkbox" checked/> 额度检查</div>
      </div>
    </div>
    
    <div class="toolbar">
      <button class="btn secondary" onclick="prevStep(2)">上一步</button>
      <button class="btn" onclick="createCampaign()">创建活动</button>
    </div>
  </div>
</div>

<div class="card">
  <div class="mini">活动列表</div>
  <div class="toolbar">
    <input placeholder="搜索活动..." />
    <select><option>全部渠道</option><option>营销主渠道</option><option>交易通知</option><option>系统告警</option></select>
    <select><option>全部状态</option><option>已完成</option><option>进行中</option><option>草稿</option></select>
  </div>
  
  <table class="table">
    <thead>
      <tr>
        <th>名称</th>
        <th>发件渠道</th>
        <th>模板</th>
        <th>受众</th>
        <th>状态</th>
        <th>实验</th>
        <th>打开率</th>
        <th>点击率</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>双11预热-1</td>
        <td><span class="badge marketing">营销主渠道</span></td>
        <td>简约促销 (多语言)</td>
        <td>双11活动</td>
        <td>已完成</td>
        <td><span class="badge ab-test">主题行测试</span></td>
        <td>38.2%</td>
        <td>6.1%</td>
        <td>
          <button class="btn mini">查看</button>
          <button class="btn mini secondary">复制</button>
        </td>
      </tr>
      <tr>
        <td>圣诞促销</td>
        <td><span class="badge marketing">营销主渠道</span></td>
        <td>节日祝福 (多语言)</td>
        <td>VIP 用户</td>
        <td>进行中</td>
        <td><span class="badge ab-test">时间测试</span></td>
        <td>—</td>
        <td>—</td>
        <td>
          <button class="btn mini">查看</button>
          <button class="btn mini secondary">暂停</button>
        </td>
      </tr>
      <tr>
        <td>订单确认</td>
        <td><span class="badge transactional">交易通知</span></td>
        <td>简约促销 (中文)</td>
        <td>新订单用户</td>
        <td>已完成</td>
        <td>—</td>
        <td>45.8%</td>
        <td>12.3%</td>
        <td>
          <button class="btn mini">查看</button>
          <button class="btn mini secondary">复制</button>
        </td>
      </tr>
      <tr>
        <td>系统维护通知</td>
        <td><span class="badge system">系统告警</span></td>
        <td>周报模板 (中文)</td>
        <td>管理员</td>
        <td>已完成</td>
        <td>—</td>
        <td>78.9%</td>
        <td>23.1%</td>
        <td>
          <button class="btn mini">查看</button>
          <button class="btn mini secondary">复制</button>
        </td>
      </tr>
    </tbody>
  </table>
</div>

<!-- 创建A/B测试弹窗 -->
<div id="create-ab-test" class="card" style="display:none; max-width:900px; position:fixed; top:5%; left:50%; transform:translateX(-50%); z-index:1000;">
  <div class="mini">创建A/B测试策略</div>
  
  <div class="grid cols-2">
    <div>
      <label>实验名称 *</label>
      <input placeholder="如：主题行测试-双11" />
      
      <label>实验类型 *</label>
      <select id="ab-test-type" onchange="loadTestVariants()">
        <option value="">请选择类型</option>
        <option value="subject">主题行测试</option>
        <option value="sender">发件人名称</option>
        <option value="send-time">发送时间</option>
        <option value="content">内容块</option>
        <option value="template">模板版本</option>
      </select>
      
      <label>测试要素</label>
      <div id="test-variants">
        <div class="card" style="background: var(--panel-2);">
          <div class="mini">变体配置</div>
          <div id="variant-list">
            <!-- 动态生成变体配置 -->
          </div>
          <button class="btn mini" onclick="addVariant()">添加变体</button>
        </div>
      </div>
    </div>
    
    <div>
      <label>样本配置</label>
      <div class="row">
        <div>
          <label>总样本比例</label>
          <input type="number" value="20" min="5" max="100" />%
        </div>
        <div>
          <label>变体数量</label>
          <input type="number" value="2" min="2" max="5" />
        </div>
      </div>
      
      <label>流量分配</label>
      <select>
        <option value="equal">平均分配</option>
        <option value="weighted">加权分配</option>
        <option value="bayesian">贝叶斯优化</option>
        <option value="manual">手动分配</option>
      </select>
      
      <label>胜出指标</label>
      <select>
        <option value="open_rate">打开率</option>
        <option value="click_rate">点击率</option>
        <option value="conversion_rate">转化率</option>
        <option value="revenue">收入</option>
      </select>
      
      <label>观察期</label>
      <input type="number" value="6" /> 小时
      
      <label>优胜策略</label>
      <select>
        <option value="auto">观察期结束自动选择</option>
        <option value="significance">达到显著性提前胜出</option>
        <option value="manual">仅记录不自动发送</option>
      </select>
    </div>
  </div>
  
  <div class="toolbar">
    <button class="btn" onclick="createABTest()">创建实验</button>
    <button class="btn secondary" onclick="hideCreateABTest()">取消</button>
  </div>
</div>

<style>
.badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.badge.marketing {
  background: #e3f2fd;
  color: #1976d2;
}

.badge.transactional {
  background: #e8f5e8;
  color: #388e3c;
}

.badge.system {
  background: #fff3e0;
  color: #f57c00;
}

.badge.ab-test {
  background: #f3e5f5;
  color: #7b1fa2;
}

.campaign-step {
  margin-bottom: 20px;
}
</style>

<script>
function showCreateCampaign() {
  document.getElementById('create-campaign').style.display = 'block';
}

function hideCreateCampaign() {
  document.getElementById('create-campaign').style.display = 'none';
}

function nextStep(step) {
  // 隐藏当前步骤
  const currentStep = document.querySelector('.campaign-step:not([style*="display: none"])');
  if (currentStep) {
    currentStep.style.display = 'none';
  }
  
  // 显示下一步
  document.getElementById('step-' + step).style.display = 'block';
}

function prevStep(step) {
  // 隐藏当前步骤
  const currentStep = document.querySelector('.campaign-step:not([style*="display: none"])');
  if (currentStep) {
    currentStep.style.display = 'none';
  }
  
  // 显示上一步
  document.getElementById('step-' + step).style.display = 'block';
}

function loadChannelInfo() {
  const channelId = document.getElementById('campaign-channel').value;
  const channelInfo = document.getElementById('channel-info');
  
  if (channelId) {
    // 模拟加载渠道信息
    const channelData = {
      '1': {
        domain: 'marketing.example.com',
        ipPool: 'Pool-Marketing',
        quota: '50,000 (已用 32,450)',
        rateLimit: '标准策略',
        scheduleType: '时区匹配',
        scheduleTime: '10:00',
        timezoneStrategy: '用户所在时区',
        timezoneSource: '用户注册时区, IP地理位置'
      },
      '2': {
        domain: 'notify.example.com',
        ipPool: 'Pool-Transactional',
        quota: '10,000 (已用 8,200)',
        rateLimit: '保守策略',
        scheduleType: '固定时间',
        scheduleTime: '09:00',
        timezoneStrategy: '—',
        timezoneSource: '—'
      },
      '3': {
        domain: 'alerts.example.com',
        ipPool: 'Pool-System',
        quota: '1,000 (已用 150)',
        rateLimit: '激进策略',
        scheduleType: '立即发送',
        scheduleTime: '—',
        timezoneStrategy: '—',
        timezoneSource: '—'
      }
    };
    
    const data = channelData[channelId];
    document.getElementById('channel-domain').textContent = data.domain;
    document.getElementById('channel-ip-pool').textContent = data.ipPool;
    document.getElementById('channel-quota').textContent = data.quota;
    document.getElementById('channel-rate-limit').textContent = data.rateLimit;
    document.getElementById('schedule-type').textContent = data.scheduleType;
    document.getElementById('schedule-time').textContent = data.scheduleTime;
    
    const timezoneInfo = document.getElementById('timezone-info');
    if (data.scheduleType === '时区匹配') {
      document.getElementById('timezone-strategy').textContent = data.timezoneStrategy;
      document.getElementById('timezone-source').textContent = data.timezoneSource;
      timezoneInfo.style.display = 'block';
    } else {
      timezoneInfo.style.display = 'none';
    }
    
    channelInfo.style.display = 'block';
  } else {
    channelInfo.style.display = 'none';
  }
}

function loadTemplateInfo() {
  const templateId = document.getElementById('campaign-template').value;
  const multilingualOptions = document.getElementById('multilingual-options');
  
  if (templateId === '1' || templateId === '2') {
    multilingualOptions.style.display = 'block';
  } else {
    multilingualOptions.style.display = 'none';
  }
}

function toggleABTestConfig() {
  const enableABTest = document.getElementById('enable-ab-test').checked;
  const abTestConfig = document.getElementById('ab-test-config');
  
  if (enableABTest) {
    abTestConfig.style.display = 'block';
  } else {
    abTestConfig.style.display = 'none';
  }
}

function toggleSendTimeConfig() {
  const sendTimeType = document.getElementById('send-time-type').value;
  const scheduledConfig = document.getElementById('scheduled-time-config');
  const timezoneConfig = document.getElementById('timezone-optimize-config');
  
  scheduledConfig.style.display = 'none';
  timezoneConfig.style.display = 'none';
  
  if (sendTimeType === 'scheduled') {
    scheduledConfig.style.display = 'block';
  } else if (sendTimeType === 'timezone') {
    timezoneConfig.style.display = 'block';
  }
}

function showCreateABTest() {
  document.getElementById('create-ab-test').style.display = 'block';
}

function hideCreateABTest() {
  document.getElementById('create-ab-test').style.display = 'none';
}

function loadTestVariants() {
  const testType = document.getElementById('ab-test-type').value;
  const variantList = document.getElementById('variant-list');
  
  // 清空现有变体
  variantList.innerHTML = '';
  
  if (testType) {
    // 根据测试类型生成默认变体
    const variants = getDefaultVariants(testType);
    variants.forEach((variant, index) => {
      addVariant(variant);
    });
  }
}

function getDefaultVariants(testType) {
  const variants = {
    'subject': [
      { name: '变体A', value: '🎉 双11预热，限时优惠等你来！' },
      { name: '变体B', value: '🔥 双11大促即将开始，抢先一步！' }
    ],
    'sender': [
      { name: '变体A', value: 'Marketing Team' },
      { name: '变体B', value: 'Customer Success' }
    ],
    'send-time': [
      { name: '变体A', value: '09:00' },
      { name: '变体B', value: '14:00' }
    ],
    'content': [
      { name: '变体A', value: '简约版本' },
      { name: '变体B', value: '详细版本' }
    ],
    'template': [
      { name: '变体A', value: '模板A' },
      { name: '变体B', value: '模板B' }
    ]
  };
  
  return variants[testType] || [];
}

function addVariant(defaultVariant = null) {
  const variantList = document.getElementById('variant-list');
  const variantIndex = variantList.children.length;
  
  const variantDiv = document.createElement('div');
  variantDiv.className = 'row';
  variantDiv.style.marginBottom = '10px';
  
  const variantName = defaultVariant ? defaultVariant.name : `变体${String.fromCharCode(65 + variantIndex)}`;
  const variantValue = defaultVariant ? defaultVariant.value : '';
  
  variantDiv.innerHTML = `
    <div style="flex: 1;">
      <input placeholder="变体名称" value="${variantName}" />
    </div>
    <div style="flex: 2;">
      <input placeholder="变体内容" value="${variantValue}" />
    </div>
    <div>
      <button class="btn mini secondary" onclick="removeVariant(this)">删除</button>
    </div>
  `;
  
  variantList.appendChild(variantDiv);
}

function removeVariant(button) {
  button.parentElement.parentElement.remove();
}

function createABTest() {
  // 创建A/B测试逻辑
  alert('A/B测试策略创建成功！');
  hideCreateABTest();
}

function createCampaign() {
  // 创建活动逻辑
  alert('活动创建成功！');
  hideCreateCampaign();
}
</script>


