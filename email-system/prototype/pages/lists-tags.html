<!-- 工具栏 -->
<div class="toolbar">
  <button class="btn" onclick="createNewTag()">🏷️ 新建标签</button>
  <button class="btn secondary">📊 统计报告</button>
  <button class="btn ghost">🔄 同步数据</button>
</div>

<!-- 列表功能已移除，保留标签管理 -->

<!-- 标签管理 -->
<div class="card">
  <div class="mini">🏷️ 标签管理</div>
  <div class="grid cols-4" id="tags-grid">
    <!-- 数据将通过 JavaScript 动态加载 -->
  </div>
</div>

<!-- 新建标签弹窗 -->
<div class="modal" id="dlg-new-tag" style="display:none">
  <div class="dialog">
    <div class="header">
      <div>新建标签</div>
      <button class="btn ghost" data-close="dlg-new-tag">关闭</button>
    </div>
    <div class="body">
      <div class="row">
        <div>
          <label>标签名称</label>
          <input id="tag-name" placeholder="例如：vip-2024-q4" />
          <div class="hint">命名规范：小写-中划线、1-40 字，禁止表情。</div>
        </div>
        <div>
          <label>颜色（可选）</label>
          <select id="tag-color">
            <option value="default">默认</option>
            <option value="blue">蓝色</option>
            <option value="green">绿色</option>
            <option value="orange">橙色</option>
            <option value="red">红色</option>
          </select>
        </div>
      </div>

      <div class="row">
        <div>
          <label>类型</label>
          <select id="tag-type">
            <option value="static">静态</option>
            <option value="dynamic">动态</option>
          </select>
        </div>
        <div id="dynamic-estimate" style="display:none">
          <label>预估人数</label>
          <div data-role="est-count" class="pill">-</div>
        </div>
      </div>

      <div id="static-section">
        <label>静态标签：是否立即圈选人员</label>
        <div class="row">
          <div class="pill">
            <input type="radio" id="static-no-select" name="static-pick" value="none" checked>
            <label for="static-no-select">不圈选（稍后批量打标）</label>
          </div>
          <div class="pill">
            <input type="radio" id="static-use-rules" name="static-pick" value="rules">
            <label for="static-use-rules">使用条件圈选并立即打标</label>
          </div>
        </div>
      </div>

      <div id="dyn-section" style="display:none">
        <div class="tabs">
          <button class="tab active" data-tab-group="tag" data-tab-target="#panel-rules">条件</button>
          <button class="tab" data-tab-group="tag" data-tab-target="#panel-preview">预估与示例</button>
        </div>
        <div id="panel-rules" data-tab-panel="tag">
          <div id="cond-builder">
            <div class="row">
              <div style="max-width:220px">
                <label>组合逻辑</label>
                <select data-role="seg-combinator">
                  <option value="AND">AND（且）</option>
                  <option value="OR">OR（或）</option>
                </select>
              </div>
              <div class="hint">用于多条条件之间的组合方式，可在保存后由系统编译为规则树。</div>
            </div>
            <div data-role="seg-rows"></div>
            <div class="row">
              <div class="flex-0">
                <button class="btn secondary" data-action="add-seg-row">添加条件</button>
              </div>
              <div class="flex-0">
                <button class="btn secondary" data-action="estimate">预估</button>
              </div>
            </div>
            <div class="hint">
              条件建议：
              1) 行为：最近7天有购买（事件窗口计数≥1）；
              2) 互动：近30天打开≥1或点击≥1；
              3) 属性：地区=CN，订阅状态=active；
              4) 关系：包含标签/列表。
            </div>
          </div>
        </div>
        <div id="panel-preview" data-tab-panel="tag" style="display:none">
          <div class="card ghost">
            <div class="row"><div class="pill">预估人数</div><div data-role="est-count" class="pill ok">12,345 人</div></div>
            <div class="divider"></div>
            <table class="table">
              <thead><tr><th>邮箱</th><th>姓名</th><th>最近购买</th></tr></thead>
              <tbody>
                <tr><td><EMAIL></td><td>张三</td><td>3 天前</td></tr>
                <tr><td><EMAIL></td><td>李四</td><td>未购买</td></tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- 静态圈选时复用条件构建器（隐藏/显示控制） -->
      <div id="static-rules-wrapper" style="display:none">
        <div class="divider"></div>
        <label>条件圈选</label>
        <div class="row">
          <div style="max-width:220px">
            <label>组合逻辑</label>
            <select data-role="seg-combinator">
              <option value="AND">AND（且）</option>
              <option value="OR">OR（或）</option>
            </select>
          </div>
        </div>
        <div data-role="seg-rows"></div>
        <div class="row">
          <div class="flex-0"><button class="btn secondary" data-action="add-seg-row">添加条件</button></div>
          <div class="flex-0"><button class="btn secondary" data-action="estimate">预估</button></div>
          <div class="flex-0"><span data-role="est-count" class="pill">-</span></div>
        </div>
      </div>
    </div>
    <div class="footer">
      <button class="btn" data-close="dlg-new-tag">保存</button>
      <button class="btn secondary" data-close="dlg-new-tag">取消</button>
    </div>
  </div>
</div>


