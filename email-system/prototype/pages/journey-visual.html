<!-- 自动化旅程可视化设计页面 -->
<div class="toolbar">
  <button class="btn" onclick="saveJourney()">💾 保存旅程</button>
  <button class="btn secondary" onclick="testJourney()">🧪 测试流程</button>
  <button class="btn secondary" onclick="publishJourney()">🚀 发布旅程</button>
  <button class="btn ghost" onclick="backToList()">← 返回列表</button>
</div>

<!-- 进度条 -->
<div class="progress-bar">
  <div class="progress-step active">序列邮件</div>
  <div class="progress-step">潜在客户</div>
  <div class="progress-step">发送选项</div>
  <div class="progress-step">审核</div>
</div>

<!-- 主设计区域 -->
<div class="journey-canvas-container">
  <!-- 左侧工具栏 -->
  <div class="canvas-toolbar">
    <div class="toolbar-section">
      <div class="section-title">节点类型</div>
      <div class="node-type" draggable="true" data-type="trigger" onclick="addNode('trigger')">
        <div class="node-icon">🔴</div>
        <div class="node-label">触发器</div>
      </div>
      <div class="node-type" draggable="true" data-type="email" onclick="addNode('email')">
        <div class="node-icon">📧</div>
        <div class="node-label">电子邮件</div>
      </div>
      <div class="node-type" draggable="true" data-type="delay" onclick="addNode('delay')">
        <div class="node-icon">⏰</div>
        <div class="node-label">延迟</div>
      </div>
      <div class="node-type" draggable="true" data-type="condition" onclick="addNode('condition')">
        <div class="node-icon">❓</div>
        <div class="node-label">条件</div>
      </div>
      <div class="node-type" draggable="true" data-type="goal" onclick="addNode('goal')">
        <div class="node-icon">🎯</div>
        <div class="node-label">目标</div>
      </div>
    </div>
    
    <div class="toolbar-section">
      <div class="section-title">画布控制</div>
      <div class="canvas-controls">
        <button class="btn mini" onclick="zoomIn()">+</button>
        <div class="zoom-level">80%</div>
        <button class="btn mini" onclick="zoomOut()">-</button>
      </div>
      <button class="btn mini" onclick="centerCanvas()">居中</button>
      <button class="btn mini" onclick="fitToScreen()">适应屏幕</button>
    </div>
  </div>
  
  <!-- 主画布区域 -->
  <div class="canvas-main">
    <div id="journey-canvas" class="journey-canvas">
      <!-- 开始节点 -->
      <div class="flow-node start-node" data-node-id="start" data-node-type="start">
        <div class="node-header start-header">
          <div class="node-icon">🟢</div>
          <div class="node-title">开始</div>
        </div>
        <div class="node-content">
          <div class="node-description">旅程开始点</div>
        </div>
        <div class="node-connector bottom" data-connector="start"></div>
      </div>
      
      <!-- 示例流程节点 -->
      <div class="flow-node email-node" data-node-id="email1" data-node-type="email" style="top: 120px; left: 200px;">
        <div class="node-header email-header">
          <div class="node-icon">📧</div>
          <div class="node-title">电子邮件</div>
        </div>
        <div class="node-content">
          <div class="node-description">11111</div>
        </div>
        <div class="node-connector top" data-connector="email1-top"></div>
        <div class="node-connector bottom" data-connector="email1-bottom"></div>
        <div class="node-actions">
          <button class="btn mini" onclick="editNode('email1')">编辑</button>
          <button class="btn mini secondary" onclick="deleteNode('email1')">删除</button>
        </div>
      </div>
      
      <div class="flow-node delay-node" data-node-id="delay1" data-node-type="delay" style="top: 240px; left: 200px;">
        <div class="node-header delay-header">
          <div class="node-icon">⏰</div>
          <div class="node-title">延迟</div>
        </div>
        <div class="node-content">
          <div class="node-description">等待1小时</div>
        </div>
        <div class="node-connector top" data-connector="delay1-top"></div>
        <div class="node-connector bottom" data-connector="delay1-bottom"></div>
        <div class="node-actions">
          <button class="btn mini" onclick="editNode('delay1')">编辑</button>
          <button class="btn mini secondary" onclick="deleteNode('delay1')">删除</button>
        </div>
      </div>
      
      <div class="flow-node email-node" data-node-id="email2" data-node-type="email" style="top: 360px; left: 200px;">
        <div class="node-header email-header">
          <div class="node-icon">📧</div>
          <div class="node-title">电子邮件</div>
        </div>
        <div class="node-content">
          <div class="node-description">22222</div>
        </div>
        <div class="node-connector top" data-connector="email2-top"></div>
        <div class="node-connector bottom" data-connector="email2-bottom"></div>
        <div class="node-actions">
          <button class="btn mini" onclick="editNode('email2')">编辑</button>
          <button class="btn mini secondary" onclick="deleteNode('email2')">删除</button>
        </div>
      </div>
      
      <div class="flow-node condition-node" data-node-id="condition1" data-node-type="condition" style="top: 480px; left: 200px;">
        <div class="node-header condition-header">
          <div class="node-icon">❓</div>
          <div class="node-title">触发器</div>
        </div>
        <div class="node-content">
          <div class="node-description">打开链接</div>
          <div class="node-description">等待1小时</div>
        </div>
        <div class="node-connector top" data-connector="condition1-top"></div>
        <div class="node-connector left" data-connector="condition1-left"></div>
        <div class="node-connector right" data-connector="condition1-right"></div>
        <div class="node-actions">
          <button class="btn mini" onclick="editNode('condition1')">编辑</button>
          <button class="btn mini secondary" onclick="deleteNode('condition1')">删除</button>
        </div>
      </div>
      
      <div class="flow-node email-node" data-node-id="email3" data-node-type="email" style="top: 600px; left: 100px;">
        <div class="node-header email-header">
          <div class="node-icon">📧</div>
          <div class="node-title">电子邮件</div>
        </div>
        <div class="node-content">
          <div class="node-description">33333</div>
        </div>
        <div class="node-connector top" data-connector="email3-top"></div>
        <div class="node-actions">
          <button class="btn mini" onclick="editNode('email3')">编辑</button>
          <button class="btn mini secondary" onclick="deleteNode('email3')">删除</button>
        </div>
      </div>
      
      <div class="flow-node email-node" data-node-id="email4" data-node-type="email" style="top: 600px; left: 300px;">
        <div class="node-header email-header">
          <div class="node-icon">📧</div>
          <div class="node-title">电子邮件</div>
        </div>
        <div class="node-content">
          <div class="node-description">44444</div>
        </div>
        <div class="node-connector top" data-connector="email4-top"></div>
        <div class="node-actions">
          <button class="btn mini" onclick="editNode('email4')">编辑</button>
          <button class="btn mini secondary" onclick="deleteNode('email4')">删除</button>
        </div>
      </div>
      
      <!-- 连接线 -->
      <svg class="connection-lines" width="100%" height="100%">
        <defs>
          <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
          </marker>
        </defs>
        <path d="M 200 100 L 200 120" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
        <path d="M 200 200 L 200 240" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
        <path d="M 200 320 L 200 360" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
        <path d="M 200 440 L 200 480" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
        <path d="M 150 480 L 100 600" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
        <path d="M 250 480 L 300 600" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
      </svg>
    </div>
  </div>
  
  <!-- 右侧属性面板 -->
  <div class="properties-panel">
    <div class="panel-header">
      <div class="panel-title">属性配置</div>
      <button class="btn mini" onclick="closeProperties()">×</button>
    </div>
    
    <div id="properties-content" class="panel-content">
      <div class="no-selection">
        <div class="hint">选择一个节点来配置其属性</div>
      </div>
    </div>
  </div>
</div>

<!-- 节点编辑弹窗 -->
<div id="node-edit-modal" class="modal" style="display: none;">
  <div class="modal-content">
    <div class="modal-header">
      <div class="modal-title">编辑节点</div>
      <button class="btn mini" onclick="closeNodeEdit()">×</button>
    </div>
    
    <div class="modal-body">
      <div id="node-edit-form">
        <!-- 动态表单内容 -->
      </div>
    </div>
    
    <div class="modal-footer">
      <button class="btn" onclick="saveNodeEdit()">保存</button>
      <button class="btn secondary" onclick="closeNodeEdit()">取消</button>
    </div>
  </div>
</div>

<style>
.journey-canvas-container {
  display: flex;
  height: calc(100vh - 200px);
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.canvas-toolbar {
  width: 200px;
  background: white;
  border-right: 1px solid #e9ecef;
  padding: 16px;
  overflow-y: auto;
}

.toolbar-section {
  margin-bottom: 24px;
}

.section-title {
  font-weight: 600;
  color: #495057;
  margin-bottom: 12px;
  font-size: 14px;
}

.node-type {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.node-type:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.node-type.dragging {
  opacity: 0.5;
}

.node-icon {
  font-size: 20px;
  margin-right: 12px;
}

.node-label {
  font-size: 14px;
  color: #495057;
}

.canvas-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.zoom-level {
  font-size: 12px;
  color: #6c757d;
  min-width: 40px;
  text-align: center;
}

.canvas-main {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.journey-canvas {
  width: 100%;
  height: 100%;
  position: relative;
  background: #f8f9fa;
  background-image: 
    radial-gradient(circle, #e9ecef 1px, transparent 1px);
  background-size: 20px 20px;
}

.flow-node {
  position: absolute;
  width: 200px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  cursor: move;
  user-select: none;
  z-index: 10;
}

.flow-node:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.flow-node.selected {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.node-header {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #e9ecef;
  border-radius: 6px 6px 0 0;
}

.start-header {
  background: #e8f5e8;
  border-color: #28a745;
}

.email-header {
  background: #e3f2fd;
  border-color: #007bff;
}

.delay-header {
  background: #fff3e0;
  border-color: #fd7e14;
}

.condition-header {
  background: #fce4ec;
  border-color: #e91e63;
}

.node-icon {
  font-size: 16px;
  margin-right: 8px;
}

.node-title {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.node-content {
  padding: 12px;
}

.node-description {
  font-size: 13px;
  color: #6c757d;
  margin-bottom: 4px;
}

.node-connector {
  position: absolute;
  width: 12px;
  height: 12px;
  background: #007bff;
  border: 2px solid white;
  border-radius: 50%;
  cursor: pointer;
}

.node-connector.top {
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.node-connector.bottom {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.node-connector.left {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.node-connector.right {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.node-actions {
  position: absolute;
  top: -8px;
  right: -8px;
  display: none;
  gap: 4px;
}

.flow-node:hover .node-actions {
  display: flex;
}

.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 5;
}

.properties-panel {
  width: 300px;
  background: white;
  border-left: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
}

.panel-title {
  font-weight: 600;
  color: #495057;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.no-selection {
  text-align: center;
  color: #6c757d;
  padding: 40px 20px;
}

.progress-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  padding: 20px;
  background: white;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.progress-step {
  display: flex;
  align-items: center;
  color: #6c757d;
  font-size: 14px;
  position: relative;
}

.progress-step.active {
  color: #007bff;
  font-weight: 600;
}

.progress-step:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -20px;
  top: 50%;
  width: 20px;
  height: 2px;
  background: #e9ecef;
  transform: translateY(-50%);
}

.progress-step.active:not(:last-child)::after {
  background: #007bff;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.modal-title {
  font-weight: 600;
  font-size: 18px;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  max-height: 60vh;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e9ecef;
}

/* 拖拽状态 */
.dragging {
  opacity: 0.5;
}

.drag-over {
  border-color: #007bff;
  background: rgba(0,123,255,0.05);
}
</style>

<script>
let selectedNode = null;
let isDragging = false;
let dragOffset = { x: 0, y: 0 };
let canvasZoom = 0.8;

// 初始化画布
document.addEventListener('DOMContentLoaded', function() {
  initializeCanvas();
  initializeDragAndDrop();
});

function initializeCanvas() {
  const canvas = document.getElementById('journey-canvas');
  
  // 画布点击事件
  canvas.addEventListener('click', function(e) {
    if (e.target === canvas) {
      deselectAllNodes();
    }
  });
}

function initializeDragAndDrop() {
  // 节点拖拽
  const nodes = document.querySelectorAll('.flow-node');
  nodes.forEach(node => {
    node.addEventListener('mousedown', startDrag);
  });
  
  // 节点类型拖拽
  const nodeTypes = document.querySelectorAll('.node-type');
  nodeTypes.forEach(nodeType => {
    nodeType.addEventListener('dragstart', handleDragStart);
    nodeType.addEventListener('dragend', handleDragEnd);
  });
  
  // 画布拖拽区域
  const canvas = document.getElementById('journey-canvas');
  canvas.addEventListener('dragover', handleDragOver);
  canvas.addEventListener('drop', handleDrop);
}

function startDrag(e) {
  if (e.target.closest('.node-actions') || e.target.closest('.node-connector')) {
    return;
  }
  
  isDragging = true;
  selectedNode = e.currentTarget;
  selectNode(selectedNode);
  
  const rect = selectedNode.getBoundingClientRect();
  const canvasRect = document.getElementById('journey-canvas').getBoundingClientRect();
  
  dragOffset.x = e.clientX - rect.left;
  dragOffset.y = e.clientY - rect.top;
  
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);
  
  e.preventDefault();
}

function onDrag(e) {
  if (!isDragging || !selectedNode) return;
  
  const canvas = document.getElementById('journey-canvas');
  const canvasRect = canvas.getBoundingClientRect();
  
  const x = (e.clientX - canvasRect.left - dragOffset.x) / canvasZoom;
  const y = (e.clientY - canvasRect.top - dragOffset.y) / canvasZoom;
  
  selectedNode.style.left = Math.max(0, x) + 'px';
  selectedNode.style.top = Math.max(0, y) + 'px';
  
  updateConnections();
}

function stopDrag() {
  isDragging = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
}

function handleDragStart(e) {
  e.dataTransfer.setData('text/plain', e.target.dataset.type);
  e.target.classList.add('dragging');
}

function handleDragEnd(e) {
  e.target.classList.remove('dragging');
}

function handleDragOver(e) {
  e.preventDefault();
  e.dataTransfer.dropEffect = 'copy';
}

function handleDrop(e) {
  e.preventDefault();
  const nodeType = e.dataTransfer.getData('text/plain');
  const canvas = document.getElementById('journey-canvas');
  const canvasRect = canvas.getBoundingClientRect();
  
  const x = (e.clientX - canvasRect.left) / canvasZoom;
  const y = (e.clientY - canvasRect.top) / canvasZoom;
  
  addNodeAtPosition(nodeType, x, y);
}

function addNode(nodeType) {
  const canvas = document.getElementById('journey-canvas');
  const canvasRect = canvas.getBoundingClientRect();
  
  // 在画布中心添加节点
  const x = (canvasRect.width / 2 - 100) / canvasZoom;
  const y = (canvasRect.height / 2 - 50) / canvasZoom;
  
  addNodeAtPosition(nodeType, x, y);
}

function addNodeAtPosition(nodeType, x, y) {
  const nodeId = 'node_' + Date.now();
  const nodeHtml = createNodeHTML(nodeType, nodeId);
  
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = nodeHtml;
  const newNode = tempDiv.firstElementChild;
  
  newNode.style.left = x + 'px';
  newNode.style.top = y + 'px';
  
  document.getElementById('journey-canvas').appendChild(newNode);
  
  // 初始化新节点的拖拽
  newNode.addEventListener('mousedown', startDrag);
  
  // 自动选择新节点
  selectNode(newNode);
  showNodeProperties(newNode);
}

function createNodeHTML(nodeType, nodeId) {
  const nodeConfigs = {
    trigger: {
      icon: '🔴',
      title: '触发器',
      description: '配置触发条件'
    },
    email: {
      icon: '📧',
      title: '电子邮件',
      description: '配置邮件内容'
    },
    delay: {
      icon: '⏰',
      title: '延迟',
      description: '配置等待时间'
    },
    condition: {
      icon: '❓',
      title: '条件',
      description: '配置判断条件'
    },
    goal: {
      icon: '🎯',
      title: '目标',
      description: '配置目标动作'
    }
  };
  
  const config = nodeConfigs[nodeType];
  
  return `
    <div class="flow-node ${nodeType}-node" data-node-id="${nodeId}" data-node-type="${nodeType}">
      <div class="node-header ${nodeType}-header">
        <div class="node-icon">${config.icon}</div>
        <div class="node-title">${config.title}</div>
      </div>
      <div class="node-content">
        <div class="node-description">${config.description}</div>
      </div>
      <div class="node-connector top" data-connector="${nodeId}-top"></div>
      <div class="node-connector bottom" data-connector="${nodeId}-bottom"></div>
      ${nodeType === 'condition' ? '<div class="node-connector left" data-connector="' + nodeId + '-left"></div><div class="node-connector right" data-connector="' + nodeId + '-right"></div>' : ''}
      <div class="node-actions">
        <button class="btn mini" onclick="editNode('${nodeId}')">编辑</button>
        <button class="btn mini secondary" onclick="deleteNode('${nodeId}')">删除</button>
      </div>
    </div>
  `;
}

function selectNode(node) {
  deselectAllNodes();
  node.classList.add('selected');
  selectedNode = node;
  showNodeProperties(node);
}

function deselectAllNodes() {
  document.querySelectorAll('.flow-node').forEach(node => {
    node.classList.remove('selected');
  });
  selectedNode = null;
  hideNodeProperties();
}

function showNodeProperties(node) {
  const propertiesContent = document.getElementById('properties-content');
  const nodeType = node.dataset.nodeType;
  
  propertiesContent.innerHTML = createPropertiesHTML(nodeType, node);
}

function hideNodeProperties() {
  const propertiesContent = document.getElementById('properties-content');
  propertiesContent.innerHTML = '<div class="no-selection"><div class="hint">选择一个节点来配置其属性</div></div>';
}

function createPropertiesHTML(nodeType, node) {
  const properties = {
    trigger: `
      <div class="property-group">
        <label>触发类型</label>
        <select>
          <option>加入列表</option>
          <option>字段变更</option>
          <option>事件触发</option>
          <option>API调用</option>
        </select>
      </div>
      <div class="property-group">
        <label>触发条件</label>
        <input placeholder="如：列表名称、字段名等" />
      </div>
    `,
    email: `
      <div class="property-group">
        <label>邮件模板</label>
        <select>
          <option>欢迎邮件</option>
          <option>促销邮件</option>
          <option>通知邮件</option>
        </select>
      </div>
      <div class="property-group">
        <label>主题行</label>
        <input placeholder="邮件主题" />
      </div>
      <div class="property-group">
        <label>发件人</label>
        <input placeholder="发件人名称" />
      </div>
    `,
    delay: `
      <div class="property-group">
        <label>延迟类型</label>
        <select>
          <option>固定时间</option>
          <option>动态时间</option>
          <option>条件延迟</option>
        </select>
      </div>
      <div class="property-group">
        <label>延迟时长</label>
        <div class="row">
          <input type="number" value="1" style="width: 60px;" />
          <select style="width: 80px;">
            <option>分钟</option>
            <option>小时</option>
            <option>天</option>
          </select>
        </div>
      </div>
    `,
    condition: `
      <div class="property-group">
        <label>条件类型</label>
        <select>
          <option>打开邮件</option>
          <option>点击链接</option>
          <option>字段值</option>
          <option>标签</option>
        </select>
      </div>
      <div class="property-group">
        <label>条件值</label>
        <input placeholder="条件判断值" />
      </div>
      <div class="property-group">
        <label>分支标签</label>
        <div class="row">
          <input placeholder="是" value="Yes" />
          <input placeholder="否" value="No" />
        </div>
      </div>
    `,
    goal: `
      <div class="property-group">
        <label>目标类型</label>
        <select>
          <option>完成旅程</option>
          <option>设置标签</option>
          <option>更新字段</option>
          <option>Webhook</option>
        </select>
      </div>
      <div class="property-group">
        <label>目标配置</label>
        <input placeholder="目标具体配置" />
      </div>
    `
  };
  
  return `
    <div class="property-group">
      <label>节点名称</label>
      <input value="${node.querySelector('.node-title').textContent}" onchange="updateNodeTitle('${node.dataset.nodeId}', this.value)" />
    </div>
    ${properties[nodeType] || ''}
  `;
}

function updateNodeTitle(nodeId, title) {
  const node = document.querySelector(`[data-node-id="${nodeId}"]`);
  if (node) {
    node.querySelector('.node-title').textContent = title;
  }
}

function editNode(nodeId) {
  const node = document.querySelector(`[data-node-id="${nodeId}"]`);
  if (node) {
    showNodeEditModal(node);
  }
}

function showNodeEditModal(node) {
  const modal = document.getElementById('node-edit-modal');
  const form = document.getElementById('node-edit-form');
  const nodeType = node.dataset.nodeType;
  
  form.innerHTML = createNodeEditForm(nodeType, node);
  modal.style.display = 'flex';
}

function createNodeEditForm(nodeType, node) {
  // 根据节点类型创建详细的编辑表单
  return createPropertiesHTML(nodeType, node);
}

function closeNodeEdit() {
  document.getElementById('node-edit-modal').style.display = 'none';
}

function saveNodeEdit() {
  // 保存节点编辑逻辑
  closeNodeEdit();
}

function deleteNode(nodeId) {
  if (confirm('确定要删除这个节点吗？')) {
    const node = document.querySelector(`[data-node-id="${nodeId}"]`);
    if (node) {
      node.remove();
      updateConnections();
      if (selectedNode === node) {
        deselectAllNodes();
      }
    }
  }
}

function updateConnections() {
  // 更新连接线逻辑
  console.log('Updating connections...');
}

function zoomIn() {
  canvasZoom = Math.min(canvasZoom * 1.2, 2);
  updateZoom();
}

function zoomOut() {
  canvasZoom = Math.max(canvasZoom / 1.2, 0.3);
  updateZoom();
}

function updateZoom() {
  const canvas = document.getElementById('journey-canvas');
  canvas.style.transform = `scale(${canvasZoom})`;
  canvas.style.transformOrigin = 'top left';
  
  document.querySelector('.zoom-level').textContent = Math.round(canvasZoom * 100) + '%';
}

function centerCanvas() {
  // 居中画布逻辑
  console.log('Centering canvas...');
}

function fitToScreen() {
  // 适应屏幕逻辑
  console.log('Fitting to screen...');
}

function saveJourney() {
  // 保存旅程逻辑
  alert('旅程保存成功！');
}

function testJourney() {
  // 测试旅程逻辑
  alert('开始测试旅程...');
}

function publishJourney() {
  // 发布旅程逻辑
  if (confirm('确定要发布这个旅程吗？')) {
    alert('旅程发布成功！');
  }
}

function backToList() {
  // 返回列表逻辑
  window.location.href = 'journeys.html';
}

function closeProperties() {
  // 关闭属性面板逻辑
  console.log('Closing properties panel...');
}

// 页面初始化函数
function initJourneyCanvas() {
  console.log('初始化自动化旅程画布');
  
  // 设置画布事件监听
  const canvas = document.getElementById('journey-canvas');
  if (canvas) {
    // 画布点击事件
    canvas.addEventListener('click', function(e) {
      if (e.target === canvas) {
        deselectAllNodes();
      }
    });
    
    // 画布拖拽事件
    canvas.addEventListener('dragover', function(e) {
      e.preventDefault();
    });
    
    canvas.addEventListener('drop', function(e) {
      e.preventDefault();
      const nodeType = e.dataTransfer.getData('text/plain');
      if (nodeType) {
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        addNodeAtPosition(nodeType, x, y);
      }
    });
  }
  
  // 初始化工具栏拖拽
  initToolbarDrag();
}

// 初始化工具栏拖拽功能
function initToolbarDrag() {
  const nodeTypes = document.querySelectorAll('.node-type');
  nodeTypes.forEach(nodeType => {
    nodeType.addEventListener('dragstart', function(e) {
      e.dataTransfer.setData('text/plain', this.dataset.type);
      this.style.opacity = '0.5';
    });
    
    nodeType.addEventListener('dragend', function() {
      this.style.opacity = '1';
    });
  });
}
</script>
