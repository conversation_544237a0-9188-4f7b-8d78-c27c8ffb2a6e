<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Email System 原型（多文件版）</title>
  <link rel="stylesheet" href="assets/style.css" />
</head>
<body>
  <header>
    <div class="brand">Email System 原型</div>
    <input placeholder="全局搜索（联系人、活动、模板…）" />
    <button class="btn secondary" onclick="startDemo()" style="padding: 6px 12px; font-size: 12px;">🎬 功能演示</button>
    <div class="mini">不含用户系统</div>
  </header>

  <div class="layout">
    <nav id="sidebar">
      <div class="group">数据与人群</div>
      <a href="#contacts" data-page="contacts" class="active">📇 联系人</a>
      <a href="#lists-tags" data-page="lists-tags">🏷️ 标签</a>
      <a href="#segments" data-page="segments">🎯 人群圈选</a>
      <a href="#suppression-consent" data-page="suppression-consent">🚫 退订与同意</a>

      <div class="group">内容与触达</div>
      <a href="#templates" data-page="templates">🧱 模板与内容</a>
      <a href="#campaigns" data-page="campaigns">🚀 活动</a>
      <a href="#ab-tests" data-page="ab-tests">🧪 A/B 测试</a>
      <a href="#journey-visual" data-page="journey-visual">🔁 自动化</a>

      <div class="group">投递与追踪</div>
      <a href="#channels" data-page="channels">📡 发件渠道</a>
      <a href="#deliverability" data-page="deliverability">📤 投递与可达性</a>
      <a href="#analytics" data-page="analytics">📈 追踪与报表</a>

      <div class="group">采集与平台</div>
      <a href="#forms-preferences" data-page="forms-preferences">📝 表单与偏好中心</a>
      <a href="#integrations" data-page="integrations">🔌 开放平台与集成</a>
      <a href="#extensions" data-page="extensions">🧩 扩展功能</a>

      <div class="group">合规与商业</div>
      <a href="#billing-credits" data-page="billing-credits">💳 计费与额度</a>
      <a href="#security-compliance" data-page="security-compliance">🛡️ 安全与合规</a>

      <div class="group">管理与设置</div>
      <a href="#tenant-settings" data-page="tenant-settings">⚙️ 工作区设置</a>
      <a href="#operations" data-page="operations">🧭 运维与管理</a>
    </nav>

    <main id="app">
      <noscript>需要启用 JavaScript 才能查看原型内容。</noscript>
    </main>
  </div>

  <script src="assets/app.js"></script>
  <script src="demo-script.js"></script>
</body>
</html>


