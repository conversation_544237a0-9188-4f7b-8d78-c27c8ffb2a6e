// 全局状态管理
window.AppState = {
  contacts: [],
  campaigns: [],
  templates: [],
  lists: [],
  tags: [],
  segments: [],
  currentUser: { name: 'Demo User', email: '<EMAIL>' },
  notifications: []
};

// 工具函数
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function showNotification(message, type = 'success') {
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.innerHTML = `
    <div class="notification-content">
      <span>${message}</span>
      <button onclick="this.parentElement.parentElement.remove()">×</button>
    </div>
  `;
  document.body.appendChild(notification);

  setTimeout(() => {
    notification.remove();
  }, 5000);
}

function setActiveNav(page){
  document.querySelectorAll('nav a').forEach(a=>{
    a.classList.toggle('active', a.dataset.page===page);
  });
}

async function loadPage(page){
  // 路由兼容：旧的 campaigns-ab 重定向到 campaigns
  if(page === 'campaigns-ab'){
    page = 'campaigns';
    history.replaceState(null, '', '#campaigns');
  }
  try {
    const res = await fetch(`pages/${page}.html`);
    const html = await res.text();
    const app = document.getElementById('app');
    app.innerHTML = html;
    setActiveNav(page);
    initPageInteractions();

    // 执行页面内联脚本（确保 onclick 等全局函数可用）
    executeInlineScripts(app);

    // 页面加载后的特定初始化
    initPageData(page);
  } catch (e) {
    document.getElementById('app').innerHTML = `<div class="card">无法加载页面：${page}</div>`;
  }
}

// 执行容器内的内联/外链脚本
function executeInlineScripts(container){
  if(!container) return;
  const scripts = Array.from(container.querySelectorAll('script'));
  scripts.forEach(old => {
    const s = document.createElement('script');
    if (old.src) {
      s.src = old.src;
    } else {
      s.textContent = old.textContent || '';
    }
    document.body.appendChild(s);
    // 可选：清理避免重复执行
    old.parentNode && old.parentNode.removeChild(old);
  });
}

// 页面数据初始化
function initPageData(page) {
  switch(page) {
    case 'contacts':
      initContactsData();
      break;
    case 'campaigns':
      initCampaignsData();
      break;
    case 'templates':
      initTemplatesData();
      break;
    case 'lists-tags':
      initListsTagsData();
      break;
    case 'journey-visual':
      initJourneyVisualData();
      break;
  }
}

// 联系人页面数据初始化
function initContactsData() {
  if (window.AppState.contacts.length === 0) {
    // 初始化示例数据
    window.AppState.contacts = [
      {
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        status: 'active',
        tags: ['VIP', '双11活动'],
        createdAt: new Date('2024-01-15'),
        lastActivity: new Date('2024-08-10')
      },
      {
        id: '2',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        status: 'active',
        tags: ['新用户'],
        createdAt: new Date('2024-08-01'),
        lastActivity: new Date('2024-08-11')
      }
    ];
  }
  updateContactsTable();
}

function initRouter(){
  function go(){
    const hash = location.hash.replace('#','') || 'contacts';
    loadPage(hash);
  }
  window.addEventListener('hashchange', go);
  document.querySelectorAll('nav a').forEach(a=>{
    a.addEventListener('click', ()=>{
      // hashchange 会触发加载
    });
  });
  go();
}

function initPageInteractions(){
  // 面板开合
  document.querySelectorAll('[data-open]').forEach(btn=>{
    btn.addEventListener('click',()=>{
      const id = btn.getAttribute('data-open');
      const el = document.getElementById(id);
      if(el){ el.style.display='block'; }
    });
  });
  document.querySelectorAll('[data-close]').forEach(btn=>{
    btn.addEventListener('click',()=>{
      const id = btn.getAttribute('data-close');
      const el = document.getElementById(id);
      if(el){ el.style.display='none'; }
    });
  });

  // Tabs 切换（data-tab="name" data-tab-target="#id"）
  document.querySelectorAll('[data-tab-target]').forEach(tab=>{
    tab.addEventListener('click',()=>{
      const t = tab.getAttribute('data-tab-target');
      const group = tab.getAttribute('data-tab-group') || 'default';
      document.querySelectorAll(`[data-tab-group="${group}"]`).forEach(x=>x.classList.remove('active'));
      tab.classList.add('active');
      document.querySelectorAll(`[data-tab-panel="${group}"]`).forEach(p=>p.style.display='none');
      const panel = document.querySelector(t);
      if(panel){ panel.style.display='block'; }
    });
  });

  // 条件构建器：添加条件（作用域到最近的容器）
  document.querySelectorAll('[data-action="add-seg-row"]').forEach(btn=>{
    btn.addEventListener('click',()=>{
      const context = btn.closest('.dialog') || btn.closest('.card') || document;
      let wrap = context.querySelector('[data-role="seg-rows"]');
      if(!wrap){ wrap = context.querySelector('#seg-rows'); }
      if(!wrap) return;
      const row = document.createElement('div');
      row.className='row';
      row.innerHTML = `
        <div>
          <label>字段</label>
          <select class="seg-field">
            <option value="event:purchase">行为：购买</option>
            <option value="event:open">行为：打开</option>
            <option value="event:click">行为：点击</option>
            <option value="status">订阅状态</option>
            <option value="attr:region">属性：地区</option>
            <option value="rel:tag">关系：标签</option>
            <option value="rel:list">关系：列表</option>
          </select>
        </div>
        <div>
          <label>运算符</label>
          <select><option>&gt;=</option><option>&lt;=</option><option>包含</option><option>不包含</option><option>等于</option></select>
        </div>
        <div class="seg-value"><label>值</label><input placeholder="7 天 / 1 次 / VIP / active"/></div>
        <div class="seg-window" style="display:none"><label>时间窗口（天）</label><input type="number" min="1" placeholder="例如：7"/></div>
        <div style="flex:0"><button class="btn secondary">删除</button></div>
      `;
      row.querySelector('button').addEventListener('click',()=>row.remove());
      // 行为类字段显示时间窗口输入
      const fieldSel = row.querySelector('.seg-field');
      const winBox = row.querySelector('.seg-window');
      const onFieldChange = ()=>{
        const v = fieldSel.value || '';
        const isEvent = v.startsWith('event:');
        winBox.style.display = isEvent ? 'block' : 'none';
      };
      fieldSel.addEventListener('change', onFieldChange);
      onFieldChange();
      wrap.appendChild(row);
    });
  });

  // 人群圈选：预估人数（示例演示，作用域到容器）
  document.querySelectorAll('[data-action="estimate"]').forEach(btn=>{
    btn.addEventListener('click',()=>{
      const context = btn.closest('.dialog') || btn.closest('.card') || document;
      const ests = context.querySelectorAll('[data-role="est-count"], #est-count');
      ests.forEach(el=>{ el.textContent = '12,345 人'; });
    });
  });

  // 新建标签弹窗：类型与圈选联动
  const dlg = document.getElementById('dlg-new-tag');
  if(dlg){
    const typeSel = dlg.querySelector('#tag-type');
    const dyn = dlg.querySelector('#dyn-section');
    const stat = dlg.querySelector('#static-section');
    const dynEst = dlg.querySelector('#dynamic-estimate');
    const staticPickRadios = dlg.querySelectorAll('input[name="static-pick"]');
    const staticRules = dlg.querySelector('#static-rules-wrapper');

    const render = ()=>{
      const isDyn = typeSel && typeSel.value === 'dynamic';
      if(dyn) dyn.style.display = isDyn ? 'block' : 'none';
      if(dynEst) dynEst.style.display = isDyn ? 'block' : 'none';
      if(stat) stat.style.display = isDyn ? 'none' : 'block';

      const pick = Array.from(staticPickRadios).find(r=>r.checked);
      const useRules = pick && pick.value === 'rules';
      if(staticRules) staticRules.style.display = (!isDyn && useRules) ? 'block' : 'none';
    };

    if(typeSel){ typeSel.addEventListener('change', render); }
    staticPickRadios.forEach(r=> r.addEventListener('change', render));
    render();
  }

  // 批量操作：联动额外参数输入
  const bulkAction = document.getElementById('bulk-action');
  const bulkExtra = document.getElementById('bulk-extra');
  if(bulkAction && bulkExtra){
    const renderExtra = ()=>{
      const v = bulkAction.value;
      if(v==='tag'){
        bulkExtra.innerHTML = '<label>标签</label><input id="bulk-tags" placeholder="输入多个标签，用逗号分隔">';
      }else if(v==='add-to-list'){
        bulkExtra.innerHTML = '<label>列表</label><select id="bulk-list"><option>黑五预热</option><option>VIP 用户</option></select>';
      }else{
        bulkExtra.innerHTML = '';
      }
    };
    bulkAction.addEventListener('change', renderExtra);
    renderExtra();
  }

  // 批量操作：执行（示例提示）
  document.querySelectorAll('[data-action="apply-bulk"]').forEach(btn=>{
    btn.addEventListener('click',()=>{
      const scope = (document.getElementById('bulk-scope')||{}).value || 'all';
      const action = (document.getElementById('bulk-action')||{}).value || 'tag';
      const msg = scope==='all' ? '已对当前预估人群执行：' : '已对选中联系人执行：';
      alert(msg + action);
    });
  });

  // 预览样本：全选
  const chkAll = document.getElementById('chk-all');
  if(chkAll){
    chkAll.addEventListener('change',()=>{
      document.querySelectorAll('.chk').forEach(c=>{ c.checked = chkAll.checked; });
    });
  }

  // 模板多语言：语言切换与新增语言（示例交互）
  const localeSel = document.getElementById('tpl-locale');
  const addLocaleBtn = document.getElementById('add-locale');
  const subjectInput = document.getElementById('tpl-subject');
  const preheaderInput = document.getElementById('tpl-preheader');
  const htmlInput = document.getElementById('tpl-html');
  const previewTitle = document.getElementById('preview-title');
  const previewSub = document.getElementById('preview-sub');
  const previewCta = document.getElementById('preview-cta');

  // 简单内存存储不同语言的字段
  const defaultLocale = 'zh-CN';
  const localeStore = window.__tplLocaleStore || (window.__tplLocaleStore = {
    'zh-CN': {
      subject: '本周特惠来啦！限时7折优惠等你来抢！',
      preheader: '内含 7 折券码与精选商品，限时抢购，先到先得！',
      title: '🎉 本周特惠来啦！',
      sub: 'Hello {{first_name}}，专属优惠等你来拿',
      cta: '🚀 立即抢购'
    },
    'en-US': {
      subject: 'This Week Only! 30% OFF awaits you!',
      preheader: 'Exclusive coupon and picks inside. Limited time.',
      title: '🎉 Big Deals This Week!',
      sub: 'Hello {{first_name}}, your exclusive offer is here',
      cta: '🚀 Shop Now'
    },
    'es-ES': {
      subject: '¡Sólo esta semana! ¡30% de descuento!',
      preheader: 'Cupón exclusivo y selección especial. Tiempo limitado.',
      title: '🎉 Grandes ofertas esta semana',
      sub: 'Hola {{first_name}}, tu oferta exclusiva ya está aquí',
      cta: '🚀 Comprar ahora'
    }
  });

  const renderLocale = ()=>{
    if(!localeSel) return;
    const loc = localeSel.value || defaultLocale;
    const data = localeStore[loc] || localeStore[defaultLocale];
    if(subjectInput) subjectInput.value = data.subject || '';
    if(preheaderInput) preheaderInput.value = data.preheader || '';
    if(previewTitle) previewTitle.textContent = data.title || '';
    if(previewSub) previewSub.textContent = (data.sub || '').replace('{{first_name}}','John');
    if(previewCta) previewCta.textContent = data.cta || '';
  };

  if(localeSel){
    localeSel.addEventListener('change', renderLocale);
    renderLocale();
  }

  if(addLocaleBtn && localeSel){
    addLocaleBtn.addEventListener('click',()=>{
      const newCode = prompt('输入新的语言代码（如 fr-FR）：');
      if(!newCode) return;
      if(!localeStore[newCode]){
        // 从当前语言拷贝
        const from = localeSel.value || defaultLocale;
        localeStore[newCode] = { ...localeStore[from] };
        const opt = document.createElement('option');
        opt.value = newCode; opt.textContent = newCode; // 简化展示
        localeSel.appendChild(opt);
        localeSel.value = newCode;
        renderLocale();
        alert('已新增语言：' + newCode + '（基于 ' + from + ' 拷贝）');
      } else {
        alert('语言已存在：' + newCode);
      }
    });
  }
}

document.addEventListener('DOMContentLoaded', initRouter);

// ===== 联系人管理功能 =====
function updateContactsTable() {
  const tbody = document.querySelector('#contacts-table tbody');
  if (!tbody) return;

  const contacts = window.AppState.contacts;
  tbody.innerHTML = contacts.map(contact => `
    <tr>
      <td><input type="checkbox" data-contact-id="${contact.id}"></td>
      <td>
        <div style="font-weight: 600;">${contact.firstName} ${contact.lastName}</div>
        <div style="font-size: 12px; color: var(--muted);">${contact.email}</div>
      </td>
      <td>
        <span class="pill ${contact.status === 'active' ? 'success' : 'warning'}">${contact.status}</span>
      </td>
      <td>
        ${contact.tags.map(tag => `<span class="pill secondary">${tag}</span>`).join(' ')}
      </td>
      <td>${contact.createdAt.toLocaleDateString()}</td>
      <td>${contact.lastActivity.toLocaleDateString()}</td>
      <td>
        <button class="btn secondary" style="padding: 4px 8px; font-size: 12px;" onclick="editContact('${contact.id}')">编辑</button>
        <button class="btn danger" style="padding: 4px 8px; font-size: 12px;" onclick="deleteContact('${contact.id}')">删除</button>
      </td>
    </tr>
  `).join('');
}

// 兼容各页面实现：若存在页面内卡片/抽屉则直接打开，否则使用全局通知/模态
function showImportPanel() {
  const el = document.getElementById('import-contacts') || document.getElementById('import-panel');
  if (el) { el.style.display = ''; return; }
  showNotification('导入面板功能开发中...', 'info');
}

function showCreateContact() {
  const panel = document.getElementById('create-contact');
  if (panel) { panel.style.display = ''; return; }
  // 回退为全局模态创建（简化版）
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h3>新建联系人</h3>
        <button onclick="this.closest('.modal').remove()">×</button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div>
            <label>名字 *</label>
            <input id="new-contact-first-name" placeholder="请输入名字">
          </div>
          <div>
            <label>姓氏</label>
            <input id="new-contact-last-name" placeholder="请输入姓氏">
          </div>
        </div>
        <label>邮箱地址 *</label>
        <input id="new-contact-email" type="email" placeholder="请输入邮箱地址">

        <label>标签</label>
        <input id="new-contact-tags" placeholder="用逗号分隔多个标签">
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="this.closest('.modal').remove()">取消</button>
        <button class="btn" onclick="createContact()">创建联系人</button>
      </div>
    </div>
  `;
  document.body.appendChild(modal);
}

function createContact() {
  const firstName = document.getElementById('new-contact-first-name').value;
  const lastName = document.getElementById('new-contact-last-name').value;
  const email = document.getElementById('new-contact-email').value;
  const tags = document.getElementById('new-contact-tags').value.split(',').map(t => t.trim()).filter(t => t);

  if (!firstName || !email) {
    showNotification('请填写必填字段', 'error');
    return;
  }

  // 检查邮箱是否已存在
  if (window.AppState.contacts.some(c => c.email === email)) {
    showNotification('该邮箱地址已存在', 'error');
    return;
  }

  const newContact = {
    id: generateId(),
    firstName,
    lastName,
    email,
    status: 'active',
    tags,
    createdAt: new Date(),
    lastActivity: new Date()
  };

  window.AppState.contacts.push(newContact);
  updateContactsTable();
  document.querySelector('.modal').remove();
  showNotification('联系人创建成功！');
}

function editContact(contactId) {
  const contact = window.AppState.contacts.find(c => c.id === contactId);
  if (!contact) return;

  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h3>编辑联系人</h3>
        <button onclick="this.closest('.modal').remove()">×</button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div>
            <label>名字 *</label>
            <input id="edit-contact-first-name" value="${contact.firstName}">
          </div>
          <div>
            <label>姓氏</label>
            <input id="edit-contact-last-name" value="${contact.lastName}">
          </div>
        </div>
        <label>邮箱地址 *</label>
        <input id="edit-contact-email" type="email" value="${contact.email}">

        <label>状态</label>
        <select id="edit-contact-status">
          <option value="active" ${contact.status === 'active' ? 'selected' : ''}>活跃</option>
          <option value="unsubscribed" ${contact.status === 'unsubscribed' ? 'selected' : ''}>已退订</option>
          <option value="bounced" ${contact.status === 'bounced' ? 'selected' : ''}>硬退回</option>
        </select>

        <label>标签</label>
        <input id="edit-contact-tags" value="${contact.tags.join(', ')}" placeholder="用逗号分隔多个标签">
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="this.closest('.modal').remove()">取消</button>
        <button class="btn" onclick="updateContact('${contactId}')">保存更改</button>
      </div>
    </div>
  `;
  document.body.appendChild(modal);
}

function updateContact(contactId) {
  const contact = window.AppState.contacts.find(c => c.id === contactId);
  if (!contact) return;

  const firstName = document.getElementById('edit-contact-first-name').value;
  const lastName = document.getElementById('edit-contact-last-name').value;
  const email = document.getElementById('edit-contact-email').value;
  const status = document.getElementById('edit-contact-status').value;
  const tags = document.getElementById('edit-contact-tags').value.split(',').map(t => t.trim()).filter(t => t);

  if (!firstName || !email) {
    showNotification('请填写必填字段', 'error');
    return;
  }

  // 检查邮箱是否与其他联系人冲突
  if (window.AppState.contacts.some(c => c.email === email && c.id !== contactId)) {
    showNotification('该邮箱地址已被其他联系人使用', 'error');
    return;
  }

  contact.firstName = firstName;
  contact.lastName = lastName;
  contact.email = email;
  contact.status = status;
  contact.tags = tags;

  updateContactsTable();
  document.querySelector('.modal').remove();
  showNotification('联系人更新成功！');
}

function deleteContact(contactId) {
  if (confirm('确定要删除这个联系人吗？此操作不可撤销。')) {
    window.AppState.contacts = window.AppState.contacts.filter(c => c.id !== contactId);
    updateContactsTable();
    showNotification('联系人已删除');
  }
}

function showBulkTag() {
  const bar = document.getElementById('bulk-bar');
  if (bar) { bar.style.display = 'flex'; return; }
  // 回退为全局模态版本
  const selectedContacts = Array.from(document.querySelectorAll('#contacts-table input[type="checkbox"]:checked'))
    .map(cb => cb.getAttribute('data-contact-id'));
  if (selectedContacts.length === 0) { showNotification('请先选择要操作的联系人', 'warning'); return; }
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h3>批量操作 (${selectedContacts.length} 个联系人)</h3>
        <button onclick="this.closest('.modal').remove()">×</button>
      </div>
      <div class="modal-body">
        <label>操作类型</label>
        <select id="bulk-operation">
          <option value="add-tags">添加标签</option>
          <option value="remove-tags">移除标签</option>
          <option value="change-status">更改状态</option>
        </select>
        <div id="bulk-tags-input">
          <label>标签</label>
          <input id="bulk-tags" placeholder="用逗号分隔多个标签">
        </div>
        <div id="bulk-status-input" style="display:none;">
          <label>状态</label>
          <select id="bulk-status">
            <option value="active">活跃</option>
            <option value="unsubscribed">已退订</option>
            <option value="bounced">硬退回</option>
          </select>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="this.closest('.modal').remove()">取消</button>
        <button class="btn" onclick="executeBulkOperation('${selectedContacts.join(',')}')">执行操作</button>
      </div>
    </div>
  `;
  document.body.appendChild(modal);
  document.getElementById('bulk-operation').addEventListener('change', function() {
    const tagsInput = document.getElementById('bulk-tags-input');
    const statusInput = document.getElementById('bulk-status-input');
    if (this.value === 'change-status') { tagsInput.style.display = 'none'; statusInput.style.display = 'block'; }
    else { tagsInput.style.display = 'block'; statusInput.style.display = 'none'; }
  });
}

// 将关键函数显式挂到 window，确保内联 onclick 能访问
window.showImportPanel = showImportPanel;
window.showCreateContact = showCreateContact;
window.showBulkTag = showBulkTag;

// 字段管理（全局可用）：优先打开联系人页的面板
function showFieldsManager(){
  const el = document.getElementById('fields-manager');
  if (el) {
    el.style.display = 'flex';
    if (typeof window.renderFieldsTable === 'function') window.renderFieldsTable();
    if (typeof window.renderCustomFieldInputs === 'function') window.renderCustomFieldInputs();
    if (typeof window.bindAutoKey === 'function') window.bindAutoKey();
    return;
  }
  showNotification('请在联系人页面使用"字段管理"', 'info');
}

function hideFieldsManager(){
  const el = document.getElementById('fields-manager');
  if (el) { el.style.display = 'none'; return; }
}

window.showFieldsManager = showFieldsManager;
window.hideFieldsManager = hideFieldsManager;

function executeBulkOperation(contactIds) {
  const ids = contactIds.split(',');
  const operation = document.getElementById('bulk-operation').value;

  let count = 0;

  if (operation === 'add-tags' || operation === 'remove-tags') {
    const tags = document.getElementById('bulk-tags').value.split(',').map(t => t.trim()).filter(t => t);
    if (tags.length === 0) {
      showNotification('请输入标签', 'error');
      return;
    }

    ids.forEach(id => {
      const contact = window.AppState.contacts.find(c => c.id === id);
      if (contact) {
        if (operation === 'add-tags') {
          tags.forEach(tag => {
            if (!contact.tags.includes(tag)) {
              contact.tags.push(tag);
            }
          });
        } else {
          contact.tags = contact.tags.filter(tag => !tags.includes(tag));
        }
        count++;
      }
    });
  } else if (operation === 'change-status') {
    const status = document.getElementById('bulk-status').value;

    ids.forEach(id => {
      const contact = window.AppState.contacts.find(c => c.id === id);
      if (contact) {
        contact.status = status;
        count++;
      }
    });
  }

  updateContactsTable();
  document.querySelector('.modal').remove();
  showNotification(`批量操作完成，已处理 ${count} 个联系人`);
}

// ===== 活动管理功能 =====
function initCampaignsData() {
  if (window.AppState.campaigns.length === 0) {
    window.AppState.campaigns = [
      {
        id: '1',
        name: '双11预热活动',
        status: 'draft',
        type: 'broadcast',
        templateId: '1',
        audienceCount: 15000,
        createdAt: new Date('2024-08-01'),
        scheduledAt: null,
        stats: { sent: 0, delivered: 0, opened: 0, clicked: 0 }
      },
      {
        id: '2',
        name: '新品发布通知',
        status: 'sent',
        type: 'broadcast',
        templateId: '2',
        audienceCount: 8500,
        createdAt: new Date('2024-07-28'),
        scheduledAt: new Date('2024-07-30'),
        stats: { sent: 8500, delivered: 8234, opened: 3456, clicked: 567 }
      }
    ];
  }
  updateCampaignsTable();
}

function updateCampaignsTable() {
  const tbody = document.querySelector('#campaigns-table tbody');
  if (!tbody) return;

  const campaigns = window.AppState.campaigns;
  tbody.innerHTML = campaigns.map(campaign => `
    <tr>
      <td>
        <div style="font-weight: 600;">${campaign.name}</div>
        <div style="font-size: 12px; color: var(--muted);">${campaign.type}</div>
      </td>
      <td>
        <span class="pill ${getStatusClass(campaign.status)}">${getStatusText(campaign.status)}</span>
      </td>
      <td>${campaign.audienceCount.toLocaleString()}</td>
      <td>${campaign.stats.sent.toLocaleString()}</td>
      <td>${campaign.stats.delivered.toLocaleString()}</td>
      <td>${campaign.stats.opened.toLocaleString()}</td>
      <td>${campaign.stats.clicked.toLocaleString()}</td>
      <td>${campaign.createdAt.toLocaleDateString()}</td>
      <td>
        <button class="btn secondary" style="padding: 4px 8px; font-size: 12px;" onclick="editCampaign('${campaign.id}')">编辑</button>
        <button class="btn secondary" style="padding: 4px 8px; font-size: 12px;" onclick="viewCampaignStats('${campaign.id}')">统计</button>
        ${campaign.status === 'draft' ? `<button class="btn" style="padding: 4px 8px; font-size: 12px;" onclick="sendCampaign('${campaign.id}')">发送</button>` : ''}
      </td>
    </tr>
  `).join('');
}

function getStatusClass(status) {
  switch(status) {
    case 'draft': return 'secondary';
    case 'scheduled': return 'warning';
    case 'sending': return 'info';
    case 'sent': return 'success';
    case 'failed': return 'error';
    default: return 'secondary';
  }
}

function getStatusText(status) {
  switch(status) {
    case 'draft': return '草稿';
    case 'scheduled': return '已计划';
    case 'sending': return '发送中';
    case 'sent': return '已发送';
    case 'failed': return '发送失败';
    default: return status;
  }
}

function showCreateCampaign() {
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content" style="max-width: 600px;">
      <div class="modal-header">
        <h3>创建新活动</h3>
        <button onclick="this.closest('.modal').remove()">×</button>
      </div>
      <div class="modal-body">
        <label>活动名称 *</label>
        <input id="new-campaign-name" placeholder="请输入活动名称">

        <label>活动类型</label>
        <select id="new-campaign-type">
          <option value="broadcast">群发邮件</option>
          <option value="drip">滴灌营销</option>
          <option value="trigger">触发邮件</option>
        </select>

        <label>邮件模板</label>
        <select id="new-campaign-template">
          <option value="">请选择模板</option>
          <option value="1">简约促销模板</option>
          <option value="2">节日祝福模板</option>
          <option value="3">周报模板</option>
        </select>

        <label>目标受众</label>
        <select id="new-campaign-audience">
          <option value="">请选择受众</option>
          <option value="all">所有联系人</option>
          <option value="active">活跃用户</option>
          <option value="vip">VIP用户</option>
        </select>

        <label>发送时间</label>
        <select id="new-campaign-schedule">
          <option value="now">立即发送</option>
          <option value="schedule">计划发送</option>
        </select>

        <div id="schedule-time" style="display:none;">
          <label>计划时间</label>
          <input id="new-campaign-datetime" type="datetime-local">
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="this.closest('.modal').remove()">取消</button>
        <button class="btn" onclick="createCampaign()">创建活动</button>
      </div>
    </div>
  `;
  document.body.appendChild(modal);

  // 计划发送时间显示/隐藏
  document.getElementById('new-campaign-schedule').addEventListener('change', function() {
    const scheduleTime = document.getElementById('schedule-time');
    scheduleTime.style.display = this.value === 'schedule' ? 'block' : 'none';
  });
}

function createCampaign() {
  const name = document.getElementById('new-campaign-name').value;
  const type = document.getElementById('new-campaign-type').value;
  const templateId = document.getElementById('new-campaign-template').value;
  const audience = document.getElementById('new-campaign-audience').value;
  const schedule = document.getElementById('new-campaign-schedule').value;
  const datetime = document.getElementById('new-campaign-datetime').value;

  if (!name || !templateId || !audience) {
    showNotification('请填写必填字段', 'error');
    return;
  }

  const newCampaign = {
    id: generateId(),
    name,
    type,
    templateId,
    status: schedule === 'now' ? 'sending' : 'scheduled',
    audienceCount: getAudienceCount(audience),
    createdAt: new Date(),
    scheduledAt: schedule === 'schedule' ? new Date(datetime) : null,
    stats: { sent: 0, delivered: 0, opened: 0, clicked: 0 }
  };

  window.AppState.campaigns.push(newCampaign);
  updateCampaignsTable();
  document.querySelector('.modal').remove();

  if (schedule === 'now') {
    showNotification('活动创建成功，正在发送中...', 'success');
    // 模拟发送过程
    setTimeout(() => {
      newCampaign.status = 'sent';
      newCampaign.stats = {
        sent: newCampaign.audienceCount,
        delivered: Math.floor(newCampaign.audienceCount * 0.97),
        opened: Math.floor(newCampaign.audienceCount * 0.25),
        clicked: Math.floor(newCampaign.audienceCount * 0.05)
      };
      updateCampaignsTable();
      showNotification('活动发送完成！', 'success');
    }, 3000);
  } else {
    showNotification('活动创建成功，已计划发送！', 'success');
  }
}

function getAudienceCount(audience) {
  switch(audience) {
    case 'all': return window.AppState.contacts.length || 12000;
    case 'active': return Math.floor((window.AppState.contacts.length || 12000) * 0.7);
    case 'vip': return Math.floor((window.AppState.contacts.length || 12000) * 0.15);
    default: return 0;
  }
}

function editCampaign(campaignId) {
  const campaign = window.AppState.campaigns.find(c => c.id === campaignId);
  if (!campaign) return;

  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content" style="max-width: 600px;">
      <div class="modal-header">
        <h3>编辑活动</h3>
        <button onclick="this.closest('.modal').remove()">×</button>
      </div>
      <div class="modal-body">
        <label>活动名称 *</label>
        <input id="edit-campaign-name" value="${campaign.name}">

        <label>活动状态</label>
        <select id="edit-campaign-status">
          <option value="draft" ${campaign.status === 'draft' ? 'selected' : ''}>草稿</option>
          <option value="scheduled" ${campaign.status === 'scheduled' ? 'selected' : ''}>已计划</option>
          <option value="sent" ${campaign.status === 'sent' ? 'selected' : ''}>已发送</option>
          <option value="failed" ${campaign.status === 'failed' ? 'selected' : ''}>发送失败</option>
        </select>

        <div class="row">
          <div>
            <label>发送数量</label>
            <input id="edit-campaign-sent" type="number" value="${campaign.stats.sent}">
          </div>
          <div>
            <label>送达数量</label>
            <input id="edit-campaign-delivered" type="number" value="${campaign.stats.delivered}">
          </div>
        </div>

        <div class="row">
          <div>
            <label>打开数量</label>
            <input id="edit-campaign-opened" type="number" value="${campaign.stats.opened}">
          </div>
          <div>
            <label>点击数量</label>
            <input id="edit-campaign-clicked" type="number" value="${campaign.stats.clicked}">
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="this.closest('.modal').remove()">取消</button>
        <button class="btn" onclick="updateCampaign('${campaignId}')">保存更改</button>
      </div>
    </div>
  `;
  document.body.appendChild(modal);
}

function updateCampaign(campaignId) {
  const campaign = window.AppState.campaigns.find(c => c.id === campaignId);
  if (!campaign) return;

  const name = document.getElementById('edit-campaign-name').value;
  const status = document.getElementById('edit-campaign-status').value;
  const sent = parseInt(document.getElementById('edit-campaign-sent').value) || 0;
  const delivered = parseInt(document.getElementById('edit-campaign-delivered').value) || 0;
  const opened = parseInt(document.getElementById('edit-campaign-opened').value) || 0;
  const clicked = parseInt(document.getElementById('edit-campaign-clicked').value) || 0;

  if (!name) {
    showNotification('请填写活动名称', 'error');
    return;
  }

  campaign.name = name;
  campaign.status = status;
  campaign.stats = { sent, delivered, opened, clicked };

  updateCampaignsTable();
  document.querySelector('.modal').remove();
  showNotification('活动更新成功！');
}

function sendCampaign(campaignId) {
  const campaign = window.AppState.campaigns.find(c => c.id === campaignId);
  if (!campaign) return;

  if (confirm(`确定要发送活动"${campaign.name}"吗？此操作不可撤销。`)) {
    campaign.status = 'sending';
    updateCampaignsTable();
    showNotification('活动开始发送...', 'info');

    // 模拟发送过程
    setTimeout(() => {
      campaign.status = 'sent';
      campaign.stats = {
        sent: campaign.audienceCount,
        delivered: Math.floor(campaign.audienceCount * 0.97),
        opened: Math.floor(campaign.audienceCount * 0.25),
        clicked: Math.floor(campaign.audienceCount * 0.05)
      };
      updateCampaignsTable();
      showNotification('活动发送完成！', 'success');
    }, 2000);
  }
}

function viewCampaignStats(campaignId) {
  const campaign = window.AppState.campaigns.find(c => c.id === campaignId);
  if (!campaign) return;

  const deliveryRate = campaign.stats.sent > 0 ? (campaign.stats.delivered / campaign.stats.sent * 100).toFixed(1) : '0';
  const openRate = campaign.stats.delivered > 0 ? (campaign.stats.opened / campaign.stats.delivered * 100).toFixed(1) : '0';
  const clickRate = campaign.stats.delivered > 0 ? (campaign.stats.clicked / campaign.stats.delivered * 100).toFixed(1) : '0';
  const ctr = campaign.stats.opened > 0 ? (campaign.stats.clicked / campaign.stats.opened * 100).toFixed(1) : '0';

  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content" style="max-width: 700px;">
      <div class="modal-header">
        <h3>活动统计 - ${campaign.name}</h3>
        <button onclick="this.closest('.modal').remove()">×</button>
      </div>
      <div class="modal-body">
        <div class="grid cols-2" style="margin-bottom: 20px;">
          <div class="card" style="text-align: center; margin: 0;">
            <div style="font-size: 24px; font-weight: 700; color: var(--pri);">${campaign.stats.sent.toLocaleString()}</div>
            <div style="font-size: 13px; color: var(--muted);">发送数量</div>
          </div>
          <div class="card" style="text-align: center; margin: 0;">
            <div style="font-size: 24px; font-weight: 700; color: var(--success);">${deliveryRate}%</div>
            <div style="font-size: 13px; color: var(--muted);">送达率</div>
          </div>
          <div class="card" style="text-align: center; margin: 0;">
            <div style="font-size: 24px; font-weight: 700; color: var(--warning);">${openRate}%</div>
            <div style="font-size: 13px; color: var(--muted);">打开率</div>
          </div>
          <div class="card" style="text-align: center; margin: 0;">
            <div style="font-size: 24px; font-weight: 700; color: var(--danger);">${clickRate}%</div>
            <div style="font-size: 13px; color: var(--muted);">点击率</div>
          </div>
        </div>

        <div class="card" style="margin: 0;">
          <div class="mini">详细数据</div>
          <table class="table" style="margin: 0;">
            <tbody>
              <tr><td>发送数量</td><td>${campaign.stats.sent.toLocaleString()}</td></tr>
              <tr><td>送达数量</td><td>${campaign.stats.delivered.toLocaleString()}</td></tr>
              <tr><td>打开数量</td><td>${campaign.stats.opened.toLocaleString()}</td></tr>
              <tr><td>点击数量</td><td>${campaign.stats.clicked.toLocaleString()}</td></tr>
              <tr><td>点击打开率</td><td>${ctr}%</td></tr>
              <tr><td>创建时间</td><td>${campaign.createdAt.toLocaleString()}</td></tr>
              ${campaign.scheduledAt ? `<tr><td>计划时间</td><td>${campaign.scheduledAt.toLocaleString()}</td></tr>` : ''}
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="this.closest('.modal').remove()">关闭</button>
        <button class="btn secondary" onclick="exportCampaignStats('${campaignId}')">导出数据</button>
      </div>
    </div>
  `;
  document.body.appendChild(modal);
}

function exportCampaignStats(campaignId) {
  const campaign = window.AppState.campaigns.find(c => c.id === campaignId);
  if (!campaign) return;

  showNotification('统计数据导出功能开发中...', 'info');
}

// ===== 模板管理功能 =====
function initTemplatesData() {
  if (window.AppState.templates.length === 0) {
    window.AppState.templates = [
      {
        id: '1',
        name: '简约促销模板',
        status: 'published',
        category: 'promotion',
        subject: '本周特惠来啦！限时7折优惠等你来抢！',
        preheader: '内含 7 折券码与精选商品，限时抢购，先到先得！',
        htmlContent: '<div>促销邮件内容...</div>',
        usageCount: 1234,
        createdAt: new Date('2024-07-15'),
        updatedAt: new Date('2024-08-01')
      },
      {
        id: '2',
        name: '节日祝福模板',
        status: 'draft',
        category: 'holiday',
        subject: '节日快乐！特别优惠送给您',
        preheader: '感谢您的支持，节日特惠等您来享',
        htmlContent: '<div>节日祝福内容...</div>',
        usageCount: 89,
        createdAt: new Date('2024-08-05'),
        updatedAt: new Date('2024-08-10')
      },
      {
        id: '3',
        name: '周报模板',
        status: 'published',
        category: 'newsletter',
        subject: '本周精选内容汇总',
        preheader: '一周热点，不容错过',
        htmlContent: '<div>周报内容...</div>',
        usageCount: 567,
        createdAt: new Date('2024-06-20'),
        updatedAt: new Date('2024-07-28')
      }
    ];
  }
  updateTemplatesGrid();
}

function updateTemplatesGrid() {
  const grid = document.querySelector('#templates-grid');
  if (!grid) return;

  const templates = window.AppState.templates;
  grid.innerHTML = templates.map(template => `
    <div class="card" style="cursor: pointer; transition: all 0.2s ease;" onclick="editTemplate('${template.id}')">
      <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;">
        <div class="mini">${template.name}</div>
        <div style="font-size: 12px; color: ${template.status === 'published' ? 'var(--success)' : 'var(--warning)'};">
          ${template.status === 'published' ? '✅ 已发布' : '⚠️ 草稿'}
        </div>
      </div>
      <div style="height: 120px; background: ${getTemplateGradient(template.category)}; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 12px;">
        <div style="text-align: center; color: var(--pri);">
          <div style="font-size: 24px; margin-bottom: 8px;">${getTemplateIcon(template.category)}</div>
          <div style="font-weight: 600;">${template.category}</div>
        </div>
      </div>
      <div class="hint">点击编辑 | 使用次数: ${template.usageCount.toLocaleString()}</div>
    </div>
  `).join('');
}

function getTemplateGradient(category) {
  switch(category) {
    case 'promotion': return 'linear-gradient(135deg, #f0f9ff, #e0f2fe)';
    case 'holiday': return 'linear-gradient(135deg, #fef3c7, #fde68a)';
    case 'newsletter': return 'linear-gradient(135deg, #f0fdf4, #dcfce7)';
    default: return 'linear-gradient(135deg, #f8fafc, #f1f5f9)';
  }
}

function getTemplateIcon(category) {
  switch(category) {
    case 'promotion': return '🎯';
    case 'holiday': return '🎉';
    case 'newsletter': return '📊';
    default: return '📧';
  }
}

function createNewTemplate() {
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content" style="max-width: 600px;">
      <div class="modal-header">
        <h3>创建新模板</h3>
        <button onclick="this.closest('.modal').remove()">×</button>
      </div>
      <div class="modal-body">
        <label>模板名称 *</label>
        <input id="new-template-name" placeholder="请输入模板名称">

        <label>模板分类</label>
        <select id="new-template-category">
          <option value="promotion">促销活动</option>
          <option value="holiday">节日祝福</option>
          <option value="newsletter">新闻周报</option>
          <option value="welcome">欢迎邮件</option>
          <option value="notification">通知邮件</option>
        </select>

        <div class="divider"></div>
        <div class="row" style="align-items:center">
          <div class="flex-0"><input type="checkbox" id="new-tpl-multi"></div>
          <div><label for="new-tpl-multi" style="margin:0">支持多语言</label><div class="hint">开启后可为多个语言分别编辑主题/预览/正文，并支持一键翻译</div></div>
        </div>

        <div id="new-tpl-locale-config" style="display:none;">
          <label>选择语言</label>
          <div id="new-tpl-locale-checkboxes" class="row" style="align-items:center">
            <div class="pill"><input type="checkbox" value="zh-CN" checked disabled> zh-CN</div>
            <div class="pill"><input type="checkbox" value="en-US" checked> en-US</div>
            <div class="pill"><input type="checkbox" value="es-ES"> es-ES</div>
          </div>

          <div class="row">
            <div>
              <label>编辑语言</label>
              <select id="new-tpl-locale-active">
                <option value="zh-CN">zh-CN</option>
                <option value="en-US">en-US</option>
                <option value="es-ES">es-ES</option>
              </select>
            </div>
            <div class="flex-0" style="align-self:flex-end">
              <button class="btn secondary" id="new-tpl-auto-translate">自动翻译到其他语言</button>
            </div>
          </div>
        </div>

        <label>邮件主题 *</label>
        <input id="new-template-subject" placeholder="请输入邮件主题">

        <label>预览文本</label>
        <input id="new-template-preheader" placeholder="请输入预览文本">

        <label>邮件内容 (HTML)</label>
        <textarea id="new-template-content" style="min-height: 150px;" placeholder="请输入HTML内容"></textarea>
        <textarea id="new-tpl-locale-json" style="display:none"></textarea>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="this.closest('.modal').remove()">取消</button>
        <button class="btn" onclick="createTemplate()">创建模板</button>
      </div>
    </div>
  `;
  document.body.appendChild(modal);

  // 多语言创建交互
  const multiChk = modal.querySelector('#new-tpl-multi');
  const cfg = modal.querySelector('#new-tpl-locale-config');
  const chkWrap = modal.querySelector('#new-tpl-locale-checkboxes');
  const activeSel = modal.querySelector('#new-tpl-locale-active');
  const subEl = modal.querySelector('#new-template-subject');
  const preEl = modal.querySelector('#new-template-preheader');
  const htmlEl = modal.querySelector('#new-template-content');
  const jsonEl = modal.querySelector('#new-tpl-locale-json');
  const autoBtn = modal.querySelector('#new-tpl-auto-translate');

  const defaultLocale = 'zh-CN';
  const localeData = {
    'zh-CN': { subject: '', preheader: '', content: '' },
    'en-US': { subject: '', preheader: '', content: '' },
    'es-ES': { subject: '', preheader: '', content: '' },
  };
  const enabledLocales = new Set(['zh-CN', 'en-US']);

  function syncJson() {
    jsonEl.value = JSON.stringify({ locales: Object.fromEntries([...enabledLocales].map(l => [l, localeData[l]])) });
  }

  function renderActiveLocaleFields() {
    const loc = activeSel.value || defaultLocale;
    const data = localeData[loc] || { subject: '', preheader: '', content: '' };
    subEl.value = data.subject || '';
    preEl.value = data.preheader || '';
    htmlEl.value = data.content || '';
  }

  function persistCurrentFields() {
    const loc = activeSel.value || defaultLocale;
    localeData[loc] = {
      subject: subEl.value || '',
      preheader: preEl.value || '',
      content: htmlEl.value || '',
    };
    syncJson();
  }

  function applyAutoTranslate(from) {
    const base = localeData[from];
    const translate = (text, to) => {
      if (!text) return text;
      if (to === 'en-US') return `[EN] ${text}`;
      if (to === 'es-ES') return `[ES] ${text}`;
      return text;
    };
    enabledLocales.forEach(loc => {
      if (loc === from) return;
      localeData[loc] = {
        subject: translate(base.subject, loc),
        preheader: translate(base.preheader, loc),
        content: translate(base.content, loc),
      };
    });
    if (activeSel.value !== from) renderActiveLocaleFields();
    syncJson();
  }

  multiChk.addEventListener('change', () => {
    cfg.style.display = multiChk.checked ? 'block' : 'none';
    if (multiChk.checked) {
      // 将当前输入存入默认语言
      localeData[defaultLocale] = {
        subject: subEl.value || '',
        preheader: preEl.value || '',
        content: htmlEl.value || '',
      };
      syncJson();
    } else {
      jsonEl.value = '';
    }
  });

  // 语言勾选切换
  chkWrap.querySelectorAll('input[type="checkbox"]').forEach(cb => {
    cb.addEventListener('change', () => {
      const code = cb.value;
      if (cb.checked) {
        enabledLocales.add(code);
        if (!localeData[code]) localeData[code] = { subject: '', preheader: '', content: '' };
      } else {
        if (code === defaultLocale) { cb.checked = true; return; }
        enabledLocales.delete(code);
      }
      // 更新可编辑语言下拉
      const prev = activeSel.value;
      activeSel.innerHTML = [...enabledLocales].map(l => `<option value="${l}">${l}</option>`).join('');
      activeSel.value = [...enabledLocales].includes(prev) ? prev : defaultLocale;
      renderActiveLocaleFields();
      syncJson();
    });
  });

  // 切换编辑语言时载入对应内容
  activeSel.addEventListener('change', () => {
    renderActiveLocaleFields();
  });

  // 输入变更写回当前语言
  [subEl, preEl, htmlEl].forEach(el => el.addEventListener('input', () => {
    if (!multiChk.checked) return;
    persistCurrentFields();
  }));

  // 自动翻译
  autoBtn.addEventListener('click', (e) => {
    e.preventDefault();
    applyAutoTranslate(defaultLocale);
    showNotification('已根据默认语言生成其他语言内容（示例翻译）', 'info');
  });
}

function createTemplate() {
  const name = document.getElementById('new-template-name').value;
  const category = document.getElementById('new-template-category').value;
  const subject = document.getElementById('new-template-subject').value;
  const preheader = document.getElementById('new-template-preheader').value;
  const content = document.getElementById('new-template-content').value;
  const isMultiEl = document.getElementById('new-tpl-multi');
  const localeJsonEl = document.getElementById('new-tpl-locale-json');

  if (!name || !subject) {
    showNotification('请填写必填字段', 'error');
    return;
  }

  let localesPayload = null;
  if (isMultiEl && isMultiEl.checked && localeJsonEl && localeJsonEl.value) {
    try { localesPayload = JSON.parse(localeJsonEl.value).locales || null; } catch(e) { localesPayload = null; }
  }

  const newTemplate = {
    id: generateId(),
    name,
    category,
    subject,
    preheader,
    htmlContent: content || '<div>模板内容...</div>',
    status: 'draft',
    usageCount: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    isMultilingual: !!localesPayload,
    locales: localesPayload
  };

  window.AppState.templates.push(newTemplate);
  updateTemplatesGrid();
  document.querySelector('.modal').remove();
  showNotification('模板创建成功！');
}

function editTemplate(templateId) {
  const template = window.AppState.templates.find(t => t.id === templateId);
  if (!template) return;

  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content" style="max-width: 800px;">
      <div class="modal-header">
        <h3>编辑模板 - ${template.name}</h3>
        <button onclick="this.closest('.modal').remove()">×</button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div>
            <label>模板名称 *</label>
            <input id="edit-template-name" value="${template.name}">
          </div>
          <div>
            <label>状态</label>
            <select id="edit-template-status">
              <option value="draft" ${template.status === 'draft' ? 'selected' : ''}>草稿</option>
              <option value="published" ${template.status === 'published' ? 'selected' : ''}>已发布</option>
              <option value="archived" ${template.status === 'archived' ? 'selected' : ''}>已归档</option>
            </select>
          </div>
        </div>

        <div class="row">
          <div>
            <label>分类</label>
            <select id="edit-template-category">
              <option value="promotion" ${template.category === 'promotion' ? 'selected' : ''}>促销活动</option>
              <option value="holiday" ${template.category === 'holiday' ? 'selected' : ''}>节日祝福</option>
              <option value="newsletter" ${template.category === 'newsletter' ? 'selected' : ''}>新闻周报</option>
              <option value="welcome" ${template.category === 'welcome' ? 'selected' : ''}>欢迎邮件</option>
              <option value="notification" ${template.category === 'notification' ? 'selected' : ''}>通知邮件</option>
            </select>
          </div>
          <div>
            <label>使用次数</label>
            <input id="edit-template-usage" type="number" value="${template.usageCount}">
          </div>
        </div>

        <label>邮件主题 *</label>
        <input id="edit-template-subject" value="${template.subject}">

        <label>预览文本</label>
        <input id="edit-template-preheader" value="${template.preheader}">

        <label>邮件内容 (HTML)</label>
        <textarea id="edit-template-content" style="min-height: 200px;">${template.htmlContent}</textarea>

        <div class="card" style="background: var(--panel-2); margin-top: 16px;">
          <div class="mini">预览</div>
          <div style="border: 1px solid var(--border); border-radius: 8px; padding: 16px; background: white;">
            <div style="font-weight: 600; margin-bottom: 8px;">${template.subject}</div>
            <div style="font-size: 12px; color: var(--muted); margin-bottom: 12px;">${template.preheader}</div>
            <div style="border-top: 1px solid var(--border); padding-top: 12px;">
              <div id="template-preview">${template.htmlContent}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="this.closest('.modal').remove()">取消</button>
        <button class="btn secondary" onclick="previewTemplate('${templateId}')">预览</button>
        <button class="btn" onclick="updateTemplate('${templateId}')">保存更改</button>
      </div>
    </div>
  `;
  document.body.appendChild(modal);

  // 实时预览更新
  ['edit-template-subject', 'edit-template-preheader', 'edit-template-content'].forEach(id => {
    document.getElementById(id).addEventListener('input', function() {
      updateTemplatePreview();
    });
  });
}

function updateTemplatePreview() {
  const subject = document.getElementById('edit-template-subject').value;
  const preheader = document.getElementById('edit-template-preheader').value;
  const content = document.getElementById('edit-template-content').value;

  const preview = document.querySelector('.modal .card');
  if (preview) {
    preview.innerHTML = `
      <div class="mini">预览</div>
      <div style="border: 1px solid var(--border); border-radius: 8px; padding: 16px; background: white;">
        <div style="font-weight: 600; margin-bottom: 8px;">${subject}</div>
        <div style="font-size: 12px; color: var(--muted); margin-bottom: 12px;">${preheader}</div>
        <div style="border-top: 1px solid var(--border); padding-top: 12px;">
          <div id="template-preview">${content}</div>
        </div>
      </div>
    `;
  }
}

function updateTemplate(templateId) {
  const template = window.AppState.templates.find(t => t.id === templateId);
  if (!template) return;

  const name = document.getElementById('edit-template-name').value;
  const status = document.getElementById('edit-template-status').value;
  const category = document.getElementById('edit-template-category').value;
  const usage = parseInt(document.getElementById('edit-template-usage').value) || 0;
  const subject = document.getElementById('edit-template-subject').value;
  const preheader = document.getElementById('edit-template-preheader').value;
  const content = document.getElementById('edit-template-content').value;

  if (!name || !subject) {
    showNotification('请填写必填字段', 'error');
    return;
  }

  template.name = name;
  template.status = status;
  template.category = category;
  template.usageCount = usage;
  template.subject = subject;
  template.preheader = preheader;
  template.htmlContent = content;
  template.updatedAt = new Date();

  updateTemplatesGrid();
  document.querySelector('.modal').remove();
  showNotification('模板更新成功！');
}

function previewTemplate(templateId) {
  const template = window.AppState.templates.find(t => t.id === templateId);
  if (!template) return;

  // 在新窗口中打开预览
  const previewWindow = window.open('', '_blank', 'width=600,height=800');
  previewWindow.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>模板预览 - ${template.name}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .email-preview { max-width: 600px; margin: 0 auto; border: 1px solid #ddd; }
        .email-header { background: #f5f5f5; padding: 10px; border-bottom: 1px solid #ddd; }
        .email-content { padding: 20px; }
      </style>
    </head>
    <body>
      <div class="email-preview">
        <div class="email-header">
          <strong>主题:</strong> ${template.subject}<br>
          <strong>预览:</strong> ${template.preheader}
        </div>
        <div class="email-content">
          ${template.htmlContent}
        </div>
      </div>
    </body>
    </html>
  `);
  previewWindow.document.close();
}

// ===== 列表和标签管理功能 =====
function initListsTagsData() {
  if (window.AppState.lists.length === 0) {
    window.AppState.lists = [
      {
        id: '1',
        name: '双11活动',
        description: '双11促销活动相关用户',
        contactCount: 15000,
        createdAt: new Date('2024-07-01'),
        updatedAt: new Date('2024-08-01')
      },
      {
        id: '2',
        name: 'VIP客户',
        description: '高价值VIP客户群体',
        contactCount: 2500,
        createdAt: new Date('2024-06-15'),
        updatedAt: new Date('2024-07-28')
      }
    ];
  }

  if (window.AppState.tags.length === 0) {
    window.AppState.tags = [
      {
        id: '1',
        name: 'VIP',
        color: '#10b981',
        contactCount: 2500,
        createdAt: new Date('2024-06-01')
      },
      {
        id: '2',
        name: '新用户',
        color: '#3b82f6',
        contactCount: 5200,
        createdAt: new Date('2024-07-01')
      },
      {
        id: '3',
        name: '活跃用户',
        color: '#f59e0b',
        contactCount: 8900,
        createdAt: new Date('2024-05-15')
      }
    ];
  }

  updateListsTable();
  updateTagsGrid();
}

function updateListsTable() {
  const tbody = document.querySelector('#lists-table tbody');
  if (!tbody) return;

  const lists = window.AppState.lists;
  tbody.innerHTML = lists.map(list => `
    <tr>
      <td>
        <div style="font-weight: 600;">${list.name}</div>
        <div style="font-size: 12px; color: var(--muted);">${list.description}</div>
      </td>
      <td>${list.contactCount.toLocaleString()}</td>
      <td>${list.createdAt.toLocaleDateString()}</td>
      <td>${list.updatedAt.toLocaleDateString()}</td>
      <td>
        <button class="btn secondary" style="padding: 4px 8px; font-size: 12px;" onclick="editList('${list.id}')">编辑</button>
        <button class="btn secondary" style="padding: 4px 8px; font-size: 12px;" onclick="viewListContacts('${list.id}')">查看联系人</button>
        <button class="btn danger" style="padding: 4px 8px; font-size: 12px;" onclick="deleteList('${list.id}')">删除</button>
      </td>
    </tr>
  `).join('');
}

function updateTagsGrid() {
  const grid = document.querySelector('#tags-grid');
  if (!grid) return;

  const tags = window.AppState.tags;
  grid.innerHTML = tags.map(tag => `
    <div class="card" style="cursor: pointer; transition: all 0.2s ease;" onclick="editTag('${tag.id}')">
      <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;">
        <div style="display: flex; align-items: center; gap: 8px;">
          <div style="width: 12px; height: 12px; border-radius: 50%; background: ${tag.color};"></div>
          <div class="mini">${tag.name}</div>
        </div>
        <div style="font-size: 12px; color: var(--muted);">${tag.contactCount} 个联系人</div>
      </div>
      <div style="height: 80px; background: linear-gradient(135deg, ${tag.color}20, ${tag.color}10); border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-bottom: 12px;">
        <div style="text-align: center; color: ${tag.color};">
          <div style="font-size: 24px; margin-bottom: 8px;">🏷️</div>
          <div style="font-weight: 600;">${tag.name}</div>
        </div>
      </div>
      <div class="hint">点击编辑 | 创建于 ${tag.createdAt.toLocaleDateString()}</div>
    </div>
  `).join('');
}

function createNewList() {
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h3>创建新列表</h3>
        <button onclick="this.closest('.modal').remove()">×</button>
      </div>
      <div class="modal-body">
        <label>列表名称 *</label>
        <input id="new-list-name" placeholder="请输入列表名称">

        <label>描述</label>
        <textarea id="new-list-description" placeholder="请输入列表描述" style="min-height: 80px;"></textarea>

        <label>初始联系人</label>
        <select id="new-list-contacts">
          <option value="">暂不添加联系人</option>
          <option value="all">所有联系人</option>
          <option value="active">活跃联系人</option>
          <option value="recent">最近添加的联系人</option>
        </select>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="this.closest('.modal').remove()">取消</button>
        <button class="btn" onclick="createList()">创建列表</button>
      </div>
    </div>
  `;
  document.body.appendChild(modal);
}

function createList() {
  const name = document.getElementById('new-list-name').value;
  const description = document.getElementById('new-list-description').value;
  const contacts = document.getElementById('new-list-contacts').value;

  if (!name) {
    showNotification('请填写列表名称', 'error');
    return;
  }

  const contactCount = getListContactCount(contacts);

  const newList = {
    id: generateId(),
    name,
    description,
    contactCount,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  window.AppState.lists.push(newList);
  updateListsTable();
  document.querySelector('.modal').remove();
  showNotification('列表创建成功！');
}

function getListContactCount(contacts) {
  switch(contacts) {
    case 'all': return window.AppState.contacts.length || 12000;
    case 'active': return Math.floor((window.AppState.contacts.length || 12000) * 0.7);
    case 'recent': return Math.floor((window.AppState.contacts.length || 12000) * 0.1);
    default: return 0;
  }
}

function createNewTag() {
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h3>创建新标签</h3>
        <button onclick="this.closest('.modal').remove()">×</button>
      </div>
      <div class="modal-body">
        <label>标签名称 *</label>
        <input id="new-tag-name" placeholder="请输入标签名称">

        <label>标签颜色</label>
        <div class="row">
          <input id="new-tag-color" type="color" value="#3b82f6" style="width: 60px; height: 40px; padding: 4px;">
          <input id="new-tag-color-text" value="#3b82f6" placeholder="#3b82f6">
        </div>

        <label>应用到现有联系人</label>
        <select id="new-tag-apply">
          <option value="">暂不应用</option>
          <option value="all">所有联系人</option>
          <option value="active">活跃联系人</option>
          <option value="vip">VIP联系人</option>
        </select>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="this.closest('.modal').remove()">取消</button>
        <button class="btn" onclick="createTag()">创建标签</button>
      </div>
    </div>
  `;
  document.body.appendChild(modal);

  // 颜色选择器同步
  document.getElementById('new-tag-color').addEventListener('change', function() {
    document.getElementById('new-tag-color-text').value = this.value;
  });

  document.getElementById('new-tag-color-text').addEventListener('change', function() {
    document.getElementById('new-tag-color').value = this.value;
  });
}

function createTag() {
  const name = document.getElementById('new-tag-name').value;
  const color = document.getElementById('new-tag-color').value;
  const apply = document.getElementById('new-tag-apply').value;

  if (!name) {
    showNotification('请填写标签名称', 'error');
    return;
  }

  // 检查标签名称是否已存在
  if (window.AppState.tags.some(t => t.name === name)) {
    showNotification('标签名称已存在', 'error');
    return;
  }

  const contactCount = getTagContactCount(apply);

  const newTag = {
    id: generateId(),
    name,
    color,
    contactCount,
    createdAt: new Date()
  };

  window.AppState.tags.push(newTag);

  // 应用到现有联系人
  if (apply && contactCount > 0) {
    applyTagToContacts(newTag.id, apply);
  }

  updateTagsGrid();
  document.querySelector('.modal').remove();
  showNotification('标签创建成功！');
}

function getTagContactCount(apply) {
  switch(apply) {
    case 'all': return window.AppState.contacts.length || 8000;
    case 'active': return Math.floor((window.AppState.contacts.length || 8000) * 0.7);
    case 'vip': return Math.floor((window.AppState.contacts.length || 8000) * 0.15);
    default: return 0;
  }
}

function applyTagToContacts(tagId, apply) {
  const tag = window.AppState.tags.find(t => t.id === tagId);
  if (!tag) return;

  // 这里可以实现具体的标签应用逻辑
  // 由于是演示，我们只是更新联系人的标签数组
  window.AppState.contacts.forEach(contact => {
    if (shouldApplyTag(contact, apply)) {
      if (!contact.tags.includes(tag.name)) {
        contact.tags.push(tag.name);
      }
    }
  });

  updateContactsTable();
}

function shouldApplyTag(contact, apply) {
  switch(apply) {
    case 'all': return true;
    case 'active': return contact.status === 'active';
    case 'vip': return contact.tags.includes('VIP');
    default: return false;
  }
}

function editTag(tagId) {
  const tag = window.AppState.tags.find(t => t.id === tagId);
  if (!tag) return;

  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h3>编辑标签</h3>
        <button onclick="this.closest('.modal').remove()">×</button>
      </div>
      <div class="modal-body">
        <label>标签名称 *</label>
        <input id="edit-tag-name" value="${tag.name}">

        <label>标签颜色</label>
        <div class="row">
          <input id="edit-tag-color" type="color" value="${tag.color}" style="width: 60px; height: 40px; padding: 4px;">
          <input id="edit-tag-color-text" value="${tag.color}">
        </div>

        <label>联系人数量</label>
        <input id="edit-tag-count" type="number" value="${tag.contactCount}">

        <div class="card" style="background: var(--panel-2);">
          <div class="mini">预览</div>
          <div style="display: flex; align-items: center; gap: 8px;">
            <div id="tag-preview-color" style="width: 12px; height: 12px; border-radius: 50%; background: ${tag.color};"></div>
            <span id="tag-preview-name">${tag.name}</span>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="this.closest('.modal').remove()">取消</button>
        <button class="btn danger" onclick="deleteTag('${tagId}')">删除标签</button>
        <button class="btn" onclick="updateTag('${tagId}')">保存更改</button>
      </div>
    </div>
  `;
  document.body.appendChild(modal);

  // 实时预览更新
  ['edit-tag-name', 'edit-tag-color', 'edit-tag-color-text'].forEach(id => {
    document.getElementById(id).addEventListener('input', function() {
      updateTagPreview();
    });
  });

  // 颜色选择器同步
  document.getElementById('edit-tag-color').addEventListener('change', function() {
    document.getElementById('edit-tag-color-text').value = this.value;
    updateTagPreview();
  });

  document.getElementById('edit-tag-color-text').addEventListener('change', function() {
    document.getElementById('edit-tag-color').value = this.value;
    updateTagPreview();
  });
}

function updateTagPreview() {
  const name = document.getElementById('edit-tag-name').value;
  const color = document.getElementById('edit-tag-color').value;

  document.getElementById('tag-preview-name').textContent = name;
  document.getElementById('tag-preview-color').style.background = color;
}

function updateTag(tagId) {
  const tag = window.AppState.tags.find(t => t.id === tagId);
  if (!tag) return;

  const name = document.getElementById('edit-tag-name').value;
  const color = document.getElementById('edit-tag-color').value;
  const count = parseInt(document.getElementById('edit-tag-count').value) || 0;

  if (!name) {
    showNotification('请填写标签名称', 'error');
    return;
  }

  // 检查标签名称是否与其他标签冲突
  if (window.AppState.tags.some(t => t.name === name && t.id !== tagId)) {
    showNotification('标签名称已存在', 'error');
    return;
  }

  const oldName = tag.name;
  tag.name = name;
  tag.color = color;
  tag.contactCount = count;

  // 更新联系人中的标签名称
  if (oldName !== name) {
    window.AppState.contacts.forEach(contact => {
      const index = contact.tags.indexOf(oldName);
      if (index !== -1) {
        contact.tags[index] = name;
      }
    });
    updateContactsTable();
  }

  updateTagsGrid();
  document.querySelector('.modal').remove();
  showNotification('标签更新成功！');
}

function deleteTag(tagId) {
  const tag = window.AppState.tags.find(t => t.id === tagId);
  if (!tag) return;

  if (confirm(`确定要删除标签"${tag.name}"吗？这将从所有联系人中移除此标签。`)) {
    // 从所有联系人中移除此标签
    window.AppState.contacts.forEach(contact => {
      contact.tags = contact.tags.filter(t => t !== tag.name);
    });

    // 删除标签
    window.AppState.tags = window.AppState.tags.filter(t => t.id !== tagId);

    updateTagsGrid();
    updateContactsTable();
    document.querySelector('.modal').remove();
    showNotification('标签已删除');
  }
}

// 自动化旅程可视化页面初始化
function initJourneyVisualData() {
  // 初始化画布和节点
  if (typeof initJourneyCanvas === 'function') {
    initJourneyCanvas();
  }
  
  // 加载示例数据
  loadSampleJourneyData();
}

// 加载示例旅程数据
function loadSampleJourneyData() {
  // 这里可以加载预设的旅程数据
  console.log('加载自动化旅程示例数据');
}


