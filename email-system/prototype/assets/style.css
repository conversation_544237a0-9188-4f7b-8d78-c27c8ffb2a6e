:root { 
  --bg: #f8fafc;
  --panel: #ffffff;
  --panel-2: #f1f5f9;
  --muted: #64748b;
  --text: #0f172a;
  --text-secondary: #475569;
  --pri: #3b82f6;
  --pri-light: #dbeafe;
  --pri-dark: #1d4ed8;
  --success: #10b981;
  --success-light: #d1fae5;
  --warning: #f59e0b;
  --warning-light: #fef3c7;
  --danger: #ef4444;
  --danger-light: #fee2e2;
  --border: #e2e8f0;
  --border-light: #f1f5f9;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  background: var(--bg);
  color: var(--text);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Header */
header {
  height: 64px;
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 0 24px;
  background: var(--panel);
  border-bottom: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

header .brand {
  font-weight: 700;
  font-size: 18px;
  letter-spacing: -0.025em;
  color: var(--pri);
  display: flex;
  align-items: center;
  gap: 8px;
}

header .brand::before {
  content: "📧";
  font-size: 20px;
}

header input {
  flex: 1;
  max-width: 480px;
  background: var(--panel-2);
  border: 1px solid var(--border);
  border-radius: 12px;
  color: var(--text);
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.2s ease;
}

header input:focus {
  outline: none;
  border-color: var(--pri);
  box-shadow: 0 0 0 3px var(--pri-light);
  background: var(--panel);
}

header input::placeholder {
  color: var(--muted);
}

/* Layout */
.layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  height: calc(100vh - 64px);
}

/* Navigation */
nav {
  background: var(--panel);
  border-right: 1px solid var(--border);
  padding: 20px 0;
  overflow-y: auto;
  box-shadow: var(--shadow-sm);
}

nav .group {
  margin: 24px 0 12px 24px;
  color: var(--muted);
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

nav a {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  color: var(--text-secondary);
  padding: 12px 24px;
  margin: 2px 12px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}

nav a:hover {
  background: var(--panel-2);
  color: var(--text);
  transform: translateX(2px);
}

nav a.active {
  background: var(--pri-light);
  color: var(--pri-dark);
  font-weight: 600;
}

nav a.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--pri);
  border-radius: 0 2px 2px 0;
}

/* Main Content */
main {
  padding: 24px;
  overflow-y: auto;
  background: var(--bg);
}

/* Cards */
.card {
  background: var(--panel);
  border: 1px solid var(--border);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: var(--shadow);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* Toolbar */
.toolbar {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin: 16px 0 24px;
  align-items: center;
}

/* Buttons */
.btn {
  background: var(--pri);
  border: 0;
  color: white;
  padding: 10px 16px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  box-shadow: var(--shadow-sm);
}

.btn:hover {
  background: var(--pri-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(0);
}

.btn.secondary {
  background: var(--panel);
  color: var(--text-secondary);
  border: 1px solid var(--border);
}

.btn.secondary:hover {
  background: var(--panel-2);
  border-color: var(--pri);
  color: var(--pri);
}

.btn.warn {
  background: var(--warning);
}

.btn.warn:hover {
  background: #d97706;
}

.btn.danger {
  background: var(--danger);
}

.btn.danger:hover {
  background: #dc2626;
}

.btn.ghost {
  background: transparent;
  border: 1px dashed var(--border);
  color: var(--text-secondary);
}

.btn.ghost:hover {
  background: var(--panel-2);
  border-color: var(--pri);
  color: var(--pri);
}

/* Grid */
.grid {
  display: grid;
  gap: 20px;
}

.grid.cols-2 {
  grid-template-columns: 1fr 1fr;
}

.grid.cols-3 {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid.cols-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Tables */
.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background: var(--panel);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
}

.table th,
.table td {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-light);
  font-size: 14px;
  text-align: left;
}

.table th {
  background: var(--panel-2);
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Sortable headers */
.table th.sortable {
  cursor: pointer;
  user-select: none;
}
.table th.sortable .sort-indicator {
  margin-left: 6px;
  font-size: 12px;
  color: var(--muted);
}
.table th.sortable.active {
  color: var(--pri-dark);
}
.table th.sortable.active .sort-indicator {
  color: var(--pri);
}

.table tbody tr {
  transition: background-color 0.2s ease;
}

.table tbody tr:hover {
  background: var(--panel-2);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* KPI Cards */
.kpi {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.kpi .item {
  background: var(--panel);
  border: 1px solid var(--border);
  border-radius: 16px;
  padding: 24px;
  box-shadow: var(--shadow);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.kpi .item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--pri), var(--pri-light));
}

.kpi .item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.kpi .num {
  font-size: 32px;
  font-weight: 800;
  color: var(--text);
  line-height: 1;
  margin-bottom: 8px;
}

.kpi .mini {
  font-size: 13px;
  color: var(--muted);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Form Elements */
label {
  display: block;
  margin: 16px 0 8px;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 14px;
}

input[type="text"],
input[type="number"],
input[type="email"],
select,
textarea {
  width: 100%;
  background: var(--panel);
  border: 1px solid var(--border);
  color: var(--text);
  border-radius: 10px;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.2s ease;
  font-family: inherit;
}

input[type="text"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--pri);
  box-shadow: 0 0 0 3px var(--pri-light);
}

input[type="text"]::placeholder,
input[type="number"]::placeholder,
input[type="email"]::placeholder {
  color: var(--muted);
}

textarea {
  min-height: 120px;
  resize: vertical;
}

/* Rows */
.row {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  margin-bottom: 16px;
}

.row > * {
  flex: 1;
}

.row .flex-0 {
  flex: 0;
}

/* Dividers */
.divider {
  height: 1px;
  background: var(--border);
  margin: 24px 0;
  opacity: 0.6;
}

/* Pagination */
.pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 12px;
}
.pagination .pages {
  display: flex;
  gap: 6px;
}
.pagination .pages .page-btn {
  min-width: 32px;
  height: 32px;
  padding: 0 10px;
  border-radius: 8px;
  border: 1px solid var(--border);
  background: var(--panel);
  color: var(--text-secondary);
  cursor: pointer;
}
.pagination .pages .page-btn.active {
  background: var(--pri-light);
  color: var(--pri-dark);
  border-color: var(--pri);
}
.pagination .info {
  color: var(--muted);
  font-size: 13px;
}

/* Toasts */
.toast-container {
  position: fixed;
  right: 20px;
  bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 1000;
}
.toast {
  background: var(--panel);
  border: 1px solid var(--border);
  border-left: 4px solid var(--pri);
  padding: 12px 14px;
  border-radius: 10px;
  box-shadow: var(--shadow-md);
  min-width: 260px;
}
.toast.success { border-left-color: var(--success); }
.toast.warn { border-left-color: var(--warning); }
.toast.error { border-left-color: var(--danger); }
.toast .title { font-weight: 700; margin-bottom: 4px; }
.toast .msg { font-size: 13px; color: var(--text-secondary); }

/* Skeletons */
.skeleton {
  display: inline-block;
  height: 12px;
  width: 100%;
  background: linear-gradient(90deg, #eee, #f5f5f5, #eee);
  background-size: 200% 100%;
  animation: skeleton 1.2s ease-in-out infinite;
  border-radius: 6px;
}
@keyframes skeleton {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Pills */
.pill {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 999px;
  background: var(--panel-2);
  border: 1px solid var(--border);
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-right: 8px;
  margin-bottom: 4px;
}

.pill.ok {
  background: var(--success-light);
  border-color: var(--success);
  color: var(--success);
}

.pill.warnTxt {
  background: var(--warning-light);
  border-color: var(--warning);
  color: var(--warning);
}

.pill.err {
  background: var(--danger-light);
  border-color: var(--danger);
  color: var(--danger);
}

/* Status Colors */
.ok { color: var(--success); }
.warnTxt { color: var(--warning); }
.err { color: var(--danger); }

/* Hints */
.hint {
  color: var(--muted);
  font-size: 13px;
  margin-top: 8px;
  line-height: 1.4;
}

/* Tabs */
.tabs {
  display: flex;
  gap: 4px;
  margin: 16px 0 20px;
  background: var(--panel-2);
  padding: 4px;
  border-radius: 12px;
  border: 1px solid var(--border);
}

.tab {
  padding: 10px 16px;
  border-radius: 8px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  flex: 1;
  text-align: center;
}

.tab:hover {
  background: var(--panel);
  color: var(--text);
}

.tab.active {
  background: var(--panel);
  color: var(--pri);
  box-shadow: var(--shadow-sm);
}

/* Special Elements */
.sticky {
  position: sticky;
  top: 20px;
}

.ghost {
  opacity: 0.7;
}

/* Responsive */
/* Modal */
.modal {
  position: fixed;
  inset: 0;
  background: rgba(15, 23, 42, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  z-index: 200;
}

.dialog {
  background: var(--panel);
  border: 1px solid var(--border);
  border-radius: 16px;
  width: min(960px, 100%);
  max-height: 80vh;
  overflow: auto;
  box-shadow: var(--shadow-lg);
}

.dialog .header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  background: var(--panel);
  z-index: 1;
}

.dialog .body {
  padding: 20px;
}

.dialog .footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 16px 20px;
  border-top: 1px solid var(--border);
}

@media (max-width: 1024px) {
  .layout {
    grid-template-columns: 1fr;
  }
  
  nav {
    display: none;
  }
  
  .grid.cols-2,
  .grid.cols-3 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  main {
    padding: 16px;
  }
  
  .card {
    padding: 16px;
  }
  
  .kpi .item {
    padding: 20px;
  }
  
  .kpi .num {
    font-size: 28px;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.view {
  animation: fadeIn 0.3s ease-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--panel-2);
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--muted);
}

/* Enhanced Modal Styles */
.modal-content {
  background: var(--panel);
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.modal-header button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--muted);
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.modal-header button:hover {
  background: var(--panel-2);
}

.modal-body {
  padding: 20px 24px;
}

.modal-footer {
  padding: 16px 24px 20px;
  border-top: 1px solid var(--border);
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* Notification Styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--panel);
  border: 1px solid var(--border);
  border-radius: 12px;
  box-shadow: var(--shadow-lg);
  z-index: 1001;
  min-width: 300px;
  animation: slideIn 0.3s ease;
}

.notification.success {
  border-color: var(--success);
  background: var(--success-light);
}

.notification.error {
  border-color: var(--danger);
  background: var(--danger-light);
}

.notification.warning {
  border-color: var(--warning);
  background: var(--warning-light);
}

.notification.info {
  border-color: var(--pri);
  background: var(--pri-light);
}

.notification-content {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.notification-content button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: var(--muted);
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.notification-content button:hover {
  background: rgba(0, 0, 0, 0.1);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Enhanced Pills */
.pill.success {
  background: var(--success-light);
  border-color: var(--success);
  color: var(--success);
}

.pill.secondary {
  background: var(--panel-2);
  border-color: var(--border);
  color: var(--text-secondary);
}


