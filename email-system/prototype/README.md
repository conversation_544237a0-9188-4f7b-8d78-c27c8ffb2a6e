# 邮件营销系统原型

这是一个完整的邮件营销系统原型，包含所有核心功能模块的交互式界面设计。

## 功能模块

### 1. 多租户与权限管理
- 租户管理
- 用户权限控制
- 角色管理

### 2. 联系人管理
- 联系人列表
- 字段管理
- 标签管理
- 导入导出

### 3. 列表与标签
- 联系人列表管理
- 标签系统
- 细分管理

### 4. 细分管理
- 动态细分
- 行为细分
- 属性细分

### 5. 抑制与同意管理
- 退订管理
- 同意管理
- 投诉处理

### 6. 模板与内容
- 邮件模板设计
- 多语言支持
- 模板版本管理

### 7. 活动与AB测试
- 邮件活动创建
- A/B测试配置
- 活动管理

### 8. 自动化旅程 🆕
- **可视化节点编辑器** - 拖拽式流程设计
- 旅程配置
- 触发条件设置
- 节点连接管理

### 9. 投递与可达性
- 发件渠道管理
- 域名管理
- IP池管理
- 限速策略

### 10. 追踪与分析
- 打开率追踪
- 点击率分析
- 报表生成

### 11. 表单与偏好中心
- 订阅表单
- 偏好设置
- 退订页面

### 12. 开放平台与集成
- API接口
- Webhook配置
- 第三方集成

### 13. 运维与管理
- 系统监控
- 日志管理
- 性能优化

### 14. 扩展功能
- 插件系统
- 自定义扩展
- 功能模块化

### 15. 计费与额度
- 使用量统计
- 计费规则
- 额度管理

### 16. 安全与合规
- 数据加密
- 合规检查
- 安全审计

## 新增功能

### 发件渠道管理
- 支持多种业务场景（营销、交易、系统）
- 资源隔离和独立管理
- 渠道权限控制

### 自动化旅程可视化编辑器 🆕
- **拖拽式节点设计**：支持拖拽创建和连接节点
- **多种节点类型**：触发、邮件、延迟、条件、目标等
- **实时属性配置**：右侧属性面板实时编辑节点属性
- **画布控制**：缩放、居中、适应屏幕等画布操作
- **连接线管理**：自动绘制节点间的连接关系

## 使用方法

1. 启动本地服务器：
   ```bash
   cd prototype
   python3 -m http.server 8000
   ```

2. 在浏览器中访问：`http://localhost:8000`

3. 导航到相应功能模块：
   - 点击"🔁 自动化"进入自动化旅程页面
   - 在自动化页面点击"🎨 可视化编辑器"进入可视化设计器
   - 或直接访问 `#journey-visual` 路由

## 技术特点

- **响应式设计**：支持各种屏幕尺寸
- **模块化架构**：功能模块独立，易于扩展
- **交互式原型**：完整的用户交互流程
- **现代化UI**：使用CSS Grid和Flexbox布局
- **JavaScript驱动**：动态内容加载和状态管理

## 文件结构

```
prototype/
├── index.html          # 主导航页面
├── assets/
│   ├── app.js         # 全局路由和功能
│   └── style.css      # 全局样式
├── pages/             # 功能页面
│   ├── contacts.html  # 联系人管理
│   ├── campaigns.html # 活动管理
│   ├── journeys.html  # 自动化旅程
│   ├── journey-visual.html # 🆕 可视化编辑器
│   ├── channels.html  # 🆕 发件渠道管理
│   └── ...           # 其他功能页面
└── README.md          # 说明文档
```

## 开发说明

- 所有页面都支持路由导航
- 使用 `loadPage()` 函数进行页面切换
- 每个页面都有独立的JavaScript功能
- 支持内联脚本执行和全局函数调用
- 使用事件委托和模块化设计模式
