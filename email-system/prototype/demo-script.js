// Email System 原型演示脚本
// 用于自动演示系统的主要功能

class DemoScript {
  constructor() {
    this.currentStep = 0;
    this.steps = [
      {
        title: "欢迎使用 Email System 原型",
        description: "这是一个完整的邮件营销系统原型，包含联系人管理、活动创建、模板编辑等功能。",
        action: () => this.showWelcome()
      },
      {
        title: "联系人管理演示",
        description: "演示如何创建和管理联系人",
        action: () => this.demoContacts()
      },
      {
        title: "标签管理演示", 
        description: "演示如何创建标签并应用到联系人",
        action: () => this.demoTags()
      },
      {
        title: "模板创建演示",
        description: "演示如何创建邮件模板",
        action: () => this.demoTemplates()
      },
      {
        title: "活动创建演示",
        description: "演示如何创建和发送邮件活动",
        action: () => this.demoCampaigns()
      },
      {
        title: "演示完成",
        description: "感谢观看！您现在可以自由探索系统的各项功能。",
        action: () => this.showComplete()
      }
    ];
  }

  start() {
    this.showDemoPanel();
    this.executeStep(0);
  }

  showDemoPanel() {
    const panel = document.createElement('div');
    panel.id = 'demo-panel';
    panel.style.cssText = `
      position: fixed;
      top: 20px;
      left: 20px;
      width: 300px;
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 12px;
      box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
      z-index: 1000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
    
    panel.innerHTML = `
      <div style="padding: 16px; border-bottom: 1px solid #e2e8f0;">
        <h3 style="margin: 0; font-size: 16px; color: #1f2937;">🎬 系统演示</h3>
      </div>
      <div id="demo-content" style="padding: 16px;">
        <div id="demo-title" style="font-weight: 600; margin-bottom: 8px;"></div>
        <div id="demo-description" style="font-size: 14px; color: #6b7280; margin-bottom: 16px;"></div>
        <div style="display: flex; gap: 8px;">
          <button id="demo-prev" style="padding: 6px 12px; border: 1px solid #d1d5db; background: white; border-radius: 6px; cursor: pointer;">上一步</button>
          <button id="demo-next" style="padding: 6px 12px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer;">下一步</button>
          <button id="demo-close" style="padding: 6px 12px; border: 1px solid #d1d5db; background: white; border-radius: 6px; cursor: pointer;">关闭</button>
        </div>
        <div style="margin-top: 12px; font-size: 12px; color: #9ca3af;">
          步骤 <span id="demo-step">1</span> / <span id="demo-total">${this.steps.length}</span>
        </div>
      </div>
    `;
    
    document.body.appendChild(panel);
    
    // 绑定事件
    document.getElementById('demo-prev').onclick = () => this.prevStep();
    document.getElementById('demo-next').onclick = () => this.nextStep();
    document.getElementById('demo-close').onclick = () => this.close();
  }

  executeStep(stepIndex) {
    if (stepIndex < 0 || stepIndex >= this.steps.length) return;
    
    this.currentStep = stepIndex;
    const step = this.steps[stepIndex];
    
    // 更新面板内容
    document.getElementById('demo-title').textContent = step.title;
    document.getElementById('demo-description').textContent = step.description;
    document.getElementById('demo-step').textContent = stepIndex + 1;
    
    // 更新按钮状态
    document.getElementById('demo-prev').disabled = stepIndex === 0;
    document.getElementById('demo-next').disabled = stepIndex === this.steps.length - 1;
    
    // 执行步骤动作
    step.action();
  }

  nextStep() {
    if (this.currentStep < this.steps.length - 1) {
      this.executeStep(this.currentStep + 1);
    }
  }

  prevStep() {
    if (this.currentStep > 0) {
      this.executeStep(this.currentStep - 1);
    }
  }

  close() {
    const panel = document.getElementById('demo-panel');
    if (panel) {
      panel.remove();
    }
  }

  // 演示步骤实现
  showWelcome() {
    // 高亮导航菜单
    this.highlightElement('nav', '这是系统的主导航菜单，包含了所有功能模块');
  }

  demoContacts() {
    // 导航到联系人页面
    window.location.hash = '#contacts';
    setTimeout(() => {
      this.highlightElement('.toolbar', '点击这里的按钮可以创建新联系人或进行批量操作');
    }, 500);
  }

  demoTags() {
    // 导航到标签页面
    window.location.hash = '#lists-tags';
    setTimeout(() => {
      this.highlightElement('.toolbar', '在这里可以创建和管理标签，标签可以用于分类联系人');
    }, 500);
  }

  demoTemplates() {
    // 导航到模板页面
    window.location.hash = '#templates';
    setTimeout(() => {
      this.highlightElement('#templates-grid', '这里显示所有邮件模板，点击可以编辑模板内容');
    }, 500);
  }

  demoCampaigns() {
    // 导航到活动页面
    window.location.hash = '#campaigns';
    setTimeout(() => {
      this.highlightElement('#campaigns-table', '活动列表显示所有邮件活动的状态和统计数据');
    }, 500);
  }

  showComplete() {
    this.removeHighlight();
    // 可以添加完成后的操作
  }

  // 工具方法
  highlightElement(selector, tooltip) {
    this.removeHighlight();
    
    const element = document.querySelector(selector);
    if (!element) return;
    
    // 添加高亮样式
    element.style.position = 'relative';
    element.style.zIndex = '999';
    element.style.boxShadow = '0 0 0 3px #3b82f6, 0 0 20px rgba(59, 130, 246, 0.3)';
    element.style.borderRadius = '8px';
    element.classList.add('demo-highlight');
    
    // 添加提示
    if (tooltip) {
      const tip = document.createElement('div');
      tip.className = 'demo-tooltip';
      tip.style.cssText = `
        position: absolute;
        top: -40px;
        left: 50%;
        transform: translateX(-50%);
        background: #1f2937;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1001;
      `;
      tip.textContent = tooltip;
      element.appendChild(tip);
    }
  }

  removeHighlight() {
    const highlighted = document.querySelectorAll('.demo-highlight');
    highlighted.forEach(el => {
      el.style.boxShadow = '';
      el.style.zIndex = '';
      el.classList.remove('demo-highlight');
      
      const tooltip = el.querySelector('.demo-tooltip');
      if (tooltip) {
        tooltip.remove();
      }
    });
  }
}

// 自动启动演示（可选）
// window.addEventListener('load', () => {
//   setTimeout(() => {
//     const demo = new DemoScript();
//     demo.start();
//   }, 2000);
// });

// 手动启动演示的函数
window.startDemo = function() {
  const demo = new DemoScript();
  demo.start();
};

// 在控制台提示用户可以启动演示
console.log('💡 提示：输入 startDemo() 可以启动系统功能演示');
