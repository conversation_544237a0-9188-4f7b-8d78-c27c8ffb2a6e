-- Email-System 数据库设计
-- 基于 Clean Architecture + DDD 的邮件营销系统
-- 多租户支持，所有表包含 tenant_id 进行租户隔离

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ========================================
-- 联系人管理模块
-- ========================================

-- ----------------------------
-- Table structure for contacts
-- ----------------------------
CREATE TABLE IF NOT EXISTS `contacts` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `email` VARCHAR(255) NOT NULL COMMENT '邮箱地址',
  `status` VARCHAR(32) NOT NULL DEFAULT 'active' COMMENT '联系人状态: active/suppressed/unconfirmed/bounced/complained/unsubscribed',
  `preferred_language` VARCHAR(16) NOT NULL DEFAULT 'zh-CN' COMMENT '首选语言',
  `country_code` VARCHAR(2) NULL COMMENT '国家代码',
  `timezone` VARCHAR(64) NULL COMMENT '时区',
  `notes` TEXT NULL COMMENT '备注',
  `attributes` JSON NULL COMMENT '自定义属性',
  
  -- 退订和同意相关字段
  `unsubscribe_token` VARCHAR(64) NULL COMMENT '退订令牌',
  `unsubscribed_at` DATETIME NULL COMMENT '退订时间',
  `consent_status` VARCHAR(32) NOT NULL DEFAULT 'unknown' COMMENT '同意状态: unknown/granted/withdrawn/pending',
  `consent_source` VARCHAR(64) NULL COMMENT '同意来源',
  `consent_at` DATETIME NULL COMMENT '同意时间',
  `consent_ip` VARCHAR(45) NULL COMMENT '同意IP',
  `consent_user_agent` VARCHAR(512) NULL COMMENT '同意用户代理',
  
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY `uk_contact_email` (`tenant_id`, `email`),
  KEY `idx_contact_tenant` (`tenant_id`),
  KEY `idx_contact_status` (`tenant_id`, `status`),
  KEY `idx_contact_unsubscribe_token` (`unsubscribe_token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联系人表';

-- ========================================
-- 标签管理模块
-- ========================================

-- ----------------------------
-- Table structure for tags
-- ----------------------------
CREATE TABLE IF NOT EXISTS `tags` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `name` VARCHAR(64) NOT NULL COMMENT '标签名称',
  `description` VARCHAR(255) NULL COMMENT '标签描述',
  `type` VARCHAR(32) NOT NULL DEFAULT 'rule_based' COMMENT '标签类型: rule_based/static_list',
  `color` VARCHAR(16) NULL COMMENT '标签颜色',
  `rule_tree_json` JSON NULL COMMENT '规则树JSON',
  `refresh_policy` VARCHAR(64) NULL COMMENT '刷新策略: schedule/trigger/once',
  `last_refresh_at` DATETIME NULL COMMENT '最后刷新时间',
  `member_count` BIGINT NOT NULL DEFAULT 0 COMMENT '成员数量',
  `usage_count` BIGINT NOT NULL DEFAULT 0 COMMENT '使用次数',
  `created_by` BIGINT NOT NULL COMMENT '创建人',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY `uk_tag_name` (`tenant_id`, `name`),
  KEY `idx_tag_tenant` (`tenant_id`),
  KEY `idx_tag_type` (`tenant_id`, `type`),
  KEY `idx_tag_refresh` (`tenant_id`, `refresh_policy`, `last_refresh_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

-- ----------------------------
-- Table structure for contact_tags
-- ----------------------------
CREATE TABLE IF NOT EXISTS `contact_tags` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `contact_id` BIGINT NOT NULL COMMENT '联系人ID',
  `tag_id` BIGINT NOT NULL COMMENT '标签ID',
  `added_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  
  UNIQUE KEY `uk_contact_tag` (`tenant_id`, `contact_id`, `tag_id`),
  KEY `idx_contact_tag_tenant` (`tenant_id`),
  KEY `idx_contact_tag_contact` (`tenant_id`, `contact_id`),
  KEY `idx_contact_tag_tag` (`tenant_id`, `tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联系人标签关联表';

-- ========================================
-- 人群圈选模块
-- ========================================

-- ----------------------------
-- Table structure for segments
-- ----------------------------
CREATE TABLE IF NOT EXISTS `segments` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `name` VARCHAR(128) NOT NULL COMMENT '人群圈选名称',
  `tag_id` BIGINT NOT NULL COMMENT '关联标签ID',
  `rule_json` JSON NOT NULL COMMENT '圈选规则JSON',
  `type` VARCHAR(32) NOT NULL COMMENT '类型: dynamic/static',
  `refresh_policy` VARCHAR(64) NULL COMMENT '刷新策略: schedule/trigger/once',
  `status` VARCHAR(32) NOT NULL DEFAULT 'active' COMMENT '状态: active/inactive/archived',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  KEY `idx_segment_tenant` (`tenant_id`),
  KEY `idx_segment_tag` (`tenant_id`, `tag_id`),
  KEY `idx_segment_type` (`tenant_id`, `type`),
  KEY `idx_segment_status` (`tenant_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人群圈选表';

-- ----------------------------
-- Table structure for segment_snapshots
-- ----------------------------
CREATE TABLE IF NOT EXISTS `segment_snapshots` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `segment_id` BIGINT NOT NULL COMMENT '人群圈选ID',
  `data_json` LONGTEXT NOT NULL COMMENT '快照数据JSON(联系人ID列表)',
  `size` BIGINT NOT NULL COMMENT '快照大小',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `expires_at` DATETIME NULL COMMENT '过期时间',
  
  KEY `idx_segment_snapshot_tenant` (`tenant_id`),
  KEY `idx_segment_snapshot_segment` (`tenant_id`, `segment_id`),
  KEY `idx_segment_snapshot_expires` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人群圈选快照表';

-- ----------------------------
-- Table structure for segment_jobs
-- ----------------------------
CREATE TABLE IF NOT EXISTS `segment_jobs` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `segment_id` BIGINT NOT NULL COMMENT '人群圈选ID',
  `job_type` VARCHAR(32) NOT NULL COMMENT '任务类型: preview/rebuild',
  `status` VARCHAR(32) NOT NULL DEFAULT 'queued' COMMENT '状态: queued/running/completed/failed/cancelled',
  `progress` INT NOT NULL DEFAULT 0 COMMENT '进度百分比',
  `result_json` JSON NULL COMMENT '任务结果JSON',
  `error_msg` TEXT NULL COMMENT '错误信息',
  `started_at` DATETIME NULL COMMENT '开始时间',
  `finished_at` DATETIME NULL COMMENT '完成时间',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  KEY `idx_segment_job_tenant` (`tenant_id`),
  KEY `idx_segment_job_segment` (`tenant_id`, `segment_id`),
  KEY `idx_segment_job_status` (`tenant_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='人群圈选任务表';

-- ========================================
-- 模板管理模块 (Email-System内部表)
-- ========================================

-- ----------------------------
-- Table structure for template_locales
-- ----------------------------
CREATE TABLE IF NOT EXISTS `template_locales` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `template_id` BIGINT NOT NULL COMMENT '外部Email模块模板ID',
  `locale` VARCHAR(16) NOT NULL COMMENT '语言代码',
  
  -- 多语言内容
  `name` VARCHAR(255) NOT NULL COMMENT '模板名称',
  `subject` VARCHAR(512) NOT NULL COMMENT '邮件主题',
  `pre_header` VARCHAR(255) NULL COMMENT '预览文本',
  `html_content` LONGTEXT NULL COMMENT 'HTML内容',
  `text_content` LONGTEXT NULL COMMENT '纯文本内容',
  
  -- 追踪配置（JSON）
  `tracking_options` JSON NULL COMMENT '追踪配置',
  
  -- 状态与元数据
  `status` VARCHAR(32) NOT NULL DEFAULT 'draft' COMMENT '状态: draft/active/inactive/archived',
  `version` INT NOT NULL DEFAULT 1 COMMENT '版本号',
  `variables` JSON NULL COMMENT '模板变量',
  `metadata` JSON NULL COMMENT '元数据',
  
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY `uk_template_locale` (`tenant_id`, `template_id`, `locale`),
  KEY `idx_template_locale_tenant` (`tenant_id`),
  KEY `idx_template_locale_template` (`tenant_id`, `template_id`),
  KEY `idx_template_locale_unique` (`locale`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板多语言内容表(Email-System内部表)';

-- ========================================
-- 发送管理模块
-- ========================================

-- ----------------------------
-- Table structure for send_plans
-- ----------------------------
CREATE TABLE IF NOT EXISTS `send_plans` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `name` VARCHAR(128) NOT NULL COMMENT '发送计划名称',
  `plan_type` VARCHAR(32) NOT NULL COMMENT '计划类型: campaign/automation/manual',
  `template_id` BIGINT NOT NULL COMMENT '模板ID',
  `audience_config` JSON NOT NULL COMMENT '受众配置',
  `send_config` JSON NOT NULL COMMENT '发送配置',
  `schedule_config` JSON NULL COMMENT '调度配置',
  `status` VARCHAR(32) NOT NULL DEFAULT 'draft' COMMENT '状态: draft/scheduled/running/paused/completed/failed/cancelled',
  `priority` INT NOT NULL DEFAULT 100 COMMENT '优先级',
  `estimated_size` BIGINT NULL COMMENT '预估发送数量',
  `actual_size` BIGINT NULL COMMENT '实际发送数量',
  `scheduled_at` DATETIME NULL COMMENT '计划发送时间',
  `started_at` DATETIME NULL COMMENT '开始时间',
  `finished_at` DATETIME NULL COMMENT '完成时间',
  `error_message` TEXT NULL COMMENT '错误信息',
  `created_by` BIGINT NOT NULL COMMENT '创建人',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  KEY `idx_send_plan_tenant` (`tenant_id`),
  KEY `idx_send_plan_status` (`tenant_id`, `status`),
  KEY `idx_send_plan_schedule` (`tenant_id`, `scheduled_at`),
  KEY `idx_send_plan_template` (`tenant_id`, `template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发送计划表';

-- ----------------------------
-- Table structure for send_batches
-- ----------------------------
CREATE TABLE IF NOT EXISTS `send_batches` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `plan_id` BIGINT NOT NULL COMMENT '发送计划ID',
  `batch_number` INT NOT NULL COMMENT '批次号',
  `size` INT NOT NULL COMMENT '批次大小',
  `status` VARCHAR(32) NOT NULL DEFAULT 'pending' COMMENT '状态: pending/processing/completed/failed/skipped',
  `scheduled_at` DATETIME NULL COMMENT '计划执行时间',
  `started_at` DATETIME NULL COMMENT '开始时间',
  `finished_at` DATETIME NULL COMMENT '完成时间',
  `progress` INT NOT NULL DEFAULT 0 COMMENT '进度百分比',
  `sent_count` INT NOT NULL DEFAULT 0 COMMENT '已发送数量',
  `failed_count` INT NOT NULL DEFAULT 0 COMMENT '失败数量',
  `skipped_count` INT NOT NULL DEFAULT 0 COMMENT '跳过数量',
  `error_message` TEXT NULL COMMENT '错误信息',
  `metadata` JSON NULL COMMENT '元数据',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY `uk_send_batch` (`tenant_id`, `plan_id`, `batch_number`),
  KEY `idx_send_batch_tenant` (`tenant_id`),
  KEY `idx_send_batch_plan` (`tenant_id`, `plan_id`),
  KEY `idx_send_batch_status` (`tenant_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发送批次表';

-- ----------------------------
-- Table structure for send_records
-- ----------------------------
CREATE TABLE IF NOT EXISTS `send_records` (
  `record_id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `batch_id` BIGINT NOT NULL COMMENT '批次ID',
  `plan_id` BIGINT NOT NULL COMMENT '计划ID',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `contact_id` BIGINT NOT NULL COMMENT '联系人ID',
  `email_address` VARCHAR(255) NOT NULL COMMENT '邮箱地址',
  `template_id` VARCHAR(128) NOT NULL COMMENT '模板ID',
  `status` VARCHAR(32) NOT NULL DEFAULT 'pending' COMMENT '状态: pending/sent/delivered/bounced/failed/skipped',
  `attempt_count` INT NOT NULL DEFAULT 0 COMMENT '尝试次数',
  `last_attempt_at` DATETIME NULL COMMENT '最后尝试时间',
  `sent_at` DATETIME NULL COMMENT '发送时间',
  `delivered_at` DATETIME NULL COMMENT '送达时间',
  `bounced_at` DATETIME NULL COMMENT '退信时间',
  `failed_at` DATETIME NULL COMMENT '失败时间',
  `error_message` TEXT NULL COMMENT '错误信息',
  `metadata` JSON NULL COMMENT '元数据',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  KEY `idx_send_record_batch` (`tenant_id`, `batch_id`),
  KEY `idx_send_record_plan` (`tenant_id`, `plan_id`),
  KEY `idx_send_record_tenant` (`tenant_id`),
  KEY `idx_send_record_contact` (`tenant_id`, `contact_id`),
  KEY `idx_send_record_email` (`tenant_id`, `email_address`),
  KEY `idx_send_record_status` (`tenant_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发送记录表';

-- ----------------------------
-- Table structure for audience_snapshots
-- ----------------------------
CREATE TABLE IF NOT EXISTS `audience_snapshots` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `plan_id` BIGINT NOT NULL COMMENT '发送计划ID',
  `contact_ids` LONGTEXT NOT NULL COMMENT '联系人ID列表(JSON)',
  `total_size` BIGINT NOT NULL COMMENT '总数量',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `expires_at` DATETIME NULL COMMENT '过期时间',
  
  KEY `idx_audience_snapshot_tenant` (`tenant_id`),
  KEY `idx_audience_snapshot_plan` (`tenant_id`, `plan_id`),
  KEY `idx_audience_snapshot_expires` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='受众快照表';

-- ========================================
-- 追踪分析模块
-- ========================================

-- ----------------------------
-- Table structure for tracking_events
-- ----------------------------
CREATE TABLE IF NOT EXISTS `tracking_events` (
  `id` VARCHAR(64) PRIMARY KEY COMMENT '事件ID',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `type` VARCHAR(32) NOT NULL COMMENT '事件类型: open/click/conversion/reply/beacon/amp_open',
  `campaign_id` VARCHAR(64) NULL COMMENT '活动ID',
  `message_id` VARCHAR(64) NULL COMMENT '消息ID',
  `subscriber_id` VARCHAR(64) NULL COMMENT '订阅者ID',
  `link_id` VARCHAR(64) NULL COMMENT '链接ID',
  
  -- 分类信息
  `open_class` VARCHAR(32) NULL COMMENT '打开分类: human/prefetch/mpp/proxy/scanner/unknown',
  `source` VARCHAR(32) NULL COMMENT '事件来源: direct/apple_mpp/gmail_proxy/scanner/edge/unknown',
  `is_unique` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否唯一事件',
  `is_first_hit` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否首次触发',
  
  -- 请求信息
  `user_agent` TEXT NULL COMMENT '用户代理',
  `ip_address` VARCHAR(45) NULL COMMENT 'IP地址',
  `geo_location` JSON NULL COMMENT '地理位置信息',
  `headers` JSON NULL COMMENT '请求头',
  
  -- 安全信息
  `signature_valid` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '签名是否有效',
  `timestamp` BIGINT NOT NULL COMMENT '时间戳',
  `nonce` VARCHAR(64) NULL COMMENT '随机数',
  
  -- 转化信息（仅转化事件）
  `conversion_type` VARCHAR(64) NULL COMMENT '转化类型',
  `order_id` VARCHAR(128) NULL COMMENT '订单ID',
  `value` DECIMAL(10,2) NULL COMMENT '转化价值',
  `currency` VARCHAR(8) NULL COMMENT '货币',
  
  -- 点击信息（仅点击事件）
  `destination_host` VARCHAR(255) NULL COMMENT '目标主机',
  `original_url` TEXT NULL COMMENT '原始URL',
  
  `occurred_at` DATETIME NOT NULL COMMENT '发生时间',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  KEY `idx_tracking_event_tenant` (`tenant_id`),
  KEY `idx_tracking_event_type` (`tenant_id`, `type`),
  KEY `idx_tracking_event_campaign` (`tenant_id`, `campaign_id`),
  KEY `idx_tracking_event_subscriber` (`tenant_id`, `subscriber_id`),
  KEY `idx_tracking_event_occurred` (`tenant_id`, `occurred_at`),
  KEY `idx_tracking_event_unique` (`tenant_id`, `is_unique`, `type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='追踪事件表';

-- ----------------------------
-- Table structure for analytics_metrics
-- ----------------------------
CREATE TABLE IF NOT EXISTS `analytics_metrics` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `metric_date` DATE NOT NULL COMMENT '指标日期',
  `campaign_id` VARCHAR(64) NULL COMMENT '活动ID',
  `template_id` BIGINT NULL COMMENT '模板ID',
  `segment_id` BIGINT NULL COMMENT '人群ID',
  
  -- 发送指标
  `sent_count` BIGINT NOT NULL DEFAULT 0 COMMENT '发送数量',
  `delivered_count` BIGINT NOT NULL DEFAULT 0 COMMENT '送达数量',
  `bounced_count` BIGINT NOT NULL DEFAULT 0 COMMENT '退信数量',
  `failed_count` BIGINT NOT NULL DEFAULT 0 COMMENT '失败数量',
  
  -- 打开指标
  `total_opens` BIGINT NOT NULL DEFAULT 0 COMMENT '总打开次数',
  `unique_opens` BIGINT NOT NULL DEFAULT 0 COMMENT '唯一打开次数',
  `human_opens` BIGINT NOT NULL DEFAULT 0 COMMENT '人工打开次数',
  `machine_opens` BIGINT NOT NULL DEFAULT 0 COMMENT '机器打开次数',
  
  -- 点击指标
  `total_clicks` BIGINT NOT NULL DEFAULT 0 COMMENT '总点击次数',
  `unique_clicks` BIGINT NOT NULL DEFAULT 0 COMMENT '唯一点击次数',
  
  -- 转化指标
  `conversions` BIGINT NOT NULL DEFAULT 0 COMMENT '转化次数',
  `conversion_value` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '转化价值',
  
  -- 退订和投诉
  `unsubscribes` BIGINT NOT NULL DEFAULT 0 COMMENT '退订次数',
  `complaints` BIGINT NOT NULL DEFAULT 0 COMMENT '投诉次数',
  
  -- 计算率值 (Wilson置信区间下界)
  `open_rate_wilson_lower` DECIMAL(5,4) NOT NULL DEFAULT 0.0000 COMMENT '打开率威尔逊下界',
  `click_rate_wilson_lower` DECIMAL(5,4) NOT NULL DEFAULT 0.0000 COMMENT '点击率威尔逊下界',
  `human_open_rate_wilson_lower` DECIMAL(5,4) NOT NULL DEFAULT 0.0000 COMMENT '人工打开率威尔逊下界',
  
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY `uk_analytics_metrics` (`tenant_id`, `metric_date`, `campaign_id`, `template_id`, `segment_id`),
  KEY `idx_analytics_metrics_tenant` (`tenant_id`),
  KEY `idx_analytics_metrics_date` (`tenant_id`, `metric_date`),
  KEY `idx_analytics_metrics_campaign` (`tenant_id`, `campaign_id`),
  KEY `idx_analytics_metrics_template` (`tenant_id`, `template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分析指标表';

-- ========================================
-- 通用导入模块
-- ========================================

-- ----------------------------
-- Table structure for import_jobs
-- ----------------------------
CREATE TABLE IF NOT EXISTS `import_jobs` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `business_scenario` VARCHAR(32) NOT NULL DEFAULT 'contacts' COMMENT '业务场景: contacts/one_time_email/tag_members/custom',
  `scenario_config` JSON NULL COMMENT '场景特定配置',
  `target_entity_type` VARCHAR(32) NULL COMMENT '目标实体类型: contacts/tags/emails',
  `target_entity_id` BIGINT NULL COMMENT '目标实体ID(如标签ID)',
  `business_metadata` JSON NULL COMMENT '业务元数据',
  
  -- 文件信息
  `file_name` VARCHAR(255) NOT NULL COMMENT '原始文件名',
  `file_url` VARCHAR(512) NOT NULL COMMENT '文件URL',
  `file_type` VARCHAR(16) NOT NULL COMMENT '文件类型: csv/excel/json',
  `file_size` BIGINT NOT NULL DEFAULT 0 COMMENT '文件大小(字节)',
  
  -- 映射配置
  `mapping_config` JSON NOT NULL COMMENT '字段映射配置',
  `validation_rules` JSON NULL COMMENT '验证规则配置',
  
  -- 处理状态
  `status` VARCHAR(32) NOT NULL DEFAULT 'queued' COMMENT '状态: queued/parsing/validating/processing/completed/failed/cancelled',
  `current_phase` VARCHAR(32) NULL COMMENT '当前阶段: parsing/validating/processing/storing',
  `progress` INT NOT NULL DEFAULT 0 COMMENT '进度百分比(0-100)',
  
  -- 统计信息
  `total_rows` BIGINT NOT NULL DEFAULT 0 COMMENT '总行数',
  `valid_rows` BIGINT NOT NULL DEFAULT 0 COMMENT '有效行数',
  `invalid_rows` BIGINT NOT NULL DEFAULT 0 COMMENT '无效行数',
  `duplicate_rows` BIGINT NOT NULL DEFAULT 0 COMMENT '重复行数',
  `processed_count` BIGINT NOT NULL DEFAULT 0 COMMENT '已处理数量',
  `success_count` BIGINT NOT NULL DEFAULT 0 COMMENT '成功数量',
  `failed_count` BIGINT NOT NULL DEFAULT 0 COMMENT '失败数量',
  
  -- 时间信息
  `estimated_duration` INT NULL COMMENT '预估处理时长(秒)',
  `estimated_completion` DATETIME NULL COMMENT '预估完成时间',
  `started_at` DATETIME NULL COMMENT '开始处理时间',
  `finished_at` DATETIME NULL COMMENT '完成时间',
  
  -- 错误信息
  `error_message` TEXT NULL COMMENT '错误信息',
  `error_summary` JSON NULL COMMENT '错误汇总',
  
  `created_by` BIGINT NOT NULL COMMENT '创建人',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  KEY `idx_import_job_tenant` (`tenant_id`),
  KEY `idx_import_job_status` (`tenant_id`, `status`),
  KEY `idx_import_job_scenario` (`tenant_id`, `business_scenario`),
  KEY `idx_import_job_target` (`tenant_id`, `target_entity_type`, `target_entity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用导入任务表';

-- ----------------------------
-- Table structure for import_scenario_configs
-- ----------------------------
CREATE TABLE IF NOT EXISTS `import_scenario_configs` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `scenario_code` VARCHAR(32) NOT NULL COMMENT '场景代码',
  `scenario_name` VARCHAR(64) NOT NULL COMMENT '场景名称',
  `description` VARCHAR(255) NULL COMMENT '场景描述',
  `target_entity_type` VARCHAR(32) NOT NULL COMMENT '目标实体类型',
  `required_fields` JSON NOT NULL COMMENT '必填字段列表',
  `optional_fields` JSON NULL COMMENT '可选字段列表',
  `validation_rules` JSON NULL COMMENT '验证规则配置',
  `default_mapping` JSON NULL COMMENT '默认字段映射',
  `post_process_config` JSON NULL COMMENT '后处理配置',
  `status` VARCHAR(16) NOT NULL DEFAULT 'active' COMMENT '状态: active/inactive',
  `created_by` BIGINT NULL COMMENT '创建人',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY `uk_scenario_code` (`tenant_id`, `scenario_code`),
  KEY `idx_scenario_status` (`tenant_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导入场景配置表';

-- ----------------------------
-- Table structure for import_errors
-- ----------------------------
CREATE TABLE IF NOT EXISTS `import_errors` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `job_id` BIGINT NOT NULL COMMENT '导入任务ID',
  `batch_no` INT NOT NULL COMMENT '批次号',
  `row_number` BIGINT NOT NULL COMMENT '原始行号',
  `raw_data` JSON NOT NULL COMMENT '原始行数据(仅保存失败行)',
  `error_type` VARCHAR(32) NOT NULL COMMENT '错误类型: validation/business/system',
  `error_code` VARCHAR(64) NULL COMMENT '错误代码',
  `error_message` VARCHAR(1024) NOT NULL COMMENT '错误描述',
  `retry_count` INT NOT NULL DEFAULT 0 COMMENT '重试次数',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  KEY `idx_import_error_job` (`tenant_id`, `job_id`),
  KEY `idx_import_error_batch` (`tenant_id`, `job_id`, `batch_no`),
  KEY `idx_import_error_type` (`tenant_id`, `error_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导入错误记录表(仅保存失败行)';

-- ----------------------------
-- Table structure for import_batches
-- ----------------------------
CREATE TABLE IF NOT EXISTS `import_batches` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
  `job_id` BIGINT NOT NULL COMMENT '导入任务ID',
  `batch_no` INT NOT NULL COMMENT '批次号',
  `start_row` BIGINT NOT NULL COMMENT '起始行号',
  `end_row` BIGINT NOT NULL COMMENT '结束行号',
  `batch_size` INT NOT NULL COMMENT '批次大小',
  `status` VARCHAR(32) NOT NULL DEFAULT 'pending' COMMENT '状态: pending/processing/completed/failed',
  `processed_count` INT NOT NULL DEFAULT 0 COMMENT '已处理数量',
  `success_count` INT NOT NULL DEFAULT 0 COMMENT '成功数量',
  `failed_count` INT NOT NULL DEFAULT 0 COMMENT '失败数量',
  `started_at` DATETIME NULL COMMENT '开始时间',
  `finished_at` DATETIME NULL COMMENT '完成时间',
  `error_message` TEXT NULL COMMENT '错误信息',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  UNIQUE KEY `uk_import_batch` (`tenant_id`, `job_id`, `batch_no`),
  KEY `idx_import_batch_job` (`tenant_id`, `job_id`),
  KEY `idx_import_batch_status` (`tenant_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='导入批次表';

SET FOREIGN_KEY_CHECKS = 1;