# Email-System AI工具系统详细设计文档

## 文档信息
- **版本**: v1.0
- **日期**: 2025-08-18
- **作者**: AI Assistant
- **项目**: Email-System AI工具集成

## 1. 系统概述

### 1.1 设计目标
为Email-System设计一个功能丰富的AI工具系统，支持多轮对话、预设提示词、租户可调整配置，专门针对邮件营销场景提供智能化支持。

### 1.2 核心功能
- **多轮对话管理**: 上下文保持、会话状态管理、智能记忆
- **预设提示词系统**: 场景化模板、动态参数替换、版本管理
- **多租户支持**: 租户隔离、个性化配置、使用配额管理
- **邮件场景支持**: 模板生成、回复建议、内容优化
- **翻译功能**: 多语言翻译、本地化支持
- **智能内容生成**: 邮件主题、正文、营销文案
- **情感分析**: 邮件情感识别、回复语调调整
- **个性化推荐**: 基于用户行为的内容推荐
- **A/B测试支持**: 内容变体生成、效果预测
- **数据洞察**: 邮件效果分析、优化建议
- **自动化工作流**: 智能触发、条件判断
- **合规检查**: 内容合规性检测、风险评估

### 1.3 技术原则
- 遵循Email-System的Clean Architecture + DDD设计模式
- 使用HTTP Streaming替代WebSocket实现实时对话
- 仅使用GET/POST接口，统一响应格式
- 支持多AI模型提供商（OpenAI、Claude、本地模型）
- 租户级别的数据隔离和配置管理

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    AI工具系统架构                              │
├─────────────────────────────────────────────────────────────┤
│  接口层 (Interfaces)                                         │
│  ├── HTTP API (REST)                                        │
│  ├── HTTP Streaming (实时对话流式响应)                        │
│  └── gRPC 服务 (内部服务调用)                                │
├─────────────────────────────────────────────────────────────┤
│  应用层 (Application)                                        │
│  ├── 对话应用服务                                            │
│  ├── 提示词应用服务                                          │
│  ├── 翻译应用服务                                            │
│  ├── 内容生成应用服务                                        │
│  └── 分析应用服务                                            │
├─────────────────────────────────────────────────────────────┤
│  领域层 (Domain)                                             │
│  ├── 对话聚合 (Conversation)                                │
│  ├── 提示词聚合 (PromptTemplate)                            │
│  ├── AI模型聚合 (AIModel)                                   │
│  ├── 租户配置聚合 (TenantAIConfig)                          │
│  └── 内容聚合 (Content)                                     │
├─────────────────────────────────────────────────────────────┤
│  基础设施层 (Infrastructure)                                 │
│  ├── AI模型适配器 (OpenAI, Claude, 本地模型)                │
│  ├── 向量数据库 (Embedding存储)                              │
│  ├── 缓存层 (Redis)                                         │
│  ├── 消息队列 (异步处理)                                     │
│  └── 外部服务集成                                            │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心模块设计

#### 2.2.1 对话管理模块
- **会话生命周期管理**: 创建、激活、暂停、归档
- **上下文窗口管理**: 智能压缩、优先级排序
- **历史记录压缩**: 语义保留的压缩算法
- **多轮对话状态机**: 状态转换、触发条件

#### 2.2.2 提示词管理模块
- **模板分类管理**: 按场景、类型分类
- **动态参数注入**: 变量替换、条件逻辑
- **版本控制**: 模板版本管理、回滚机制
- **效果评估**: 使用统计、效果评分

#### 2.2.3 AI模型管理模块
- **多模型支持**: OpenAI、Claude、本地模型
- **负载均衡**: 请求分发、故障转移
- **降级策略**: 模型不可用时的备选方案
- **成本控制**: 使用配额、成本监控

#### 2.2.4 租户配置模块
- **个性化设置**: 模型选择、参数调优
- **使用配额管理**: 请求限制、Token限制
- **权限控制**: 功能开关、访问控制
- **计费统计**: 使用量统计、成本分析

## 3. 数据模型设计

### 3.1 核心数据表

#### 3.1.1 AI对话会话表 (ai_conversations)
```sql
CREATE TABLE ai_conversations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    session_id VARCHAR(64) NOT NULL COMMENT '会话唯一标识',
    title VARCHAR(255) DEFAULT NULL COMMENT '对话标题',
    scenario VARCHAR(64) NOT NULL COMMENT '使用场景',
    context_data JSON DEFAULT NULL COMMENT '上下文数据',
    status ENUM('active','archived','deleted') DEFAULT 'active',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_session_id (session_id),
    KEY idx_tenant_user (tenant_id, user_id),
    KEY idx_scenario (scenario)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 3.1.2 AI对话消息表 (ai_conversation_messages)
```sql
CREATE TABLE ai_conversation_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    conversation_id BIGINT NOT NULL,
    message_id VARCHAR(64) NOT NULL COMMENT '消息唯一标识',
    role ENUM('user','assistant','system') NOT NULL,
    content LONGTEXT NOT NULL,
    content_type ENUM('text','json','markdown') DEFAULT 'text',
    metadata JSON DEFAULT NULL COMMENT '消息元数据',
    token_count INT DEFAULT 0 COMMENT 'Token消耗数量',
    model_used VARCHAR(64) DEFAULT NULL COMMENT '使用的AI模型',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    KEY idx_conversation (conversation_id),
    FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### 3.1.3 AI提示词模板表 (ai_prompt_templates)
```sql
CREATE TABLE ai_prompt_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL,
    template_code VARCHAR(64) NOT NULL COMMENT '模板代码',
    name VARCHAR(255) NOT NULL,
    description TEXT DEFAULT NULL,
    category VARCHAR(64) NOT NULL COMMENT '分类',
    scenario VARCHAR(64) NOT NULL COMMENT '具体场景',
    prompt_content LONGTEXT NOT NULL COMMENT '提示词内容',
    variables JSON DEFAULT NULL COMMENT '可配置变量定义',
    model_config JSON DEFAULT NULL COMMENT '模型配置参数',
    version INT DEFAULT 1,
    status ENUM('draft','active','archived') DEFAULT 'draft',
    usage_count BIGINT DEFAULT 0 COMMENT '使用次数',
    effectiveness_score DECIMAL(3,2) DEFAULT NULL COMMENT '效果评分',
    created_by BIGINT DEFAULT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_tenant_code (tenant_id, template_code),
    KEY idx_category_scenario (category, scenario)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 3.2 扩展数据表
- **租户AI配置表** (tenant_ai_configs): 租户级AI配置管理
- **AI使用统计表** (ai_usage_statistics): 使用量和成本统计
- **AI内容生成记录表** (ai_content_generations): 内容生成历史记录

## 4. API接口设计

### 4.1 统一响应格式
```go
type APIResponse struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
    Meta    *Meta       `json:"meta,omitempty"`
}
```

### 4.2 核心接口

#### 4.2.1 对话管理接口
```
POST /api/ai/conversations/create     # 创建对话会话
POST /api/ai/conversations/chat       # 发送消息（流式响应）
POST /api/ai/conversations/history    # 获取对话历史
POST /api/ai/conversations/list       # 获取对话列表
```

#### 4.2.2 提示词模板接口
```
POST /api/ai/prompt-templates/create  # 创建提示词模板
GET  /api/ai/prompt-templates/get     # 获取提示词模板
POST /api/ai/prompt-templates/update  # 更新提示词模板
POST /api/ai/prompt-templates/list    # 获取模板列表
```

#### 4.2.3 内容生成接口
```
POST /api/ai/content/generate-email-subject    # 生成邮件主题
POST /api/ai/content/generate-email-content    # 生成邮件内容
POST /api/ai/content/translate                 # 翻译内容
POST /api/ai/content/generate-reply-suggestions # 生成回复建议
```

## 5. 多轮对话机制

### 5.1 上下文管理策略
- **分层上下文架构**: 会话级、任务级、消息级、系统级
- **上下文窗口管理**: 智能压缩、优先级排序
- **智能记忆管理**: 重要性评分、衰减机制

### 5.2 会话状态机
```go
type ConversationState string

const (
    StateInitialized ConversationState = "initialized"
    StateActive      ConversationState = "active"
    StateWaiting     ConversationState = "waiting"
    StatePaused      ConversationState = "paused"
    StateCompleted   ConversationState = "completed"
    StateArchived    ConversationState = "archived"
)
```

### 5.3 流式响应处理
使用HTTP Streaming (Server-Sent Events) 实现实时对话：
```
Content-Type: text/event-stream

data: {"type":"chunk","content":"正在分析您的邮件内容...","timestamp":1692345678}
data: {"type":"text","content":"建议优化主题为：","timestamp":1692345679}
data: {"type":"done","content":"","timestamp":**********}
```

## 6. 预设提示词系统

### 6.1 模板分类体系
- **邮件类** (email): 主题优化、内容生成、格式调整
- **翻译类** (translation): 多语言翻译、本地化适配
- **分析类** (analysis): 效果分析、优化建议
- **自动化类** (automation): 工作流触发、条件判断

### 6.2 动态参数系统
```json
{
  "variables": {
    "industry": {
      "type": "string",
      "description": "行业类型",
      "enum": ["technology", "finance", "retail", "healthcare"]
    },
    "target_audience": {
      "type": "string",
      "description": "目标受众"
    },
    "tone": {
      "type": "enum",
      "options": ["professional", "casual", "urgent", "friendly"]
    }
  }
}
```

### 6.3 版本管理
- 模板版本控制
- 效果对比分析
- 自动回滚机制

## 7. 翻译功能集成

### 7.1 与template_locales集成
- 自动检测源语言
- 批量翻译模板内容
- 保持格式和变量完整性

### 7.2 翻译质量保证
- 上下文感知翻译
- 术语一致性检查
- 人工审核工作流

## 8. 租户配置管理

### 8.1 多级配置体系
- **系统级**: 默认配置、全局限制
- **租户级**: 个性化设置、使用配额
- **用户级**: 个人偏好、历史记录

### 8.2 配置项目
```json
{
  "model_provider": "openai",
  "model_name": "gpt-4",
  "api_config": {
    "api_key": "encrypted_key",
    "base_url": "https://api.openai.com/v1"
  },
  "usage_limits": {
    "daily_requests": 1000,
    "monthly_tokens": 100000
  },
  "feature_flags": {
    "enable_translation": true,
    "enable_content_generation": true,
    "enable_analysis": true
  }
}
```

## 9. 安全与权限

### 9.1 数据安全
- API密钥加密存储
- 敏感数据脱敏
- 审计日志记录

### 9.2 访问控制
- 基于角色的权限控制
- 租户数据隔离
- API访问限制

### 9.3 使用配额
- 请求频率限制
- Token使用限制
- 成本控制机制

## 10. 性能与监控

### 10.1 性能优化
- 响应缓存策略
- 异步处理机制
- 负载均衡配置

### 10.2 监控指标
- API响应时间
- 模型调用成功率
- 用户满意度评分
- 成本使用情况

## 11. 扩展性设计

### 11.1 插件化架构
- 模型提供商插件
- 功能模块插件
- 自定义处理器

### 11.2 集成能力
- 第三方AI服务集成
- 外部数据源连接
- Webhook事件通知

## 12. 实施建议

### 12.1 开发阶段
1. **Phase 1**: 基础对话功能、提示词管理
2. **Phase 2**: 翻译功能、内容生成
3. **Phase 3**: 高级分析、自动化工作流
4. **Phase 4**: 性能优化、扩展功能

### 12.2 技术要求
- Go 1.21+
- MySQL 8.0
- Redis 7.0
- 向量数据库 (如Qdrant)

### 12.3 部署架构
- 微服务部署
- 容器化支持
- 水平扩展能力

## 13. 详细技术实现

### 13.1 领域实体设计

#### 13.1.1 对话聚合根
```go
package entity

import (
    "time"
    "encoding/json"
)

// Conversation 对话聚合根
type Conversation struct {
    ID          int64                  `json:"id"`
    TenantID    int64                  `json:"tenant_id"`
    UserID      int64                  `json:"user_id"`
    SessionID   string                 `json:"session_id"`
    Title       string                 `json:"title"`
    Scenario    ConversationScenario   `json:"scenario"`
    ContextData map[string]interface{} `json:"context_data"`
    Status      ConversationStatus     `json:"status"`
    Messages    []ConversationMessage  `json:"messages"`
    CreatedAt   time.Time             `json:"created_at"`
    UpdatedAt   time.Time             `json:"updated_at"`
}

type ConversationScenario string

const (
    ScenarioEmailTemplate     ConversationScenario = "email_template"
    ScenarioAutoReply        ConversationScenario = "auto_reply"
    ScenarioTranslation      ConversationScenario = "translation"
    ScenarioContentGeneration ConversationScenario = "content_generation"
    ScenarioAnalysis         ConversationScenario = "analysis"
)

type ConversationStatus string

const (
    StatusActive   ConversationStatus = "active"
    StatusArchived ConversationStatus = "archived"
    StatusDeleted  ConversationStatus = "deleted"
)

// AddMessage 添加消息到对话
func (c *Conversation) AddMessage(role MessageRole, content string, metadata map[string]interface{}) error {
    message := ConversationMessage{
        MessageID:   generateMessageID(),
        Role:        role,
        Content:     content,
        ContentType: ContentTypeText,
        Metadata:    metadata,
        CreatedAt:   time.Now(),
    }

    c.Messages = append(c.Messages, message)
    c.UpdatedAt = time.Now()

    return nil
}

// GetRecentMessages 获取最近的消息
func (c *Conversation) GetRecentMessages(limit int) []ConversationMessage {
    if len(c.Messages) <= limit {
        return c.Messages
    }
    return c.Messages[len(c.Messages)-limit:]
}
```

#### 13.1.2 提示词模板聚合根
```go
// PromptTemplate 提示词模板聚合根
type PromptTemplate struct {
    ID                  int64                  `json:"id"`
    TenantID           int64                  `json:"tenant_id"`
    TemplateCode       string                 `json:"template_code"`
    Name               string                 `json:"name"`
    Description        string                 `json:"description"`
    Category           TemplateCategory       `json:"category"`
    Scenario           string                 `json:"scenario"`
    PromptContent      string                 `json:"prompt_content"`
    Variables          map[string]Variable    `json:"variables"`
    ModelConfig        ModelConfig            `json:"model_config"`
    Version            int                    `json:"version"`
    Status             TemplateStatus         `json:"status"`
    UsageCount         int64                  `json:"usage_count"`
    EffectivenessScore float64               `json:"effectiveness_score"`
    CreatedBy          int64                  `json:"created_by"`
    CreatedAt          time.Time             `json:"created_at"`
    UpdatedAt          time.Time             `json:"updated_at"`
}

type Variable struct {
    Type        string      `json:"type"`
    Description string      `json:"description"`
    Required    bool        `json:"required"`
    Default     interface{} `json:"default,omitempty"`
    Options     []string    `json:"options,omitempty"`
    Validation  string      `json:"validation,omitempty"`
}

type ModelConfig struct {
    Temperature      float64 `json:"temperature"`
    MaxTokens        int     `json:"max_tokens"`
    TopP            float64 `json:"top_p"`
    FrequencyPenalty float64 `json:"frequency_penalty"`
    PresencePenalty  float64 `json:"presence_penalty"`
}

// RenderPrompt 渲染提示词
func (pt *PromptTemplate) RenderPrompt(variables map[string]interface{}) (string, error) {
    // 验证必需变量
    if err := pt.validateVariables(variables); err != nil {
        return "", err
    }

    // 应用默认值
    mergedVars := pt.applyDefaults(variables)

    // 渲染模板
    rendered, err := pt.renderTemplate(pt.PromptContent, mergedVars)
    if err != nil {
        return "", err
    }

    return rendered, nil
}
```

### 13.2 应用服务实现

#### 13.2.1 对话应用服务
```go
package service

import (
    "context"
    "fmt"
    "gitee.com/heiyee/platforms/email-system/internal/domain/ai/entity"
    "gitee.com/heiyee/platforms/email-system/internal/domain/ai/repository"
)

type ConversationApplicationService struct {
    conversationRepo repository.ConversationRepository
    aiModelService   AIModelService
    promptService    PromptTemplateService
    logger          logiface.Logger
}

// CreateConversation 创建对话
func (s *ConversationApplicationService) CreateConversation(
    ctx context.Context,
    req *dto.CreateConversationRequest,
) (*dto.ConversationResponse, error) {

    // 创建对话实体
    conversation := &entity.Conversation{
        TenantID:    req.TenantID,
        UserID:      req.UserID,
        SessionID:   generateSessionID(),
        Title:       req.Title,
        Scenario:    entity.ConversationScenario(req.Scenario),
        ContextData: req.ContextData,
        Status:      entity.StatusActive,
        CreatedAt:   time.Now(),
        UpdatedAt:   time.Now(),
    }

    // 保存到数据库
    if err := s.conversationRepo.Save(ctx, conversation); err != nil {
        s.logger.Error(ctx, "Failed to save conversation", logiface.Error(err))
        return nil, fmt.Errorf("save conversation: %w", err)
    }

    return dto.ToConversationResponse(conversation), nil
}

// SendMessage 发送消息（流式响应）
func (s *ConversationApplicationService) SendMessage(
    ctx context.Context,
    req *dto.SendMessageRequest,
) (<-chan dto.StreamResponse, error) {

    // 获取对话
    conversation, err := s.conversationRepo.FindBySessionID(ctx, req.SessionID)
    if err != nil {
        return nil, fmt.Errorf("find conversation: %w", err)
    }

    // 添加用户消息
    if err := conversation.AddMessage(
        entity.RoleUser,
        req.Message,
        req.Context,
    ); err != nil {
        return nil, fmt.Errorf("add user message: %w", err)
    }

    // 创建响应通道
    responseChan := make(chan dto.StreamResponse, 100)

    // 异步处理AI响应
    go s.processAIResponse(ctx, conversation, responseChan)

    return responseChan, nil
}

func (s *ConversationApplicationService) processAIResponse(
    ctx context.Context,
    conversation *entity.Conversation,
    responseChan chan<- dto.StreamResponse,
) {
    defer close(responseChan)

    // 构建AI请求
    aiRequest := s.buildAIRequest(conversation)

    // 调用AI模型
    aiResponseChan, err := s.aiModelService.GenerateStream(ctx, aiRequest)
    if err != nil {
        responseChan <- dto.StreamResponse{
            Type:    "error",
            Content: err.Error(),
        }
        return
    }

    var fullResponse strings.Builder

    // 处理流式响应
    for chunk := range aiResponseChan {
        fullResponse.WriteString(chunk.Content)

        responseChan <- dto.StreamResponse{
            Type:      "chunk",
            Content:   chunk.Content,
            Timestamp: time.Now().UnixMilli(),
        }
    }

    // 保存AI响应
    if err := conversation.AddMessage(
        entity.RoleAssistant,
        fullResponse.String(),
        map[string]interface{}{
            "model": aiRequest.Model,
            "tokens": len(fullResponse.String()) / 4, // 粗略估算
        },
    ); err != nil {
        s.logger.Error(ctx, "Failed to save AI response", logiface.Error(err))
    }

    // 更新对话
    if err := s.conversationRepo.Update(ctx, conversation); err != nil {
        s.logger.Error(ctx, "Failed to update conversation", logiface.Error(err))
    }

    // 发送完成信号
    responseChan <- dto.StreamResponse{
        Type:      "done",
        Timestamp: time.Now().UnixMilli(),
    }
}
```

### 13.3 基础设施层实现

#### 13.3.1 AI模型适配器
```go
package adapter

import (
    "context"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "strings"
)

// OpenAIAdapter OpenAI模型适配器
type OpenAIAdapter struct {
    apiKey  string
    baseURL string
    client  *http.Client
}

type OpenAIRequest struct {
    Model       string    `json:"model"`
    Messages    []Message `json:"messages"`
    Temperature float64   `json:"temperature"`
    MaxTokens   int       `json:"max_tokens"`
    Stream      bool      `json:"stream"`
}

type Message struct {
    Role    string `json:"role"`
    Content string `json:"content"`
}

// GenerateStream 流式生成
func (a *OpenAIAdapter) GenerateStream(ctx context.Context, req AIRequest) (<-chan StreamChunk, error) {
    openaiReq := OpenAIRequest{
        Model:       req.Model,
        Messages:    convertMessages(req.Messages),
        Temperature: req.Temperature,
        MaxTokens:   req.MaxTokens,
        Stream:      true,
    }

    reqBody, err := json.Marshal(openaiReq)
    if err != nil {
        return nil, fmt.Errorf("marshal request: %w", err)
    }

    httpReq, err := http.NewRequestWithContext(
        ctx,
        "POST",
        a.baseURL+"/chat/completions",
        strings.NewReader(string(reqBody)),
    )
    if err != nil {
        return nil, fmt.Errorf("create request: %w", err)
    }

    httpReq.Header.Set("Authorization", "Bearer "+a.apiKey)
    httpReq.Header.Set("Content-Type", "application/json")

    resp, err := a.client.Do(httpReq)
    if err != nil {
        return nil, fmt.Errorf("send request: %w", err)
    }

    if resp.StatusCode != http.StatusOK {
        return nil, fmt.Errorf("API error: %d", resp.StatusCode)
    }

    chunkChan := make(chan StreamChunk, 100)

    go a.processStreamResponse(resp.Body, chunkChan)

    return chunkChan, nil
}

func (a *OpenAIAdapter) processStreamResponse(body io.ReadCloser, chunkChan chan<- StreamChunk) {
    defer close(chunkChan)
    defer body.Close()

    scanner := bufio.NewScanner(body)

    for scanner.Scan() {
        line := scanner.Text()

        if !strings.HasPrefix(line, "data: ") {
            continue
        }

        data := strings.TrimPrefix(line, "data: ")

        if data == "[DONE]" {
            break
        }

        var streamResp OpenAIStreamResponse
        if err := json.Unmarshal([]byte(data), &streamResp); err != nil {
            continue
        }

        if len(streamResp.Choices) > 0 && streamResp.Choices[0].Delta.Content != "" {
            chunkChan <- StreamChunk{
                Content: streamResp.Choices[0].Delta.Content,
            }
        }
    }
}
```

### 13.4 配置文件模板

#### 13.4.1 AI工具配置
```yaml
# AI工具系统配置
ai_tool:
  # 默认模型配置
  default_model:
    provider: "openai"
    model: "gpt-4"
    temperature: 0.7
    max_tokens: 2000

  # 模型提供商配置
  providers:
    openai:
      base_url: "https://api.openai.com/v1"
      timeout: 30
      retry_count: 3

    anthropic:
      base_url: "https://api.anthropic.com"
      timeout: 30
      retry_count: 3

  # 上下文管理配置
  context:
    max_tokens: 8000
    compression_ratio: 0.7
    memory_retention_days: 30

  # 使用限制配置
  limits:
    default_daily_requests: 1000
    default_monthly_tokens: 100000
    max_conversation_length: 50

  # 缓存配置
  cache:
    prompt_template_ttl: 3600  # 1小时
    conversation_context_ttl: 1800  # 30分钟
    ai_response_ttl: 300  # 5分钟

  # 功能开关
  features:
    enable_streaming: true
    enable_translation: true
    enable_content_generation: true
    enable_analysis: true
    enable_memory_compression: true
```

#### 13.4.2 预设提示词模板示例
```json
{
  "templates": [
    {
      "template_code": "email_subject_optimizer",
      "name": "邮件主题优化器",
      "category": "email",
      "scenario": "subject_optimization",
      "prompt_content": "你是一个专业的邮件营销专家。请根据以下信息优化邮件主题：\n\n行业：{{industry}}\n目标受众：{{target_audience}}\n语调：{{tone}}\n原始主题：{{original_subject}}\n\n请提供3-5个优化后的主题建议，每个主题都应该：\n1. 吸引目标受众的注意力\n2. 符合行业特点\n3. 保持适当的语调\n4. 长度控制在50字符以内\n\n请按照以下格式输出：\n1. [优化主题1]\n2. [优化主题2]\n...",
      "variables": {
        "industry": {
          "type": "string",
          "description": "行业类型",
          "required": true
        },
        "target_audience": {
          "type": "string",
          "description": "目标受众",
          "required": true
        },
        "tone": {
          "type": "enum",
          "description": "语调风格",
          "options": ["professional", "casual", "urgent", "friendly"],
          "default": "professional"
        },
        "original_subject": {
          "type": "string",
          "description": "原始邮件主题",
          "required": true
        }
      },
      "model_config": {
        "temperature": 0.7,
        "max_tokens": 500
      }
    },
    {
      "template_code": "email_content_generator",
      "name": "邮件内容生成器",
      "category": "email",
      "scenario": "content_generation",
      "prompt_content": "你是一个专业的邮件内容创作专家。请根据以下要求生成邮件内容：\n\n邮件类型：{{email_type}}\n主题：{{subject}}\n目标受众：{{target_audience}}\n关键信息：{{key_points}}\n行动号召：{{cta}}\n语调：{{tone}}\n\n请生成一份完整的邮件内容，包括：\n1. 个性化的开头问候\n2. 清晰的正文内容\n3. 明确的行动号召\n4. 专业的结尾\n\n内容要求：\n- 语言简洁明了\n- 结构清晰\n- 符合目标受众特点\n- 包含适当的情感元素",
      "variables": {
        "email_type": {
          "type": "enum",
          "description": "邮件类型",
          "options": ["newsletter", "promotion", "welcome", "follow_up", "announcement"],
          "required": true
        },
        "subject": {
          "type": "string",
          "description": "邮件主题",
          "required": true
        },
        "target_audience": {
          "type": "string",
          "description": "目标受众",
          "required": true
        },
        "key_points": {
          "type": "array",
          "description": "关键信息点",
          "required": true
        },
        "cta": {
          "type": "string",
          "description": "行动号召",
          "required": false
        },
        "tone": {
          "type": "enum",
          "description": "语调风格",
          "options": ["professional", "casual", "urgent", "friendly"],
          "default": "professional"
        }
      },
      "model_config": {
        "temperature": 0.8,
        "max_tokens": 1000
      }
    }
  ]
}
```

## 14. 数据库迁移脚本

### 14.1 AI工具核心表创建脚本
```sql
-- AI工具系统数据库迁移脚本
-- 版本: v1.0
-- 日期: 2025-08-18

-- AI对话会话表
CREATE TABLE ai_conversations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    session_id VARCHAR(64) NOT NULL COMMENT '会话唯一标识',
    title VARCHAR(255) DEFAULT NULL COMMENT '对话标题',
    scenario VARCHAR(64) NOT NULL COMMENT '使用场景：email_template,auto_reply,translation,content_generation',
    context_data JSON DEFAULT NULL COMMENT '上下文数据',
    status ENUM('active','archived','deleted') DEFAULT 'active',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_session_id (session_id),
    KEY idx_tenant_user (tenant_id, user_id),
    KEY idx_scenario (scenario),
    KEY idx_status_updated (status, updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话会话表';

-- AI对话消息表
CREATE TABLE ai_conversation_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    conversation_id BIGINT NOT NULL,
    message_id VARCHAR(64) NOT NULL COMMENT '消息唯一标识',
    role ENUM('user','assistant','system') NOT NULL,
    content LONGTEXT NOT NULL,
    content_type ENUM('text','json','markdown') DEFAULT 'text',
    metadata JSON DEFAULT NULL COMMENT '消息元数据',
    token_count INT DEFAULT 0 COMMENT 'Token消耗数量',
    model_used VARCHAR(64) DEFAULT NULL COMMENT '使用的AI模型',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,

    KEY idx_conversation (conversation_id),
    KEY idx_role_created (role, created_at),
    FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话消息表';

-- AI提示词模板表
CREATE TABLE ai_prompt_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL,
    template_code VARCHAR(64) NOT NULL COMMENT '模板代码',
    name VARCHAR(255) NOT NULL,
    description TEXT DEFAULT NULL,
    category VARCHAR(64) NOT NULL COMMENT '分类：email,translation,content,analysis',
    scenario VARCHAR(64) NOT NULL COMMENT '具体场景',
    prompt_content LONGTEXT NOT NULL COMMENT '提示词内容',
    variables JSON DEFAULT NULL COMMENT '可配置变量定义',
    model_config JSON DEFAULT NULL COMMENT '模型配置参数',
    version INT DEFAULT 1,
    status ENUM('draft','active','archived') DEFAULT 'draft',
    usage_count BIGINT DEFAULT 0 COMMENT '使用次数',
    effectiveness_score DECIMAL(3,2) DEFAULT NULL COMMENT '效果评分',
    created_by BIGINT DEFAULT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_tenant_code (tenant_id, template_code),
    KEY idx_category_scenario (category, scenario),
    KEY idx_status_usage (status, usage_count),
    KEY idx_effectiveness (effectiveness_score DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI提示词模板表';

-- 租户AI配置表
CREATE TABLE tenant_ai_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL,
    model_provider VARCHAR(64) NOT NULL COMMENT '模型提供商：openai,anthropic,local',
    model_name VARCHAR(128) NOT NULL COMMENT '模型名称',
    api_config JSON NOT NULL COMMENT 'API配置信息',
    usage_limits JSON DEFAULT NULL COMMENT '使用限制配置',
    cost_settings JSON DEFAULT NULL COMMENT '成本控制设置',
    feature_flags JSON DEFAULT NULL COMMENT '功能开关',
    is_default BOOLEAN DEFAULT FALSE,
    status ENUM('active','inactive','suspended') DEFAULT 'active',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_tenant_provider_model (tenant_id, model_provider, model_name),
    KEY idx_tenant_default (tenant_id, is_default),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='租户AI配置表';

-- AI使用统计表
CREATE TABLE ai_usage_statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL,
    user_id BIGINT DEFAULT NULL,
    date DATE NOT NULL,
    scenario VARCHAR(64) NOT NULL,
    model_provider VARCHAR(64) NOT NULL,
    model_name VARCHAR(128) NOT NULL,
    request_count INT DEFAULT 0,
    token_input_count BIGINT DEFAULT 0,
    token_output_count BIGINT DEFAULT 0,
    cost_amount DECIMAL(10,4) DEFAULT 0.0000,
    success_count INT DEFAULT 0,
    error_count INT DEFAULT 0,
    avg_response_time_ms INT DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    UNIQUE KEY uk_tenant_user_date_scenario (tenant_id, user_id, date, scenario, model_provider, model_name),
    KEY idx_tenant_date (tenant_id, date),
    KEY idx_scenario_date (scenario, date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI使用统计表';

-- AI内容生成记录表
CREATE TABLE ai_content_generations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    conversation_id BIGINT DEFAULT NULL,
    generation_type VARCHAR(64) NOT NULL COMMENT '生成类型：email_subject,email_content,translation,reply_suggestion',
    input_data JSON NOT NULL COMMENT '输入数据',
    output_data JSON NOT NULL COMMENT '输出数据',
    template_id BIGINT DEFAULT NULL COMMENT '关联的模板ID',
    prompt_template_id BIGINT DEFAULT NULL COMMENT '使用的提示词模板',
    model_used VARCHAR(64) NOT NULL,
    quality_score DECIMAL(3,2) DEFAULT NULL COMMENT '质量评分',
    user_feedback ENUM('positive','negative','neutral') DEFAULT NULL,
    status ENUM('generated','approved','rejected','used') DEFAULT 'generated',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    KEY idx_tenant_user (tenant_id, user_id),
    KEY idx_type_status (generation_type, status),
    KEY idx_conversation (conversation_id),
    KEY idx_template (template_id),
    KEY idx_quality (quality_score DESC),
    FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE SET NULL,
    FOREIGN KEY (prompt_template_id) REFERENCES ai_prompt_templates(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI内容生成记录表';
```

### 14.2 初始数据插入脚本
```sql
-- 插入默认提示词模板
INSERT INTO ai_prompt_templates (
    tenant_id, template_code, name, description, category, scenario,
    prompt_content, variables, model_config, status, created_by
) VALUES
(0, 'email_subject_optimizer', '邮件主题优化器', '优化邮件主题以提高打开率', 'email', 'subject_optimization',
'你是一个专业的邮件营销专家。请根据以下信息优化邮件主题：

行业：{{industry}}
目标受众：{{target_audience}}
语调：{{tone}}
原始主题：{{original_subject}}

请提供3-5个优化后的主题建议，每个主题都应该：
1. 吸引目标受众的注意力
2. 符合行业特点
3. 保持适当的语调
4. 长度控制在50字符以内

请按照以下格式输出：
1. [优化主题1]
2. [优化主题2]
...',
JSON_OBJECT(
    'industry', JSON_OBJECT('type', 'string', 'description', '行业类型', 'required', true),
    'target_audience', JSON_OBJECT('type', 'string', 'description', '目标受众', 'required', true),
    'tone', JSON_OBJECT('type', 'enum', 'description', '语调风格', 'options', JSON_ARRAY('professional', 'casual', 'urgent', 'friendly'), 'default', 'professional'),
    'original_subject', JSON_OBJECT('type', 'string', 'description', '原始邮件主题', 'required', true)
),
JSON_OBJECT('temperature', 0.7, 'max_tokens', 500),
'active', 1),

(0, 'email_content_generator', '邮件内容生成器', '生成完整的邮件内容', 'email', 'content_generation',
'你是一个专业的邮件内容创作专家。请根据以下要求生成邮件内容：

邮件类型：{{email_type}}
主题：{{subject}}
目标受众：{{target_audience}}
关键信息：{{key_points}}
行动号召：{{cta}}
语调：{{tone}}

请生成一份完整的邮件内容，包括：
1. 个性化的开头问候
2. 清晰的正文内容
3. 明确的行动号召
4. 专业的结尾

内容要求：
- 语言简洁明了
- 结构清晰
- 符合目标受众特点
- 包含适当的情感元素',
JSON_OBJECT(
    'email_type', JSON_OBJECT('type', 'enum', 'description', '邮件类型', 'options', JSON_ARRAY('newsletter', 'promotion', 'welcome', 'follow_up', 'announcement'), 'required', true),
    'subject', JSON_OBJECT('type', 'string', 'description', '邮件主题', 'required', true),
    'target_audience', JSON_OBJECT('type', 'string', 'description', '目标受众', 'required', true),
    'key_points', JSON_OBJECT('type', 'array', 'description', '关键信息点', 'required', true),
    'cta', JSON_OBJECT('type', 'string', 'description', '行动号召', 'required', false),
    'tone', JSON_OBJECT('type', 'enum', 'description', '语调风格', 'options', JSON_ARRAY('professional', 'casual', 'urgent', 'friendly'), 'default', 'professional')
),
JSON_OBJECT('temperature', 0.8, 'max_tokens', 1000),
'active', 1),

(0, 'translation_assistant', '翻译助手', '多语言翻译助手', 'translation', 'content_translation',
'你是一个专业的翻译专家。请将以下内容从{{source_language}}翻译成{{target_language}}：

原文：{{content}}

翻译要求：
1. 保持原文的语调和风格
2. 确保术语的准确性
3. 适应目标语言的文化背景
4. 保持格式和结构不变

请提供准确、自然的翻译结果。',
JSON_OBJECT(
    'content', JSON_OBJECT('type', 'string', 'description', '待翻译内容', 'required', true),
    'source_language', JSON_OBJECT('type', 'string', 'description', '源语言', 'required', true),
    'target_language', JSON_OBJECT('type', 'string', 'description', '目标语言', 'required', true)
),
JSON_OBJECT('temperature', 0.3, 'max_tokens', 2000),
'active', 1);
```

## 15. 部署配置

### 15.1 Docker配置
```dockerfile
# Dockerfile for AI Tool Service
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o ai-tool-service ./cmd/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/ai-tool-service .
COPY --from=builder /app/configs ./configs

EXPOSE 8090
CMD ["./ai-tool-service"]
```

### 15.2 Docker Compose配置
```yaml
version: '3.8'

services:
  ai-tool-service:
    build: .
    ports:
      - "8090:8090"
    environment:
      - ENV=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      - vector-db
    volumes:
      - ./configs:/app/configs
      - ./logs:/app/logs
    networks:
      - email-system-network

  vector-db:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
    networks:
      - email-system-network

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: email_system
    volumes:
      - mysql_data:/var/lib/mysql
      - ./migrations:/docker-entrypoint-initdb.d
    networks:
      - email-system-network

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    networks:
      - email-system-network

volumes:
  mysql_data:
  redis_data:
  qdrant_data:

networks:
  email-system-network:
    driver: bridge
```

### 15.3 Kubernetes部署配置
```yaml
# ai-tool-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-tool-service
  labels:
    app: ai-tool-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-tool-service
  template:
    metadata:
      labels:
        app: ai-tool-service
    spec:
      containers:
      - name: ai-tool-service
        image: ai-tool-service:latest
        ports:
        - containerPort: 8090
        env:
        - name: DB_HOST
          value: "mysql-service"
        - name: REDIS_HOST
          value: "redis-service"
        - name: VECTOR_DB_HOST
          value: "qdrant-service"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8090
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8090
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: ai-tool-service
spec:
  selector:
    app: ai-tool-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8090
  type: LoadBalancer
```

## 16. 监控与运维

### 16.1 监控指标配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ai-tool-service'
    static_configs:
      - targets: ['ai-tool-service:8090']
    metrics_path: /metrics
    scrape_interval: 10s

  - job_name: 'vector-db'
    static_configs:
      - targets: ['qdrant:6333']
    metrics_path: /metrics
```

### 16.2 Grafana仪表板配置
```json
{
  "dashboard": {
    "title": "AI Tool Service Dashboard",
    "panels": [
      {
        "title": "API请求量",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "AI模型调用成功率",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(ai_model_requests_success_total[5m]) / rate(ai_model_requests_total[5m]) * 100"
          }
        ]
      },
      {
        "title": "Token使用量",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(ai_tokens_consumed_total[5m])",
            "legendFormat": "{{model}} {{tenant_id}}"
          }
        ]
      },
      {
        "title": "响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      }
    ]
  }
}
```

### 16.3 日志配置
```yaml
# logrus配置
logging:
  level: info
  format: json
  output: stdout
  fields:
    service: ai-tool-service
    version: v1.0

  # 结构化日志字段
  structured_fields:
    - tenant_id
    - user_id
    - session_id
    - model_provider
    - request_id
    - trace_id
```

## 17. 安全配置

### 17.1 API密钥管理
```go
// 密钥加密存储
type SecretManager struct {
    encryptionKey []byte
    vault         VaultClient
}

func (sm *SecretManager) StoreAPIKey(tenantID int64, provider string, apiKey string) error {
    // 加密API密钥
    encryptedKey, err := sm.encrypt(apiKey)
    if err != nil {
        return err
    }

    // 存储到安全存储
    secretPath := fmt.Sprintf("ai-keys/%d/%s", tenantID, provider)
    return sm.vault.Write(secretPath, map[string]interface{}{
        "api_key": encryptedKey,
        "created_at": time.Now(),
    })
}

func (sm *SecretManager) GetAPIKey(tenantID int64, provider string) (string, error) {
    secretPath := fmt.Sprintf("ai-keys/%d/%s", tenantID, provider)
    secret, err := sm.vault.Read(secretPath)
    if err != nil {
        return "", err
    }

    encryptedKey := secret["api_key"].(string)
    return sm.decrypt(encryptedKey)
}
```

### 17.2 访问控制配置
```yaml
# RBAC配置
rbac:
  roles:
    ai_admin:
      permissions:
        - ai:conversations:*
        - ai:templates:*
        - ai:configs:*
        - ai:statistics:read

    ai_user:
      permissions:
        - ai:conversations:create
        - ai:conversations:read:own
        - ai:templates:read
        - ai:content:generate

    ai_viewer:
      permissions:
        - ai:conversations:read:own
        - ai:templates:read
        - ai:statistics:read:own

  tenant_isolation:
    enabled: true
    enforce_tenant_id: true
    allow_cross_tenant: false
```

---

**文档状态**: 完整设计文档已完成，包含详细的技术实现、部署配置和运维方案
**文档版本**: v1.0
**最后更新**: 2025-08-18
**适用范围**: Email-System AI工具系统开发与部署
