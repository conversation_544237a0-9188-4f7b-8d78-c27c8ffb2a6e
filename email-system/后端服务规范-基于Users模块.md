# Email-System 后端服务开发规范（基于 Users 模块实践）

版本: v1.0.0  日期: 2025-08-13  适用范围: email-system 全链路后端开发

本文档基于 `users` 模块的服务端实现，总结并标准化 email-system 的架构分层、接口规范、错误处理、依赖注入、数据访问、可观测性、安全与测试等实践，确保风格一致、可维护与可扩展。

---

## 1. 架构与项目结构

- 采用 Clean Architecture + DDD 分层，禁止业务逻辑泄露到接口/基础设施层。
- 分层职责：
  - Interfaces/Transport: HTTP/gRPC 适配、参数校验、统一响应与错误翻译。
  - Application: 用例编排、事务边界、DTO 转换、领域服务协调。
  - Domain: 实体/值对象/聚合根/仓储接口/领域服务，保持纯净。
  - Infrastructure: 持久化实现、外部服务客户端、适配器、缓存等。

推荐目录（与 users 一致）：
```
cmd/                         # 入口(main)，生命周期、DI、服务注册
internal/
  interfaces/                # HTTP/gRPC/web 适配层
  application/               # 应用服务与用例
  domain/                    # 领域模型、仓储接口
  infrastructure/            # 持久化/外部依赖实现
api/                         # proto 或 HTTP 文档
configs/                     # 配置模板
pkg/                         # 本服务内共享包(可选)
```

注意：
- 公共库依赖统一使用根仓库下 `pkg/`（日志、响应、middleware、usercontext、otel、db 等）。
- 不允许跨越分层直接依赖低层实现，所有高层依赖通过接口注入。

---

## 2. HTTP API 规范

- 仅允许 GET/POST 方法；路由不使用 path 参数，统一使用 JSON Body 或 Query。示例参考 users：
```
POST /api/email/account/list
POST /api/email/account/get
POST /api/email/account/create
POST /api/email/account/update
POST /api/email/account/delete
```
- 统一响应结构使用 `pkg/common/response`，严禁返回内部错误细节：
```
type Response struct {
  Code    int         `json:"code"`
  Message string      `json:"message"`
  Data    interface{} `json:"data,omitempty"`
  Meta    *Meta       `json:"meta,omitempty"`
}
```
- 成功/错误返回：`response.Success/Created/Updated/Deleted/Paginated`、`response.ValidationError/Unauthorized/Forbidden/NotFound/TooManyRequests/InternalError`。
- ID/分页参数：支持字符串/数字混合输入，业务层统一转为 int64；分页统一传 `page`/`size` 或 `offset`/`limit`，不要在 Example/Model 中塞分页。

---

## 3. 中间件与路由装配

- 必须启用统一中间件（参考 users `internal/interfaces/http/routes/routes.go`）：
  - RequestID/TraceID 注入
  - Recovery（隐藏栈）
  - Metrics（Prometheus）
  - 安全响应头
  - 请求体大小限制
  - 超时（按接口配置，默认 10s）
  - 访问日志（敏感字段脱敏）
  - 用户与应用信息注入（见第4节）
- 统一在 `routes.SetupRoutes(engine, container, ...)` 中完成分组与注册；所有业务路由放在 `config.GlobalAPIPrefix` 下；鉴权组务必使用 `RequireAuthedMiddleware()`。

---

## 4. 用户上下文与租户/应用隔离（email 模块持有 appId）

- 获取方式：全部从中间件注入的 `context.Context` 获取，不允许在 handler 中手工注入未校验的值。
- email-system 的 handler 仅做“读取 context → 构造 DTO → 调用应用服务”。
- 邮件域（账户、模板、发送任务）在 email 模块内以 `(tenantId, internalAppId)` 作为隔离与归属键。
- email-system 通过“固定配置的 internalAppId”对接 email 模块：
  - email-system 自身不变更 appId；仅在调用 email 模块 API 时附带固定的 `internal_app_id`。
  - 领域内唯一性、查询筛选均由 email 模块按 `(tenantId, internalAppId, ...)` 执行。
- 与 `pkg/usercontext` 对齐，禁止依赖全局变量；必要时通过适配器从 gRPC/HTTP 注入。

集成边界：
- 账户/模板：由 email 模块自主管理与持久化；email-system 通过 email 模块 API 进行创建/更新/查询，不直接持久化。
- 发送请求：email-system 通过 email 模块 API 写入 `email_messages` 表，实现“请求-执行”隔离（无消息队列方案）。

---

## 5. 依赖注入（DI）与生命周期

- 构造函数注入所有必需依赖；严禁运行时 `nil` 检查（fail fast）。参考 users `internal/infrastructure/container/dependency_container.go`：
  - 分阶段初始化：基础设施 → 仓储 → 领域服务/工厂 → 应用服务 → 处理器。
  - 构建 HTTP/gRPC 服务器并注册路由/服务。
- 依赖不可变（只读字段）；在 `cmd/main.go` 中统一初始化日志、配置、OTel、DI 容器、HTTP/gRPC 服务注册。

---

## 6. 日志与可观测性

- 日志：使用 `pkg/logiface`，所有日志必须携带 `context.Context`，自动贯穿 trace_id。
- 等级与分流：App/Access/Error 三流；敏感字段脱敏（如密码、token）。
- OpenTelemetry：使用 `pkg/otel` 初始化 TracerProvider；中间件自动注入 TraceID；数据库与外部调用建议打点；TraceID 回传至响应 Meta。

---

## 7. DTO 与校验

- 入参 DTO 使用 `binding` tag（gin + go-playground/validator）进行字段约束；复杂规则在应用服务中做二次校验（如策略校验）。
- 统一错误翻译：
  - 参数绑定错误使用 `response.GinValidationError` 或 `response.ValidationError`（字段级）。
  - 领域/业务错误翻译为统一的业务错误码与消息（见第8节）。
- 切勿将系统错误 `err.Error()` 直接返回前端。

---

## 8. 错误处理与错误码

- 参考 users 的自定义错误体系：模块内定义错误类型与错误码区间，HTTP Handler 负责将其映射到统一响应（见 users `internal/interfaces/http/handlers/error_handler.go`）。
- 错误分类：
  - 参数/校验错误：字段级返回。
  - 资源/状态类业务错误：返回业务错误码与友好消息。
  - 系统错误：统一 `InternalError`，不暴露细节。
- 错误包装：所有向上返回错误使用 `fmt.Errorf("context: %w", err)`，便于追踪。

建议 email-system 的错误码区间：
- 邮件账户/发件配置：120000-120099
- 邮件模板：120100-120199
- 邮件发送/任务：120200-120299
- 附件与存储：120300-120399
- 第三方服务：120800-120899
- 系统错误：120900-120999

---

## 9. 数据访问（GORM）与仓储模式

- 禁止自动预加载（不使用 `Preload` 默认行为）；先查主实体，关联数据按需手动加载。
- 避免 N+1：收集 ID 批量查询并组装；必要时并发查询，注意上下文取消与超时。
- 仓储接口定义在领域层，具体实现放在 `infrastructure/persistence`；通用方法可抽到 `BaseRepository`；查询构造使用参数对象（QueryParams）。
- 事务边界：在应用服务中明确开启/提交/回滚；避免跨层隐式事务。
- 上下文：所有 DB 调用使用 `db.WithContext(ctx)`，尊重超时与取消。

建议 QueryParams 关键字段：
- 基础过滤：IDs、TenantID、Status、Keyword
- 业务开关：WithProvider、WithStats...
- 分页排序：Page/Size 或 Offset/Limit、OrderBy/OrderDir

---

## 10. 安全与韧性

- 输入校验与清洗：邮箱地址、域名、回邮地址、OAuth 凭据等需严格校验与长度限制。
- 配置安全：敏感配置通过配置中心与环境变量注入；不得硬编码。
- 外部调用：统一重试、指数退避、超时、熔断；记录上下文 Trace；错误统一包装并翻译。
- 速率限制与防刷：按 IP/用户/租户限流（中间件或网关）。
- 文件与附件：限制大小/类型、病毒扫描（如接入外部服务）、临时存储自动清理。

---

## 11. 邮件域特定约束（继承 users 规范并结合 email）

- 应用隔离：所有“账户/模板/任务/日志”均以 `tenantId` 作为隔离与归属。
- 发送任务：任务创建、分片、重试与状态推进由应用服务控制；状态机持久化；失败原因仅保留摘要返回，细节入日志。
- 账号与提供商：账号唯一性在 `(tenantId, provider, account)` 范围内校验；禁用自动发现，显式配置。
- 模板：保存、预览、渲染与变量校验分层实现；渲染错误不向前端泄露模板引擎细节。
- 发送通道：第三方 API/SMTP 客户端通过接口定义并由 DI 注入；禁止在应用服务内 new 具体实现。

所有账户/模板均在 email 模块持久化；email-system 仅记录业务侧引用（如模板代码、账户引用 ID），不落库账户与模板详情。

---

## 12. 路由与 Handler 模式（示例）

- 路由注册：
```
engine := gin.New()
auth := engine.Group(apis.Prefix)
auth.Use(httpmiddleware.RequireAuthedMiddleware())
{
  auth.POST("/email/account/list", accountHandler.List)
  auth.POST("/email/account/create", accountHandler.Create)
  auth.POST("/email/account/get", accountHandler.Get)
  auth.POST("/email/account/update", accountHandler.Update)
  auth.POST("/email/account/delete", accountHandler.Delete)
}
```
- Handler 模式（精简）：
```
func (h *AccountHandler) Create(c *gin.Context) {
  var req dto.CreateAccountRequest
  if err := c.ShouldBindJSON(&req); err != nil { response.GinValidationError(c, err); return }
  req.TenantID = getTenantIDFromContext(c)
  // 通过 email 模块客户端调用创建，email-system 不直接持久化账户
  out, err := h.emailClient.Accounts.Create(c.Request.Context(), &req)
  if err != nil { HandleEmailError(c, err); return }
  response.Created(c, out)
}
```

---

## 13. 测试规范

- 单元测试：表驱动，必要用例并行；对外部接口统一 mock；导出函数需覆盖；`go test -cover`。
- 集成测试：对主要用例编排与仓储进行真实 DB 测试，使用事务回滚或隔离库。
- 基准测试：模板渲染/批量发送路径可加 benchmark 以监控回归。

---

## 14. 配置与运行

- 配置中心：借鉴 users Nacos 方案，支持热更新；日志级别/DB/外部服务端点可动态调整。
- OTel：`configs` 中提供可选端点；为空则跳过初始化。
- 服务注册：如需 gRPC，参考 users 对接注册中心；HTTP 必须暴露 `/health` 并返回统一响应。

---

## 15. 前后端契约

- 前端以响应 `code` 字段判定成功（保持与现有前端约定一致）；关键状态码定义常量；其他场景展示服务端 `message` 字段。
- 分页与搜索实时性：搜索触发时重置至第一页；空列表返回空数组而非 null。

---

## 16. 清单（Checklist）

- [ ] 路由仅用 GET/POST，且无 path 参数
- [ ] 启用统一中间件栈（Trace/Recovery/Metrics/Security/Timeout/AccessLog）
- [ ] 响应结构统一且隐藏内部错误
- [ ] 邮件域以 tenantId 作为隔离与归属
- [ ] 构造函数注入，禁止运行时 nil 检查
- [ ] 所有 DB 调用使用 `WithContext(ctx)` 且无自动预加载
- [ ] 关联查询批量加载，避免 N+1
- [ ] DTO 校验 + 统一错误翻译
- [ ] 外部调用具备超时/重试/熔断/限流
- [ ] 日志结构化 + trace 贯穿 + 敏感字段脱敏
- [ ] 单测覆盖导出函数，集成测试覆盖关键链路

---

## 17. 参考（来自 users 模块实现）

- 依赖注入容器：`users/internal/infrastructure/container/dependency_container.go`
- 中间件装配：`users/internal/interfaces/http/routes/routes.go`
- 统一响应与错误翻译：`users/internal/interfaces/http/handlers/error_handler.go`
- 应用服务模式：`users/internal/application/user/service/user_application_service.go`
- 仓储与查询：`users/internal/infrastructure/persistence/*.go`
- 上下文与隔离：`pkg/usercontext` 与 users 相关适配层
- 可观测性：`pkg/otel`, users `cmd/main.go`

注：本规范与仓库根级别“Workspace Rules”和“Backend Go Guidelines”完全兼容；如有冲突，以 workspace 全局规则优先。


## 18. 与 email 模块对接接口（草案）

- 账户管理：
  - POST `/api/email/account/create` { tenant_id, name, provider, type, from_address, smtp_config, limits }
  - POST `/api/email/account/update` { id, tenant_id, ... }
  - POST `/api/email/account/get` { id, tenant_id }
  - POST `/api/email/account/list` { tenant_id, keyword, page, size }
  - POST `/api/email/account/delete` { id, tenant_id }
- 模板管理：
  - POST `/api/email/template/create` { tenant_id, template_code, account_id, subject, html_content, variables, rate_limits }
  - POST `/api/email/template/update` { id, tenant_id, ... }
  - POST `/api/email/template/get` { id, tenant_id } 或 { tenant_id, template_code }
  - POST `/api/email/template/list` { tenant_id, status, page, size }
  - POST `/api/email/template/delete` { id, tenant_id }
- 发送请求：
  - POST `/api/email/message/send` { tenant_id, template_code | subject/html_content, from_address | account_id, to[], cc[], bcc[], variables, priority }

约束：
- 所有接口均使用统一响应；校验错误字段级返回；系统错误隐藏。
- 请求必须携带 `tenant_id`，并由 email-system 注入固定的 `internal_app_id` 与之一起传入 email 模块。


