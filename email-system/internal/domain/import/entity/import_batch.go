package entity

import (
	"time"
)

// ImportBatchStatus 导入批次状态
type ImportBatchStatus string

const (
	ImportBatchStatusPending    ImportBatchStatus = "pending"    // 待处理
	ImportBatchStatusProcessing ImportBatchStatus = "processing" // 处理中
	ImportBatchStatusCompleted  ImportBatchStatus = "completed"  // 已完成
	ImportBatchStatusFailed     ImportBatchStatus = "failed"     // 失败
)

// IsValid 检查状态是否有效
func (s ImportBatchStatus) IsValid() bool {
	switch s {
	case ImportBatchStatusPending, ImportBatchStatusProcessing, ImportBatchStatusCompleted, ImportBatchStatusFailed:
		return true
	default:
		return false
	}
}

// ImportBatch 导入批次实体
type ImportBatch struct {
	ID             int64             `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID       int64             `json:"tenant_id" gorm:"not null;index:idx_import_batch_tenant"`
	JobID          int64             `json:"job_id" gorm:"not null;index:idx_import_batch_job"`
	BatchNo        int               `json:"batch_no" gorm:"not null;uniqueIndex:uk_import_batch"`
	StartRow       int64             `json:"start_row" gorm:"not null"`
	EndRow         int64             `json:"end_row" gorm:"not null"`
	BatchSize      int               `json:"batch_size" gorm:"not null"`
	Status         ImportBatchStatus `json:"status" gorm:"not null;default:'pending';size:32"`
	ProcessedCount int               `json:"processed_count" gorm:"not null;default:0"`
	SuccessCount   int               `json:"success_count" gorm:"not null;default:0"`
	FailedCount    int               `json:"failed_count" gorm:"not null;default:0"`
	StartedAt      *time.Time        `json:"started_at"`
	FinishedAt     *time.Time        `json:"finished_at"`
	ErrorMessage   *string           `json:"error_message" gorm:"type:text"`
	CreatedAt      time.Time         `json:"created_at" gorm:"not null;autoCreateTime"`
	UpdatedAt      time.Time         `json:"updated_at" gorm:"not null;autoUpdateTime"`
}

// TableName 指定表名
func (ImportBatch) TableName() string {
	return "import_batches"
}

// IsPending 是否为待处理状态
func (ib *ImportBatch) IsPending() bool {
	return ib.Status == ImportBatchStatusPending
}

// IsProcessing 是否在处理中
func (ib *ImportBatch) IsProcessing() bool {
	return ib.Status == ImportBatchStatusProcessing
}

// IsCompleted 是否已完成
func (ib *ImportBatch) IsCompleted() bool {
	return ib.Status == ImportBatchStatusCompleted
}

// IsFailed 是否失败
func (ib *ImportBatch) IsFailed() bool {
	return ib.Status == ImportBatchStatusFailed
}

// Start 开始处理
func (ib *ImportBatch) Start() {
	now := time.Now()
	ib.Status = ImportBatchStatusProcessing
	ib.StartedAt = &now
	ib.UpdatedAt = now
}

// UpdateProgress 更新处理进度
func (ib *ImportBatch) UpdateProgress(processedCount, successCount, failedCount int) {
	ib.ProcessedCount = processedCount
	ib.SuccessCount = successCount
	ib.FailedCount = failedCount
	ib.UpdatedAt = time.Now()
}

// Complete 完成处理
func (ib *ImportBatch) Complete() {
	now := time.Now()
	ib.Status = ImportBatchStatusCompleted
	ib.FinishedAt = &now
	ib.UpdatedAt = now
}

// Fail 标记失败
func (ib *ImportBatch) Fail(errorMessage string) {
	now := time.Now()
	ib.Status = ImportBatchStatusFailed
	ib.FinishedAt = &now
	ib.ErrorMessage = &errorMessage
	ib.UpdatedAt = now
}

// GetProgressPercentage 获取进度百分比
func (ib *ImportBatch) GetProgressPercentage() int {
	if ib.BatchSize == 0 {
		return 0
	}
	return (ib.ProcessedCount * 100) / ib.BatchSize
}

// GetDuration 获取处理时长
func (ib *ImportBatch) GetDuration() *time.Duration {
	if ib.StartedAt == nil {
		return nil
	}

	endTime := time.Now()
	if ib.FinishedAt != nil {
		endTime = *ib.FinishedAt
	}

	duration := endTime.Sub(*ib.StartedAt)
	return &duration
}

// Validate 验证批次数据
func (ib *ImportBatch) Validate() error {
	if ib.TenantID <= 0 {
		return NewImportBatchValidationError("tenant_id", "租户ID不能为空")
	}

	if ib.JobID <= 0 {
		return NewImportBatchValidationError("job_id", "任务ID不能为空")
	}

	if ib.BatchNo < 0 {
		return NewImportBatchValidationError("batch_no", "批次号不能为负数")
	}

	if ib.StartRow <= 0 {
		return NewImportBatchValidationError("start_row", "起始行号必须大于0")
	}

	if ib.EndRow <= 0 {
		return NewImportBatchValidationError("end_row", "结束行号必须大于0")
	}

	if ib.StartRow > ib.EndRow {
		return NewImportBatchValidationError("start_row", "起始行号不能大于结束行号")
	}

	if ib.BatchSize <= 0 {
		return NewImportBatchValidationError("batch_size", "批次大小必须大于0")
	}

	expectedSize := int(ib.EndRow - ib.StartRow + 1)
	if ib.BatchSize != expectedSize {
		return NewImportBatchValidationError("batch_size", "批次大小与行号范围不匹配")
	}

	if !ib.Status.IsValid() {
		return NewImportBatchValidationError("status", "无效的批次状态")
	}

	if ib.ProcessedCount < 0 {
		return NewImportBatchValidationError("processed_count", "已处理数量不能为负数")
	}

	if ib.SuccessCount < 0 {
		return NewImportBatchValidationError("success_count", "成功数量不能为负数")
	}

	if ib.FailedCount < 0 {
		return NewImportBatchValidationError("failed_count", "失败数量不能为负数")
	}

	if ib.ProcessedCount > ib.BatchSize {
		return NewImportBatchValidationError("processed_count", "已处理数量不能大于批次大小")
	}

	if ib.SuccessCount+ib.FailedCount > ib.ProcessedCount {
		return NewImportBatchValidationError("processed_count", "成功数量加失败数量不能大于已处理数量")
	}

	return nil
}

// Clone 克隆批次实体
func (ib *ImportBatch) Clone() *ImportBatch {
	clone := *ib

	if ib.StartedAt != nil {
		startedAt := *ib.StartedAt
		clone.StartedAt = &startedAt
	}

	if ib.FinishedAt != nil {
		finishedAt := *ib.FinishedAt
		clone.FinishedAt = &finishedAt
	}

	if ib.ErrorMessage != nil {
		errorMessage := *ib.ErrorMessage
		clone.ErrorMessage = &errorMessage
	}

	return &clone
}
