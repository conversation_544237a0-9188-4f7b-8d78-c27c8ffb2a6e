package entity

import (
	"encoding/json"
	"fmt"
	"time"
)

// ScenarioStatus 场景状态
type ScenarioStatus string

const (
	ScenarioStatusActive   ScenarioStatus = "active"   // 激活
	ScenarioStatusInactive ScenarioStatus = "inactive" // 停用
)

// IsValid 检查状态是否有效
func (s ScenarioStatus) IsValid() bool {
	switch s {
	case ScenarioStatusActive, ScenarioStatusInactive:
		return true
	default:
		return false
	}
}

// ImportScenarioConfig 导入场景配置实体
type ImportScenarioConfig struct {
	ID                int64          `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID          int64          `json:"tenant_id" gorm:"not null;index:idx_scenario_tenant"`
	ScenarioCode      string         `json:"scenario_code" gorm:"not null;size:32;uniqueIndex:uk_scenario_code"`
	ScenarioName      string         `json:"scenario_name" gorm:"not null;size:64"`
	Description       *string        `json:"description" gorm:"size:255"`
	TargetEntityType  string         `json:"target_entity_type" gorm:"not null;size:32"`
	RequiredFields    string         `json:"required_fields" gorm:"not null;type:json"`
	OptionalFields    *string        `json:"optional_fields" gorm:"type:json"`
	ValidationRules   *string        `json:"validation_rules" gorm:"type:json"`
	DefaultMapping    *string        `json:"default_mapping" gorm:"type:json"`
	PostProcessConfig *string        `json:"post_process_config" gorm:"type:json"`
	Status            ScenarioStatus `json:"status" gorm:"not null;default:'active';size:16"`
	CreatedBy         *int64         `json:"created_by"`
	CreatedAt         time.Time      `json:"created_at" gorm:"not null;autoCreateTime"`
	UpdatedAt         time.Time      `json:"updated_at" gorm:"not null;autoUpdateTime"`
}

// TableName 指定表名
func (ImportScenarioConfig) TableName() string {
	return "import_scenario_configs"
}

// GetRequiredFields 获取必填字段列表
func (isc *ImportScenarioConfig) GetRequiredFields() ([]string, error) {
	var fields []string
	if err := json.Unmarshal([]byte(isc.RequiredFields), &fields); err != nil {
		return nil, fmt.Errorf("unmarshal required fields: %w", err)
	}
	return fields, nil
}

// SetRequiredFields 设置必填字段列表
func (isc *ImportScenarioConfig) SetRequiredFields(fields []string) error {
	data, err := json.Marshal(fields)
	if err != nil {
		return fmt.Errorf("marshal required fields: %w", err)
	}
	isc.RequiredFields = string(data)
	return nil
}

// GetOptionalFields 获取可选字段列表
func (isc *ImportScenarioConfig) GetOptionalFields() ([]string, error) {
	if isc.OptionalFields == nil || *isc.OptionalFields == "" {
		return []string{}, nil
	}

	var fields []string
	if err := json.Unmarshal([]byte(*isc.OptionalFields), &fields); err != nil {
		return nil, fmt.Errorf("unmarshal optional fields: %w", err)
	}
	return fields, nil
}

// SetOptionalFields 设置可选字段列表
func (isc *ImportScenarioConfig) SetOptionalFields(fields []string) error {
	if fields == nil {
		isc.OptionalFields = nil
		return nil
	}

	data, err := json.Marshal(fields)
	if err != nil {
		return fmt.Errorf("marshal optional fields: %w", err)
	}

	fieldsStr := string(data)
	isc.OptionalFields = &fieldsStr
	return nil
}

// GetValidationRules 获取验证规则
func (isc *ImportScenarioConfig) GetValidationRules() (map[string]interface{}, error) {
	if isc.ValidationRules == nil || *isc.ValidationRules == "" {
		return make(map[string]interface{}), nil
	}

	var rules map[string]interface{}
	if err := json.Unmarshal([]byte(*isc.ValidationRules), &rules); err != nil {
		return nil, fmt.Errorf("unmarshal validation rules: %w", err)
	}
	return rules, nil
}

// SetValidationRules 设置验证规则
func (isc *ImportScenarioConfig) SetValidationRules(rules map[string]interface{}) error {
	if rules == nil {
		isc.ValidationRules = nil
		return nil
	}

	data, err := json.Marshal(rules)
	if err != nil {
		return fmt.Errorf("marshal validation rules: %w", err)
	}

	rulesStr := string(data)
	isc.ValidationRules = &rulesStr
	return nil
}

// GetDefaultMapping 获取默认字段映射
func (isc *ImportScenarioConfig) GetDefaultMapping() (map[string]string, error) {
	if isc.DefaultMapping == nil || *isc.DefaultMapping == "" {
		return make(map[string]string), nil
	}

	var mapping map[string]string
	if err := json.Unmarshal([]byte(*isc.DefaultMapping), &mapping); err != nil {
		return nil, fmt.Errorf("unmarshal default mapping: %w", err)
	}
	return mapping, nil
}

// SetDefaultMapping 设置默认字段映射
func (isc *ImportScenarioConfig) SetDefaultMapping(mapping map[string]string) error {
	if mapping == nil {
		isc.DefaultMapping = nil
		return nil
	}

	data, err := json.Marshal(mapping)
	if err != nil {
		return fmt.Errorf("marshal default mapping: %w", err)
	}

	mappingStr := string(data)
	isc.DefaultMapping = &mappingStr
	return nil
}

// GetPostProcessConfig 获取后处理配置
func (isc *ImportScenarioConfig) GetPostProcessConfig() (map[string]interface{}, error) {
	if isc.PostProcessConfig == nil || *isc.PostProcessConfig == "" {
		return make(map[string]interface{}), nil
	}

	var config map[string]interface{}
	if err := json.Unmarshal([]byte(*isc.PostProcessConfig), &config); err != nil {
		return nil, fmt.Errorf("unmarshal post process config: %w", err)
	}
	return config, nil
}

// SetPostProcessConfig 设置后处理配置
func (isc *ImportScenarioConfig) SetPostProcessConfig(config map[string]interface{}) error {
	if config == nil {
		isc.PostProcessConfig = nil
		return nil
	}

	data, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("marshal post process config: %w", err)
	}

	configStr := string(data)
	isc.PostProcessConfig = &configStr
	return nil
}

// IsActive 是否为激活状态
func (isc *ImportScenarioConfig) IsActive() bool {
	return isc.Status == ScenarioStatusActive
}

// Activate 激活场景
func (isc *ImportScenarioConfig) Activate() {
	isc.Status = ScenarioStatusActive
	isc.UpdatedAt = time.Now()
}

// Deactivate 停用场景
func (isc *ImportScenarioConfig) Deactivate() {
	isc.Status = ScenarioStatusInactive
	isc.UpdatedAt = time.Now()
}

// Validate 验证场景配置数据
func (isc *ImportScenarioConfig) Validate() error {
	if isc.TenantID <= 0 {
		return NewImportScenarioValidationError("tenant_id", "租户ID不能为空")
	}

	if isc.ScenarioCode == "" {
		return NewImportScenarioValidationError("scenario_code", "场景代码不能为空")
	}

	if len(isc.ScenarioCode) > 32 {
		return NewImportScenarioValidationError("scenario_code", "场景代码长度不能超过32个字符")
	}

	if isc.ScenarioName == "" {
		return NewImportScenarioValidationError("scenario_name", "场景名称不能为空")
	}

	if len(isc.ScenarioName) > 64 {
		return NewImportScenarioValidationError("scenario_name", "场景名称长度不能超过64个字符")
	}

	if isc.TargetEntityType == "" {
		return NewImportScenarioValidationError("target_entity_type", "目标实体类型不能为空")
	}

	if isc.RequiredFields == "" {
		return NewImportScenarioValidationError("required_fields", "必填字段列表不能为空")
	}

	if !isc.Status.IsValid() {
		return NewImportScenarioValidationError("status", "无效的场景状态")
	}

	// 验证必填字段JSON格式
	var requiredFields []string
	if err := json.Unmarshal([]byte(isc.RequiredFields), &requiredFields); err != nil {
		return NewImportScenarioValidationError("required_fields", "必填字段格式无效")
	}

	if len(requiredFields) == 0 {
		return NewImportScenarioValidationError("required_fields", "至少需要一个必填字段")
	}

	return nil
}

// Clone 克隆场景配置实体
func (isc *ImportScenarioConfig) Clone() *ImportScenarioConfig {
	clone := *isc

	if isc.Description != nil {
		description := *isc.Description
		clone.Description = &description
	}

	if isc.OptionalFields != nil {
		optionalFields := *isc.OptionalFields
		clone.OptionalFields = &optionalFields
	}

	if isc.ValidationRules != nil {
		validationRules := *isc.ValidationRules
		clone.ValidationRules = &validationRules
	}

	if isc.DefaultMapping != nil {
		defaultMapping := *isc.DefaultMapping
		clone.DefaultMapping = &defaultMapping
	}

	if isc.PostProcessConfig != nil {
		postProcessConfig := *isc.PostProcessConfig
		clone.PostProcessConfig = &postProcessConfig
	}

	if isc.CreatedBy != nil {
		createdBy := *isc.CreatedBy
		clone.CreatedBy = &createdBy
	}

	return &clone
}
