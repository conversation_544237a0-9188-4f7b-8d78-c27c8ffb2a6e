package entity

import "fmt"

// ImportServiceError 导入服务错误 - 标准化错误结构（避免与ImportError实体冲突）
type ImportServiceError struct {
	Code    int
	Message string
	Field   string
	Details string
}

func (e *ImportServiceError) Error() string {
	if e.Field != "" {
		return fmt.Sprintf("[%d] %s: %s", e.Code, e.Field, e.Message)
	}
	return fmt.Sprintf("[%d] %s", e.Code, e.Message)
}

// 错误码定义 - 导入系统错误码范围: 120400-120499
const (
	// 导入任务验证错误 (120400-120409)
	CodeImportValidationError         = 120400
	CodeImportJobNotFound             = 120401
	CodeImportBusinessScenarioInvalid = 120402
	CodeImportFileTypeInvalid         = 120403
	CodeImportConfigInvalid           = 120404

	// 导入任务操作错误 (120410-120419)
	CodeImportJobCreateFailed  = 120410
	CodeImportJobUpdateFailed  = 120411
	CodeImportJobDeleteFailed  = 120412
	CodeImportJobProcessFailed = 120413
	CodeImportJobCancelFailed  = 120414

	// 导入任务状态错误 (120420-120429)
	CodeImportJobStatusInvalid = 120420
	CodeImportJobStatusError   = 120421

	// 导入文件处理错误 (120430-120439)
	CodeImportFileParsingFailed    = 120430
	CodeImportFileValidationFailed = 120431
	CodeImportFileProcessingFailed = 120432
	CodeImportFileNotAccessible    = 120433

	// 导入权限错误 (120440-120449)
	CodeImportPermissionDenied = 120440
	CodeImportUserUnauthorized = 120441
	CodeImportResourceNotFound = 120442
)

// 标准化错误构造函数
func NewImportJobNotFoundServiceError(jobID, tenantID int64) *ImportServiceError {
	return &ImportServiceError{
		Code:    CodeImportJobNotFound,
		Message: "导入任务不存在",
		Details: fmt.Sprintf("jobID: %d, tenantID: %d", jobID, tenantID),
	}
}

func NewImportJobValidationServiceError(field, message string) *ImportServiceError {
	return &ImportServiceError{
		Code:    CodeImportValidationError,
		Message: message,
		Field:   field,
	}
}

func NewImportJobCreateFailedServiceError(details string) *ImportServiceError {
	return &ImportServiceError{
		Code:    CodeImportJobCreateFailed,
		Message: "创建导入任务失败",
		Details: details,
	}
}

func NewImportJobProcessFailedServiceError(details string) *ImportServiceError {
	return &ImportServiceError{
		Code:    CodeImportJobProcessFailed,
		Message: "处理导入任务失败",
		Details: details,
	}
}

func NewImportJobCancelFailedServiceError(details string) *ImportServiceError {
	return &ImportServiceError{
		Code:    CodeImportJobCancelFailed,
		Message: "取消导入任务失败",
		Details: details,
	}
}

func NewImportJobDeleteFailedServiceError(details string) *ImportServiceError {
	return &ImportServiceError{
		Code:    CodeImportJobDeleteFailed,
		Message: "删除导入任务失败",
		Details: details,
	}
}

func NewImportJobStatusInvalidServiceError(status string) *ImportServiceError {
	return &ImportServiceError{
		Code:    CodeImportJobStatusInvalid,
		Message: "导入任务状态无效",
		Details: fmt.Sprintf("status: %s", status),
	}
}

func NewImportPermissionDeniedServiceError() *ImportServiceError {
	return &ImportServiceError{
		Code:    CodeImportPermissionDenied,
		Message: "没有权限执行此操作",
	}
}

func NewImportBusinessScenarioInvalidServiceError(scenario string) *ImportServiceError {
	return &ImportServiceError{
		Code:    CodeImportBusinessScenarioInvalid,
		Message: "不支持的业务场景",
		Details: fmt.Sprintf("scenario: %s", scenario),
	}
}

func NewImportFileTypeInvalidServiceError(fileType string) *ImportServiceError {
	return &ImportServiceError{
		Code:    CodeImportFileTypeInvalid,
		Message: "不支持的文件类型",
		Details: fmt.Sprintf("fileType: %s", fileType),
	}
}

// ==== 保留原有错误类型以保持兼容性 ====

// ImportJobValidationError 导入任务验证错误
type ImportJobValidationError struct {
	Field   string
	Message string
}

func (e ImportJobValidationError) Error() string {
	return fmt.Sprintf("import job validation error on field '%s': %s", e.Field, e.Message)
}

// NewImportJobValidationError 创建导入任务验证错误
func NewImportJobValidationError(field, message string) error {
	return ImportJobValidationError{
		Field:   field,
		Message: message,
	}
}

// ImportScenarioValidationError 导入场景配置验证错误
type ImportScenarioValidationError struct {
	Field   string
	Message string
}

func (e ImportScenarioValidationError) Error() string {
	return fmt.Sprintf("import scenario validation error on field '%s': %s", e.Field, e.Message)
}

// NewImportScenarioValidationError 创建导入场景配置验证错误
func NewImportScenarioValidationError(field, message string) error {
	return ImportScenarioValidationError{
		Field:   field,
		Message: message,
	}
}

// ImportErrorValidationError 导入错误记录验证错误
type ImportErrorValidationError struct {
	Field   string
	Message string
}

func (e ImportErrorValidationError) Error() string {
	return fmt.Sprintf("import error validation error on field '%s': %s", e.Field, e.Message)
}

// NewImportErrorValidationError 创建导入错误记录验证错误
func NewImportErrorValidationError(field, message string) error {
	return ImportErrorValidationError{
		Field:   field,
		Message: message,
	}
}

// ImportBatchValidationError 导入批次验证错误
type ImportBatchValidationError struct {
	Field   string
	Message string
}

func (e ImportBatchValidationError) Error() string {
	return fmt.Sprintf("import batch validation error on field '%s': %s", e.Field, e.Message)
}

// NewImportBatchValidationError 创建导入批次验证错误
func NewImportBatchValidationError(field, message string) error {
	return ImportBatchValidationError{
		Field:   field,
		Message: message,
	}
}

// ImportNotFoundError 导入任务未找到错误
type ImportNotFoundError struct {
	ID       int64
	TenantID int64
}

func (e ImportNotFoundError) Error() string {
	return fmt.Sprintf("import job not found: id=%d, tenant_id=%d", e.ID, e.TenantID)
}

// NewImportNotFoundError 创建导入任务未找到错误
func NewImportNotFoundError(id, tenantID int64) error {
	return ImportNotFoundError{
		ID:       id,
		TenantID: tenantID,
	}
}

// ImportScenarioNotFoundError 导入场景配置未找到错误
type ImportScenarioNotFoundError struct {
	Code     string
	TenantID int64
}

func (e ImportScenarioNotFoundError) Error() string {
	return fmt.Sprintf("import scenario config not found: code=%s, tenant_id=%d", e.Code, e.TenantID)
}

// NewImportScenarioNotFoundError 创建导入场景配置未找到错误
func NewImportScenarioNotFoundError(code string, tenantID int64) error {
	return ImportScenarioNotFoundError{
		Code:     code,
		TenantID: tenantID,
	}
}

// ImportProcessingError 导入处理错误
type ImportProcessingError struct {
	JobID   int64
	Phase   string
	Message string
	Cause   error
}

func (e ImportProcessingError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("import processing error in %s phase for job %d: %s (caused by: %v)",
			e.Phase, e.JobID, e.Message, e.Cause)
	}
	return fmt.Sprintf("import processing error in %s phase for job %d: %s",
		e.Phase, e.JobID, e.Message)
}

func (e ImportProcessingError) Unwrap() error {
	return e.Cause
}

// NewImportProcessingError 创建导入处理错误
func NewImportProcessingError(jobID int64, phase, message string, cause error) error {
	return ImportProcessingError{
		JobID:   jobID,
		Phase:   phase,
		Message: message,
		Cause:   cause,
	}
}

// ImportFileError 导入文件错误
type ImportFileError struct {
	FileURL string
	Message string
	Cause   error
}

func (e ImportFileError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("import file error for %s: %s (caused by: %v)",
			e.FileURL, e.Message, e.Cause)
	}
	return fmt.Sprintf("import file error for %s: %s", e.FileURL, e.Message)
}

func (e ImportFileError) Unwrap() error {
	return e.Cause
}

// NewImportFileError 创建导入文件错误
func NewImportFileError(fileURL, message string, cause error) error {
	return ImportFileError{
		FileURL: fileURL,
		Message: message,
		Cause:   cause,
	}
}

// ImportDataValidationError 导入数据验证错误
type ImportDataValidationError struct {
	RowNumber int64
	Field     string
	Value     interface{}
	Message   string
}

func (e ImportDataValidationError) Error() string {
	return fmt.Sprintf("import data validation error at row %d, field '%s' (value: %v): %s",
		e.RowNumber, e.Field, e.Value, e.Message)
}

// NewImportDataValidationError 创建导入数据验证错误
func NewImportDataValidationError(rowNumber int64, field string, value interface{}, message string) error {
	return ImportDataValidationError{
		RowNumber: rowNumber,
		Field:     field,
		Value:     value,
		Message:   message,
	}
}

// ImportBusinessError 导入业务错误
type ImportBusinessError struct {
	RowNumber int64
	Operation string
	Message   string
	Cause     error
}

func (e ImportBusinessError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("import business error at row %d during %s: %s (caused by: %v)",
			e.RowNumber, e.Operation, e.Message, e.Cause)
	}
	return fmt.Sprintf("import business error at row %d during %s: %s",
		e.RowNumber, e.Operation, e.Message)
}

func (e ImportBusinessError) Unwrap() error {
	return e.Cause
}

// NewImportBusinessError 创建导入业务错误
func NewImportBusinessError(rowNumber int64, operation, message string, cause error) error {
	return ImportBusinessError{
		RowNumber: rowNumber,
		Operation: operation,
		Message:   message,
		Cause:     cause,
	}
}
