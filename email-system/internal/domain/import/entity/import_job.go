package entity

import (
	"encoding/json"
	"fmt"
	"time"
)

// ImportJobStatus 导入任务状态
type ImportJobStatus string

const (
	ImportJobStatusQueued     ImportJobStatus = "queued"     // 排队中
	ImportJobStatusParsing    ImportJobStatus = "parsing"    // 解析中
	ImportJobStatusValidating ImportJobStatus = "validating" // 验证中
	ImportJobStatusProcessing ImportJobStatus = "processing" // 处理中
	ImportJobStatusCompleted  ImportJobStatus = "completed"  // 已完成
	ImportJobStatusFailed     ImportJobStatus = "failed"     // 失败
	ImportJobStatusCancelled  ImportJobStatus = "cancelled"  // 已取消
)

// IsValid 检查状态是否有效
func (s ImportJobStatus) IsValid() bool {
	switch s {
	case ImportJobStatusQueued, ImportJobStatusParsing, ImportJobStatusValidating,
		ImportJobStatusProcessing, ImportJobStatusCompleted, ImportJobStatusFailed, ImportJobStatusCancelled:
		return true
	default:
		return false
	}
}

// ImportJobPhase 导入任务阶段
type ImportJobPhase string

const (
	ImportJobPhaseParsing    ImportJobPhase = "parsing"    // 解析阶段
	ImportJobPhaseValidating ImportJobPhase = "validating" // 验证阶段
	ImportJobPhaseProcessing ImportJobPhase = "processing" // 处理阶段
	ImportJobPhaseStoring    ImportJobPhase = "storing"    // 存储阶段
)

// BusinessScenario 业务场景
type BusinessScenario string

const (
	BusinessScenarioContacts     BusinessScenario = "contacts"       // 联系人导入
	BusinessScenarioOneTimeEmail BusinessScenario = "one_time_email" // 一次性邮件发送
	BusinessScenarioTagMembers   BusinessScenario = "tag_members"    // 标签成员导入
	BusinessScenarioCustom       BusinessScenario = "custom"         // 自定义场景
)

// IsValid 检查业务场景是否有效
func (s BusinessScenario) IsValid() bool {
	switch s {
	case BusinessScenarioContacts, BusinessScenarioOneTimeEmail, BusinessScenarioTagMembers, BusinessScenarioCustom:
		return true
	default:
		return false
	}
}

// FileType 文件类型
type FileType string

const (
	FileTypeCSV   FileType = "csv"
	FileTypeExcel FileType = "excel"
	FileTypeJSON  FileType = "json"
)

// IsValid 检查文件类型是否有效
func (f FileType) IsValid() bool {
	switch f {
	case FileTypeCSV, FileTypeExcel, FileTypeJSON:
		return true
	default:
		return false
	}
}

// ImportJob 导入任务实体
type ImportJob struct {
	ID               int64            `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID         int64            `json:"tenant_id" gorm:"not null;index:idx_import_job_tenant"`
	BusinessScenario BusinessScenario `json:"business_scenario" gorm:"not null;default:'contacts';size:32"`
	ScenarioConfig   *string          `json:"scenario_config" gorm:"type:json"`
	TargetEntityType *string          `json:"target_entity_type" gorm:"size:32"`
	TargetEntityID   *int64           `json:"target_entity_id"`
	BusinessMetadata *string          `json:"business_metadata" gorm:"type:json"`

	// 文件信息
	FileName string   `json:"file_name" gorm:"not null;size:255"`
	FileURL  string   `json:"file_url" gorm:"not null;size:512"`
	FileType FileType `json:"file_type" gorm:"not null;size:16"`
	FileSize int64    `json:"file_size" gorm:"not null;default:0"`

	// 映射配置
	MappingConfig   string  `json:"mapping_config" gorm:"not null;type:json"`
	ValidationRules *string `json:"validation_rules" gorm:"type:json"`

	// 处理状态
	Status       ImportJobStatus `json:"status" gorm:"not null;default:'queued';size:32"`
	CurrentPhase *ImportJobPhase `json:"current_phase" gorm:"size:32"`
	Progress     int             `json:"progress" gorm:"not null;default:0"`

	// 统计信息
	TotalRows      int64 `json:"total_rows" gorm:"not null;default:0"`
	ValidRows      int64 `json:"valid_rows" gorm:"not null;default:0"`
	InvalidRows    int64 `json:"invalid_rows" gorm:"not null;default:0"`
	DuplicateRows  int64 `json:"duplicate_rows" gorm:"not null;default:0"`
	ProcessedCount int64 `json:"processed_count" gorm:"not null;default:0"`
	SuccessCount   int64 `json:"success_count" gorm:"not null;default:0"`
	FailedCount    int64 `json:"failed_count" gorm:"not null;default:0"`

	// 时间信息
	EstimatedDuration   *int       `json:"estimated_duration"` // 秒
	EstimatedCompletion *time.Time `json:"estimated_completion"`
	StartedAt           *time.Time `json:"started_at"`
	FinishedAt          *time.Time `json:"finished_at"`

	// 错误信息
	ErrorMessage *string `json:"error_message" gorm:"type:text"`
	ErrorSummary *string `json:"error_summary" gorm:"type:json"`

	CreatedBy int64     `json:"created_by" gorm:"not null"`
	CreatedAt time.Time `json:"created_at" gorm:"not null;autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"not null;autoUpdateTime"`
}

// TableName 指定表名
func (ImportJob) TableName() string {
	return "import_jobs"
}

// GetScenarioConfig 获取场景配置
func (ij *ImportJob) GetScenarioConfig() (map[string]interface{}, error) {
	if ij.ScenarioConfig == nil || *ij.ScenarioConfig == "" {
		return make(map[string]interface{}), nil
	}

	var config map[string]interface{}
	if err := json.Unmarshal([]byte(*ij.ScenarioConfig), &config); err != nil {
		return nil, fmt.Errorf("unmarshal scenario config: %w", err)
	}

	return config, nil
}

// SetScenarioConfig 设置场景配置
func (ij *ImportJob) SetScenarioConfig(config map[string]interface{}) error {
	if config == nil {
		ij.ScenarioConfig = nil
		return nil
	}

	data, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("marshal scenario config: %w", err)
	}

	configStr := string(data)
	ij.ScenarioConfig = &configStr
	return nil
}

// GetBusinessMetadata 获取业务元数据
func (ij *ImportJob) GetBusinessMetadata() (map[string]interface{}, error) {
	if ij.BusinessMetadata == nil || *ij.BusinessMetadata == "" {
		return make(map[string]interface{}), nil
	}

	var metadata map[string]interface{}
	if err := json.Unmarshal([]byte(*ij.BusinessMetadata), &metadata); err != nil {
		return nil, fmt.Errorf("unmarshal business metadata: %w", err)
	}

	return metadata, nil
}

// SetBusinessMetadata 设置业务元数据
func (ij *ImportJob) SetBusinessMetadata(metadata map[string]interface{}) error {
	if metadata == nil {
		ij.BusinessMetadata = nil
		return nil
	}

	data, err := json.Marshal(metadata)
	if err != nil {
		return fmt.Errorf("marshal business metadata: %w", err)
	}

	metadataStr := string(data)
	ij.BusinessMetadata = &metadataStr
	return nil
}

// GetMappingConfig 获取映射配置
func (ij *ImportJob) GetMappingConfig() (map[string]interface{}, error) {
	var config map[string]interface{}
	if err := json.Unmarshal([]byte(ij.MappingConfig), &config); err != nil {
		return nil, fmt.Errorf("unmarshal mapping config: %w", err)
	}

	return config, nil
}

// SetMappingConfig 设置映射配置
func (ij *ImportJob) SetMappingConfig(config map[string]interface{}) error {
	data, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("marshal mapping config: %w", err)
	}

	ij.MappingConfig = string(data)
	return nil
}

// GetValidationRules 获取验证规则
func (ij *ImportJob) GetValidationRules() (map[string]interface{}, error) {
	if ij.ValidationRules == nil || *ij.ValidationRules == "" {
		return make(map[string]interface{}), nil
	}

	var rules map[string]interface{}
	if err := json.Unmarshal([]byte(*ij.ValidationRules), &rules); err != nil {
		return nil, fmt.Errorf("unmarshal validation rules: %w", err)
	}

	return rules, nil
}

// SetValidationRules 设置验证规则
func (ij *ImportJob) SetValidationRules(rules map[string]interface{}) error {
	if rules == nil {
		ij.ValidationRules = nil
		return nil
	}

	data, err := json.Marshal(rules)
	if err != nil {
		return fmt.Errorf("marshal validation rules: %w", err)
	}

	rulesStr := string(data)
	ij.ValidationRules = &rulesStr
	return nil
}

// GetErrorSummary 获取错误汇总
func (ij *ImportJob) GetErrorSummary() (map[string]interface{}, error) {
	if ij.ErrorSummary == nil || *ij.ErrorSummary == "" {
		return make(map[string]interface{}), nil
	}

	var summary map[string]interface{}
	if err := json.Unmarshal([]byte(*ij.ErrorSummary), &summary); err != nil {
		return nil, fmt.Errorf("unmarshal error summary: %w", err)
	}

	return summary, nil
}

// SetErrorSummary 设置错误汇总
func (ij *ImportJob) SetErrorSummary(summary map[string]interface{}) error {
	if summary == nil {
		ij.ErrorSummary = nil
		return nil
	}

	data, err := json.Marshal(summary)
	if err != nil {
		return fmt.Errorf("marshal error summary: %w", err)
	}

	summaryStr := string(data)
	ij.ErrorSummary = &summaryStr
	return nil
}

// IsQueued 是否为排队状态
func (ij *ImportJob) IsQueued() bool {
	return ij.Status == ImportJobStatusQueued
}

// IsRunning 是否在运行中
func (ij *ImportJob) IsRunning() bool {
	return ij.Status == ImportJobStatusParsing ||
		ij.Status == ImportJobStatusValidating ||
		ij.Status == ImportJobStatusProcessing
}

// IsCompleted 是否已完成
func (ij *ImportJob) IsCompleted() bool {
	return ij.Status == ImportJobStatusCompleted
}

// IsFailed 是否失败
func (ij *ImportJob) IsFailed() bool {
	return ij.Status == ImportJobStatusFailed
}

// IsCancelled 是否已取消
func (ij *ImportJob) IsCancelled() bool {
	return ij.Status == ImportJobStatusCancelled
}

// Start 开始任务
func (ij *ImportJob) Start() {
	now := time.Now()
	ij.Status = ImportJobStatusParsing
	phase := ImportJobPhaseParsing
	ij.CurrentPhase = &phase
	ij.StartedAt = &now
	ij.UpdatedAt = now
}

// SetPhase 设置当前阶段
func (ij *ImportJob) SetPhase(phase ImportJobPhase) {
	ij.CurrentPhase = &phase
	ij.UpdatedAt = time.Now()

	// 根据阶段设置状态
	switch phase {
	case ImportJobPhaseParsing:
		ij.Status = ImportJobStatusParsing
	case ImportJobPhaseValidating:
		ij.Status = ImportJobStatusValidating
	case ImportJobPhaseProcessing:
		ij.Status = ImportJobStatusProcessing
	case ImportJobPhaseStoring:
		ij.Status = ImportJobStatusProcessing
	}
}

// UpdateProgress 更新进度
func (ij *ImportJob) UpdateProgress(progress int) {
	if progress < 0 {
		progress = 0
	}
	if progress > 100 {
		progress = 100
	}
	ij.Progress = progress
	ij.UpdatedAt = time.Now()
}

// UpdateStatistics 更新统计信息
func (ij *ImportJob) UpdateStatistics(totalRows, validRows, invalidRows, duplicateRows int64) {
	ij.TotalRows = totalRows
	ij.ValidRows = validRows
	ij.InvalidRows = invalidRows
	ij.DuplicateRows = duplicateRows
	ij.UpdatedAt = time.Now()
}

// UpdateProcessCount 更新处理计数
func (ij *ImportJob) UpdateProcessCount(processedCount, successCount, failedCount int64) {
	ij.ProcessedCount = processedCount
	ij.SuccessCount = successCount
	ij.FailedCount = failedCount
	ij.UpdatedAt = time.Now()

	// 计算进度
	if ij.ValidRows > 0 {
		progress := int((processedCount * 100) / ij.ValidRows)
		ij.UpdateProgress(progress)
	}
}

// Complete 完成任务
func (ij *ImportJob) Complete() {
	now := time.Now()
	ij.Status = ImportJobStatusCompleted
	ij.FinishedAt = &now
	ij.Progress = 100
	ij.UpdatedAt = now
}

// Fail 标记任务失败
func (ij *ImportJob) Fail(errorMessage string) {
	now := time.Now()
	ij.Status = ImportJobStatusFailed
	ij.FinishedAt = &now
	ij.ErrorMessage = &errorMessage
	ij.UpdatedAt = now
}

// Cancel 取消任务
func (ij *ImportJob) Cancel() {
	now := time.Now()
	ij.Status = ImportJobStatusCancelled
	ij.FinishedAt = &now
	ij.UpdatedAt = now
}

// EstimateDuration 预估处理时长
func (ij *ImportJob) EstimateDuration(rowsPerSecond int) {
	if ij.TotalRows > 0 && rowsPerSecond > 0 {
		duration := int(ij.TotalRows) / rowsPerSecond
		ij.EstimatedDuration = &duration

		if ij.StartedAt != nil {
			completion := ij.StartedAt.Add(time.Duration(duration) * time.Second)
			ij.EstimatedCompletion = &completion
		}
	}
}

// Validate 验证导入任务数据
func (ij *ImportJob) Validate() error {
	if ij.TenantID <= 0 {
		return NewImportJobValidationError("tenant_id", "租户ID不能为空")
	}

	if !ij.BusinessScenario.IsValid() {
		return NewImportJobValidationError("business_scenario", "无效的业务场景")
	}

	if ij.FileName == "" {
		return NewImportJobValidationError("file_name", "文件名不能为空")
	}

	if ij.FileURL == "" {
		return NewImportJobValidationError("file_url", "文件URL不能为空")
	}

	if !ij.FileType.IsValid() {
		return NewImportJobValidationError("file_type", "无效的文件类型")
	}

	if ij.MappingConfig == "" {
		return NewImportJobValidationError("mapping_config", "映射配置不能为空")
	}

	if !ij.Status.IsValid() {
		return NewImportJobValidationError("status", "无效的任务状态")
	}

	if ij.Progress < 0 || ij.Progress > 100 {
		return NewImportJobValidationError("progress", "进度必须在0-100之间")
	}

	if ij.CreatedBy <= 0 {
		return NewImportJobValidationError("created_by", "创建人不能为空")
	}

	return nil
}

// Clone 克隆导入任务实体
func (ij *ImportJob) Clone() *ImportJob {
	clone := *ij

	if ij.ScenarioConfig != nil {
		config := *ij.ScenarioConfig
		clone.ScenarioConfig = &config
	}

	if ij.TargetEntityType != nil {
		entityType := *ij.TargetEntityType
		clone.TargetEntityType = &entityType
	}

	if ij.TargetEntityID != nil {
		entityID := *ij.TargetEntityID
		clone.TargetEntityID = &entityID
	}

	if ij.BusinessMetadata != nil {
		metadata := *ij.BusinessMetadata
		clone.BusinessMetadata = &metadata
	}

	if ij.ValidationRules != nil {
		rules := *ij.ValidationRules
		clone.ValidationRules = &rules
	}

	if ij.CurrentPhase != nil {
		phase := *ij.CurrentPhase
		clone.CurrentPhase = &phase
	}

	if ij.EstimatedDuration != nil {
		duration := *ij.EstimatedDuration
		clone.EstimatedDuration = &duration
	}

	if ij.EstimatedCompletion != nil {
		completion := *ij.EstimatedCompletion
		clone.EstimatedCompletion = &completion
	}

	if ij.StartedAt != nil {
		startedAt := *ij.StartedAt
		clone.StartedAt = &startedAt
	}

	if ij.FinishedAt != nil {
		finishedAt := *ij.FinishedAt
		clone.FinishedAt = &finishedAt
	}

	if ij.ErrorMessage != nil {
		errorMessage := *ij.ErrorMessage
		clone.ErrorMessage = &errorMessage
	}

	if ij.ErrorSummary != nil {
		errorSummary := *ij.ErrorSummary
		clone.ErrorSummary = &errorSummary
	}

	return &clone
}
