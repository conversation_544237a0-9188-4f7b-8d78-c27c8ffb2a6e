package entity

import (
	"encoding/json"
	"fmt"
	"time"
)

// ImportErrorType 导入错误类型
type ImportErrorType string

const (
	ImportErrorTypeValidation ImportErrorType = "validation" // 验证错误
	ImportErrorTypeBusiness   ImportErrorType = "business"   // 业务错误
	ImportErrorTypeSystem     ImportErrorType = "system"     // 系统错误
)

// IsValid 检查错误类型是否有效
func (t ImportErrorType) IsValid() bool {
	switch t {
	case ImportErrorTypeValidation, ImportErrorTypeBusiness, ImportErrorTypeSystem:
		return true
	default:
		return false
	}
}

// ImportError 导入错误实体
type ImportError struct {
	ID           int64           `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID     int64           `json:"tenant_id" gorm:"not null;index:idx_import_error_tenant"`
	JobID        int64           `json:"job_id" gorm:"not null;index:idx_import_error_job"`
	BatchNo      int             `json:"batch_no" gorm:"not null"`
	RowNumber    int64           `json:"row_number" gorm:"not null"`
	RawData      string          `json:"raw_data" gorm:"not null;type:json"`
	ErrorType    ImportErrorType `json:"error_type" gorm:"not null;size:32"`
	ErrorCode    *string         `json:"error_code" gorm:"size:64"`
	ErrorMessage string          `json:"error_message" gorm:"not null;size:1024"`
	RetryCount   int             `json:"retry_count" gorm:"not null;default:0"`
	CreatedAt    time.Time       `json:"created_at" gorm:"not null;autoCreateTime"`
	UpdatedAt    time.Time       `json:"updated_at" gorm:"not null;autoUpdateTime"`
}

// TableName 指定表名
func (ImportError) TableName() string {
	return "import_errors"
}

// GetRawData 获取原始数据
func (ie *ImportError) GetRawData() (map[string]interface{}, error) {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(ie.RawData), &data); err != nil {
		return nil, fmt.Errorf("unmarshal raw data: %w", err)
	}
	return data, nil
}

// SetRawData 设置原始数据
func (ie *ImportError) SetRawData(data map[string]interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("marshal raw data: %w", err)
	}
	ie.RawData = string(jsonData)
	return nil
}

// IncrementRetry 增加重试次数
func (ie *ImportError) IncrementRetry() {
	ie.RetryCount++
	ie.UpdatedAt = time.Now()
}

// IsValidationError 是否为验证错误
func (ie *ImportError) IsValidationError() bool {
	return ie.ErrorType == ImportErrorTypeValidation
}

// IsBusinessError 是否为业务错误
func (ie *ImportError) IsBusinessError() bool {
	return ie.ErrorType == ImportErrorTypeBusiness
}

// IsSystemError 是否为系统错误
func (ie *ImportError) IsSystemError() bool {
	return ie.ErrorType == ImportErrorTypeSystem
}

// CanRetry 是否可以重试
func (ie *ImportError) CanRetry(maxRetries int) bool {
	// 验证错误通常不需要重试
	if ie.IsValidationError() {
		return false
	}

	// 业务错误根据错误代码决定是否重试
	if ie.IsBusinessError() {
		// 某些业务错误可以重试，例如外部依赖不可用
		return ie.RetryCount < maxRetries
	}

	// 系统错误通常可以重试
	if ie.IsSystemError() {
		return ie.RetryCount < maxRetries
	}

	return false
}

// Validate 验证错误记录数据
func (ie *ImportError) Validate() error {
	if ie.TenantID <= 0 {
		return NewImportErrorValidationError("tenant_id", "租户ID不能为空")
	}

	if ie.JobID <= 0 {
		return NewImportErrorValidationError("job_id", "任务ID不能为空")
	}

	if ie.BatchNo < 0 {
		return NewImportErrorValidationError("batch_no", "批次号不能为负数")
	}

	if ie.RowNumber <= 0 {
		return NewImportErrorValidationError("row_number", "行号必须大于0")
	}

	if ie.RawData == "" {
		return NewImportErrorValidationError("raw_data", "原始数据不能为空")
	}

	if !ie.ErrorType.IsValid() {
		return NewImportErrorValidationError("error_type", "无效的错误类型")
	}

	if ie.ErrorMessage == "" {
		return NewImportErrorValidationError("error_message", "错误消息不能为空")
	}

	if len(ie.ErrorMessage) > 1024 {
		return NewImportErrorValidationError("error_message", "错误消息长度不能超过1024个字符")
	}

	if ie.RetryCount < 0 {
		return NewImportErrorValidationError("retry_count", "重试次数不能为负数")
	}

	// 验证原始数据JSON格式
	var rawData map[string]interface{}
	if err := json.Unmarshal([]byte(ie.RawData), &rawData); err != nil {
		return NewImportErrorValidationError("raw_data", "原始数据格式无效")
	}

	return nil
}

// Clone 克隆错误记录实体
func (ie *ImportError) Clone() *ImportError {
	clone := *ie

	if ie.ErrorCode != nil {
		errorCode := *ie.ErrorCode
		clone.ErrorCode = &errorCode
	}

	return &clone
}
