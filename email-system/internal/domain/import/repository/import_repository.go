package repository

import (
	"context"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/entity"
)

// ImportJobRepository 导入任务仓储接口
type ImportJobRepository interface {
	// Create 创建导入任务
	Create(ctx context.Context, job *entity.ImportJob) error

	// GetByID 根据ID获取导入任务
	GetByID(ctx context.Context, id, tenantID int64) (*entity.ImportJob, error)

	// Update 更新导入任务
	Update(ctx context.Context, job *entity.ImportJob) error

	// UpdateStatus 更新任务状态
	UpdateStatus(ctx context.Context, id, tenantID int64, status entity.ImportJobStatus) error

	// UpdateProgress 更新任务进度
	UpdateProgress(ctx context.Context, id, tenantID int64, progress int) error

	// UpdatePhase 更新任务阶段
	UpdatePhase(ctx context.Context, id, tenantID int64, phase entity.ImportJobPhase) error

	// UpdateStatistics 更新统计信息
	UpdateStatistics(ctx context.Context, id, tenantID int64, 
		totalRows, validRows, invalidRows, duplicateRows int64) error

	// UpdateProcessCount 更新处理计数
	UpdateProcessCount(ctx context.Context, id, tenantID int64, 
		processedCount, successCount, failedCount int64) error

	// MarkCompleted 标记任务完成
	MarkCompleted(ctx context.Context, id, tenantID int64) error

	// MarkFailed 标记任务失败
	MarkFailed(ctx context.Context, id, tenantID int64, errorMessage string) error

	// List 分页查询导入任务列表
	List(ctx context.Context, tenantID int64, status entity.ImportJobStatus, 
		limit, offset int) ([]*entity.ImportJob, int64, error)

	// ListByBusinessScenario 根据业务场景查询任务列表
	ListByBusinessScenario(ctx context.Context, tenantID int64, 
		scenario entity.BusinessScenario, limit, offset int) ([]*entity.ImportJob, int64, error)

	// Delete 删除导入任务
	Delete(ctx context.Context, id, tenantID int64) error

	// GetRunningJobs 获取运行中的任务列表
	GetRunningJobs(ctx context.Context, tenantID int64) ([]*entity.ImportJob, error)

	// GetQueuedJobs 获取排队中的任务列表
	GetQueuedJobs(ctx context.Context, tenantID int64, limit int) ([]*entity.ImportJob, error)
}

// ImportScenarioConfigRepository 导入场景配置仓储接口
type ImportScenarioConfigRepository interface {
	// Create 创建场景配置
	Create(ctx context.Context, config *entity.ImportScenarioConfig) error

	// GetByCode 根据场景代码获取配置
	GetByCode(ctx context.Context, code string, tenantID int64) (*entity.ImportScenarioConfig, error)

	// GetByID 根据ID获取配置
	GetByID(ctx context.Context, id, tenantID int64) (*entity.ImportScenarioConfig, error)

	// Update 更新场景配置
	Update(ctx context.Context, config *entity.ImportScenarioConfig) error

	// Delete 删除场景配置
	Delete(ctx context.Context, id, tenantID int64) error

	// List 分页查询场景配置列表
	List(ctx context.Context, tenantID int64, status entity.ScenarioStatus, 
		limit, offset int) ([]*entity.ImportScenarioConfig, int64, error)

	// ListActive 获取激活的场景配置列表
	ListActive(ctx context.Context, tenantID int64) ([]*entity.ImportScenarioConfig, error)

	// Activate 激活场景配置
	Activate(ctx context.Context, id, tenantID int64) error

	// Deactivate 停用场景配置
	Deactivate(ctx context.Context, id, tenantID int64) error
}

// ImportErrorRepository 导入错误仓储接口
type ImportErrorRepository interface {
	// Create 创建错误记录
	Create(ctx context.Context, importError *entity.ImportError) error

	// BatchCreate 批量创建错误记录
	BatchCreate(ctx context.Context, errors []*entity.ImportError) error

	// GetByJobID 根据任务ID获取错误列表
	GetByJobID(ctx context.Context, jobID, tenantID int64, 
		limit, offset int) ([]*entity.ImportError, int64, error)

	// GetByJobAndBatch 根据任务ID和批次号获取错误列表
	GetByJobAndBatch(ctx context.Context, jobID, tenantID int64, batchNo int, 
		limit, offset int) ([]*entity.ImportError, int64, error)

	// GetErrorSummary 获取错误汇总统计
	GetErrorSummary(ctx context.Context, jobID, tenantID int64) (map[string]int64, error)

	// GetRetryableErrors 获取可重试的错误列表
	GetRetryableErrors(ctx context.Context, jobID, tenantID int64, 
		maxRetries int) ([]*entity.ImportError, error)

	// UpdateRetryCount 更新重试次数
	UpdateRetryCount(ctx context.Context, id, tenantID int64) error

	// Delete 删除错误记录
	Delete(ctx context.Context, id, tenantID int64) error

	// DeleteByJobID 删除指定任务的所有错误记录
	DeleteByJobID(ctx context.Context, jobID, tenantID int64) error
}

// ImportBatchRepository 导入批次仓储接口
type ImportBatchRepository interface {
	// Create 创建批次
	Create(ctx context.Context, batch *entity.ImportBatch) error

	// BatchCreate 批量创建批次
	BatchCreate(ctx context.Context, batches []*entity.ImportBatch) error

	// GetByID 根据ID获取批次
	GetByID(ctx context.Context, id, tenantID int64) (*entity.ImportBatch, error)

	// GetByJobAndBatch 根据任务ID和批次号获取批次
	GetByJobAndBatch(ctx context.Context, jobID, tenantID int64, batchNo int) (*entity.ImportBatch, error)

	// Update 更新批次
	Update(ctx context.Context, batch *entity.ImportBatch) error

	// UpdateStatus 更新批次状态
	UpdateStatus(ctx context.Context, id, tenantID int64, status entity.ImportBatchStatus) error

	// UpdateProgress 更新批次进度
	UpdateProgress(ctx context.Context, id, tenantID int64, 
		processedCount, successCount, failedCount int) error

	// MarkCompleted 标记批次完成
	MarkCompleted(ctx context.Context, id, tenantID int64) error

	// MarkFailed 标记批次失败
	MarkFailed(ctx context.Context, id, tenantID int64, errorMessage string) error

	// ListByJobID 根据任务ID获取批次列表
	ListByJobID(ctx context.Context, jobID, tenantID int64) ([]*entity.ImportBatch, error)

	// GetPendingBatches 获取待处理的批次列表
	GetPendingBatches(ctx context.Context, jobID, tenantID int64) ([]*entity.ImportBatch, error)

	// GetBatchStatistics 获取批次统计信息
	GetBatchStatistics(ctx context.Context, jobID, tenantID int64) (map[string]int64, error)

	// Delete 删除批次
	Delete(ctx context.Context, id, tenantID int64) error

	// DeleteByJobID 删除指定任务的所有批次
	DeleteByJobID(ctx context.Context, jobID, tenantID int64) error
}