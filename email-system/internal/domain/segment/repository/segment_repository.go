package repository

import (
	"context"

	"gitee.com/heiyee/platforms/email-system/internal/domain/segment/entity"
)

// SegmentRepository 人群圈选仓储接口
type SegmentRepository interface {
	// Create 创建人群圈选
	Create(ctx context.Context, segment *entity.Segment) error

	// Update 更新人群圈选
	Update(ctx context.Context, segment *entity.Segment) error

	// Delete 删除人群圈选
	Delete(ctx context.Context, tenantID, segmentID int64) error

	// FindByID 根据ID查找人群圈选
	FindByID(ctx context.Context, tenantID, segmentID int64) (*entity.Segment, error)

	// FindByTagID 根据标签ID查找人群圈选
	FindByTagID(ctx context.Context, tenantID, tagID int64) (*entity.Segment, error)

	// FindByIDs 根据ID列表批量查找人群圈选
	FindByIDs(ctx context.Context, tenantID int64, segmentIDs []int64) ([]*entity.Segment, error)

	// List 获取人群圈选列表
	List(ctx context.Context, params *ListParams) (*ListResult, error)

	// Count 统计人群圈选数量
	Count(ctx context.Context, tenantID int64, filters *ListFilters) (int64, error)

	// GetActiveSegments 获取活跃的人群圈选
	GetActiveSegments(ctx context.Context, tenantID int64) ([]*entity.Segment, error)

	// GetSegmentsByType 根据类型获取人群圈选
	GetSegmentsByType(ctx context.Context, tenantID int64, segmentType entity.SegmentType) ([]*entity.Segment, error)

	// CreateSnapshot 创建人群圈选快照
	CreateSnapshot(ctx context.Context, snapshot *entity.SegmentSnapshot) error

	// DeleteOldSnapshots 删除旧快照，保留最新的指定数量
	DeleteOldSnapshots(ctx context.Context, tenantID, segmentID int64, keepCount int) error

	// GetLatestSnapshot 获取最新的人群圈选快照
	GetLatestSnapshot(ctx context.Context, tenantID, segmentID int64) (*entity.SegmentSnapshot, error)
}

// SegmentJobRepository 人群圈选任务仓储接口
type SegmentJobRepository interface {
	// Create 创建人群圈选任务
	Create(ctx context.Context, job *entity.SegmentJob) error

	// Update 更新人群圈选任务
	Update(ctx context.Context, job *entity.SegmentJob) error

	// FindByID 根据ID查找人群圈选任务
	FindByID(ctx context.Context, tenantID, jobID int64) (*entity.SegmentJob, error)

	// FindBySegmentID 根据人群圈选ID查找任务
	FindBySegmentID(ctx context.Context, tenantID, segmentID int64, jobType entity.SegmentJobType) (*entity.SegmentJob, error)

	// List 获取人群圈选任务列表
	List(ctx context.Context, params *JobListParams) (*JobListResult, error)

	// GetRunningJobs 获取运行中的任务
	GetRunningJobs(ctx context.Context, tenantID int64) ([]*entity.SegmentJob, error)

	// GetPendingJobs 获取待处理的任务
	GetPendingJobs(ctx context.Context, tenantID int64, limit int) ([]*entity.SegmentJob, error)

	// UpdateJobStatus 更新任务状态
	UpdateJobStatus(ctx context.Context, tenantID, jobID int64, status entity.SegmentJobStatus, progress int, errorMsg *string) error

	// SetJobResult 设置任务结果
	SetJobResult(ctx context.Context, tenantID, jobID int64, result map[string]interface{}) error
}

// ListParams 人群圈选列表查询参数
type ListParams struct {
	TenantID int64        `json:"tenant_id"`
	Filters  *ListFilters `json:"filters"`
	Sort     *SortOptions `json:"sort"`
	Page     int          `json:"page"`
	Size     int          `json:"size"`
	Offset   int          `json:"offset"`
	Limit    int          `json:"limit"`
}

// ListFilters 人群圈选列表过滤条件
type ListFilters struct {
	Name   string               `json:"name"`
	TagID  int64                `json:"tag_id"`
	Type   entity.SegmentType   `json:"type"`
	Status entity.SegmentStatus `json:"status"`
}

// SortOptions 排序选项
type SortOptions struct {
	Field string `json:"field"`
	Order string `json:"order"` // asc, desc
}

// ListResult 人群圈选列表结果
type ListResult struct {
	Segments   []*entity.Segment `json:"segments"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	Size       int               `json:"size"`
	TotalPages int               `json:"total_pages"`
}

// JobListParams 人群圈选任务列表查询参数
type JobListParams struct {
	TenantID  int64                   `json:"tenant_id"`
	SegmentID int64                   `json:"segment_id"`
	JobType   entity.SegmentJobType   `json:"job_type"`
	Status    entity.SegmentJobStatus `json:"status"`
	Page      int                     `json:"page"`
	Size      int                     `json:"size"`
	Offset    int                     `json:"offset"`
	Limit     int                     `json:"limit"`
}

// JobListResult 人群圈选任务列表结果
type JobListResult struct {
	Jobs       []*entity.SegmentJob `json:"jobs"`
	Total      int64                `json:"total"`
	Page       int                  `json:"page"`
	Size       int                  `json:"size"`
	TotalPages int                  `json:"total_pages"`
}

// NewListParams 创建人群圈选列表查询参数
func NewListParams(tenantID int64) *ListParams {
	return &ListParams{
		TenantID: tenantID,
		Filters:  &ListFilters{},
		Sort: &SortOptions{
			Field: "created_at",
			Order: "desc",
		},
		Page: 1,
		Size: 20,
	}
}

// WithFilters 设置过滤条件
func (p *ListParams) WithFilters(filters *ListFilters) *ListParams {
	p.Filters = filters
	return p
}

// WithSort 设置排序
func (p *ListParams) WithSort(field, order string) *ListParams {
	p.Sort = &SortOptions{
		Field: field,
		Order: order,
	}
	return p
}

// WithPagination 设置分页
func (p *ListParams) WithPagination(page, size int) *ListParams {
	p.Page = page
	p.Size = size
	return p
}

// WithOffset 设置偏移量分页
func (p *ListParams) WithOffset(offset, limit int) *ListParams {
	p.Offset = offset
	p.Limit = limit
	return p
}

// GetOffset 获取偏移量
func (p *ListParams) GetOffset() int {
	if p.Offset > 0 {
		return p.Offset
	}
	if p.Page > 0 && p.Size > 0 {
		return (p.Page - 1) * p.Size
	}
	return 0
}

// GetLimit 获取限制数量
func (p *ListParams) GetLimit() int {
	if p.Limit > 0 {
		return p.Limit
	}
	if p.Size > 0 {
		return p.Size
	}
	return 20
}

// NewJobListParams 创建人群圈选任务列表查询参数
func NewJobListParams(tenantID int64) *JobListParams {
	return &JobListParams{
		TenantID: tenantID,
		Page:     1,
		Size:     20,
	}
}

// GetOffset 获取偏移量
func (p *JobListParams) GetOffset() int {
	if p.Offset > 0 {
		return p.Offset
	}
	if p.Page > 0 && p.Size > 0 {
		return (p.Page - 1) * p.Size
	}
	return 0
}

// GetLimit 获取限制数量
func (p *JobListParams) GetLimit() int {
	if p.Limit > 0 {
		return p.Limit
	}
	if p.Size > 0 {
		return p.Size
	}
	return 20
}
