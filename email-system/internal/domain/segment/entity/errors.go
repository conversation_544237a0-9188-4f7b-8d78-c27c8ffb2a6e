package entity

import "fmt"

// SegmentError 人群圈选相关错误
type SegmentError struct {
	Code    string
	Message string
	Field   string
	Details string
}

func (e *SegmentError) Error() string {
	if e.Field != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Code, e.Field, e.Message)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// 错误码定义
const (
	// 人群圈选验证错误 (120200-120219)
	CodeSegmentValidationError = "120200"
	CodeSegmentNameInvalid     = "120201"
	CodeSegmentNameExists      = "120202"
	CodeSegmentNotFound        = "120203"
	CodeSegmentTypeInvalid     = "120204"
	CodeSegmentStatusInvalid   = "120205"
	CodeSegmentRuleInvalid     = "120206"
	CodeSegmentPolicyInvalid   = "120207"
	CodeSegmentTagInvalid      = "120208"

	// 人群圈选操作错误 (120220-120239)
	CodeSegmentCreateFailed  = "120220"
	CodeSegmentUpdateFailed  = "120221"
	CodeSegmentDeleteFailed  = "120222"
	CodeSegmentQueryFailed   = "120223"
	CodeSegmentRebuildFailed = "120224"
	CodeSegmentPreviewFailed = "120225"
	CodeSegmentExportFailed  = "120226"

	// 人群圈选规则错误 (120240-120259)
	CodeSegmentRuleParseError     = "120240"
	CodeSegmentRuleExecuteError   = "120241"
	CodeSegmentRuleValidateError  = "120242"
	CodeSegmentRuleConditionError = "120243"
	CodeSegmentRuleOperatorError  = "120244"
	CodeSegmentRuleFieldError     = "120245"
	CodeSegmentRuleValueError     = "120246"

	// 人群圈选任务错误 (120260-120279)
	CodeSegmentJobNotFound     = "120260"
	CodeSegmentJobCreateFailed = "120261"
	CodeSegmentJobUpdateFailed = "120262"
	CodeSegmentJobCancelFailed = "120263"
	CodeSegmentJobStatusError  = "120264"
	CodeSegmentJobTimeoutError = "120265"
	CodeSegmentJobResultError  = "120266"

	// 人群圈选快照错误 (120280-120299)
	CodeSegmentSnapshotNotFound     = "120280"
	CodeSegmentSnapshotCreateFailed = "120281"
	CodeSegmentSnapshotDeleteFailed = "120282"
	CodeSegmentSnapshotExpired      = "120283"
	CodeSegmentSnapshotInvalid      = "120284"
)

// NewSegmentValidationError 创建人群圈选验证错误
func NewSegmentValidationError(field, message string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentValidationError,
		Message: message,
		Field:   field,
	}
}

// NewSegmentNameInvalidError 创建人群圈选名称无效错误
func NewSegmentNameInvalidError(name string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentNameInvalid,
		Message: "人群圈选名称格式不正确",
		Field:   "name",
		Details: name,
	}
}

// NewSegmentNameExistsError 创建人群圈选名称已存在错误
func NewSegmentNameExistsError(name string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentNameExists,
		Message: "人群圈选名称已存在",
		Field:   "name",
		Details: name,
	}
}

// NewSegmentNotFoundError 创建人群圈选不存在错误
func NewSegmentNotFoundError(identifier string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentNotFound,
		Message: "人群圈选不存在",
		Details: identifier,
	}
}

// NewSegmentTypeInvalidError 创建人群圈选类型无效错误
func NewSegmentTypeInvalidError(segmentType string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentTypeInvalid,
		Message: "无效的人群圈选类型",
		Field:   "type",
		Details: segmentType,
	}
}

// NewSegmentStatusInvalidError 创建人群圈选状态无效错误
func NewSegmentStatusInvalidError(status string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentStatusInvalid,
		Message: "无效的人群圈选状态",
		Field:   "status",
		Details: status,
	}
}

// NewSegmentRuleInvalidError 创建人群圈选规则无效错误
func NewSegmentRuleInvalidError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentRuleInvalid,
		Message: "人群圈选规则格式无效",
		Field:   "rule_json",
		Details: reason,
	}
}

// NewSegmentPolicyInvalidError 创建刷新策略无效错误
func NewSegmentPolicyInvalidError(policy string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentPolicyInvalid,
		Message: "无效的刷新策略",
		Field:   "refresh_policy",
		Details: policy,
	}
}

// NewSegmentTagInvalidError 创建标签无效错误
func NewSegmentTagInvalidError(tagID int64) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentTagInvalid,
		Message: "无效的标签ID",
		Field:   "tag_id",
		Details: fmt.Sprintf("tag_id: %d", tagID),
	}
}

// NewSegmentCreateFailedError 创建人群圈选创建失败错误
func NewSegmentCreateFailedError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentCreateFailed,
		Message: "创建人群圈选失败",
		Details: reason,
	}
}

// NewSegmentUpdateFailedError 创建人群圈选更新失败错误
func NewSegmentUpdateFailedError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentUpdateFailed,
		Message: "更新人群圈选失败",
		Details: reason,
	}
}

// NewSegmentDeleteFailedError 创建人群圈选删除失败错误
func NewSegmentDeleteFailedError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentDeleteFailed,
		Message: "删除人群圈选失败",
		Details: reason,
	}
}

// NewSegmentQueryFailedError 创建人群圈选查询失败错误
func NewSegmentQueryFailedError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentQueryFailed,
		Message: "查询人群圈选失败",
		Details: reason,
	}
}

// NewSegmentRebuildFailedError 创建人群圈选重建失败错误
func NewSegmentRebuildFailedError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentRebuildFailed,
		Message: "重建人群圈选失败",
		Details: reason,
	}
}

// NewSegmentPreviewFailedError 创建人群圈选预览失败错误
func NewSegmentPreviewFailedError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentPreviewFailed,
		Message: "预览人群圈选失败",
		Details: reason,
	}
}

// NewSegmentExportFailedError 创建人群圈选导出失败错误
func NewSegmentExportFailedError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentExportFailed,
		Message: "导出人群圈选失败",
		Details: reason,
	}
}

// NewSegmentRuleParseError 创建人群圈选规则解析错误
func NewSegmentRuleParseError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentRuleParseError,
		Message: "人群圈选规则解析失败",
		Details: reason,
	}
}

// NewSegmentRuleExecuteError 创建人群圈选规则执行错误
func NewSegmentRuleExecuteError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentRuleExecuteError,
		Message: "人群圈选规则执行失败",
		Details: reason,
	}
}

// NewSegmentRuleValidateError 创建人群圈选规则验证错误
func NewSegmentRuleValidateError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentRuleValidateError,
		Message: "人群圈选规则验证失败",
		Details: reason,
	}
}

// NewSegmentRuleConditionError 创建人群圈选规则条件错误
func NewSegmentRuleConditionError(condition, reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentRuleConditionError,
		Message: fmt.Sprintf("人群圈选规则条件错误: %s", condition),
		Details: reason,
	}
}

// NewSegmentRuleOperatorError 创建人群圈选规则操作符错误
func NewSegmentRuleOperatorError(operator, reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentRuleOperatorError,
		Message: fmt.Sprintf("人群圈选规则操作符错误: %s", operator),
		Details: reason,
	}
}

// NewSegmentRuleFieldError 创建人群圈选规则字段错误
func NewSegmentRuleFieldError(field, reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentRuleFieldError,
		Message: fmt.Sprintf("人群圈选规则字段错误: %s", field),
		Details: reason,
	}
}

// NewSegmentRuleValueError 创建人群圈选规则值错误
func NewSegmentRuleValueError(value, reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentRuleValueError,
		Message: fmt.Sprintf("人群圈选规则值错误: %s", value),
		Details: reason,
	}
}

// NewSegmentJobNotFoundError 创建人群圈选任务不存在错误
func NewSegmentJobNotFoundError(jobID int64) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentJobNotFound,
		Message: "人群圈选任务不存在",
		Details: fmt.Sprintf("job_id: %d", jobID),
	}
}

// NewSegmentJobCreateFailedError 创建人群圈选任务创建失败错误
func NewSegmentJobCreateFailedError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentJobCreateFailed,
		Message: "创建人群圈选任务失败",
		Details: reason,
	}
}

// NewSegmentJobUpdateFailedError 创建人群圈选任务更新失败错误
func NewSegmentJobUpdateFailedError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentJobUpdateFailed,
		Message: "更新人群圈选任务失败",
		Details: reason,
	}
}

// NewSegmentJobCancelFailedError 创建人群圈选任务取消失败错误
func NewSegmentJobCancelFailedError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentJobCancelFailed,
		Message: "取消人群圈选任务失败",
		Details: reason,
	}
}

// NewSegmentJobStatusError 创建人群圈选任务状态错误
func NewSegmentJobStatusError(status, reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentJobStatusError,
		Message: fmt.Sprintf("人群圈选任务状态错误: %s", status),
		Details: reason,
	}
}

// NewSegmentJobTimeoutError 创建人群圈选任务超时错误
func NewSegmentJobTimeoutError(jobID int64) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentJobTimeoutError,
		Message: "人群圈选任务执行超时",
		Details: fmt.Sprintf("job_id: %d", jobID),
	}
}

// NewSegmentJobResultError 创建人群圈选任务结果错误
func NewSegmentJobResultError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentJobResultError,
		Message: "人群圈选任务结果错误",
		Details: reason,
	}
}

// NewSegmentSnapshotInvalidError 创建人群圈选快照无效错误
func NewSegmentSnapshotInvalidError(reason string) *SegmentError {
	return &SegmentError{
		Code:    CodeSegmentSnapshotInvalid,
		Message: "人群圈选快照无效",
		Details: reason,
	}
}
