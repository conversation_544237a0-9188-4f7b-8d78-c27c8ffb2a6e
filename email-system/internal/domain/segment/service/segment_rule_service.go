package service

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/contact/entity"
	contactRepository "gitee.com/heiyee/platforms/email-system/internal/domain/contact/repository"
	ruleService "gitee.com/heiyee/platforms/email-system/internal/domain/rule/service"
	segmentEntity "gitee.com/heiyee/platforms/email-system/internal/domain/segment/entity"
	segmentRepository "gitee.com/heiyee/platforms/email-system/internal/domain/segment/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// SegmentRuleService 人群圈选规则服务
type SegmentRuleService struct {
	logger         logiface.Logger
	segmentRepo    segmentRepository.SegmentRepository
	segmentJobRepo segmentRepository.SegmentJobRepository
	contactRepo    contactRepository.ContactRepository
	ruleEngine     *ruleService.RuleEngine
}

// NewSegmentRuleService 创建人群圈选规则服务
func NewSegmentRuleService(
	logger logiface.Logger,
	segmentRepo segmentRepository.SegmentRepository,
	segmentJobRepo segmentRepository.SegmentJobRepository,
	contactRepo contactRepository.ContactRepository,
	ruleEngine *ruleService.RuleEngine,
) *SegmentRuleService {
	return &SegmentRuleService{
		logger:         logger,
		segmentRepo:    segmentRepo,
		segmentJobRepo: segmentJobRepo,
		contactRepo:    contactRepo,
		ruleEngine:     ruleEngine,
	}
}

// ExecuteSegmentRule 执行人群圈选规则
func (s *SegmentRuleService) ExecuteSegmentRule(ctx context.Context, segment *segmentEntity.Segment) ([]int64, error) {
	s.logger.Info(ctx, "Executing segment rule",
		logiface.Int64("segment_id", segment.ID),
		logiface.String("segment_name", segment.Name))

	// 检查人群圈选类型
	if segment.Type != segmentEntity.SegmentTypeDynamic {
		return nil, segmentEntity.NewSegmentRuleExecuteError("只有动态人群圈选可以执行规则")
	}

	// 获取规则
	rule, err := segment.GetRule()
	if err != nil {
		s.logger.Error(ctx, "Failed to get segment rule",
			logiface.Error(err),
			logiface.Int64("segment_id", segment.ID))
	}

	if rule == nil {
		return nil, segmentEntity.NewSegmentRuleExecuteError("人群圈选没有有效的规则")
	}

	// 解析规则
	parsedRule, err := s.ruleEngine.ParseRule(rule)
	if err != nil {
		s.logger.Error(ctx, "Failed to parse segment rule",
			logiface.Error(err),
			logiface.Int64("segment_id", segment.ID))
	}

	// 获取所有活跃联系人
	contacts, err := s.contactRepo.FindActiveContacts(ctx, segment.TenantID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find active contacts",
			logiface.Error(err),
			logiface.Int64("tenant_id", segment.TenantID))
	}

	s.logger.Info(ctx, "Found active contacts for segment rule",
		logiface.Int("contact_count", len(contacts)))
	var matchedContactIDs []int64
	for _, contact := range contacts {
		// 构建联系人数据
		contactData := s.buildContactData(contact)

		// 评估规则
		matched, err := s.ruleEngine.EvaluateRule(ctx, parsedRule, contactData)
		if err != nil {
			s.logger.Error(ctx, "Failed to evaluate segment rule for contact",
				logiface.Error(err),
				logiface.Int64("contact_id", contact.ID))
		}

		if matched {
			matchedContactIDs = append(matchedContactIDs, contact.ID)
		}
	}

	s.logger.Info(ctx, "Segment rule execution completed",
		logiface.Int64("segment_id", segment.ID),
		logiface.Int("total_contacts", len(contacts)))

	return matchedContactIDs, nil
}

// PreviewSegmentRule 预览人群圈选规则
func (s *SegmentRuleService) PreviewSegmentRule(ctx context.Context, tenantID int64, rule map[string]interface{}, limit int) ([]int64, int64, error) {
	s.logger.Info(ctx, "Previewing segment rule",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("limit", limit))
	// 验证规则
	if err := s.ruleEngine.ValidateRule(rule); err != nil {
		return nil, 0, err
	}

	// 解析规则
	parsedRule, err := s.ruleEngine.ParseRule(rule)
	if err != nil {
		s.logger.Error(ctx, "Failed to parse rule for preview",
			logiface.Error(err))
		return nil, 0, segmentEntity.NewSegmentRuleParseError(fmt.Sprintf("parse rule: %v", err))
	}
	// 获取活跃联系人（限制数量以提高预览性能）
	previewLimit := limit
	if previewLimit <= 0 {
		previewLimit = 1000 // 默认预览1000个联系人
	}

	contacts, err := s.contactRepo.FindActiveContactsWithLimit(ctx, tenantID, previewLimit)
	if err != nil {
		s.logger.Error(ctx, "Failed to find active contacts for preview",
			logiface.Error(err))
		return nil, 0, segmentEntity.NewSegmentRuleExecuteError(fmt.Sprintf("find active contacts: %v", err))
	}
	// 评估规则
	var matchedContactIDs []int64
	totalEvaluated := int64(0)

	for _, contact := range contacts {
		totalEvaluated++

		// 构建联系人数据
		contactData := s.buildContactData(contact)

		// 评估规则
		matched, err := s.ruleEngine.EvaluateRule(ctx, parsedRule, contactData)
		if err != nil {
			s.logger.Error(ctx, "Failed to evaluate rule for contact in preview",
				logiface.Error(err),
				logiface.Int64("contact_id", contact.ID))
		}

		if matched {
			matchedContactIDs = append(matchedContactIDs, contact.ID)

			// 如果达到预览限制，停止评估
			if len(matchedContactIDs) >= limit && limit > 0 {
				break
			}
		}
	}

	// 估算总匹配数量
	var estimatedTotal int64
	if totalEvaluated > 0 {
		matchRate := float64(len(matchedContactIDs)) / float64(totalEvaluated)
		totalContacts, _ := s.contactRepo.CountActiveContacts(ctx, tenantID)
		estimatedTotal = int64(float64(totalContacts) * matchRate)
	}

	s.logger.Info(ctx, "Segment rule preview completed",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("total_evaluated", totalEvaluated),
		logiface.Int64("estimated_total", estimatedTotal))

	return matchedContactIDs, estimatedTotal, nil
}

// RebuildSegment 重建人群圈选
func (s *SegmentRuleService) RebuildSegment(ctx context.Context, segment *segmentEntity.Segment) error {
	s.logger.Info(ctx, "Rebuilding segment",
		logiface.Int64("segment_id", segment.ID),
		logiface.String("segment_name", segment.Name))
	// 检查人群圈选类型
	if segment.Type != segmentEntity.SegmentTypeDynamic {
		return segmentEntity.NewSegmentRebuildFailedError("只有动态人群圈选可以重建")
	}

	// 执行规则获取匹配的联系人
	matchedContactIDs, err := s.ExecuteSegmentRule(ctx, segment)
	if err != nil {
		return err
	}

	// 创建快照
	snapshot := &segmentEntity.SegmentSnapshot{
		TenantID:   segment.TenantID,
		SegmentID:  segment.ID,
		ContactIDs: matchedContactIDs,
		Size:       int64(len(matchedContactIDs)),
	}

	if err := s.createSegmentSnapshot(ctx, snapshot); err != nil {
		s.logger.Error(ctx, "Failed to create segment snapshot",
			logiface.Error(err),
			logiface.Int64("segment_id", segment.ID))
	}

	s.logger.Info(ctx, "Segment rebuilt successfully",
		logiface.Int64("segment_id", segment.ID),
		logiface.Int("contact_count", len(matchedContactIDs)))
	return nil
}

// ValidateSegmentRule 验证人群圈选规则
func (s *SegmentRuleService) ValidateSegmentRule(ctx context.Context, rule map[string]interface{}) error {
	s.logger.Debug(ctx, "Validating segment rule",
		logiface.Any("rule", rule))
	return s.ruleEngine.ValidateRule(rule)
}

// buildContactData 构建联系人数据用于规则评估
func (s *SegmentRuleService) buildContactData(contact interface{}) map[string]interface{} {
	data := make(map[string]interface{})

	// 如果已经是map格式，直接返回
	if contactMap, ok := contact.(map[string]interface{}); ok {
		return contactMap
	}

	// 尝试转换联系人实体为map
	if contactEntity, ok := contact.(*entity.Contact); ok {
		// 基本字段
		data["id"] = contactEntity.ID
		data["email"] = contactEntity.Email
		data["status"] = string(contactEntity.Status)
		data["preferred_language"] = contactEntity.PreferredLanguage
		data["consent_status"] = string(contactEntity.ConsentStatus)
		data["created_at"] = contactEntity.CreatedAt
		data["updated_at"] = contactEntity.UpdatedAt

		// 可选字段
		if contactEntity.CountryCode != nil {
			data["country_code"] = *contactEntity.CountryCode
		}
		if contactEntity.Timezone != nil {
			data["timezone"] = *contactEntity.Timezone
		}
		if contactEntity.UnsubscribedAt != nil {
			data["unsubscribed_at"] = *contactEntity.UnsubscribedAt
		}
		if contactEntity.ConsentAt != nil {
			data["consent_at"] = *contactEntity.ConsentAt
		}

		// 自定义属性
		if contactEntity.Attributes != nil {
			data["attributes"] = contactEntity.Attributes
			// 将自定义属性展平到根级别，方便规则引用
			for key, value := range contactEntity.Attributes {
				data[fmt.Sprintf("attributes.%s", key)] = value
			}
		} else {
			data["attributes"] = make(map[string]interface{})
		}

		// 计算字段（便于规则使用）
		data["is_active"] = contactEntity.Status == entity.ContactStatusActive
		data["is_unsubscribed"] = contactEntity.IsUnsubscribed()
		data["has_valid_consent"] = contactEntity.HasValidConsent()
		data["can_receive_email"] = contactEntity.CanReceiveEmail()

		// 时间相关计算字段
		if !contactEntity.CreatedAt.IsZero() {
			data["days_since_created"] = int(time.Since(contactEntity.CreatedAt).Hours() / 24)
		}
		if !contactEntity.UpdatedAt.IsZero() {
			data["days_since_updated"] = int(time.Since(contactEntity.UpdatedAt).Hours() / 24)
		}
		if contactEntity.UnsubscribedAt != nil {
			data["days_since_unsubscribed"] = int(time.Since(*contactEntity.UnsubscribedAt).Hours() / 24)
		}

		return data
	}

	// 如果是其他类型，尝试通过反射转换（兼容性处理）
	if contactValue := reflect.ValueOf(contact); contactValue.Kind() == reflect.Ptr && !contactValue.IsNil() {
		contactValue = contactValue.Elem()
		if contactValue.Kind() == reflect.Struct {
			contactType := contactValue.Type()
			for i := 0; i < contactValue.NumField(); i++ {
				field := contactType.Field(i)
				fieldValue := contactValue.Field(i)

				// 只处理导出的字段
				if field.PkgPath == "" && fieldValue.CanInterface() {
					// 获取JSON标签作为键名
					jsonTag := field.Tag.Get("json")
					key := field.Name
					if jsonTag != "" && jsonTag != "-" {
						if parts := strings.Split(jsonTag, ","); len(parts) > 0 && parts[0] != "" {
							key = parts[0]
						}
					}

					// 处理指针类型
					if fieldValue.Kind() == reflect.Ptr {
						if !fieldValue.IsNil() {
							data[strings.ToLower(key)] = fieldValue.Elem().Interface()
						}
					} else {
						data[strings.ToLower(key)] = fieldValue.Interface()
					}
				}
			}
		}
	}

	return data
}

// createSegmentSnapshot 创建人群圈选快照
func (s *SegmentRuleService) createSegmentSnapshot(ctx context.Context, snapshot *segmentEntity.SegmentSnapshot) error {
	s.logger.Info(ctx, "Creating segment snapshot",
		logiface.Int64("segment_id", snapshot.SegmentID),
		logiface.Int64("size", snapshot.Size))

	// 1. 序列化联系人ID列表
	err := snapshot.SetContactIDs(snapshot.ContactIDs)
	if err != nil {
		s.logger.Error(ctx, "Failed to serialize contact IDs",
			logiface.Error(err),
			logiface.Int64("segment_id", snapshot.SegmentID))
		return err
	}

	// 2. 设置默认过期时间（7天）
	if snapshot.ExpiresAt == nil {
		snapshot.SetExpiration(7 * 24 * time.Hour)
	}

	// 3. 删除旧快照（保留最新的）
	if err := s.segmentRepo.DeleteOldSnapshots(ctx, snapshot.TenantID, snapshot.SegmentID, 5); err != nil {
		s.logger.Warn(ctx, "Failed to delete old snapshots",
			logiface.Error(err),
			logiface.Int64("segment_id", snapshot.SegmentID))
		// 不影响主流程，继续执行
	}

	// 4. 保存快照到数据库
	err = s.segmentRepo.CreateSnapshot(ctx, snapshot)
	if err != nil {
		s.logger.Error(ctx, "Failed to create segment snapshot in database",
			logiface.Error(err),
			logiface.Int64("segment_id", snapshot.SegmentID))
		return err
	}

	s.logger.Info(ctx, "Segment snapshot created successfully",
		logiface.Int64("segment_id", snapshot.SegmentID),
		logiface.Int64("snapshot_id", snapshot.ID),
		logiface.Int64("contact_count", snapshot.Size))

	return nil
}

// GetSegmentRuleFields 获取人群圈选规则可用字段
func (s *SegmentRuleService) GetSegmentRuleFields() []SegmentRuleField {
	return []SegmentRuleField{
		{
			Name:        "email",
			Label:       "邮箱地址",
			Type:        "string",
			Description: "联系人的邮箱地址",
			Operators:   []string{"eq", "ne", "contains", "starts_with", "ends_with", "regex"},
		},
		{
			Name:        "status",
			Label:       "状态",
			Type:        "string",
			Description: "联系人状态",
			Operators:   []string{"eq", "ne", "in", "not_in"},
			Options: []FieldOption{
				{Label: "活跃", Value: "active"},
				{Label: "被抑制", Value: "suppressed"},
				{Label: "未确认", Value: "unconfirmed"},
				{Label: "退信", Value: "bounced"},
				{Label: "投诉", Value: "complained"},
				{Label: "已退订", Value: "unsubscribed"},
			},
		},
		{
			Name:        "preferred_language",
			Label:       "首选语言",
			Type:        "string",
			Description: "联系人的首选语言",
			Operators:   []string{"eq", "ne", "in", "not_in"},
		},
		{
			Name:        "country_code",
			Label:       "国家代码",
			Type:        "string",
			Description: "联系人所在国家的代码",
			Operators:   []string{"eq", "ne", "in", "not_in"},
		},
		{
			Name:        "consent_status",
			Label:       "同意状态",
			Type:        "string",
			Description: "联系人的同意状态",
			Operators:   []string{"eq", "ne", "in", "not_in"},
		},
		{
			Name:        "created_at",
			Label:       "创建时间",
			Type:        "datetime",
			Description: "联系人的创建时间",
			Operators:   []string{"eq", "ne", "gt", "gte", "lt", "lte", "before", "after", "within_days", "older_than"},
		},
		{
			Name:        "attributes.name",
			Label:       "姓名",
			Type:        "string",
			Description: "联系人的姓名",
			Operators:   []string{"eq", "ne", "contains", "starts_with", "ends_with", "exists", "not_exists"},
		},
		{
			Name:        "attributes.age",
			Label:       "年龄",
			Type:        "number",
			Description: "联系人的年龄",
			Operators:   []string{"eq", "ne", "gt", "gte", "lt", "lte"},
		},
		{
			Name:        "attributes.city",
			Label:       "城市",
			Type:        "string",
			Description: "联系人所在城市",
			Operators:   []string{"eq", "ne", "contains", "in", "not_in", "exists", "not_exists"},
		},
		{
			Name:        "attributes.total_amount",
			Label:       "总消费金额",
			Type:        "number",
			Description: "联系人的总消费金额",
			Operators:   []string{"eq", "ne", "gt", "gte", "lt", "lte"},
		},
	}
}

// SegmentRuleField 人群圈选规则字段
type SegmentRuleField struct {
	Name        string        `json:"name"`
	Label       string        `json:"label"`
	Type        string        `json:"type"`
	Description string        `json:"description"`
	Operators   []string      `json:"operators"`
	Options     []FieldOption `json:"options,omitempty"`
}

// FieldOption 字段选项
type FieldOption struct {
	Label string      `json:"label"`
	Value interface{} `json:"value"`
}
