package entity

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"
)

// Contact 联系人实体
type Contact struct {
	ID                int64                  `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID          int64                  `json:"tenant_id" gorm:"not null;index:idx_contact_tenant"`
	Email             string                 `json:"email" gorm:"not null;size:255;uniqueIndex:uk_contact_email"`
	Status            ContactStatus          `json:"status" gorm:"not null;default:'active';size:32"`
	PreferredLanguage string                 `json:"preferred_language" gorm:"not null;default:'zh-CN';size:16"`
	CountryCode       *string                `json:"country_code" gorm:"size:2"`
	Timezone          *string                `json:"timezone" gorm:"size:64"`
	Notes             *string                `json:"notes" gorm:"type:text"`
	Attributes        map[string]interface{} `json:"attributes" gorm:"type:json"`

	// 退订和同意相关字段
	UnsubscribeToken *string       `json:"unsubscribe_token" gorm:"size:64;index:idx_contact_unsubscribe_token"`
	UnsubscribedAt   *time.Time    `json:"unsubscribed_at"`
	ConsentStatus    ConsentStatus `json:"consent_status" gorm:"not null;default:'unknown';size:32"`
	ConsentSource    *string       `json:"consent_source" gorm:"size:64"`
	ConsentAt        *time.Time    `json:"consent_at"`
	ConsentIP        *string       `json:"consent_ip" gorm:"size:45"`
	ConsentUserAgent *string       `json:"consent_user_agent" gorm:"size:512"`

	CreatedAt time.Time `json:"created_at" gorm:"not null;autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"not null;autoUpdateTime"`
}

// ContactStatus 联系人状态
type ContactStatus string

const (
	ContactStatusActive       ContactStatus = "active"
	ContactStatusSuppressed   ContactStatus = "suppressed"
	ContactStatusUnconfirmed  ContactStatus = "unconfirmed"
	ContactStatusBounced      ContactStatus = "bounced"
	ContactStatusComplained   ContactStatus = "complained"
	ContactStatusUnsubscribed ContactStatus = "unsubscribed"
)

// IsValid 检查联系人状态是否有效
func (s ContactStatus) IsValid() bool {
	switch s {
	case ContactStatusActive, ContactStatusSuppressed, ContactStatusUnconfirmed, ContactStatusBounced, ContactStatusComplained, ContactStatusUnsubscribed:
		return true
	default:
		return false
	}
}

// ConsentStatus 同意状态
type ConsentStatus string

const (
	ConsentStatusUnknown   ConsentStatus = "unknown"   // 未知
	ConsentStatusGranted   ConsentStatus = "granted"   // 已同意
	ConsentStatusWithdrawn ConsentStatus = "withdrawn" // 已撤回
	ConsentStatusPending   ConsentStatus = "pending"   // 待确认
)

// IsValid 检查同意状态是否有效
func (s ConsentStatus) IsValid() bool {
	switch s {
	case ConsentStatusUnknown, ConsentStatusGranted, ConsentStatusWithdrawn, ConsentStatusPending:
		return true
	default:
		return false
	}
}

// TableName 指定表名
func (Contact) TableName() string {
	return "contacts"
}

// SetAttribute 设置自定义属性
func (c *Contact) SetAttribute(key string, value interface{}) {
	if c.Attributes == nil {
		c.Attributes = make(map[string]interface{})
	}
	c.Attributes[key] = value
}

// GetAttribute 获取自定义属性
func (c *Contact) GetAttribute(key string) (interface{}, bool) {
	if c.Attributes == nil {
		return nil, false
	}
	value, exists := c.Attributes[key]
	return value, exists
}

// GetAttributeString 获取字符串类型的自定义属性
func (c *Contact) GetAttributeString(key string) string {
	if value, exists := c.GetAttribute(key); exists {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

// GetAttributeInt64 获取int64类型的自定义属性
func (c *Contact) GetAttributeInt64(key string) int64 {
	if value, exists := c.GetAttribute(key); exists {
		switch v := value.(type) {
		case int64:
			return v
		case float64:
			return int64(v)
		case int:
			return int64(v)
		}
	}
	return 0
}

// GetAttributeBool 获取bool类型的自定义属性
func (c *Contact) GetAttributeBool(key string) bool {
	if value, exists := c.GetAttribute(key); exists {
		if b, ok := value.(bool); ok {
			return b
		}
	}
	return false
}

// MergeAttributes 合并属性
func (c *Contact) MergeAttributes(newAttrs map[string]interface{}, policy MergePolicy) {
	if c.Attributes == nil {
		c.Attributes = make(map[string]interface{})
	}

	for key, newValue := range newAttrs {
		switch policy {
		case MergePolicyKeepExisting:
			if _, exists := c.Attributes[key]; !exists {
				c.Attributes[key] = newValue
			}
		case MergePolicyNewOverwrite:
			c.Attributes[key] = newValue
		case MergePolicyConcat:
			if existingValue, exists := c.Attributes[key]; exists {
				if existingStr, ok := existingValue.(string); ok {
					if newStr, ok := newValue.(string); ok {
						c.Attributes[key] = existingStr + " " + newStr
					} else {
						c.Attributes[key] = newValue
					}
				} else {
					c.Attributes[key] = newValue
				}
			} else {
				c.Attributes[key] = newValue
			}
		}
	}
}

// MergePolicy 合并策略
type MergePolicy string

const (
	MergePolicyKeepExisting MergePolicy = "keep_existing"
	MergePolicyNewOverwrite MergePolicy = "new_overwrite"
	MergePolicyMostRecent   MergePolicy = "most_recent"
	MergePolicyConcat       MergePolicy = "concat"
)

// IsValidMergePolicy 检查合并策略是否有效
func IsValidMergePolicy(policy MergePolicy) bool {
	switch policy {
	case MergePolicyKeepExisting, MergePolicyNewOverwrite, MergePolicyMostRecent, MergePolicyConcat:
		return true
	default:
		return false
	}
}

// Clone 克隆联系人实体
func (c *Contact) Clone() *Contact {
	clone := &Contact{
		ID:                c.ID,
		TenantID:          c.TenantID,
		Email:             c.Email,
		Status:            c.Status,
		PreferredLanguage: c.PreferredLanguage,
		CreatedAt:         c.CreatedAt,
		UpdatedAt:         c.UpdatedAt,
	}

	if c.CountryCode != nil {
		countryCode := *c.CountryCode
		clone.CountryCode = &countryCode
	}

	if c.Timezone != nil {
		timezone := *c.Timezone
		clone.Timezone = &timezone
	}

	if c.Notes != nil {
		notes := *c.Notes
		clone.Notes = &notes
	}

	if c.Attributes != nil {
		clone.Attributes = make(map[string]interface{})
		for k, v := range c.Attributes {
			clone.Attributes[k] = v
		}
	}

	return clone
}

// ToJSON 转换为JSON字符串
func (c *Contact) ToJSON() (string, error) {
	data, err := json.Marshal(c)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// Validate 验证联系人数据
func (c *Contact) Validate() error {
	if c.TenantID <= 0 {
		return NewContactValidationError("tenant_id", "租户ID不能为空")
	}

	if c.Email == "" {
		return NewContactValidationError("email", "邮箱地址不能为空")
	}

	if !c.Status.IsValid() {
		return NewContactValidationError("status", "无效的联系人状态")
	}

	// 验证邮箱格式
	if !isValidEmail(c.Email) {
		return NewContactValidationError("email", "邮箱格式不正确")
	}

	return nil
}

// isValidEmail 简单的邮箱格式验证
func isValidEmail(email string) bool {
	// 这里可以使用更复杂的邮箱验证逻辑
	return len(email) > 0 && len(email) <= 255
}

// GenerateUnsubscribeToken 生成退订令牌
func (c *Contact) GenerateUnsubscribeToken() string {
	// 使用联系人ID、邮箱和时间戳生成唯一令牌
	data := fmt.Sprintf("%d:%s:%d", c.ID, c.Email, time.Now().Unix())
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])[:32] // 取前32位
}

// SetUnsubscribeToken 设置退订令牌
func (c *Contact) SetUnsubscribeToken() {
	token := c.GenerateUnsubscribeToken()
	c.UnsubscribeToken = &token
}

// Unsubscribe 执行退订操作
func (c *Contact) Unsubscribe() {
	now := time.Now()
	c.Status = ContactStatusUnsubscribed
	c.UnsubscribedAt = &now
	c.ConsentStatus = ConsentStatusWithdrawn
}

// IsUnsubscribed 检查是否已退订
func (c *Contact) IsUnsubscribed() bool {
	return c.Status == ContactStatusUnsubscribed || c.UnsubscribedAt != nil
}

// GrantConsent 授予同意
func (c *Contact) GrantConsent(source, ip, userAgent string) {
	now := time.Now()
	c.ConsentStatus = ConsentStatusGranted
	c.ConsentAt = &now
	if source != "" {
		c.ConsentSource = &source
	}
	if ip != "" {
		c.ConsentIP = &ip
	}
	if userAgent != "" {
		c.ConsentUserAgent = &userAgent
	}
}

// WithdrawConsent 撤回同意
func (c *Contact) WithdrawConsent() {
	c.ConsentStatus = ConsentStatusWithdrawn
	// 撤回同意时通常也会退订
	c.Unsubscribe()
}

// HasValidConsent 检查是否有有效同意
func (c *Contact) HasValidConsent() bool {
	return c.ConsentStatus == ConsentStatusGranted && !c.IsUnsubscribed()
}

// CanReceiveEmail 检查是否可以接收邮件
func (c *Contact) CanReceiveEmail() bool {
	// 必须是活跃状态且有有效同意
	return c.Status == ContactStatusActive && c.HasValidConsent()
}
