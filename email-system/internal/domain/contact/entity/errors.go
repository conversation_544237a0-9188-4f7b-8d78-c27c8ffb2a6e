package entity

import "fmt"

// ContactError 联系人相关错误
type ContactError struct {
	Code    string
	Message string
	Field   string
	Details string
}

func (e *ContactError) Error() string {
	if e.Field != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Code, e.Field, e.Message)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// 错误码定义
const (
	// 联系人验证错误 (120000-120099)
	CodeContactValidationError = "120001"
	CodeContactEmailInvalid    = "120002"
	CodeContactEmailExists     = "120003"
	CodeContactNotFound        = "120004"
	CodeContactStatusInvalid   = "120005"
	CodeContactLanguageInvalid = "120006"
	CodeContactTimezoneInvalid = "120007"
	CodeContactAttributeError  = "120008"

	// 联系人操作错误 (120010-120019)
	CodeContactCreateFailed = "120010"
	CodeContactUpdateFailed = "120011"
	CodeContactDeleteFailed = "120012"
	CodeContactQueryFailed  = "120013"

	// 联系人导入错误 (120020-120029)
	CodeContactImportFailed      = "120020"
	CodeContactImportFileInvalid = "120021"
	CodeContactImportDataInvalid = "120022"
	CodeContactImportJobNotFound = "120023"

	// 联系人属性错误 (120030-120039)
	CodeContactAttributeNotFound     = "120030"
	CodeContactAttributeTypeInvalid  = "120031"
	CodeContactAttributeValueInvalid = "120032"
)

// NewContactValidationError 创建联系人验证错误
func NewContactValidationError(field, message string) *ContactError {
	return &ContactError{
		Code:    CodeContactValidationError,
		Message: message,
		Field:   field,
	}
}

// NewContactEmailInvalidError 创建邮箱无效错误
func NewContactEmailInvalidError(email string) *ContactError {
	return &ContactError{
		Code:    CodeContactEmailInvalid,
		Message: "邮箱格式不正确",
		Field:   "email",
		Details: email,
	}
}

// NewContactEmailExistsError 创建邮箱已存在错误
func NewContactEmailExistsError(email string) *ContactError {
	return &ContactError{
		Code:    CodeContactEmailExists,
		Message: "邮箱地址已存在",
		Field:   "email",
		Details: email,
	}
}

// NewContactNotFoundError 创建联系人不存在错误
func NewContactNotFoundError(identifier string) *ContactError {
	return &ContactError{
		Code:    CodeContactNotFound,
		Message: "联系人不存在",
		Details: identifier,
	}
}

// NewContactStatusInvalidError 创建状态无效错误
func NewContactStatusInvalidError(status string) *ContactError {
	return &ContactError{
		Code:    CodeContactStatusInvalid,
		Message: "无效的联系人状态",
		Field:   "status",
		Details: status,
	}
}

// NewContactLanguageInvalidError 创建语言无效错误
func NewContactLanguageInvalidError(language string) *ContactError {
	return &ContactError{
		Code:    CodeContactLanguageInvalid,
		Message: "无效的偏好语言",
		Field:   "preferred_language",
		Details: language,
	}
}

// NewContactTimezoneInvalidError 创建时区无效错误
func NewContactTimezoneInvalidError(timezone string) *ContactError {
	return &ContactError{
		Code:    CodeContactTimezoneInvalid,
		Message: "无效的时区",
		Field:   "timezone",
		Details: timezone,
	}
}

// NewContactAttributeError 创建属性错误
func NewContactAttributeError(field, message string) *ContactError {
	return &ContactError{
		Code:    CodeContactAttributeError,
		Message: message,
		Field:   field,
	}
}

// NewContactCreateFailedError 创建联系人创建失败错误
func NewContactCreateFailedError(reason string) *ContactError {
	return &ContactError{
		Code:    CodeContactCreateFailed,
		Message: "创建联系人失败",
		Details: reason,
	}
}

// NewContactUpdateFailedError 创建联系人更新失败错误
func NewContactUpdateFailedError(reason string) *ContactError {
	return &ContactError{
		Code:    CodeContactUpdateFailed,
		Message: "更新联系人失败",
		Details: reason,
	}
}

// NewContactDeleteFailedError 创建联系人删除失败错误
func NewContactDeleteFailedError(reason string) *ContactError {
	return &ContactError{
		Code:    CodeContactDeleteFailed,
		Message: "删除联系人失败",
		Details: reason,
	}
}

// NewContactQueryFailedError 创建联系人查询失败错误
func NewContactQueryFailedError(reason string) *ContactError {
	return &ContactError{
		Code:    CodeContactQueryFailed,
		Message: "查询联系人失败",
		Details: reason,
	}
}

// NewContactImportFailedError 创建联系人导入失败错误
func NewContactImportFailedError(reason string) *ContactError {
	return &ContactError{
		Code:    CodeContactImportFailed,
		Message: "导入联系人失败",
		Details: reason,
	}
}

// NewContactImportFileInvalidError 创建导入文件无效错误
func NewContactImportFileInvalidError(reason string) *ContactError {
	return &ContactError{
		Code:    CodeContactImportFileInvalid,
		Message: "导入文件格式无效",
		Details: reason,
	}
}

// NewContactImportDataInvalidError 创建导入数据无效错误
func NewContactImportDataInvalidError(reason string) *ContactError {
	return &ContactError{
		Code:    CodeContactImportDataInvalid,
		Message: "导入数据格式无效",
		Details: reason,
	}
}

// NewContactImportJobNotFoundError 创建导入任务不存在错误
func NewContactImportJobNotFoundError(jobID string) *ContactError {
	return &ContactError{
		Code:    CodeContactImportJobNotFound,
		Message: "导入任务不存在",
		Details: jobID,
	}
}

// NewContactAttributeNotFoundError 创建属性不存在错误
func NewContactAttributeNotFoundError(attribute string) *ContactError {
	return &ContactError{
		Code:    CodeContactAttributeNotFound,
		Message: "联系人属性不存在",
		Field:   attribute,
	}
}

// NewContactAttributeTypeInvalidError 创建属性类型无效错误
func NewContactAttributeTypeInvalidError(attribute, expectedType string) *ContactError {
	return &ContactError{
		Code:    CodeContactAttributeTypeInvalid,
		Message: fmt.Sprintf("属性类型无效，期望类型: %s", expectedType),
		Field:   attribute,
	}
}

// NewContactAttributeValueInvalidError 创建属性值无效错误
func NewContactAttributeValueInvalidError(attribute, reason string) *ContactError {
	return &ContactError{
		Code:    CodeContactAttributeValueInvalid,
		Message: "属性值无效",
		Field:   attribute,
		Details: reason,
	}
}
