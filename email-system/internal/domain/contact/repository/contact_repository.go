package repository

import (
	"context"

	"gitee.com/heiyee/platforms/email-system/internal/domain/contact/entity"
)

// ContactRepository 联系人仓储接口
type ContactRepository interface {
	// Create 创建联系人
	Create(ctx context.Context, contact *entity.Contact) error

	// Update 更新联系人
	Update(ctx context.Context, contact *entity.Contact) error

	// Delete 删除联系人
	Delete(ctx context.Context, tenantID, contactID int64) error

	// FindByID 根据ID查找联系人
	FindByID(ctx context.Context, tenantID, contactID int64) (*entity.Contact, error)

	// FindByEmail 根据邮箱查找联系人
	FindByEmail(ctx context.Context, tenantID int64, email string) (*entity.Contact, error)

	// FindByUnsubscribeToken 根据退订令牌查找联系人
	FindByUnsubscribeToken(ctx context.Context, tenantID int64, token string) (*entity.Contact, error)

	// FindActiveContacts 查找所有活跃联系人
	FindActiveContacts(ctx context.Context, tenantID int64) ([]*entity.Contact, error)

	// FindActiveContactsWithLimit 查找活跃联系人（限制数量）
	FindActiveContactsWithLimit(ctx context.Context, tenantID int64, limit int) ([]*entity.Contact, error)

	// CountActiveContacts 统计活跃联系人数量
	CountActiveContacts(ctx context.Context, tenantID int64) (int64, error)

	// FindByIDs 根据ID列表批量查找联系人
	FindByIDs(ctx context.Context, tenantID int64, contactIDs []int64) ([]*entity.Contact, error)

	// Search 搜索联系人
	Search(ctx context.Context, params *SearchParams) (*SearchResult, error)

	// Count 统计联系人数量
	Count(ctx context.Context, tenantID int64, filters *SearchFilters) (int64, error)

	// ExistsByEmail 检查邮箱是否存在
	ExistsByEmail(ctx context.Context, tenantID int64, email string) (bool, error)

	// BatchCreate 批量创建联系人
	BatchCreate(ctx context.Context, contacts []*entity.Contact) error

	// BatchUpdate 批量更新联系人
	BatchUpdate(ctx context.Context, contacts []*entity.Contact) error

	// UpdateStatus 更新联系人状态
	UpdateStatus(ctx context.Context, tenantID int64, contactIDs []int64, status entity.ContactStatus) error

	// GetContactsByTags 根据标签获取联系人
	GetContactsByTags(ctx context.Context, tenantID int64, tagIDs []int64, params *SearchParams) (*SearchResult, error)

	// GetContactsByLists 根据列表获取联系人
	GetContactsByLists(ctx context.Context, tenantID int64, listIDs []int64, params *SearchParams) (*SearchResult, error)
}

// SearchParams 搜索参数
type SearchParams struct {
	TenantID int64          `json:"tenant_id"`
	Filters  *SearchFilters `json:"filters"`
	Sort     *SortOptions   `json:"sort"`
	Page     int            `json:"page"`
	Size     int            `json:"size"`
	Offset   int            `json:"offset"`
	Limit    int            `json:"limit"`
}

// SearchFilters 搜索过滤条件
type SearchFilters struct {
	// 基础字段过滤
	Email             string               `json:"email"`
	Status            entity.ContactStatus `json:"status"`
	PreferredLanguage string               `json:"preferred_language"`
	CountryCode       string               `json:"country_code"`
	Timezone          string               `json:"timezone"`
	Keyword           string               `json:"keyword"`

	// 标签和列表过滤
	TagIDs  []int64 `json:"tag_ids"`
	ListIDs []int64 `json:"list_ids"`

	// 自定义属性过滤
	Attributes map[string]interface{} `json:"attributes"`

	// 时间范围过滤
	CreatedAtRange *TimeRange `json:"created_at_range"`
	UpdatedAtRange *TimeRange `json:"updated_at_range"`

	// 排除条件
	ExcludeContactIDs []int64 `json:"exclude_contact_ids"`
	ExcludeTagIDs     []int64 `json:"exclude_tag_ids"`
	ExcludeListIDs    []int64 `json:"exclude_list_ids"`
}

// TimeRange 时间范围
type TimeRange struct {
	Start *string `json:"start"`
	End   *string `json:"end"`
}

// SortOptions 排序选项
type SortOptions struct {
	Field string `json:"field"`
	Order string `json:"order"` // asc, desc
}

// SearchResult 搜索结果
type SearchResult struct {
	Contacts   []*entity.Contact `json:"contacts"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	Size       int               `json:"size"`
	TotalPages int               `json:"total_pages"`
}

// NewSearchParams 创建搜索参数
func NewSearchParams(tenantID int64) *SearchParams {
	return &SearchParams{
		TenantID: tenantID,
		Filters:  &SearchFilters{},
		Sort: &SortOptions{
			Field: "created_at",
			Order: "desc",
		},
		Page: 1,
		Size: 20,
	}
}

// WithFilters 设置过滤条件
func (p *SearchParams) WithFilters(filters *SearchFilters) *SearchParams {
	p.Filters = filters
	return p
}

// WithSort 设置排序
func (p *SearchParams) WithSort(field, order string) *SearchParams {
	p.Sort = &SortOptions{
		Field: field,
		Order: order,
	}
	return p
}

// WithPagination 设置分页
func (p *SearchParams) WithPagination(page, size int) *SearchParams {
	p.Page = page
	p.Size = size
	return p
}

// WithOffset 设置偏移量分页
func (p *SearchParams) WithOffset(offset, limit int) *SearchParams {
	p.Offset = offset
	p.Limit = limit
	return p
}

// GetOffset 获取偏移量
func (p *SearchParams) GetOffset() int {
	if p.Offset > 0 {
		return p.Offset
	}
	if p.Page > 0 && p.Size > 0 {
		return (p.Page - 1) * p.Size
	}
	return 0
}

// GetLimit 获取限制数量
func (p *SearchParams) GetLimit() int {
	if p.Limit > 0 {
		return p.Limit
	}
	if p.Size > 0 {
		return p.Size
	}
	return 20
}

// Validate 验证搜索参数
func (p *SearchParams) Validate() error {
	if p.TenantID <= 0 {
		return entity.NewContactValidationError("tenant_id", "租户ID不能为空")
	}

	if p.Page < 0 {
		return entity.NewContactValidationError("page", "页码不能小于0")
	}

	if p.Size < 0 || p.Size > 1000 {
		return entity.NewContactValidationError("size", "每页大小必须在1-1000之间")
	}

	if p.Sort != nil {
		if p.Sort.Order != "" && p.Sort.Order != "asc" && p.Sort.Order != "desc" {
			return entity.NewContactValidationError("sort.order", "排序方向只能是asc或desc")
		}
	}

	return nil
}
