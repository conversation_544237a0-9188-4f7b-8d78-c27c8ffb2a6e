package entity

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

// RuleOperator 规则操作符
type RuleOperator string

const (
	// 逻辑操作符
	OperatorAnd RuleOperator = "and"
	OperatorOr  RuleOperator = "or"
	OperatorNot RuleOperator = "not"

	// 比较操作符
	OperatorEqual              RuleOperator = "eq"           // 等于
	OperatorNotEqual           RuleOperator = "ne"           // 不等于
	OperatorGreaterThan        RuleOperator = "gt"           // 大于
	OperatorGreaterThanOrEqual RuleOperator = "gte"          // 大于等于
	OperatorLessThan           RuleOperator = "lt"           // 小于
	OperatorLessThanOrEqual    RuleOperator = "lte"          // 小于等于
	OperatorIn                 RuleOperator = "in"           // 包含在列表中
	OperatorNotIn              RuleOperator = "not_in"       // 不包含在列表中
	OperatorContains           RuleOperator = "contains"     // 包含
	OperatorNotContains        RuleOperator = "not_contains" // 不包含
	OperatorStartsWith         RuleOperator = "starts_with"  // 以...开始
	OperatorEndsWith           RuleOperator = "ends_with"    // 以...结束
	OperatorRegex              RuleOperator = "regex"        // 正则表达式
	OperatorExists             RuleOperator = "exists"       // 字段存在
	OperatorNotExists          RuleOperator = "not_exists"   // 字段不存在
	OperatorIsEmpty            RuleOperator = "is_empty"     // 为空
	OperatorIsNotEmpty         RuleOperator = "is_not_empty" // 不为空

	// 时间操作符
	OperatorBefore     RuleOperator = "before"      // 在...之前
	OperatorAfter      RuleOperator = "after"       // 在...之后
	OperatorWithinDays RuleOperator = "within_days" // 在...天内
	OperatorOlderThan  RuleOperator = "older_than"  // 超过...天
)

// FieldType 字段类型
type FieldType string

const (
	FieldTypeString   FieldType = "string"
	FieldTypeNumber   FieldType = "number"
	FieldTypeBoolean  FieldType = "boolean"
	FieldTypeDate     FieldType = "date"
	FieldTypeDateTime FieldType = "datetime"
	FieldTypeArray    FieldType = "array"
	FieldTypeObject   FieldType = "object"
)

// Rule 规则结构
type Rule struct {
	Operator   RuleOperator `json:"operator"`
	Conditions []*Condition `json:"conditions,omitempty"`
	Rules      []*Rule      `json:"rules,omitempty"`
}

// Condition 条件结构
type Condition struct {
	Field    string       `json:"field"`
	Operator RuleOperator `json:"operator"`
	Value    interface{}  `json:"value"`
	Type     FieldType    `json:"type,omitempty"`
}

// RuleContext 规则执行上下文
type RuleContext struct {
	Data   map[string]interface{} `json:"data"`
	Fields map[string]FieldType   `json:"fields,omitempty"`
}

// NewRule 创建新规则
func NewRule(operator RuleOperator) *Rule {
	return &Rule{
		Operator:   operator,
		Conditions: make([]*Condition, 0),
		Rules:      make([]*Rule, 0),
	}
}

// NewCondition 创建新条件
func NewCondition(field string, operator RuleOperator, value interface{}) *Condition {
	return &Condition{
		Field:    field,
		Operator: operator,
		Value:    value,
		Type:     inferFieldType(value),
	}
}

// AddCondition 添加条件
func (r *Rule) AddCondition(condition *Condition) *Rule {
	r.Conditions = append(r.Conditions, condition)
	return r
}

// AddRule 添加子规则
func (r *Rule) AddRule(rule *Rule) *Rule {
	r.Rules = append(r.Rules, rule)
	return r
}

// Validate 验证规则
func (r *Rule) Validate() error {
	if r.Operator == "" {
		return NewRuleValidationError("operator", "规则操作符不能为空")
	}

	if !r.isValidOperator() {
		return NewRuleValidationError("operator", fmt.Sprintf("无效的规则操作符: %s", r.Operator))
	}

	// 逻辑操作符需要子规则或条件
	if r.isLogicalOperator() {
		if len(r.Rules) == 0 && len(r.Conditions) == 0 {
			return NewRuleValidationError("rules", "逻辑操作符需要至少一个子规则或条件")
		}
	}

	// 验证条件
	for i, condition := range r.Conditions {
		if err := condition.Validate(); err != nil {
			return fmt.Errorf("condition[%d]: %w", i, err)
		}
	}

	// 验证子规则
	for i, rule := range r.Rules {
		if err := rule.Validate(); err != nil {
			return fmt.Errorf("rule[%d]: %w", i, err)
		}
	}

	return nil
}

// Evaluate 评估规则
func (r *Rule) Evaluate(ctx *RuleContext) (bool, error) {
	if err := r.Validate(); err != nil {
		return false, err
	}

	switch r.Operator {
	case OperatorAnd:
		return r.evaluateAnd(ctx)
	case OperatorOr:
		return r.evaluateOr(ctx)
	case OperatorNot:
		return r.evaluateNot(ctx)
	default:
		return false, NewRuleExecutionError(fmt.Sprintf("unsupported operator: %s", r.Operator))
	}
}

// evaluateAnd 评估AND操作
func (r *Rule) evaluateAnd(ctx *RuleContext) (bool, error) {
	// 评估所有条件
	for _, condition := range r.Conditions {
		result, err := condition.Evaluate(ctx)
		if err != nil {
			return false, err
		}
		if !result {
			return false, nil
		}
	}

	// 评估所有子规则
	for _, rule := range r.Rules {
		result, err := rule.Evaluate(ctx)
		if err != nil {
			return false, err
		}
		if !result {
			return false, nil
		}
	}

	return true, nil
}

// evaluateOr 评估OR操作
func (r *Rule) evaluateOr(ctx *RuleContext) (bool, error) {
	// 评估所有条件
	for _, condition := range r.Conditions {
		result, err := condition.Evaluate(ctx)
		if err != nil {
			return false, err
		}
		if result {
			return true, nil
		}
	}

	// 评估所有子规则
	for _, rule := range r.Rules {
		result, err := rule.Evaluate(ctx)
		if err != nil {
			return false, err
		}
		if result {
			return true, nil
		}
	}

	return false, nil
}

// evaluateNot 评估NOT操作
func (r *Rule) evaluateNot(ctx *RuleContext) (bool, error) {
	if len(r.Rules) != 1 {
		return false, NewRuleValidationError("rules", "NOT操作符只能有一个子规则")
	}

	result, err := r.Rules[0].Evaluate(ctx)
	if err != nil {
		return false, err
	}

	return !result, nil
}

// isValidOperator 检查操作符是否有效
func (r *Rule) isValidOperator() bool {
	validOperators := []RuleOperator{
		OperatorAnd, OperatorOr, OperatorNot,
	}

	for _, op := range validOperators {
		if r.Operator == op {
			return true
		}
	}

	return false
}

// isLogicalOperator 检查是否为逻辑操作符
func (r *Rule) isLogicalOperator() bool {
	return r.Operator == OperatorAnd || r.Operator == OperatorOr || r.Operator == OperatorNot
}

// Validate 验证条件
func (c *Condition) Validate() error {
	if c.Field == "" {
		return NewRuleValidationError("field", "字段名不能为空")
	}

	if c.Operator == "" {
		return NewRuleValidationError("operator", "操作符不能为空")
	}

	if !c.isValidOperator() {
		return NewRuleValidationError("operator", fmt.Sprintf("无效的操作符: %s", c.Operator))
	}

	// 某些操作符不需要值
	if c.needsValue() && c.Value == nil {
		return NewRuleValidationError("value", "该操作符需要提供值")
	}

	return nil
}

// Evaluate 评估条件
func (c *Condition) Evaluate(ctx *RuleContext) (bool, error) {
	if err := c.Validate(); err != nil {
		return false, err
	}

	// 获取字段值
	fieldValue := getFieldValue(ctx.Data, c.Field)

	switch c.Operator {
	case OperatorEqual:
		return c.evaluateEqual(fieldValue)
	case OperatorNotEqual:
		result, err := c.evaluateEqual(fieldValue)
		return !result, err
	case OperatorGreaterThan:
		return c.evaluateGreaterThan(fieldValue)
	case OperatorGreaterThanOrEqual:
		return c.evaluateGreaterThanOrEqual(fieldValue)
	case OperatorLessThan:
		return c.evaluateLessThan(fieldValue)
	case OperatorLessThanOrEqual:
		return c.evaluateLessThanOrEqual(fieldValue)
	case OperatorIn:
		return c.evaluateIn(fieldValue)
	case OperatorNotIn:
		result, err := c.evaluateIn(fieldValue)
		return !result, err
	case OperatorContains:
		return c.evaluateContains(fieldValue)
	case OperatorNotContains:
		result, err := c.evaluateContains(fieldValue)
		return !result, err
	case OperatorStartsWith:
		return c.evaluateStartsWith(fieldValue)
	case OperatorEndsWith:
		return c.evaluateEndsWith(fieldValue)
	case OperatorExists:
		return fieldValue != nil, nil
	case OperatorNotExists:
		return fieldValue == nil, nil
	case OperatorIsEmpty:
		return c.evaluateIsEmpty(fieldValue)
	case OperatorIsNotEmpty:
		result, err := c.evaluateIsEmpty(fieldValue)
		return !result, err
	default:
		return false, NewRuleExecutionError(fmt.Sprintf("unsupported condition operator: %s", c.Operator))
	}
}

// inferFieldType 推断字段类型
func inferFieldType(value interface{}) FieldType {
	if value == nil {
		return FieldTypeString
	}

	switch value.(type) {
	case string:
		return FieldTypeString
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64:
		return FieldTypeNumber
	case bool:
		return FieldTypeBoolean
	case time.Time:
		return FieldTypeDateTime
	case []interface{}:
		return FieldTypeArray
	case map[string]interface{}:
		return FieldTypeObject
	default:
		return FieldTypeString
	}
}

// getFieldValue 获取字段值，支持嵌套字段
func getFieldValue(data map[string]interface{}, field string) interface{} {
	if data == nil {
		return nil
	}

	// 支持点号分隔的嵌套字段，如 "user.profile.name"
	parts := strings.Split(field, ".")
	current := data

	for i, part := range parts {
		if current == nil {
			return nil
		}

		value, exists := current[part]
		if !exists {
			return nil
		}

		// 如果是最后一个部分，返回值
		if i == len(parts)-1 {
			return value
		}

		// 否则继续向下查找
		if nested, ok := value.(map[string]interface{}); ok {
			current = nested
		} else {
			return nil
		}
	}

	return nil
}

// isValidOperator 检查条件操作符是否有效
func (c *Condition) isValidOperator() bool {
	validOperators := []RuleOperator{
		OperatorEqual, OperatorNotEqual,
		OperatorGreaterThan, OperatorGreaterThanOrEqual,
		OperatorLessThan, OperatorLessThanOrEqual,
		OperatorIn, OperatorNotIn,
		OperatorContains, OperatorNotContains,
		OperatorStartsWith, OperatorEndsWith,
		OperatorRegex, OperatorExists, OperatorNotExists,
		OperatorIsEmpty, OperatorIsNotEmpty,
		OperatorBefore, OperatorAfter,
		OperatorWithinDays, OperatorOlderThan,
	}

	for _, op := range validOperators {
		if c.Operator == op {
			return true
		}
	}

	return false
}

// needsValue 检查操作符是否需要值
func (c *Condition) needsValue() bool {
	noValueOperators := []RuleOperator{
		OperatorExists, OperatorNotExists,
		OperatorIsEmpty, OperatorIsNotEmpty,
	}

	for _, op := range noValueOperators {
		if c.Operator == op {
			return false
		}
	}

	return true
}

// evaluateEqual 评估等于操作
func (c *Condition) evaluateEqual(fieldValue interface{}) (bool, error) {
	return compareValues(fieldValue, c.Value) == 0, nil
}

// evaluateGreaterThan 评估大于操作
func (c *Condition) evaluateGreaterThan(fieldValue interface{}) (bool, error) {
	return compareValues(fieldValue, c.Value) > 0, nil
}

// evaluateGreaterThanOrEqual 评估大于等于操作
func (c *Condition) evaluateGreaterThanOrEqual(fieldValue interface{}) (bool, error) {
	return compareValues(fieldValue, c.Value) >= 0, nil
}

// evaluateLessThan 评估小于操作
func (c *Condition) evaluateLessThan(fieldValue interface{}) (bool, error) {
	return compareValues(fieldValue, c.Value) < 0, nil
}

// evaluateLessThanOrEqual 评估小于等于操作
func (c *Condition) evaluateLessThanOrEqual(fieldValue interface{}) (bool, error) {
	return compareValues(fieldValue, c.Value) <= 0, nil
}

// evaluateIn 评估包含操作
func (c *Condition) evaluateIn(fieldValue interface{}) (bool, error) {
	valueList, ok := c.Value.([]interface{})
	if !ok {
		return false, NewRuleExecutionError("IN操作符的值必须是数组")
	}

	for _, item := range valueList {
		if compareValues(fieldValue, item) == 0 {
			return true, nil
		}
	}

	return false, nil
}

// evaluateContains 评估包含操作
func (c *Condition) evaluateContains(fieldValue interface{}) (bool, error) {
	fieldStr := toString(fieldValue)
	valueStr := toString(c.Value)

	return strings.Contains(fieldStr, valueStr), nil
}

// evaluateStartsWith 评估以...开始操作
func (c *Condition) evaluateStartsWith(fieldValue interface{}) (bool, error) {
	fieldStr := toString(fieldValue)
	valueStr := toString(c.Value)

	return strings.HasPrefix(fieldStr, valueStr), nil
}

// evaluateEndsWith 评估以...结束操作
func (c *Condition) evaluateEndsWith(fieldValue interface{}) (bool, error) {
	fieldStr := toString(fieldValue)
	valueStr := toString(c.Value)

	return strings.HasSuffix(fieldStr, valueStr), nil
}

// evaluateIsEmpty 评估为空操作
func (c *Condition) evaluateIsEmpty(fieldValue interface{}) (bool, error) {
	if fieldValue == nil {
		return true, nil
	}

	switch v := fieldValue.(type) {
	case string:
		return v == "", nil
	case []interface{}:
		return len(v) == 0, nil
	case map[string]interface{}:
		return len(v) == 0, nil
	default:
		return false, nil
	}
}

// compareValues 比较两个值
func compareValues(a, b interface{}) int {
	if a == nil && b == nil {
		return 0
	}
	if a == nil {
		return -1
	}
	if b == nil {
		return 1
	}

	// 尝试转换为数字比较
	if aNum, aOk := toNumber(a); aOk {
		if bNum, bOk := toNumber(b); bOk {
			if aNum < bNum {
				return -1
			} else if aNum > bNum {
				return 1
			} else {
				return 0
			}
		}
	}

	// 字符串比较
	aStr := toString(a)
	bStr := toString(b)

	if aStr < bStr {
		return -1
	} else if aStr > bStr {
		return 1
	} else {
		return 0
	}
}

// toString 转换为字符串
func toString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int, int8, int16, int32, int64:
		return fmt.Sprintf("%d", v)
	case uint, uint8, uint16, uint32, uint64:
		return fmt.Sprintf("%d", v)
	case float32, float64:
		return fmt.Sprintf("%f", v)
	case bool:
		return fmt.Sprintf("%t", v)
	case time.Time:
		return v.Format(time.RFC3339)
	default:
		return fmt.Sprintf("%v", v)
	}
}

// toNumber 转换为数字
func toNumber(value interface{}) (float64, bool) {
	if value == nil {
		return 0, false
	}

	switch v := value.(type) {
	case int:
		return float64(v), true
	case int8:
		return float64(v), true
	case int16:
		return float64(v), true
	case int32:
		return float64(v), true
	case int64:
		return float64(v), true
	case uint:
		return float64(v), true
	case uint8:
		return float64(v), true
	case uint16:
		return float64(v), true
	case uint32:
		return float64(v), true
	case uint64:
		return float64(v), true
	case float32:
		return float64(v), true
	case float64:
		return v, true
	case string:
		if num, err := strconv.ParseFloat(v, 64); err == nil {
			return num, true
		}
		return 0, false
	default:
		return 0, false
	}
}
