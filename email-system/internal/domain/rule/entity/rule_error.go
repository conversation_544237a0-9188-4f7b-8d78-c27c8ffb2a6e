package entity

import "fmt"

// RuleErrorCode 规则错误码
type RuleErrorCode string

const (
	// 规则验证错误
	CodeRuleValidationError  RuleErrorCode = "RULE_VALIDATION_ERROR"
	CodeRuleOperatorInvalid  RuleErrorCode = "RULE_OPERATOR_INVALID"
	CodeRuleConditionInvalid RuleErrorCode = "RULE_CONDITION_INVALID"
	CodeRuleFieldInvalid     RuleErrorCode = "RULE_FIELD_INVALID"
	CodeRuleValueInvalid     RuleErrorCode = "RULE_VALUE_INVALID"
	CodeRuleStructureInvalid RuleErrorCode = "RULE_STRUCTURE_INVALID"

	// 规则执行错误
	CodeRuleExecutionError  RuleErrorCode = "RULE_EXECUTION_ERROR"
	CodeRuleEvaluationError RuleErrorCode = "RULE_EVALUATION_ERROR"
	CodeRuleContextError    RuleErrorCode = "RULE_CONTEXT_ERROR"
	CodeRuleTypeError       RuleErrorCode = "RULE_TYPE_ERROR"
	CodeRuleComparisonError RuleErrorCode = "RULE_COMPARISON_ERROR"

	// 规则解析错误
	CodeRuleParseError       RuleErrorCode = "RULE_PARSE_ERROR"
	CodeRuleSerializeError   RuleErrorCode = "RULE_SERIALIZE_ERROR"
	CodeRuleDeserializeError RuleErrorCode = "RULE_DESERIALIZE_ERROR"
	CodeRuleFormatError      RuleErrorCode = "RULE_FORMAT_ERROR"
)

// RuleError 规则错误
type RuleError struct {
	Code    RuleErrorCode          `json:"code"`
	Message string                 `json:"message"`
	Field   string                 `json:"field,omitempty"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// Error 实现error接口
func (e *RuleError) Error() string {
	if e.Field != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Code, e.Field, e.Message)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// NewRuleValidationError 创建规则验证错误
func NewRuleValidationError(field, message string) *RuleError {
	return &RuleError{
		Code:    CodeRuleValidationError,
		Message: message,
		Field:   field,
	}
}

// NewRuleOperatorInvalidError 创建规则操作符无效错误
func NewRuleOperatorInvalidError(operator string) *RuleError {
	return &RuleError{
		Code:    CodeRuleOperatorInvalid,
		Message: fmt.Sprintf("无效的规则操作符: %s", operator),
		Field:   "operator",
	}
}

// NewRuleConditionInvalidError 创建规则条件无效错误
func NewRuleConditionInvalidError(message string) *RuleError {
	return &RuleError{
		Code:    CodeRuleConditionInvalid,
		Message: message,
		Field:   "condition",
	}
}

// NewRuleFieldInvalidError 创建规则字段无效错误
func NewRuleFieldInvalidError(field, message string) *RuleError {
	return &RuleError{
		Code:    CodeRuleFieldInvalid,
		Message: message,
		Field:   field,
	}
}

// NewRuleValueInvalidError 创建规则值无效错误
func NewRuleValueInvalidError(message string) *RuleError {
	return &RuleError{
		Code:    CodeRuleValueInvalid,
		Message: message,
		Field:   "value",
	}
}

// NewRuleStructureInvalidError 创建规则结构无效错误
func NewRuleStructureInvalidError(message string) *RuleError {
	return &RuleError{
		Code:    CodeRuleStructureInvalid,
		Message: message,
	}
}

// NewRuleExecutionError 创建规则执行错误
func NewRuleExecutionError(message string) *RuleError {
	return &RuleError{
		Code:    CodeRuleExecutionError,
		Message: message,
	}
}

// NewRuleEvaluationError 创建规则评估错误
func NewRuleEvaluationError(message string) *RuleError {
	return &RuleError{
		Code:    CodeRuleEvaluationError,
		Message: message,
	}
}

// NewRuleContextError 创建规则上下文错误
func NewRuleContextError(message string) *RuleError {
	return &RuleError{
		Code:    CodeRuleContextError,
		Message: message,
	}
}

// NewRuleTypeError 创建规则类型错误
func NewRuleTypeError(expected, actual string) *RuleError {
	return &RuleError{
		Code:    CodeRuleTypeError,
		Message: fmt.Sprintf("类型不匹配，期望: %s，实际: %s", expected, actual),
	}
}

// NewRuleComparisonError 创建规则比较错误
func NewRuleComparisonError(message string) *RuleError {
	return &RuleError{
		Code:    CodeRuleComparisonError,
		Message: message,
	}
}

// NewRuleParseError 创建规则解析错误
func NewRuleParseError(message string) *RuleError {
	return &RuleError{
		Code:    CodeRuleParseError,
		Message: message,
	}
}

// NewRuleSerializeError 创建规则序列化错误
func NewRuleSerializeError(message string) *RuleError {
	return &RuleError{
		Code:    CodeRuleSerializeError,
		Message: message,
	}
}

// NewRuleDeserializeError 创建规则反序列化错误
func NewRuleDeserializeError(message string) *RuleError {
	return &RuleError{
		Code:    CodeRuleDeserializeError,
		Message: message,
	}
}

// NewRuleFormatError 创建规则格式错误
func NewRuleFormatError(message string) *RuleError {
	return &RuleError{
		Code:    CodeRuleFormatError,
		Message: message,
	}
}
