package service

import (
	"context"
	"encoding/json"
	"fmt"

	"gitee.com/heiyee/platforms/email-system/internal/domain/rule/entity"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// RuleEngine 规则引擎
type RuleEngine struct {
	logger logiface.Logger
}

// NewRuleEngine 创建规则引擎
func NewRuleEngine(logger logiface.Logger) *RuleEngine {
	return &RuleEngine{
		logger: logger,
	}
}

// ParseRule 解析规则
func (e *RuleEngine) ParseRule(ruleData map[string]interface{}) (*entity.Rule, error) {
	e.logger.Debug(context.Background(), "Parsing rule",
		logiface.Any("rule_data", ruleData))

	// 将map转换为JSON再解析为Rule结构
	jsonData, err := json.Marshal(ruleData)
	if err != nil {
		return nil, entity.NewRuleSerializeError(fmt.Sprintf("marshal rule data: %v", err))
	}

	var rule entity.Rule
	if err := json.Unmarshal(jsonData, &rule); err != nil {
		return nil, entity.NewRuleDeserializeError(fmt.Sprintf("unmarshal rule: %v", err))
	}

	// 验证规则
	if err := rule.Validate(); err != nil {
		return nil, err
	}

	return &rule, nil
}

// EvaluateRule 评估规则
func (e *RuleEngine) EvaluateRule(ctx context.Context, rule *entity.Rule, data map[string]interface{}) (bool, error) {
	e.logger.Debug(ctx, "Evaluating rule",
		logiface.String("operator", string(rule.Operator)),
		logiface.Int("conditions_count", len(rule.Conditions)),
		logiface.Int("rules_count", len(rule.Rules)))

	// 创建规则上下文
	ruleCtx := &entity.RuleContext{
		Data: data,
	}

	// 评估规则
	result, err := rule.Evaluate(ruleCtx)
	if err != nil {
		e.logger.Error(ctx, "Failed to evaluate rule",
			logiface.Error(err),
			logiface.String("operator", string(rule.Operator)))
		return false, err
	}

	e.logger.Debug(ctx, "Rule evaluation completed",
		logiface.Bool("result", result))

	return result, nil
}

// EvaluateRuleFromData 从数据直接评估规则
func (e *RuleEngine) EvaluateRuleFromData(ctx context.Context, ruleData map[string]interface{}, data map[string]interface{}) (bool, error) {
	// 解析规则
	rule, err := e.ParseRule(ruleData)
	if err != nil {
		return false, err
	}

	// 评估规则
	return e.EvaluateRule(ctx, rule, data)
}

// ValidateRule 验证规则
func (e *RuleEngine) ValidateRule(ruleData map[string]interface{}) error {
	e.logger.Debug(context.Background(), "Validating rule",
		logiface.Any("rule_data", ruleData))

	// 解析规则
	rule, err := e.ParseRule(ruleData)
	if err != nil {
		return err
	}

	// 验证规则
	return rule.Validate()
}

// BuildContactRule 构建联系人规则
func (e *RuleEngine) BuildContactRule(conditions []ContactCondition) (*entity.Rule, error) {
	if len(conditions) == 0 {
		return nil, entity.NewRuleValidationError("conditions", "条件列表不能为空")
	}

	rule := entity.NewRule(entity.OperatorAnd)

	for _, condition := range conditions {
		ruleCondition, err := e.buildContactCondition(condition)
		if err != nil {
			return nil, err
		}
		rule.AddCondition(ruleCondition)
	}

	return rule, nil
}

// buildContactCondition 构建联系人条件
func (e *RuleEngine) buildContactCondition(condition ContactCondition) (*entity.Condition, error) {
	// 验证字段名
	if !e.isValidContactField(condition.Field) {
		return nil, entity.NewRuleFieldInvalidError(condition.Field, "无效的联系人字段")
	}

	// 创建条件
	ruleCondition := entity.NewCondition(condition.Field, entity.RuleOperator(condition.Operator), condition.Value)

	// 验证条件
	if err := ruleCondition.Validate(); err != nil {
		return nil, err
	}

	return ruleCondition, nil
}

// isValidContactField 检查是否为有效的联系人字段
func (e *RuleEngine) isValidContactField(field string) bool {
	validFields := []string{
		"email", "status", "preferred_language", "country_code", "timezone",
		"consent_status", "created_at", "updated_at", "unsubscribed_at",
		"attributes.name", "attributes.age", "attributes.city", "attributes.gender",
		"attributes.total_amount", "attributes.last_login", "attributes.source",
	}

	for _, validField := range validFields {
		if field == validField {
			return true
		}
	}

	// 支持attributes下的任意字段
	if len(field) > 11 && field[:11] == "attributes." {
		return true
	}

	return false
}

// ContactCondition 联系人条件
type ContactCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

// RuleBuilder 规则构建器
type RuleBuilder struct {
	rule *entity.Rule
}

// NewRuleBuilder 创建规则构建器
func NewRuleBuilder(operator entity.RuleOperator) *RuleBuilder {
	return &RuleBuilder{
		rule: entity.NewRule(operator),
	}
}

// AddCondition 添加条件
func (b *RuleBuilder) AddCondition(field string, operator entity.RuleOperator, value interface{}) *RuleBuilder {
	condition := entity.NewCondition(field, operator, value)
	b.rule.AddCondition(condition)
	return b
}

// AddRule 添加子规则
func (b *RuleBuilder) AddRule(rule *entity.Rule) *RuleBuilder {
	b.rule.AddRule(rule)
	return b
}

// Build 构建规则
func (b *RuleBuilder) Build() (*entity.Rule, error) {
	if err := b.rule.Validate(); err != nil {
		return nil, err
	}
	return b.rule, nil
}

// RuleTemplate 规则模板
type RuleTemplate struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Rule        map[string]interface{} `json:"rule"`
	Fields      []TemplateField        `json:"fields"`
}

// TemplateField 模板字段
type TemplateField struct {
	Name         string      `json:"name"`
	Label        string      `json:"label"`
	Type         string      `json:"type"`
	Required     bool        `json:"required"`
	Options      []Option    `json:"options,omitempty"`
	DefaultValue interface{} `json:"default_value,omitempty"`
}

// Option 选项
type Option struct {
	Label string      `json:"label"`
	Value interface{} `json:"value"`
}

// GetCommonRuleTemplates 获取常用规则模板
func (e *RuleEngine) GetCommonRuleTemplates() []RuleTemplate {
	return []RuleTemplate{
		{
			Name:        "active_contacts",
			Description: "活跃联系人",
			Rule: map[string]interface{}{
				"operator": "and",
				"conditions": []map[string]interface{}{
					{
						"field":    "status",
						"operator": "eq",
						"value":    "active",
					},
					{
						"field":    "consent_status",
						"operator": "eq",
						"value":    "granted",
					},
				},
			},
		},
		{
			Name:        "vip_customers",
			Description: "VIP客户",
			Rule: map[string]interface{}{
				"operator": "and",
				"conditions": []map[string]interface{}{
					{
						"field":    "status",
						"operator": "eq",
						"value":    "active",
					},
					{
						"field":    "attributes.total_amount",
						"operator": "gte",
						"value":    10000,
					},
				},
			},
		},
		{
			Name:        "new_users",
			Description: "新用户（注册30天内）",
			Rule: map[string]interface{}{
				"operator": "and",
				"conditions": []map[string]interface{}{
					{
						"field":    "status",
						"operator": "eq",
						"value":    "active",
					},
					{
						"field":    "created_at",
						"operator": "within_days",
						"value":    30,
					},
				},
			},
		},
		{
			Name:        "beijing_users",
			Description: "北京用户",
			Rule: map[string]interface{}{
				"operator": "and",
				"conditions": []map[string]interface{}{
					{
						"field":    "status",
						"operator": "eq",
						"value":    "active",
					},
					{
						"field":    "attributes.city",
						"operator": "eq",
						"value":    "北京",
					},
				},
			},
		},
	}
}
