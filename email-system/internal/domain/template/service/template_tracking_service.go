package service

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/email-system/internal/domain/template/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/template/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// TemplateTrackingService 模板追踪服务
type TemplateTrackingService struct {
	logger                logiface.Logger
	templateLocaleRepo    repository.TemplateLocaleRepository
	defaultTrackingConfig *DefaultTrackingConfig
}

// DefaultTrackingConfig 默认追踪配置
type DefaultTrackingConfig struct {
	Enabled               bool
	UTMAutoAppend         bool
	UTMSource             string
	UTMMedium             string
	ConversionWindowHours int
	AttributionWindowHours int
	EnablePixel           bool
	EnableBeacon          bool
	EnableAMP             bool
}

// NewTemplateTrackingService 创建模板追踪服务
func NewTemplateTrackingService(
	logger logiface.Logger,
	templateLocaleRepo repository.TemplateLocaleRepository,
) *TemplateTrackingService {
	return &TemplateTrackingService{
		logger:             logger,
		templateLocaleRepo: templateLocaleRepo,
		defaultTrackingConfig: &DefaultTrackingConfig{
			Enabled:                true,
			UTMAutoAppend:          true,
			UTMSource:              "newsletter",
			UTMMedium:              "email", 
			ConversionWindowHours:  24,
			AttributionWindowHours: 24,
			EnablePixel:            true,
			EnableBeacon:           true,
			EnableAMP:              true,
		},
	}
}

// GetTrackingOptions 获取模板追踪配置（包含默认策略）
func (s *TemplateTrackingService) GetTrackingOptions(ctx context.Context, tenantID, templateID int64, locale string) (*entity.TrackingOptions, error) {
	s.logger.Debug(ctx, "Getting tracking options",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID),
		logiface.String("locale", locale))

	// 查找模板多语言版本
	templateLocale, err := s.templateLocaleRepo.FindByTemplateAndLocale(ctx, tenantID, templateID, locale)
	if err != nil {
		s.logger.Error(ctx, "Failed to find template locale",
			logiface.Error(err),
			logiface.Int64("template_id", templateID),
			logiface.String("locale", locale))
		return nil, err
	}

	if templateLocale == nil {
		s.logger.Debug(ctx, "Template locale not found, using default tracking config",
			logiface.Int64("template_id", templateID),
			logiface.String("locale", locale))
		return s.getDefaultTrackingOptions(), nil
	}

	// 获取配置（如果没有配置则使用默认值）
	options := templateLocale.GetTrackingOptionsWithDefaults()
	
	s.logger.Debug(ctx, "Retrieved tracking options",
		logiface.Int64("template_id", templateID),
		logiface.String("locale", locale),
		logiface.Bool("enabled", options.Enabled))

	return options, nil
}

// UpdateTrackingOptions 更新模板追踪配置
func (s *TemplateTrackingService) UpdateTrackingOptions(ctx context.Context, tenantID, templateID int64, locale string, options *entity.TrackingOptions) error {
	s.logger.Info(ctx, "Updating tracking options",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID),
		logiface.String("locale", locale),
		logiface.Bool("enabled", options.Enabled))

	// 验证配置
	if err := s.validateTrackingOptions(options); err != nil {
		s.logger.Error(ctx, "Invalid tracking options",
			logiface.Error(err),
			logiface.Int64("template_id", templateID))
		return err
	}

	// 更新配置
	err := s.templateLocaleRepo.UpdateTrackingOptions(ctx, tenantID, templateID, locale, options)
	if err != nil {
		s.logger.Error(ctx, "Failed to update tracking options",
			logiface.Error(err),
			logiface.Int64("template_id", templateID),
			logiface.String("locale", locale))
		return err
	}

	s.logger.Info(ctx, "Tracking options updated successfully",
		logiface.Int64("template_id", templateID),
		logiface.String("locale", locale))

	return nil
}

// ResetToDefaults 重置为默认追踪配置
func (s *TemplateTrackingService) ResetToDefaults(ctx context.Context, tenantID, templateID int64, locale string) error {
	s.logger.Info(ctx, "Resetting tracking options to defaults",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID),
		logiface.String("locale", locale))

	defaultOptions := s.getDefaultTrackingOptions()
	
	return s.UpdateTrackingOptions(ctx, tenantID, templateID, locale, defaultOptions)
}

// IsTrackingEnabled 检查是否启用追踪
func (s *TemplateTrackingService) IsTrackingEnabled(ctx context.Context, tenantID, templateID int64, locale string) (bool, error) {
	options, err := s.GetTrackingOptions(ctx, tenantID, templateID, locale)
	if err != nil {
		return false, err
	}
	
	return options.Enabled, nil
}

// GetUTMDefaults 获取UTM默认参数
func (s *TemplateTrackingService) GetUTMDefaults(ctx context.Context, tenantID, templateID int64, locale string) (*entity.UTMOptions, error) {
	options, err := s.GetTrackingOptions(ctx, tenantID, templateID, locale)
	if err != nil {
		return nil, err
	}
	
	if options.UTM == nil {
		return &entity.UTMOptions{
			AutoAppend: s.defaultTrackingConfig.UTMAutoAppend,
			Source:     s.defaultTrackingConfig.UTMSource,
			Medium:     s.defaultTrackingConfig.UTMMedium,
		}, nil
	}
	
	return options.UTM, nil
}

// GenerateTrackingDomain 生成追踪域名
func (s *TemplateTrackingService) GenerateTrackingDomain(ctx context.Context, tenantID int64) string {
	// 简单实现：track.{tenant_id}.example.com
	// 实际环境中应该根据租户配置和域名策略生成
	return fmt.Sprintf("track.tenant%d.example.com", tenantID)
}

// ValidateTrackingDomain 验证追踪域名
func (s *TemplateTrackingService) ValidateTrackingDomain(ctx context.Context, domain string) (*entity.DomainStatus, error) {
	s.logger.Debug(ctx, "Validating tracking domain",
		logiface.String("domain", domain))
	
	// TODO: 实际验证DNS记录、SSL证书等
	// 这里简化处理，返回已验证状态
	status := entity.DomainStatusVerified
	return &status, nil
}

// getDefaultTrackingOptions 获取默认追踪配置
func (s *TemplateTrackingService) getDefaultTrackingOptions() *entity.TrackingOptions {
	return &entity.TrackingOptions{
		Enabled: s.defaultTrackingConfig.Enabled,
		UTM: &entity.UTMOptions{
			AutoAppend: s.defaultTrackingConfig.UTMAutoAppend,
			Source:     s.defaultTrackingConfig.UTMSource,
			Medium:     s.defaultTrackingConfig.UTMMedium,
		},
		Attribution: &entity.AttributionOptions{
			WindowHours: s.defaultTrackingConfig.AttributionWindowHours,
		},
		Pixel: &entity.PixelOptions{
			EnablePixel:  s.defaultTrackingConfig.EnablePixel,
			EnableBeacon: s.defaultTrackingConfig.EnableBeacon,
			EnableAMP:    s.defaultTrackingConfig.EnableAMP,
		},
		ConversionWindowHours: s.defaultTrackingConfig.ConversionWindowHours,
	}
}

// validateTrackingOptions 验证追踪配置
func (s *TemplateTrackingService) validateTrackingOptions(options *entity.TrackingOptions) error {
	if options == nil {
		return fmt.Errorf("tracking options cannot be nil")
	}
	
	// 验证转化窗口
	if options.ConversionWindowHours < 0 || options.ConversionWindowHours > 720 { // 最大30天
		return fmt.Errorf("conversion window hours must be between 0 and 720")
	}
	
	// 验证归因窗口
	if options.Attribution != nil {
		if options.Attribution.WindowHours < 0 || options.Attribution.WindowHours > 720 {
			return fmt.Errorf("attribution window hours must be between 0 and 720")
		}
	}
	
	// 验证域名白名单
	if options.LinkDomainWhitelist != nil {
		for _, domain := range options.LinkDomainWhitelist {
			if domain == "" {
				return fmt.Errorf("domain whitelist cannot contain empty domains")
			}
		}
	}
	
	return nil
}

// GetTrackingConfiguration 获取完整的追踪配置信息
func (s *TemplateTrackingService) GetTrackingConfiguration(ctx context.Context, tenantID, templateID int64, locale string) (*TrackingConfiguration, error) {
	options, err := s.GetTrackingOptions(ctx, tenantID, templateID, locale)
	if err != nil {
		return nil, err
	}
	
	// 生成追踪域名
	trackingDomain := s.GenerateTrackingDomain(ctx, tenantID)
	if options.Domain != nil && options.Domain.TrackingDomain != "" {
		trackingDomain = options.Domain.TrackingDomain
	}
	
	return &TrackingConfiguration{
		Options:        options,
		TrackingDomain: trackingDomain,
		IsEnabled:      options.Enabled,
		DefaultLocale:  locale,
	}, nil
}

// TrackingConfiguration 追踪配置信息
type TrackingConfiguration struct {
	Options        *entity.TrackingOptions `json:"options"`
	TrackingDomain string                  `json:"tracking_domain"`
	IsEnabled      bool                    `json:"is_enabled"`
	DefaultLocale  string                  `json:"default_locale"`
}