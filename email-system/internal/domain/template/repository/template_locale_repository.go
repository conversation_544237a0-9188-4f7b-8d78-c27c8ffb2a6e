package repository

import (
	"context"

	"gitee.com/heiyee/platforms/email-system/internal/domain/template/entity"
)

// TemplateLocaleRepository 模板多语言仓储接口
type TemplateLocaleRepository interface {
	// Create 创建模板多语言版本
	Create(ctx context.Context, templateLocale *entity.TemplateLocale) error

	// Update 更新模板多语言版本
	Update(ctx context.Context, templateLocale *entity.TemplateLocale) error

	// Delete 删除模板多语言版本
	Delete(ctx context.Context, tenantID, templateID int64, locale string) error

	// FindByID 根据ID查找模板多语言版本
	FindByID(ctx context.Context, tenantID int64, id int64) (*entity.TemplateLocale, error)

	// FindByTemplateAndLocale 根据模板ID和语言查找
	FindByTemplateAndLocale(ctx context.Context, tenantID, templateID int64, locale string) (*entity.TemplateLocale, error)

	// FindByTemplateID 根据模板ID查找所有语言版本
	FindByTemplateID(ctx context.Context, tenantID, templateID int64) ([]*entity.TemplateLocale, error)

	// FindActiveByTemplateID 根据模板ID查找所有激活的语言版本
	FindActiveByTemplateID(ctx context.Context, tenantID, templateID int64) ([]*entity.TemplateLocale, error)

	// List 获取模板多语言版本列表
	List(ctx context.Context, params *ListParams) (*ListResult, error)

	// Count 统计模板多语言版本数量
	Count(ctx context.Context, tenantID int64, filters *ListFilters) (int64, error)

	// UpdateTrackingOptions 更新追踪配置
	UpdateTrackingOptions(ctx context.Context, tenantID, templateID int64, locale string, options *entity.TrackingOptions) error

	// GetDefaultLocale 获取默认语言版本
	GetDefaultLocale(ctx context.Context, tenantID, templateID int64) (*entity.TemplateLocale, error)

	// SetDefaultLocale 设置默认语言版本
	SetDefaultLocale(ctx context.Context, tenantID, templateID int64, locale string) error

	// BulkUpdateStatus 批量更新状态
	BulkUpdateStatus(ctx context.Context, tenantID int64, ids []int64, status entity.TemplateLocaleStatus) error

	// GetByStatus 根据状态获取模板多语言版本
	GetByStatus(ctx context.Context, tenantID int64, status entity.TemplateLocaleStatus, limit int) ([]*entity.TemplateLocale, error)
}

// ListParams 模板多语言版本列表查询参数
type ListParams struct {
	TenantID   int64        `json:"tenant_id"`
	TemplateID int64        `json:"template_id"`
	Filters    *ListFilters `json:"filters"`
	Sort       *SortOptions `json:"sort"`
	Page       int          `json:"page"`
	Size       int          `json:"size"`
	Offset     int          `json:"offset"`
	Limit      int          `json:"limit"`
}

// ListFilters 模板多语言版本列表过滤条件
type ListFilters struct {
	Locale          string                       `json:"locale"`
	Status          entity.TemplateLocaleStatus  `json:"status"`
	Name            string                       `json:"name"`
	TrackingEnabled *bool                       `json:"tracking_enabled"`
	CreatedAfter    *string                     `json:"created_after"`
	CreatedBefore   *string                     `json:"created_before"`
}

// SortOptions 排序选项
type SortOptions struct {
	Field string `json:"field"`
	Order string `json:"order"` // asc, desc
}

// ListResult 模板多语言版本列表结果
type ListResult struct {
	TemplateLocales []*entity.TemplateLocale `json:"template_locales"`
	Total           int64                    `json:"total"`
	Page            int                      `json:"page"`
	Size            int                      `json:"size"`
	TotalPages      int                      `json:"total_pages"`
}

// NewListParams 创建模板多语言版本列表查询参数
func NewListParams(tenantID int64) *ListParams {
	return &ListParams{
		TenantID: tenantID,
		Filters:  &ListFilters{},
		Sort: &SortOptions{
			Field: "created_at",
			Order: "desc",
		},
		Page: 1,
		Size: 20,
	}
}

// WithTemplateID 设置模板ID
func (p *ListParams) WithTemplateID(templateID int64) *ListParams {
	p.TemplateID = templateID
	return p
}

// WithFilters 设置过滤条件
func (p *ListParams) WithFilters(filters *ListFilters) *ListParams {
	p.Filters = filters
	return p
}

// WithSort 设置排序
func (p *ListParams) WithSort(field, order string) *ListParams {
	p.Sort = &SortOptions{
		Field: field,
		Order: order,
	}
	return p
}

// WithPagination 设置分页
func (p *ListParams) WithPagination(page, size int) *ListParams {
	p.Page = page
	p.Size = size
	return p
}

// WithOffset 设置偏移量分页
func (p *ListParams) WithOffset(offset, limit int) *ListParams {
	p.Offset = offset
	p.Limit = limit
	return p
}

// GetOffset 获取偏移量
func (p *ListParams) GetOffset() int {
	if p.Offset > 0 {
		return p.Offset
	}
	if p.Page > 0 && p.Size > 0 {
		return (p.Page - 1) * p.Size
	}
	return 0
}

// GetLimit 获取限制数量
func (p *ListParams) GetLimit() int {
	if p.Limit > 0 {
		return p.Limit
	}
	if p.Size > 0 {
		return p.Size
	}
	return 20
}