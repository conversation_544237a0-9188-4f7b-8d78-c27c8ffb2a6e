package entity

import "fmt"

// TemplateValidationError 模板验证错误
type TemplateValidationError struct {
	Field   string
	Message string
}

func (e *TemplateValidationError) Error() string {
	return fmt.Sprintf("模板验证错误 [%s]: %s", e.Field, e.Message)
}

// NewTemplateValidationError 创建模板验证错误
func NewTemplateValidationError(field, message string) error {
	return &TemplateValidationError{
		Field:   field,
		Message: message,
	}
}

// TemplateNotFoundError 模板未找到错误
type TemplateNotFoundError struct {
	TenantID   int64
	TemplateID int64
	Locale     string
}

func (e *TemplateNotFoundError) Error() string {
	if e.Locale != "" {
		return fmt.Sprintf("模板未找到: tenant_id=%d, template_id=%d, locale=%s", e.TenantID, e.TemplateID, e.Locale)
	}
	return fmt.Sprintf("模板未找到: tenant_id=%d, template_id=%d", e.TenantID, e.TemplateID)
}

// NewTemplateNotFoundError 创建模板未找到错误
func NewTemplateNotFoundError(tenantID, templateID int64, locale string) error {
	return &TemplateNotFoundError{
		TenantID:   tenantID,
		TemplateID: templateID,
		Locale:     locale,
	}
}

// TemplateRenderError 模板渲染错误
type TemplateRenderError struct {
	TemplateID int64
	Locale     string
	Message    string
	Cause      error
}

func (e *TemplateRenderError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("模板渲染错误 [template_id=%d, locale=%s]: %s, 原因: %v", 
			e.TemplateID, e.Locale, e.Message, e.Cause)
	}
	return fmt.Sprintf("模板渲染错误 [template_id=%d, locale=%s]: %s", 
		e.TemplateID, e.Locale, e.Message)
}

// Unwrap 返回底层错误
func (e *TemplateRenderError) Unwrap() error {
	return e.Cause
}

// NewTemplateRenderError 创建模板渲染错误
func NewTemplateRenderError(templateID int64, locale, message string, cause error) error {
	return &TemplateRenderError{
		TemplateID: templateID,
		Locale:     locale,
		Message:    message,
		Cause:      cause,
	}
}

// TemplateTrackingError 模板追踪错误
type TemplateTrackingError struct {
	TemplateID int64
	Locale     string
	Message    string
	Cause      error
}

func (e *TemplateTrackingError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("模板追踪错误 [template_id=%d, locale=%s]: %s, 原因: %v",
			e.TemplateID, e.Locale, e.Message, e.Cause)
	}
	return fmt.Sprintf("模板追踪错误 [template_id=%d, locale=%s]: %s",
		e.TemplateID, e.Locale, e.Message)
}

// Unwrap 返回底层错误
func (e *TemplateTrackingError) Unwrap() error {
	return e.Cause
}

// NewTemplateTrackingError 创建模板追踪错误
func NewTemplateTrackingError(templateID int64, locale, message string, cause error) error {
	return &TemplateTrackingError{
		TemplateID: templateID,
		Locale:     locale,
		Message:    message,
		Cause:      cause,
	}
}