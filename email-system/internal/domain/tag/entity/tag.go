package entity

import (
	"encoding/json"
	"time"
)

// Tag 标签实体
type Tag struct {
	ID            int64      `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID      int64      `json:"tenant_id" gorm:"not null;index:idx_tag_tenant"`
	Name          string     `json:"name" gorm:"not null;size:64;uniqueIndex:uk_tag_name"`
	Description   *string    `json:"description" gorm:"size:255"`
	Type          TagType    `json:"type" gorm:"not null;default:'rule_based';size:32"`
	Color         *string    `json:"color" gorm:"size:16"`
	RuleTreeJSON  *string    `json:"rule_tree_json" gorm:"type:json"`
	RefreshPolicy *string    `json:"refresh_policy" gorm:"size:64"`
	LastRefreshAt *time.Time `json:"last_refresh_at"`
	MemberCount   int64      `json:"member_count" gorm:"not null;default:0"`
	UsageCount    int64      `json:"usage_count" gorm:"not null;default:0"`
	CreatedBy     int64      `json:"created_by" gorm:"not null"`
	CreatedAt     time.Time  `json:"created_at" gorm:"not null;autoCreateTime"`
	UpdatedAt     time.Time  `json:"updated_at" gorm:"not null;autoUpdateTime"`
}

// TagType 标签类型
type TagType string

const (
	TagTypeRuleBased  TagType = "rule_based"  // 规则圈选
	TagTypeStaticList TagType = "static_list" // 静态列表
)

// IsValid 检查标签类型是否有效
func (t TagType) IsValid() bool {
	switch t {
	case TagTypeRuleBased, TagTypeStaticList:
		return true
	default:
		return false
	}
}

// RefreshPolicy 刷新策略
type RefreshPolicy string

const (
	RefreshPolicySchedule RefreshPolicy = "schedule" // 定时刷新
	RefreshPolicyTrigger  RefreshPolicy = "trigger"  // 触发刷新
	RefreshPolicyOnce     RefreshPolicy = "once"     // 一次性
)

// IsValidRefreshPolicy 检查刷新策略是否有效
func IsValidRefreshPolicy(policy RefreshPolicy) bool {
	switch policy {
	case RefreshPolicySchedule, RefreshPolicyTrigger, RefreshPolicyOnce:
		return true
	default:
		return false
	}
}

// TableName 指定表名
func (Tag) TableName() string {
	return "tags"
}

// GetRuleTree 获取规则树
func (t *Tag) GetRuleTree() (map[string]interface{}, error) {
	if t.RuleTreeJSON == nil || *t.RuleTreeJSON == "" {
		return nil, nil
	}

	var ruleTree map[string]interface{}
	if err := json.Unmarshal([]byte(*t.RuleTreeJSON), &ruleTree); err != nil {
		return nil, err
	}

	return ruleTree, nil
}

// SetRuleTree 设置规则树
func (t *Tag) SetRuleTree(ruleTree map[string]interface{}) error {
	if ruleTree == nil {
		t.RuleTreeJSON = nil
		return nil
	}

	data, err := json.Marshal(ruleTree)
	if err != nil {
		return err
	}

	ruleTreeJSON := string(data)
	t.RuleTreeJSON = &ruleTreeJSON
	return nil
}

// IsRuleBased 是否为规则圈选类型
func (t *Tag) IsRuleBased() bool {
	return t.Type == TagTypeRuleBased
}

// IsStaticList 是否为静态列表类型
func (t *Tag) IsStaticList() bool {
	return t.Type == TagTypeStaticList
}

// CanRefresh 是否可以刷新
func (t *Tag) CanRefresh() bool {
	return t.IsRuleBased() && t.RefreshPolicy != nil && *t.RefreshPolicy != string(RefreshPolicyOnce)
}

// IncrementUsage 增加使用次数
func (t *Tag) IncrementUsage() {
	t.UsageCount++
}

// UpdateMemberCount 更新成员数量
func (t *Tag) UpdateMemberCount(count int64) {
	t.MemberCount = count
}

// Clone 克隆标签实体
func (t *Tag) Clone() *Tag {
	clone := &Tag{
		ID:          t.ID,
		TenantID:    t.TenantID,
		Name:        t.Name,
		Type:        t.Type,
		MemberCount: t.MemberCount,
		UsageCount:  t.UsageCount,
		CreatedBy:   t.CreatedBy,
		CreatedAt:   t.CreatedAt,
		UpdatedAt:   t.UpdatedAt,
	}

	if t.Description != nil {
		description := *t.Description
		clone.Description = &description
	}

	if t.Color != nil {
		color := *t.Color
		clone.Color = &color
	}

	if t.RuleTreeJSON != nil {
		ruleTreeJSON := *t.RuleTreeJSON
		clone.RuleTreeJSON = &ruleTreeJSON
	}

	if t.RefreshPolicy != nil {
		refreshPolicy := *t.RefreshPolicy
		clone.RefreshPolicy = &refreshPolicy
	}

	if t.LastRefreshAt != nil {
		lastRefreshAt := *t.LastRefreshAt
		clone.LastRefreshAt = &lastRefreshAt
	}

	return clone
}

// ToJSON 转换为JSON字符串
func (t *Tag) ToJSON() (string, error) {
	data, err := json.Marshal(t)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// Validate 验证标签数据
func (t *Tag) Validate() error {
	if t.TenantID <= 0 {
		return NewTagValidationError("tenant_id", "租户ID不能为空")
	}

	if t.Name == "" {
		return NewTagValidationError("name", "标签名称不能为空")
	}

	if len(t.Name) > 64 {
		return NewTagValidationError("name", "标签名称长度不能超过64个字符")
	}

	if !t.Type.IsValid() {
		return NewTagValidationError("type", "无效的标签类型")
	}

	if t.CreatedBy <= 0 {
		return NewTagValidationError("created_by", "创建人不能为空")
	}

	// 规则圈选类型必须有规则树
	if t.Type == TagTypeRuleBased && (t.RuleTreeJSON == nil || *t.RuleTreeJSON == "") {
		return NewTagValidationError("rule_tree_json", "规则圈选类型必须设置规则树")
	}

	// 验证刷新策略
	if t.RefreshPolicy != nil && !IsValidRefreshPolicy(RefreshPolicy(*t.RefreshPolicy)) {
		return NewTagValidationError("refresh_policy", "无效的刷新策略")
	}

	return nil
}

// ContactTag 联系人标签关联实体
type ContactTag struct {
	ID        int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID  int64     `json:"tenant_id" gorm:"not null;index:idx_contact_tag_tenant"`
	ContactID int64     `json:"contact_id" gorm:"not null;uniqueIndex:uk_contact_tag"`
	TagID     int64     `json:"tag_id" gorm:"not null;uniqueIndex:uk_contact_tag"`
	AddedAt   time.Time `json:"added_at" gorm:"not null;autoCreateTime"`
}

// TableName 指定表名
func (ContactTag) TableName() string {
	return "contact_tags"
}

// Validate 验证联系人标签关联数据
func (ct *ContactTag) Validate() error {
	if ct.TenantID <= 0 {
		return NewTagValidationError("tenant_id", "租户ID不能为空")
	}

	if ct.ContactID <= 0 {
		return NewTagValidationError("contact_id", "联系人ID不能为空")
	}

	if ct.TagID <= 0 {
		return NewTagValidationError("tag_id", "标签ID不能为空")
	}

	return nil
}

// HasValidRule 检查是否有有效的规则
func (t *Tag) HasValidRule() bool {
	if t.Type != TagTypeRuleBased {
		return false
	}

	if t.RuleTreeJSON == nil || *t.RuleTreeJSON == "" {
		return false
	}

	// 尝试解析规则
	_, err := t.GetRuleTree()
	return err == nil
}

// ShouldAutoRefresh 检查是否应该自动刷新
func (t *Tag) ShouldAutoRefresh() bool {
	if !t.CanRefresh() {
		return false
	}

	// 检查刷新策略
	if t.RefreshPolicy == nil {
		return false
	}

	policy := RefreshPolicy(*t.RefreshPolicy)

	// 定时刷新策略需要检查上次刷新时间
	if policy == RefreshPolicySchedule {
		// 如果从未刷新过，需要刷新
		if t.LastRefreshAt == nil {
			return true
		}

		// 检查是否超过刷新间隔（这里假设为1小时）
		refreshInterval := time.Hour
		return time.Since(*t.LastRefreshAt) > refreshInterval
	}

	// 触发刷新策略不自动刷新
	return false
}

// MarkRefreshed 标记已刷新
func (t *Tag) MarkRefreshed() {
	now := time.Now()
	t.LastRefreshAt = &now
	t.UpdatedAt = now
}
