package entity

import "fmt"

// TagError 标签相关错误
type TagError struct {
	Code    string
	Message string
	Field   string
	Details string
}

func (e *TagError) Error() string {
	if e.Field != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Code, e.Field, e.Message)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// 错误码定义
const (
	// 标签验证错误 (120100-120119)
	CodeTagValidationError = "120100"
	CodeTagNameInvalid     = "120101"
	CodeTagNameExists      = "120102"
	CodeTagNotFound        = "120103"
	CodeTagTypeInvalid     = "120104"
	CodeTagColorInvalid    = "120105"
	CodeTagRuleInvalid     = "120106"
	CodeTagPolicyInvalid   = "120107"

	// 标签操作错误 (120120-120139)
	CodeTagCreateFailed  = "120120"
	CodeTagUpdateFailed  = "120121"
	CodeTagDeleteFailed  = "120122"
	CodeTagQueryFailed   = "120123"
	CodeTagRefreshFailed = "120124"
	CodeTagMergeFailed   = "120125"
	CodeTagRenameFailed  = "120126"

	// 标签赋值错误 (120140-120159)
	CodeTagAssignFailed        = "120140"
	CodeTagUnassignFailed      = "120141"
	CodeTagBatchAssignFailed   = "120142"
	CodeTagBatchUnassignFailed = "120143"
	CodeTagAssignmentExists    = "120144"
	CodeTagAssignmentNotFound  = "120145"

	// 标签规则错误 (120160-120179)
	CodeTagRuleParseError     = "120160"
	CodeTagRuleExecuteError   = "120161"
	CodeTagRuleValidateError  = "120162"
	CodeTagRuleConditionError = "120163"

	// 标签权限错误 (120180-120199)
	CodeTagPermissionDenied = "120180"
	CodeTagAccessDenied     = "120181"
	CodeTagOwnershipError   = "120182"
)

// NewTagValidationError 创建标签验证错误
func NewTagValidationError(field, message string) *TagError {
	return &TagError{
		Code:    CodeTagValidationError,
		Message: message,
		Field:   field,
	}
}

// NewTagNameInvalidError 创建标签名称无效错误
func NewTagNameInvalidError(name string) *TagError {
	return &TagError{
		Code:    CodeTagNameInvalid,
		Message: "标签名称格式不正确",
		Field:   "name",
		Details: name,
	}
}

// NewTagNameExistsError 创建标签名称已存在错误
func NewTagNameExistsError(name string) *TagError {
	return &TagError{
		Code:    CodeTagNameExists,
		Message: "标签名称已存在",
		Field:   "name",
		Details: name,
	}
}

// NewTagNotFoundError 创建标签不存在错误
func NewTagNotFoundError(identifier string) *TagError {
	return &TagError{
		Code:    CodeTagNotFound,
		Message: "标签不存在",
		Details: identifier,
	}
}

// NewTagTypeInvalidError 创建标签类型无效错误
func NewTagTypeInvalidError(tagType string) *TagError {
	return &TagError{
		Code:    CodeTagTypeInvalid,
		Message: "无效的标签类型",
		Field:   "type",
		Details: tagType,
	}
}

// NewTagColorInvalidError 创建标签颜色无效错误
func NewTagColorInvalidError(color string) *TagError {
	return &TagError{
		Code:    CodeTagColorInvalid,
		Message: "无效的标签颜色",
		Field:   "color",
		Details: color,
	}
}

// NewTagRuleInvalidError 创建标签规则无效错误
func NewTagRuleInvalidError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagRuleInvalid,
		Message: "标签规则格式无效",
		Field:   "rule_tree_json",
		Details: reason,
	}
}

// NewTagPolicyInvalidError 创建刷新策略无效错误
func NewTagPolicyInvalidError(policy string) *TagError {
	return &TagError{
		Code:    CodeTagPolicyInvalid,
		Message: "无效的刷新策略",
		Field:   "refresh_policy",
		Details: policy,
	}
}

// NewTagCreateFailedError 创建标签创建失败错误
func NewTagCreateFailedError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagCreateFailed,
		Message: "创建标签失败",
		Details: reason,
	}
}

// NewTagUpdateFailedError 创建标签更新失败错误
func NewTagUpdateFailedError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagUpdateFailed,
		Message: "更新标签失败",
		Details: reason,
	}
}

// NewTagDeleteFailedError 创建标签删除失败错误
func NewTagDeleteFailedError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagDeleteFailed,
		Message: "删除标签失败",
		Details: reason,
	}
}

// NewTagQueryFailedError 创建标签查询失败错误
func NewTagQueryFailedError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagQueryFailed,
		Message: "查询标签失败",
		Details: reason,
	}
}

// NewTagRefreshFailedError 创建标签刷新失败错误
func NewTagRefreshFailedError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagRefreshFailed,
		Message: "刷新标签失败",
		Details: reason,
	}
}

// NewTagMergeFailedError 创建标签合并失败错误
func NewTagMergeFailedError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagMergeFailed,
		Message: "合并标签失败",
		Details: reason,
	}
}

// NewTagRenameFailedError 创建标签重命名失败错误
func NewTagRenameFailedError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagRenameFailed,
		Message: "重命名标签失败",
		Details: reason,
	}
}

// NewTagAssignFailedError 创建标签赋值失败错误
func NewTagAssignFailedError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagAssignFailed,
		Message: "标签赋值失败",
		Details: reason,
	}
}

// NewTagUnassignFailedError 创建标签取消赋值失败错误
func NewTagUnassignFailedError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagUnassignFailed,
		Message: "取消标签赋值失败",
		Details: reason,
	}
}

// NewTagBatchAssignFailedError 创建批量标签赋值失败错误
func NewTagBatchAssignFailedError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagBatchAssignFailed,
		Message: "批量标签赋值失败",
		Details: reason,
	}
}

// NewTagBatchUnassignFailedError 创建批量标签取消赋值失败错误
func NewTagBatchUnassignFailedError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagBatchUnassignFailed,
		Message: "批量取消标签赋值失败",
		Details: reason,
	}
}

// NewTagAssignmentExistsError 创建标签赋值已存在错误
func NewTagAssignmentExistsError(contactID, tagID int64) *TagError {
	return &TagError{
		Code:    CodeTagAssignmentExists,
		Message: "标签赋值关系已存在",
		Details: fmt.Sprintf("contact_id: %d, tag_id: %d", contactID, tagID),
	}
}

// NewTagAssignmentNotFoundError 创建标签赋值不存在错误
func NewTagAssignmentNotFoundError(contactID, tagID int64) *TagError {
	return &TagError{
		Code:    CodeTagAssignmentNotFound,
		Message: "标签赋值关系不存在",
		Details: fmt.Sprintf("contact_id: %d, tag_id: %d", contactID, tagID),
	}
}

// NewTagRuleParseError 创建标签规则解析错误
func NewTagRuleParseError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagRuleParseError,
		Message: "标签规则解析失败",
		Details: reason,
	}
}

// NewTagRuleExecuteError 创建标签规则执行错误
func NewTagRuleExecuteError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagRuleExecuteError,
		Message: "标签规则执行失败",
		Details: reason,
	}
}

// NewTagRuleValidateError 创建标签规则验证错误
func NewTagRuleValidateError(reason string) *TagError {
	return &TagError{
		Code:    CodeTagRuleValidateError,
		Message: "标签规则验证失败",
		Details: reason,
	}
}

// NewTagRuleConditionError 创建标签规则条件错误
func NewTagRuleConditionError(condition, reason string) *TagError {
	return &TagError{
		Code:    CodeTagRuleConditionError,
		Message: fmt.Sprintf("标签规则条件错误: %s", condition),
		Details: reason,
	}
}

// NewTagPermissionDeniedError 创建标签权限拒绝错误
func NewTagPermissionDeniedError(action string) *TagError {
	return &TagError{
		Code:    CodeTagPermissionDenied,
		Message: "标签操作权限不足",
		Details: action,
	}
}

// NewTagAccessDeniedError 创建标签访问拒绝错误
func NewTagAccessDeniedError(tagID int64) *TagError {
	return &TagError{
		Code:    CodeTagAccessDenied,
		Message: "标签访问权限不足",
		Details: fmt.Sprintf("tag_id: %d", tagID),
	}
}

// NewTagOwnershipError 创建标签所有权错误
func NewTagOwnershipError(tagID int64) *TagError {
	return &TagError{
		Code:    CodeTagOwnershipError,
		Message: "标签所有权验证失败",
		Details: fmt.Sprintf("tag_id: %d", tagID),
	}
}
