package service

import (
	"context"
	"fmt"

	contactRepository "gitee.com/heiyee/platforms/email-system/internal/domain/contact/repository"
	ruleService "gitee.com/heiyee/platforms/email-system/internal/domain/rule/service"
	"gitee.com/heiyee/platforms/email-system/internal/domain/tag/entity"
	tagRepository "gitee.com/heiyee/platforms/email-system/internal/domain/tag/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// TagRuleService 标签规则服务
type TagRuleService struct {
	logger         logiface.Logger
	tagRepo        tagRepository.TagRepository
	contactTagRepo tagRepository.ContactTagRepository
	contactRepo    contactRepository.ContactRepository
	ruleEngine     *ruleService.RuleEngine
}

// NewTagRuleService 创建标签规则服务
func NewTagRuleService(
	logger logiface.Logger,
	tagRepo tagRepository.TagRepository,
	contactTagRepo tagRepository.ContactTagRepository,
	contactRepo contactRepository.ContactRepository,
	ruleEngine *ruleService.RuleEngine,
) *TagRuleService {
	return &TagRuleService{
		logger:         logger,
		tagRepo:        tagRepo,
		contactTagRepo: contactTagRepo,
		contactRepo:    contactRepo,
		ruleEngine:     ruleEngine,
	}
}

// ExecuteTagRule 执行标签规则
func (s *TagRuleService) ExecuteTagRule(ctx context.Context, tag *entity.Tag) ([]int64, error) {
	s.logger.Info(ctx, "Executing tag rule",
		logiface.Int64("tag_id", tag.ID),
		logiface.String("tag_name", tag.Name))

	// 检查标签类型
	if tag.Type != entity.TagTypeRuleBased {
		return nil, entity.NewTagRuleExecuteError("只有规则圈选类型的标签可以执行规则")
	}

	// 检查是否有有效规则
	if !tag.HasValidRule() {
		return nil, entity.NewTagRuleExecuteError("标签没有有效的规则")
	}

	// 获取规则树
	ruleTree, err := tag.GetRuleTree()
	if err != nil {
		s.logger.Error(ctx, "Failed to get rule tree",
			logiface.Error(err),
			logiface.Int64("tag_id", tag.ID))
	}

	// 解析规则
	rule, err := s.ruleEngine.ParseRule(ruleTree)
	if err != nil {
		s.logger.Error(ctx, "Failed to parse rule",
			logiface.Error(err),
			logiface.Int64("tag_id", tag.ID))
	}

	// 获取所有活跃联系人
	contacts, err := s.contactRepo.FindActiveContacts(ctx, tag.TenantID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find active contacts",
			logiface.Error(err),
			logiface.Int64("tenant_id", tag.TenantID))
	}

	s.logger.Info(ctx, "Found active contacts",
		logiface.Int("contact_count", len(contacts)))
	var matchedContactIDs []int64
	for _, contact := range contacts {
		// 构建联系人数据
		contactData := s.buildContactData(contact)

		// 评估规则
		matched, err := s.ruleEngine.EvaluateRule(ctx, rule, contactData)
		if err != nil {
			s.logger.Error(ctx, "Failed to evaluate rule for contact",
				logiface.Error(err),
				logiface.Int64("contact_id", contact.ID))
		}

		if matched {
			matchedContactIDs = append(matchedContactIDs, contact.ID)
		}
	}

	s.logger.Info(ctx, "Tag rule execution completed",
		logiface.Int64("tag_id", tag.ID),
		logiface.Int("total_contacts", len(contacts)))

	return matchedContactIDs, nil
}

// RefreshTagMembers 刷新标签成员
func (s *TagRuleService) RefreshTagMembers(ctx context.Context, tag *entity.Tag) error {
	s.logger.Info(ctx, "Refreshing tag members",
		logiface.Int64("tag_id", tag.ID),
		logiface.String("tag_name", tag.Name))
	// 检查是否可以刷新
	if !tag.CanRefresh() {
		return entity.NewTagRefreshFailedError("标签不支持刷新")
	}

	// 执行规则获取匹配的联系人
	matchedContactIDs, err := s.ExecuteTagRule(ctx, tag)
	if err != nil {
		return err
	}

	// 获取当前标签的所有联系人
	currentContactIDs, err := s.contactTagRepo.GetTagContactIDs(ctx, tag.TenantID, tag.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to get current tag contacts",
			logiface.Error(err),
			logiface.Int64("tag_id", tag.ID))
	}

	// 计算需要添加和移除的联系人
	toAdd, toRemove := s.calculateMemberChanges(currentContactIDs, matchedContactIDs)

	s.logger.Info(ctx, "Calculated member changes",
		logiface.Int64("tag_id", tag.ID),
		logiface.Int("to_add", len(toAdd)),
		logiface.Int("to_remove", len(toRemove)))

	// 移除不再匹配的联系人
	if len(toRemove) > 0 {
		if err := s.contactTagRepo.BatchUnassignTags(ctx, tag.TenantID, toRemove, []int64{tag.ID}); err != nil {
			s.logger.Error(ctx, "Failed to remove contacts from tag",
				logiface.Error(err),
				logiface.Int64("tag_id", tag.ID))
		}
	}

	// 添加新匹配的联系人
	if len(toAdd) > 0 {
		var contactTags []*entity.ContactTag
		for _, contactID := range toAdd {
			contactTag := &entity.ContactTag{
				TenantID:  tag.TenantID,
				ContactID: contactID,
				TagID:     tag.ID,
			}
			contactTags = append(contactTags, contactTag)
		}

		if err := s.contactTagRepo.BatchAssignTags(ctx, contactTags); err != nil {
			s.logger.Error(ctx, "Failed to add contacts to tag",
				logiface.Error(err),
				logiface.Int64("tag_id", tag.ID))
		}
	}

	// 更新标签成员数量
	newMemberCount := int64(len(matchedContactIDs))
	if err := s.tagRepo.UpdateMemberCount(ctx, tag.TenantID, tag.ID, newMemberCount); err != nil {
		s.logger.Error(ctx, "Failed to update tag member count",
			logiface.Error(err),
			logiface.Int64("tag_id", tag.ID))
	}

	// 标记标签已刷新
	tag.MarkRefreshed()
	if err := s.tagRepo.Update(ctx, tag); err != nil {
		s.logger.Error(ctx, "Failed to mark tag as refreshed",
			logiface.Error(err),
			logiface.Int64("tag_id", tag.ID))
	}

	s.logger.Info(ctx, "Tag members refreshed successfully",
		logiface.Int64("tag_id", tag.ID),
		logiface.Int64("member_count", newMemberCount))
	return nil
}

// ValidateTagRule 验证标签规则
func (s *TagRuleService) ValidateTagRule(ctx context.Context, ruleTree map[string]interface{}) error {
	s.logger.Debug(ctx, "Validating tag rule",
		logiface.Any("rule_tree", ruleTree))
	return s.ruleEngine.ValidateRule(ruleTree)
}

// PreviewTagRule 预览标签规则
func (s *TagRuleService) PreviewTagRule(ctx context.Context, tenantID int64, ruleTree map[string]interface{}, limit int) ([]int64, int64, error) {
	s.logger.Info(ctx, "Previewing tag rule",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("limit", limit))
	// 验证规则
	if err := s.ValidateTagRule(ctx, ruleTree); err != nil {
		return nil, 0, err
	}

	// 解析规则
	rule, err := s.ruleEngine.ParseRule(ruleTree)
	if err != nil {
		s.logger.Error(ctx, "Failed to parse rule for preview",
			logiface.Error(err))
		return nil, 0, entity.NewTagRuleParseError(fmt.Sprintf("parse rule: %v", err))
	}

	// 获取活跃联系人（限制数量以提高预览性能）
	previewLimit := limit
	if previewLimit <= 0 {
		previewLimit = 1000 // 默认预览1000个联系人
	}

	contacts, err := s.contactRepo.FindActiveContactsWithLimit(ctx, tenantID, previewLimit)
	if err != nil {
		s.logger.Error(ctx, "Failed to find active contacts for preview",
			logiface.Error(err))
		return nil, 0, entity.NewTagRuleExecuteError(fmt.Sprintf("find active contacts: %v", err))
	}

	// 评估规则
	var matchedContactIDs []int64
	totalEvaluated := int64(0)

	for _, contact := range contacts {
		totalEvaluated++

		// 构建联系人数据
		contactData := s.buildContactData(contact)

		// 评估规则
		matched, err := s.ruleEngine.EvaluateRule(ctx, rule, contactData)
		if err != nil {
			s.logger.Error(ctx, "Failed to evaluate rule for contact in preview",
				logiface.Error(err),
				logiface.Int64("contact_id", contact.ID))
		}

		if matched {
			matchedContactIDs = append(matchedContactIDs, contact.ID)

			// 如果达到预览限制，停止评估
			if len(matchedContactIDs) >= limit && limit > 0 {
				break
			}
		}
	}

	// 估算总匹配数量
	var estimatedTotal int64
	if totalEvaluated > 0 {
		matchRate := float64(len(matchedContactIDs)) / float64(totalEvaluated)
		totalContacts, _ := s.contactRepo.CountActiveContacts(ctx, tenantID)
		estimatedTotal = int64(float64(totalContacts) * matchRate)
	}

	s.logger.Info(ctx, "Tag rule preview completed",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("total_evaluated", totalEvaluated),
		logiface.Int64("estimated_total", estimatedTotal))

	return matchedContactIDs, estimatedTotal, nil
}

// buildContactData 构建联系人数据用于规则评估
func (s *TagRuleService) buildContactData(contact interface{}) map[string]interface{} {
	// 这里需要根据实际的联系人实体结构来构建数据
	// 假设contact是一个包含所有字段的结构体或map

	data := make(map[string]interface{})

	// 使用反射或类型断言来提取联系人数据
	// 这里简化处理，实际应用中需要根据具体的联系人实体来实现
	if contactMap, ok := contact.(map[string]interface{}); ok {
		return contactMap
	}

	// 如果是联系人实体，需要转换为map
	// 这里需要根据实际的联系人实体结构来实现
	// 例如：
	// if c, ok := contact.(*contactEntity.Contact); ok {
	//     data["id"] = c.ID
	//     data["email"] = c.Email
	//     data["status"] = string(c.Status)
	//     data["preferred_language"] = c.PreferredLanguage
	//     data["country_code"] = c.CountryCode
	//     data["timezone"] = c.Timezone
	//     data["consent_status"] = string(c.ConsentStatus)
	//     data["created_at"] = c.CreatedAt
	//     data["updated_at"] = c.UpdatedAt
	//     data["unsubscribed_at"] = c.UnsubscribedAt
	//     data["attributes"] = c.Attributes
	// }

	return data
}

// calculateMemberChanges 计算成员变更
func (s *TagRuleService) calculateMemberChanges(current, target []int64) (toAdd, toRemove []int64) {
	// 创建映射以提高查找效率
	currentMap := make(map[int64]bool)
	for _, id := range current {
		currentMap[id] = true
	}

	targetMap := make(map[int64]bool)
	for _, id := range target {
		targetMap[id] = true
	}

	// 找出需要添加的联系人（在target中但不在current中）
	for _, id := range target {
		if !currentMap[id] {
			toAdd = append(toAdd, id)
		}
	}

	// 找出需要移除的联系人（在current中但不在target中）
	for _, id := range current {
		if !targetMap[id] {
			toRemove = append(toRemove, id)
		}
	}

	return toAdd, toRemove
}

// RefreshAllRuleBasedTags 刷新所有规则圈选标签
func (s *TagRuleService) RefreshAllRuleBasedTags(ctx context.Context, tenantID int64) error {
	s.logger.Info(ctx, "Refreshing all rule-based tags",
		logiface.Int64("tenant_id", tenantID))

	// 获取所有规则圈选类型的标签
	tags, err := s.tagRepo.GetTagsByType(ctx, tenantID, entity.TagTypeRuleBased)
	if err != nil {
		s.logger.Error(ctx, "Failed to get rule-based tags",
			logiface.Error(err))
		return entity.NewTagQueryFailedError(fmt.Sprintf("get rule-based tags: %v", err))
	}

	s.logger.Info(ctx, "Found rule-based tags",
		logiface.Int("tag_count", len(tags)))

	var refreshedCount int
	var failedCount int

	for _, tag := range tags {
		// 检查是否应该刷新
		if !tag.ShouldAutoRefresh() {
			s.logger.Debug(ctx, "Skipping tag refresh",
				logiface.Int64("tag_id", tag.ID),
				logiface.String("reason", "should not auto refresh"))
			continue
		}

		// 刷新标签成员
		if err := s.RefreshTagMembers(ctx, tag); err != nil {
			s.logger.Error(ctx, "Failed to refresh tag members",
				logiface.Error(err),
				logiface.Int64("tag_id", tag.ID))
			failedCount++
			continue
		}

		refreshedCount++
	}

	s.logger.Info(ctx, "Completed refreshing rule-based tags",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("total_tags", len(tags)),
		logiface.Int("refreshed_count", refreshedCount),
		logiface.Int("failed_count", failedCount))

	return nil
}

// GetTagRuleFields 获取标签规则可用字段
func (s *TagRuleService) GetTagRuleFields() []TagRuleField {
	return []TagRuleField{
		{
			Name:        "email",
			Label:       "邮箱地址",
			Type:        "string",
			Description: "联系人的邮箱地址",
			Operators:   []string{"eq", "ne", "contains", "starts_with", "ends_with", "regex"},
		},
		{
			Name:        "status",
			Label:       "状态",
			Type:        "string",
			Description: "联系人状态",
			Operators:   []string{"eq", "ne", "in", "not_in"},
			Options: []FieldOption{
				{Label: "活跃", Value: "active"},
				{Label: "被抑制", Value: "suppressed"},
				{Label: "未确认", Value: "unconfirmed"},
				{Label: "退信", Value: "bounced"},
				{Label: "投诉", Value: "complained"},
				{Label: "已退订", Value: "unsubscribed"},
			},
		},
		{
			Name:        "preferred_language",
			Label:       "首选语言",
			Type:        "string",
			Description: "联系人的首选语言",
			Operators:   []string{"eq", "ne", "in", "not_in"},
			Options: []FieldOption{
				{Label: "中文", Value: "zh-CN"},
				{Label: "英文", Value: "en-US"},
				{Label: "日文", Value: "ja-JP"},
			},
		},
		{
			Name:        "country_code",
			Label:       "国家代码",
			Type:        "string",
			Description: "联系人所在国家的代码",
			Operators:   []string{"eq", "ne", "in", "not_in"},
		},
		{
			Name:        "consent_status",
			Label:       "同意状态",
			Type:        "string",
			Description: "联系人的同意状态",
			Operators:   []string{"eq", "ne", "in", "not_in"},
			Options: []FieldOption{
				{Label: "未知", Value: "unknown"},
				{Label: "已同意", Value: "granted"},
				{Label: "已撤回", Value: "withdrawn"},
				{Label: "待确认", Value: "pending"},
			},
		},
		{
			Name:        "created_at",
			Label:       "创建时间",
			Type:        "datetime",
			Description: "联系人的创建时间",
			Operators:   []string{"eq", "ne", "gt", "gte", "lt", "lte", "before", "after", "within_days", "older_than"},
		},
		{
			Name:        "updated_at",
			Label:       "更新时间",
			Type:        "datetime",
			Description: "联系人的最后更新时间",
			Operators:   []string{"eq", "ne", "gt", "gte", "lt", "lte", "before", "after", "within_days", "older_than"},
		},
		{
			Name:        "attributes.name",
			Label:       "姓名",
			Type:        "string",
			Description: "联系人的姓名",
			Operators:   []string{"eq", "ne", "contains", "starts_with", "ends_with", "exists", "not_exists"},
		},
		{
			Name:        "attributes.age",
			Label:       "年龄",
			Type:        "number",
			Description: "联系人的年龄",
			Operators:   []string{"eq", "ne", "gt", "gte", "lt", "lte"},
		},
		{
			Name:        "attributes.city",
			Label:       "城市",
			Type:        "string",
			Description: "联系人所在城市",
			Operators:   []string{"eq", "ne", "contains", "in", "not_in", "exists", "not_exists"},
		},
		{
			Name:        "attributes.total_amount",
			Label:       "总消费金额",
			Type:        "number",
			Description: "联系人的总消费金额",
			Operators:   []string{"eq", "ne", "gt", "gte", "lt", "lte"},
		},
	}
}

// TagRuleField 标签规则字段
type TagRuleField struct {
	Name        string        `json:"name"`
	Label       string        `json:"label"`
	Type        string        `json:"type"`
	Description string        `json:"description"`
	Operators   []string      `json:"operators"`
	Options     []FieldOption `json:"options,omitempty"`
}

// FieldOption 字段选项
type FieldOption struct {
	Label string      `json:"label"`
	Value interface{} `json:"value"`
}
