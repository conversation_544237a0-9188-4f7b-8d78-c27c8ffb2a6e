package repository

import (
	"context"

	"gitee.com/heiyee/platforms/email-system/internal/domain/tag/entity"
)

// TagRepository 标签仓储接口
type TagRepository interface {
	// Create 创建标签
	Create(ctx context.Context, tag *entity.Tag) error

	// Update 更新标签
	Update(ctx context.Context, tag *entity.Tag) error

	// Delete 删除标签
	Delete(ctx context.Context, tenantID, tagID int64) error

	// FindByID 根据ID查找标签
	FindByID(ctx context.Context, tenantID, tagID int64) (*entity.Tag, error)

	// FindByName 根据名称查找标签
	FindByName(ctx context.Context, tenantID int64, name string) (*entity.Tag, error)

	// FindByIDs 根据ID列表批量查找标签
	FindByIDs(ctx context.Context, tenantID int64, tagIDs []int64) ([]*entity.Tag, error)

	// List 获取标签列表
	List(ctx context.Context, params *ListParams) (*ListResult, error)

	// Count 统计标签数量
	Count(ctx context.Context, tenantID int64, filters *ListFilters) (int64, error)

	// ExistsByName 检查标签名称是否存在
	ExistsByName(ctx context.Context, tenantID int64, name string) (bool, error)

	// UpdateMemberCount 更新标签成员数量
	UpdateMemberCount(ctx context.Context, tenantID, tagID int64, count int64) error

	// IncrementUsageCount 增加标签使用次数
	IncrementUsageCount(ctx context.Context, tenantID, tagID int64) error

	// GetPopularTags 获取热门标签
	GetPopularTags(ctx context.Context, tenantID int64, limit int) ([]*entity.Tag, error)

	// GetRecentTags 获取最近使用的标签
	GetRecentTags(ctx context.Context, tenantID int64, limit int) ([]*entity.Tag, error)

	// GetTagsByType 根据类型获取标签
	GetTagsByType(ctx context.Context, tenantID int64, tagType entity.TagType) ([]*entity.Tag, error)
}

// ContactTagRepository 联系人标签关联仓储接口
type ContactTagRepository interface {
	// AssignTag 为联系人分配标签
	AssignTag(ctx context.Context, contactTag *entity.ContactTag) error

	// UnassignTag 取消联系人标签分配
	UnassignTag(ctx context.Context, tenantID, contactID, tagID int64) error

	// BatchAssignTags 批量为联系人分配标签
	BatchAssignTags(ctx context.Context, contactTags []*entity.ContactTag) error

	// BatchUnassignTags 批量取消联系人标签分配
	BatchUnassignTags(ctx context.Context, tenantID int64, contactIDs []int64, tagIDs []int64) error

	// GetContactTags 获取联系人的标签列表
	GetContactTags(ctx context.Context, tenantID, contactID int64) ([]*entity.Tag, error)

	// GetTagContacts 获取标签下的联系人ID列表
	GetTagContacts(ctx context.Context, tenantID, tagID int64, params *ContactListParams) (*ContactListResult, error)

	// CountTagContacts 统计标签下的联系人数量
	CountTagContacts(ctx context.Context, tenantID, tagID int64) (int64, error)

	// ExistsAssignment 检查联系人标签分配是否存在
	ExistsAssignment(ctx context.Context, tenantID, contactID, tagID int64) (bool, error)

	// GetContactsByTags 根据标签获取联系人ID列表
	GetContactsByTags(ctx context.Context, tenantID int64, tagIDs []int64, operator TagOperator) ([]int64, error)

	// RemoveContactFromAllTags 从所有标签中移除联系人
	RemoveContactFromAllTags(ctx context.Context, tenantID, contactID int64) error

	// GetTagContactIDs 获取标签下的联系人ID列表
	GetTagContactIDs(ctx context.Context, tenantID, tagID int64) ([]int64, error)
}

// ListParams 标签列表查询参数
type ListParams struct {
	TenantID int64        `json:"tenant_id"`
	Filters  *ListFilters `json:"filters"`
	Sort     *SortOptions `json:"sort"`
	Page     int          `json:"page"`
	Size     int          `json:"size"`
	Offset   int          `json:"offset"`
	Limit    int          `json:"limit"`
}

// ListFilters 标签列表过滤条件
type ListFilters struct {
	Name           string         `json:"name"`
	Type           entity.TagType `json:"type"`
	Color          string         `json:"color"`
	CreatedBy      int64          `json:"created_by"`
	Keyword        string         `json:"keyword"`
	UsageMin       int64          `json:"usage_min"`
	UsageMax       int64          `json:"usage_max"`
	MemberMin      int64          `json:"member_min"`
	MemberMax      int64          `json:"member_max"`
	CreatedAtRange *TimeRange     `json:"created_at_range"`
	UpdatedAtRange *TimeRange     `json:"updated_at_range"`
}

// TimeRange 时间范围
type TimeRange struct {
	Start *string `json:"start"`
	End   *string `json:"end"`
}

// SortOptions 排序选项
type SortOptions struct {
	Field string `json:"field"`
	Order string `json:"order"` // asc, desc
}

// ListResult 标签列表结果
type ListResult struct {
	Tags       []*entity.Tag `json:"tags"`
	Total      int64         `json:"total"`
	Page       int           `json:"page"`
	Size       int           `json:"size"`
	TotalPages int           `json:"total_pages"`
}

// ContactListParams 联系人列表查询参数
type ContactListParams struct {
	Page   int `json:"page"`
	Size   int `json:"size"`
	Offset int `json:"offset"`
	Limit  int `json:"limit"`
}

// ContactListResult 联系人列表结果
type ContactListResult struct {
	ContactIDs []int64 `json:"contact_ids"`
	Total      int64   `json:"total"`
	Page       int     `json:"page"`
	Size       int     `json:"size"`
	TotalPages int     `json:"total_pages"`
}

// TagOperator 标签操作符
type TagOperator string

const (
	TagOperatorAnd TagOperator = "and" // 交集
	TagOperatorOr  TagOperator = "or"  // 并集
)

// NewListParams 创建标签列表查询参数
func NewListParams(tenantID int64) *ListParams {
	return &ListParams{
		TenantID: tenantID,
		Filters:  &ListFilters{},
		Sort: &SortOptions{
			Field: "created_at",
			Order: "desc",
		},
		Page: 1,
		Size: 20,
	}
}

// WithFilters 设置过滤条件
func (p *ListParams) WithFilters(filters *ListFilters) *ListParams {
	p.Filters = filters
	return p
}

// WithSort 设置排序
func (p *ListParams) WithSort(field, order string) *ListParams {
	p.Sort = &SortOptions{
		Field: field,
		Order: order,
	}
	return p
}

// WithPagination 设置分页
func (p *ListParams) WithPagination(page, size int) *ListParams {
	p.Page = page
	p.Size = size
	return p
}

// WithOffset 设置偏移量分页
func (p *ListParams) WithOffset(offset, limit int) *ListParams {
	p.Offset = offset
	p.Limit = limit
	return p
}

// GetOffset 获取偏移量
func (p *ListParams) GetOffset() int {
	if p.Offset > 0 {
		return p.Offset
	}
	if p.Page > 0 && p.Size > 0 {
		return (p.Page - 1) * p.Size
	}
	return 0
}

// GetLimit 获取限制数量
func (p *ListParams) GetLimit() int {
	if p.Limit > 0 {
		return p.Limit
	}
	if p.Size > 0 {
		return p.Size
	}
	return 20
}

// NewContactListParams 创建联系人列表查询参数
func NewContactListParams() *ContactListParams {
	return &ContactListParams{
		Page: 1,
		Size: 20,
	}
}

// GetOffset 获取偏移量
func (p *ContactListParams) GetOffset() int {
	if p.Offset > 0 {
		return p.Offset
	}
	if p.Page > 0 && p.Size > 0 {
		return (p.Page - 1) * p.Size
	}
	return 0
}

// GetLimit 获取限制数量
func (p *ContactListParams) GetLimit() int {
	if p.Limit > 0 {
		return p.Limit
	}
	if p.Size > 0 {
		return p.Size
	}
	return 20
}
