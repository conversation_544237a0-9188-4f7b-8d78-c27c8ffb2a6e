package service

import (
	"net"
	"regexp"
	"strings"
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/tracking/entity"
)

// EventClassifier 事件分类器
type EventClassifier struct {
	mppDetector     *MPPDetector
	proxyDetector   *ProxyDetector
	scannerDetector *ScannerDetector
}

// NewEventClassifier 创建事件分类器
func NewEventClassifier() *EventClassifier {
	return &EventClassifier{
		mppDetector:     NewMPPDetector(),
		proxyDetector:   NewProxyDetector(),
		scannerDetector: NewScannerDetector(),
	}
}

// ClassifyOpenEvent 分类打开事件
func (c *EventClassifier) ClassifyOpenEvent(event *entity.TrackingEvent) entity.OpenClassification {
	// 1. 检测Apple MPP
	if c.mppDetector.IsMPP(event.UserAgent, event.IPAddress, event.Headers) {
		return entity.OpenClassMPP
	}

	// 2. 检测Gmail代理
	if c.proxyDetector.IsProxy(event.UserAgent, event.IPAddress, event.Headers) {
		return entity.OpenClassProxy
	}

	// 3. 检测安全扫描器
	if c.scannerDetector.IsScanner(event.UserAgent, event.IPAddress, event.Headers, event.OccurredAt) {
		return entity.OpenClassScanner
	}

	// 4. 默认为人类打开
	return entity.OpenClassHuman
}

// MPPDetector Apple Mail Privacy Protection检测器
type MPPDetector struct {
	appleIPRanges   []IPRange
	appleUAPatterns []*regexp.Regexp
}

// IPRange IP地址范围
type IPRange struct {
	CIDR string
	Net  *net.IPNet
}

// NewMPPDetector 创建MPP检测器
func NewMPPDetector() *MPPDetector {
	detector := &MPPDetector{
		appleIPRanges:   []IPRange{},
		appleUAPatterns: []*regexp.Regexp{},
	}

	// 初始化Apple IP范围（这些需要定期更新）
	appleIPRanges := []string{
		"********/8",
		"********/8",
		"************/24",
		"2620:149::/32",
	}

	for _, cidr := range appleIPRanges {
		_, net, err := net.ParseCIDR(cidr)
		if err == nil {
			detector.appleIPRanges = append(detector.appleIPRanges, IPRange{
				CIDR: cidr,
				Net:  net,
			})
		}
	}

	// 初始化UA模式
	uaPatterns := []string{
		`(?i)AppleWebKit.*Mail/`,
		`(?i)Mail/\d+\.\d+.*AppleWebKit`,
		`(?i)AppleMail/\d+`,
	}

	for _, pattern := range uaPatterns {
		if regex, err := regexp.Compile(pattern); err == nil {
			detector.appleUAPatterns = append(detector.appleUAPatterns, regex)
		}
	}

	return detector
}

// IsMPP 检测是否为Apple MPP
func (d *MPPDetector) IsMPP(userAgent, ipAddress string, headers map[string]string) bool {
	// 1. 检查IP地址是否在Apple范围内
	if d.isAppleIPRange(ipAddress) {
		return true
	}

	// 2. 检查User-Agent模式
	if d.hasAppleUserAgent(userAgent) {
		return true
	}

	// 3. 检查特征头部
	if d.hasAppleHeaders(headers) {
		return true
	}

	// 4. 检查批量模式（同一时间大量请求）
	if d.isBatchPattern(headers) {
		return true
	}

	return false
}

func (d *MPPDetector) isAppleIPRange(ipAddress string) bool {
	ip := net.ParseIP(ipAddress)
	if ip == nil {
		return false
	}

	for _, ipRange := range d.appleIPRanges {
		if ipRange.Net.Contains(ip) {
			return true
		}
	}

	return false
}

func (d *MPPDetector) hasAppleUserAgent(userAgent string) bool {
	for _, pattern := range d.appleUAPatterns {
		if pattern.MatchString(userAgent) {
			return true
		}
	}
	return false
}

func (d *MPPDetector) hasAppleHeaders(headers map[string]string) bool {
	// 检查特征性头部
	if xForwardedFor, ok := headers["x-forwarded-for"]; ok {
		// Apple代理服务器的特征
		if strings.Contains(xForwardedFor, "apple") ||
			strings.Contains(xForwardedFor, "icloud") {
			return true
		}
	}

	// 检查Accept头部
	if accept, ok := headers["accept"]; ok {
		// MPP通常只接受图片
		if strings.Contains(accept, "image/*") && !strings.Contains(accept, "text/html") {
			return true
		}
	}

	return false
}

func (d *MPPDetector) isBatchPattern(headers map[string]string) bool {
	// 检查是否缺少典型的浏览器头部
	typicalHeaders := []string{"accept-language", "accept-encoding", "cache-control"}
	missingCount := 0

	for _, header := range typicalHeaders {
		if _, ok := headers[header]; !ok {
			missingCount++
		}
	}

	// 如果缺少多个典型头部，可能是批量请求
	return missingCount >= 2
}

// ProxyDetector 代理检测器
type ProxyDetector struct {
	proxyPatterns []*regexp.Regexp
	proxyIPRanges []IPRange
}

// NewProxyDetector 创建代理检测器
func NewProxyDetector() *ProxyDetector {
	detector := &ProxyDetector{
		proxyPatterns: []*regexp.Regexp{},
		proxyIPRanges: []IPRange{},
	}

	// Gmail代理的特征模式
	patterns := []string{
		`(?i)googleimageproxy`,
		`(?i)gmail.*proxy`,
		`(?i)google.*cache`,
	}

	for _, pattern := range patterns {
		if regex, err := regexp.Compile(pattern); err == nil {
			detector.proxyPatterns = append(detector.proxyPatterns, regex)
		}
	}

	// Google IP范围
	googleIPRanges := []string{
		"*******/24",
		"*******/24",
		"***********/16",
		"***********/16",
	}

	for _, cidr := range googleIPRanges {
		_, net, err := net.ParseCIDR(cidr)
		if err == nil {
			detector.proxyIPRanges = append(detector.proxyIPRanges, IPRange{
				CIDR: cidr,
				Net:  net,
			})
		}
	}

	return detector
}

// IsProxy 检测是否为代理
func (d *ProxyDetector) IsProxy(userAgent, ipAddress string, headers map[string]string) bool {
	// 1. 检查User-Agent中的代理标识
	for _, pattern := range d.proxyPatterns {
		if pattern.MatchString(userAgent) {
			return true
		}
	}

	// 2. 检查Via头部
	if via, ok := headers["via"]; ok && via != "" {
		return true
	}

	// 3. 检查X-Forwarded-For头部
	if xForwardedFor, ok := headers["x-forwarded-for"]; ok {
		if strings.Contains(strings.ToLower(xForwardedFor), "google") ||
			strings.Contains(strings.ToLower(xForwardedFor), "proxy") {
			return true
		}
	}

	// 4. 检查IP范围
	ip := net.ParseIP(ipAddress)
	if ip != nil {
		for _, ipRange := range d.proxyIPRanges {
			if ipRange.Net.Contains(ip) {
				return true
			}
		}
	}

	return false
}

// ScannerDetector 扫描器检测器
type ScannerDetector struct {
	scannerPatterns []*regexp.Regexp
	datacenterASNs  map[int]bool
}

// NewScannerDetector 创建扫描器检测器
func NewScannerDetector() *ScannerDetector {
	detector := &ScannerDetector{
		scannerPatterns: []*regexp.Regexp{},
		datacenterASNs:  make(map[int]bool),
	}

	// 扫描器特征模式
	patterns := []string{
		`(?i)bot\b`,
		`(?i)crawler`,
		`(?i)scanner`,
		`(?i)security`,
		`(?i)antivirus`,
		`(?i)firewall`,
		`(?i)curl`,
		`(?i)wget`,
		`(?i)python-requests`,
	}

	for _, pattern := range patterns {
		if regex, err := regexp.Compile(pattern); err == nil {
			detector.scannerPatterns = append(detector.scannerPatterns, regex)
		}
	}

	// 常见数据中心ASN
	datacenterASNs := []int{
		13335, // Cloudflare
		14618, // Amazon
		15169, // Google
		16509, // AWS
		8075,  // Microsoft
	}

	for _, asn := range datacenterASNs {
		detector.datacenterASNs[asn] = true
	}

	return detector
}

// IsScanner 检测是否为扫描器
func (d *ScannerDetector) IsScanner(userAgent, ipAddress string, headers map[string]string, occurredAt time.Time) bool {
	// 1. 检查User-Agent特征
	for _, pattern := range d.scannerPatterns {
		if pattern.MatchString(userAgent) {
			return true
		}
	}

	// 2. 检查缺少浏览器头部
	if d.lacksNormalHeaders(headers) {
		return true
	}

	// 3. 检查时间模式（投递后很短时间内的访问）
	if d.isSuspiciousTiming(occurredAt) {
		return true
	}

	// 4. 检查Referer缺失（正常邮件客户端通常有Referer）
	if _, ok := headers["referer"]; !ok {
		// 缺少Referer且有其他可疑特征
		if d.lacksNormalHeaders(headers) || len(userAgent) < 20 {
			return true
		}
	}

	return false
}

func (d *ScannerDetector) lacksNormalHeaders(headers map[string]string) bool {
	normalHeaders := []string{
		"accept-language",
		"accept-encoding",
		"accept",
		"connection",
	}

	missingCount := 0
	for _, header := range normalHeaders {
		if _, ok := headers[header]; !ok {
			missingCount++
		}
	}

	// 如果缺少超过一半的正常头部，可能是扫描器
	return missingCount > len(normalHeaders)/2
}

func (d *ScannerDetector) isSuspiciousTiming(occurredAt time.Time) bool {
	// 这里需要结合业务逻辑，检查是否在邮件发送后很短时间内
	// 暂时返回false，实际实现需要查询邮件发送时间
	return false
}
