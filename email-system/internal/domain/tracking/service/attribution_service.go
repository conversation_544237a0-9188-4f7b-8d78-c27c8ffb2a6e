package service

import (
	"crypto/rand"
	"fmt"
	"math"
	"math/big"

	"gitee.com/heiyee/platforms/email-system/internal/domain/tracking/entity"
)

// AttributionService 归因服务
type AttributionService struct {
	eventRepo TrackingEventRepository
	estimator *HumanOpenEstimator
}

// TrackingEventRepository 追踪事件仓储接口
type TrackingEventRepository interface {
	GetOpenEvents(campaignID string, timeWindow entity.TimeWindow) ([]*entity.TrackingEvent, error)
	GetClickEvents(campaignID string, timeWindow entity.TimeWindow) ([]*entity.TrackingEvent, error)
	GetConversionEvents(campaignID string, timeWindow entity.TimeWindow) ([]*entity.TrackingEvent, error)
	GetEventsBySubscriber(subscriberID string, timeWindow entity.TimeWindow) ([]*entity.TrackingEvent, error)
}

// NewAttributionService 创建归因服务
func NewAttributionService(eventRepo TrackingEventRepository) *AttributionService {
	return &AttributionService{
		eventRepo: eventRepo,
		estimator: NewHumanOpenEstimator(),
	}
}

// CalculateHumanOpens 计算人类打开数
func (s *AttributionService) CalculateHumanOpens(campaignID string, timeWindow entity.TimeWindow) (*HumanOpenResult, error) {
	// 1. 获取所有打开事件
	opens, err := s.eventRepo.GetOpenEvents(campaignID, timeWindow)
	if err != nil {
		return nil, fmt.Errorf("failed to get open events: %w", err)
	}

	// 2. 获取点击事件用于回填
	clicks, err := s.eventRepo.GetClickEvents(campaignID, timeWindow)
	if err != nil {
		return nil, fmt.Errorf("failed to get click events: %w", err)
	}

	// 3. 基于点击回填人类打开
	humanOpens := s.backfillFromClicks(opens, clicks)

	// 4. 对代理/MPP事件进行估算
	estimatedOpens := s.estimator.EstimateHumanOpens(opens, campaignID)

	// 5. 合并结果
	result := s.mergeResults(humanOpens, estimatedOpens, opens)

	return result, nil
}

// HumanOpenResult 人类打开结果
type HumanOpenResult struct {
	GrossOpens          int64                               `json:"gross_opens"`
	UniqueGrossOpens    int64                               `json:"unique_gross_opens"`
	EstimatedHumanOpens int64                               `json:"estimated_human_opens"`
	UniqueHumanOpens    int64                               `json:"unique_human_opens"`
	ClassificationStats map[entity.OpenClassification]int64 `json:"classification_stats"`
	EstimationDetails   *EstimationDetails                  `json:"estimation_details"`
	ConfidenceInterval  *ConfidenceInterval                 `json:"confidence_interval"`
}

// EstimationDetails 估算详情
type EstimationDetails struct {
	ClickbackfillCount int64   `json:"click_backfill_count"`
	EstimatedCount     int64   `json:"estimated_count"`
	EstimationRate     float64 `json:"estimation_rate"`
	ModelVersion       string  `json:"model_version"`
}

// ConfidenceInterval 置信区间
type ConfidenceInterval struct {
	Lower float64 `json:"lower"`
	Upper float64 `json:"upper"`
	Level float64 `json:"level"` // 置信水平，如0.95
}

// backfillFromClicks 从点击事件回填人类打开
func (s *AttributionService) backfillFromClicks(opens []*entity.TrackingEvent, clicks []*entity.TrackingEvent) map[string]bool {
	humanOpens := make(map[string]bool)
	clickedUsers := make(map[string]bool)

	// 标记有点击的用户
	for _, click := range clicks {
		clickedUsers[click.SubscriberID] = true
	}

	// 回填：有点击的用户标记为人类打开
	for _, open := range opens {
		if clickedUsers[open.SubscriberID] {
			humanOpens[open.SubscriberID] = true
		}
	}

	return humanOpens
}

// mergeResults 合并结果
func (s *AttributionService) mergeResults(humanOpens map[string]bool, estimatedOpens *EstimationResult, allOpens []*entity.TrackingEvent) *HumanOpenResult {
	result := &HumanOpenResult{
		ClassificationStats: make(map[entity.OpenClassification]int64),
		EstimationDetails: &EstimationDetails{
			ModelVersion: estimatedOpens.ModelVersion,
		},
		ConfidenceInterval: &ConfidenceInterval{
			Level: 0.95,
		},
	}

	uniqueSubscribers := make(map[string]bool)
	uniqueHumanSubscribers := make(map[string]bool)

	for _, open := range allOpens {
		// 总打开统计
		result.GrossOpens++
		uniqueSubscribers[open.SubscriberID] = true

		// 分类统计
		result.ClassificationStats[open.OpenClass]++

		// 人类打开统计
		isHuman := false
		if humanOpens[open.SubscriberID] {
			// 点击回填的人类打开
			isHuman = true
			result.EstimationDetails.ClickbackfillCount++
		} else if estimatedOpens.EstimatedUsers[open.SubscriberID] {
			// 估算的人类打开
			isHuman = true
			result.EstimationDetails.EstimatedCount++
		} else if open.OpenClass == entity.OpenClassHuman {
			// 直接识别的人类打开
			isHuman = true
		}

		if isHuman {
			result.EstimatedHumanOpens++
			uniqueHumanSubscribers[open.SubscriberID] = true
		}
	}

	result.UniqueGrossOpens = int64(len(uniqueSubscribers))
	result.UniqueHumanOpens = int64(len(uniqueHumanSubscribers))

	// 计算估算率
	if result.EstimationDetails.EstimatedCount > 0 {
		result.EstimationDetails.EstimationRate = float64(result.EstimationDetails.EstimatedCount) / float64(result.EstimatedHumanOpens)
	}

	// 计算置信区间（可选，仅在有足够样本时计算）
	result.ConfidenceInterval = s.calculateConfidenceInterval(result)

	return result
}

// calculateConfidenceInterval 计算置信区间
func (s *AttributionService) calculateConfidenceInterval(result *HumanOpenResult) *ConfidenceInterval {
	// 使用 Wilson 分数区间估计比例的置信区间
	// 比例 p = UniqueHumanOpens / UniqueGrossOpens（独立用户口径更稳健）
	n := float64(result.UniqueGrossOpens)
	if n <= 0 {
		return &ConfidenceInterval{Lower: 0, Upper: 0, Level: 0.95}
	}

	k := float64(result.UniqueHumanOpens)
	phat := k / n

	level := 0.95
	if result.ConfidenceInterval != nil && result.ConfidenceInterval.Level > 0 {
		level = result.ConfidenceInterval.Level
	}
	z := zForConfidenceLevel(level)

	z2 := z * z
	denom := 1 + z2/n
	center := (phat + z2/(2*n)) / denom
	margin := (z * math.Sqrt((phat*(1-phat))/n+z2/(4*n*n))) / denom

	lower := center - margin
	upper := center + margin

	if lower < 0 {
		lower = 0
	}
	if upper > 1 {
		upper = 1
	}

	return &ConfidenceInterval{Lower: lower, Upper: upper, Level: level}
}

// zForConfidenceLevel 返回给定置信水平的近似 z 值（双侧）
func zForConfidenceLevel(level float64) float64 {
	// 常用水平映射；默认回退到 1.96 (95%)
	switch {
	case nearly(level, 0.80):
		return 1.2816
	case nearly(level, 0.85):
		return 1.4395
	case nearly(level, 0.90):
		return 1.6449
	case nearly(level, 0.95):
		return 1.96
	case nearly(level, 0.98):
		return 2.3263
	case nearly(level, 0.99):
		return 2.5758
	default:
		return 1.96
	}
}

func nearly(a, b float64) bool {
	const eps = 1e-6
	return math.Abs(a-b) < eps
}

// HumanOpenEstimator 人类打开估算器
type HumanOpenEstimator struct {
	baselineCalculator *BaselineCalculator
}

// NewHumanOpenEstimator 创建人类打开估算器
func NewHumanOpenEstimator() *HumanOpenEstimator {
	return &HumanOpenEstimator{
		baselineCalculator: NewBaselineCalculator(),
	}
}

// EstimationResult 估算结果
type EstimationResult struct {
	EstimatedUsers map[string]bool `json:"estimated_users"`
	EstimationRate float64         `json:"estimation_rate"`
	BaselineRate   float64         `json:"baseline_rate"`
	ModelVersion   string          `json:"model_version"`
	SegmentInfo    *SegmentInfo    `json:"segment_info"`
}

// SegmentInfo 分群信息
type SegmentInfo struct {
	Industry     string `json:"industry"`
	ISP          string `json:"isp"`
	DeviceType   string `json:"device_type"`
	ActivityType string `json:"activity_type"`
}

// EstimateHumanOpens 估算人类打开
func (e *HumanOpenEstimator) EstimateHumanOpens(opens []*entity.TrackingEvent, campaignID string) *EstimationResult {
	result := &EstimationResult{
		EstimatedUsers: make(map[string]bool),
		ModelVersion:   "v1.0",
	}

	// 1. 计算基准打开率
	baselineRate := e.baselineCalculator.CalculateBaselineRate(campaignID)
	result.BaselineRate = baselineRate

	// 2. 设置估算系数（可配置）
	estimationAlpha := 0.8 // 默认估算系数
	result.EstimationRate = baselineRate * estimationAlpha

	// 3. 对MPP/Scanner事件进行估算
	for _, open := range opens {
		shouldEstimate := false

		switch open.OpenClass {
		case entity.OpenClassMPP:
			shouldEstimate = true
		case entity.OpenClassScanner:
			shouldEstimate = e.shouldEstimateScanner(open)
		case entity.OpenClassProxy:
			shouldEstimate = e.shouldEstimateProxy(open)
		}

		if shouldEstimate {
			// 使用概率模型决定是否算作人类打开
			if e.shouldCountAsHuman(result.EstimationRate) {
				result.EstimatedUsers[open.SubscriberID] = true
			}
		}
	}

	return result
}

// shouldEstimateScanner 判断是否应该对扫描器事件进行估算
func (e *HumanOpenEstimator) shouldEstimateScanner(open *entity.TrackingEvent) bool {
	// 对于扫描器，通常不估算为人类打开
	return false
}

// shouldEstimateProxy 判断是否应该对代理事件进行估算
func (e *HumanOpenEstimator) shouldEstimateProxy(open *entity.TrackingEvent) bool {
	// Gmail代理的首次打开通常是真实用户触发的
	return open.IsFirstHit
}

// shouldCountAsHuman 基于概率判断是否算作人类打开
func (e *HumanOpenEstimator) shouldCountAsHuman(probability float64) bool {
	if probability <= 0 {
		return false
	}
	if probability >= 1 {
		return true
	}
	// 生成 [0,1) 的随机数：使用加密安全的随机源避免引入 math.Rand 依赖
	n, err := rand.Int(rand.Reader, big.NewInt(1_000_000))
	if err != nil {
		// 退化为保守策略
		return false
	}
	return float64(n.Int64())/1_000_000.0 < probability
}

// BaselineCalculator 基准计算器
type BaselineCalculator struct {
	// 可以注入数据库连接来获取历史数据
}

// NewBaselineCalculator 创建基准计算器
func NewBaselineCalculator() *BaselineCalculator {
	return &BaselineCalculator{}
}

// CalculateBaselineRate 计算基准打开率
func (c *BaselineCalculator) CalculateBaselineRate(campaignID string) float64 {
	// 这里应该基于历史数据计算，暂时返回行业平均值
	return 0.25 // 25%的基准打开率
}
