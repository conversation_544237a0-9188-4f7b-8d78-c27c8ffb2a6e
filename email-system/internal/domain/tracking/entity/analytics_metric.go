package entity

import (
	"time"
)

// AnalyticsMetric 分析指标
type AnalyticsMetric struct {
	ID         string `json:"id"`
	TenantID   int64  `json:"tenant_id"`
	CampaignID string `json:"campaign_id"`

	// 时间维度
	HourWindow time.Time `json:"hour_window"`
	DayWindow  time.Time `json:"day_window"`

	// 基础指标
	Sends        int64 `json:"sends"`
	Delivered    int64 `json:"delivered"`
	Bounces      int64 `json:"bounces"`
	Complaints   int64 `json:"complaints"`
	Unsubscribes int64 `json:"unsubscribes"`

	// 打开指标
	GrossOpens       int64 `json:"gross_opens"`
	UniqueGrossOpens int64 `json:"unique_gross_opens"`
	MPPOpens         int64 `json:"mpp_opens"`
	ProxyOpens       int64 `json:"proxy_opens"`
	ScannerOpens     int64 `json:"scanner_opens"`
	DirectOpens      int64 `json:"direct_opens"`

	// 点击指标
	Clicks       int64 `json:"clicks"`
	UniqueClicks int64 `json:"unique_clicks"`

	// 转化指标
	Conversions int64   `json:"conversions"`
	Revenue     float64 `json:"revenue"`
	Currency    string  `json:"currency"`

	// 审计字段
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// TimeWindow 时间窗口
type TimeWindow struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
}
