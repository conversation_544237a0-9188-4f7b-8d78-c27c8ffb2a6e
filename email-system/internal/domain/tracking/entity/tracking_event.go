package entity

import (
	"fmt"
	"math/rand"
	"time"
)

// TrackingEvent 追踪事件实体
type TrackingEvent struct {
	ID           string    `json:"id"`
	TenantID     int64     `json:"tenant_id"`
	Type         EventType `json:"type"`
	CampaignID   string    `json:"campaign_id"`
	MessageID    string    `json:"message_id"`
	SubscriberID string    `json:"subscriber_id"`
	LinkID       string    `json:"link_id,omitempty"`

	// 分类信息
	OpenClass  OpenClassification `json:"open_class,omitempty"`
	Source     EventSource        `json:"source,omitempty"`
	IsUnique   bool               `json:"is_unique"`
	IsFirstHit bool               `json:"is_first_hit"`

	// 请求信息
	UserAgent   string            `json:"user_agent"`
	IPAddress   string            `json:"ip_address"`
	GeoLocation *GeoLocation      `json:"geo_location,omitempty"`
	Headers     map[string]string `json:"headers,omitempty"`

	// 安全信息
	SignatureValid bool   `json:"signature_valid"`
	Timestamp      int64  `json:"timestamp"`
	Nonce          string `json:"nonce"`

	// 转化信息（仅转化事件）
	ConversionType string  `json:"conversion_type,omitempty"`
	OrderID        string  `json:"order_id,omitempty"`
	Value          float64 `json:"value,omitempty"`
	Currency       string  `json:"currency,omitempty"`

	// 点击信息（仅点击事件）
	DestinationHost string `json:"destination_host,omitempty"`
	OriginalURL     string `json:"original_url,omitempty"`

	OccurredAt time.Time `json:"occurred_at"`
	CreatedAt  time.Time `json:"created_at"`
}

// EventType 事件类型
type EventType string

const (
	EventTypeOpen       EventType = "open"
	EventTypeClick      EventType = "click"
	EventTypeConversion EventType = "conversion"
	EventTypeReply      EventType = "reply"
	EventTypeBeacon     EventType = "beacon"
	EventTypeAMPOpen    EventType = "amp_open"
)

// OpenClassification 打开分类
type OpenClassification string

const (
	OpenClassHuman    OpenClassification = "human"
	OpenClassPrefetch OpenClassification = "prefetch"
	OpenClassMPP      OpenClassification = "mpp"
	OpenClassProxy    OpenClassification = "proxy"
	OpenClassScanner  OpenClassification = "scanner"
	OpenClassUnknown  OpenClassification = "unknown"
)

// EventSource 事件来源
type EventSource string

const (
	EventSourceDirect     EventSource = "direct"
	EventSourceAppleMPP   EventSource = "apple_mpp"
	EventSourceGmailProxy EventSource = "gmail_proxy"
	EventSourceScanner    EventSource = "scanner"
	EventSourceEdge       EventSource = "edge"
	EventSourceUnknown    EventSource = "unknown"
)

// GeoLocation 地理位置信息
type GeoLocation struct {
	Country string  `json:"country"`
	Region  string  `json:"region"`
	City    string  `json:"city"`
	Lat     float64 `json:"lat,omitempty"`
	Lon     float64 `json:"lon,omitempty"`
}

// NewTrackingEvent 创建新的追踪事件
func NewTrackingEvent(eventType EventType, tenantID int64) *TrackingEvent {
	now := time.Now()
	return &TrackingEvent{
		ID:         generateEventID(),
		TenantID:   tenantID,
		Type:       eventType,
		IsUnique:   false,
		IsFirstHit: false,
		Headers:    make(map[string]string),
		OccurredAt: now,
		CreatedAt:  now,
	}
}

// IsOpenEvent 判断是否为打开事件
func (e *TrackingEvent) IsOpenEvent() bool {
	return e.Type == EventTypeOpen || e.Type == EventTypeBeacon || e.Type == EventTypeAMPOpen
}

// IsClickEvent 判断是否为点击事件
func (e *TrackingEvent) IsClickEvent() bool {
	return e.Type == EventTypeClick
}

// IsConversionEvent 判断是否为转化事件
func (e *TrackingEvent) IsConversionEvent() bool {
	return e.Type == EventTypeConversion
}

// GetDedupeKey 获取去重键
func (e *TrackingEvent) GetDedupeKey() string {
	if e.IsClickEvent() {
		return fmt.Sprintf("%s|%s|%s|%s|%s",
			e.Type, e.CampaignID, e.SubscriberID, e.MessageID, e.LinkID)
	}
	return fmt.Sprintf("%s|%s|%s|%s",
		e.Type, e.CampaignID, e.SubscriberID, e.MessageID)
}

// generateEventID 生成事件ID
func generateEventID() string {
	// 使用时间戳和随机数生成唯一ID
	return fmt.Sprintf("%d_%s", time.Now().UnixNano(), generateRandomString(8))
}

func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}
