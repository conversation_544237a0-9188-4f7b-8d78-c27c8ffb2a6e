package service

import (
	"context"
	"fmt"
	"time"

	segmentRepository "gitee.com/heiyee/platforms/email-system/internal/domain/segment/repository"
	"gitee.com/heiyee/platforms/email-system/internal/domain/send/entity"
	sendRepository "gitee.com/heiyee/platforms/email-system/internal/domain/send/repository"
	"gitee.com/heiyee/platforms/email-system/internal/infrastructure/client"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// SendScheduler 发送调度器
type SendScheduler struct {
	logger               logiface.Logger
	sendPlanRepo         sendRepository.SendPlanRepository
	sendBatchRepo        sendRepository.SendBatchRepository
	audienceSnapshotRepo sendRepository.AudienceSnapshotRepository
	sendRecordRepo       sendRepository.SendRecordRepository
	segmentRepo          segmentRepository.SegmentRepository
	emailClient          client.EmailClient
}

// NewSendScheduler 创建发送调度器
func NewSendScheduler(
	logger logiface.Logger,
	sendPlanRepo sendRepository.SendPlanRepository,
	sendBatchRepo sendRepository.SendBatchRepository,
	audienceSnapshotRepo sendRepository.AudienceSnapshotRepository,
	sendRecordRepo sendRepository.SendRecordRepository,
	segmentRepo segmentRepository.SegmentRepository,
	emailClient client.EmailClient,
) *SendScheduler {
	return &SendScheduler{
		logger:               logger,
		sendPlanRepo:         sendPlanRepo,
		sendBatchRepo:        sendBatchRepo,
		audienceSnapshotRepo: audienceSnapshotRepo,
		sendRecordRepo:       sendRecordRepo,
		segmentRepo:          segmentRepo,
		emailClient:          emailClient,
	}
}

// SchedulePlans 调度发送计划
func (s *SendScheduler) SchedulePlans(ctx context.Context) error {
	s.logger.Info(ctx, "Starting to schedule send plans")

	// 查找需要调度的计划
	plans, err := s.sendPlanRepo.FindScheduledPlans(ctx, 100)
	if err != nil {
		s.logger.Error(ctx, "Failed to find scheduled plans", logiface.Error(err))
		return entity.NewSendScheduleError(fmt.Sprintf("find scheduled plans: %v", err))
	}

	s.logger.Info(ctx, "Found scheduled plans", logiface.Int("count", len(plans)))

	for _, plan := range plans {
		if err := s.schedulePlan(ctx, plan); err != nil {
			s.logger.Error(ctx, "Failed to schedule plan",
				logiface.Error(err),
				logiface.Int64("plan_id", plan.PlanID))
			continue
		}
	}

	return nil
}

// schedulePlan 调度单个发送计划
func (s *SendScheduler) schedulePlan(ctx context.Context, plan *entity.SendPlan) error {
	s.logger.Info(ctx, "Scheduling plan",
		logiface.Int64("plan_id", plan.PlanID),
		logiface.String("plan_type", string(plan.PlanType)))

	// 检查计划是否可以开始
	if !plan.CanStart() {
		s.logger.Debug(ctx, "Plan cannot start", logiface.Int64("plan_id", plan.PlanID))
		return nil
	}

	// 检查是否已过期
	if plan.IsExpired() {
		s.logger.Info(ctx, "Plan is expired, marking as expired", logiface.Int64("plan_id", plan.PlanID))
		plan.MarkExpired()
		return s.sendPlanRepo.Update(ctx, plan)
	}

	// 开始执行计划
	if err := plan.Start(); err != nil {
		return err
	}

	// 更新计划状态
	if err := s.sendPlanRepo.Update(ctx, plan); err != nil {
		s.logger.Error(ctx, "Failed to update plan status",
			logiface.Error(err),
			logiface.Int64("plan_id", plan.PlanID))
		return entity.NewSendPlanUpdateFailedError(fmt.Sprintf("update plan status: %v", err))
	}

	// 创建受众快照
	snapshot, err := s.createAudienceSnapshot(ctx, plan)
	if err != nil {
		s.logger.Error(ctx, "Failed to create audience snapshot",
			logiface.Error(err),
			logiface.Int64("plan_id", plan.PlanID))
		return err
	}

	// 创建发送批次
	if err := s.createSendBatches(ctx, plan, snapshot); err != nil {
		s.logger.Error(ctx, "Failed to create send batches",
			logiface.Error(err),
			logiface.Int64("plan_id", plan.PlanID))
		return err
	}

	s.logger.Info(ctx, "Plan scheduled successfully",
		logiface.Int64("plan_id", plan.PlanID),
		logiface.Int("audience_count", snapshot.TotalCount))

	return nil
}

// createAudienceSnapshot 创建受众快照
func (s *SendScheduler) createAudienceSnapshot(ctx context.Context, plan *entity.SendPlan) (*entity.AudienceSnapshot, error) {
	s.logger.Info(ctx, "Creating audience snapshot", logiface.Int64("plan_id", plan.PlanID))

	// TODO: 根据计划类型和配置获取受众圈选条件
	// 这里暂时使用模拟数据
	segmentQuery := map[string]interface{}{
		"type": "all_active_contacts",
		"filters": map[string]interface{}{
			"status": "active",
		},
	}

	// TODO: 执行圈选逻辑获取实际受众数量
	// 这里暂时使用模拟数据
	totalCount := 10000

	snapshot := &entity.AudienceSnapshot{
		PlanID:         plan.PlanID,
		TenantID:       plan.TenantID,
		SegmentQuery:   segmentQuery,
		MaterializedAt: time.Now(),
		TotalCount:     totalCount,
	}

	if err := snapshot.Validate(); err != nil {
		return nil, err
	}

	if err := s.audienceSnapshotRepo.Create(ctx, snapshot); err != nil {
		return nil, entity.NewAudienceSnapshotCreateFailedError(fmt.Sprintf("create snapshot: %v", err))
	}

	return snapshot, nil
}

// createSendBatches 创建发送批次
func (s *SendScheduler) createSendBatches(ctx context.Context, plan *entity.SendPlan, snapshot *entity.AudienceSnapshot) error {
	s.logger.Info(ctx, "Creating send batches",
		logiface.Int64("plan_id", plan.PlanID),
		logiface.Int("total_count", snapshot.TotalCount))

	if snapshot.IsEmpty() {
		s.logger.Info(ctx, "Audience snapshot is empty, no batches to create",
			logiface.Int64("plan_id", plan.PlanID))
		return nil
	}

	// 获取批次大小
	batchSize := entity.GetEstimatedBatchSize(plan.PlanType)
	batchCount := snapshot.GetEstimatedBatchCount(batchSize)

	s.logger.Info(ctx, "Calculated batch parameters",
		logiface.Int("batch_size", batchSize),
		logiface.Int("batch_count", batchCount))

	// 创建批次
	now := time.Now()
	for i := 0; i < batchCount; i++ {
		// 计算当前批次的实际大小
		currentBatchSize := batchSize
		if i == batchCount-1 {
			// 最后一个批次可能不满
			remaining := snapshot.TotalCount - i*batchSize
			if remaining < batchSize {
				currentBatchSize = remaining
			}
		}

		batch := &entity.SendBatch{
			PlanID:       plan.PlanID,
			TenantID:     plan.TenantID,
			ExpectedSize: currentBatchSize,
			SendAt:       now.Add(time.Duration(i) * time.Minute), // 每分钟发送一个批次
			Priority:     plan.Priority,
			Status:       entity.BatchStatusPending,
		}

		// 设置取消截止时间（发送前5分钟）
		cancelableUntil := batch.SendAt.Add(-5 * time.Minute)
		batch.SetCancelableUntil(cancelableUntil)

		if err := batch.Validate(); err != nil {
			return err
		}

		if err := s.sendBatchRepo.Create(ctx, batch); err != nil {
			return entity.NewSendBatchCreateFailedError(fmt.Sprintf("create batch %d: %v", i, err))
		}

		s.logger.Debug(ctx, "Created send batch",
			logiface.Int64("batch_id", batch.BatchID),
			logiface.Int("expected_size", batch.ExpectedSize),
			logiface.Time("send_at", batch.SendAt))
	}

	return nil
}

// ProcessBatches 处理发送批次
func (s *SendScheduler) ProcessBatches(ctx context.Context) error {
	s.logger.Info(ctx, "Starting to process send batches")

	// 查找准备发送的批次
	batches, err := s.sendBatchRepo.FindReadyBatches(ctx, 50)
	if err != nil {
		s.logger.Error(ctx, "Failed to find ready batches", logiface.Error(err))
		return entity.NewSendScheduleError(fmt.Sprintf("find ready batches: %v", err))
	}

	s.logger.Info(ctx, "Found ready batches", logiface.Int("count", len(batches)))

	for _, batch := range batches {
		if err := s.processBatch(ctx, batch); err != nil {
			s.logger.Error(ctx, "Failed to process batch",
				logiface.Error(err),
				logiface.Int64("batch_id", batch.BatchID))
			continue
		}
	}

	return nil
}

// processBatch 处理单个发送批次
func (s *SendScheduler) processBatch(ctx context.Context, batch *entity.SendBatch) error {
	s.logger.Info(ctx, "Processing batch",
		logiface.Int64("batch_id", batch.BatchID),
		logiface.Int("expected_size", batch.ExpectedSize))

	// 检查批次是否准备好发送
	if !batch.IsReadyToSend() {
		s.logger.Debug(ctx, "Batch is not ready to send", logiface.Int64("batch_id", batch.BatchID))
		return nil
	}

	// 开始执行批次
	if err := batch.Start(); err != nil {
		return err
	}

	// 更新批次状态
	if err := s.sendBatchRepo.Update(ctx, batch); err != nil {
		s.logger.Error(ctx, "Failed to update batch status",
			logiface.Error(err),
			logiface.Int64("batch_id", batch.BatchID))
		return entity.NewSendBatchUpdateFailedError(fmt.Sprintf("update batch status: %v", err))
	}

	// 生成发送记录
	if err := s.generateSendRecords(ctx, batch); err != nil {
		s.logger.Error(ctx, "Failed to generate send records",
			logiface.Error(err),
			logiface.Int64("batch_id", batch.BatchID))
		return err
	}

	// 执行实际发送
	if err := s.executeBatchSend(ctx, batch); err != nil {
		s.logger.Error(ctx, "Failed to execute batch send",
			logiface.Error(err),
			logiface.Int64("batch_id", batch.BatchID))
		return err
	}

	s.logger.Info(ctx, "Batch processed successfully", logiface.Int64("batch_id", batch.BatchID))

	return nil
}

// generateSendRecords 生成发送记录
func (s *SendScheduler) generateSendRecords(ctx context.Context, batch *entity.SendBatch) error {
	s.logger.Info(ctx, "Generating send records",
		logiface.Int64("batch_id", batch.BatchID),
		logiface.Int("expected_size", batch.ExpectedSize))

	// TODO: 根据受众快照和批次信息生成实际的发送记录
	// 这里暂时生成模拟记录
	var records []*entity.SendRecord
	for i := 0; i < batch.ExpectedSize; i++ {
		record := &entity.SendRecord{
			BatchID:      batch.BatchID,
			PlanID:       batch.PlanID,
			TenantID:     batch.TenantID,
			ContactID:    int64(i + 1), // 模拟联系人ID
			EmailAddress: fmt.Sprintf("<EMAIL>", i+1),
			TemplateID:   "template_001", // 模拟模板ID
			Status:       entity.SendStatusPending,
			AttemptCount: 0,
		}

		if err := record.Validate(); err != nil {
			return err
		}

		records = append(records, record)
	}

	// 批量创建发送记录
	if err := s.sendRecordRepo.BatchCreate(ctx, records); err != nil {
		return entity.NewSendRecordCreateFailedError(fmt.Sprintf("batch create records: %v", err))
	}

	s.logger.Info(ctx, "Generated send records",
		logiface.Int64("batch_id", batch.BatchID),
		logiface.Int("record_count", len(records)))

	return nil
}

// executeBatchSend 执行批次发送
func (s *SendScheduler) executeBatchSend(ctx context.Context, batch *entity.SendBatch) error {
	s.logger.Info(ctx, "Executing batch send", logiface.Int64("batch_id", batch.BatchID))

	// 获取待发送记录
	records, err := s.sendRecordRepo.FindPendingRecords(ctx, batch.BatchID, batch.ExpectedSize)
	if err != nil {
		return entity.NewSendRecordQueryFailedError(fmt.Sprintf("find pending records: %v", err))
	}

	if len(records) == 0 {
		s.logger.Info(ctx, "No pending records found", logiface.Int64("batch_id", batch.BatchID))
		return s.completeBatch(ctx, batch)
	}

	// 构建批量发送请求
	emailRequests := make([]*client.EmailSendRequest, 0, len(records))
	for _, record := range records {
		emailReq := &client.EmailSendRequest{
			TenantID:   record.TenantID,
			RequestID:  fmt.Sprintf("record_%d", record.RecordID),
			TemplateID: record.TemplateID,
			Priority:   batch.Priority,
			ToEmail:    record.EmailAddress,
			Variables:  make(map[string]interface{}),
		}
		emailRequests = append(emailRequests, emailReq)
	}

	batchReq := &client.EmailBatchSendRequest{
		TenantID:  batch.TenantID,
		BatchID:   fmt.Sprintf("batch_%d", batch.BatchID),
		RequestID: fmt.Sprintf("plan_%d_batch_%d", batch.PlanID, batch.BatchID),
		Emails:    emailRequests,
	}

	// 调用Email服务发送
	response, err := s.emailClient.SendBatchEmails(ctx, batchReq)
	if err != nil {
		s.logger.Error(ctx, "Failed to send batch emails",
			logiface.Error(err),
			logiface.Int64("batch_id", batch.BatchID))
		return s.failBatch(ctx, batch, err.Error())
	}

	// 更新发送记录状态
	if err := s.updateSendRecords(ctx, records, response); err != nil {
		s.logger.Error(ctx, "Failed to update send records",
			logiface.Error(err),
			logiface.Int64("batch_id", batch.BatchID))
		return err
	}

	// 完成批次
	return s.completeBatch(ctx, batch)
}

// updateSendRecords 更新发送记录状态
func (s *SendScheduler) updateSendRecords(ctx context.Context, records []*entity.SendRecord, response *client.EmailBatchSendResponse) error {
	s.logger.Info(ctx, "Updating send records",
		logiface.Int("record_count", len(records)),
		logiface.Int("success_count", response.SuccessCount),
		logiface.Int("failed_count", response.FailedCount))

	// 简化处理：假设所有记录都成功发送
	// TODO: 根据实际响应结果更新每个记录的状态
	for _, record := range records {
		record.MarkSent()
	}

	// 批量更新记录
	if err := s.sendRecordRepo.BatchUpdate(ctx, records); err != nil {
		return entity.NewSendRecordUpdateFailedError(fmt.Sprintf("batch update records: %v", err))
	}

	return nil
}

// completeBatch 完成批次
func (s *SendScheduler) completeBatch(ctx context.Context, batch *entity.SendBatch) error {
	if err := batch.Complete(); err != nil {
		return err
	}

	if err := s.sendBatchRepo.Update(ctx, batch); err != nil {
		return entity.NewSendBatchUpdateFailedError(fmt.Sprintf("complete batch: %v", err))
	}

	s.logger.Info(ctx, "Batch completed", logiface.Int64("batch_id", batch.BatchID))
	return nil
}

// failBatch 标记批次失败
func (s *SendScheduler) failBatch(ctx context.Context, batch *entity.SendBatch, errorMessage string) error {
	batch.MarkFailed()

	if err := s.sendBatchRepo.Update(ctx, batch); err != nil {
		return entity.NewSendBatchUpdateFailedError(fmt.Sprintf("fail batch: %v", err))
	}

	s.logger.Error(ctx, "Batch failed",
		logiface.Int64("batch_id", batch.BatchID),
		logiface.String("error", errorMessage))
	return nil
}
