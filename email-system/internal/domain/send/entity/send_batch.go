package entity

import (
	"time"
)

// BatchStatus 批次状态
type BatchStatus string

const (
	BatchStatusPending   BatchStatus = "pending"   // 待处理
	BatchStatusEnqueued  BatchStatus = "enqueued"  // 已入队
	BatchStatusRunning   BatchStatus = "running"   // 运行中
	BatchStatusPaused    BatchStatus = "paused"    // 暂停
	BatchStatusCompleted BatchStatus = "completed" // 已完成
	BatchStatusCanceled  BatchStatus = "canceled"  // 已取消
	BatchStatusExpired   BatchStatus = "expired"   // 已过期
	BatchStatusFailed    BatchStatus = "failed"    // 失败
)

// SendBatch 发送批次实体
type SendBatch struct {
	BatchID         int64       `json:"batch_id" gorm:"primaryKey;autoIncrement"`
	PlanID          int64       `json:"plan_id" gorm:"not null;index:idx_send_batch_plan"`
	TenantID        int64       `json:"tenant_id" gorm:"not null;index:idx_send_batch_tenant"`
	ExpectedSize    int         `json:"expected_size" gorm:"not null"`
	SendAt          time.Time   `json:"send_at" gorm:"not null"`
	Priority        int         `json:"priority" gorm:"not null"`
	Status          BatchStatus `json:"status" gorm:"not null;default:'pending';index:idx_send_batch_status"`
	ThrottleKey     *string     `json:"throttle_key" gorm:"size:256"`
	CancelableUntil *time.Time  `json:"cancelable_until"`
	CreatedAt       time.Time   `json:"created_at" gorm:"not null;autoCreateTime"`
	UpdatedAt       time.Time   `json:"updated_at" gorm:"not null;autoUpdateTime"`
}

// TableName 指定表名
func (SendBatch) TableName() string {
	return "send_batches"
}

// IsValid 检查批次状态是否有效
func (bs BatchStatus) IsValid() bool {
	switch bs {
	case BatchStatusPending, BatchStatusEnqueued, BatchStatusRunning, BatchStatusPaused,
		BatchStatusCompleted, BatchStatusCanceled, BatchStatusExpired, BatchStatusFailed:
		return true
	default:
		return false
	}
}

// Validate 验证发送批次
func (sb *SendBatch) Validate() error {
	if sb.PlanID <= 0 {
		return NewSendBatchValidationError("plan_id", "计划ID不能为空")
	}

	if sb.TenantID <= 0 {
		return NewSendBatchValidationError("tenant_id", "租户ID不能为空")
	}

	if sb.ExpectedSize <= 0 {
		return NewSendBatchValidationError("expected_size", "预期大小必须大于0")
	}

	if sb.Priority < 1 || sb.Priority > 10 {
		return NewSendBatchValidationError("priority", "优先级必须在1-10之间")
	}

	if !sb.Status.IsValid() {
		return NewSendBatchValidationError("status", "无效的批次状态")
	}

	// 验证取消截止时间
	if sb.CancelableUntil != nil && sb.CancelableUntil.Before(sb.SendAt) {
		return NewSendBatchValidationError("cancelable_until", "取消截止时间不能早于发送时间")
	}

	return nil
}

// CanEnqueue 检查是否可以入队
func (sb *SendBatch) CanEnqueue() bool {
	return sb.Status == BatchStatusPending
}

// CanStart 检查是否可以开始执行
func (sb *SendBatch) CanStart() bool {
	return sb.Status == BatchStatusEnqueued || sb.Status == BatchStatusPaused
}

// CanPause 检查是否可以暂停
func (sb *SendBatch) CanPause() bool {
	return sb.Status == BatchStatusRunning
}

// CanCancel 检查是否可以取消
func (sb *SendBatch) CanCancel() bool {
	// 检查状态
	if sb.Status != BatchStatusPending && sb.Status != BatchStatusEnqueued && sb.Status != BatchStatusPaused {
		return false
	}

	// 检查取消截止时间
	if sb.CancelableUntil != nil && time.Now().After(*sb.CancelableUntil) {
		return false
	}

	return true
}

// IsExpired 检查是否已过期
func (sb *SendBatch) IsExpired(deadline *time.Time) bool {
	if deadline == nil {
		return false
	}
	return time.Now().After(*deadline)
}

// ShouldSend 检查是否应该发送
func (sb *SendBatch) ShouldSend() bool {
	if sb.Status != BatchStatusEnqueued {
		return false
	}

	return time.Now().After(sb.SendAt) || time.Now().Equal(sb.SendAt)
}

// Enqueue 入队
func (sb *SendBatch) Enqueue() error {
	if !sb.CanEnqueue() {
		return NewSendBatchStateError("批次当前状态不允许入队")
	}

	sb.Status = BatchStatusEnqueued
	sb.UpdatedAt = time.Now()
	return nil
}

// Start 开始执行
func (sb *SendBatch) Start() error {
	if !sb.CanStart() {
		return NewSendBatchStateError("批次当前状态不允许开始执行")
	}

	sb.Status = BatchStatusRunning
	sb.UpdatedAt = time.Now()
	return nil
}

// Pause 暂停
func (sb *SendBatch) Pause() error {
	if !sb.CanPause() {
		return NewSendBatchStateError("批次当前状态不允许暂停")
	}

	sb.Status = BatchStatusPaused
	sb.UpdatedAt = time.Now()
	return nil
}

// Cancel 取消
func (sb *SendBatch) Cancel() error {
	if !sb.CanCancel() {
		return NewSendBatchStateError("批次当前状态不允许取消或已超过取消截止时间")
	}

	sb.Status = BatchStatusCanceled
	sb.UpdatedAt = time.Now()
	return nil
}

// Complete 完成
func (sb *SendBatch) Complete() error {
	if sb.Status != BatchStatusRunning {
		return NewSendBatchStateError("只有运行中的批次可以标记为完成")
	}

	sb.Status = BatchStatusCompleted
	sb.UpdatedAt = time.Now()
	return nil
}

// MarkExpired 标记为过期
func (sb *SendBatch) MarkExpired() {
	sb.Status = BatchStatusExpired
	sb.UpdatedAt = time.Now()
}

// MarkFailed 标记为失败
func (sb *SendBatch) MarkFailed() {
	sb.Status = BatchStatusFailed
	sb.UpdatedAt = time.Now()
}

// SetThrottleKey 设置限流键
func (sb *SendBatch) SetThrottleKey(key string) {
	if key == "" {
		sb.ThrottleKey = nil
	} else {
		sb.ThrottleKey = &key
	}
}

// GetThrottleKey 获取限流键
func (sb *SendBatch) GetThrottleKey() string {
	if sb.ThrottleKey == nil {
		return ""
	}
	return *sb.ThrottleKey
}

// SetCancelableUntil 设置取消截止时间
func (sb *SendBatch) SetCancelableUntil(until time.Time) {
	sb.CancelableUntil = &until
}

// GetCancelableUntil 获取取消截止时间
func (sb *SendBatch) GetCancelableUntil() *time.Time {
	return sb.CancelableUntil
}

// IsReadyToSend 检查是否准备好发送
func (sb *SendBatch) IsReadyToSend() bool {
	return sb.Status == BatchStatusEnqueued && sb.ShouldSend()
}

// GetEstimatedBatchSize 根据计划类型获取建议的批次大小
func GetEstimatedBatchSize(planType PlanType) int {
	switch planType {
	case PlanTypeAutomation:
		return 500 // 100-1000，取中间值
	case PlanTypeCampaign, PlanTypeManual:
		return 10000 // 5k-20k，取中间值
	default:
		return 1000
	}
}

// Clone 克隆发送批次
func (sb *SendBatch) Clone() *SendBatch {
	clone := *sb

	if sb.ThrottleKey != nil {
		throttleKey := *sb.ThrottleKey
		clone.ThrottleKey = &throttleKey
	}

	if sb.CancelableUntil != nil {
		cancelableUntil := *sb.CancelableUntil
		clone.CancelableUntil = &cancelableUntil
	}

	return &clone
}
