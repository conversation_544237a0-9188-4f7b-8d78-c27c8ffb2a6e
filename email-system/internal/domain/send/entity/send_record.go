package entity

import (
	"encoding/json"
	"time"
)

// SendStatus 发送状态
type SendStatus string

const (
	SendStatusPending   SendStatus = "pending"   // 待发送
	SendStatusSent      SendStatus = "sent"      // 已发送
	SendStatusDelivered SendStatus = "delivered" // 已送达
	SendStatusBounced   SendStatus = "bounced"   // 退信
	SendStatusFailed    SendStatus = "failed"    // 发送失败
	SendStatusSkipped   SendStatus = "skipped"   // 跳过
)

// SendRecord 发送记录实体
type SendRecord struct {
	RecordID      int64                  `json:"record_id" gorm:"primaryKey;autoIncrement"`
	BatchID       int64                  `json:"batch_id" gorm:"not null;index:idx_send_record_batch"`
	PlanID        int64                  `json:"plan_id" gorm:"not null;index:idx_send_record_plan"`
	TenantID      int64                  `json:"tenant_id" gorm:"not null;index:idx_send_record_tenant"`
	ContactID     int64                  `json:"contact_id" gorm:"not null;index:idx_send_record_contact"`
	EmailAddress  string                 `json:"email_address" gorm:"not null;size:255;index:idx_send_record_email"`
	TemplateID    string                 `json:"template_id" gorm:"not null;size:128"`
	Status        SendStatus             `json:"status" gorm:"not null;default:'pending';index:idx_send_record_status"`
	AttemptCount  int                    `json:"attempt_count" gorm:"not null;default:0"`
	LastAttemptAt *time.Time             `json:"last_attempt_at"`
	SentAt        *time.Time             `json:"sent_at"`
	DeliveredAt   *time.Time             `json:"delivered_at"`
	BouncedAt     *time.Time             `json:"bounced_at"`
	FailedAt      *time.Time             `json:"failed_at"`
	ErrorMessage  *string                `json:"error_message" gorm:"type:text"`
	Metadata      map[string]interface{} `json:"metadata" gorm:"type:json"`
	CreatedAt     time.Time              `json:"created_at" gorm:"not null;autoCreateTime"`
	UpdatedAt     time.Time              `json:"updated_at" gorm:"not null;autoUpdateTime"`
}

// TableName 指定表名
func (SendRecord) TableName() string {
	return "send_records"
}

// IsValid 检查发送状态是否有效
func (ss SendStatus) IsValid() bool {
	switch ss {
	case SendStatusPending, SendStatusSent, SendStatusDelivered, SendStatusBounced, SendStatusFailed, SendStatusSkipped:
		return true
	default:
		return false
	}
}

// Validate 验证发送记录
func (sr *SendRecord) Validate() error {
	if sr.BatchID <= 0 {
		return NewSendRecordValidationError("batch_id", "批次ID不能为空")
	}

	if sr.PlanID <= 0 {
		return NewSendRecordValidationError("plan_id", "计划ID不能为空")
	}

	if sr.TenantID <= 0 {
		return NewSendRecordValidationError("tenant_id", "租户ID不能为空")
	}

	if sr.ContactID <= 0 {
		return NewSendRecordValidationError("contact_id", "联系人ID不能为空")
	}

	if sr.EmailAddress == "" {
		return NewSendRecordValidationError("email_address", "邮箱地址不能为空")
	}

	if len(sr.EmailAddress) > 255 {
		return NewSendRecordValidationError("email_address", "邮箱地址长度不能超过255个字符")
	}

	if sr.TemplateID == "" {
		return NewSendRecordValidationError("template_id", "模板ID不能为空")
	}

	if len(sr.TemplateID) > 128 {
		return NewSendRecordValidationError("template_id", "模板ID长度不能超过128个字符")
	}

	if !sr.Status.IsValid() {
		return NewSendRecordValidationError("status", "无效的发送状态")
	}

	if sr.AttemptCount < 0 {
		return NewSendRecordValidationError("attempt_count", "尝试次数不能为负数")
	}

	return nil
}

// IsPending 检查是否为待发送状态
func (sr *SendRecord) IsPending() bool {
	return sr.Status == SendStatusPending
}

// IsSent 检查是否已发送
func (sr *SendRecord) IsSent() bool {
	return sr.Status == SendStatusSent
}

// IsDelivered 检查是否已送达
func (sr *SendRecord) IsDelivered() bool {
	return sr.Status == SendStatusDelivered
}

// IsBounced 检查是否退信
func (sr *SendRecord) IsBounced() bool {
	return sr.Status == SendStatusBounced
}

// IsFailed 检查是否发送失败
func (sr *SendRecord) IsFailed() bool {
	return sr.Status == SendStatusFailed
}

// IsSkipped 检查是否跳过
func (sr *SendRecord) IsSkipped() bool {
	return sr.Status == SendStatusSkipped
}

// CanRetry 检查是否可以重试
func (sr *SendRecord) CanRetry(maxAttempts int) bool {
	if sr.Status != SendStatusFailed {
		return false
	}

	return sr.AttemptCount < maxAttempts
}

// MarkSent 标记为已发送
func (sr *SendRecord) MarkSent() {
	now := time.Now()
	sr.Status = SendStatusSent
	sr.SentAt = &now
	sr.LastAttemptAt = &now
	sr.AttemptCount++
	sr.UpdatedAt = now
}

// MarkDelivered 标记为已送达
func (sr *SendRecord) MarkDelivered() {
	now := time.Now()
	sr.Status = SendStatusDelivered
	sr.DeliveredAt = &now
	sr.UpdatedAt = now
}

// MarkBounced 标记为退信
func (sr *SendRecord) MarkBounced(errorMessage string) {
	now := time.Now()
	sr.Status = SendStatusBounced
	sr.BouncedAt = &now
	sr.ErrorMessage = &errorMessage
	sr.UpdatedAt = now
}

// MarkFailed 标记为发送失败
func (sr *SendRecord) MarkFailed(errorMessage string) {
	now := time.Now()
	sr.Status = SendStatusFailed
	sr.FailedAt = &now
	sr.ErrorMessage = &errorMessage
	sr.LastAttemptAt = &now
	sr.AttemptCount++
	sr.UpdatedAt = now
}

// MarkSkipped 标记为跳过
func (sr *SendRecord) MarkSkipped(reason string) {
	now := time.Now()
	sr.Status = SendStatusSkipped
	sr.ErrorMessage = &reason
	sr.UpdatedAt = now
}

// IncrementAttempt 增加尝试次数
func (sr *SendRecord) IncrementAttempt() {
	now := time.Now()
	sr.AttemptCount++
	sr.LastAttemptAt = &now
	sr.UpdatedAt = now
}

// GetMetadataJSON 获取元数据的JSON字符串
func (sr *SendRecord) GetMetadataJSON() (string, error) {
	if sr.Metadata == nil {
		return "{}", nil
	}

	data, err := json.Marshal(sr.Metadata)
	if err != nil {
		return "", err
	}

	return string(data), nil
}

// SetMetadataFromJSON 从JSON字符串设置元数据
func (sr *SendRecord) SetMetadataFromJSON(jsonStr string) error {
	if jsonStr == "" {
		sr.Metadata = make(map[string]interface{})
		return nil
	}

	var metadata map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &metadata); err != nil {
		return err
	}

	sr.Metadata = metadata
	return nil
}

// SetMetadata 设置元数据字段
func (sr *SendRecord) SetMetadata(key string, value interface{}) {
	if sr.Metadata == nil {
		sr.Metadata = make(map[string]interface{})
	}
	sr.Metadata[key] = value
}

// GetMetadata 获取元数据字段
func (sr *SendRecord) GetMetadata(key string) (interface{}, bool) {
	if sr.Metadata == nil {
		return nil, false
	}
	value, exists := sr.Metadata[key]
	return value, exists
}

// GetErrorMessage 获取错误消息
func (sr *SendRecord) GetErrorMessage() string {
	if sr.ErrorMessage == nil {
		return ""
	}
	return *sr.ErrorMessage
}

// Clone 克隆发送记录
func (sr *SendRecord) Clone() *SendRecord {
	clone := *sr

	if sr.LastAttemptAt != nil {
		lastAttemptAt := *sr.LastAttemptAt
		clone.LastAttemptAt = &lastAttemptAt
	}

	if sr.SentAt != nil {
		sentAt := *sr.SentAt
		clone.SentAt = &sentAt
	}

	if sr.DeliveredAt != nil {
		deliveredAt := *sr.DeliveredAt
		clone.DeliveredAt = &deliveredAt
	}

	if sr.BouncedAt != nil {
		bouncedAt := *sr.BouncedAt
		clone.BouncedAt = &bouncedAt
	}

	if sr.FailedAt != nil {
		failedAt := *sr.FailedAt
		clone.FailedAt = &failedAt
	}

	if sr.ErrorMessage != nil {
		errorMessage := *sr.ErrorMessage
		clone.ErrorMessage = &errorMessage
	}

	// 深拷贝元数据
	if sr.Metadata != nil {
		clone.Metadata = make(map[string]interface{})
		for k, v := range sr.Metadata {
			clone.Metadata[k] = v
		}
	}

	return &clone
}
