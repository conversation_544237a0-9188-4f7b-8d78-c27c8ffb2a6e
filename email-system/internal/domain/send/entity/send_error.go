package entity

import "fmt"

// SendErrorCode 发送错误码
type SendErrorCode string

const (
	// 发送计划错误
	CodeSendPlanValidationError   SendErrorCode = "SEND_PLAN_VALIDATION_ERROR"
	CodeSendPlanNotFoundError     SendErrorCode = "SEND_PLAN_NOT_FOUND_ERROR"
	CodeSendPlanStateError        SendErrorCode = "SEND_PLAN_STATE_ERROR"
	CodeSendPlanCreateFailedError SendErrorCode = "SEND_PLAN_CREATE_FAILED_ERROR"
	CodeSendPlanUpdateFailedError SendErrorCode = "SEND_PLAN_UPDATE_FAILED_ERROR"
	CodeSendPlanDeleteFailedError SendErrorCode = "SEND_PLAN_DELETE_FAILED_ERROR"
	CodeSendPlanQueryFailedError  SendErrorCode = "SEND_PLAN_QUERY_FAILED_ERROR"

	// 发送批次错误
	CodeSendBatchValidationError   SendErrorCode = "SEND_BATCH_VALIDATION_ERROR"
	CodeSendBatchNotFoundError     SendErrorCode = "SEND_BATCH_NOT_FOUND_ERROR"
	CodeSendBatchStateError        SendErrorCode = "SEND_BATCH_STATE_ERROR"
	CodeSendBatchCreateFailedError SendErrorCode = "SEND_BATCH_CREATE_FAILED_ERROR"
	CodeSendBatchUpdateFailedError SendErrorCode = "SEND_BATCH_UPDATE_FAILED_ERROR"
	CodeSendBatchDeleteFailedError SendErrorCode = "SEND_BATCH_DELETE_FAILED_ERROR"
	CodeSendBatchQueryFailedError  SendErrorCode = "SEND_BATCH_QUERY_FAILED_ERROR"

	// 受众快照错误
	CodeAudienceSnapshotValidationError   SendErrorCode = "AUDIENCE_SNAPSHOT_VALIDATION_ERROR"
	CodeAudienceSnapshotNotFoundError     SendErrorCode = "AUDIENCE_SNAPSHOT_NOT_FOUND_ERROR"
	CodeAudienceSnapshotCreateFailedError SendErrorCode = "AUDIENCE_SNAPSHOT_CREATE_FAILED_ERROR"
	CodeAudienceSnapshotUpdateFailedError SendErrorCode = "AUDIENCE_SNAPSHOT_UPDATE_FAILED_ERROR"
	CodeAudienceSnapshotDeleteFailedError SendErrorCode = "AUDIENCE_SNAPSHOT_DELETE_FAILED_ERROR"
	CodeAudienceSnapshotQueryFailedError  SendErrorCode = "AUDIENCE_SNAPSHOT_QUERY_FAILED_ERROR"

	// 发送记录错误
	CodeSendRecordValidationError   SendErrorCode = "SEND_RECORD_VALIDATION_ERROR"
	CodeSendRecordNotFoundError     SendErrorCode = "SEND_RECORD_NOT_FOUND_ERROR"
	CodeSendRecordCreateFailedError SendErrorCode = "SEND_RECORD_CREATE_FAILED_ERROR"
	CodeSendRecordUpdateFailedError SendErrorCode = "SEND_RECORD_UPDATE_FAILED_ERROR"
	CodeSendRecordDeleteFailedError SendErrorCode = "SEND_RECORD_DELETE_FAILED_ERROR"
	CodeSendRecordQueryFailedError  SendErrorCode = "SEND_RECORD_QUERY_FAILED_ERROR"

	// 发送调度错误
	CodeSendScheduleError  SendErrorCode = "SEND_SCHEDULE_ERROR"
	CodeSendExecutionError SendErrorCode = "SEND_EXECUTION_ERROR"
	CodeSendThrottleError  SendErrorCode = "SEND_THROTTLE_ERROR"
	CodeSendRetryError     SendErrorCode = "SEND_RETRY_ERROR"
)

// SendError 发送错误
type SendError struct {
	Code    SendErrorCode          `json:"code"`
	Message string                 `json:"message"`
	Field   string                 `json:"field,omitempty"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// Error 实现error接口
func (e *SendError) Error() string {
	if e.Field != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Code, e.Field, e.Message)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// NewSendPlanValidationError 创建发送计划验证错误
func NewSendPlanValidationError(field, message string) *SendError {
	return &SendError{
		Code:    CodeSendPlanValidationError,
		Message: message,
		Field:   field,
	}
}

// NewSendPlanNotFoundError 创建发送计划未找到错误
func NewSendPlanNotFoundError(message string) *SendError {
	return &SendError{
		Code:    CodeSendPlanNotFoundError,
		Message: message,
	}
}

// NewSendPlanStateError 创建发送计划状态错误
func NewSendPlanStateError(message string) *SendError {
	return &SendError{
		Code:    CodeSendPlanStateError,
		Message: message,
	}
}

// NewSendPlanCreateFailedError 创建发送计划创建失败错误
func NewSendPlanCreateFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeSendPlanCreateFailedError,
		Message: message,
	}
}

// NewSendPlanUpdateFailedError 创建发送计划更新失败错误
func NewSendPlanUpdateFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeSendPlanUpdateFailedError,
		Message: message,
	}
}

// NewSendPlanDeleteFailedError 创建发送计划删除失败错误
func NewSendPlanDeleteFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeSendPlanDeleteFailedError,
		Message: message,
	}
}

// NewSendPlanQueryFailedError 创建发送计划查询失败错误
func NewSendPlanQueryFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeSendPlanQueryFailedError,
		Message: message,
	}
}

// NewSendBatchValidationError 创建发送批次验证错误
func NewSendBatchValidationError(field, message string) *SendError {
	return &SendError{
		Code:    CodeSendBatchValidationError,
		Message: message,
		Field:   field,
	}
}

// NewSendBatchNotFoundError 创建发送批次未找到错误
func NewSendBatchNotFoundError(message string) *SendError {
	return &SendError{
		Code:    CodeSendBatchNotFoundError,
		Message: message,
	}
}

// NewSendBatchStateError 创建发送批次状态错误
func NewSendBatchStateError(message string) *SendError {
	return &SendError{
		Code:    CodeSendBatchStateError,
		Message: message,
	}
}

// NewSendBatchCreateFailedError 创建发送批次创建失败错误
func NewSendBatchCreateFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeSendBatchCreateFailedError,
		Message: message,
	}
}

// NewSendBatchUpdateFailedError 创建发送批次更新失败错误
func NewSendBatchUpdateFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeSendBatchUpdateFailedError,
		Message: message,
	}
}

// NewSendBatchDeleteFailedError 创建发送批次删除失败错误
func NewSendBatchDeleteFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeSendBatchDeleteFailedError,
		Message: message,
	}
}

// NewSendBatchQueryFailedError 创建发送批次查询失败错误
func NewSendBatchQueryFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeSendBatchQueryFailedError,
		Message: message,
	}
}

// NewAudienceSnapshotValidationError 创建受众快照验证错误
func NewAudienceSnapshotValidationError(field, message string) *SendError {
	return &SendError{
		Code:    CodeAudienceSnapshotValidationError,
		Message: message,
		Field:   field,
	}
}

// NewAudienceSnapshotNotFoundError 创建受众快照未找到错误
func NewAudienceSnapshotNotFoundError(message string) *SendError {
	return &SendError{
		Code:    CodeAudienceSnapshotNotFoundError,
		Message: message,
	}
}

// NewAudienceSnapshotCreateFailedError 创建受众快照创建失败错误
func NewAudienceSnapshotCreateFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeAudienceSnapshotCreateFailedError,
		Message: message,
	}
}

// NewAudienceSnapshotUpdateFailedError 创建受众快照更新失败错误
func NewAudienceSnapshotUpdateFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeAudienceSnapshotUpdateFailedError,
		Message: message,
	}
}

// NewAudienceSnapshotDeleteFailedError 创建受众快照删除失败错误
func NewAudienceSnapshotDeleteFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeAudienceSnapshotDeleteFailedError,
		Message: message,
	}
}

// NewAudienceSnapshotQueryFailedError 创建受众快照查询失败错误
func NewAudienceSnapshotQueryFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeAudienceSnapshotQueryFailedError,
		Message: message,
	}
}

// NewSendRecordValidationError 创建发送记录验证错误
func NewSendRecordValidationError(field, message string) *SendError {
	return &SendError{
		Code:    CodeSendRecordValidationError,
		Message: message,
		Field:   field,
	}
}

// NewSendRecordNotFoundError 创建发送记录未找到错误
func NewSendRecordNotFoundError(message string) *SendError {
	return &SendError{
		Code:    CodeSendRecordNotFoundError,
		Message: message,
	}
}

// NewSendRecordCreateFailedError 创建发送记录创建失败错误
func NewSendRecordCreateFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeSendRecordCreateFailedError,
		Message: message,
	}
}

// NewSendRecordUpdateFailedError 创建发送记录更新失败错误
func NewSendRecordUpdateFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeSendRecordUpdateFailedError,
		Message: message,
	}
}

// NewSendRecordDeleteFailedError 创建发送记录删除失败错误
func NewSendRecordDeleteFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeSendRecordDeleteFailedError,
		Message: message,
	}
}

// NewSendRecordQueryFailedError 创建发送记录查询失败错误
func NewSendRecordQueryFailedError(message string) *SendError {
	return &SendError{
		Code:    CodeSendRecordQueryFailedError,
		Message: message,
	}
}

// NewSendScheduleError 创建发送调度错误
func NewSendScheduleError(message string) *SendError {
	return &SendError{
		Code:    CodeSendScheduleError,
		Message: message,
	}
}

// NewSendExecutionError 创建发送执行错误
func NewSendExecutionError(message string) *SendError {
	return &SendError{
		Code:    CodeSendExecutionError,
		Message: message,
	}
}

// NewSendThrottleError 创建发送限流错误
func NewSendThrottleError(message string) *SendError {
	return &SendError{
		Code:    CodeSendThrottleError,
		Message: message,
	}
}

// NewSendRetryError 创建发送重试错误
func NewSendRetryError(message string) *SendError {
	return &SendError{
		Code:    CodeSendRetryError,
		Message: message,
	}
}
