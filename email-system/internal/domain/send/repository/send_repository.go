package repository

import (
	"context"
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/send/entity"
)

// SendPlanRepository 发送计划仓储接口
type SendPlanRepository interface {
	// Create 创建发送计划
	Create(ctx context.Context, plan *entity.SendPlan) error

	// Update 更新发送计划
	Update(ctx context.Context, plan *entity.SendPlan) error

	// Delete 删除发送计划
	Delete(ctx context.Context, tenantID, planID int64) error

	// FindByID 根据ID查找发送计划
	FindByID(ctx context.Context, tenantID, planID int64) (*entity.SendPlan, error)

	// FindByStatus 根据状态查找发送计划
	FindByStatus(ctx context.Context, tenantID int64, status entity.PlanStatus, limit int) ([]*entity.SendPlan, error)

	// FindExpiredPlans 查找过期的发送计划
	FindExpiredPlans(ctx context.Context, limit int) ([]*entity.SendPlan, error)

	// FindScheduledPlans 查找需要调度的发送计划
	FindScheduledPlans(ctx context.Context, limit int) ([]*entity.SendPlan, error)

	// List 分页查询发送计划
	List(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.SendPlan, int64, error)

	// Count 统计发送计划数量
	Count(ctx context.Context, tenantID int64) (int64, error)

	// CountByStatus 根据状态统计发送计划数量
	CountByStatus(ctx context.Context, tenantID int64, status entity.PlanStatus) (int64, error)
}

// SendBatchRepository 发送批次仓储接口
type SendBatchRepository interface {
	// Create 创建发送批次
	Create(ctx context.Context, batch *entity.SendBatch) error

	// Update 更新发送批次
	Update(ctx context.Context, batch *entity.SendBatch) error

	// Delete 删除发送批次
	Delete(ctx context.Context, tenantID, batchID int64) error

	// FindByID 根据ID查找发送批次
	FindByID(ctx context.Context, tenantID, batchID int64) (*entity.SendBatch, error)

	// FindByPlanID 根据计划ID查找发送批次
	FindByPlanID(ctx context.Context, tenantID, planID int64) ([]*entity.SendBatch, error)

	// FindByStatus 根据状态查找发送批次
	FindByStatus(ctx context.Context, tenantID int64, status entity.BatchStatus, limit int) ([]*entity.SendBatch, error)

	// FindReadyBatches 查找准备发送的批次
	FindReadyBatches(ctx context.Context, limit int) ([]*entity.SendBatch, error)

	// FindExpiredBatches 查找过期的批次
	FindExpiredBatches(ctx context.Context, deadline time.Time, limit int) ([]*entity.SendBatch, error)

	// List 分页查询发送批次
	List(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.SendBatch, int64, error)

	// ListByPlanID 根据计划ID分页查询发送批次
	ListByPlanID(ctx context.Context, tenantID, planID int64, offset, limit int) ([]*entity.SendBatch, int64, error)

	// Count 统计发送批次数量
	Count(ctx context.Context, tenantID int64) (int64, error)

	// CountByPlanID 根据计划ID统计发送批次数量
	CountByPlanID(ctx context.Context, tenantID, planID int64) (int64, error)

	// CountByStatus 根据状态统计发送批次数量
	CountByStatus(ctx context.Context, tenantID int64, status entity.BatchStatus) (int64, error)
}

// AudienceSnapshotRepository 受众快照仓储接口
type AudienceSnapshotRepository interface {
	// Create 创建受众快照
	Create(ctx context.Context, snapshot *entity.AudienceSnapshot) error

	// Update 更新受众快照
	Update(ctx context.Context, snapshot *entity.AudienceSnapshot) error

	// Delete 删除受众快照
	Delete(ctx context.Context, tenantID, snapshotID int64) error

	// FindByID 根据ID查找受众快照
	FindByID(ctx context.Context, tenantID, snapshotID int64) (*entity.AudienceSnapshot, error)

	// FindByPlanID 根据计划ID查找受众快照
	FindByPlanID(ctx context.Context, tenantID, planID int64) (*entity.AudienceSnapshot, error)

	// List 分页查询受众快照
	List(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.AudienceSnapshot, int64, error)

	// ListByPlanID 根据计划ID分页查询受众快照
	ListByPlanID(ctx context.Context, tenantID, planID int64, offset, limit int) ([]*entity.AudienceSnapshot, int64, error)

	// Count 统计受众快照数量
	Count(ctx context.Context, tenantID int64) (int64, error)

	// CountByPlanID 根据计划ID统计受众快照数量
	CountByPlanID(ctx context.Context, tenantID, planID int64) (int64, error)
}

// SendRecordRepository 发送记录仓储接口
type SendRecordRepository interface {
	// Create 创建发送记录
	Create(ctx context.Context, record *entity.SendRecord) error

	// BatchCreate 批量创建发送记录
	BatchCreate(ctx context.Context, records []*entity.SendRecord) error

	// Update 更新发送记录
	Update(ctx context.Context, record *entity.SendRecord) error

	// BatchUpdate 批量更新发送记录
	BatchUpdate(ctx context.Context, records []*entity.SendRecord) error

	// Delete 删除发送记录
	Delete(ctx context.Context, tenantID, recordID int64) error

	// FindByID 根据ID查找发送记录
	FindByID(ctx context.Context, tenantID, recordID int64) (*entity.SendRecord, error)

	// FindByBatchID 根据批次ID查找发送记录
	FindByBatchID(ctx context.Context, tenantID, batchID int64) ([]*entity.SendRecord, error)

	// FindByPlanID 根据计划ID查找发送记录
	FindByPlanID(ctx context.Context, tenantID, planID int64) ([]*entity.SendRecord, error)

	// FindByStatus 根据状态查找发送记录
	FindByStatus(ctx context.Context, tenantID int64, status entity.SendStatus, limit int) ([]*entity.SendRecord, error)

	// FindPendingRecords 查找待发送记录
	FindPendingRecords(ctx context.Context, batchID int64, limit int) ([]*entity.SendRecord, error)

	// FindRetryableRecords 查找可重试的记录
	FindRetryableRecords(ctx context.Context, maxAttempts int, limit int) ([]*entity.SendRecord, error)

	// List 分页查询发送记录
	List(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.SendRecord, int64, error)

	// ListByBatchID 根据批次ID分页查询发送记录
	ListByBatchID(ctx context.Context, tenantID, batchID int64, offset, limit int) ([]*entity.SendRecord, int64, error)

	// ListByPlanID 根据计划ID分页查询发送记录
	ListByPlanID(ctx context.Context, tenantID, planID int64, offset, limit int) ([]*entity.SendRecord, int64, error)

	// Count 统计发送记录数量
	Count(ctx context.Context, tenantID int64) (int64, error)

	// CountByBatchID 根据批次ID统计发送记录数量
	CountByBatchID(ctx context.Context, tenantID, batchID int64) (int64, error)

	// CountByPlanID 根据计划ID统计发送记录数量
	CountByPlanID(ctx context.Context, tenantID, planID int64) (int64, error)

	// CountByStatus 根据状态统计发送记录数量
	CountByStatus(ctx context.Context, tenantID int64, status entity.SendStatus) (int64, error)

	// CountByBatchIDAndStatus 根据批次ID和状态统计发送记录数量
	CountByBatchIDAndStatus(ctx context.Context, tenantID, batchID int64, status entity.SendStatus) (int64, error)

	// GetSendStatistics 获取发送统计信息
	GetSendStatistics(ctx context.Context, tenantID, planID int64) (map[entity.SendStatus]int64, error)

	// GetBatchSendStatistics 获取批次发送统计信息
	GetBatchSendStatistics(ctx context.Context, tenantID, batchID int64) (map[entity.SendStatus]int64, error)
}
