package client

import (
	"context"
	"time"

	"gitee.com/heiyee/platforms/pkg/logiface"
)

// EmailSendRequest 邮件发送请求
type EmailSendRequest struct {
	// 基本信息
	TenantID   int64  `json:"tenant_id"`
	RequestID  string `json:"request_id"`
	TemplateID string `json:"template_id"`
	Priority   int    `json:"priority"`

	// 收件人信息
	ToEmail string `json:"to_email"`
	ToName  string `json:"to_name,omitempty"`

	// 发件人信息（可选，使用模板默认值）
	FromEmail string `json:"from_email,omitempty"`
	FromName  string `json:"from_name,omitempty"`

	// 邮件内容（可选，使用模板默认值）
	Subject string `json:"subject,omitempty"`

	// 模板变量
	Variables map[string]interface{} `json:"variables,omitempty"`

	// 发送选项
	SendAt    *time.Time `json:"send_at,omitempty"`    // 定时发送
	ExpiresAt *time.Time `json:"expires_at,omitempty"` // 过期时间

	// 回调配置
	CallbackURL string            `json:"callback_url,omitempty"`
	Metadata    map[string]string `json:"metadata,omitempty"`

	// 重试配置
	RetryPolicy *RetryPolicy `json:"retry_policy,omitempty"`
}

// EmailSendResponse 邮件发送响应
type EmailSendResponse struct {
	Success   bool   `json:"success"`
	MessageID string `json:"message_id,omitempty"`
	Error     string `json:"error,omitempty"`
	Code      string `json:"code,omitempty"`
}

// EmailBatchSendRequest 批量邮件发送请求
type EmailBatchSendRequest struct {
	// 基本信息
	TenantID  int64  `json:"tenant_id"`
	BatchID   string `json:"batch_id"`
	RequestID string `json:"request_id"`

	// 邮件列表
	Emails []*EmailSendRequest `json:"emails"`

	// 批次选项
	MaxConcurrency int        `json:"max_concurrency,omitempty"` // 最大并发数
	SendAt         *time.Time `json:"send_at,omitempty"`         // 定时发送
	ExpiresAt      *time.Time `json:"expires_at,omitempty"`      // 过期时间

	// 回调配置
	CallbackURL string            `json:"callback_url,omitempty"`
	Metadata    map[string]string `json:"metadata,omitempty"`

	// 重试配置
	RetryPolicy *RetryPolicy `json:"retry_policy,omitempty"`
}

// EmailBatchSendResponse 批量邮件发送响应
type EmailBatchSendResponse struct {
	Success      bool                 `json:"success"`
	BatchID      string               `json:"batch_id"`
	TotalCount   int                  `json:"total_count"`
	SuccessCount int                  `json:"success_count"`
	FailedCount  int                  `json:"failed_count"`
	Results      []*EmailSendResponse `json:"results,omitempty"`
	Error        string               `json:"error,omitempty"`
	Code         string               `json:"code,omitempty"`
}

// RetryPolicy 重试策略
type RetryPolicy struct {
	Mode             string   `json:"mode"`               // NONE|RETRY_LINEAR|RETRY_EXPONENTIAL
	MaxAttempts      int      `json:"max_attempts"`       // 最大重试次数
	InitialBackoffMs int      `json:"initial_backoff_ms"` // 初始退避时间(毫秒)
	MaxBackoffMs     int      `json:"max_backoff_ms"`     // 最大退避时间(毫秒)
	RetryOn          []string `json:"retry_on"`           // 重试的错误类型
}

// EmailStatusRequest 邮件状态查询请求
type EmailStatusRequest struct {
	TenantID  int64  `json:"tenant_id"`
	MessageID string `json:"message_id"`
}

// EmailStatusResponse 邮件状态查询响应
type EmailStatusResponse struct {
	Success   bool                `json:"success"`
	MessageID string              `json:"message_id"`
	Status    string              `json:"status"` // pending|sent|delivered|bounced|failed
	Events    []*EmailStatusEvent `json:"events,omitempty"`
	Error     string              `json:"error,omitempty"`
	Code      string              `json:"code,omitempty"`
}

// EmailStatusEvent 邮件状态事件
type EmailStatusEvent struct {
	Type      string                 `json:"type"` // sent|delivered|bounced|failed|opened|clicked
	Timestamp time.Time              `json:"timestamp"`
	Message   string                 `json:"message,omitempty"`
	Details   map[string]interface{} `json:"details,omitempty"`
}

// EmailTemplateRequest 邮件模板查询请求
type EmailTemplateRequest struct {
	TenantID   int64  `json:"tenant_id"`
	TemplateID string `json:"template_id"`
}

// EmailTemplateResponse 邮件模板查询响应
type EmailTemplateResponse struct {
	Success    bool     `json:"success"`
	TemplateID string   `json:"template_id"`
	Name       string   `json:"name"`
	Subject    string   `json:"subject"`
	FromEmail  string   `json:"from_email"`
	FromName   string   `json:"from_name"`
	Variables  []string `json:"variables,omitempty"`
	Error      string   `json:"error,omitempty"`
	Code       string   `json:"code,omitempty"`
}

// EmailClient Email服务客户端接口
type EmailClient interface {
	// SendEmail 发送单个邮件
	SendEmail(ctx context.Context, req *EmailSendRequest) (*EmailSendResponse, error)

	// SendBatchEmails 批量发送邮件
	SendBatchEmails(ctx context.Context, req *EmailBatchSendRequest) (*EmailBatchSendResponse, error)

	// GetEmailStatus 获取邮件状态
	GetEmailStatus(ctx context.Context, req *EmailStatusRequest) (*EmailStatusResponse, error)

	// GetTemplate 获取邮件模板信息
	GetTemplate(ctx context.Context, req *EmailTemplateRequest) (*EmailTemplateResponse, error)

	// CancelEmail 取消邮件发送（如果还未发送）
	CancelEmail(ctx context.Context, tenantID int64, messageID string) error

	// CancelBatch 取消批次发送（如果还未发送）
	CancelBatch(ctx context.Context, tenantID int64, batchID string) error
}

// EmailClientImpl Email服务客户端实现
type EmailClientImpl struct {
	logger logiface.Logger
	// TODO: 添加gRPC客户端连接
	// grpcClient email_pb.EmailServiceClient
}

// NewEmailClient 创建Email客户端
func NewEmailClient(logger logiface.Logger) EmailClient {
	return &EmailClientImpl{
		logger: logger,
	}
}

// SendEmail 发送单个邮件
func (c *EmailClientImpl) SendEmail(ctx context.Context, req *EmailSendRequest) (*EmailSendResponse, error) {
	c.logger.Info(ctx, "Sending email",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("request_id", req.RequestID),
		logiface.String("template_id", req.TemplateID),
		logiface.String("to_email", req.ToEmail))

	// TODO: 实现gRPC调用
	// 暂时返回模拟响应
	return &EmailSendResponse{
		Success:   true,
		MessageID: "mock_message_id_" + req.RequestID,
	}, nil
}

// SendBatchEmails 批量发送邮件
func (c *EmailClientImpl) SendBatchEmails(ctx context.Context, req *EmailBatchSendRequest) (*EmailBatchSendResponse, error) {
	c.logger.Info(ctx, "Sending batch emails",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("batch_id", req.BatchID),
		logiface.String("request_id", req.RequestID),
		logiface.Int("email_count", len(req.Emails)))

	// TODO: 实现gRPC调用
	// 暂时返回模拟响应
	return &EmailBatchSendResponse{
		Success:      true,
		BatchID:      req.BatchID,
		TotalCount:   len(req.Emails),
		SuccessCount: len(req.Emails),
		FailedCount:  0,
	}, nil
}

// GetEmailStatus 获取邮件状态
func (c *EmailClientImpl) GetEmailStatus(ctx context.Context, req *EmailStatusRequest) (*EmailStatusResponse, error) {
	c.logger.Debug(ctx, "Getting email status",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("message_id", req.MessageID))

	// TODO: 实现gRPC调用
	// 暂时返回模拟响应
	return &EmailStatusResponse{
		Success:   true,
		MessageID: req.MessageID,
		Status:    "sent",
		Events: []*EmailStatusEvent{
			{
				Type:      "sent",
				Timestamp: time.Now(),
				Message:   "Email sent successfully",
			},
		},
	}, nil
}

// GetTemplate 获取邮件模板信息
func (c *EmailClientImpl) GetTemplate(ctx context.Context, req *EmailTemplateRequest) (*EmailTemplateResponse, error) {
	c.logger.Debug(ctx, "Getting email template",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("template_id", req.TemplateID))

	// TODO: 实现gRPC调用
	// 暂时返回模拟响应
	return &EmailTemplateResponse{
		Success:    true,
		TemplateID: req.TemplateID,
		Name:       "Mock Template",
		Subject:    "Mock Subject",
		FromEmail:  "<EMAIL>",
		FromName:   "Mock Sender",
		Variables:  []string{"name", "email", "company"},
	}, nil
}

// CancelEmail 取消邮件发送
func (c *EmailClientImpl) CancelEmail(ctx context.Context, tenantID int64, messageID string) error {
	c.logger.Info(ctx, "Canceling email",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("message_id", messageID))

	// TODO: 实现gRPC调用
	return nil
}

// CancelBatch 取消批次发送
func (c *EmailClientImpl) CancelBatch(ctx context.Context, tenantID int64, batchID string) error {
	c.logger.Info(ctx, "Canceling batch",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("batch_id", batchID))

	// TODO: 实现gRPC调用
	return nil
}
