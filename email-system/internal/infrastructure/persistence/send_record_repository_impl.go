package persistence

import (
	"context"

	"gitee.com/heiyee/platforms/email-system/internal/domain/send/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/send/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gorm.io/gorm"
)

// SendRecordRepositoryImpl 发送记录仓储实现
type SendRecordRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewSendRecordRepository 创建发送记录仓储
func NewSendRecordRepository(db *gorm.DB, logger logiface.Logger) repository.SendRecordRepository {
	return &SendRecordRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建发送记录
func (r *SendRecordRepositoryImpl) Create(ctx context.Context, record *entity.SendRecord) error {
	if err := r.db.WithContext(ctx).Create(record).Error; err != nil {
		r.logger.Error(ctx, "Failed to create send record",
			logiface.Error(err),
			logiface.Int64("batch_id", record.BatchID))
		return err
	}
	return nil
}

// BatchCreate 批量创建发送记录
func (r *SendRecordRepositoryImpl) BatchCreate(ctx context.Context, records []*entity.SendRecord) error {
	if len(records) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).CreateInBatches(records, 1000).Error; err != nil {
		r.logger.Error(ctx, "Failed to batch create send records",
			logiface.Error(err),
			logiface.Int("record_count", len(records)))
		return err
	}
	return nil
}

// Update 更新发送记录
func (r *SendRecordRepositoryImpl) Update(ctx context.Context, record *entity.SendRecord) error {
	if err := r.db.WithContext(ctx).Save(record).Error; err != nil {
		r.logger.Error(ctx, "Failed to update send record",
			logiface.Error(err),
			logiface.Int64("record_id", record.RecordID))
		return err
	}
	return nil
}

// BatchUpdate 批量更新发送记录
func (r *SendRecordRepositoryImpl) BatchUpdate(ctx context.Context, records []*entity.SendRecord) error {
	if len(records) == 0 {
		return nil
	}

	// 使用事务批量更新
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, record := range records {
			if err := tx.Save(record).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// Delete 删除发送记录
func (r *SendRecordRepositoryImpl) Delete(ctx context.Context, tenantID, recordID int64) error {
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND record_id = ?", tenantID, recordID).
		Delete(&entity.SendRecord{})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to delete send record",
			logiface.Error(result.Error),
			logiface.Int64("record_id", recordID))
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.logger.Warn(ctx, "Send record not found for deletion",
			logiface.Int64("record_id", recordID))
	}

	return nil
}

// FindByID 根据ID查找发送记录
func (r *SendRecordRepositoryImpl) FindByID(ctx context.Context, tenantID, recordID int64) (*entity.SendRecord, error) {
	var record entity.SendRecord
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND record_id = ?", tenantID, recordID).
		First(&record).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find send record by ID",
			logiface.Error(err),
			logiface.Int64("record_id", recordID))
		return nil, err
	}

	return &record, nil
}

// FindByBatchID 根据批次ID查找发送记录
func (r *SendRecordRepositoryImpl) FindByBatchID(ctx context.Context, tenantID, batchID int64) ([]*entity.SendRecord, error) {
	var records []*entity.SendRecord
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND batch_id = ?", tenantID, batchID).
		Order("created_at ASC").
		Find(&records).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to find send records by batch ID",
			logiface.Error(err),
			logiface.Int64("batch_id", batchID))
		return nil, err
	}

	return records, nil
}

// FindByPlanID 根据计划ID查找发送记录
func (r *SendRecordRepositoryImpl) FindByPlanID(ctx context.Context, tenantID, planID int64) ([]*entity.SendRecord, error) {
	var records []*entity.SendRecord
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		Order("created_at ASC").
		Find(&records).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to find send records by plan ID",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return nil, err
	}

	return records, nil
}

// FindByStatus 根据状态查找发送记录
func (r *SendRecordRepositoryImpl) FindByStatus(ctx context.Context, tenantID int64, status entity.SendStatus, limit int) ([]*entity.SendRecord, error) {
	var records []*entity.SendRecord
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Order("created_at ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&records).Error
	if err != nil {
		r.logger.Error(ctx, "Failed to find send records by status",
			logiface.Error(err),
			logiface.String("status", string(status)))
		return nil, err
	}

	return records, nil
}

// FindPendingRecords 查找待发送记录
func (r *SendRecordRepositoryImpl) FindPendingRecords(ctx context.Context, batchID int64, limit int) ([]*entity.SendRecord, error) {
	var records []*entity.SendRecord
	query := r.db.WithContext(ctx).
		Where("batch_id = ? AND status = ?", batchID, entity.SendStatusPending).
		Order("created_at ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&records).Error
	if err != nil {
		r.logger.Error(ctx, "Failed to find pending send records",
			logiface.Error(err),
			logiface.Int64("batch_id", batchID))
		return nil, err
	}

	return records, nil
}

// FindRetryableRecords 查找可重试的记录
func (r *SendRecordRepositoryImpl) FindRetryableRecords(ctx context.Context, maxAttempts int, limit int) ([]*entity.SendRecord, error) {
	var records []*entity.SendRecord
	query := r.db.WithContext(ctx).
		Where("status = ? AND attempt_count < ?", entity.SendStatusFailed, maxAttempts).
		Order("last_attempt_at ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&records).Error
	if err != nil {
		r.logger.Error(ctx, "Failed to find retryable send records",
			logiface.Error(err),
			logiface.Int("max_attempts", maxAttempts))
		return nil, err
	}

	return records, nil
}

// List 分页查询发送记录
func (r *SendRecordRepositoryImpl) List(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.SendRecord, int64, error) {
	var records []*entity.SendRecord
	var total int64

	// 查询总数
	if err := r.db.WithContext(ctx).Model(&entity.SendRecord{}).
		Where("tenant_id = ?", tenantID).
		Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count send records",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, 0, err
	}

	// 查询数据
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&records).Error; err != nil {
		r.logger.Error(ctx, "Failed to list send records",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, 0, err
	}

	return records, total, nil
}

// ListByBatchID 根据批次ID分页查询发送记录
func (r *SendRecordRepositoryImpl) ListByBatchID(ctx context.Context, tenantID, batchID int64, offset, limit int) ([]*entity.SendRecord, int64, error) {
	var records []*entity.SendRecord
	var total int64

	// 查询总数
	if err := r.db.WithContext(ctx).Model(&entity.SendRecord{}).
		Where("tenant_id = ? AND batch_id = ?", tenantID, batchID).
		Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count send records by batch ID",
			logiface.Error(err),
			logiface.Int64("batch_id", batchID))
		return nil, 0, err
	}

	// 查询数据
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND batch_id = ?", tenantID, batchID).
		Order("created_at ASC").
		Offset(offset).
		Limit(limit).
		Find(&records).Error; err != nil {
		r.logger.Error(ctx, "Failed to list send records by batch ID",
			logiface.Error(err),
			logiface.Int64("batch_id", batchID))
		return nil, 0, err
	}

	return records, total, nil
}

// ListByPlanID 根据计划ID分页查询发送记录
func (r *SendRecordRepositoryImpl) ListByPlanID(ctx context.Context, tenantID, planID int64, offset, limit int) ([]*entity.SendRecord, int64, error) {
	var records []*entity.SendRecord
	var total int64

	// 查询总数
	if err := r.db.WithContext(ctx).Model(&entity.SendRecord{}).
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count send records by plan ID",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return nil, 0, err
	}

	// 查询数据
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		Order("created_at ASC").
		Offset(offset).
		Limit(limit).
		Find(&records).Error; err != nil {
		r.logger.Error(ctx, "Failed to list send records by plan ID",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return nil, 0, err
	}

	return records, total, nil
}

// Count 统计发送记录数量
func (r *SendRecordRepositoryImpl) Count(ctx context.Context, tenantID int64) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.SendRecord{}).
		Where("tenant_id = ?", tenantID).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to count send records",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return 0, err
	}

	return count, nil
}

// CountByBatchID 根据批次ID统计发送记录数量
func (r *SendRecordRepositoryImpl) CountByBatchID(ctx context.Context, tenantID, batchID int64) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.SendRecord{}).
		Where("tenant_id = ? AND batch_id = ?", tenantID, batchID).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to count send records by batch ID",
			logiface.Error(err),
			logiface.Int64("batch_id", batchID))
		return 0, err
	}

	return count, nil
}

// CountByPlanID 根据计划ID统计发送记录数量
func (r *SendRecordRepositoryImpl) CountByPlanID(ctx context.Context, tenantID, planID int64) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.SendRecord{}).
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to count send records by plan ID",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return 0, err
	}

	return count, nil
}

// CountByStatus 根据状态统计发送记录数量
func (r *SendRecordRepositoryImpl) CountByStatus(ctx context.Context, tenantID int64, status entity.SendStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.SendRecord{}).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to count send records by status",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("status", string(status)))
		return 0, err
	}

	return count, nil
}

// CountByBatchIDAndStatus 根据批次ID和状态统计发送记录数量
func (r *SendRecordRepositoryImpl) CountByBatchIDAndStatus(ctx context.Context, tenantID, batchID int64, status entity.SendStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.SendRecord{}).
		Where("tenant_id = ? AND batch_id = ? AND status = ?", tenantID, batchID, status).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to count send records by batch ID and status",
			logiface.Error(err),
			logiface.Int64("batch_id", batchID),
			logiface.String("status", string(status)))
		return 0, err
	}

	return count, nil
}

// GetSendStatistics 获取发送统计信息
func (r *SendRecordRepositoryImpl) GetSendStatistics(ctx context.Context, tenantID, planID int64) (map[entity.SendStatus]int64, error) {
	var results []struct {
		Status string
		Count  int64
	}

	err := r.db.WithContext(ctx).Model(&entity.SendRecord{}).
		Select("status, COUNT(*) as count").
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		Group("status").
		Scan(&results).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to get send statistics",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return nil, err
	}

	statistics := make(map[entity.SendStatus]int64)
	for _, result := range results {
		statistics[entity.SendStatus(result.Status)] = result.Count
	}

	return statistics, nil
}

// GetBatchSendStatistics 获取批次发送统计信息
func (r *SendRecordRepositoryImpl) GetBatchSendStatistics(ctx context.Context, tenantID, batchID int64) (map[entity.SendStatus]int64, error) {
	var results []struct {
		Status string
		Count  int64
	}

	err := r.db.WithContext(ctx).Model(&entity.SendRecord{}).
		Select("status, COUNT(*) as count").
		Where("tenant_id = ? AND batch_id = ?", tenantID, batchID).
		Group("status").
		Scan(&results).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to get batch send statistics",
			logiface.Error(err),
			logiface.Int64("batch_id", batchID))
		return nil, err
	}

	statistics := make(map[entity.SendStatus]int64)
	for _, result := range results {
		statistics[entity.SendStatus(result.Status)] = result.Count
	}

	return statistics, nil
}
