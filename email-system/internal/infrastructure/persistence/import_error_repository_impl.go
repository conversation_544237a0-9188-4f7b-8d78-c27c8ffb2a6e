package persistence

import (
	"context"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/repository"
	"time"

	"gorm.io/gorm"
)

// importErrorRepositoryImpl 导入错误仓储实现
type importErrorRepositoryImpl struct {
	db *gorm.DB
}

// NewImportErrorRepository 创建导入错误仓储实例
func NewImportErrorRepository(db *gorm.DB) repository.ImportErrorRepository {
	return &importErrorRepositoryImpl{
		db: db,
	}
}

// Create 创建错误记录
func (r *importErrorRepositoryImpl) Create(ctx context.Context, importError *entity.ImportError) error {
	return r.db.WithContext(ctx).Create(importError).Error
}

// BatchCreate 批量创建错误记录
func (r *importErrorRepositoryImpl) BatchCreate(ctx context.Context, errors []*entity.ImportError) error {
	if len(errors) == 0 {
		return nil
	}

	// 使用批量插入，每批最多1000条记录
	batchSize := 1000
	for i := 0; i < len(errors); i += batchSize {
		end := i + batchSize
		if end > len(errors) {
			end = len(errors)
		}

		if err := r.db.WithContext(ctx).CreateInBatches(errors[i:end], batchSize).Error; err != nil {
			return err
		}
	}

	return nil
}

// GetByJobID 根据任务ID获取错误列表
func (r *importErrorRepositoryImpl) GetByJobID(ctx context.Context, jobID, tenantID int64,
	limit, offset int) ([]*entity.ImportError, int64, error) {

	var errors []*entity.ImportError
	var total int64

	query := r.db.WithContext(ctx).
		Where("job_id = ? AND tenant_id = ?", jobID, tenantID)

	// 获取总数
	if err := query.Model(&entity.ImportError{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("row_number ASC").
		Limit(limit).Offset(offset).
		Find(&errors).Error

	return errors, total, err
}

// GetByJobAndBatch 根据任务ID和批次号获取错误列表
func (r *importErrorRepositoryImpl) GetByJobAndBatch(ctx context.Context, jobID, tenantID int64, batchNo int,
	limit, offset int) ([]*entity.ImportError, int64, error) {

	var errors []*entity.ImportError
	var total int64

	query := r.db.WithContext(ctx).
		Where("job_id = ? AND tenant_id = ? AND batch_no = ?", jobID, tenantID, batchNo)

	// 获取总数
	if err := query.Model(&entity.ImportError{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("row_number ASC").
		Limit(limit).Offset(offset).
		Find(&errors).Error

	return errors, total, err
}

// GetErrorSummary 获取错误汇总统计
func (r *importErrorRepositoryImpl) GetErrorSummary(ctx context.Context, jobID, tenantID int64) (map[string]int64, error) {
	var results []struct {
		ErrorType string `json:"error_type"`
		Count     int64  `json:"count"`
	}

	err := r.db.WithContext(ctx).
		Model(&entity.ImportError{}).
		Select("error_type, COUNT(*) as count").
		Where("job_id = ? AND tenant_id = ?", jobID, tenantID).
		Group("error_type").
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	summary := make(map[string]int64)
	for _, result := range results {
		summary[result.ErrorType] = result.Count
	}

	return summary, nil
}

// GetRetryableErrors 获取可重试的错误列表
func (r *importErrorRepositoryImpl) GetRetryableErrors(ctx context.Context, jobID, tenantID int64,
	maxRetries int) ([]*entity.ImportError, error) {

	var errors []*entity.ImportError

	err := r.db.WithContext(ctx).
		Where("job_id = ? AND tenant_id = ? AND retry_count < ? AND error_type != ?",
			jobID, tenantID, maxRetries, entity.ImportErrorTypeValidation).
		Order("row_number ASC").
		Find(&errors).Error

	return errors, err
}

// UpdateRetryCount 更新重试次数
func (r *importErrorRepositoryImpl) UpdateRetryCount(ctx context.Context, id, tenantID int64) error {
	return r.db.WithContext(ctx).
		Model(&entity.ImportError{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Updates(map[string]interface{}{
			"retry_count": gorm.Expr("retry_count + 1"),
			"updated_at":  time.Now(),
		}).Error
}

// Delete 删除错误记录
func (r *importErrorRepositoryImpl) Delete(ctx context.Context, id, tenantID int64) error {
	return r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Delete(&entity.ImportError{}).Error
}

// DeleteByJobID 删除指定任务的所有错误记录
func (r *importErrorRepositoryImpl) DeleteByJobID(ctx context.Context, jobID, tenantID int64) error {
	return r.db.WithContext(ctx).
		Where("job_id = ? AND tenant_id = ?", jobID, tenantID).
		Delete(&entity.ImportError{}).Error
}
