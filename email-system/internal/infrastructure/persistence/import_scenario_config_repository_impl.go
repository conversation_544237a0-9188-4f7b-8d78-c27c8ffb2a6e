package persistence

import (
	"context"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/repository"
	"time"

	"gorm.io/gorm"
)

// importScenarioConfigRepositoryImpl 导入场景配置仓储实现
type importScenarioConfigRepositoryImpl struct {
	db *gorm.DB
}

// NewImportScenarioConfigRepository 创建导入场景配置仓储实例
func NewImportScenarioConfigRepository(db *gorm.DB) repository.ImportScenarioConfigRepository {
	return &importScenarioConfigRepositoryImpl{
		db: db,
	}
}

// Create 创建场景配置
func (r *importScenarioConfigRepositoryImpl) Create(ctx context.Context, config *entity.ImportScenarioConfig) error {
	return r.db.WithContext(ctx).Create(config).Error
}

// GetByCode 根据场景代码获取配置
func (r *importScenarioConfigRepositoryImpl) GetByCode(ctx context.Context, code string, tenantID int64) (*entity.ImportScenarioConfig, error) {
	var config entity.ImportScenarioConfig
	err := r.db.WithContext(ctx).
		Where("scenario_code = ? AND tenant_id = ?", code, tenantID).
		First(&config).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, entity.NewImportScenarioNotFoundError(code, tenantID)
		}
		return nil, err
	}

	return &config, nil
}

// GetByID 根据ID获取配置
func (r *importScenarioConfigRepositoryImpl) GetByID(ctx context.Context, id, tenantID int64) (*entity.ImportScenarioConfig, error) {
	var config entity.ImportScenarioConfig
	err := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		First(&config).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, entity.NewImportScenarioNotFoundError("", tenantID)
		}
		return nil, err
	}

	return &config, nil
}

// Update 更新场景配置
func (r *importScenarioConfigRepositoryImpl) Update(ctx context.Context, config *entity.ImportScenarioConfig) error {
	return r.db.WithContext(ctx).Save(config).Error
}

// Delete 删除场景配置
func (r *importScenarioConfigRepositoryImpl) Delete(ctx context.Context, id, tenantID int64) error {
	return r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Delete(&entity.ImportScenarioConfig{}).Error
}

// List 分页查询场景配置列表
func (r *importScenarioConfigRepositoryImpl) List(ctx context.Context, tenantID int64, status entity.ScenarioStatus,
	limit, offset int) ([]*entity.ImportScenarioConfig, int64, error) {

	var configs []*entity.ImportScenarioConfig
	var total int64

	query := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID)

	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	if err := query.Model(&entity.ImportScenarioConfig{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("created_at DESC").
		Limit(limit).Offset(offset).
		Find(&configs).Error

	return configs, total, err
}

// ListActive 获取激活的场景配置列表
func (r *importScenarioConfigRepositoryImpl) ListActive(ctx context.Context, tenantID int64) ([]*entity.ImportScenarioConfig, error) {
	var configs []*entity.ImportScenarioConfig

	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, entity.ScenarioStatusActive).
		Order("scenario_name ASC").
		Find(&configs).Error

	return configs, err
}

// Activate 激活场景配置
func (r *importScenarioConfigRepositoryImpl) Activate(ctx context.Context, id, tenantID int64) error {
	return r.db.WithContext(ctx).
		Model(&entity.ImportScenarioConfig{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Updates(map[string]interface{}{
			"status":     entity.ScenarioStatusActive,
			"updated_at": time.Now(),
		}).Error
}

// Deactivate 停用场景配置
func (r *importScenarioConfigRepositoryImpl) Deactivate(ctx context.Context, id, tenantID int64) error {
	return r.db.WithContext(ctx).
		Model(&entity.ImportScenarioConfig{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Updates(map[string]interface{}{
			"status":     entity.ScenarioStatusInactive,
			"updated_at": time.Now(),
		}).Error
}
