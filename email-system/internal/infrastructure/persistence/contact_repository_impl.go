package persistence

import (
	"context"
	"fmt"
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/contact/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/contact/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gorm.io/gorm"
)

// ContactRepositoryImpl 联系人仓储实现
type ContactRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewContactRepositoryImpl 创建联系人仓储实现
func NewContactRepositoryImpl(db *gorm.DB, logger logiface.Logger) repository.ContactRepository {
	return &ContactRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建联系人
func (r *ContactRepositoryImpl) Create(ctx context.Context, contact *entity.Contact) error {
	if err := r.db.WithContext(ctx).Create(contact).Error; err != nil {
		r.logger.Error(ctx, "Failed to create contact",
			logiface.Error(err),
			logiface.String("email", contact.Email))
		return err
	}
	return nil
}

// Update 更新联系人
func (r *ContactRepositoryImpl) Update(ctx context.Context, contact *entity.Contact) error {
	contact.UpdatedAt = time.Now()
	if err := r.db.WithContext(ctx).Save(contact).Error; err != nil {
		r.logger.Error(ctx, "Failed to update contact",
			logiface.Error(err),
			logiface.Int64("contact_id", contact.ID))
		return err
	}
	return nil
}

// Delete 删除联系人
func (r *ContactRepositoryImpl) Delete(ctx context.Context, tenantID, contactID int64) error {
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, contactID).
		Delete(&entity.Contact{})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to delete contact",
			logiface.Error(result.Error),
			logiface.Int64("contact_id", contactID))
		return result.Error
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("contact not found")
	}

	return nil
}

// FindByID 根据ID查找联系人
func (r *ContactRepositoryImpl) FindByID(ctx context.Context, tenantID, contactID int64) (*entity.Contact, error) {
	var contact entity.Contact
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, contactID).
		First(&contact).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find contact by ID",
			logiface.Error(err),
			logiface.Int64("contact_id", contactID))
		return nil, err
	}

	return &contact, nil
}

// FindByUnsubscribeToken 根据退订令牌查找联系人
func (r *ContactRepositoryImpl) FindByUnsubscribeToken(ctx context.Context, tenantID int64, token string) (*entity.Contact, error) {
	var contact entity.Contact
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND unsubscribe_token = ?", tenantID, token).
		First(&contact).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find contact by unsubscribe token",
			logiface.Error(err),
			logiface.String("token", token))
		return nil, err
	}

	return &contact, nil
}

// FindActiveContacts 查找所有活跃联系人
func (r *ContactRepositoryImpl) FindActiveContacts(ctx context.Context, tenantID int64) ([]*entity.Contact, error) {
	var contacts []*entity.Contact
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, entity.ContactStatusActive).
		Find(&contacts).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to find active contacts",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, err
	}

	return contacts, nil
}

// FindActiveContactsWithLimit 查找活跃联系人（限制数量）
func (r *ContactRepositoryImpl) FindActiveContactsWithLimit(ctx context.Context, tenantID int64, limit int) ([]*entity.Contact, error) {
	var contacts []*entity.Contact
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, entity.ContactStatusActive).
		Limit(limit).
		Find(&contacts).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to find active contacts with limit",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int("limit", limit))
		return nil, err
	}

	return contacts, nil
}

// CountActiveContacts 统计活跃联系人数量
func (r *ContactRepositoryImpl) CountActiveContacts(ctx context.Context, tenantID int64) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.Contact{}).
		Where("tenant_id = ? AND status = ?", tenantID, entity.ContactStatusActive).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to count active contacts",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return 0, err
	}

	return count, nil
}

// FindByEmail 根据邮箱查找联系人
func (r *ContactRepositoryImpl) FindByEmail(ctx context.Context, tenantID int64, email string) (*entity.Contact, error) {
	var contact entity.Contact
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND email = ?", tenantID, email).
		First(&contact).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find contact by email",
			logiface.Error(err),
			logiface.String("email", email))
		return nil, err
	}

	return &contact, nil
}

// FindByIDs 根据ID列表批量查找联系人
func (r *ContactRepositoryImpl) FindByIDs(ctx context.Context, tenantID int64, contactIDs []int64) ([]*entity.Contact, error) {
	if len(contactIDs) == 0 {
		return []*entity.Contact{}, nil
	}

	var contacts []*entity.Contact
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id IN ?", tenantID, contactIDs).
		Find(&contacts).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to find contacts by IDs",
			logiface.Error(err),
			logiface.Any("contact_ids", contactIDs))
		return nil, err
	}

	return contacts, nil
}

// Search 搜索联系人
func (r *ContactRepositoryImpl) Search(ctx context.Context, params *repository.SearchParams) (*repository.SearchResult, error) {
	query := r.db.WithContext(ctx).Model(&entity.Contact{}).
		Where("tenant_id = ?", params.TenantID)

	// 应用过滤条件
	query = r.applyFilters(query, params.Filters)

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count contacts",
			logiface.Error(err),
			logiface.Int64("tenant_id", params.TenantID))
		return nil, err
	}

	// 应用排序
	if params.Sort != nil && params.Sort.Field != "" {
		order := "DESC"
		if params.Sort.Order == "asc" {
			order = "ASC"
		}
		query = query.Order(fmt.Sprintf("%s %s", params.Sort.Field, order))
	} else {
		query = query.Order("created_at DESC")
	}

	// 应用分页
	offset := params.GetOffset()
	limit := params.GetLimit()
	query = query.Offset(offset).Limit(limit)

	// 执行查询
	var contacts []*entity.Contact
	if err := query.Find(&contacts).Error; err != nil {
		r.logger.Error(ctx, "Failed to search contacts",
			logiface.Error(err),
			logiface.Int64("tenant_id", params.TenantID))
		return nil, err
	}

	// 计算总页数
	totalPages := int(total) / params.GetLimit()
	if int(total)%params.GetLimit() > 0 {
		totalPages++
	}

	return &repository.SearchResult{
		Contacts:   contacts,
		Total:      total,
		Page:       params.Page,
		Size:       params.GetLimit(),
		TotalPages: totalPages,
	}, nil
}

// Count 统计联系人数量
func (r *ContactRepositoryImpl) Count(ctx context.Context, tenantID int64, filters *repository.SearchFilters) (int64, error) {
	query := r.db.WithContext(ctx).Model(&entity.Contact{}).
		Where("tenant_id = ?", tenantID)

	// 应用过滤条件
	query = r.applyFilters(query, filters)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to count contacts",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return 0, err
	}

	return count, nil
}

// ExistsByEmail 检查邮箱是否存在
func (r *ContactRepositoryImpl) ExistsByEmail(ctx context.Context, tenantID int64, email string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.Contact{}).
		Where("tenant_id = ? AND email = ?", tenantID, email).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to check email existence",
			logiface.Error(err),
			logiface.String("email", email))
		return false, err
	}

	return count > 0, nil
}

// BatchCreate 批量创建联系人
func (r *ContactRepositoryImpl) BatchCreate(ctx context.Context, contacts []*entity.Contact) error {
	if len(contacts) == 0 {
		return nil
	}

	// 使用事务批量创建
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.CreateInBatches(contacts, 100).Error; err != nil {
			r.logger.Error(ctx, "Failed to batch create contacts",
				logiface.Error(err),
				logiface.Int("count", len(contacts)))
			return err
		}
		return nil
	})
}

// BatchUpdate 批量更新联系人
func (r *ContactRepositoryImpl) BatchUpdate(ctx context.Context, contacts []*entity.Contact) error {
	if len(contacts) == 0 {
		return nil
	}

	// 使用事务批量更新
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, contact := range contacts {
			contact.UpdatedAt = time.Now()
			if err := tx.Save(contact).Error; err != nil {
				r.logger.Error(ctx, "Failed to update contact in batch",
					logiface.Error(err),
					logiface.Int64("contact_id", contact.ID))
				return err
			}
		}
		return nil
	})
}

// UpdateStatus 更新联系人状态
func (r *ContactRepositoryImpl) UpdateStatus(ctx context.Context, tenantID int64, contactIDs []int64, status entity.ContactStatus) error {
	if len(contactIDs) == 0 {
		return nil
	}

	result := r.db.WithContext(ctx).Model(&entity.Contact{}).
		Where("tenant_id = ? AND id IN ?", tenantID, contactIDs).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to update contact status",
			logiface.Error(result.Error),
			logiface.Any("contact_ids", contactIDs),
			logiface.String("status", string(status)))
		return result.Error
	}

	return nil
}

// GetContactsByTags 根据标签获取联系人
func (r *ContactRepositoryImpl) GetContactsByTags(ctx context.Context, tenantID int64, tagIDs []int64, params *repository.SearchParams) (*repository.SearchResult, error) {
	// TODO: 实现根据标签获取联系人的逻辑
	// 这需要联系人标签关联表的支持
	return &repository.SearchResult{
		Contacts:   []*entity.Contact{},
		Total:      0,
		Page:       params.Page,
		Size:       params.GetLimit(),
		TotalPages: 0,
	}, nil
}

// GetContactsByLists 根据列表获取联系人
func (r *ContactRepositoryImpl) GetContactsByLists(ctx context.Context, tenantID int64, listIDs []int64, params *repository.SearchParams) (*repository.SearchResult, error) {
	// TODO: 实现根据列表获取联系人的逻辑
	// 这需要列表成员关联表的支持
	return &repository.SearchResult{
		Contacts:   []*entity.Contact{},
		Total:      0,
		Page:       params.Page,
		Size:       params.GetLimit(),
		TotalPages: 0,
	}, nil
}

// applyFilters 应用过滤条件
func (r *ContactRepositoryImpl) applyFilters(query *gorm.DB, filters *repository.SearchFilters) *gorm.DB {
	if filters == nil {
		return query
	}

	// 状态过滤
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}

	// 邮箱过滤
	if filters.Email != "" {
		query = query.Where("email = ?", filters.Email)
	}

	// 偏好语言过滤
	if filters.PreferredLanguage != "" {
		query = query.Where("preferred_language = ?", filters.PreferredLanguage)
	}

	// 国家代码过滤
	if filters.CountryCode != "" {
		query = query.Where("country_code = ?", filters.CountryCode)
	}

	// 关键词搜索
	if filters.Keyword != "" {
		keyword := "%" + filters.Keyword + "%"
		query = query.Where("email LIKE ? OR notes LIKE ?", keyword, keyword)
	}

	// 排除联系人ID
	if len(filters.ExcludeContactIDs) > 0 {
		query = query.Where("id NOT IN ?", filters.ExcludeContactIDs)
	}

	// 时间范围过滤
	if filters.CreatedAtRange != nil {
		if filters.CreatedAtRange.Start != nil && *filters.CreatedAtRange.Start != "" {
			query = query.Where("created_at >= ?", *filters.CreatedAtRange.Start)
		}
		if filters.CreatedAtRange.End != nil && *filters.CreatedAtRange.End != "" {
			query = query.Where("created_at <= ?", *filters.CreatedAtRange.End)
		}
	}

	// 自定义属性过滤
	if len(filters.Attributes) > 0 {
		for key, value := range filters.Attributes {
			// 使用JSON查询语法
			query = query.Where("JSON_EXTRACT(attributes, ?) = ?", "$."+key, value)
		}
	}

	return query
}
