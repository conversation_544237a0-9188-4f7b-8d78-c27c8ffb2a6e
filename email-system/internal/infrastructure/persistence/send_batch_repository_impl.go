package persistence

import (
	"context"
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/send/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/send/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gorm.io/gorm"
)

// SendBatchRepositoryImpl 发送批次仓储实现
type SendBatchRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewSendBatchRepository 创建发送批次仓储
func NewSendBatchRepository(db *gorm.DB, logger logiface.Logger) repository.SendBatchRepository {
	return &SendBatchRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建发送批次
func (r *SendBatchRepositoryImpl) Create(ctx context.Context, batch *entity.SendBatch) error {
	if err := r.db.WithContext(ctx).Create(batch).Error; err != nil {
		r.logger.Error(ctx, "Failed to create send batch",
			logiface.Error(err),
			logiface.Int64("plan_id", batch.PlanID))
		return err
	}
	return nil
}

// Update 更新发送批次
func (r *SendBatchRepositoryImpl) Update(ctx context.Context, batch *entity.SendBatch) error {
	if err := r.db.WithContext(ctx).Save(batch).Error; err != nil {
		r.logger.Error(ctx, "Failed to update send batch",
			logiface.Error(err),
			logiface.Int64("batch_id", batch.BatchID))
		return err
	}
	return nil
}

// Delete 删除发送批次
func (r *SendBatchRepositoryImpl) Delete(ctx context.Context, tenantID, batchID int64) error {
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND batch_id = ?", tenantID, batchID).
		Delete(&entity.SendBatch{})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to delete send batch",
			logiface.Error(result.Error),
			logiface.Int64("batch_id", batchID))
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.logger.Warn(ctx, "Send batch not found for deletion",
			logiface.Int64("batch_id", batchID))
	}

	return nil
}

// FindByID 根据ID查找发送批次
func (r *SendBatchRepositoryImpl) FindByID(ctx context.Context, tenantID, batchID int64) (*entity.SendBatch, error) {
	var batch entity.SendBatch
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND batch_id = ?", tenantID, batchID).
		First(&batch).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find send batch by ID",
			logiface.Error(err),
			logiface.Int64("batch_id", batchID))
		return nil, err
	}

	return &batch, nil
}

// FindByPlanID 根据计划ID查找发送批次
func (r *SendBatchRepositoryImpl) FindByPlanID(ctx context.Context, tenantID, planID int64) ([]*entity.SendBatch, error) {
	var batches []*entity.SendBatch
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		Order("send_at ASC").
		Find(&batches).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to find send batches by plan ID",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return nil, err
	}

	return batches, nil
}

// FindByStatus 根据状态查找发送批次
func (r *SendBatchRepositoryImpl) FindByStatus(ctx context.Context, tenantID int64, status entity.BatchStatus, limit int) ([]*entity.SendBatch, error) {
	var batches []*entity.SendBatch
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Order("send_at ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&batches).Error
	if err != nil {
		r.logger.Error(ctx, "Failed to find send batches by status",
			logiface.Error(err),
			logiface.String("status", string(status)))
		return nil, err
	}

	return batches, nil
}

// FindReadyBatches 查找准备发送的批次
func (r *SendBatchRepositoryImpl) FindReadyBatches(ctx context.Context, limit int) ([]*entity.SendBatch, error) {
	var batches []*entity.SendBatch
	now := time.Now()

	query := r.db.WithContext(ctx).
		Where("status = ? AND send_at <= ?", entity.BatchStatusEnqueued, now).
		Order("priority DESC, send_at ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&batches).Error
	if err != nil {
		r.logger.Error(ctx, "Failed to find ready send batches", logiface.Error(err))
		return nil, err
	}

	return batches, nil
}

// FindExpiredBatches 查找过期的批次
func (r *SendBatchRepositoryImpl) FindExpiredBatches(ctx context.Context, deadline time.Time, limit int) ([]*entity.SendBatch, error) {
	var batches []*entity.SendBatch

	query := r.db.WithContext(ctx).
		Where("send_at < ? AND status IN (?)",
			deadline, []entity.BatchStatus{entity.BatchStatusPending, entity.BatchStatusEnqueued}).
		Order("send_at ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&batches).Error
	if err != nil {
		r.logger.Error(ctx, "Failed to find expired send batches", logiface.Error(err))
		return nil, err
	}

	return batches, nil
}

// List 分页查询发送批次
func (r *SendBatchRepositoryImpl) List(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.SendBatch, int64, error) {
	var batches []*entity.SendBatch
	var total int64

	// 查询总数
	if err := r.db.WithContext(ctx).Model(&entity.SendBatch{}).
		Where("tenant_id = ?", tenantID).
		Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count send batches",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, 0, err
	}

	// 查询数据
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&batches).Error; err != nil {
		r.logger.Error(ctx, "Failed to list send batches",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, 0, err
	}

	return batches, total, nil
}

// ListByPlanID 根据计划ID分页查询发送批次
func (r *SendBatchRepositoryImpl) ListByPlanID(ctx context.Context, tenantID, planID int64, offset, limit int) ([]*entity.SendBatch, int64, error) {
	var batches []*entity.SendBatch
	var total int64

	// 查询总数
	if err := r.db.WithContext(ctx).Model(&entity.SendBatch{}).
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count send batches by plan ID",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return nil, 0, err
	}

	// 查询数据
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		Order("send_at ASC").
		Offset(offset).
		Limit(limit).
		Find(&batches).Error; err != nil {
		r.logger.Error(ctx, "Failed to list send batches by plan ID",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return nil, 0, err
	}

	return batches, total, nil
}

// Count 统计发送批次数量
func (r *SendBatchRepositoryImpl) Count(ctx context.Context, tenantID int64) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.SendBatch{}).
		Where("tenant_id = ?", tenantID).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to count send batches",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return 0, err
	}

	return count, nil
}

// CountByPlanID 根据计划ID统计发送批次数量
func (r *SendBatchRepositoryImpl) CountByPlanID(ctx context.Context, tenantID, planID int64) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.SendBatch{}).
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to count send batches by plan ID",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return 0, err
	}

	return count, nil
}

// CountByStatus 根据状态统计发送批次数量
func (r *SendBatchRepositoryImpl) CountByStatus(ctx context.Context, tenantID int64, status entity.BatchStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.SendBatch{}).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to count send batches by status",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("status", string(status)))
		return 0, err
	}

	return count, nil
}
