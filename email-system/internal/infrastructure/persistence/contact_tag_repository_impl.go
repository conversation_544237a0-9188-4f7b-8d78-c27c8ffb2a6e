package persistence

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/email-system/internal/domain/tag/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/tag/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gorm.io/gorm"
)

// ContactTagRepositoryImpl 联系人标签关联仓储实现
type ContactTagRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewContactTagRepositoryImpl 创建联系人标签关联仓储实现
func NewContactTagRepositoryImpl(db *gorm.DB, logger logiface.Logger) repository.ContactTagRepository {
	return &ContactTagRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// AssignTag 为联系人分配标签
func (r *ContactTagRepositoryImpl) AssignTag(ctx context.Context, contactTag *entity.ContactTag) error {
	if err := r.db.WithContext(ctx).Create(contactTag).Error; err != nil {
		r.logger.Error(ctx, "Failed to assign tag",
			logiface.Error(err),
			logiface.Int64("contact_id", contactTag.ContactID),
			logiface.Int64("tag_id", contactTag.TagID))
		return err
	}
	return nil
}

// UnassignTag 取消联系人标签分配
func (r *ContactTagRepositoryImpl) UnassignTag(ctx context.Context, tenantID, contactID, tagID int64) error {
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND contact_id = ? AND tag_id = ?", tenantID, contactID, tagID).
		Delete(&entity.ContactTag{})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to unassign tag",
			logiface.Error(result.Error),
			logiface.Int64("contact_id", contactID),
			logiface.Int64("tag_id", tagID))
		return result.Error
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("contact tag assignment not found")
	}

	return nil
}

// BatchAssignTags 批量为联系人分配标签
func (r *ContactTagRepositoryImpl) BatchAssignTags(ctx context.Context, contactTags []*entity.ContactTag) error {
	if len(contactTags) == 0 {
		return nil
	}

	// 使用事务批量创建
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.CreateInBatches(contactTags, 100).Error; err != nil {
			r.logger.Error(ctx, "Failed to batch assign tags",
				logiface.Error(err),
				logiface.Int("count", len(contactTags)))
			return err
		}
		return nil
	})
}

// BatchUnassignTags 批量取消联系人标签分配
func (r *ContactTagRepositoryImpl) BatchUnassignTags(ctx context.Context, tenantID int64, contactIDs []int64, tagIDs []int64) error {
	if len(contactIDs) == 0 || len(tagIDs) == 0 {
		return nil
	}

	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND contact_id IN ? AND tag_id IN ?", tenantID, contactIDs, tagIDs).
		Delete(&entity.ContactTag{})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to batch unassign tags",
			logiface.Error(result.Error),
			logiface.Any("contact_ids", contactIDs),
			logiface.Any("tag_ids", tagIDs))
		return result.Error
	}

	return nil
}

// GetContactTags 获取联系人的标签列表
func (r *ContactTagRepositoryImpl) GetContactTags(ctx context.Context, tenantID, contactID int64) ([]*entity.Tag, error) {
	var tags []*entity.Tag
	err := r.db.WithContext(ctx).
		Table("tags").
		Joins("INNER JOIN contact_tags ON tags.id = contact_tags.tag_id").
		Where("contact_tags.tenant_id = ? AND contact_tags.contact_id = ?", tenantID, contactID).
		Find(&tags).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to get contact tags",
			logiface.Error(err),
			logiface.Int64("contact_id", contactID))
		return nil, err
	}

	return tags, nil
}

// GetTagContacts 获取标签下的联系人ID列表
func (r *ContactTagRepositoryImpl) GetTagContacts(ctx context.Context, tenantID, tagID int64, params *repository.ContactListParams) (*repository.ContactListResult, error) {
	query := r.db.WithContext(ctx).Model(&entity.ContactTag{}).
		Where("tenant_id = ? AND tag_id = ?", tenantID, tagID)

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count tag contacts",
			logiface.Error(err),
			logiface.Int64("tag_id", tagID))
		return nil, err
	}

	// 应用分页
	offset := params.GetOffset()
	limit := params.GetLimit()
	query = query.Offset(offset).Limit(limit)

	// 执行查询
	var contactTags []entity.ContactTag
	if err := query.Find(&contactTags).Error; err != nil {
		r.logger.Error(ctx, "Failed to get tag contacts",
			logiface.Error(err),
			logiface.Int64("tag_id", tagID))
		return nil, err
	}

	// 提取联系人ID
	contactIDs := make([]int64, len(contactTags))
	for i, ct := range contactTags {
		contactIDs[i] = ct.ContactID
	}

	// 计算总页数
	totalPages := int(total) / params.GetLimit()
	if int(total)%params.GetLimit() > 0 {
		totalPages++
	}

	return &repository.ContactListResult{
		ContactIDs: contactIDs,
		Total:      total,
		Page:       params.Page,
		Size:       params.GetLimit(),
		TotalPages: totalPages,
	}, nil
}

// CountTagContacts 统计标签下的联系人数量
func (r *ContactTagRepositoryImpl) CountTagContacts(ctx context.Context, tenantID, tagID int64) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.ContactTag{}).
		Where("tenant_id = ? AND tag_id = ?", tenantID, tagID).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to count tag contacts",
			logiface.Error(err),
			logiface.Int64("tag_id", tagID))
		return 0, err
	}

	return count, nil
}

// ExistsAssignment 检查联系人标签分配是否存在
func (r *ContactTagRepositoryImpl) ExistsAssignment(ctx context.Context, tenantID, contactID, tagID int64) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.ContactTag{}).
		Where("tenant_id = ? AND contact_id = ? AND tag_id = ?", tenantID, contactID, tagID).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to check tag assignment existence",
			logiface.Error(err),
			logiface.Int64("contact_id", contactID),
			logiface.Int64("tag_id", tagID))
		return false, err
	}

	return count > 0, nil
}

// GetContactsByTags 根据标签获取联系人ID列表
func (r *ContactTagRepositoryImpl) GetContactsByTags(ctx context.Context, tenantID int64, tagIDs []int64, operator repository.TagOperator) ([]int64, error) {
	if len(tagIDs) == 0 {
		return []int64{}, nil
	}

	var contactIDs []int64

	switch operator {
	case repository.TagOperatorAnd:
		// 交集：联系人必须拥有所有指定的标签
		query := r.db.WithContext(ctx).
			Table("contact_tags").
			Select("contact_id").
			Where("tenant_id = ? AND tag_id IN ?", tenantID, tagIDs).
			Group("contact_id").
			Having("COUNT(DISTINCT tag_id) = ?", len(tagIDs))

		if err := query.Pluck("contact_id", &contactIDs).Error; err != nil {
			r.logger.Error(ctx, "Failed to get contacts by tags (AND)",
				logiface.Error(err),
				logiface.Any("tag_ids", tagIDs))
			return nil, err
		}

	case repository.TagOperatorOr:
		// 并集：联系人拥有任意一个指定的标签
		query := r.db.WithContext(ctx).
			Table("contact_tags").
			Select("DISTINCT contact_id").
			Where("tenant_id = ? AND tag_id IN ?", tenantID, tagIDs)

		if err := query.Pluck("contact_id", &contactIDs).Error; err != nil {
			r.logger.Error(ctx, "Failed to get contacts by tags (OR)",
				logiface.Error(err),
				logiface.Any("tag_ids", tagIDs))
			return nil, err
		}

	default:
		return nil, fmt.Errorf("unsupported tag operator: %s", operator)
	}

	return contactIDs, nil
}

// RemoveContactFromAllTags 从所有标签中移除联系人
func (r *ContactTagRepositoryImpl) RemoveContactFromAllTags(ctx context.Context, tenantID, contactID int64) error {
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND contact_id = ?", tenantID, contactID).
		Delete(&entity.ContactTag{})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to remove contact from all tags",
			logiface.Error(result.Error),
			logiface.Int64("contact_id", contactID))
		return result.Error
	}

	return nil
}

// GetTagContactIDs 获取标签下的联系人ID列表
func (r *ContactTagRepositoryImpl) GetTagContactIDs(ctx context.Context, tenantID, tagID int64) ([]int64, error) {
	var contactIDs []int64
	err := r.db.WithContext(ctx).
		Table("contact_tags").
		Select("contact_id").
		Where("tenant_id = ? AND tag_id = ?", tenantID, tagID).
		Pluck("contact_id", &contactIDs).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to get tag contact IDs",
			logiface.Error(err),
			logiface.Int64("tag_id", tagID))
		return nil, err
	}

	return contactIDs, nil
}
