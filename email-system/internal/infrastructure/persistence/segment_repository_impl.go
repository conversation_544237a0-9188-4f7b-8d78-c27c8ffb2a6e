package persistence

import (
	"context"
	"fmt"
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/segment/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/segment/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gorm.io/gorm"
)

// SegmentRepositoryImpl 人群圈选仓储实现
type SegmentRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewSegmentRepositoryImpl 创建人群圈选仓储实现
func NewSegmentRepositoryImpl(db *gorm.DB, logger logiface.Logger) repository.SegmentRepository {
	return &SegmentRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建人群圈选
func (r *SegmentRepositoryImpl) Create(ctx context.Context, segment *entity.Segment) error {
	if err := r.db.WithContext(ctx).Create(segment).Error; err != nil {
		r.logger.Error(ctx, "Failed to create segment",
			logiface.Error(err),
			logiface.String("name", segment.Name))
		return err
	}
	return nil
}

// Update 更新人群圈选
func (r *SegmentRepositoryImpl) Update(ctx context.Context, segment *entity.Segment) error {
	segment.UpdatedAt = time.Now()
	if err := r.db.WithContext(ctx).Save(segment).Error; err != nil {
		r.logger.Error(ctx, "Failed to update segment",
			logiface.Error(err),
			logiface.Int64("segment_id", segment.ID))
		return err
	}
	return nil
}

// Delete 删除人群圈选
func (r *SegmentRepositoryImpl) Delete(ctx context.Context, tenantID, segmentID int64) error {
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, segmentID).
		Delete(&entity.Segment{})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to delete segment",
			logiface.Error(result.Error),
			logiface.Int64("segment_id", segmentID))
		return result.Error
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("segment not found")
	}

	return nil
}

// FindByID 根据ID查找人群圈选
func (r *SegmentRepositoryImpl) FindByID(ctx context.Context, tenantID, segmentID int64) (*entity.Segment, error) {
	var segment entity.Segment
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, segmentID).
		First(&segment).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find segment by ID",
			logiface.Error(err),
			logiface.Int64("segment_id", segmentID))
		return nil, err
	}

	return &segment, nil
}

// FindByTagID 根据标签ID查找人群圈选
func (r *SegmentRepositoryImpl) FindByTagID(ctx context.Context, tenantID, tagID int64) (*entity.Segment, error) {
	var segment entity.Segment
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND tag_id = ?", tenantID, tagID).
		First(&segment).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find segment by tag ID",
			logiface.Error(err),
			logiface.Int64("tag_id", tagID))
		return nil, err
	}

	return &segment, nil
}

// FindByIDs 根据ID列表批量查找人群圈选
func (r *SegmentRepositoryImpl) FindByIDs(ctx context.Context, tenantID int64, segmentIDs []int64) ([]*entity.Segment, error) {
	if len(segmentIDs) == 0 {
		return []*entity.Segment{}, nil
	}

	var segments []*entity.Segment
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id IN ?", tenantID, segmentIDs).
		Find(&segments).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to find segments by IDs",
			logiface.Error(err),
			logiface.Any("segment_ids", segmentIDs))
		return nil, err
	}

	return segments, nil
}

// List 获取人群圈选列表
func (r *SegmentRepositoryImpl) List(ctx context.Context, params *repository.ListParams) (*repository.ListResult, error) {
	query := r.db.WithContext(ctx).Model(&entity.Segment{}).
		Where("tenant_id = ?", params.TenantID)

	// 应用过滤条件
	query = r.applyFilters(query, params.Filters)

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count segments",
			logiface.Error(err),
			logiface.Int64("tenant_id", params.TenantID))
		return nil, err
	}

	// 应用排序
	if params.Sort != nil && params.Sort.Field != "" {
		order := "DESC"
		if params.Sort.Order == "asc" {
			order = "ASC"
		}
		query = query.Order(fmt.Sprintf("%s %s", params.Sort.Field, order))
	} else {
		query = query.Order("created_at DESC")
	}

	// 应用分页
	offset := params.GetOffset()
	limit := params.GetLimit()
	query = query.Offset(offset).Limit(limit)

	// 执行查询
	var segments []*entity.Segment
	if err := query.Find(&segments).Error; err != nil {
		r.logger.Error(ctx, "Failed to list segments",
			logiface.Error(err),
			logiface.Int64("tenant_id", params.TenantID))
		return nil, err
	}

	// 计算总页数
	totalPages := int(total) / params.GetLimit()
	if int(total)%params.GetLimit() > 0 {
		totalPages++
	}

	return &repository.ListResult{
		Segments:   segments,
		Total:      total,
		Page:       params.Page,
		Size:       params.GetLimit(),
		TotalPages: totalPages,
	}, nil
}

// Count 统计人群圈选数量
func (r *SegmentRepositoryImpl) Count(ctx context.Context, tenantID int64, filters *repository.ListFilters) (int64, error) {
	query := r.db.WithContext(ctx).Model(&entity.Segment{}).
		Where("tenant_id = ?", tenantID)

	// 应用过滤条件
	query = r.applyFilters(query, filters)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to count segments",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return 0, err
	}

	return count, nil
}

// GetActiveSegments 获取活跃的人群圈选
func (r *SegmentRepositoryImpl) GetActiveSegments(ctx context.Context, tenantID int64) ([]*entity.Segment, error) {
	var segments []*entity.Segment
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, entity.SegmentStatusActive).
		Find(&segments).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to get active segments",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, err
	}

	return segments, nil
}

// GetSegmentsByType 根据类型获取人群圈选
func (r *SegmentRepositoryImpl) GetSegmentsByType(ctx context.Context, tenantID int64, segmentType entity.SegmentType) ([]*entity.Segment, error) {
	var segments []*entity.Segment
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND type = ?", tenantID, segmentType).
		Find(&segments).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to get segments by type",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("type", string(segmentType)))
		return nil, err
	}

	return segments, nil
}

// applyFilters 应用过滤条件
func (r *SegmentRepositoryImpl) applyFilters(query *gorm.DB, filters *repository.ListFilters) *gorm.DB {
	if filters == nil {
		return query
	}

	// 名称过滤
	if filters.Name != "" {
		query = query.Where("name LIKE ?", "%"+filters.Name+"%")
	}

	// 标签ID过滤
	if filters.TagID > 0 {
		query = query.Where("tag_id = ?", filters.TagID)
	}

	// 类型过滤
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}

	// 状态过滤
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}

	return query
}

// CreateSnapshot 创建人群圈选快照
func (r *SegmentRepositoryImpl) CreateSnapshot(ctx context.Context, snapshot *entity.SegmentSnapshot) error {
	if err := r.db.WithContext(ctx).Create(snapshot).Error; err != nil {
		r.logger.Error(ctx, "Failed to create segment snapshot",
			logiface.Error(err),
			logiface.Int64("segment_id", snapshot.SegmentID))
		return err
	}
	return nil
}

// DeleteOldSnapshots 删除旧快照，保留最新的指定数量
func (r *SegmentRepositoryImpl) DeleteOldSnapshots(ctx context.Context, tenantID, segmentID int64, keepCount int) error {
	// 查询需要保留的快照ID（按创建时间倒序，取前keepCount个）
	var keepIDs []int64
	if err := r.db.WithContext(ctx).Model(&entity.SegmentSnapshot{}).
		Where("tenant_id = ? AND segment_id = ?", tenantID, segmentID).
		Order("created_at DESC").
		Limit(keepCount).
		Pluck("id", &keepIDs).Error; err != nil {
		r.logger.Error(ctx, "Failed to query snapshots to keep",
			logiface.Error(err),
			logiface.Int64("segment_id", segmentID))
		return err
	}

	// 如果没有需要保留的快照，直接返回
	if len(keepIDs) == 0 {
		return nil
	}

	// 删除不在保留列表中的快照
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND segment_id = ? AND id NOT IN ?", tenantID, segmentID, keepIDs).
		Delete(&entity.SegmentSnapshot{})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to delete old snapshots",
			logiface.Error(result.Error),
			logiface.Int64("segment_id", segmentID))
		return result.Error
	}

	r.logger.Info(ctx, "Deleted old segment snapshots",
		logiface.Int64("segment_id", segmentID),
		logiface.Int64("deleted_count", result.RowsAffected))

	return nil
}

// GetLatestSnapshot 获取最新的人群圈选快照
func (r *SegmentRepositoryImpl) GetLatestSnapshot(ctx context.Context, tenantID, segmentID int64) (*entity.SegmentSnapshot, error) {
	var snapshot entity.SegmentSnapshot
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND segment_id = ?", tenantID, segmentID).
		Order("created_at DESC").
		First(&snapshot).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to get latest segment snapshot",
			logiface.Error(err),
			logiface.Int64("segment_id", segmentID))
		return nil, err
	}

	return &snapshot, nil
}
