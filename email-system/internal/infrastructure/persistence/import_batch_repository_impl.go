package persistence

import (
	"context"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/repository"
	"time"

	"gorm.io/gorm"
)

// importBatchRepositoryImpl 导入批次仓储实现
type importBatchRepositoryImpl struct {
	db *gorm.DB
}

// NewImportBatchRepository 创建导入批次仓储实例
func NewImportBatchRepository(db *gorm.DB) repository.ImportBatchRepository {
	return &importBatchRepositoryImpl{
		db: db,
	}
}

// Create 创建批次
func (r *importBatchRepositoryImpl) Create(ctx context.Context, batch *entity.ImportBatch) error {
	return r.db.WithContext(ctx).Create(batch).Error
}

// BatchCreate 批量创建批次
func (r *importBatchRepositoryImpl) BatchCreate(ctx context.Context, batches []*entity.ImportBatch) error {
	if len(batches) == 0 {
		return nil
	}

	// 使用批量插入
	return r.db.WithContext(ctx).CreateInBatches(batches, 100).Error
}

// GetByID 根据ID获取批次
func (r *importBatchRepositoryImpl) GetByID(ctx context.Context, id, tenantID int64) (*entity.ImportBatch, error) {
	var batch entity.ImportBatch
	err := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		First(&batch).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &batch, nil
}

// GetByJobAndBatch 根据任务ID和批次号获取批次
func (r *importBatchRepositoryImpl) GetByJobAndBatch(ctx context.Context, jobID, tenantID int64, batchNo int) (*entity.ImportBatch, error) {
	var batch entity.ImportBatch
	err := r.db.WithContext(ctx).
		Where("job_id = ? AND tenant_id = ? AND batch_no = ?", jobID, tenantID, batchNo).
		First(&batch).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &batch, nil
}

// Update 更新批次
func (r *importBatchRepositoryImpl) Update(ctx context.Context, batch *entity.ImportBatch) error {
	return r.db.WithContext(ctx).Save(batch).Error
}

// UpdateStatus 更新批次状态
func (r *importBatchRepositoryImpl) UpdateStatus(ctx context.Context, id, tenantID int64, status entity.ImportBatchStatus) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	// 如果状态是处理中，设置开始时间
	if status == entity.ImportBatchStatusProcessing {
		updates["started_at"] = time.Now()
	}

	// 如果状态是完成或失败，设置结束时间
	if status == entity.ImportBatchStatusCompleted || status == entity.ImportBatchStatusFailed {
		updates["finished_at"] = time.Now()
	}

	return r.db.WithContext(ctx).
		Model(&entity.ImportBatch{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Updates(updates).Error
}

// UpdateProgress 更新批次进度
func (r *importBatchRepositoryImpl) UpdateProgress(ctx context.Context, id, tenantID int64,
	processedCount, successCount, failedCount int) error {
	return r.db.WithContext(ctx).
		Model(&entity.ImportBatch{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Updates(map[string]interface{}{
			"processed_count": processedCount,
			"success_count":   successCount,
			"failed_count":    failedCount,
			"updated_at":      time.Now(),
		}).Error
}

// MarkCompleted 标记批次完成
func (r *importBatchRepositoryImpl) MarkCompleted(ctx context.Context, id, tenantID int64) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&entity.ImportBatch{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Updates(map[string]interface{}{
			"status":      entity.ImportBatchStatusCompleted,
			"finished_at": &now,
			"updated_at":  now,
		}).Error
}

// MarkFailed 标记批次失败
func (r *importBatchRepositoryImpl) MarkFailed(ctx context.Context, id, tenantID int64, errorMessage string) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&entity.ImportBatch{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Updates(map[string]interface{}{
			"status":        entity.ImportBatchStatusFailed,
			"error_message": errorMessage,
			"finished_at":   &now,
			"updated_at":    now,
		}).Error
}

// ListByJobID 根据任务ID获取批次列表
func (r *importBatchRepositoryImpl) ListByJobID(ctx context.Context, jobID, tenantID int64) ([]*entity.ImportBatch, error) {
	var batches []*entity.ImportBatch

	err := r.db.WithContext(ctx).
		Where("job_id = ? AND tenant_id = ?", jobID, tenantID).
		Order("batch_no ASC").
		Find(&batches).Error

	return batches, err
}

// GetPendingBatches 获取待处理的批次列表
func (r *importBatchRepositoryImpl) GetPendingBatches(ctx context.Context, jobID, tenantID int64) ([]*entity.ImportBatch, error) {
	var batches []*entity.ImportBatch

	err := r.db.WithContext(ctx).
		Where("job_id = ? AND tenant_id = ? AND status = ?",
			jobID, tenantID, entity.ImportBatchStatusPending).
		Order("batch_no ASC").
		Find(&batches).Error

	return batches, err
}

// GetBatchStatistics 获取批次统计信息
func (r *importBatchRepositoryImpl) GetBatchStatistics(ctx context.Context, jobID, tenantID int64) (map[string]int64, error) {
	var results []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}

	err := r.db.WithContext(ctx).
		Model(&entity.ImportBatch{}).
		Select("status, COUNT(*) as count").
		Where("job_id = ? AND tenant_id = ?", jobID, tenantID).
		Group("status").
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	statistics := make(map[string]int64)
	for _, result := range results {
		statistics[result.Status] = result.Count
	}

	// 计算总的处理统计
	var totals struct {
		TotalProcessed int64 `json:"total_processed"`
		TotalSuccess   int64 `json:"total_success"`
		TotalFailed    int64 `json:"total_failed"`
	}

	err = r.db.WithContext(ctx).
		Model(&entity.ImportBatch{}).
		Select("SUM(processed_count) as total_processed, SUM(success_count) as total_success, SUM(failed_count) as total_failed").
		Where("job_id = ? AND tenant_id = ?", jobID, tenantID).
		Scan(&totals).Error

	if err != nil {
		return nil, err
	}

	statistics["total_processed"] = totals.TotalProcessed
	statistics["total_success"] = totals.TotalSuccess
	statistics["total_failed"] = totals.TotalFailed

	return statistics, nil
}

// Delete 删除批次
func (r *importBatchRepositoryImpl) Delete(ctx context.Context, id, tenantID int64) error {
	return r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Delete(&entity.ImportBatch{}).Error
}

// DeleteByJobID 删除指定任务的所有批次
func (r *importBatchRepositoryImpl) DeleteByJobID(ctx context.Context, jobID, tenantID int64) error {
	return r.db.WithContext(ctx).
		Where("job_id = ? AND tenant_id = ?", jobID, tenantID).
		Delete(&entity.ImportBatch{}).Error
}
