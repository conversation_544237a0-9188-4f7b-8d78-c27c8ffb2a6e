package persistence

import (
	"context"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/repository"
	"time"

	"gorm.io/gorm"
)

// importJobRepositoryImpl 导入任务仓储实现
type importJobRepositoryImpl struct {
	db *gorm.DB
}

// NewImportJobRepository 创建导入任务仓储实例
func NewImportJobRepository(db *gorm.DB) repository.ImportJobRepository {
	return &importJobRepositoryImpl{
		db: db,
	}
}

// Create 创建导入任务
func (r *importJobRepositoryImpl) Create(ctx context.Context, job *entity.ImportJob) error {
	return r.db.WithContext(ctx).Create(job).Error
}

// GetByID 根据ID获取导入任务
func (r *importJobRepositoryImpl) GetByID(ctx context.Context, id, tenantID int64) (*entity.ImportJob, error) {
	var job entity.ImportJob
	err := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		First(&job).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, entity.NewImportNotFoundError(id, tenantID)
		}
		return nil, err
	}

	return &job, nil
}

// Update 更新导入任务
func (r *importJobRepositoryImpl) Update(ctx context.Context, job *entity.ImportJob) error {
	return r.db.WithContext(ctx).Save(job).Error
}

// UpdateStatus 更新任务状态
func (r *importJobRepositoryImpl) UpdateStatus(ctx context.Context, id, tenantID int64, status entity.ImportJobStatus) error {
	return r.db.WithContext(ctx).
		Model(&entity.ImportJob{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Updates(map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		}).Error
}

// UpdateProgress 更新任务进度
func (r *importJobRepositoryImpl) UpdateProgress(ctx context.Context, id, tenantID int64, progress int) error {
	return r.db.WithContext(ctx).
		Model(&entity.ImportJob{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Updates(map[string]interface{}{
			"progress":   progress,
			"updated_at": time.Now(),
		}).Error
}

// UpdatePhase 更新任务阶段
func (r *importJobRepositoryImpl) UpdatePhase(ctx context.Context, id, tenantID int64, phase entity.ImportJobPhase) error {
	updates := map[string]interface{}{
		"current_phase": phase,
		"updated_at":    time.Now(),
	}

	// 根据阶段设置状态
	switch phase {
	case entity.ImportJobPhaseParsing:
		updates["status"] = entity.ImportJobStatusParsing
	case entity.ImportJobPhaseValidating:
		updates["status"] = entity.ImportJobStatusValidating
	case entity.ImportJobPhaseProcessing:
		updates["status"] = entity.ImportJobStatusProcessing
	case entity.ImportJobPhaseStoring:
		updates["status"] = entity.ImportJobStatusProcessing
	}

	return r.db.WithContext(ctx).
		Model(&entity.ImportJob{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Updates(updates).Error
}

// UpdateStatistics 更新统计信息
func (r *importJobRepositoryImpl) UpdateStatistics(ctx context.Context, id, tenantID int64,
	totalRows, validRows, invalidRows, duplicateRows int64) error {
	return r.db.WithContext(ctx).
		Model(&entity.ImportJob{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Updates(map[string]interface{}{
			"total_rows":     totalRows,
			"valid_rows":     validRows,
			"invalid_rows":   invalidRows,
			"duplicate_rows": duplicateRows,
			"updated_at":     time.Now(),
		}).Error
}

// UpdateProcessCount 更新处理计数
func (r *importJobRepositoryImpl) UpdateProcessCount(ctx context.Context, id, tenantID int64,
	processedCount, successCount, failedCount int64) error {
	updates := map[string]interface{}{
		"processed_count": processedCount,
		"success_count":   successCount,
		"failed_count":    failedCount,
		"updated_at":      time.Now(),
	}

	// 如果处理完成，更新进度为100%
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 获取当前任务的有效行数
		var job entity.ImportJob
		if err := tx.Where("id = ? AND tenant_id = ?", id, tenantID).
			Select("valid_rows").First(&job).Error; err != nil {
			return err
		}

		// 计算进度
		if job.ValidRows > 0 {
			progress := int((processedCount * 100) / job.ValidRows)
			if progress > 100 {
				progress = 100
			}
			updates["progress"] = progress
		}

		return tx.Model(&entity.ImportJob{}).
			Where("id = ? AND tenant_id = ?", id, tenantID).
			Updates(updates).Error
	})

	return err
}

// MarkCompleted 标记任务完成
func (r *importJobRepositoryImpl) MarkCompleted(ctx context.Context, id, tenantID int64) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&entity.ImportJob{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Updates(map[string]interface{}{
			"status":      entity.ImportJobStatusCompleted,
			"progress":    100,
			"finished_at": &now,
			"updated_at":  now,
		}).Error
}

// MarkFailed 标记任务失败
func (r *importJobRepositoryImpl) MarkFailed(ctx context.Context, id, tenantID int64, errorMessage string) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&entity.ImportJob{}).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Updates(map[string]interface{}{
			"status":        entity.ImportJobStatusFailed,
			"error_message": errorMessage,
			"finished_at":   &now,
			"updated_at":    now,
		}).Error
}

// List 分页查询导入任务列表
func (r *importJobRepositoryImpl) List(ctx context.Context, tenantID int64, status entity.ImportJobStatus,
	limit, offset int) ([]*entity.ImportJob, int64, error) {

	var jobs []*entity.ImportJob
	var total int64

	query := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID)

	if status != "" {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	if err := query.Model(&entity.ImportJob{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("created_at DESC").
		Limit(limit).Offset(offset).
		Find(&jobs).Error

	return jobs, total, err
}

// ListByBusinessScenario 根据业务场景查询任务列表
func (r *importJobRepositoryImpl) ListByBusinessScenario(ctx context.Context, tenantID int64,
	scenario entity.BusinessScenario, limit, offset int) ([]*entity.ImportJob, int64, error) {

	var jobs []*entity.ImportJob
	var total int64

	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND business_scenario = ?", tenantID, scenario)

	// 获取总数
	if err := query.Model(&entity.ImportJob{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	err := query.Order("created_at DESC").
		Limit(limit).Offset(offset).
		Find(&jobs).Error

	return jobs, total, err
}

// Delete 删除导入任务
func (r *importJobRepositoryImpl) Delete(ctx context.Context, id, tenantID int64) error {
	return r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Delete(&entity.ImportJob{}).Error
}

// GetRunningJobs 获取运行中的任务列表
func (r *importJobRepositoryImpl) GetRunningJobs(ctx context.Context, tenantID int64) ([]*entity.ImportJob, error) {
	var jobs []*entity.ImportJob

	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status IN (?)", tenantID, []entity.ImportJobStatus{
			entity.ImportJobStatusParsing,
			entity.ImportJobStatusValidating,
			entity.ImportJobStatusProcessing,
		}).
		Order("created_at ASC").
		Find(&jobs).Error

	return jobs, err
}

// GetQueuedJobs 获取排队中的任务列表
func (r *importJobRepositoryImpl) GetQueuedJobs(ctx context.Context, tenantID int64, limit int) ([]*entity.ImportJob, error) {
	var jobs []*entity.ImportJob

	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, entity.ImportJobStatusQueued).
		Order("created_at ASC").
		Limit(limit).
		Find(&jobs).Error

	return jobs, err
}
