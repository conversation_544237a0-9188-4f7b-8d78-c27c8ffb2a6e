package persistence

import (
	"context"
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/send/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/send/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gorm.io/gorm"
)

// SendPlanRepositoryImpl 发送计划仓储实现
type SendPlanRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewSendPlanRepository 创建发送计划仓储
func NewSendPlanRepository(db *gorm.DB, logger logiface.Logger) repository.SendPlanRepository {
	return &SendPlanRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建发送计划
func (r *SendPlanRepositoryImpl) Create(ctx context.Context, plan *entity.SendPlan) error {
	if err := r.db.WithContext(ctx).Create(plan).Error; err != nil {
		r.logger.Error(ctx, "Failed to create send plan",
			logiface.Error(err),
			logiface.String("display_name", plan.DisplayName))
		return err
	}
	return nil
}

// Update 更新发送计划
func (r *SendPlanRepositoryImpl) Update(ctx context.Context, plan *entity.SendPlan) error {
	if err := r.db.WithContext(ctx).Save(plan).Error; err != nil {
		r.logger.Error(ctx, "Failed to update send plan",
			logiface.Error(err),
			logiface.Int64("plan_id", plan.PlanID))
		return err
	}
	return nil
}

// Delete 删除发送计划
func (r *SendPlanRepositoryImpl) Delete(ctx context.Context, tenantID, planID int64) error {
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		Delete(&entity.SendPlan{})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to delete send plan",
			logiface.Error(result.Error),
			logiface.Int64("plan_id", planID))
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.logger.Warn(ctx, "Send plan not found for deletion",
			logiface.Int64("plan_id", planID))
	}

	return nil
}

// FindByID 根据ID查找发送计划
func (r *SendPlanRepositoryImpl) FindByID(ctx context.Context, tenantID, planID int64) (*entity.SendPlan, error) {
	var plan entity.SendPlan
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		First(&plan).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find send plan by ID",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return nil, err
	}

	return &plan, nil
}

// FindByStatus 根据状态查找发送计划
func (r *SendPlanRepositoryImpl) FindByStatus(ctx context.Context, tenantID int64, status entity.PlanStatus, limit int) ([]*entity.SendPlan, error) {
	var plans []*entity.SendPlan
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Order("created_at ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&plans).Error
	if err != nil {
		r.logger.Error(ctx, "Failed to find send plans by status",
			logiface.Error(err),
			logiface.String("status", string(status)))
		return nil, err
	}

	return plans, nil
}

// FindExpiredPlans 查找过期的发送计划
func (r *SendPlanRepositoryImpl) FindExpiredPlans(ctx context.Context, limit int) ([]*entity.SendPlan, error) {
	var plans []*entity.SendPlan
	now := time.Now()

	query := r.db.WithContext(ctx).
		Where("deadline IS NOT NULL AND deadline < ? AND status IN (?)",
			now, []entity.PlanStatus{entity.PlanStatusDraft, entity.PlanStatusRunning, entity.PlanStatusPaused}).
		Order("deadline ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&plans).Error
	if err != nil {
		r.logger.Error(ctx, "Failed to find expired send plans", logiface.Error(err))
		return nil, err
	}

	return plans, nil
}

// FindScheduledPlans 查找需要调度的发送计划
func (r *SendPlanRepositoryImpl) FindScheduledPlans(ctx context.Context, limit int) ([]*entity.SendPlan, error) {
	var plans []*entity.SendPlan
	now := time.Now()

	query := r.db.WithContext(ctx).
		Where("status = ? AND (schedule_from IS NULL OR schedule_from <= ?)",
			entity.PlanStatusDraft, now).
		Order("priority DESC, created_at ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&plans).Error
	if err != nil {
		r.logger.Error(ctx, "Failed to find scheduled send plans", logiface.Error(err))
		return nil, err
	}

	return plans, nil
}

// List 分页查询发送计划
func (r *SendPlanRepositoryImpl) List(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.SendPlan, int64, error) {
	var plans []*entity.SendPlan
	var total int64

	// 查询总数
	if err := r.db.WithContext(ctx).Model(&entity.SendPlan{}).
		Where("tenant_id = ?", tenantID).
		Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count send plans",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, 0, err
	}

	// 查询数据
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&plans).Error; err != nil {
		r.logger.Error(ctx, "Failed to list send plans",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, 0, err
	}

	return plans, total, nil
}

// Count 统计发送计划数量
func (r *SendPlanRepositoryImpl) Count(ctx context.Context, tenantID int64) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.SendPlan{}).
		Where("tenant_id = ?", tenantID).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to count send plans",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return 0, err
	}

	return count, nil
}

// CountByStatus 根据状态统计发送计划数量
func (r *SendPlanRepositoryImpl) CountByStatus(ctx context.Context, tenantID int64, status entity.PlanStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.SendPlan{}).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to count send plans by status",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("status", string(status)))
		return 0, err
	}

	return count, nil
}
