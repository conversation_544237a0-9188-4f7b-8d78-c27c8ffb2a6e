package persistence

import (
	"context"
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/segment/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/segment/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gorm.io/gorm"
)

// SegmentJobRepositoryImpl 人群圈选任务仓储实现
type SegmentJobRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewSegmentJobRepositoryImpl 创建人群圈选任务仓储实现
func NewSegmentJobRepositoryImpl(db *gorm.DB, logger logiface.Logger) repository.SegmentJobRepository {
	return &SegmentJobRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建人群圈选任务
func (r *SegmentJobRepositoryImpl) Create(ctx context.Context, job *entity.SegmentJob) error {
	if err := r.db.WithContext(ctx).Create(job).Error; err != nil {
		r.logger.Error(ctx, "Failed to create segment job",
			logiface.Error(err),
			logiface.Int64("segment_id", job.SegmentID),
			logiface.String("job_type", string(job.JobType)))
		return err
	}
	return nil
}

// Update 更新人群圈选任务
func (r *SegmentJobRepositoryImpl) Update(ctx context.Context, job *entity.SegmentJob) error {
	job.UpdatedAt = time.Now()
	if err := r.db.WithContext(ctx).Save(job).Error; err != nil {
		r.logger.Error(ctx, "Failed to update segment job",
			logiface.Error(err),
			logiface.Int64("job_id", job.ID))
		return err
	}
	return nil
}

// FindByID 根据ID查找人群圈选任务
func (r *SegmentJobRepositoryImpl) FindByID(ctx context.Context, tenantID, jobID int64) (*entity.SegmentJob, error) {
	var job entity.SegmentJob
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, jobID).
		First(&job).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find segment job by ID",
			logiface.Error(err),
			logiface.Int64("job_id", jobID))
		return nil, err
	}

	return &job, nil
}

// FindBySegmentID 根据人群圈选ID查找任务
func (r *SegmentJobRepositoryImpl) FindBySegmentID(ctx context.Context, tenantID, segmentID int64, jobType entity.SegmentJobType) (*entity.SegmentJob, error) {
	var job entity.SegmentJob
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND segment_id = ? AND job_type = ?", tenantID, segmentID, jobType).
		Order("created_at DESC").
		First(&job).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find segment job by segment ID",
			logiface.Error(err),
			logiface.Int64("segment_id", segmentID),
			logiface.String("job_type", string(jobType)))
		return nil, err
	}

	return &job, nil
}

// List 获取人群圈选任务列表
func (r *SegmentJobRepositoryImpl) List(ctx context.Context, params *repository.JobListParams) (*repository.JobListResult, error) {
	query := r.db.WithContext(ctx).Model(&entity.SegmentJob{}).
		Where("tenant_id = ?", params.TenantID)

	// 应用过滤条件
	if params.SegmentID > 0 {
		query = query.Where("segment_id = ?", params.SegmentID)
	}

	if params.JobType != "" {
		query = query.Where("job_type = ?", params.JobType)
	}

	if params.Status != "" {
		query = query.Where("status = ?", params.Status)
	}

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count segment jobs",
			logiface.Error(err),
			logiface.Int64("tenant_id", params.TenantID))
		return nil, err
	}

	// 应用排序
	query = query.Order("created_at DESC")

	// 应用分页
	offset := params.GetOffset()
	limit := params.GetLimit()
	query = query.Offset(offset).Limit(limit)

	// 执行查询
	var jobs []*entity.SegmentJob
	if err := query.Find(&jobs).Error; err != nil {
		r.logger.Error(ctx, "Failed to list segment jobs",
			logiface.Error(err),
			logiface.Int64("tenant_id", params.TenantID))
		return nil, err
	}

	// 计算总页数
	totalPages := int(total) / params.GetLimit()
	if int(total)%params.GetLimit() > 0 {
		totalPages++
	}

	return &repository.JobListResult{
		Jobs:       jobs,
		Total:      total,
		Page:       params.Page,
		Size:       params.GetLimit(),
		TotalPages: totalPages,
	}, nil
}

// GetRunningJobs 获取运行中的任务
func (r *SegmentJobRepositoryImpl) GetRunningJobs(ctx context.Context, tenantID int64) ([]*entity.SegmentJob, error) {
	var jobs []*entity.SegmentJob
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, entity.SegmentJobStatusRunning).
		Find(&jobs).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to get running segment jobs",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, err
	}

	return jobs, nil
}

// GetPendingJobs 获取待处理的任务
func (r *SegmentJobRepositoryImpl) GetPendingJobs(ctx context.Context, tenantID int64, limit int) ([]*entity.SegmentJob, error) {
	var jobs []*entity.SegmentJob
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, entity.SegmentJobStatusQueued).
		Order("created_at ASC").
		Limit(limit).
		Find(&jobs).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to get pending segment jobs",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, err
	}

	return jobs, nil
}

// UpdateJobStatus 更新任务状态
func (r *SegmentJobRepositoryImpl) UpdateJobStatus(ctx context.Context, tenantID, jobID int64, status entity.SegmentJobStatus, progress int, errorMsg *string) error {
	updates := map[string]interface{}{
		"status":     status,
		"progress":   progress,
		"updated_at": time.Now(),
	}

	// 设置开始时间
	if status == entity.SegmentJobStatusRunning {
		updates["started_at"] = time.Now()
	}

	// 设置完成时间
	if status == entity.SegmentJobStatusCompleted || status == entity.SegmentJobStatusFailed || status == entity.SegmentJobStatusCancelled {
		updates["finished_at"] = time.Now()
	}

	// 设置错误信息
	if errorMsg != nil {
		updates["error_msg"] = *errorMsg
	}

	result := r.db.WithContext(ctx).Model(&entity.SegmentJob{}).
		Where("tenant_id = ? AND id = ?", tenantID, jobID).
		Updates(updates)

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to update segment job status",
			logiface.Error(result.Error),
			logiface.Int64("job_id", jobID),
			logiface.String("status", string(status)))
		return result.Error
	}

	return nil
}

// SetJobResult 设置任务结果
func (r *SegmentJobRepositoryImpl) SetJobResult(ctx context.Context, tenantID, jobID int64, result map[string]interface{}) error {
	// 将结果转换为JSON字符串
	job := &entity.SegmentJob{}
	if err := job.SetResult(result); err != nil {
		return err
	}

	updates := map[string]interface{}{
		"result_json": job.ResultJSON,
		"updated_at":  time.Now(),
	}

	dbResult := r.db.WithContext(ctx).Model(&entity.SegmentJob{}).
		Where("tenant_id = ? AND id = ?", tenantID, jobID).
		Updates(updates)

	if dbResult.Error != nil {
		r.logger.Error(ctx, "Failed to set segment job result",
			logiface.Error(dbResult.Error),
			logiface.Int64("job_id", jobID))
		return dbResult.Error
	}

	return nil
}
