package persistence

import (
	"context"
	"fmt"
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/tag/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/tag/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gorm.io/gorm"
)

// TagRepositoryImpl 标签仓储实现
type TagRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewTagRepositoryImpl 创建标签仓储实现
func NewTagRepositoryImpl(db *gorm.DB, logger logiface.Logger) repository.TagRepository {
	return &TagRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建标签
func (r *TagRepositoryImpl) Create(ctx context.Context, tag *entity.Tag) error {
	if err := r.db.WithContext(ctx).Create(tag).Error; err != nil {
		r.logger.Error(ctx, "Failed to create tag",
			logiface.Error(err),
			logiface.String("name", tag.Name))
		return err
	}
	return nil
}

// Update 更新标签
func (r *TagRepositoryImpl) Update(ctx context.Context, tag *entity.Tag) error {
	tag.UpdatedAt = time.Now()
	if err := r.db.WithContext(ctx).Save(tag).Error; err != nil {
		r.logger.Error(ctx, "Failed to update tag",
			logiface.Error(err),
			logiface.Int64("tag_id", tag.ID))
		return err
	}
	return nil
}

// Delete 删除标签
func (r *TagRepositoryImpl) Delete(ctx context.Context, tenantID, tagID int64) error {
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, tagID).
		Delete(&entity.Tag{})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to delete tag",
			logiface.Error(result.Error),
			logiface.Int64("tag_id", tagID))
		return result.Error
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("tag not found")
	}

	return nil
}

// FindByID 根据ID查找标签
func (r *TagRepositoryImpl) FindByID(ctx context.Context, tenantID, tagID int64) (*entity.Tag, error) {
	var tag entity.Tag
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, tagID).
		First(&tag).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find tag by ID",
			logiface.Error(err),
			logiface.Int64("tag_id", tagID))
		return nil, err
	}

	return &tag, nil
}

// FindByName 根据名称查找标签
func (r *TagRepositoryImpl) FindByName(ctx context.Context, tenantID int64, name string) (*entity.Tag, error) {
	var tag entity.Tag
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND name = ?", tenantID, name).
		First(&tag).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find tag by name",
			logiface.Error(err),
			logiface.String("name", name))
		return nil, err
	}

	return &tag, nil
}

// FindByIDs 根据ID列表批量查找标签
func (r *TagRepositoryImpl) FindByIDs(ctx context.Context, tenantID int64, tagIDs []int64) ([]*entity.Tag, error) {
	if len(tagIDs) == 0 {
		return []*entity.Tag{}, nil
	}

	var tags []*entity.Tag
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id IN ?", tenantID, tagIDs).
		Find(&tags).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to find tags by IDs",
			logiface.Error(err),
			logiface.Any("tag_ids", tagIDs))
		return nil, err
	}

	return tags, nil
}

// List 获取标签列表
func (r *TagRepositoryImpl) List(ctx context.Context, params *repository.ListParams) (*repository.ListResult, error) {
	query := r.db.WithContext(ctx).Model(&entity.Tag{}).
		Where("tenant_id = ?", params.TenantID)

	// 应用过滤条件
	query = r.applyFilters(query, params.Filters)

	// 统计总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count tags",
			logiface.Error(err),
			logiface.Int64("tenant_id", params.TenantID))
		return nil, err
	}

	// 应用排序
	if params.Sort != nil && params.Sort.Field != "" {
		order := "DESC"
		if params.Sort.Order == "asc" {
			order = "ASC"
		}
		query = query.Order(fmt.Sprintf("%s %s", params.Sort.Field, order))
	} else {
		query = query.Order("created_at DESC")
	}

	// 应用分页
	offset := params.GetOffset()
	limit := params.GetLimit()
	query = query.Offset(offset).Limit(limit)

	// 执行查询
	var tags []*entity.Tag
	if err := query.Find(&tags).Error; err != nil {
		r.logger.Error(ctx, "Failed to list tags",
			logiface.Error(err),
			logiface.Int64("tenant_id", params.TenantID))
		return nil, err
	}

	// 计算总页数
	totalPages := int(total) / params.GetLimit()
	if int(total)%params.GetLimit() > 0 {
		totalPages++
	}

	return &repository.ListResult{
		Tags:       tags,
		Total:      total,
		Page:       params.Page,
		Size:       params.GetLimit(),
		TotalPages: totalPages,
	}, nil
}

// Count 统计标签数量
func (r *TagRepositoryImpl) Count(ctx context.Context, tenantID int64, filters *repository.ListFilters) (int64, error) {
	query := r.db.WithContext(ctx).Model(&entity.Tag{}).
		Where("tenant_id = ?", tenantID)

	// 应用过滤条件
	query = r.applyFilters(query, filters)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		r.logger.Error(ctx, "Failed to count tags",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return 0, err
	}

	return count, nil
}

// ExistsByName 检查标签名称是否存在
func (r *TagRepositoryImpl) ExistsByName(ctx context.Context, tenantID int64, name string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.Tag{}).
		Where("tenant_id = ? AND name = ?", tenantID, name).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to check tag name existence",
			logiface.Error(err),
			logiface.String("name", name))
		return false, err
	}

	return count > 0, nil
}

// UpdateMemberCount 更新标签成员数量
func (r *TagRepositoryImpl) UpdateMemberCount(ctx context.Context, tenantID, tagID int64, count int64) error {
	result := r.db.WithContext(ctx).Model(&entity.Tag{}).
		Where("tenant_id = ? AND id = ?", tenantID, tagID).
		Updates(map[string]interface{}{
			"member_count": count,
			"updated_at":   time.Now(),
		})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to update tag member count",
			logiface.Error(result.Error),
			logiface.Int64("tag_id", tagID),
			logiface.Int64("count", count))
		return result.Error
	}

	return nil
}

// IncrementUsageCount 增加标签使用次数
func (r *TagRepositoryImpl) IncrementUsageCount(ctx context.Context, tenantID, tagID int64) error {
	result := r.db.WithContext(ctx).Model(&entity.Tag{}).
		Where("tenant_id = ? AND id = ?", tenantID, tagID).
		Updates(map[string]interface{}{
			"usage_count": gorm.Expr("usage_count + 1"),
			"updated_at":  time.Now(),
		})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to increment tag usage count",
			logiface.Error(result.Error),
			logiface.Int64("tag_id", tagID))
		return result.Error
	}

	return nil
}

// GetPopularTags 获取热门标签
func (r *TagRepositoryImpl) GetPopularTags(ctx context.Context, tenantID int64, limit int) ([]*entity.Tag, error) {
	var tags []*entity.Tag
	err := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID).
		Order("usage_count DESC, member_count DESC").
		Limit(limit).
		Find(&tags).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to get popular tags",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, err
	}

	return tags, nil
}

// GetRecentTags 获取最近使用的标签
func (r *TagRepositoryImpl) GetRecentTags(ctx context.Context, tenantID int64, limit int) ([]*entity.Tag, error) {
	var tags []*entity.Tag
	err := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID).
		Order("updated_at DESC").
		Limit(limit).
		Find(&tags).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to get recent tags",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, err
	}

	return tags, nil
}

// applyFilters 应用过滤条件
func (r *TagRepositoryImpl) applyFilters(query *gorm.DB, filters *repository.ListFilters) *gorm.DB {
	if filters == nil {
		return query
	}

	// 名称过滤
	if filters.Name != "" {
		query = query.Where("name = ?", filters.Name)
	}

	// 类型过滤
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}

	// 颜色过滤
	if filters.Color != "" {
		query = query.Where("color = ?", filters.Color)
	}

	// 创建人过滤
	if filters.CreatedBy > 0 {
		query = query.Where("created_by = ?", filters.CreatedBy)
	}

	// 关键词搜索
	if filters.Keyword != "" {
		keyword := "%" + filters.Keyword + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", keyword, keyword)
	}

	// 使用次数范围过滤
	if filters.UsageMin > 0 {
		query = query.Where("usage_count >= ?", filters.UsageMin)
	}
	if filters.UsageMax > 0 {
		query = query.Where("usage_count <= ?", filters.UsageMax)
	}

	// 成员数量范围过滤
	if filters.MemberMin > 0 {
		query = query.Where("member_count >= ?", filters.MemberMin)
	}
	if filters.MemberMax > 0 {
		query = query.Where("member_count <= ?", filters.MemberMax)
	}

	// 时间范围过滤
	if filters.CreatedAtRange != nil {
		if filters.CreatedAtRange.Start != nil && *filters.CreatedAtRange.Start != "" {
			query = query.Where("created_at >= ?", *filters.CreatedAtRange.Start)
		}
		if filters.CreatedAtRange.End != nil && *filters.CreatedAtRange.End != "" {
			query = query.Where("created_at <= ?", *filters.CreatedAtRange.End)
		}
	}

	if filters.UpdatedAtRange != nil {
		if filters.UpdatedAtRange.Start != nil && *filters.UpdatedAtRange.Start != "" {
			query = query.Where("updated_at >= ?", *filters.UpdatedAtRange.Start)
		}
		if filters.UpdatedAtRange.End != nil && *filters.UpdatedAtRange.End != "" {
			query = query.Where("updated_at <= ?", *filters.UpdatedAtRange.End)
		}
	}

	return query
}

// GetTagsByType 根据类型获取标签
func (r *TagRepositoryImpl) GetTagsByType(ctx context.Context, tenantID int64, tagType entity.TagType) ([]*entity.Tag, error) {
	var tags []*entity.Tag
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND type = ?", tenantID, tagType).
		Find(&tags).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to get tags by type",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("type", string(tagType)))
		return nil, err
	}

	return tags, nil
}
