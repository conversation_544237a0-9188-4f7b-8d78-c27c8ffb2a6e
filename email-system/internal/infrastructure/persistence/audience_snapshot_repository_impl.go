package persistence

import (
	"context"

	"gitee.com/heiyee/platforms/email-system/internal/domain/send/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/send/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gorm.io/gorm"
)

// AudienceSnapshotRepositoryImpl 受众快照仓储实现
type AudienceSnapshotRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewAudienceSnapshotRepository 创建受众快照仓储
func NewAudienceSnapshotRepository(db *gorm.DB, logger logiface.Logger) repository.AudienceSnapshotRepository {
	return &AudienceSnapshotRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建受众快照
func (r *AudienceSnapshotRepositoryImpl) Create(ctx context.Context, snapshot *entity.AudienceSnapshot) error {
	if err := r.db.WithContext(ctx).Create(snapshot).Error; err != nil {
		r.logger.Error(ctx, "Failed to create audience snapshot",
			logiface.Error(err),
			logiface.Int64("plan_id", snapshot.PlanID))
		return err
	}
	return nil
}

// Update 更新受众快照
func (r *AudienceSnapshotRepositoryImpl) Update(ctx context.Context, snapshot *entity.AudienceSnapshot) error {
	if err := r.db.WithContext(ctx).Save(snapshot).Error; err != nil {
		r.logger.Error(ctx, "Failed to update audience snapshot",
			logiface.Error(err),
			logiface.Int64("snapshot_id", snapshot.SnapshotID))
		return err
	}
	return nil
}

// Delete 删除受众快照
func (r *AudienceSnapshotRepositoryImpl) Delete(ctx context.Context, tenantID, snapshotID int64) error {
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND snapshot_id = ?", tenantID, snapshotID).
		Delete(&entity.AudienceSnapshot{})

	if result.Error != nil {
		r.logger.Error(ctx, "Failed to delete audience snapshot",
			logiface.Error(result.Error),
			logiface.Int64("snapshot_id", snapshotID))
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.logger.Warn(ctx, "Audience snapshot not found for deletion",
			logiface.Int64("snapshot_id", snapshotID))
	}

	return nil
}

// FindByID 根据ID查找受众快照
func (r *AudienceSnapshotRepositoryImpl) FindByID(ctx context.Context, tenantID, snapshotID int64) (*entity.AudienceSnapshot, error) {
	var snapshot entity.AudienceSnapshot
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND snapshot_id = ?", tenantID, snapshotID).
		First(&snapshot).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find audience snapshot by ID",
			logiface.Error(err),
			logiface.Int64("snapshot_id", snapshotID))
		return nil, err
	}

	return &snapshot, nil
}

// FindByPlanID 根据计划ID查找受众快照
func (r *AudienceSnapshotRepositoryImpl) FindByPlanID(ctx context.Context, tenantID, planID int64) (*entity.AudienceSnapshot, error) {
	var snapshot entity.AudienceSnapshot
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		Order("created_at DESC").
		First(&snapshot).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.Error(ctx, "Failed to find audience snapshot by plan ID",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return nil, err
	}

	return &snapshot, nil
}

// List 分页查询受众快照
func (r *AudienceSnapshotRepositoryImpl) List(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.AudienceSnapshot, int64, error) {
	var snapshots []*entity.AudienceSnapshot
	var total int64

	// 查询总数
	if err := r.db.WithContext(ctx).Model(&entity.AudienceSnapshot{}).
		Where("tenant_id = ?", tenantID).
		Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count audience snapshots",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, 0, err
	}

	// 查询数据
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&snapshots).Error; err != nil {
		r.logger.Error(ctx, "Failed to list audience snapshots",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, 0, err
	}

	return snapshots, total, nil
}

// ListByPlanID 根据计划ID分页查询受众快照
func (r *AudienceSnapshotRepositoryImpl) ListByPlanID(ctx context.Context, tenantID, planID int64, offset, limit int) ([]*entity.AudienceSnapshot, int64, error) {
	var snapshots []*entity.AudienceSnapshot
	var total int64

	// 查询总数
	if err := r.db.WithContext(ctx).Model(&entity.AudienceSnapshot{}).
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		Count(&total).Error; err != nil {
		r.logger.Error(ctx, "Failed to count audience snapshots by plan ID",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return nil, 0, err
	}

	// 查询数据
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&snapshots).Error; err != nil {
		r.logger.Error(ctx, "Failed to list audience snapshots by plan ID",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return nil, 0, err
	}

	return snapshots, total, nil
}

// Count 统计受众快照数量
func (r *AudienceSnapshotRepositoryImpl) Count(ctx context.Context, tenantID int64) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.AudienceSnapshot{}).
		Where("tenant_id = ?", tenantID).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to count audience snapshots",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return 0, err
	}

	return count, nil
}

// CountByPlanID 根据计划ID统计受众快照数量
func (r *AudienceSnapshotRepositoryImpl) CountByPlanID(ctx context.Context, tenantID, planID int64) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&entity.AudienceSnapshot{}).
		Where("tenant_id = ? AND plan_id = ?", tenantID, planID).
		Count(&count).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to count audience snapshots by plan ID",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return 0, err
	}

	return count, nil
}
