package container

import (
	"fmt"
	"time"

	"gitee.com/heiyee/platforms/email-system/pkg/config"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	// id 生成器暂未使用，移除以通过编译；如需请引入并注入实现

	// 应用服务
	contactService "gitee.com/heiyee/platforms/email-system/internal/application/contact/service"
	importService "gitee.com/heiyee/platforms/email-system/internal/application/import/service"
	segmentService "gitee.com/heiyee/platforms/email-system/internal/application/segment/service"
	sendService "gitee.com/heiyee/platforms/email-system/internal/application/send/service"
	tagService "gitee.com/heiyee/platforms/email-system/internal/application/tag/service"
	trackingService "gitee.com/heiyee/platforms/email-system/internal/application/tracking/service"

	// 领域服务
	ruleService "gitee.com/heiyee/platforms/email-system/internal/domain/rule/service"
	segmentDomainService "gitee.com/heiyee/platforms/email-system/internal/domain/segment/service"
	sendDomainService "gitee.com/heiyee/platforms/email-system/internal/domain/send/service"
	tagDomainService "gitee.com/heiyee/platforms/email-system/internal/domain/tag/service"

	// 仓储接口
	importRepository "gitee.com/heiyee/platforms/email-system/internal/domain/import/repository"

	// 客户端
	"gitee.com/heiyee/platforms/email-system/internal/infrastructure/client"

	// 仓储实现
	"gitee.com/heiyee/platforms/email-system/internal/infrastructure/persistence"

	// HTTP处理器
	"gitee.com/heiyee/platforms/email-system/internal/interfaces/http/handlers"

	// 领域实体
	contactEntity "gitee.com/heiyee/platforms/email-system/internal/domain/contact/entity"
	segmentEntity "gitee.com/heiyee/platforms/email-system/internal/domain/segment/entity"
	tagEntity "gitee.com/heiyee/platforms/email-system/internal/domain/tag/entity"
)

// DependencyContainer 依赖注入容器
type DependencyContainer struct {
	config *config.AppConfig
	logger logiface.Logger
	db     *gorm.DB
	// idGen  *id.IDGenerator

	// 仓储
	contactRepo              *persistence.ContactRepositoryImpl
	importJobRepo            importRepository.ImportJobRepository
	importScenarioConfigRepo importRepository.ImportScenarioConfigRepository
	importErrorRepo          importRepository.ImportErrorRepository
	importBatchRepo          importRepository.ImportBatchRepository
	tagRepo                  *persistence.TagRepositoryImpl
	contactTagRepo           *persistence.ContactTagRepositoryImpl
	segmentRepo              *persistence.SegmentRepositoryImpl
	segmentJobRepo           *persistence.SegmentJobRepositoryImpl
	sendPlanRepo             *persistence.SendPlanRepositoryImpl
	sendBatchRepo            *persistence.SendBatchRepositoryImpl
	audienceSnapshotRepo     *persistence.AudienceSnapshotRepositoryImpl
	sendRecordRepo           *persistence.SendRecordRepositoryImpl

	// 应用服务
	contactService  *contactService.ContactApplicationService
	importService   importService.ImportApplicationService
	tagService      *tagService.TagApplicationService
	segmentService  *segmentService.SegmentApplicationService
	sendService     *sendService.SendApplicationService
	trackingService *trackingService.TrackingApplicationService

	// 领域服务
	ruleEngine         *ruleService.RuleEngine
	tagRuleService     *tagDomainService.TagRuleService
	segmentRuleService *segmentDomainService.SegmentRuleService
	sendScheduler      *sendDomainService.SendScheduler

	// 客户端
	emailClient client.EmailClient

	// HTTP处理器
	contactHandler     *handlers.ContactHandler
	importHandler      *handlers.ImportHandler
	tagHandler         *handlers.TagHandler
	segmentHandler     *handlers.SegmentHandler
	unsubscribeHandler *handlers.UnsubscribeHandler
	ruleHandler        *handlers.RuleHandler
	sendHandler        *handlers.SendHandler
	trackingHandler    *handlers.TrackingHandler
	analyticsHandler   *handlers.AnalyticsHandler
}

// NewDependencyContainer 创建依赖注入容器
func NewDependencyContainer(config *config.AppConfig, logger logiface.Logger) *DependencyContainer {
	return &DependencyContainer{
		config: config,
		logger: logger,
	}
}

// Initialize 初始化依赖
func (c *DependencyContainer) Initialize() error {

	// 初始化数据库
	if err := c.initDatabase(); err != nil {
		return fmt.Errorf("failed to initialize database: %w", err)
	}

	// 初始化仓储
	c.initRepositories()

	// 初始化领域服务
	c.initDomainServices()

	// 初始化应用服务
	c.initApplicationServices()

	// 初始化HTTP处理器
	c.initHTTPHandlers()

	return nil
}

// initDatabase 初始化数据库
func (c *DependencyContainer) initDatabase() error {
	// 配置GORM日志级别
	logLevel := logger.Silent
	if c.config.Server.Mode == "debug" {
		logLevel = logger.Info
	}

	// 创建数据库连接
	// 构建DSN
	params := ""
	for key, value := range c.config.Database.MySQL.Params {
		if params != "" {
			params += "&"
		}
		params += fmt.Sprintf("%s=%s", key, value)
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?%s",
		c.config.Database.MySQL.Username,
		c.config.Database.MySQL.Password,
		c.config.Database.MySQL.Host,
		c.config.Database.MySQL.Port,
		c.config.Database.MySQL.Database,
		params)
	
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
	})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层的sql.DB对象
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get sql.DB: %w", err)
	}

	// 配置连接池
	sqlDB.SetMaxOpenConns(c.config.Database.MySQL.MaxOpenConns)
	sqlDB.SetMaxIdleConns(c.config.Database.MySQL.MaxIdleConns)
	
	// 解析连接生命周期
	connMaxLifetime, err := time.ParseDuration(c.config.Database.MySQL.ConnMaxLifetime)
	if err != nil {
		connMaxLifetime = 3600 * time.Second // 默认1小时
	}
	sqlDB.SetConnMaxLifetime(connMaxLifetime)
	
	// 解析连接空闲时间
	connMaxIdleTime, err := time.ParseDuration(c.config.Database.MySQL.ConnMaxIdleTime)
	if err != nil {
		connMaxIdleTime = 600 * time.Second // 默认10分钟
	}
	sqlDB.SetConnMaxIdleTime(connMaxIdleTime)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	c.db = db

	// 自动迁移数据库表
	if err := c.migrateDatabase(); err != nil {
		return fmt.Errorf("failed to migrate database: %w", err)
	}

	return nil
}

// migrateDatabase 迁移数据库表
func (c *DependencyContainer) migrateDatabase() error {
	// 迁移联系人相关表
	if err := c.db.AutoMigrate(&contactEntity.Contact{}); err != nil {
		return fmt.Errorf("failed to migrate contacts table: %w", err)
	}

	// 迁移标签相关表
	if err := c.db.AutoMigrate(&tagEntity.Tag{}); err != nil {
		return fmt.Errorf("failed to migrate tags table: %w", err)
	}

	if err := c.db.AutoMigrate(&tagEntity.ContactTag{}); err != nil {
		return fmt.Errorf("failed to migrate contact_tags table: %w", err)
	}

	// 迁移人群圈选相关表
	if err := c.db.AutoMigrate(&segmentEntity.Segment{}); err != nil {
		return fmt.Errorf("failed to migrate segments table: %w", err)
	}

	if err := c.db.AutoMigrate(&segmentEntity.SegmentJob{}); err != nil {
		return fmt.Errorf("failed to migrate segment_jobs table: %w", err)
	}

	return nil
}

// initRepositories 初始化仓储
func (c *DependencyContainer) initRepositories() {
	c.contactRepo = persistence.NewContactRepositoryImpl(c.db, c.logger).(*persistence.ContactRepositoryImpl)
	c.tagRepo = persistence.NewTagRepositoryImpl(c.db, c.logger).(*persistence.TagRepositoryImpl)
	c.contactTagRepo = persistence.NewContactTagRepositoryImpl(c.db, c.logger).(*persistence.ContactTagRepositoryImpl)
	c.segmentRepo = persistence.NewSegmentRepositoryImpl(c.db, c.logger).(*persistence.SegmentRepositoryImpl)
	c.segmentJobRepo = persistence.NewSegmentJobRepositoryImpl(c.db, c.logger).(*persistence.SegmentJobRepositoryImpl)

	// 导入相关仓储
	c.importJobRepo = persistence.NewImportJobRepository(c.db)
	c.importScenarioConfigRepo = persistence.NewImportScenarioConfigRepository(c.db)
	c.importErrorRepo = persistence.NewImportErrorRepository(c.db)
	c.importBatchRepo = persistence.NewImportBatchRepository(c.db)

	// 发送相关仓储
	c.sendPlanRepo = persistence.NewSendPlanRepository(c.db, c.logger).(*persistence.SendPlanRepositoryImpl)
	c.sendBatchRepo = persistence.NewSendBatchRepository(c.db, c.logger).(*persistence.SendBatchRepositoryImpl)
	c.audienceSnapshotRepo = persistence.NewAudienceSnapshotRepository(c.db, c.logger).(*persistence.AudienceSnapshotRepositoryImpl)
	c.sendRecordRepo = persistence.NewSendRecordRepository(c.db, c.logger).(*persistence.SendRecordRepositoryImpl)
}

// initDomainServices 初始化领域服务
func (c *DependencyContainer) initDomainServices() {
	// 初始化Email客户端
	c.emailClient = client.NewEmailClient(c.logger)

	// 初始化规则引擎
	c.ruleEngine = ruleService.NewRuleEngine(c.logger)

	// 初始化标签规则服务
	c.tagRuleService = tagDomainService.NewTagRuleService(
		c.logger,
		c.tagRepo,
		c.contactTagRepo,
		c.contactRepo,
		c.ruleEngine,
	)

	// 初始化人群圈选规则服务
	c.segmentRuleService = segmentDomainService.NewSegmentRuleService(
		c.logger,
		c.segmentRepo,
		c.segmentJobRepo,
		c.contactRepo,
		c.ruleEngine,
	)

	// 初始化发送调度器
	c.sendScheduler = sendDomainService.NewSendScheduler(
		c.logger,
		c.sendPlanRepo,
		c.sendBatchRepo,
		c.audienceSnapshotRepo,
		c.sendRecordRepo,
		c.segmentRepo,
		c.emailClient,
	)
}

// initApplicationServices 初始化应用服务
func (c *DependencyContainer) initApplicationServices() {
	c.contactService = contactService.NewContactApplicationService(
		c.logger,
		c.contactRepo,
	)

	// 初始化导入应用服务，暂时创建一个简单的实现
	c.importService = importService.NewImportApplicationService(
		c.importJobRepo,
		c.importScenarioConfigRepo,
		c.importErrorRepo,
		c.importBatchRepo,
		nil, // scenarioRouter - 稍后实现
	)

	c.tagService = tagService.NewTagApplicationService(
		c.logger,
		c.tagRepo,
		c.contactTagRepo,
		c.contactRepo,
	)

	c.segmentService = segmentService.NewSegmentApplicationService(
		c.logger,
		c.segmentRepo,
		c.segmentJobRepo,
		c.tagRepo,
		c.contactRepo,
		c.segmentRuleService,
	)

	c.sendService = sendService.NewSendApplicationService(
		c.logger,
		c.sendPlanRepo,
		c.sendBatchRepo,
		c.audienceSnapshotRepo,
		c.sendRecordRepo,
		c.sendScheduler,
	)

	c.trackingService = trackingService.NewTrackingApplicationService(
		c.logger,
	)
}

// initHTTPHandlers 初始化HTTP处理器
func (c *DependencyContainer) initHTTPHandlers() {
	baseURL := c.config.Server.BaseURL
	if baseURL == "" {
		baseURL = "http://localhost:8085"
	}

	c.contactHandler = handlers.NewContactHandler(
		c.logger,
		c.contactService,
	)

	c.importHandler = handlers.NewImportHandler(
		c.importService,
	)

	c.tagHandler = handlers.NewTagHandler(
		c.logger,
		c.tagService,
	)

	c.segmentHandler = handlers.NewSegmentHandler(
		c.logger,
		c.segmentService,
	)

	c.unsubscribeHandler = handlers.NewUnsubscribeHandler(
		c.logger,
		c.contactService,
		baseURL,
	)

	c.ruleHandler = handlers.NewRuleHandler(
		c.logger,
		c.ruleEngine,
	)

	c.sendHandler = handlers.NewSendHandler(
		c.logger,
		c.sendService,
	)

	c.trackingHandler = handlers.NewTrackingHandler(
		c.logger,
		c.trackingService,
	)

	c.analyticsHandler = handlers.NewAnalyticsHandler(
		c.logger,
	)
}

// GetDatabase 获取数据库连接
func (c *DependencyContainer) GetDatabase() *gorm.DB {
	return c.db
}

// GetIDGenerator 获取ID生成器
// func (c *DependencyContainer) GetIDGenerator() *id.IDGenerator {
//     return c.idGen
// }

// GetLogger 获取日志器
func (c *DependencyContainer) GetLogger() logiface.Logger {
	return c.logger
}

// GetContactHandler 获取联系人处理器
func (c *DependencyContainer) GetContactHandler() *handlers.ContactHandler {
	return c.contactHandler
}

// GetImportHandler 获取导入处理器
func (c *DependencyContainer) GetImportHandler() *handlers.ImportHandler {
	return c.importHandler
}

// GetTagHandler 获取标签处理器
func (c *DependencyContainer) GetTagHandler() *handlers.TagHandler {
	return c.tagHandler
}

// GetSegmentHandler 获取人群圈选处理器
func (c *DependencyContainer) GetSegmentHandler() *handlers.SegmentHandler {
	return c.segmentHandler
}

// GetUnsubscribeHandler 获取退订处理器
func (c *DependencyContainer) GetUnsubscribeHandler() *handlers.UnsubscribeHandler {
	return c.unsubscribeHandler
}

// GetRuleHandler 获取规则处理器
func (c *DependencyContainer) GetRuleHandler() *handlers.RuleHandler {
	return c.ruleHandler
}

// GetSendHandler 获取发送处理器
func (c *DependencyContainer) GetSendHandler() *handlers.SendHandler {
	return c.sendHandler
}

// GetTrackingHandler 获取追踪处理器
func (c *DependencyContainer) GetTrackingHandler() *handlers.TrackingHandler {
	return c.trackingHandler
}

// GetAnalyticsHandler 获取分析处理器
func (c *DependencyContainer) GetAnalyticsHandler() *handlers.AnalyticsHandler {
	return c.analyticsHandler
}

// GetContactService 获取联系人应用服务
func (c *DependencyContainer) GetContactService() *contactService.ContactApplicationService {
	return c.contactService
}

// GetTagService 获取标签应用服务
func (c *DependencyContainer) GetTagService() *tagService.TagApplicationService {
	return c.tagService
}

// GetSegmentService 获取人群圈选应用服务
func (c *DependencyContainer) GetSegmentService() *segmentService.SegmentApplicationService {
	return c.segmentService
}

// Close 关闭资源
func (c *DependencyContainer) Close() error {
	if c.db != nil {
		sqlDB, err := c.db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}
