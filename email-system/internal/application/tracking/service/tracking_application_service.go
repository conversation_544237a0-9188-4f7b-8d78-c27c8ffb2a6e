package service

import (
	"context"
	"fmt"
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/tracking/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/tracking/service"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// TrackingApplicationService 追踪应用服务
type TrackingApplicationService struct {
	eventRepo      TrackingEventRepository
	analyticsRepo  AnalyticsRepository
	classifier     *service.EventClassifier
	attributionSvc *service.AttributionService
	deduplicator   EventDeduplicator
	aggregator     MetricsAggregator
	eventPublisher EventPublisher
}

// TrackingEventRepository 追踪事件仓储接口
type TrackingEventRepository interface {
	Save(ctx context.Context, event *entity.TrackingEvent) error
	BatchSave(ctx context.Context, events []*entity.TrackingEvent) error
	GetByID(ctx context.Context, id string) (*entity.TrackingEvent, error)
	GetOpenEvents(campaignID string, timeWindow entity.TimeWindow) ([]*entity.TrackingEvent, error)
	GetClickEvents(campaignID string, timeWindow entity.TimeWindow) ([]*entity.TrackingEvent, error)
	GetConversionEvents(campaignID string, timeWindow entity.TimeWindow) ([]*entity.TrackingEvent, error)
	GetEventsBySubscriber(subscriberID string, timeWindow entity.TimeWindow) ([]*entity.TrackingEvent, error)
}

// AnalyticsRepository 分析仓储接口
type AnalyticsRepository interface {
	SaveMetric(ctx context.Context, metric *entity.AnalyticsMetric) error
	GetMetric(ctx context.Context, id string) (*entity.AnalyticsMetric, error)
	GetCampaignMetrics(ctx context.Context, campaignID string, timeWindow entity.TimeWindow) ([]*entity.AnalyticsMetric, error)
	UpdateMetric(ctx context.Context, metric *entity.AnalyticsMetric) error
}

// EventDeduplicator 事件去重器接口
type EventDeduplicator interface {
	IsDuplicate(ctx context.Context, event *entity.TrackingEvent) (bool, error)
	MarkProcessed(ctx context.Context, event *entity.TrackingEvent) error
}

// MetricsAggregator 指标聚合器接口
type MetricsAggregator interface {
	AggregateEvent(ctx context.Context, event *entity.TrackingEvent) error
	AggregateHourly(ctx context.Context, tenantID int64, hour time.Time) error
	AggregateDaily(ctx context.Context, tenantID int64, date time.Time) error
}

// EventPublisher 事件发布器接口
type EventPublisher interface {
	PublishTrackingEvent(ctx context.Context, event *entity.TrackingEvent) error
	PublishBatchEvents(ctx context.Context, events []*entity.TrackingEvent) error
}

// NewTrackingApplicationService 创建追踪应用服务 (简化版)
func NewTrackingApplicationService(
	logger logiface.Logger,
) *TrackingApplicationService {
	return &TrackingApplicationService{
		// TODO: 实现完整的依赖注入
	}
}

// ProcessTrackingEvent 处理追踪事件
func (s *TrackingApplicationService) ProcessTrackingEvent(ctx context.Context, event *entity.TrackingEvent) error {
	// 1. 去重检查
	isDuplicate, err := s.deduplicator.IsDuplicate(ctx, event)
	if err != nil {
		return fmt.Errorf("failed to check duplicate: %w", err)
	}

	// 2. 分类事件（如果是打开事件）
	if event.IsOpenEvent() {
		event.OpenClass = s.classifier.ClassifyOpenEvent(event)
	}

	// 3. 设置唯一性标识
	if !isDuplicate {
		event.IsUnique = true
		event.IsFirstHit = true
	}

	// 4. 保存事件
	if err := s.eventRepo.Save(ctx, event); err != nil {
		return fmt.Errorf("failed to save event: %w", err)
	}

	// 5. 标记已处理（用于去重）
	if err := s.deduplicator.MarkProcessed(ctx, event); err != nil {
		// 记录错误但不阻断流程
		// logger.Error("failed to mark event as processed", err)
	}

	// 6. 实时聚合
	if err := s.aggregator.AggregateEvent(ctx, event); err != nil {
		// 记录错误但不阻断流程
		// logger.Error("failed to aggregate event", err)
	}

	// 7. 发布事件（异步处理）
	go func() {
		if err := s.eventPublisher.PublishTrackingEvent(context.Background(), event); err != nil {
			// logger.Error("failed to publish event", err)
		}
	}()

	return nil
}

// BatchProcessTrackingEvents 批量处理追踪事件
func (s *TrackingApplicationService) BatchProcessTrackingEvents(ctx context.Context, events []*entity.TrackingEvent) ([]*ProcessResult, error) {
	results := make([]*ProcessResult, len(events))

	// 批量去重检查
	for i, event := range events {
		isDuplicate, err := s.deduplicator.IsDuplicate(ctx, event)
		if err != nil {
			results[i] = &ProcessResult{
				EventID: event.ID,
				Success: false,
				Error:   err.Error(),
			}
			continue
		}

		// 分类事件
		if event.IsOpenEvent() {
			event.OpenClass = s.classifier.ClassifyOpenEvent(event)
		}

		// 设置唯一性
		if !isDuplicate {
			event.IsUnique = true
			event.IsFirstHit = true
		}

		results[i] = &ProcessResult{
			EventID: event.ID,
			Success: true,
		}
	}

	// 批量保存
	validEvents := make([]*entity.TrackingEvent, 0, len(events))
	for i, event := range events {
		if results[i].Success {
			validEvents = append(validEvents, event)
		}
	}

	if len(validEvents) > 0 {
		if err := s.eventRepo.BatchSave(ctx, validEvents); err != nil {
			// 更新失败的结果
			for i := range results {
				if results[i].Success {
					results[i].Success = false
					results[i].Error = err.Error()
				}
			}
			return results, fmt.Errorf("failed to batch save events: %w", err)
		}

		// 批量标记已处理
		for _, event := range validEvents {
			_ = s.deduplicator.MarkProcessed(ctx, event)
		}

		// 批量聚合
		for _, event := range validEvents {
			_ = s.aggregator.AggregateEvent(ctx, event)
		}

		// 批量发布事件
		go func() {
			_ = s.eventPublisher.PublishBatchEvents(context.Background(), validEvents)
		}()
	}

	return results, nil
}

// GetCampaignStats 获取活动统计
func (s *TrackingApplicationService) GetCampaignStats(ctx context.Context, tenantID int64, campaignID string, timeWindow entity.TimeWindow) (*CampaignStatsResult, error) {
	// 1. 获取基础指标
	metrics, err := s.analyticsRepo.GetCampaignMetrics(ctx, campaignID, timeWindow)
	if err != nil {
		return nil, fmt.Errorf("failed to get campaign metrics: %w", err)
	}

	// 2. 计算人类打开数
	humanOpensResult, err := s.attributionSvc.CalculateHumanOpens(campaignID, timeWindow)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate human opens: %w", err)
	}

	// 3. 聚合统计数据
	result := s.aggregateStats(metrics, humanOpensResult)

	return result, nil
}

// ProcessResult 处理结果
type ProcessResult struct {
	EventID string `json:"event_id"`
	Success bool   `json:"success"`
	Error   string `json:"error,omitempty"`
}

// CampaignStatsResult 活动统计结果
type CampaignStatsResult struct {
	CampaignID string `json:"campaign_id"`

	// 基础指标
	Sends        int64 `json:"sends"`
	Delivered    int64 `json:"delivered"`
	Bounces      int64 `json:"bounces"`
	Complaints   int64 `json:"complaints"`
	Unsubscribes int64 `json:"unsubscribes"`

	// 打开指标
	GrossOpens          int64 `json:"gross_opens"`
	UniqueGrossOpens    int64 `json:"unique_gross_opens"`
	EstimatedHumanOpens int64 `json:"estimated_human_opens"`
	UniqueHumanOpens    int64 `json:"unique_human_opens"`

	// 点击指标
	Clicks       int64 `json:"clicks"`
	UniqueClicks int64 `json:"unique_clicks"`

	// 转化指标
	Conversions int64   `json:"conversions"`
	Revenue     float64 `json:"revenue"`
	Currency    string  `json:"currency"`

	// 计算指标
	DeliveryRate     float64 `json:"delivery_rate"`
	GrossOpenRate    float64 `json:"gross_open_rate"`
	HumanOpenRate    float64 `json:"human_open_rate"`
	ClickThroughRate float64 `json:"click_through_rate"`
	ConversionRate   float64 `json:"conversion_rate"`

	// 分类统计
	ClassificationStats *ClassificationStats `json:"classification_stats"`

	// 估算详情
	EstimationDetails *EstimationDetails `json:"estimation_details"`

	UpdatedAt time.Time `json:"updated_at"`
}

// ClassificationStats 分类统计
type ClassificationStats struct {
	MPPOpens     int64 `json:"mpp_opens"`
	ProxyOpens   int64 `json:"proxy_opens"`
	ScannerOpens int64 `json:"scanner_opens"`
	DirectOpens  int64 `json:"direct_opens"`
}

// EstimationDetails 估算详情
type EstimationDetails struct {
	ClickBackfillCount int64   `json:"click_backfill_count"`
	EstimatedCount     int64   `json:"estimated_count"`
	EstimationRate     float64 `json:"estimation_rate"`
	ModelVersion       string  `json:"model_version"`
}

// aggregateStats 聚合统计数据
func (s *TrackingApplicationService) aggregateStats(metrics []*entity.AnalyticsMetric, humanOpensResult *service.HumanOpenResult) *CampaignStatsResult {
	result := &CampaignStatsResult{
		ClassificationStats: &ClassificationStats{},
		EstimationDetails: &EstimationDetails{
			ClickBackfillCount: humanOpensResult.EstimationDetails.ClickbackfillCount,
			EstimatedCount:     humanOpensResult.EstimationDetails.EstimatedCount,
			EstimationRate:     humanOpensResult.EstimationDetails.EstimationRate,
			ModelVersion:       humanOpensResult.EstimationDetails.ModelVersion,
		},
		UpdatedAt: time.Now(),
	}

	// 聚合各个时间窗口的指标
	for _, metric := range metrics {
		result.Sends += metric.Sends
		result.Delivered += metric.Delivered
		result.Bounces += metric.Bounces
		result.Complaints += metric.Complaints
		result.Unsubscribes += metric.Unsubscribes
		result.Clicks += metric.Clicks
		result.UniqueClicks += metric.UniqueClicks
		result.Conversions += metric.Conversions
		result.Revenue += metric.Revenue

		if metric.Currency != "" {
			result.Currency = metric.Currency
		}

		// 分类统计
		result.ClassificationStats.MPPOpens += metric.MPPOpens
		result.ClassificationStats.ProxyOpens += metric.ProxyOpens
		result.ClassificationStats.ScannerOpens += metric.ScannerOpens
		result.ClassificationStats.DirectOpens += metric.DirectOpens
	}

	// 使用人类打开结果
	result.GrossOpens = humanOpensResult.GrossOpens
	result.UniqueGrossOpens = humanOpensResult.UniqueGrossOpens
	result.EstimatedHumanOpens = humanOpensResult.EstimatedHumanOpens
	result.UniqueHumanOpens = humanOpensResult.UniqueHumanOpens

	// 计算比率
	if result.Sends > 0 {
		result.DeliveryRate = float64(result.Delivered) / float64(result.Sends)
	}
	if result.Delivered > 0 {
		result.GrossOpenRate = float64(result.UniqueGrossOpens) / float64(result.Delivered)
		result.HumanOpenRate = float64(result.UniqueHumanOpens) / float64(result.Delivered)
		result.ClickThroughRate = float64(result.UniqueClicks) / float64(result.Delivered)
		result.ConversionRate = float64(result.Conversions) / float64(result.Delivered)
	}

	return result
}
