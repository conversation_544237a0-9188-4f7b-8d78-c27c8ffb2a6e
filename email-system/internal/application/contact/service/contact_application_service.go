package service

import (
	"context"
	"fmt"
	"gitee.com/heiyee/platforms/pkg/logiface"

	"gitee.com/heiyee/platforms/email-system/internal/application/contact/dto"
	"gitee.com/heiyee/platforms/email-system/internal/domain/contact/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/contact/repository"
)

// ContactApplicationService 联系人应用服务
type ContactApplicationService struct {
	logger      logiface.Logger
	contactRepo repository.ContactRepository
}

// NewContactApplicationService 创建联系人应用服务
func NewContactApplicationService(
	logger logiface.Logger,
	contactRepo repository.ContactRepository,
) *ContactApplicationService {
	return &ContactApplicationService{
		logger:      logger,
		contactRepo: contactRepo,
	}
}

// CreateContact 创建联系人
func (s *ContactApplicationService) CreateContact(ctx context.Context, req *dto.CreateContactRequest, tenantID int64) (*dto.ContactResponse, error) {
	s.logger.Info(ctx, "Creating contact",
		logiface.String("email", req.Email),
		logiface.Int64("tenant_id", tenantID))

	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 检查邮箱是否已存在
	exists, err := s.contactRepo.ExistsByEmail(ctx, tenantID, req.Email)
	if err != nil {
		s.logger.Error(ctx, "Failed to check email existence",
			logiface.Error(err),
			logiface.String("email", req.Email))
		return nil, entity.NewContactQueryFailedError(fmt.Sprintf("check email existence: %v", err))
	}

	if exists {
		return nil, entity.NewContactEmailExistsError(req.Email)
	}

	// 转换为实体
	contact := req.ToEntity(tenantID)

	// 验证实体
	if err := contact.Validate(); err != nil {
		return nil, err
	}

	// 创建联系人
	if err := s.contactRepo.Create(ctx, contact); err != nil {
		s.logger.Error(ctx, "Failed to create contact",
			logiface.Error(err),
			logiface.String("email", req.Email))
		return nil, entity.NewContactCreateFailedError(fmt.Sprintf("create contact: %v", err))
	}

	s.logger.Info(ctx, "Contact created successfully",
		logiface.Int64("contact_id", contact.ID),
		logiface.String("email", contact.Email))

	return dto.ToContactResponse(contact), nil
}

// UpdateContact 更新联系人
func (s *ContactApplicationService) UpdateContact(ctx context.Context, req *dto.UpdateContactRequest, tenantID int64) (*dto.ContactResponse, error) {
	s.logger.Info(ctx, "Updating contact",
		logiface.Int64("contact_id", req.ID),
		logiface.Int64("tenant_id", tenantID))

	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 查找联系人
	contact, err := s.contactRepo.FindByID(ctx, tenantID, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find contact",
			logiface.Error(err),
			logiface.Int64("contact_id", req.ID))
		return nil, entity.NewContactQueryFailedError(fmt.Sprintf("find contact: %v", err))
	}

	if contact == nil {
		return nil, entity.NewContactNotFoundError(fmt.Sprintf("id: %d", req.ID))
	}

	// 如果更新邮箱，检查新邮箱是否已存在
	if req.Email != "" && req.Email != contact.Email {
		exists, err := s.contactRepo.ExistsByEmail(ctx, tenantID, req.Email)
		if err != nil {
			s.logger.Error(ctx, "Failed to check email existence",
				logiface.Error(err),
				logiface.String("email", req.Email))
			return nil, entity.NewContactQueryFailedError(fmt.Sprintf("check email existence: %v", err))
		}

		if exists {
			return nil, entity.NewContactEmailExistsError(req.Email)
		}
	}

	// 应用更新
	req.ApplyToEntity(contact)

	// 验证更新后的实体
	if err := contact.Validate(); err != nil {
		return nil, err
	}

	// 更新联系人
	if err := s.contactRepo.Update(ctx, contact); err != nil {
		s.logger.Error(ctx, "Failed to update contact",
			logiface.Error(err),
			logiface.Int64("contact_id", req.ID))
		return nil, entity.NewContactUpdateFailedError(fmt.Sprintf("update contact: %v", err))
	}

	s.logger.Info(ctx, "Contact updated successfully",
		logiface.Int64("contact_id", contact.ID))

	return dto.ToContactResponse(contact), nil
}

// GetContact 获取联系人
func (s *ContactApplicationService) GetContact(ctx context.Context, req *dto.GetContactRequest, tenantID int64) (*dto.ContactResponse, error) {
	s.logger.Info(ctx, "Getting contact",
		logiface.Int64("contact_id", req.ID),
		logiface.String("email", req.Email),
		logiface.Int64("tenant_id", tenantID))

	var contact *entity.Contact
	var err error

	if req.ID > 0 {
		contact, err = s.contactRepo.FindByID(ctx, tenantID, req.ID)
	} else if req.Email != "" {
		contact, err = s.contactRepo.FindByEmail(ctx, tenantID, req.Email)
	} else {
		return nil, entity.NewContactValidationError("id_or_email", "必须提供联系人ID或邮箱地址")
	}

	if err != nil {
		s.logger.Error(ctx, "Failed to find contact",
			logiface.Error(err),
			logiface.Int64("contact_id", req.ID),
			logiface.String("email", req.Email))
		return nil, entity.NewContactQueryFailedError(fmt.Sprintf("find contact: %v", err))
	}

	if contact == nil {
		identifier := fmt.Sprintf("id: %d", req.ID)
		if req.Email != "" {
			identifier = fmt.Sprintf("email: %s", req.Email)
		}
		return nil, entity.NewContactNotFoundError(identifier)
	}

	return dto.ToContactResponse(contact), nil
}

// SearchContacts 搜索联系人
func (s *ContactApplicationService) SearchContacts(ctx context.Context, req *dto.SearchContactsRequest, tenantID int64) (*dto.ContactListResponse, error) {
	s.logger.Info(ctx, "Searching contacts",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("page", req.Page),
		logiface.Int("size", req.Size))

	// 构建搜索参数
	params := repository.NewSearchParams(tenantID)

	// 设置分页
	page := req.Page
	if page <= 0 {
		page = 1
	}
	size := req.Size
	if size <= 0 {
		size = 20
	}
	params.WithPagination(page, size)

	// 设置排序
	if req.Sort.Field != "" {
		order := req.Sort.Order
		if order == "" {
			order = "desc"
		}
		params.WithSort(req.Sort.Field, order)
	}

	// 设置过滤条件
	filters := &repository.SearchFilters{
		Email:             req.Filters.Email,
		Keyword:           req.Filters.Keyword,
		PreferredLanguage: req.Filters.PreferredLanguage,
		CountryCode:       req.Filters.CountryCode,
		Attributes:        req.Filters.Attributes,
		ExcludeContactIDs: req.Filters.ExcludeContactIDs,
		ExcludeTagIDs:     req.Filters.ExcludeTagIDs,
		ExcludeListIDs:    req.Filters.ExcludeListIDs,
	}

	if req.Filters.Status != "" {
		filters.Status = entity.ContactStatus(req.Filters.Status)
	}

	// 处理时间范围
	if req.Filters.CreatedAtRange != nil {
		filters.CreatedAtRange = &repository.TimeRange{
			Start: &req.Filters.CreatedAtRange.Start,
			End:   &req.Filters.CreatedAtRange.End,
		}
	}

	params.WithFilters(filters)

	// 验证参数
	if err := params.Validate(); err != nil {
		return nil, err
	}

	// 执行搜索
	result, err := s.contactRepo.Search(ctx, params)
	if err != nil {
		s.logger.Error(ctx, "Failed to search contacts",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, entity.NewContactQueryFailedError(fmt.Sprintf("search contacts: %v", err))
	}

	s.logger.Info(ctx, "Contacts searched successfully",
		logiface.Int64("total", result.Total),
		logiface.Int("count", len(result.Contacts)))

	return dto.ToContactListResponse(result.Contacts, result.Total, page, size), nil
}

// DeleteContact 删除联系人
func (s *ContactApplicationService) DeleteContact(ctx context.Context, req *dto.DeleteContactRequest, tenantID int64) error {
	s.logger.Info(ctx, "Deleting contact",
		logiface.Int64("contact_id", req.ID),
		logiface.Int64("tenant_id", tenantID))

	if req.ID <= 0 {
		return entity.NewContactValidationError("id", "联系人ID不能为空")
	}

	// 检查联系人是否存在
	contact, err := s.contactRepo.FindByID(ctx, tenantID, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find contact",
			logiface.Error(err),
			logiface.Int64("contact_id", req.ID))
		return entity.NewContactQueryFailedError(fmt.Sprintf("find contact: %v", err))
	}

	if contact == nil {
		return entity.NewContactNotFoundError(fmt.Sprintf("id: %d", req.ID))
	}

	// 删除联系人
	if err := s.contactRepo.Delete(ctx, tenantID, req.ID); err != nil {
		s.logger.Error(ctx, "Failed to delete contact",
			logiface.Error(err),
			logiface.Int64("contact_id", req.ID))
		return entity.NewContactDeleteFailedError(fmt.Sprintf("delete contact: %v", err))
	}

	s.logger.Info(ctx, "Contact deleted successfully",
		logiface.Int64("contact_id", req.ID))

	return nil
}

// BatchCreateContacts 批量创建联系人
func (s *ContactApplicationService) BatchCreateContacts(ctx context.Context, req *dto.BatchCreateContactsRequest, tenantID int64) ([]*dto.ContactResponse, error) {
	s.logger.Info(ctx, "Batch creating contacts",
		logiface.Int("count", len(req.Contacts)),
		logiface.Int64("tenant_id", tenantID))

	if len(req.Contacts) == 0 {
		return nil, entity.NewContactValidationError("contacts", "联系人列表不能为空")
	}

	// 验证所有请求
	for i, contactReq := range req.Contacts {
		if err := contactReq.Validate(); err != nil {
			return nil, fmt.Errorf("contact[%d]: %w", i, err)
		}
	}

	// 转换为实体
	contacts := make([]*entity.Contact, len(req.Contacts))
	for i, contactReq := range req.Contacts {
		contact := contactReq.ToEntity(tenantID)
		if err := contact.Validate(); err != nil {
			return nil, fmt.Errorf("contact[%d]: %w", i, err)
		}
		contacts[i] = contact
	}

	// 批量创建
	if err := s.contactRepo.BatchCreate(ctx, contacts); err != nil {
		s.logger.Error(ctx, "Failed to batch create contacts",
			logiface.Error(err),
			logiface.Int("count", len(contacts)))
		return nil, entity.NewContactCreateFailedError(fmt.Sprintf("batch create contacts: %v", err))
	}

	// 转换为响应
	responses := make([]*dto.ContactResponse, len(contacts))
	for i, contact := range contacts {
		responses[i] = dto.ToContactResponse(contact)
	}

	s.logger.Info(ctx, "Contacts batch created successfully",
		logiface.Int("count", len(contacts)))

	return responses, nil
}

// BatchUpdateContacts 批量更新联系人
func (s *ContactApplicationService) BatchUpdateContacts(ctx context.Context, req *dto.BatchUpdateContactsRequest, tenantID int64) ([]*dto.ContactResponse, error) {
	s.logger.Info(ctx, "Batch updating contacts",
		logiface.Int("count", len(req.Contacts)),
		logiface.Int64("tenant_id", tenantID))

	if len(req.Contacts) == 0 {
		return nil, entity.NewContactValidationError("contacts", "联系人列表不能为空")
	}

	// 验证所有请求
	for i, contactReq := range req.Contacts {
		if err := contactReq.Validate(); err != nil {
			return nil, fmt.Errorf("contact[%d]: %w", i, err)
		}
	}

	// 获取所有联系人ID
	contactIDs := make([]int64, len(req.Contacts))
	for i, contactReq := range req.Contacts {
		contactIDs[i] = contactReq.ID
	}

	// 批量查找联系人
	existingContacts, err := s.contactRepo.FindByIDs(ctx, tenantID, contactIDs)
	if err != nil {
		s.logger.Error(ctx, "Failed to find contacts",
			logiface.Error(err),
			logiface.Any("contact_ids", contactIDs))
		return nil, entity.NewContactQueryFailedError(fmt.Sprintf("find contacts: %v", err))
	}

	// 创建ID到联系人的映射
	contactMap := make(map[int64]*entity.Contact)
	for _, contact := range existingContacts {
		contactMap[contact.ID] = contact
	}

	// 应用更新
	updatedContacts := make([]*entity.Contact, 0, len(req.Contacts))
	for i, contactReq := range req.Contacts {
		contact, exists := contactMap[contactReq.ID]
		if !exists {
			return nil, entity.NewContactNotFoundError(fmt.Sprintf("contact[%d] id: %d", i, contactReq.ID))
		}

		// 克隆联系人以避免修改原始数据
		updatedContact := contact.Clone()
		contactReq.ApplyToEntity(updatedContact)

		if err := updatedContact.Validate(); err != nil {
			return nil, fmt.Errorf("contact[%d]: %w", i, err)
		}

		updatedContacts = append(updatedContacts, updatedContact)
	}

	// 批量更新
	if err := s.contactRepo.BatchUpdate(ctx, updatedContacts); err != nil {
		s.logger.Error(ctx, "Failed to batch update contacts",
			logiface.Error(err),
			logiface.Int("count", len(updatedContacts)))
		return nil, entity.NewContactUpdateFailedError(fmt.Sprintf("batch update contacts: %v", err))
	}

	// 转换为响应
	responses := make([]*dto.ContactResponse, len(updatedContacts))
	for i, contact := range updatedContacts {
		responses[i] = dto.ToContactResponse(contact)
	}

	s.logger.Info(ctx, "Contacts batch updated successfully",
		logiface.Int("count", len(updatedContacts)))

	return responses, nil
}

// UpdateContactStatus 更新联系人状态
func (s *ContactApplicationService) UpdateContactStatus(ctx context.Context, req *dto.UpdateContactStatusRequest, tenantID int64) error {
	s.logger.Info(ctx, "Updating contact status",
		logiface.Any("contact_ids", req.ContactIDs),
		logiface.String("status", req.Status),
		logiface.Int64("tenant_id", tenantID))

	if len(req.ContactIDs) == 0 {
		return entity.NewContactValidationError("contact_ids", "联系人ID列表不能为空")
	}

	if !entity.ContactStatus(req.Status).IsValid() {
		return entity.NewContactValidationError("status", "无效的联系人状态")
	}

	// 更新联系人状态
	if err := s.contactRepo.UpdateStatus(ctx, tenantID, req.ContactIDs, entity.ContactStatus(req.Status)); err != nil {
		s.logger.Error(ctx, "Failed to update contact status",
			logiface.Error(err),
			logiface.Any("contact_ids", req.ContactIDs),
			logiface.String("status", req.Status))
		return entity.NewContactUpdateFailedError(fmt.Sprintf("update contact status: %v", err))
	}

	s.logger.Info(ctx, "Contact status updated successfully",
		logiface.Int("count", len(req.ContactIDs)),
		logiface.String("status", req.Status))

	return nil
}

// Unsubscribe 处理退订请求
func (s *ContactApplicationService) Unsubscribe(ctx context.Context, req *dto.UnsubscribeRequest, tenantID int64) (*dto.UnsubscribeResponse, error) {
	s.logger.Info(ctx, "Processing unsubscribe request",
		logiface.String("token", req.Token),
		logiface.Int64("tenant_id", tenantID))

	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 根据令牌查找联系人
	contact, err := s.contactRepo.FindByUnsubscribeToken(ctx, tenantID, req.Token)
	if err != nil {
		s.logger.Error(ctx, "Failed to find contact by unsubscribe token",
			logiface.Error(err),
			logiface.String("token", req.Token))
		return nil, entity.NewContactQueryFailedError(fmt.Sprintf("find contact by token: %v", err))
	}

	if contact == nil {
		return &dto.UnsubscribeResponse{
			Success: false,
			Message: "无效的退订链接或链接已过期",
		}, nil
	}

	// 检查是否已经退订
	if contact.IsUnsubscribed() {
		return &dto.UnsubscribeResponse{
			Success:   true,
			Message:   "您已经成功退订",
			ContactID: contact.ID,
		}, nil
	}

	// 执行退订操作
	contact.Unsubscribe()

	// 更新联系人
	if err := s.contactRepo.Update(ctx, contact); err != nil {
		s.logger.Error(ctx, "Failed to update contact for unsubscribe",
			logiface.Error(err),
			logiface.Int64("contact_id", contact.ID))
		return nil, entity.NewContactUpdateFailedError(fmt.Sprintf("update contact for unsubscribe: %v", err))
	}

	s.logger.Info(ctx, "Contact unsubscribed successfully",
		logiface.Int64("contact_id", contact.ID),
		logiface.String("email", contact.Email))

	return &dto.UnsubscribeResponse{
		Success:   true,
		Message:   "退订成功",
		ContactID: contact.ID,
	}, nil
}

// GrantConsent 授予同意
func (s *ContactApplicationService) GrantConsent(ctx context.Context, req *dto.ConsentRequest, tenantID int64) (*dto.ConsentResponse, error) {
	s.logger.Info(ctx, "Granting consent",
		logiface.String("email", req.Email),
		logiface.String("source", req.Source),
		logiface.Int64("tenant_id", tenantID))

	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 查找联系人
	contact, err := s.contactRepo.FindByEmail(ctx, tenantID, req.Email)
	if err != nil {
		s.logger.Error(ctx, "Failed to find contact by email",
			logiface.Error(err),
			logiface.String("email", req.Email))
		return nil, entity.NewContactQueryFailedError(fmt.Sprintf("find contact by email: %v", err))
	}

	if contact == nil {
		return &dto.ConsentResponse{
			Success: false,
			Message: "联系人不存在",
		}, nil
	}

	// 授予同意
	contact.GrantConsent(req.Source, req.IP, req.UserAgent)

	// 如果联系人之前是退订状态，重新激活
	if contact.Status == entity.ContactStatusUnsubscribed {
		contact.Status = entity.ContactStatusActive
		contact.UnsubscribedAt = nil
	}

	// 更新联系人
	if err := s.contactRepo.Update(ctx, contact); err != nil {
		s.logger.Error(ctx, "Failed to update contact for consent",
			logiface.Error(err),
			logiface.Int64("contact_id", contact.ID))
		return nil, entity.NewContactUpdateFailedError(fmt.Sprintf("update contact for consent: %v", err))
	}

	s.logger.Info(ctx, "Consent granted successfully",
		logiface.Int64("contact_id", contact.ID),
		logiface.String("email", contact.Email))

	return &dto.ConsentResponse{
		Success:   true,
		Message:   "同意授予成功",
		ContactID: contact.ID,
	}, nil
}

// WithdrawConsent 撤回同意
func (s *ContactApplicationService) WithdrawConsent(ctx context.Context, req *dto.WithdrawConsentRequest, tenantID int64) (*dto.ConsentResponse, error) {
	s.logger.Info(ctx, "Withdrawing consent",
		logiface.String("email", req.Email),
		logiface.Int64("tenant_id", tenantID))

	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 查找联系人
	contact, err := s.contactRepo.FindByEmail(ctx, tenantID, req.Email)
	if err != nil {
		s.logger.Error(ctx, "Failed to find contact by email",
			logiface.Error(err),
			logiface.String("email", req.Email))
		return nil, entity.NewContactQueryFailedError(fmt.Sprintf("find contact by email: %v", err))
	}

	if contact == nil {
		return &dto.ConsentResponse{
			Success: false,
			Message: "联系人不存在",
		}, nil
	}

	// 撤回同意
	contact.WithdrawConsent()

	// 更新联系人
	if err := s.contactRepo.Update(ctx, contact); err != nil {
		s.logger.Error(ctx, "Failed to update contact for consent withdrawal",
			logiface.Error(err),
			logiface.Int64("contact_id", contact.ID))
		return nil, entity.NewContactUpdateFailedError(fmt.Sprintf("update contact for consent withdrawal: %v", err))
	}

	s.logger.Info(ctx, "Consent withdrawn successfully",
		logiface.Int64("contact_id", contact.ID),
		logiface.String("email", contact.Email))

	return &dto.ConsentResponse{
		Success:   true,
		Message:   "同意撤回成功",
		ContactID: contact.ID,
	}, nil
}

// GenerateUnsubscribeLink 生成退订链接
func (s *ContactApplicationService) GenerateUnsubscribeLink(ctx context.Context, req *dto.GenerateUnsubscribeLinkRequest, tenantID int64, baseURL string) (*dto.UnsubscribeLinkResponse, error) {
	s.logger.Info(ctx, "Generating unsubscribe link",
		logiface.Int64("contact_id", req.ContactID),
		logiface.Int64("tenant_id", tenantID))

	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 查找联系人
	contact, err := s.contactRepo.FindByID(ctx, tenantID, req.ContactID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find contact",
			logiface.Error(err),
			logiface.Int64("contact_id", req.ContactID))
		return nil, entity.NewContactQueryFailedError(fmt.Sprintf("find contact: %v", err))
	}

	if contact == nil {
		return nil, entity.NewContactNotFoundError(fmt.Sprintf("id: %d", req.ContactID))
	}

	// 生成或更新退订令牌
	contact.SetUnsubscribeToken()

	// 更新联系人
	if err := s.contactRepo.Update(ctx, contact); err != nil {
		s.logger.Error(ctx, "Failed to update contact with unsubscribe token",
			logiface.Error(err),
			logiface.Int64("contact_id", contact.ID))
		return nil, entity.NewContactUpdateFailedError(fmt.Sprintf("update contact with token: %v", err))
	}

	// 构建退订URL
	unsubscribeURL := fmt.Sprintf("%s/unsubscribe?token=%s", baseURL, *contact.UnsubscribeToken)

	s.logger.Info(ctx, "Unsubscribe link generated successfully",
		logiface.Int64("contact_id", contact.ID))

	return &dto.UnsubscribeLinkResponse{
		UnsubscribeURL: unsubscribeURL,
		Token:          *contact.UnsubscribeToken,
		ExpiresAt:      "永不过期", // 可以根据需要设置过期时间
	}, nil
}

// GetConsentStatus 获取同意状态
func (s *ContactApplicationService) GetConsentStatus(ctx context.Context, email string, tenantID int64) (*dto.ConsentStatusResponse, error) {
	s.logger.Info(ctx, "Getting consent status",
		logiface.String("email", email),
		logiface.Int64("tenant_id", tenantID))

	if email == "" {
		return nil, entity.NewContactValidationError("email", "邮箱地址不能为空")
	}

	// 查找联系人
	contact, err := s.contactRepo.FindByEmail(ctx, tenantID, email)
	if err != nil {
		s.logger.Error(ctx, "Failed to find contact by email",
			logiface.Error(err),
			logiface.String("email", email))
		return nil, entity.NewContactQueryFailedError(fmt.Sprintf("find contact by email: %v", err))
	}

	if contact == nil {
		return nil, entity.NewContactNotFoundError(fmt.Sprintf("email: %s", email))
	}

	return dto.ToConsentStatusResponse(contact), nil
}

// GetUnsubscribePageData 获取退订页面数据
func (s *ContactApplicationService) GetUnsubscribePageData(ctx context.Context, token string, tenantID int64) (*dto.UnsubscribePageData, error) {
	s.logger.Info(ctx, "Getting unsubscribe page data",
		logiface.String("token", token),
		logiface.Int64("tenant_id", tenantID))

	if token == "" {
		return nil, entity.NewContactValidationError("token", "退订令牌不能为空")
	}

	// 根据令牌查找联系人
	contact, err := s.contactRepo.FindByUnsubscribeToken(ctx, tenantID, token)
	if err != nil {
		s.logger.Error(ctx, "Failed to find contact by unsubscribe token",
			logiface.Error(err),
			logiface.String("token", token))
		return nil, entity.NewContactQueryFailedError(fmt.Sprintf("find contact by token: %v", err))
	}

	pageData := &dto.UnsubscribePageData{
		Token:         token,
		ContactExists: contact != nil,
	}

	if contact != nil {
		pageData.Email = contact.Email
		pageData.AlreadyUnsubscribed = contact.IsUnsubscribed()
		pageData.PreferredLanguage = contact.PreferredLanguage
	}

	return pageData, nil
}
