package dto

import (
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/contact/entity"
)

// UnsubscribeRequest 退订请求
type UnsubscribeRequest struct {
	Token  string `json:"token" binding:"required"`
	Reason string `json:"reason" binding:"omitempty,max=255"`
}

// ConsentRequest 同意请求
type ConsentRequest struct {
	Email     string `json:"email" binding:"required,email"`
	Source    string `json:"source" binding:"omitempty,max=64"`
	IP        string `json:"ip" binding:"omitempty"`
	UserAgent string `json:"user_agent" binding:"omitempty,max=512"`
}

// WithdrawConsentRequest 撤回同意请求
type WithdrawConsentRequest struct {
	Email  string `json:"email" binding:"required,email"`
	Reason string `json:"reason" binding:"omitempty,max=255"`
}

// GenerateUnsubscribeLinkRequest 生成退订链接请求
type GenerateUnsubscribeLinkRequest struct {
	ContactID int64 `json:"contact_id" binding:"required"`
}

// UnsubscribeResponse 退订响应
type UnsubscribeResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	ContactID int64  `json:"contact_id,omitempty"`
}

// ConsentResponse 同意响应
type ConsentResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	ContactID int64  `json:"contact_id,omitempty"`
}

// UnsubscribeLinkResponse 退订链接响应
type UnsubscribeLinkResponse struct {
	UnsubscribeURL string `json:"unsubscribe_url"`
	Token          string `json:"token"`
	ExpiresAt      string `json:"expires_at"`
}

// ConsentStatusResponse 同意状态响应
type ConsentStatusResponse struct {
	ContactID       int64      `json:"contact_id"`
	Email           string     `json:"email"`
	ConsentStatus   string     `json:"consent_status"`
	ConsentSource   *string    `json:"consent_source"`
	ConsentAt       *time.Time `json:"consent_at"`
	ConsentIP       *string    `json:"consent_ip"`
	IsUnsubscribed  bool       `json:"is_unsubscribed"`
	UnsubscribedAt  *time.Time `json:"unsubscribed_at"`
	CanReceiveEmail bool       `json:"can_receive_email"`
}

// UnsubscribePageData 退订页面数据
type UnsubscribePageData struct {
	Token               string `json:"token"`
	Email               string `json:"email"`
	ContactExists       bool   `json:"contact_exists"`
	AlreadyUnsubscribed bool   `json:"already_unsubscribed"`
	PreferredLanguage   string `json:"preferred_language"`
}

// ToConsentStatusResponse 转换为同意状态响应
func ToConsentStatusResponse(contact *entity.Contact) *ConsentStatusResponse {
	return &ConsentStatusResponse{
		ContactID:       contact.ID,
		Email:           contact.Email,
		ConsentStatus:   string(contact.ConsentStatus),
		ConsentSource:   contact.ConsentSource,
		ConsentAt:       contact.ConsentAt,
		ConsentIP:       contact.ConsentIP,
		IsUnsubscribed:  contact.IsUnsubscribed(),
		UnsubscribedAt:  contact.UnsubscribedAt,
		CanReceiveEmail: contact.CanReceiveEmail(),
	}
}

// Validate 验证退订请求
func (req *UnsubscribeRequest) Validate() error {
	if req.Token == "" {
		return entity.NewContactValidationError("token", "退订令牌不能为空")
	}

	if len(req.Token) < 16 {
		return entity.NewContactValidationError("token", "无效的退订令牌")
	}

	return nil
}

// Validate 验证同意请求
func (req *ConsentRequest) Validate() error {
	if req.Email == "" {
		return entity.NewContactValidationError("email", "邮箱地址不能为空")
	}

	// 这里可以添加更复杂的邮箱验证逻辑
	if len(req.Email) > 255 {
		return entity.NewContactValidationError("email", "邮箱地址长度不能超过255个字符")
	}

	return nil
}

// Validate 验证撤回同意请求
func (req *WithdrawConsentRequest) Validate() error {
	if req.Email == "" {
		return entity.NewContactValidationError("email", "邮箱地址不能为空")
	}

	if len(req.Email) > 255 {
		return entity.NewContactValidationError("email", "邮箱地址长度不能超过255个字符")
	}

	return nil
}

// Validate 验证生成退订链接请求
func (req *GenerateUnsubscribeLinkRequest) Validate() error {
	if req.ContactID <= 0 {
		return entity.NewContactValidationError("contact_id", "联系人ID不能为空")
	}

	return nil
}
