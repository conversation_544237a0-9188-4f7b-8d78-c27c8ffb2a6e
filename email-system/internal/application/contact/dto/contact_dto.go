package dto

import (
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/contact/entity"
)

// CreateContactRequest 创建联系人请求
type CreateContactRequest struct {
	Email             string                 `json:"email" binding:"required,email"`
	Status            string                 `json:"status" binding:"omitempty,oneof=active suppressed unconfirmed bounced complained"`
	PreferredLanguage string                 `json:"preferred_language" binding:"omitempty"`
	CountryCode       string                 `json:"country_code" binding:"omitempty,len=2"`
	Timezone          string                 `json:"timezone" binding:"omitempty"`
	Notes             string                 `json:"notes" binding:"omitempty"`
	Attributes        map[string]interface{} `json:"attributes" binding:"omitempty"`
	Lists             []int64                `json:"lists" binding:"omitempty"`
	Tags              []string               `json:"tags" binding:"omitempty"`
}

// UpdateContactRequest 更新联系人请求
type UpdateContactRequest struct {
	ID                int64                  `json:"id" binding:"required"`
	Email             string                 `json:"email" binding:"omitempty,email"`
	Status            string                 `json:"status" binding:"omitempty,oneof=active suppressed unconfirmed bounced complained"`
	PreferredLanguage string                 `json:"preferred_language" binding:"omitempty"`
	CountryCode       string                 `json:"country_code" binding:"omitempty,len=2"`
	Timezone          string                 `json:"timezone" binding:"omitempty"`
	Notes             string                 `json:"notes" binding:"omitempty"`
	Attributes        map[string]interface{} `json:"attributes" binding:"omitempty"`
	MergePolicy       string                 `json:"merge_policy" binding:"omitempty,oneof=keep_existing new_overwrite most_recent concat"`
}

// GetContactRequest 获取联系人请求
type GetContactRequest struct {
	ID     int64  `json:"id" binding:"omitempty"`
	Email  string `json:"email" binding:"omitempty,email"`
	Fields string `json:"fields" binding:"omitempty"`
}

// SearchContactsRequest 搜索联系人请求
type SearchContactsRequest struct {
	Filters SearchFilters `json:"filters" binding:"omitempty"`
	Sort    SortOptions   `json:"sort" binding:"omitempty"`
	Page    int           `json:"page" binding:"omitempty,min=1"`
	Size    int           `json:"size" binding:"omitempty,min=1,max=1000"`
}

// SearchFilters 搜索过滤条件
type SearchFilters struct {
	Status            string                 `json:"status" binding:"omitempty,oneof=active suppressed unconfirmed bounced complained"`
	Email             string                 `json:"email" binding:"omitempty"`
	Lists             []int64                `json:"lists" binding:"omitempty"`
	Tags              []string               `json:"tags" binding:"omitempty"`
	PreferredLanguage string                 `json:"preferred_language" binding:"omitempty"`
	CountryCode       string                 `json:"country_code" binding:"omitempty,len=2"`
	Attributes        map[string]interface{} `json:"attributes" binding:"omitempty"`
	CreatedAtRange    *TimeRange             `json:"created_at_range" binding:"omitempty"`
	LastActivityRange *TimeRange             `json:"last_activity_range" binding:"omitempty"`
	Keyword           string                 `json:"keyword" binding:"omitempty"`
	ExcludeContactIDs []int64                `json:"exclude_contact_ids" binding:"omitempty"`
	ExcludeTagIDs     []int64                `json:"exclude_tag_ids" binding:"omitempty"`
	ExcludeListIDs    []int64                `json:"exclude_list_ids" binding:"omitempty"`
}

// TimeRange 时间范围
type TimeRange struct {
	Start string `json:"start" binding:"omitempty"`
	End   string `json:"end" binding:"omitempty"`
}

// SortOptions 排序选项
type SortOptions struct {
	Field string `json:"field" binding:"omitempty"`
	Order string `json:"order" binding:"omitempty,oneof=asc desc"`
}

// DeleteContactRequest 删除联系人请求
type DeleteContactRequest struct {
	ID int64 `json:"id" binding:"required"`
}

// BatchCreateContactsRequest 批量创建联系人请求
type BatchCreateContactsRequest struct {
	Contacts []CreateContactRequest `json:"contacts" binding:"required,dive"`
}

// BatchUpdateContactsRequest 批量更新联系人请求
type BatchUpdateContactsRequest struct {
	Contacts []UpdateContactRequest `json:"contacts" binding:"required,dive"`
}

// UpdateContactStatusRequest 更新联系人状态请求
type UpdateContactStatusRequest struct {
	ContactIDs []int64 `json:"contact_ids" binding:"required"`
	Status     string  `json:"status" binding:"required,oneof=active suppressed unconfirmed bounced complained"`
}

// ContactResponse 联系人响应
type ContactResponse struct {
	ID                int64                  `json:"id"`
	Email             string                 `json:"email"`
	Status            string                 `json:"status"`
	PreferredLanguage string                 `json:"preferred_language"`
	CountryCode       *string                `json:"country_code"`
	Timezone          *string                `json:"timezone"`
	Notes             *string                `json:"notes"`
	Attributes        map[string]interface{} `json:"attributes"`
	CreatedAt         time.Time              `json:"created_at"`
	UpdatedAt         time.Time              `json:"updated_at"`
}

// ContactListResponse 联系人列表响应
type ContactListResponse struct {
	Contacts   []ContactResponse `json:"contacts"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	Size       int               `json:"size"`
	TotalPages int               `json:"total_pages"`
}

// ContactSummaryResponse 联系人摘要响应
type ContactSummaryResponse struct {
	ID                int64     `json:"id"`
	Email             string    `json:"email"`
	Status            string    `json:"status"`
	PreferredLanguage string    `json:"preferred_language"`
	CountryCode       *string   `json:"country_code"`
	CreatedAt         time.Time `json:"created_at"`
}

// ToContactResponse 转换为联系人响应
func ToContactResponse(contact *entity.Contact) *ContactResponse {
	return &ContactResponse{
		ID:                contact.ID,
		Email:             contact.Email,
		Status:            string(contact.Status),
		PreferredLanguage: contact.PreferredLanguage,
		CountryCode:       contact.CountryCode,
		Timezone:          contact.Timezone,
		Notes:             contact.Notes,
		Attributes:        contact.Attributes,
		CreatedAt:         contact.CreatedAt,
		UpdatedAt:         contact.UpdatedAt,
	}
}

// ToContactSummaryResponse 转换为联系人摘要响应
func ToContactSummaryResponse(contact *entity.Contact) *ContactSummaryResponse {
	return &ContactSummaryResponse{
		ID:                contact.ID,
		Email:             contact.Email,
		Status:            string(contact.Status),
		PreferredLanguage: contact.PreferredLanguage,
		CountryCode:       contact.CountryCode,
		CreatedAt:         contact.CreatedAt,
	}
}

// ToContactListResponse 转换为联系人列表响应
func ToContactListResponse(contacts []*entity.Contact, total int64, page, size int) *ContactListResponse {
	contactResponses := make([]ContactResponse, len(contacts))
	for i, contact := range contacts {
		contactResponses[i] = *ToContactResponse(contact)
	}

	totalPages := int(total) / size
	if int(total)%size > 0 {
		totalPages++
	}

	return &ContactListResponse{
		Contacts:   contactResponses,
		Total:      total,
		Page:       page,
		Size:       size,
		TotalPages: totalPages,
	}
}

// ToEntity 转换为联系人实体
func (req *CreateContactRequest) ToEntity(tenantID int64) *entity.Contact {
	contact := &entity.Contact{
		TenantID:          tenantID,
		Email:             req.Email,
		Status:            entity.ContactStatusActive,
		PreferredLanguage: "zh-CN",
		Attributes:        req.Attributes,
	}

	if req.Status != "" {
		contact.Status = entity.ContactStatus(req.Status)
	}

	if req.PreferredLanguage != "" {
		contact.PreferredLanguage = req.PreferredLanguage
	}

	if req.CountryCode != "" {
		contact.CountryCode = &req.CountryCode
	}

	if req.Timezone != "" {
		contact.Timezone = &req.Timezone
	}

	if req.Notes != "" {
		contact.Notes = &req.Notes
	}

	return contact
}

// ApplyToEntity 应用更新到联系人实体
func (req *UpdateContactRequest) ApplyToEntity(contact *entity.Contact) {
	if req.Email != "" {
		contact.Email = req.Email
	}

	if req.Status != "" {
		contact.Status = entity.ContactStatus(req.Status)
	}

	if req.PreferredLanguage != "" {
		contact.PreferredLanguage = req.PreferredLanguage
	}

	if req.CountryCode != "" {
		contact.CountryCode = &req.CountryCode
	}

	if req.Timezone != "" {
		contact.Timezone = &req.Timezone
	}

	if req.Notes != "" {
		contact.Notes = &req.Notes
	}

	// 处理属性合并
	if req.Attributes != nil {
		mergePolicy := entity.MergePolicyNewOverwrite
		if req.MergePolicy != "" {
			mergePolicy = entity.MergePolicy(req.MergePolicy)
		}
		contact.MergeAttributes(req.Attributes, mergePolicy)
	}
}

// Validate 验证请求参数
func (req *CreateContactRequest) Validate() error {
	if req.Email == "" {
		return entity.NewContactValidationError("email", "邮箱地址不能为空")
	}

	if req.Status != "" && !entity.ContactStatus(req.Status).IsValid() {
		return entity.NewContactValidationError("status", "无效的联系人状态")
	}

	return nil
}

// Validate 验证请求参数
func (req *UpdateContactRequest) Validate() error {
	if req.ID <= 0 {
		return entity.NewContactValidationError("id", "联系人ID不能为空")
	}

	if req.Status != "" && !entity.ContactStatus(req.Status).IsValid() {
		return entity.NewContactValidationError("status", "无效的联系人状态")
	}

	if req.MergePolicy != "" && !entity.IsValidMergePolicy(entity.MergePolicy(req.MergePolicy)) {
		return entity.NewContactValidationError("merge_policy", "无效的合并策略")
	}

	return nil
}
