package service

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/email-system/internal/application/segment/dto"
	contactRepo "gitee.com/heiyee/platforms/email-system/internal/domain/contact/repository"
	"gitee.com/heiyee/platforms/email-system/internal/domain/segment/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/segment/repository"
	segmentService "gitee.com/heiyee/platforms/email-system/internal/domain/segment/service"
	tagRepo "gitee.com/heiyee/platforms/email-system/internal/domain/tag/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// SegmentApplicationService 人群圈选应用服务
type SegmentApplicationService struct {
	logger             logiface.Logger
	segmentRepo        repository.SegmentRepository
	segmentJobRepo     repository.SegmentJobRepository
	tagRepo            tagRepo.TagRepository
	contactRepo        contactRepo.ContactRepository
	segmentRuleService *segmentService.SegmentRuleService
}

// NewSegmentApplicationService 创建人群圈选应用服务
func NewSegmentApplicationService(
	logger logiface.Logger,
	segmentRepo repository.SegmentRepository,
	segmentJobRepo repository.SegmentJobRepository,
	tagRepo tagRepo.TagRepository,
	contactRepo contactRepo.ContactRepository,
	segmentRuleService *segmentService.SegmentRuleService,
) *SegmentApplicationService {
	return &SegmentApplicationService{
		logger:             logger,
		segmentRepo:        segmentRepo,
		segmentJobRepo:     segmentJobRepo,
		tagRepo:            tagRepo,
		contactRepo:        contactRepo,
		segmentRuleService: segmentRuleService,
	}
}

// CreateSegment 创建人群圈选
func (s *SegmentApplicationService) CreateSegment(ctx context.Context, req *dto.CreateSegmentRequest, tenantID int64) (*dto.SegmentResponse, error) {
	s.logger.Info(ctx, "Creating segment",
		logiface.String("name", req.Name),
		logiface.Int64("tag_id", req.TagID),
		logiface.String("type", req.Type),
		logiface.Int64("tenant_id", tenantID))

	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 检查标签是否存在
	tag, err := s.tagRepo.FindByID(ctx, tenantID, req.TagID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find tag",
			logiface.Error(err),
			logiface.Int64("tag_id", req.TagID))
		return nil, entity.NewSegmentQueryFailedError(fmt.Sprintf("find tag: %v", err))
	}

	if tag == nil {
		return nil, entity.NewSegmentTagInvalidError(req.TagID)
	}

	// 检查标签是否已经有人群圈选
	existingSegment, err := s.segmentRepo.FindByTagID(ctx, tenantID, req.TagID)
	if err != nil {
		s.logger.Error(ctx, "Failed to check existing segment",
			logiface.Error(err),
			logiface.Int64("tag_id", req.TagID))
		return nil, entity.NewSegmentQueryFailedError(fmt.Sprintf("check existing segment: %v", err))
	}

	if existingSegment != nil {
		return nil, entity.NewSegmentCreateFailedError("标签已经关联了人群圈选")
	}

	// 转换为实体
	segment := req.ToEntity(tenantID)

	// 验证实体
	if err := segment.Validate(); err != nil {
		return nil, err
	}

	// 创建人群圈选
	if err := s.segmentRepo.Create(ctx, segment); err != nil {
		s.logger.Error(ctx, "Failed to create segment",
			logiface.Error(err),
			logiface.String("name", req.Name))
		return nil, entity.NewSegmentCreateFailedError(fmt.Sprintf("create segment: %v", err))
	}

	s.logger.Info(ctx, "Segment created successfully",
		logiface.Int64("segment_id", segment.ID),
		logiface.String("name", segment.Name))

	return dto.ToSegmentResponse(segment), nil
}

// UpdateSegment 更新人群圈选
func (s *SegmentApplicationService) UpdateSegment(ctx context.Context, req *dto.UpdateSegmentRequest, tenantID int64) (*dto.SegmentResponse, error) {
	s.logger.Info(ctx, "Updating segment",
		logiface.Int64("segment_id", req.ID),
		logiface.Int64("tenant_id", tenantID))

	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 查找人群圈选
	segment, err := s.segmentRepo.FindByID(ctx, tenantID, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find segment",
			logiface.Error(err),
			logiface.Int64("segment_id", req.ID))
		return nil, entity.NewSegmentQueryFailedError(fmt.Sprintf("find segment: %v", err))
	}

	if segment == nil {
		return nil, entity.NewSegmentNotFoundError(fmt.Sprintf("id: %d", req.ID))
	}

	// 应用更新
	if err := req.ApplyToEntity(segment); err != nil {
		return nil, err
	}

	// 验证更新后的实体
	if err := segment.Validate(); err != nil {
		return nil, err
	}

	// 更新人群圈选
	if err := s.segmentRepo.Update(ctx, segment); err != nil {
		s.logger.Error(ctx, "Failed to update segment",
			logiface.Error(err),
			logiface.Int64("segment_id", req.ID))
		return nil, entity.NewSegmentUpdateFailedError(fmt.Sprintf("update segment: %v", err))
	}

	s.logger.Info(ctx, "Segment updated successfully",
		logiface.Int64("segment_id", segment.ID))

	return dto.ToSegmentResponse(segment), nil
}

// GetSegment 获取人群圈选
func (s *SegmentApplicationService) GetSegment(ctx context.Context, req *dto.GetSegmentRequest, tenantID int64) (*dto.SegmentResponse, error) {
	s.logger.Info(ctx, "Getting segment",
		logiface.Int64("segment_id", req.ID),
		logiface.Int64("tenant_id", tenantID))

	if req.ID <= 0 {
		return nil, entity.NewSegmentValidationError("id", "人群圈选ID不能为空")
	}

	// 查找人群圈选
	segment, err := s.segmentRepo.FindByID(ctx, tenantID, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find segment",
			logiface.Error(err),
			logiface.Int64("segment_id", req.ID))
		return nil, entity.NewSegmentQueryFailedError(fmt.Sprintf("find segment: %v", err))
	}

	if segment == nil {
		return nil, entity.NewSegmentNotFoundError(fmt.Sprintf("id: %d", req.ID))
	}

	return dto.ToSegmentResponse(segment), nil
}

// ListSegments 获取人群圈选列表
func (s *SegmentApplicationService) ListSegments(ctx context.Context, req *dto.ListSegmentsRequest, tenantID int64) (*dto.SegmentListResponse, error) {
	s.logger.Info(ctx, "Listing segments",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("page", req.Page),
		logiface.Int("size", req.Size))

	// 构建查询参数
	params := repository.NewListParams(tenantID)

	// 设置分页
	page := req.Page
	if page <= 0 {
		page = 1
	}
	size := req.Size
	if size <= 0 {
		size = 20
	}
	params.WithPagination(page, size)

	// 设置排序
	if req.Sort.Field != "" {
		order := req.Sort.Order
		if order == "" {
			order = "desc"
		}
		params.WithSort(req.Sort.Field, order)
	}

	// 设置过滤条件
	filters := &repository.ListFilters{
		Name:  req.Filters.Name,
		TagID: req.Filters.TagID,
	}

	if req.Filters.Type != "" {
		filters.Type = entity.SegmentType(req.Filters.Type)
	}

	if req.Filters.Status != "" {
		filters.Status = entity.SegmentStatus(req.Filters.Status)
	}

	params.WithFilters(filters)

	// 执行查询
	result, err := s.segmentRepo.List(ctx, params)
	if err != nil {
		s.logger.Error(ctx, "Failed to list segments",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, entity.NewSegmentQueryFailedError(fmt.Sprintf("list segments: %v", err))
	}

	s.logger.Info(ctx, "Segments listed successfully",
		logiface.Int64("total", result.Total),
		logiface.Int("count", len(result.Segments)))

	return dto.ToSegmentListResponse(result.Segments, result.Total, page, size), nil
}

// DeleteSegment 删除人群圈选
func (s *SegmentApplicationService) DeleteSegment(ctx context.Context, req *dto.DeleteSegmentRequest, tenantID int64) error {
	s.logger.Info(ctx, "Deleting segment",
		logiface.Int64("segment_id", req.ID),
		logiface.Int64("tenant_id", tenantID))

	if req.ID <= 0 {
		return entity.NewSegmentValidationError("id", "人群圈选ID不能为空")
	}

	// 检查人群圈选是否存在
	segment, err := s.segmentRepo.FindByID(ctx, tenantID, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find segment",
			logiface.Error(err),
			logiface.Int64("segment_id", req.ID))
		return entity.NewSegmentQueryFailedError(fmt.Sprintf("find segment: %v", err))
	}

	if segment == nil {
		return entity.NewSegmentNotFoundError(fmt.Sprintf("id: %d", req.ID))
	}

	// 删除人群圈选
	if err := s.segmentRepo.Delete(ctx, tenantID, req.ID); err != nil {
		s.logger.Error(ctx, "Failed to delete segment",
			logiface.Error(err),
			logiface.Int64("segment_id", req.ID))
		return entity.NewSegmentDeleteFailedError(fmt.Sprintf("delete segment: %v", err))
	}

	s.logger.Info(ctx, "Segment deleted successfully",
		logiface.Int64("segment_id", req.ID))

	return nil
}

// PreviewSegment 预览人群圈选
func (s *SegmentApplicationService) PreviewSegment(ctx context.Context, req *dto.PreviewSegmentRequest, tenantID int64) (*dto.SegmentPreviewResponse, error) {
	s.logger.Info(ctx, "Previewing segment",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("limit", req.Limit))

	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// TODO: 实现规则预览逻辑
	// 这里需要根据规则查询匹配的联系人
	// 1. 解析规则树
	// 2. 构建查询条件
	// 3. 执行查询获取联系人ID列表
	// 4. 返回预览结果

	// 模拟预览结果
	response := &dto.SegmentPreviewResponse{
		ContactIDs:    []int64{},
		Total:         0,
		EstimatedSize: 0,
		SampleSize:    0,
	}

	s.logger.Info(ctx, "Segment previewed successfully",
		logiface.Int64("estimated_size", response.EstimatedSize))

	return response, nil
}

// RebuildSegment 重建人群圈选
func (s *SegmentApplicationService) RebuildSegment(ctx context.Context, req *dto.RebuildSegmentRequest, tenantID int64) (*dto.SegmentJobResponse, error) {
	s.logger.Info(ctx, "Rebuilding segment",
		logiface.Int64("segment_id", req.ID),
		logiface.Int64("tenant_id", tenantID))

	if req.ID <= 0 {
		return nil, entity.NewSegmentValidationError("id", "人群圈选ID不能为空")
	}

	// 查找人群圈选
	segment, err := s.segmentRepo.FindByID(ctx, tenantID, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find segment",
			logiface.Error(err),
			logiface.Int64("segment_id", req.ID))
		return nil, entity.NewSegmentQueryFailedError(fmt.Sprintf("find segment: %v", err))
	}

	if segment == nil {
		return nil, entity.NewSegmentNotFoundError(fmt.Sprintf("id: %d", req.ID))
	}

	// 检查是否已有运行中的重建任务
	existingJob, err := s.segmentJobRepo.FindBySegmentID(ctx, tenantID, req.ID, entity.SegmentJobTypeRebuild)
	if err != nil {
		s.logger.Error(ctx, "Failed to check existing rebuild job",
			logiface.Error(err),
			logiface.Int64("segment_id", req.ID))
		return nil, entity.NewSegmentQueryFailedError(fmt.Sprintf("check existing rebuild job: %v", err))
	}

	if existingJob != nil && existingJob.IsRunning() {
		return nil, entity.NewSegmentRebuildFailedError("已有重建任务正在运行中")
	}

	// 创建重建任务
	job := &entity.SegmentJob{
		TenantID:  tenantID,
		SegmentID: req.ID,
		JobType:   entity.SegmentJobTypeRebuild,
		Status:    entity.SegmentJobStatusQueued,
		Progress:  0,
	}

	if err := s.segmentJobRepo.Create(ctx, job); err != nil {
		s.logger.Error(ctx, "Failed to create rebuild job",
			logiface.Error(err),
			logiface.Int64("segment_id", req.ID))
		return nil, entity.NewSegmentJobCreateFailedError(fmt.Sprintf("create rebuild job: %v", err))
	}

	s.logger.Info(ctx, "Segment rebuild job created successfully",
		logiface.Int64("job_id", job.ID),
		logiface.Int64("segment_id", req.ID))

	return dto.ToSegmentJobResponse(job), nil
}

// GetSegmentJob 获取人群圈选任务
func (s *SegmentApplicationService) GetSegmentJob(ctx context.Context, req *dto.GetSegmentJobRequest, tenantID int64) (*dto.SegmentJobResponse, error) {
	s.logger.Info(ctx, "Getting segment job",
		logiface.Int64("job_id", req.ID),
		logiface.Int64("tenant_id", tenantID))

	if req.ID <= 0 {
		return nil, entity.NewSegmentValidationError("id", "任务ID不能为空")
	}

	// 查找任务
	job, err := s.segmentJobRepo.FindByID(ctx, tenantID, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find segment job",
			logiface.Error(err),
			logiface.Int64("job_id", req.ID))
		return nil, entity.NewSegmentQueryFailedError(fmt.Sprintf("find segment job: %v", err))
	}

	if job == nil {
		return nil, entity.NewSegmentJobNotFoundError(req.ID)
	}

	return dto.ToSegmentJobResponse(job), nil
}

// CancelSegmentJob 取消人群圈选任务
func (s *SegmentApplicationService) CancelSegmentJob(ctx context.Context, req *dto.CancelSegmentJobRequest, tenantID int64) error {
	s.logger.Info(ctx, "Cancelling segment job",
		logiface.Int64("job_id", req.ID),
		logiface.Int64("tenant_id", tenantID))

	if req.ID <= 0 {
		return entity.NewSegmentValidationError("id", "任务ID不能为空")
	}

	// 查找任务
	job, err := s.segmentJobRepo.FindByID(ctx, tenantID, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find segment job",
			logiface.Error(err),
			logiface.Int64("job_id", req.ID))
		return entity.NewSegmentQueryFailedError(fmt.Sprintf("find segment job: %v", err))
	}

	if job == nil {
		return entity.NewSegmentJobNotFoundError(req.ID)
	}

	// 检查任务状态
	if job.IsCompleted() || job.IsFailed() {
		return entity.NewSegmentJobStatusError(string(job.Status), "任务已完成或失败，无法取消")
	}

	// 更新任务状态为已取消
	if err := s.segmentJobRepo.UpdateJobStatus(ctx, tenantID, req.ID, entity.SegmentJobStatusCancelled, job.Progress, nil); err != nil {
		s.logger.Error(ctx, "Failed to cancel segment job",
			logiface.Error(err),
			logiface.Int64("job_id", req.ID))
		return entity.NewSegmentJobCancelFailedError(fmt.Sprintf("cancel segment job: %v", err))
	}

	s.logger.Info(ctx, "Segment job cancelled successfully",
		logiface.Int64("job_id", req.ID))

	return nil
}

// ExportSegment 导出人群圈选
func (s *SegmentApplicationService) ExportSegment(ctx context.Context, req *dto.ExportSegmentRequest, tenantID int64) (string, error) {
	s.logger.Info(ctx, "Exporting segment",
		logiface.Int64("segment_id", req.ID),
		logiface.String("format", req.Format),
		logiface.Int64("tenant_id", tenantID))

	if req.ID <= 0 {
		return "", entity.NewSegmentValidationError("id", "人群圈选ID不能为空")
	}

	// 查找人群圈选
	segment, err := s.segmentRepo.FindByID(ctx, tenantID, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find segment",
			logiface.Error(err),
			logiface.Int64("segment_id", req.ID))
		return "", entity.NewSegmentQueryFailedError(fmt.Sprintf("find segment: %v", err))
	}

	if segment == nil {
		return "", entity.NewSegmentNotFoundError(fmt.Sprintf("id: %d", req.ID))
	}

	// TODO: 实现导出逻辑
	// 1. 根据人群圈选规则查询联系人
	// 2. 根据格式生成导出文件
	// 3. 返回文件路径或下载链接

	exportPath := fmt.Sprintf("/exports/segment_%d.%s", req.ID, req.Format)

	s.logger.Info(ctx, "Segment exported successfully",
		logiface.Int64("segment_id", req.ID),
		logiface.String("export_path", exportPath))

	return exportPath, nil
}

// PreviewSegmentRule 预览人群圈选规则
func (s *SegmentApplicationService) PreviewSegmentRule(ctx context.Context, req *dto.PreviewSegmentRuleRequest, tenantID int64) (*dto.PreviewSegmentRuleResponse, error) {
	s.logger.Info(ctx, "Previewing segment rule",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("limit", req.Limit))

	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 预览规则
	contactIDs, estimatedTotal, err := s.segmentRuleService.PreviewSegmentRule(ctx, tenantID, req.Rule, req.Limit)
	if err != nil {
		return nil, err
	}

	return &dto.PreviewSegmentRuleResponse{
		ContactIDs:     contactIDs,
		MatchedCount:   int64(len(contactIDs)),
		EstimatedTotal: estimatedTotal,
		PreviewLimit:   int64(req.Limit),
	}, nil
}

// ValidateSegmentRule 验证人群圈选规则
func (s *SegmentApplicationService) ValidateSegmentRule(ctx context.Context, req *dto.ValidateSegmentRuleRequest) (*dto.ValidateSegmentRuleResponse, error) {
	s.logger.Info(ctx, "Validating segment rule")

	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 验证规则
	err := s.segmentRuleService.ValidateSegmentRule(ctx, req.Rule)

	response := &dto.ValidateSegmentRuleResponse{
		Valid: err == nil,
	}

	if err != nil {
		response.Error = err.Error()
	}

	return response, nil
}

// RebuildSegment 重建人群圈选
// RebuildSegmentByID 旧接口（避免与上面重名）
func (s *SegmentApplicationService) RebuildSegmentByID(ctx context.Context, segmentID, tenantID int64) error {
	s.logger.Info(ctx, "Rebuilding segment",
		logiface.Int64("segment_id", segmentID),
		logiface.Int64("tenant_id", tenantID))

	// 查找人群圈选
	segment, err := s.segmentRepo.FindByID(ctx, tenantID, segmentID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find segment",
			logiface.Error(err),
			logiface.Int64("segment_id", segmentID))
		return entity.NewSegmentQueryFailedError(fmt.Sprintf("find segment: %v", err))
	}

	if segment == nil {
		return entity.NewSegmentNotFoundError(fmt.Sprintf("id: %d", segmentID))
	}

	// 重建人群圈选
	return s.segmentRuleService.RebuildSegment(ctx, segment)
}
