package dto

import (
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/segment/entity"
)

// CreateSegmentRequest 创建人群圈选请求
type CreateSegmentRequest struct {
	Name          string                 `json:"name" binding:"required,max=128"`
	TagID         int64                  `json:"tag_id" binding:"required"`
	Rule          map[string]interface{} `json:"rule" binding:"required"`
	Type          string                 `json:"type" binding:"required,oneof=dynamic static"`
	RefreshPolicy string                 `json:"refresh_policy" binding:"omitempty,oneof=schedule trigger once"`
}

// UpdateSegmentRequest 更新人群圈选请求
type UpdateSegmentRequest struct {
	ID            int64                  `json:"id" binding:"required"`
	Name          string                 `json:"name" binding:"omitempty,max=128"`
	Rule          map[string]interface{} `json:"rule" binding:"omitempty"`
	RefreshPolicy string                 `json:"refresh_policy" binding:"omitempty,oneof=schedule trigger once"`
	Status        string                 `json:"status" binding:"omitempty,oneof=active inactive archived"`
}

// GetSegmentRequest 获取人群圈选请求
type GetSegmentRequest struct {
	ID int64 `json:"id" binding:"required"`
}

// ListSegmentsRequest 人群圈选列表请求
type ListSegmentsRequest struct {
	Filters ListFilters `json:"filters" binding:"omitempty"`
	Sort    SortOptions `json:"sort" binding:"omitempty"`
	Page    int         `json:"page" binding:"omitempty,min=1"`
	Size    int         `json:"size" binding:"omitempty,min=1,max=1000"`
}

// ListFilters 列表过滤条件
type ListFilters struct {
	Name   string `json:"name" binding:"omitempty"`
	TagID  int64  `json:"tag_id" binding:"omitempty"`
	Type   string `json:"type" binding:"omitempty,oneof=dynamic static"`
	Status string `json:"status" binding:"omitempty,oneof=active inactive archived"`
}

// SortOptions 排序选项
type SortOptions struct {
	Field string `json:"field" binding:"omitempty"`
	Order string `json:"order" binding:"omitempty,oneof=asc desc"`
}

// DeleteSegmentRequest 删除人群圈选请求
type DeleteSegmentRequest struct {
	ID int64 `json:"id" binding:"required"`
}

// PreviewSegmentRequest 预览人群圈选请求
type PreviewSegmentRequest struct {
	Rule   map[string]interface{} `json:"rule" binding:"required"`
	Limit  int                    `json:"limit" binding:"omitempty,min=1,max=1000"`
	Offset int                    `json:"offset" binding:"omitempty,min=0"`
}

// RebuildSegmentRequest 重建人群圈选请求
type RebuildSegmentRequest struct {
	ID int64 `json:"id" binding:"required"`
}

// ExportSegmentRequest 导出人群圈选请求
type ExportSegmentRequest struct {
	ID     int64  `json:"id" binding:"required"`
	Format string `json:"format" binding:"omitempty,oneof=csv json xlsx"`
}

// GetSegmentJobRequest 获取人群圈选任务请求
type GetSegmentJobRequest struct {
	ID int64 `json:"id" binding:"required"`
}

// CancelSegmentJobRequest 取消人群圈选任务请求
type CancelSegmentJobRequest struct {
	ID int64 `json:"id" binding:"required"`
}

// SegmentResponse 人群圈选响应
type SegmentResponse struct {
	ID            int64                  `json:"id"`
	Name          string                 `json:"name"`
	TagID         int64                  `json:"tag_id"`
	Rule          map[string]interface{} `json:"rule"`
	Type          string                 `json:"type"`
	RefreshPolicy *string                `json:"refresh_policy"`
	Status        string                 `json:"status"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
}

// SegmentListResponse 人群圈选列表响应
type SegmentListResponse struct {
	Segments   []SegmentResponse `json:"segments"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	Size       int               `json:"size"`
	TotalPages int               `json:"total_pages"`
}

// SegmentPreviewResponse 人群圈选预览响应
type SegmentPreviewResponse struct {
	ContactIDs    []int64 `json:"contact_ids"`
	Total         int64   `json:"total"`
	EstimatedSize int64   `json:"estimated_size"`
	SampleSize    int     `json:"sample_size"`
}

// SegmentJobResponse 人群圈选任务响应
type SegmentJobResponse struct {
	ID         int64                  `json:"id"`
	SegmentID  int64                  `json:"segment_id"`
	JobType    string                 `json:"job_type"`
	Status     string                 `json:"status"`
	Progress   int                    `json:"progress"`
	Result     map[string]interface{} `json:"result,omitempty"`
	ErrorMsg   *string                `json:"error_msg"`
	StartedAt  *time.Time             `json:"started_at"`
	FinishedAt *time.Time             `json:"finished_at"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
}

// ToSegmentResponse 转换为人群圈选响应
func ToSegmentResponse(segment *entity.Segment) *SegmentResponse {
	response := &SegmentResponse{
		ID:            segment.ID,
		Name:          segment.Name,
		TagID:         segment.TagID,
		Type:          string(segment.Type),
		RefreshPolicy: segment.RefreshPolicy,
		Status:        string(segment.Status),
		CreatedAt:     segment.CreatedAt,
		UpdatedAt:     segment.UpdatedAt,
	}

	// 获取规则
	if rule, err := segment.GetRule(); err == nil && rule != nil {
		response.Rule = rule
	}

	return response
}

// ToSegmentListResponse 转换为人群圈选列表响应
func ToSegmentListResponse(segments []*entity.Segment, total int64, page, size int) *SegmentListResponse {
	segmentResponses := make([]SegmentResponse, len(segments))
	for i, segment := range segments {
		segmentResponses[i] = *ToSegmentResponse(segment)
	}

	totalPages := int(total) / size
	if int(total)%size > 0 {
		totalPages++
	}

	return &SegmentListResponse{
		Segments:   segmentResponses,
		Total:      total,
		Page:       page,
		Size:       size,
		TotalPages: totalPages,
	}
}

// ToSegmentJobResponse 转换为人群圈选任务响应
func ToSegmentJobResponse(job *entity.SegmentJob) *SegmentJobResponse {
	response := &SegmentJobResponse{
		ID:         job.ID,
		SegmentID:  job.SegmentID,
		JobType:    string(job.JobType),
		Status:     string(job.Status),
		Progress:   job.Progress,
		ErrorMsg:   job.ErrorMsg,
		StartedAt:  job.StartedAt,
		FinishedAt: job.FinishedAt,
		CreatedAt:  job.CreatedAt,
		UpdatedAt:  job.UpdatedAt,
	}

	// 获取任务结果
	if result, err := job.GetResult(); err == nil && result != nil {
		response.Result = result
	}

	return response
}

// ToEntity 转换为人群圈选实体
func (req *CreateSegmentRequest) ToEntity(tenantID int64) *entity.Segment {
	segment := &entity.Segment{
		TenantID: tenantID,
		Name:     req.Name,
		TagID:    req.TagID,
		Type:     entity.SegmentType(req.Type),
		Status:   entity.SegmentStatusActive,
	}

	if req.RefreshPolicy != "" {
		segment.RefreshPolicy = &req.RefreshPolicy
	}

	// 设置规则
	if req.Rule != nil {
		segment.SetRule(req.Rule)
	}

	return segment
}

// ApplyToEntity 应用更新到人群圈选实体
func (req *UpdateSegmentRequest) ApplyToEntity(segment *entity.Segment) error {
	if req.Name != "" {
		segment.Name = req.Name
	}

	if req.RefreshPolicy != "" {
		segment.RefreshPolicy = &req.RefreshPolicy
	}

	if req.Status != "" {
		segment.Status = entity.SegmentStatus(req.Status)
	}

	// 更新规则
	if req.Rule != nil {
		if err := segment.SetRule(req.Rule); err != nil {
			return err
		}
	}

	return nil
}

// Validate 验证请求参数
func (req *CreateSegmentRequest) Validate() error {
	if req.Name == "" {
		return entity.NewSegmentValidationError("name", "人群圈选名称不能为空")
	}

	if req.TagID <= 0 {
		return entity.NewSegmentValidationError("tag_id", "标签ID不能为空")
	}

	if !entity.SegmentType(req.Type).IsValid() {
		return entity.NewSegmentValidationError("type", "无效的人群圈选类型")
	}

	if req.Rule == nil {
		return entity.NewSegmentValidationError("rule", "圈选规则不能为空")
	}

	if req.RefreshPolicy != "" && !entity.IsValidRefreshPolicy(entity.RefreshPolicy(req.RefreshPolicy)) {
		return entity.NewSegmentValidationError("refresh_policy", "无效的刷新策略")
	}

	return nil
}

// Validate 验证请求参数
func (req *UpdateSegmentRequest) Validate() error {
	if req.ID <= 0 {
		return entity.NewSegmentValidationError("id", "人群圈选ID不能为空")
	}

	if req.Status != "" && !entity.SegmentStatus(req.Status).IsValid() {
		return entity.NewSegmentValidationError("status", "无效的人群圈选状态")
	}

	if req.RefreshPolicy != "" && !entity.IsValidRefreshPolicy(entity.RefreshPolicy(req.RefreshPolicy)) {
		return entity.NewSegmentValidationError("refresh_policy", "无效的刷新策略")
	}

	return nil
}

// Validate 验证请求参数
func (req *PreviewSegmentRequest) Validate() error {
	if req.Rule == nil {
		return entity.NewSegmentValidationError("rule", "圈选规则不能为空")
	}

	if req.Limit <= 0 {
		req.Limit = 100
	}

	if req.Offset < 0 {
		req.Offset = 0
	}

	return nil
}
