package dto

import (
	"gitee.com/heiyee/platforms/email-system/internal/domain/segment/entity"
	segmentService "gitee.com/heiyee/platforms/email-system/internal/domain/segment/service"
)

// PreviewSegmentRuleRequest 预览人群圈选规则请求
type PreviewSegmentRuleRequest struct {
	Rule  map[string]interface{} `json:"rule" binding:"required"`
	Limit int                    `json:"limit" binding:"omitempty,min=1,max=1000"`
}

// PreviewSegmentRuleResponse 预览人群圈选规则响应
type PreviewSegmentRuleResponse struct {
	ContactIDs     []int64 `json:"contact_ids"`
	MatchedCount   int64   `json:"matched_count"`
	EstimatedTotal int64   `json:"estimated_total"`
	PreviewLimit   int64   `json:"preview_limit"`
}

// ValidateSegmentRuleRequest 验证人群圈选规则请求
type ValidateSegmentRuleRequest struct {
	Rule map[string]interface{} `json:"rule" binding:"required"`
}

// ValidateSegmentRuleResponse 验证人群圈选规则响应
type ValidateSegmentRuleResponse struct {
	Valid bool   `json:"valid"`
	Error string `json:"error,omitempty"`
}

// GetSegmentRuleFieldsResponse 获取人群圈选规则字段响应
type GetSegmentRuleFieldsResponse struct {
	Fields []segmentService.SegmentRuleField `json:"fields"`
}

// 注意：重建请求与响应在 segment_dto.go 中已定义，避免重复定义

// SegmentSnapshotResponse 人群圈选快照响应
type SegmentSnapshotResponse struct {
	ID         int64   `json:"id"`
	SegmentID  int64   `json:"segment_id"`
	ContactIDs []int64 `json:"contact_ids"`
	Size       int64   `json:"size"`
	CreatedAt  string  `json:"created_at"`
	ExpiresAt  *string `json:"expires_at"`
}

// Validate 验证预览规则请求
func (req *PreviewSegmentRuleRequest) Validate() error {
	if req.Rule == nil || len(req.Rule) == 0 {
		return entity.NewSegmentValidationError("rule", "规则不能为空")
	}

	if req.Limit <= 0 {
		req.Limit = 100 // 默认限制100个
	}

	if req.Limit > 1000 {
		req.Limit = 1000 // 最大限制1000个
	}

	return nil
}

// Validate 验证规则验证请求
func (req *ValidateSegmentRuleRequest) Validate() error {
	if req.Rule == nil || len(req.Rule) == 0 {
		return entity.NewSegmentValidationError("rule", "规则不能为空")
	}

	return nil
}

// Validate 验证重建人群圈选请求
func (req *RebuildSegmentRequest) Validate() error {
	if req.ID <= 0 {
		return entity.NewSegmentValidationError("id", "人群圈选ID不能为空")
	}

	return nil
}

// ToSegmentSnapshotResponse 转换为人群圈选快照响应
func ToSegmentSnapshotResponse(snapshot *entity.SegmentSnapshot) *SegmentSnapshotResponse {
	response := &SegmentSnapshotResponse{
		ID:        snapshot.ID,
		SegmentID: snapshot.SegmentID,
		Size:      snapshot.Size,
		CreatedAt: snapshot.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	if snapshot.ExpiresAt != nil {
		expiresAt := snapshot.ExpiresAt.Format("2006-01-02 15:04:05")
		response.ExpiresAt = &expiresAt
	}

	// 获取联系人ID列表
	if contactIDs, err := snapshot.GetContactIDs(); err == nil {
		response.ContactIDs = contactIDs
	}

	return response
}
