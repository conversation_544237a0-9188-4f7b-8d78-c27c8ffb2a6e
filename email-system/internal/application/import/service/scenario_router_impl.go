package service

import (
	"context"
	"fmt"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/repository"
	"time"
)

// scenarioRouterImpl 场景路由器实现
type scenarioRouterImpl struct {
	importJobRepo   repository.ImportJobRepository
	importErrorRepo repository.ImportErrorRepository
	importBatchRepo repository.ImportBatchRepository
	fileParser      FileParser
	dataValidator   DataValidator
	errorRecorder   ErrorRecorder
	handlers        map[entity.BusinessScenario]ScenarioHandler
}

// NewScenarioRouter 创建场景路由器实例
func NewScenarioRouter(
	importJobRepo repository.ImportJobRepository,
	importErrorRepo repository.ImportErrorRepository,
	importBatchRepo repository.ImportBatchRepository,
	fileParser FileParser,
	dataValidator DataValidator,
	errorRecorder ErrorRecorder,
) ScenarioRouter {
	return &scenarioRouterImpl{
		importJobRepo:   importJobRepo,
		importErrorRepo: importErrorRepo,
		importBatchRepo: importBatchRepo,
		fileParser:      fileParser,
		dataValidator:   dataValidator,
		errorRecorder:   errorRecorder,
		handlers:        make(map[entity.BusinessScenario]ScenarioHandler),
	}
}

// RegisterHandler 注册场景处理器
func (r *scenarioRouterImpl) RegisterHandler(scenario entity.BusinessScenario, handler ScenarioHandler) {
	r.handlers[scenario] = handler
}

// ProcessJob 处理导入任务
func (r *scenarioRouterImpl) ProcessJob(ctx context.Context, job *entity.ImportJob) error {
	// 获取场景处理器
	handler, exists := r.handlers[job.BusinessScenario]
	if !exists {
		return fmt.Errorf("unsupported business scenario: %s", job.BusinessScenario)
	}

	// 解析阶段
	if err := r.parseFile(ctx, job); err != nil {
		return r.markJobFailed(ctx, job, "parsing", err)
	}

	// 验证阶段
	if err := r.validateData(ctx, job, handler); err != nil {
		return r.markJobFailed(ctx, job, "validation", err)
	}

	// 处理阶段
	if err := r.processData(ctx, job, handler); err != nil {
		return r.markJobFailed(ctx, job, "processing", err)
	}

	// 完成任务
	return r.importJobRepo.MarkCompleted(ctx, job.ID, job.TenantID)
}

// parseFile 解析文件阶段
func (r *scenarioRouterImpl) parseFile(ctx context.Context, job *entity.ImportJob) error {
	// 更新任务阶段
	if err := r.importJobRepo.UpdatePhase(ctx, job.ID, job.TenantID, entity.ImportJobPhaseParsing); err != nil {
		return fmt.Errorf("update job phase: %w", err)
	}

	// 获取映射配置
	mappingConfig, err := job.GetMappingConfig()
	if err != nil {
		return fmt.Errorf("get mapping config: %w", err)
	}

	// 解析文件
	data, err := r.fileParser.ParseWithMapping(ctx, job.FileURL, job.FileType, mappingConfig)
	if err != nil {
		return fmt.Errorf("parse file: %w", err)
	}

	// 更新统计信息
	totalRows := int64(len(data))
	if err := r.importJobRepo.UpdateStatistics(ctx, job.ID, job.TenantID, totalRows, 0, 0, 0); err != nil {
		return fmt.Errorf("update statistics: %w", err)
	}

	// 创建批次
	return r.createBatches(ctx, job, totalRows)
}

// validateData 验证数据阶段
func (r *scenarioRouterImpl) validateData(ctx context.Context, job *entity.ImportJob, handler ScenarioHandler) error {
	// 更新任务阶段
	if err := r.importJobRepo.UpdatePhase(ctx, job.ID, job.TenantID, entity.ImportJobPhaseValidating); err != nil {
		return fmt.Errorf("update job phase: %w", err)
	}

	// 获取验证规则
	validationRules, err := job.GetValidationRules()
	if err != nil {
		return fmt.Errorf("get validation rules: %w", err)
	}

	// 获取映射配置重新解析文件以获取数据
	mappingConfig, err := job.GetMappingConfig()
	if err != nil {
		return fmt.Errorf("get mapping config: %w", err)
	}

	data, err := r.fileParser.ParseWithMapping(ctx, job.FileURL, job.FileType, mappingConfig)
	if err != nil {
		return fmt.Errorf("parse file for validation: %w", err)
	}

	// 验证数据
	validationResults, validData, err := handler.ValidateData(ctx, data, validationRules)
	if err != nil {
		return fmt.Errorf("validate data: %w", err)
	}

	// 统计验证结果
	validRows := int64(len(validData))
	invalidRows := int64(len(validationResults)) - validRows

	// 计算重复行数（这里简化处理，实际可能需要更复杂的逻辑）
	duplicateRows := int64(0)

	// 更新统计信息
	if err := r.importJobRepo.UpdateStatistics(ctx, job.ID, job.TenantID, job.TotalRows, validRows, invalidRows, duplicateRows); err != nil {
		return fmt.Errorf("update validation statistics: %w", err)
	}

	// 记录验证错误
	return r.recordValidationErrors(ctx, job, validationResults)
}

// processData 处理数据阶段
func (r *scenarioRouterImpl) processData(ctx context.Context, job *entity.ImportJob, handler ScenarioHandler) error {
	// 更新任务阶段
	if err := r.importJobRepo.UpdatePhase(ctx, job.ID, job.TenantID, entity.ImportJobPhaseProcessing); err != nil {
		return fmt.Errorf("update job phase: %w", err)
	}

	// 获取场景配置
	scenarioConfig, err := job.GetScenarioConfig()
	if err != nil {
		return fmt.Errorf("get scenario config: %w", err)
	}

	// 重新获取有效数据
	mappingConfig, err := job.GetMappingConfig()
	if err != nil {
		return fmt.Errorf("get mapping config: %w", err)
	}

	data, err := r.fileParser.ParseWithMapping(ctx, job.FileURL, job.FileType, mappingConfig)
	if err != nil {
		return fmt.Errorf("parse file for processing: %w", err)
	}

	validationRules, err := job.GetValidationRules()
	if err != nil {
		return fmt.Errorf("get validation rules: %w", err)
	}

	_, validData, err := handler.ValidateData(ctx, data, validationRules)
	if err != nil {
		return fmt.Errorf("re-validate data: %w", err)
	}

	// 分批处理数据
	batchSize := 1000 // 每批处理1000条
	totalProcessed := int64(0)
	totalSuccess := int64(0)
	totalFailed := int64(0)

	for i := 0; i < len(validData); i += batchSize {
		end := i + batchSize
		if end > len(validData) {
			end = len(validData)
		}

		batchData := validData[i:end]
		batchNo := i/batchSize + 1

		// 处理批次数据
		processResults, err := handler.ProcessData(ctx, batchData, scenarioConfig)
		if err != nil {
			return fmt.Errorf("process batch %d: %w", batchNo, err)
		}

		// 存储数据
		if err := handler.StoreData(ctx, processResults, scenarioConfig); err != nil {
			return fmt.Errorf("store batch %d: %w", batchNo, err)
		}

		// 统计结果
		batchProcessed := int64(len(processResults))
		batchSuccess := int64(0)
		batchFailed := int64(0)

		var businessErrors []ImportError
		for j, result := range processResults {
			if result.Success {
				batchSuccess++
			} else {
				batchFailed++
				businessErrors = append(businessErrors, ImportError{
					RowNumber:    int64(i + j + 1),
					RawData:      result.RawData,
					ErrorType:    entity.ImportErrorTypeBusiness,
					ErrorCode:    result.ErrorCode,
					ErrorMessage: result.ErrorMessage,
				})
			}
		}

		// 记录业务错误
		if len(businessErrors) > 0 {
			if err := r.errorRecorder.RecordErrors(ctx, job.ID, job.TenantID, batchNo, businessErrors); err != nil {
				return fmt.Errorf("record business errors for batch %d: %w", batchNo, err)
			}
		}

		// 更新批次状态
		if err := r.updateBatchProgress(ctx, job.ID, job.TenantID, batchNo, int(batchProcessed), int(batchSuccess), int(batchFailed)); err != nil {
			return fmt.Errorf("update batch progress: %w", err)
		}

		// 累计统计
		totalProcessed += batchProcessed
		totalSuccess += batchSuccess
		totalFailed += batchFailed

		// 更新任务进度
		if err := r.importJobRepo.UpdateProcessCount(ctx, job.ID, job.TenantID, totalProcessed, totalSuccess, totalFailed); err != nil {
			return fmt.Errorf("update process count: %w", err)
		}

		// 添加延时，避免过于频繁的数据库操作
		time.Sleep(100 * time.Millisecond)
	}

	return nil
}

// createBatches 创建批次
func (r *scenarioRouterImpl) createBatches(ctx context.Context, job *entity.ImportJob, totalRows int64) error {
	batchSize := int64(1000) // 每批1000条
	batchCount := (totalRows + batchSize - 1) / batchSize

	var batches []*entity.ImportBatch
	for i := int64(0); i < batchCount; i++ {
		startRow := i*batchSize + 1
		endRow := startRow + batchSize - 1
		if endRow > totalRows {
			endRow = totalRows
		}

		batch := &entity.ImportBatch{
			TenantID:  job.TenantID,
			JobID:     job.ID,
			BatchNo:   int(i + 1),
			StartRow:  startRow,
			EndRow:    endRow,
			BatchSize: int(endRow - startRow + 1),
			Status:    entity.ImportBatchStatusPending,
		}

		batches = append(batches, batch)
	}

	return r.importBatchRepo.BatchCreate(ctx, batches)
}

// recordValidationErrors 记录验证错误
func (r *scenarioRouterImpl) recordValidationErrors(ctx context.Context, job *entity.ImportJob, validationResults []ValidationResult) error {
	var errors []ImportError
	batchNo := 1 // 验证阶段使用固定批次号

	for _, result := range validationResults {
		if !result.Valid {
			errors = append(errors, ImportError{
				RowNumber:    result.RowNumber,
				RawData:      result.RawData,
				ErrorType:    entity.ImportErrorTypeValidation,
				ErrorCode:    result.ErrorCode,
				ErrorMessage: result.ErrorMessage,
			})
		}
	}

	if len(errors) > 0 {
		return r.errorRecorder.RecordErrors(ctx, job.ID, job.TenantID, batchNo, errors)
	}

	return nil
}

// updateBatchProgress 更新批次进度
func (r *scenarioRouterImpl) updateBatchProgress(ctx context.Context, jobID, tenantID int64, batchNo, processedCount, successCount, failedCount int) error {
	batch, err := r.importBatchRepo.GetByJobAndBatch(ctx, jobID, tenantID, batchNo)
	if err != nil {
		return fmt.Errorf("get batch: %w", err)
	}

	if batch == nil {
		return fmt.Errorf("batch not found: job_id=%d, batch_no=%d", jobID, batchNo)
	}

	// 更新批次进度
	batch.UpdateProgress(processedCount, successCount, failedCount)

	// 如果处理完成，标记批次完成
	if processedCount >= batch.BatchSize {
		if failedCount == 0 {
			batch.Complete()
		} else if successCount == 0 {
			batch.Fail("all records failed")
		} else {
			batch.Complete() // 部分成功也算完成
		}
	}

	return r.importBatchRepo.Update(ctx, batch)
}

// markJobFailed 标记任务失败
func (r *scenarioRouterImpl) markJobFailed(ctx context.Context, job *entity.ImportJob, phase string, err error) error {
	errorMessage := fmt.Sprintf("failed in %s phase: %v", phase, err)
	return r.importJobRepo.MarkFailed(ctx, job.ID, job.TenantID, errorMessage)
}
