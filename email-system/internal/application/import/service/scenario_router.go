package service

import (
	"context"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/entity"
)

// ScenarioRouter 场景路由器接口
type ScenarioRouter interface {
	// ProcessJob 处理导入任务
	ProcessJob(ctx context.Context, job *entity.ImportJob) error

	// RegisterHandler 注册场景处理器
	RegisterHandler(scenario entity.BusinessScenario, handler ScenarioHandler)
}

// ScenarioHandler 场景处理器接口
type ScenarioHandler interface {
	// ValidateData 验证数据
	ValidateData(ctx context.Context, data []map[string]interface{}, config map[string]interface{}) ([]ValidationResult, []map[string]interface{}, error)

	// ProcessData 处理数据
	ProcessData(ctx context.Context, validData []map[string]interface{}, config map[string]interface{}) ([]ProcessResult, error)

	// StoreData 存储数据
	StoreData(ctx context.Context, results []ProcessResult, config map[string]interface{}) error
}

// ValidationResult 验证结果
type ValidationResult struct {
	RowNumber    int64                  `json:"row_number"`
	RawData      map[string]interface{} `json:"raw_data"`
	Valid        bool                   `json:"valid"`
	ErrorMessage string                 `json:"error_message,omitempty"`
	ErrorCode    string                 `json:"error_code,omitempty"`
}

// ProcessResult 处理结果
type ProcessResult struct {
	RowNumber    int64                  `json:"row_number"`
	RawData      map[string]interface{} `json:"raw_data"`
	Success      bool                   `json:"success"`
	Data         interface{}            `json:"data,omitempty"`
	ErrorMessage string                 `json:"error_message,omitempty"`
	ErrorCode    string                 `json:"error_code,omitempty"`
}

// ImportError 导入错误
type ImportError struct {
	RowNumber    int64                  `json:"row_number"`
	RawData      map[string]interface{} `json:"raw_data"`
	ErrorType    entity.ImportErrorType `json:"error_type"`
	ErrorCode    string                 `json:"error_code,omitempty"`
	ErrorMessage string                 `json:"error_message"`
}

// FileParser 文件解析器接口
type FileParser interface {
	// Parse 解析文件
	Parse(ctx context.Context, fileURL string, fileType entity.FileType) ([]map[string]interface{}, error)

	// ParseWithMapping 使用映射配置解析文件
	ParseWithMapping(ctx context.Context, fileURL string, fileType entity.FileType, mapping map[string]interface{}) ([]map[string]interface{}, error)
}

// DataValidator 数据验证器接口
type DataValidator interface {
	// Validate 验证数据
	Validate(ctx context.Context, data map[string]interface{}, rules map[string]interface{}) (bool, string, string)

	// ValidateBatch 批量验证数据
	ValidateBatch(ctx context.Context, data []map[string]interface{}, rules map[string]interface{}) []ValidationResult
}

// ErrorRecorder 错误记录器接口
type ErrorRecorder interface {
	// RecordErrors 记录错误
	RecordErrors(ctx context.Context, jobID, tenantID int64, batchNo int, errors []ImportError) error
}
