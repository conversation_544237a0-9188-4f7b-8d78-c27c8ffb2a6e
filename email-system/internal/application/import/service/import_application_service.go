package service

import (
	"context"
	"fmt"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/repository"
	"time"
)

// ImportApplicationService 导入应用服务接口
type ImportApplicationService interface {
	// CreateImportJob 创建导入任务
	CreateImportJob(ctx context.Context, req *CreateImportJobRequest) (*ImportJobResponse, error)

	// GetImportJobStatus 获取导入任务状态
	GetImportJobStatus(ctx context.Context, jobID, tenantID int64) (*ImportJobStatusResponse, error)

	// ListImportJobs 获取导入任务列表
	ListImportJobs(ctx context.Context, req *ListImportJobsRequest) (*ListImportJobsResponse, error)

	// CancelImportJob 取消导入任务
	CancelImportJob(ctx context.Context, jobID, tenantID int64) error

	// RetryImportJob 重试导入任务
	RetryImportJob(ctx context.Context, jobID, tenantID int64) error

	// DeleteImportJob 删除导入任务
	DeleteImportJob(ctx context.Context, jobID, tenantID int64) error

	// ProcessImportJob 处理导入任务
	ProcessImportJob(ctx context.Context, jobID, tenantID int64) error

	// GetImportErrors 获取导入错误列表
	GetImportErrors(ctx context.Context, req *GetImportErrorsRequest) (*GetImportErrorsResponse, error)

	// GetImportBatches 获取导入批次列表
	GetImportBatches(ctx context.Context, jobID, tenantID int64) ([]*ImportBatchResponse, error)
}

// CreateImportJobRequest 创建导入任务请求
type CreateImportJobRequest struct {
	TenantID         int64                   `json:"tenant_id"`
	BusinessScenario entity.BusinessScenario `json:"business_scenario"`
	FileName         string                  `json:"file_name"`
	FileURL          string                  `json:"file_url"`
	FileType         entity.FileType         `json:"file_type"`
	FileSize         int64                   `json:"file_size"`
	MappingConfig    map[string]interface{}  `json:"mapping_config"`
	ValidationRules  map[string]interface{}  `json:"validation_rules,omitempty"`
	ScenarioConfig   map[string]interface{}  `json:"scenario_config,omitempty"`
	TargetEntityType *string                 `json:"target_entity_type,omitempty"`
	TargetEntityID   *int64                  `json:"target_entity_id,omitempty"`
	BusinessMetadata map[string]interface{}  `json:"business_metadata,omitempty"`
	CreatedBy        int64                   `json:"created_by"`
}

// ImportJobResponse 导入任务响应
type ImportJobResponse struct {
	ID                  int64                   `json:"id"`
	TenantID            int64                   `json:"tenant_id"`
	BusinessScenario    entity.BusinessScenario `json:"business_scenario"`
	FileName            string                  `json:"file_name"`
	FileURL             string                  `json:"file_url"`
	FileType            entity.FileType         `json:"file_type"`
	FileSize            int64                   `json:"file_size"`
	Status              entity.ImportJobStatus  `json:"status"`
	CurrentPhase        *entity.ImportJobPhase  `json:"current_phase,omitempty"`
	Progress            int                     `json:"progress"`
	TotalRows           int64                   `json:"total_rows"`
	ValidRows           int64                   `json:"valid_rows"`
	InvalidRows         int64                   `json:"invalid_rows"`
	DuplicateRows       int64                   `json:"duplicate_rows"`
	ProcessedCount      int64                   `json:"processed_count"`
	SuccessCount        int64                   `json:"success_count"`
	FailedCount         int64                   `json:"failed_count"`
	EstimatedDuration   *int                    `json:"estimated_duration,omitempty"`
	EstimatedCompletion *time.Time              `json:"estimated_completion,omitempty"`
	StartedAt           *time.Time              `json:"started_at,omitempty"`
	FinishedAt          *time.Time              `json:"finished_at,omitempty"`
	ErrorMessage        *string                 `json:"error_message,omitempty"`
	CreatedBy           int64                   `json:"created_by"`
	CreatedAt           time.Time               `json:"created_at"`
	UpdatedAt           time.Time               `json:"updated_at"`
}

// ImportJobStatusResponse 导入任务状态响应
type ImportJobStatusResponse struct {
	JobID               int64                   `json:"job_id"`
	BusinessScenario    entity.BusinessScenario `json:"business_scenario"`
	Status              entity.ImportJobStatus  `json:"status"`
	Progress            int                     `json:"progress"`
	CurrentPhase        *string                 `json:"current_phase,omitempty"`
	TotalRows           int64                   `json:"total_rows"`
	ValidRows           int64                   `json:"valid_rows"`
	InvalidRows         int64                   `json:"invalid_rows"`
	DuplicateRows       int64                   `json:"duplicate_rows"`
	ProcessedCount      int64                   `json:"processed_count"`
	FailedCount         int64                   `json:"failed_count"`
	EstimatedCompletion *time.Time              `json:"estimated_completion,omitempty"`
	ErrorSummary        map[string]interface{}  `json:"error_summary,omitempty"`
}

// ListImportJobsRequest 获取导入任务列表请求
type ListImportJobsRequest struct {
	TenantID         int64                   `json:"tenant_id"`
	BusinessScenario entity.BusinessScenario `json:"business_scenario,omitempty"`
	Status           entity.ImportJobStatus  `json:"status,omitempty"`
	Limit            int                     `json:"limit"`
	Offset           int                     `json:"offset"`
}

// ListImportJobsResponse 获取导入任务列表响应
type ListImportJobsResponse struct {
	Jobs  []*ImportJobResponse `json:"jobs"`
	Total int64                `json:"total"`
}

// GetImportErrorsRequest 获取导入错误请求
type GetImportErrorsRequest struct {
	JobID    int64 `json:"job_id"`
	TenantID int64 `json:"tenant_id"`
	BatchNo  *int  `json:"batch_no,omitempty"`
	Limit    int   `json:"limit"`
	Offset   int   `json:"offset"`
}

// GetImportErrorsResponse 获取导入错误响应
type GetImportErrorsResponse struct {
	Errors []*ImportErrorResponse `json:"errors"`
	Total  int64                  `json:"total"`
}

// ImportErrorResponse 导入错误响应
type ImportErrorResponse struct {
	ID           int64                  `json:"id"`
	JobID        int64                  `json:"job_id"`
	BatchNo      int                    `json:"batch_no"`
	RowNumber    int64                  `json:"row_number"`
	RawData      map[string]interface{} `json:"raw_data"`
	ErrorType    entity.ImportErrorType `json:"error_type"`
	ErrorCode    *string                `json:"error_code,omitempty"`
	ErrorMessage string                 `json:"error_message"`
	RetryCount   int                    `json:"retry_count"`
	CreatedAt    time.Time              `json:"created_at"`
}

// ImportBatchResponse 导入批次响应
type ImportBatchResponse struct {
	ID             int64                    `json:"id"`
	JobID          int64                    `json:"job_id"`
	BatchNo        int                      `json:"batch_no"`
	StartRow       int64                    `json:"start_row"`
	EndRow         int64                    `json:"end_row"`
	BatchSize      int                      `json:"batch_size"`
	Status         entity.ImportBatchStatus `json:"status"`
	ProcessedCount int                      `json:"processed_count"`
	SuccessCount   int                      `json:"success_count"`
	FailedCount    int                      `json:"failed_count"`
	StartedAt      *time.Time               `json:"started_at,omitempty"`
	FinishedAt     *time.Time               `json:"finished_at,omitempty"`
	ErrorMessage   *string                  `json:"error_message,omitempty"`
	CreatedAt      time.Time                `json:"created_at"`
	UpdatedAt      time.Time                `json:"updated_at"`
}

// importApplicationServiceImpl 导入应用服务实现
type importApplicationServiceImpl struct {
	importJobRepo            repository.ImportJobRepository
	importScenarioConfigRepo repository.ImportScenarioConfigRepository
	importErrorRepo          repository.ImportErrorRepository
	importBatchRepo          repository.ImportBatchRepository
	scenarioRouter           ScenarioRouter
}

// NewImportApplicationService 创建导入应用服务实例
func NewImportApplicationService(
	importJobRepo repository.ImportJobRepository,
	importScenarioConfigRepo repository.ImportScenarioConfigRepository,
	importErrorRepo repository.ImportErrorRepository,
	importBatchRepo repository.ImportBatchRepository,
	scenarioRouter ScenarioRouter,
) ImportApplicationService {
	return &importApplicationServiceImpl{
		importJobRepo:            importJobRepo,
		importScenarioConfigRepo: importScenarioConfigRepo,
		importErrorRepo:          importErrorRepo,
		importBatchRepo:          importBatchRepo,
		scenarioRouter:           scenarioRouter,
	}
}

// CreateImportJob 创建导入任务
func (s *importApplicationServiceImpl) CreateImportJob(ctx context.Context, req *CreateImportJobRequest) (*ImportJobResponse, error) {
	// 验证业务场景配置
	if _, err := s.importScenarioConfigRepo.GetByCode(ctx, string(req.BusinessScenario), req.TenantID); err != nil {
		return nil, fmt.Errorf("invalid business scenario: %w", err)
	}

	// 创建导入任务实体
	job := &entity.ImportJob{
		TenantID:         req.TenantID,
		BusinessScenario: req.BusinessScenario,
		FileName:         req.FileName,
		FileURL:          req.FileURL,
		FileType:         req.FileType,
		FileSize:         req.FileSize,
		Status:           entity.ImportJobStatusQueued,
		Progress:         0,
		CreatedBy:        req.CreatedBy,
	}

	// 设置映射配置
	if err := job.SetMappingConfig(req.MappingConfig); err != nil {
		return nil, fmt.Errorf("set mapping config: %w", err)
	}

	// 设置验证规则
	if req.ValidationRules != nil {
		if err := job.SetValidationRules(req.ValidationRules); err != nil {
			return nil, fmt.Errorf("set validation rules: %w", err)
		}
	}

	// 设置场景配置
	if req.ScenarioConfig != nil {
		if err := job.SetScenarioConfig(req.ScenarioConfig); err != nil {
			return nil, fmt.Errorf("set scenario config: %w", err)
		}
	}

	// 设置目标实体信息
	if req.TargetEntityType != nil {
		job.TargetEntityType = req.TargetEntityType
	}
	if req.TargetEntityID != nil {
		job.TargetEntityID = req.TargetEntityID
	}

	// 设置业务元数据
	if req.BusinessMetadata != nil {
		if err := job.SetBusinessMetadata(req.BusinessMetadata); err != nil {
			return nil, fmt.Errorf("set business metadata: %w", err)
		}
	}

	// 验证任务数据
	if err := job.Validate(); err != nil {
		return nil, fmt.Errorf("validate import job: %w", err)
	}

	// 保存到数据库
	if err := s.importJobRepo.Create(ctx, job); err != nil {
		return nil, fmt.Errorf("create import job: %w", err)
	}

	return s.convertToImportJobResponse(job), nil
}

// GetImportJobStatus 获取导入任务状态
func (s *importApplicationServiceImpl) GetImportJobStatus(ctx context.Context, jobID, tenantID int64) (*ImportJobStatusResponse, error) {
	job, err := s.importJobRepo.GetByID(ctx, jobID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("get import job: %w", err)
	}

	response := &ImportJobStatusResponse{
		JobID:               job.ID,
		BusinessScenario:    job.BusinessScenario,
		Status:              job.Status,
		Progress:            job.Progress,
		TotalRows:           job.TotalRows,
		ValidRows:           job.ValidRows,
		InvalidRows:         job.InvalidRows,
		DuplicateRows:       job.DuplicateRows,
		ProcessedCount:      job.ProcessedCount,
		FailedCount:         job.FailedCount,
		EstimatedCompletion: job.EstimatedCompletion,
	}

	if job.CurrentPhase != nil {
		phaseStr := string(*job.CurrentPhase)
		response.CurrentPhase = &phaseStr
	}

	// 获取错误汇总
	if job.IsFailed() || job.FailedCount > 0 {
		errorSummary, err := s.importErrorRepo.GetErrorSummary(ctx, jobID, tenantID)
		if err == nil {
			summary := make(map[string]interface{})
			for k, v := range errorSummary {
				summary[k] = v
			}
			response.ErrorSummary = summary
		}
	}

	return response, nil
}

// ListImportJobs 获取导入任务列表
func (s *importApplicationServiceImpl) ListImportJobs(ctx context.Context, req *ListImportJobsRequest) (*ListImportJobsResponse, error) {
	var jobs []*entity.ImportJob
	var total int64
	var err error

	if req.BusinessScenario != "" {
		jobs, total, err = s.importJobRepo.ListByBusinessScenario(ctx, req.TenantID, req.BusinessScenario, req.Limit, req.Offset)
	} else {
		jobs, total, err = s.importJobRepo.List(ctx, req.TenantID, req.Status, req.Limit, req.Offset)
	}

	if err != nil {
		return nil, fmt.Errorf("list import jobs: %w", err)
	}

	response := &ListImportJobsResponse{
		Total: total,
		Jobs:  make([]*ImportJobResponse, len(jobs)),
	}

	for i, job := range jobs {
		response.Jobs[i] = s.convertToImportJobResponse(job)
	}

	return response, nil
}

// CancelImportJob 取消导入任务
func (s *importApplicationServiceImpl) CancelImportJob(ctx context.Context, jobID, tenantID int64) error {
	job, err := s.importJobRepo.GetByID(ctx, jobID, tenantID)
	if err != nil {
		return fmt.Errorf("get import job: %w", err)
	}

	// 只有排队中或运行中的任务可以取消
	if !job.IsQueued() && !job.IsRunning() {
		return fmt.Errorf("job cannot be cancelled, current status: %s", job.Status)
	}

	// 取消任务
	job.Cancel()

	return s.importJobRepo.Update(ctx, job)
}

// RetryImportJob 重试导入任务
func (s *importApplicationServiceImpl) RetryImportJob(ctx context.Context, jobID, tenantID int64) error {
	job, err := s.importJobRepo.GetByID(ctx, jobID, tenantID)
	if err != nil {
		return fmt.Errorf("get import job: %w", err)
	}

	// 只有失败的任务可以重试
	if !job.IsFailed() {
		return fmt.Errorf("job cannot be retried, current status: %s", job.Status)
	}

	// 重置任务状态
	job.Status = entity.ImportJobStatusQueued
	job.Progress = 0
	job.CurrentPhase = nil
	job.StartedAt = nil
	job.FinishedAt = nil
	job.ErrorMessage = nil
	job.UpdatedAt = time.Now()

	return s.importJobRepo.Update(ctx, job)
}

// DeleteImportJob 删除导入任务
func (s *importApplicationServiceImpl) DeleteImportJob(ctx context.Context, jobID, tenantID int64) error {
	job, err := s.importJobRepo.GetByID(ctx, jobID, tenantID)
	if err != nil {
		return fmt.Errorf("get import job: %w", err)
	}

	// 运行中的任务不能删除
	if job.IsRunning() {
		return fmt.Errorf("cannot delete running job")
	}

	// 删除相关数据
	if err := s.importErrorRepo.DeleteByJobID(ctx, jobID, tenantID); err != nil {
		return fmt.Errorf("delete import errors: %w", err)
	}

	if err := s.importBatchRepo.DeleteByJobID(ctx, jobID, tenantID); err != nil {
		return fmt.Errorf("delete import batches: %w", err)
	}

	if err := s.importJobRepo.Delete(ctx, jobID, tenantID); err != nil {
		return fmt.Errorf("delete import job: %w", err)
	}

	return nil
}

// ProcessImportJob 处理导入任务
func (s *importApplicationServiceImpl) ProcessImportJob(ctx context.Context, jobID, tenantID int64) error {
	job, err := s.importJobRepo.GetByID(ctx, jobID, tenantID)
	if err != nil {
		return fmt.Errorf("get import job: %w", err)
	}

	// 只能处理排队中的任务
	if !job.IsQueued() {
		return fmt.Errorf("job cannot be processed, current status: %s", job.Status)
	}

	// 开始处理
	job.Start()
	if err := s.importJobRepo.Update(ctx, job); err != nil {
		return fmt.Errorf("update job status: %w", err)
	}

	// 使用场景路由器处理任务
	return s.scenarioRouter.ProcessJob(ctx, job)
}

// GetImportErrors 获取导入错误列表
func (s *importApplicationServiceImpl) GetImportErrors(ctx context.Context, req *GetImportErrorsRequest) (*GetImportErrorsResponse, error) {
	var errors []*entity.ImportError
	var total int64
	var err error

	if req.BatchNo != nil {
		errors, total, err = s.importErrorRepo.GetByJobAndBatch(ctx, req.JobID, req.TenantID, *req.BatchNo, req.Limit, req.Offset)
	} else {
		errors, total, err = s.importErrorRepo.GetByJobID(ctx, req.JobID, req.TenantID, req.Limit, req.Offset)
	}

	if err != nil {
		return nil, fmt.Errorf("get import errors: %w", err)
	}

	response := &GetImportErrorsResponse{
		Total:  total,
		Errors: make([]*ImportErrorResponse, len(errors)),
	}

	for i, importError := range errors {
		rawData, _ := importError.GetRawData()
		response.Errors[i] = &ImportErrorResponse{
			ID:           importError.ID,
			JobID:        importError.JobID,
			BatchNo:      importError.BatchNo,
			RowNumber:    importError.RowNumber,
			RawData:      rawData,
			ErrorType:    importError.ErrorType,
			ErrorCode:    importError.ErrorCode,
			ErrorMessage: importError.ErrorMessage,
			RetryCount:   importError.RetryCount,
			CreatedAt:    importError.CreatedAt,
		}
	}

	return response, nil
}

// GetImportBatches 获取导入批次列表
func (s *importApplicationServiceImpl) GetImportBatches(ctx context.Context, jobID, tenantID int64) ([]*ImportBatchResponse, error) {
	batches, err := s.importBatchRepo.ListByJobID(ctx, jobID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("get import batches: %w", err)
	}

	response := make([]*ImportBatchResponse, len(batches))
	for i, batch := range batches {
		response[i] = &ImportBatchResponse{
			ID:             batch.ID,
			JobID:          batch.JobID,
			BatchNo:        batch.BatchNo,
			StartRow:       batch.StartRow,
			EndRow:         batch.EndRow,
			BatchSize:      batch.BatchSize,
			Status:         batch.Status,
			ProcessedCount: batch.ProcessedCount,
			SuccessCount:   batch.SuccessCount,
			FailedCount:    batch.FailedCount,
			StartedAt:      batch.StartedAt,
			FinishedAt:     batch.FinishedAt,
			ErrorMessage:   batch.ErrorMessage,
			CreatedAt:      batch.CreatedAt,
			UpdatedAt:      batch.UpdatedAt,
		}
	}

	return response, nil
}

// convertToImportJobResponse 转换为导入任务响应
func (s *importApplicationServiceImpl) convertToImportJobResponse(job *entity.ImportJob) *ImportJobResponse {
	return &ImportJobResponse{
		ID:                  job.ID,
		TenantID:            job.TenantID,
		BusinessScenario:    job.BusinessScenario,
		FileName:            job.FileName,
		FileURL:             job.FileURL,
		FileType:            job.FileType,
		FileSize:            job.FileSize,
		Status:              job.Status,
		CurrentPhase:        job.CurrentPhase,
		Progress:            job.Progress,
		TotalRows:           job.TotalRows,
		ValidRows:           job.ValidRows,
		InvalidRows:         job.InvalidRows,
		DuplicateRows:       job.DuplicateRows,
		ProcessedCount:      job.ProcessedCount,
		SuccessCount:        job.SuccessCount,
		FailedCount:         job.FailedCount,
		EstimatedDuration:   job.EstimatedDuration,
		EstimatedCompletion: job.EstimatedCompletion,
		StartedAt:           job.StartedAt,
		FinishedAt:          job.FinishedAt,
		ErrorMessage:        job.ErrorMessage,
		CreatedBy:           job.CreatedBy,
		CreatedAt:           job.CreatedAt,
		UpdatedAt:           job.UpdatedAt,
	}
}
