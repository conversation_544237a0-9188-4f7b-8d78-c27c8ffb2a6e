package service

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/email-system/internal/application/tag/dto"
	contactRepo "gitee.com/heiyee/platforms/email-system/internal/domain/contact/repository"
	"gitee.com/heiyee/platforms/email-system/internal/domain/tag/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/tag/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// TagApplicationService 标签应用服务
type TagApplicationService struct {
	logger         logiface.Logger
	tagRepo        repository.TagRepository
	contactTagRepo repository.ContactTagRepository
	contactRepo    contactRepo.ContactRepository
}

// NewTagApplicationService 创建标签应用服务
func NewTagApplicationService(
	logger logiface.Logger,
	tagRepo repository.TagRepository,
	contactTagRepo repository.ContactTagRepository,
	contactRepo contactRepo.ContactRepository,
) *TagApplicationService {
	return &TagApplicationService{
		logger:         logger,
		tagRepo:        tagRepo,
		contactTagRepo: contactTagRepo,
		contactRepo:    contactRepo,
	}
}

// CreateTag 创建标签
func (s *TagApplicationService) CreateTag(ctx context.Context, req *dto.CreateTagRequest, tenantID, createdBy int64) (*dto.TagResponse, error) {
	s.logger.Info(ctx, "Creating tag",
		logiface.String("name", req.Name),
		logiface.String("type", req.Type),
		logiface.Int64("tenant_id", tenantID))

	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 检查标签名称是否已存在
	exists, err := s.tagRepo.ExistsByName(ctx, tenantID, req.Name)
	if err != nil {
		s.logger.Error(ctx, "Failed to check tag name existence",
			logiface.Error(err),
			logiface.String("name", req.Name))
		return nil, entity.NewTagQueryFailedError(fmt.Sprintf("check tag name existence: %v", err))
	}

	if exists {
		return nil, entity.NewTagNameExistsError(req.Name)
	}

	// 转换为实体
	tag := req.ToEntity(tenantID, createdBy)

	// 验证实体
	if err := tag.Validate(); err != nil {
		return nil, err
	}

	// 创建标签
	if err := s.tagRepo.Create(ctx, tag); err != nil {
		s.logger.Error(ctx, "Failed to create tag",
			logiface.Error(err),
			logiface.String("name", req.Name))
		return nil, entity.NewTagCreateFailedError(fmt.Sprintf("create tag: %v", err))
	}

	s.logger.Info(ctx, "Tag created successfully",
		logiface.Int64("tag_id", tag.ID),
		logiface.String("name", tag.Name))

	return dto.ToTagResponse(tag), nil
}

// UpdateTag 更新标签
func (s *TagApplicationService) UpdateTag(ctx context.Context, req *dto.UpdateTagRequest, tenantID int64) (*dto.TagResponse, error) {
	s.logger.Info(ctx, "Updating tag",
		logiface.Int64("tag_id", req.ID),
		logiface.Int64("tenant_id", tenantID))

	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 查找标签
	tag, err := s.tagRepo.FindByID(ctx, tenantID, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find tag",
			logiface.Error(err),
			logiface.Int64("tag_id", req.ID))
		return nil, entity.NewTagQueryFailedError(fmt.Sprintf("find tag: %v", err))
	}

	if tag == nil {
		return nil, entity.NewTagNotFoundError(fmt.Sprintf("id: %d", req.ID))
	}

	// 如果更新名称，检查新名称是否已存在
	if req.Name != "" && req.Name != tag.Name {
		exists, err := s.tagRepo.ExistsByName(ctx, tenantID, req.Name)
		if err != nil {
			s.logger.Error(ctx, "Failed to check tag name existence",
				logiface.Error(err),
				logiface.String("name", req.Name))
			return nil, entity.NewTagQueryFailedError(fmt.Sprintf("check tag name existence: %v", err))
		}

		if exists {
			return nil, entity.NewTagNameExistsError(req.Name)
		}
	}

	// 应用更新
	if err := req.ApplyToEntity(tag); err != nil {
		return nil, err
	}

	// 验证更新后的实体
	if err := tag.Validate(); err != nil {
		return nil, err
	}

	// 更新标签
	if err := s.tagRepo.Update(ctx, tag); err != nil {
		s.logger.Error(ctx, "Failed to update tag",
			logiface.Error(err),
			logiface.Int64("tag_id", req.ID))
		return nil, entity.NewTagUpdateFailedError(fmt.Sprintf("update tag: %v", err))
	}

	s.logger.Info(ctx, "Tag updated successfully",
		logiface.Int64("tag_id", tag.ID))

	return dto.ToTagResponse(tag), nil
}

// GetTag 获取标签
func (s *TagApplicationService) GetTag(ctx context.Context, req *dto.GetTagRequest, tenantID int64) (*dto.TagResponse, error) {
	s.logger.Info(ctx, "Getting tag",
		logiface.Int64("tag_id", req.ID),
		logiface.String("name", req.Name),
		logiface.Int64("tenant_id", tenantID))

	var tag *entity.Tag
	var err error

	if req.ID > 0 {
		tag, err = s.tagRepo.FindByID(ctx, tenantID, req.ID)
	} else if req.Name != "" {
		tag, err = s.tagRepo.FindByName(ctx, tenantID, req.Name)
	} else {
		return nil, entity.NewTagValidationError("id_or_name", "必须提供标签ID或名称")
	}

	if err != nil {
		s.logger.Error(ctx, "Failed to find tag",
			logiface.Error(err),
			logiface.Int64("tag_id", req.ID),
			logiface.String("name", req.Name))
		return nil, entity.NewTagQueryFailedError(fmt.Sprintf("find tag: %v", err))
	}

	if tag == nil {
		identifier := fmt.Sprintf("id: %d", req.ID)
		if req.Name != "" {
			identifier = fmt.Sprintf("name: %s", req.Name)
		}
		return nil, entity.NewTagNotFoundError(identifier)
	}

	return dto.ToTagResponse(tag), nil
}

// ListTags 获取标签列表
func (s *TagApplicationService) ListTags(ctx context.Context, req *dto.ListTagsRequest, tenantID int64) (*dto.TagListResponse, error) {
	s.logger.Info(ctx, "Listing tags",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("page", req.Page),
		logiface.Int("size", req.Size))

	// 构建查询参数
	params := repository.NewListParams(tenantID)

	// 设置分页
	page := req.Page
	if page <= 0 {
		page = 1
	}
	size := req.Size
	if size <= 0 {
		size = 20
	}
	params.WithPagination(page, size)

	// 设置排序
	if req.Sort.Field != "" {
		order := req.Sort.Order
		if order == "" {
			order = "desc"
		}
		params.WithSort(req.Sort.Field, order)
	}

	// 设置过滤条件
	filters := &repository.ListFilters{
		Name:      req.Filters.Name,
		Color:     req.Filters.Color,
		CreatedBy: req.Filters.CreatedBy,
		Keyword:   req.Filters.Keyword,
		UsageMin:  req.Filters.UsageMin,
		UsageMax:  req.Filters.UsageMax,
		MemberMin: req.Filters.MemberMin,
		MemberMax: req.Filters.MemberMax,
	}

	if req.Filters.Type != "" {
		filters.Type = entity.TagType(req.Filters.Type)
	}

	params.WithFilters(filters)

	// 执行查询
	result, err := s.tagRepo.List(ctx, params)
	if err != nil {
		s.logger.Error(ctx, "Failed to list tags",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, entity.NewTagQueryFailedError(fmt.Sprintf("list tags: %v", err))
	}

	s.logger.Info(ctx, "Tags listed successfully",
		logiface.Int64("total", result.Total),
		logiface.Int("count", len(result.Tags)))

	return dto.ToTagListResponse(result.Tags, result.Total, page, size), nil
}

// DeleteTag 删除标签
func (s *TagApplicationService) DeleteTag(ctx context.Context, req *dto.DeleteTagRequest, tenantID int64) error {
	s.logger.Info(ctx, "Deleting tag",
		logiface.Int64("tag_id", req.ID),
		logiface.Int64("tenant_id", tenantID))

	if req.ID <= 0 {
		return entity.NewTagValidationError("id", "标签ID不能为空")
	}

	// 检查标签是否存在
	tag, err := s.tagRepo.FindByID(ctx, tenantID, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find tag",
			logiface.Error(err),
			logiface.Int64("tag_id", req.ID))
		return entity.NewTagQueryFailedError(fmt.Sprintf("find tag: %v", err))
	}

	if tag == nil {
		return entity.NewTagNotFoundError(fmt.Sprintf("id: %d", req.ID))
	}

	// 删除标签
	if err := s.tagRepo.Delete(ctx, tenantID, req.ID); err != nil {
		s.logger.Error(ctx, "Failed to delete tag",
			logiface.Error(err),
			logiface.Int64("tag_id", req.ID))
		return entity.NewTagDeleteFailedError(fmt.Sprintf("delete tag: %v", err))
	}

	s.logger.Info(ctx, "Tag deleted successfully",
		logiface.Int64("tag_id", req.ID))

	return nil
}

// AssignTags 分配标签
func (s *TagApplicationService) AssignTags(ctx context.Context, req *dto.AssignTagsRequest, tenantID int64) (*dto.AssignTagsResponse, error) {
	s.logger.Info(ctx, "Assigning tags",
		logiface.Int("contact_count", len(req.ContactIDs)+len(req.Emails)),
		logiface.Int("add_tags", len(req.Add)),
		logiface.Int("remove_tags", len(req.Remove)),
		logiface.Int64("tenant_id", tenantID))

	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, err
	}

	response := &dto.AssignTagsResponse{
		Errors: []string{},
	}

	// 获取联系人ID列表
	contactIDs := req.ContactIDs
	if len(req.Emails) > 0 {
		// 根据邮箱查找联系人ID
		for _, email := range req.Emails {
			contact, err := s.contactRepo.FindByEmail(ctx, tenantID, email)
			if err != nil {
				response.Errors = append(response.Errors, fmt.Sprintf("查找联系人失败: %s", email))
				continue
			}
			if contact == nil {
				response.Errors = append(response.Errors, fmt.Sprintf("联系人不存在: %s", email))
				continue
			}
			contactIDs = append(contactIDs, contact.ID)
		}
	}

	if len(contactIDs) == 0 {
		return response, nil
	}

	// 处理添加标签
	if len(req.Add) > 0 {
		if err := s.addTagsToContacts(ctx, tenantID, contactIDs, req.Add, response); err != nil {
			return nil, err
		}
	}

	// 处理移除标签
	if len(req.Remove) > 0 {
		if err := s.removeTagsFromContacts(ctx, tenantID, contactIDs, req.Remove, response); err != nil {
			return nil, err
		}
	}

	s.logger.Info(ctx, "Tags assigned successfully",
		logiface.Int("success_count", response.SuccessCount),
		logiface.Int("failed_count", response.FailedCount))

	return response, nil
}

// RefreshTag 刷新标签
func (s *TagApplicationService) RefreshTag(ctx context.Context, req *dto.RefreshTagRequest, tenantID int64) error {
	s.logger.Info(ctx, "Refreshing tag",
		logiface.Int64("tag_id", req.ID),
		logiface.Int64("tenant_id", tenantID))

	if req.ID <= 0 {
		return entity.NewTagValidationError("id", "标签ID不能为空")
	}

	// 查找标签
	tag, err := s.tagRepo.FindByID(ctx, tenantID, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find tag",
			logiface.Error(err),
			logiface.Int64("tag_id", req.ID))
		return entity.NewTagQueryFailedError(fmt.Sprintf("find tag: %v", err))
	}

	if tag == nil {
		return entity.NewTagNotFoundError(fmt.Sprintf("id: %d", req.ID))
	}

	// 检查是否可以刷新
	if !tag.CanRefresh() {
		return entity.NewTagRefreshFailedError("标签不支持刷新或刷新策略为一次性")
	}

	// TODO: 实现标签刷新逻辑
	// 这里需要根据规则树重新计算标签成员

	s.logger.Info(ctx, "Tag refreshed successfully",
		logiface.Int64("tag_id", req.ID))

	return nil
}

// MergeTags 合并标签
func (s *TagApplicationService) MergeTags(ctx context.Context, req *dto.MergeTagsRequest, tenantID int64) error {
	s.logger.Info(ctx, "Merging tags",
		logiface.Any("source_tag_ids", req.SourceTagIDs),
		logiface.Int64("target_tag_id", req.TargetTagID),
		logiface.Int64("tenant_id", tenantID))

	if len(req.SourceTagIDs) == 0 {
		return entity.NewTagValidationError("source_tag_ids", "源标签ID列表不能为空")
	}

	if req.TargetTagID <= 0 {
		return entity.NewTagValidationError("target_tag_id", "目标标签ID不能为空")
	}

	// 检查目标标签是否存在
	targetTag, err := s.tagRepo.FindByID(ctx, tenantID, req.TargetTagID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find target tag",
			logiface.Error(err),
			logiface.Int64("target_tag_id", req.TargetTagID))
		return entity.NewTagQueryFailedError(fmt.Sprintf("find target tag: %v", err))
	}

	if targetTag == nil {
		return entity.NewTagNotFoundError(fmt.Sprintf("target tag id: %d", req.TargetTagID))
	}

	// 检查源标签是否存在
	sourceTags, err := s.tagRepo.FindByIDs(ctx, tenantID, req.SourceTagIDs)
	if err != nil {
		s.logger.Error(ctx, "Failed to find source tags",
			logiface.Error(err),
			logiface.Any("source_tag_ids", req.SourceTagIDs))
		return entity.NewTagQueryFailedError(fmt.Sprintf("find source tags: %v", err))
	}

	if len(sourceTags) != len(req.SourceTagIDs) {
		return entity.NewTagNotFoundError("部分源标签不存在")
	}

	// TODO: 实现标签合并逻辑
	// 1. 将源标签的所有联系人关联转移到目标标签
	// 2. 更新目标标签的成员数量
	// 3. 删除源标签

	s.logger.Info(ctx, "Tags merged successfully",
		logiface.Int64("target_tag_id", req.TargetTagID),
		logiface.Int("source_count", len(req.SourceTagIDs)))

	return nil
}

// RenameTag 重命名标签
func (s *TagApplicationService) RenameTag(ctx context.Context, req *dto.RenameTagRequest, tenantID int64) (*dto.TagResponse, error) {
	s.logger.Info(ctx, "Renaming tag",
		logiface.Int64("tag_id", req.ID),
		logiface.String("new_name", req.NewName),
		logiface.Int64("tenant_id", tenantID))

	if req.ID <= 0 {
		return nil, entity.NewTagValidationError("id", "标签ID不能为空")
	}

	if req.NewName == "" {
		return nil, entity.NewTagValidationError("new_name", "新标签名称不能为空")
	}

	// 查找标签
	tag, err := s.tagRepo.FindByID(ctx, tenantID, req.ID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find tag",
			logiface.Error(err),
			logiface.Int64("tag_id", req.ID))
		return nil, entity.NewTagQueryFailedError(fmt.Sprintf("find tag: %v", err))
	}

	if tag == nil {
		return nil, entity.NewTagNotFoundError(fmt.Sprintf("id: %d", req.ID))
	}

	// 检查新名称是否已存在
	if req.NewName != tag.Name {
		exists, err := s.tagRepo.ExistsByName(ctx, tenantID, req.NewName)
		if err != nil {
			s.logger.Error(ctx, "Failed to check tag name existence",
				logiface.Error(err),
				logiface.String("name", req.NewName))
			return nil, entity.NewTagQueryFailedError(fmt.Sprintf("check tag name existence: %v", err))
		}

		if exists {
			return nil, entity.NewTagNameExistsError(req.NewName)
		}
	}

	// 更新标签名称
	tag.Name = req.NewName

	// 验证更新后的实体
	if err := tag.Validate(); err != nil {
		return nil, err
	}

	// 更新标签
	if err := s.tagRepo.Update(ctx, tag); err != nil {
		s.logger.Error(ctx, "Failed to rename tag",
			logiface.Error(err),
			logiface.Int64("tag_id", req.ID))
		return nil, entity.NewTagRenameFailedError(fmt.Sprintf("rename tag: %v", err))
	}

	s.logger.Info(ctx, "Tag renamed successfully",
		logiface.Int64("tag_id", tag.ID),
		logiface.String("new_name", req.NewName))

	return dto.ToTagResponse(tag), nil
}

// addTagsToContacts 为联系人添加标签
func (s *TagApplicationService) addTagsToContacts(ctx context.Context, tenantID int64, contactIDs []int64, tagNames []string, response *dto.AssignTagsResponse) error {
	// 根据标签名称查找标签
	tags := make([]*entity.Tag, 0, len(tagNames))
	for _, tagName := range tagNames {
		tag, err := s.tagRepo.FindByName(ctx, tenantID, tagName)
		if err != nil {
			response.Errors = append(response.Errors, fmt.Sprintf("查找标签失败: %s", tagName))
			response.FailedCount++
			continue
		}
		if tag == nil {
			response.Errors = append(response.Errors, fmt.Sprintf("标签不存在: %s", tagName))
			response.FailedCount++
			continue
		}
		tags = append(tags, tag)
	}

	// 为每个联系人分配标签
	for _, contactID := range contactIDs {
		for _, tag := range tags {
			// 检查是否已经分配
			exists, err := s.contactTagRepo.ExistsAssignment(ctx, tenantID, contactID, tag.ID)
			if err != nil {
				response.Errors = append(response.Errors, fmt.Sprintf("检查标签分配失败: contact_id=%d, tag=%s", contactID, tag.Name))
				response.FailedCount++
				continue
			}

			if exists {
				// 已经分配，跳过
				continue
			}

			// 创建联系人标签关联
			contactTag := &entity.ContactTag{
				TenantID:  tenantID,
				ContactID: contactID,
				TagID:     tag.ID,
			}

			if err := s.contactTagRepo.AssignTag(ctx, contactTag); err != nil {
				response.Errors = append(response.Errors, fmt.Sprintf("分配标签失败: contact_id=%d, tag=%s", contactID, tag.Name))
				response.FailedCount++
				continue
			}

			response.SuccessCount++
		}
	}

	// 更新标签成员数量
	for _, tag := range tags {
		count, err := s.contactTagRepo.CountTagContacts(ctx, tenantID, tag.ID)
		if err != nil {
			s.logger.Error(ctx, "Failed to count tag contacts",
				logiface.Error(err),
				logiface.Int64("tag_id", tag.ID))
			continue
		}

		if err := s.tagRepo.UpdateMemberCount(ctx, tenantID, tag.ID, count); err != nil {
			s.logger.Error(ctx, "Failed to update tag member count",
				logiface.Error(err),
				logiface.Int64("tag_id", tag.ID))
		}
	}

	return nil
}

// removeTagsFromContacts 从联系人移除标签
func (s *TagApplicationService) removeTagsFromContacts(ctx context.Context, tenantID int64, contactIDs []int64, tagNames []string, response *dto.AssignTagsResponse) error {
	// 根据标签名称查找标签
	tags := make([]*entity.Tag, 0, len(tagNames))
	for _, tagName := range tagNames {
		tag, err := s.tagRepo.FindByName(ctx, tenantID, tagName)
		if err != nil {
			response.Errors = append(response.Errors, fmt.Sprintf("查找标签失败: %s", tagName))
			response.FailedCount++
			continue
		}
		if tag == nil {
			response.Errors = append(response.Errors, fmt.Sprintf("标签不存在: %s", tagName))
			response.FailedCount++
			continue
		}
		tags = append(tags, tag)
	}

	// 从每个联系人移除标签
	for _, contactID := range contactIDs {
		for _, tag := range tags {
			// 检查是否已经分配
			exists, err := s.contactTagRepo.ExistsAssignment(ctx, tenantID, contactID, tag.ID)
			if err != nil {
				response.Errors = append(response.Errors, fmt.Sprintf("检查标签分配失败: contact_id=%d, tag=%s", contactID, tag.Name))
				response.FailedCount++
				continue
			}

			if !exists {
				// 没有分配，跳过
				continue
			}

			// 移除联系人标签关联
			if err := s.contactTagRepo.UnassignTag(ctx, tenantID, contactID, tag.ID); err != nil {
				response.Errors = append(response.Errors, fmt.Sprintf("移除标签失败: contact_id=%d, tag=%s", contactID, tag.Name))
				response.FailedCount++
				continue
			}

			response.SuccessCount++
		}
	}

	// 更新标签成员数量
	for _, tag := range tags {
		count, err := s.contactTagRepo.CountTagContacts(ctx, tenantID, tag.ID)
		if err != nil {
			s.logger.Error(ctx, "Failed to count tag contacts",
				logiface.Error(err),
				logiface.Int64("tag_id", tag.ID))
			continue
		}

		if err := s.tagRepo.UpdateMemberCount(ctx, tenantID, tag.ID, count); err != nil {
			s.logger.Error(ctx, "Failed to update tag member count",
				logiface.Error(err),
				logiface.Int64("tag_id", tag.ID))
		}
	}

	return nil
}

// GetPopularTags 获取热门标签
func (s *TagApplicationService) GetPopularTags(ctx context.Context, tenantID int64, limit int) ([]*dto.TagSummaryResponse, error) {
	s.logger.Info(ctx, "Getting popular tags",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("limit", limit))

	if limit <= 0 {
		limit = 10
	}

	tags, err := s.tagRepo.GetPopularTags(ctx, tenantID, limit)
	if err != nil {
		s.logger.Error(ctx, "Failed to get popular tags",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, entity.NewTagQueryFailedError(fmt.Sprintf("get popular tags: %v", err))
	}

	responses := make([]*dto.TagSummaryResponse, len(tags))
	for i, tag := range tags {
		responses[i] = dto.ToTagSummaryResponse(tag)
	}

	return responses, nil
}

// GetRecentTags 获取最近使用的标签
func (s *TagApplicationService) GetRecentTags(ctx context.Context, tenantID int64, limit int) ([]*dto.TagSummaryResponse, error) {
	s.logger.Info(ctx, "Getting recent tags",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("limit", limit))

	if limit <= 0 {
		limit = 10
	}

	tags, err := s.tagRepo.GetRecentTags(ctx, tenantID, limit)
	if err != nil {
		s.logger.Error(ctx, "Failed to get recent tags",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, entity.NewTagQueryFailedError(fmt.Sprintf("get recent tags: %v", err))
	}

	responses := make([]*dto.TagSummaryResponse, len(tags))
	for i, tag := range tags {
		responses[i] = dto.ToTagSummaryResponse(tag)
	}

	return responses, nil
}
