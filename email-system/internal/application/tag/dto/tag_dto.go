package dto

import (
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/tag/entity"
)

// CreateTagRequest 创建标签请求
type CreateTagRequest struct {
	Name          string                 `json:"name" binding:"required,max=64"`
	Description   string                 `json:"description" binding:"omitempty,max=255"`
	Type          string                 `json:"type" binding:"required,oneof=rule_based static_list"`
	Color         string                 `json:"color" binding:"omitempty,max=16"`
	RuleTree      map[string]interface{} `json:"rule_tree" binding:"omitempty"`
	RefreshPolicy string                 `json:"refresh_policy" binding:"omitempty,oneof=schedule trigger once"`
}

// UpdateTagRequest 更新标签请求
type UpdateTagRequest struct {
	ID            int64                  `json:"id" binding:"required"`
	Name          string                 `json:"name" binding:"omitempty,max=64"`
	Description   string                 `json:"description" binding:"omitempty,max=255"`
	Color         string                 `json:"color" binding:"omitempty,max=16"`
	RuleTree      map[string]interface{} `json:"rule_tree" binding:"omitempty"`
	RefreshPolicy string                 `json:"refresh_policy" binding:"omitempty,oneof=schedule trigger once"`
}

// GetTagRequest 获取标签请求
type GetTagRequest struct {
	ID   int64  `json:"id" binding:"omitempty"`
	Name string `json:"name" binding:"omitempty"`
}

// ListTagsRequest 标签列表请求
type ListTagsRequest struct {
	Filters ListFilters `json:"filters" binding:"omitempty"`
	Sort    SortOptions `json:"sort" binding:"omitempty"`
	Page    int         `json:"page" binding:"omitempty,min=1"`
	Size    int         `json:"size" binding:"omitempty,min=1,max=1000"`
}

// ListFilters 列表过滤条件
type ListFilters struct {
	Name      string `json:"name" binding:"omitempty"`
	Type      string `json:"type" binding:"omitempty,oneof=rule_based static_list"`
	Color     string `json:"color" binding:"omitempty"`
	CreatedBy int64  `json:"created_by" binding:"omitempty"`
	Keyword   string `json:"keyword" binding:"omitempty"`
	UsageMin  int64  `json:"usage_min" binding:"omitempty"`
	UsageMax  int64  `json:"usage_max" binding:"omitempty"`
	MemberMin int64  `json:"member_min" binding:"omitempty"`
	MemberMax int64  `json:"member_max" binding:"omitempty"`
}

// SortOptions 排序选项
type SortOptions struct {
	Field string `json:"field" binding:"omitempty"`
	Order string `json:"order" binding:"omitempty,oneof=asc desc"`
}

// DeleteTagRequest 删除标签请求
type DeleteTagRequest struct {
	ID int64 `json:"id" binding:"required"`
}

// AssignTagsRequest 分配标签请求
type AssignTagsRequest struct {
	ContactIDs []int64  `json:"contact_ids" binding:"omitempty"`
	Emails     []string `json:"emails" binding:"omitempty"`
	Add        []string `json:"add" binding:"omitempty"`
	Remove     []string `json:"remove" binding:"omitempty"`
}

// RefreshTagRequest 刷新标签请求
type RefreshTagRequest struct {
	ID int64 `json:"id" binding:"required"`
}

// MergeTagsRequest 合并标签请求
type MergeTagsRequest struct {
	SourceTagIDs []int64 `json:"source_tag_ids" binding:"required"`
	TargetTagID  int64   `json:"target_tag_id" binding:"required"`
}

// RenameTagRequest 重命名标签请求
type RenameTagRequest struct {
	ID      int64  `json:"id" binding:"required"`
	NewName string `json:"new_name" binding:"required,max=64"`
}

// TagResponse 标签响应
type TagResponse struct {
	ID            int64                  `json:"id"`
	Name          string                 `json:"name"`
	Description   *string                `json:"description"`
	Type          string                 `json:"type"`
	Color         *string                `json:"color"`
	RuleTree      map[string]interface{} `json:"rule_tree,omitempty"`
	RefreshPolicy *string                `json:"refresh_policy"`
	LastRefreshAt *time.Time             `json:"last_refresh_at"`
	MemberCount   int64                  `json:"member_count"`
	UsageCount    int64                  `json:"usage_count"`
	CreatedBy     int64                  `json:"created_by"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
}

// TagListResponse 标签列表响应
type TagListResponse struct {
	Tags       []TagResponse `json:"tags"`
	Total      int64         `json:"total"`
	Page       int           `json:"page"`
	Size       int           `json:"size"`
	TotalPages int           `json:"total_pages"`
}

// TagSummaryResponse 标签摘要响应
type TagSummaryResponse struct {
	ID          int64     `json:"id"`
	Name        string    `json:"name"`
	Type        string    `json:"type"`
	Color       *string   `json:"color"`
	MemberCount int64     `json:"member_count"`
	UsageCount  int64     `json:"usage_count"`
	CreatedAt   time.Time `json:"created_at"`
}

// AssignTagsResponse 分配标签响应
type AssignTagsResponse struct {
	SuccessCount int      `json:"success_count"`
	FailedCount  int      `json:"failed_count"`
	Errors       []string `json:"errors,omitempty"`
}

// ToTagResponse 转换为标签响应
func ToTagResponse(tag *entity.Tag) *TagResponse {
	response := &TagResponse{
		ID:            tag.ID,
		Name:          tag.Name,
		Description:   tag.Description,
		Type:          string(tag.Type),
		Color:         tag.Color,
		RefreshPolicy: tag.RefreshPolicy,
		LastRefreshAt: tag.LastRefreshAt,
		MemberCount:   tag.MemberCount,
		UsageCount:    tag.UsageCount,
		CreatedBy:     tag.CreatedBy,
		CreatedAt:     tag.CreatedAt,
		UpdatedAt:     tag.UpdatedAt,
	}

	// 获取规则树
	if ruleTree, err := tag.GetRuleTree(); err == nil && ruleTree != nil {
		response.RuleTree = ruleTree
	}

	return response
}

// ToTagSummaryResponse 转换为标签摘要响应
func ToTagSummaryResponse(tag *entity.Tag) *TagSummaryResponse {
	return &TagSummaryResponse{
		ID:          tag.ID,
		Name:        tag.Name,
		Type:        string(tag.Type),
		Color:       tag.Color,
		MemberCount: tag.MemberCount,
		UsageCount:  tag.UsageCount,
		CreatedAt:   tag.CreatedAt,
	}
}

// ToTagListResponse 转换为标签列表响应
func ToTagListResponse(tags []*entity.Tag, total int64, page, size int) *TagListResponse {
	tagResponses := make([]TagResponse, len(tags))
	for i, tag := range tags {
		tagResponses[i] = *ToTagResponse(tag)
	}

	totalPages := int(total) / size
	if int(total)%size > 0 {
		totalPages++
	}

	return &TagListResponse{
		Tags:       tagResponses,
		Total:      total,
		Page:       page,
		Size:       size,
		TotalPages: totalPages,
	}
}

// ToEntity 转换为标签实体
func (req *CreateTagRequest) ToEntity(tenantID, createdBy int64) *entity.Tag {
	tag := &entity.Tag{
		TenantID:  tenantID,
		Name:      req.Name,
		Type:      entity.TagType(req.Type),
		CreatedBy: createdBy,
	}

	if req.Description != "" {
		tag.Description = &req.Description
	}

	if req.Color != "" {
		tag.Color = &req.Color
	}

	if req.RefreshPolicy != "" {
		tag.RefreshPolicy = &req.RefreshPolicy
	}

	// 设置规则树
	if req.RuleTree != nil {
		tag.SetRuleTree(req.RuleTree)
	}

	return tag
}

// ApplyToEntity 应用更新到标签实体
func (req *UpdateTagRequest) ApplyToEntity(tag *entity.Tag) error {
	if req.Name != "" {
		tag.Name = req.Name
	}

	if req.Description != "" {
		tag.Description = &req.Description
	}

	if req.Color != "" {
		tag.Color = &req.Color
	}

	if req.RefreshPolicy != "" {
		tag.RefreshPolicy = &req.RefreshPolicy
	}

	// 更新规则树
	if req.RuleTree != nil {
		if err := tag.SetRuleTree(req.RuleTree); err != nil {
			return err
		}
	}

	return nil
}

// Validate 验证请求参数
func (req *CreateTagRequest) Validate() error {
	if req.Name == "" {
		return entity.NewTagValidationError("name", "标签名称不能为空")
	}

	if !entity.TagType(req.Type).IsValid() {
		return entity.NewTagValidationError("type", "无效的标签类型")
	}

	// 规则圈选类型必须有规则树
	if req.Type == string(entity.TagTypeRuleBased) && req.RuleTree == nil {
		return entity.NewTagValidationError("rule_tree", "规则圈选类型必须设置规则树")
	}

	if req.RefreshPolicy != "" && !entity.IsValidRefreshPolicy(entity.RefreshPolicy(req.RefreshPolicy)) {
		return entity.NewTagValidationError("refresh_policy", "无效的刷新策略")
	}

	return nil
}

// Validate 验证请求参数
func (req *UpdateTagRequest) Validate() error {
	if req.ID <= 0 {
		return entity.NewTagValidationError("id", "标签ID不能为空")
	}

	if req.RefreshPolicy != "" && !entity.IsValidRefreshPolicy(entity.RefreshPolicy(req.RefreshPolicy)) {
		return entity.NewTagValidationError("refresh_policy", "无效的刷新策略")
	}

	return nil
}

// Validate 验证请求参数
func (req *AssignTagsRequest) Validate() error {
	if len(req.ContactIDs) == 0 && len(req.Emails) == 0 {
		return entity.NewTagValidationError("contacts", "必须指定联系人ID或邮箱地址")
	}

	if len(req.Add) == 0 && len(req.Remove) == 0 {
		return entity.NewTagValidationError("tags", "必须指定要添加或移除的标签")
	}

	return nil
}
