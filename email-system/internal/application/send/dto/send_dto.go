package dto

import (
	"time"

	"gitee.com/heiyee/platforms/email-system/internal/domain/send/entity"
)

// RetryPolicyRequest 重试策略请求
type RetryPolicyRequest struct {
	Mode             string   `json:"mode" binding:"required,oneof=NONE RETRY_LINEAR RETRY_EXPONENTIAL"`
	MaxAttempts      int      `json:"max_attempts" binding:"required,min=1,max=10"`
	InitialBackoffMs int      `json:"initial_backoff_ms" binding:"min=0"`
	MaxBackoffMs     int      `json:"max_backoff_ms" binding:"min=0"`
	RetryOn          []string `json:"retry_on"`
}

// CreateSendPlanRequest 创建发送计划请求
type CreateSendPlanRequest struct {
	PlanType     string              `json:"plan_type" binding:"required,oneof=campaign automation manual"`
	DisplayName  string              `json:"display_name" binding:"required,max=255"`
	TemplateID   string              `json:"template_id" binding:"required,max=128"`
	Priority     int                 `json:"priority" binding:"min=1,max=10"`
	ScheduleFrom *time.Time          `json:"schedule_from"`
	ScheduleTo   *time.Time          `json:"schedule_to"`
	Deadline     *time.Time          `json:"deadline"`
	RetryPolicy  *RetryPolicyRequest `json:"retry_policy"`
}

// UpdateSendPlanRequest 更新发送计划请求
type UpdateSendPlanRequest struct {
	DisplayName  string              `json:"display_name" binding:"omitempty,max=255"`
	TemplateID   string              `json:"template_id" binding:"omitempty,max=128"`
	Priority     int                 `json:"priority" binding:"omitempty,min=1,max=10"`
	ScheduleFrom *time.Time          `json:"schedule_from"`
	ScheduleTo   *time.Time          `json:"schedule_to"`
	Deadline     *time.Time          `json:"deadline"`
	RetryPolicy  *RetryPolicyRequest `json:"retry_policy"`
}

// ListSendPlansRequest 分页查询发送计划请求
type ListSendPlansRequest struct {
	Page     int    `json:"page" binding:"min=1"`
	PageSize int    `json:"page_size" binding:"min=1,max=100"`
	Status   string `json:"status" binding:"omitempty,oneof=draft running paused completed canceled expired"`
	PlanType string `json:"plan_type" binding:"omitempty,oneof=campaign automation manual"`
}

// RetryPolicyResponse 重试策略响应
type RetryPolicyResponse struct {
	Mode             string   `json:"mode"`
	MaxAttempts      int      `json:"max_attempts"`
	InitialBackoffMs int      `json:"initial_backoff_ms"`
	MaxBackoffMs     int      `json:"max_backoff_ms"`
	RetryOn          []string `json:"retry_on"`
}

// SendPlanResponse 发送计划响应
type SendPlanResponse struct {
	PlanID       int64                `json:"plan_id"`
	TenantID     int64                `json:"tenant_id"`
	PlanType     string               `json:"plan_type"`
	DisplayName  string               `json:"display_name"`
	TemplateID   string               `json:"template_id"`
	Priority     int                  `json:"priority"`
	ScheduleFrom *time.Time           `json:"schedule_from"`
	ScheduleTo   *time.Time           `json:"schedule_to"`
	Deadline     *time.Time           `json:"deadline"`
	Status       string               `json:"status"`
	RetryPolicy  *RetryPolicyResponse `json:"retry_policy"`
	CreatedAt    time.Time            `json:"created_at"`
	UpdatedAt    time.Time            `json:"updated_at"`
}

// ListSendPlansResponse 分页查询发送计划响应
type ListSendPlansResponse struct {
	Plans    []*SendPlanResponse `json:"plans"`
	Total    int64               `json:"total"`
	Page     int                 `json:"page"`
	PageSize int                 `json:"page_size"`
}

// SendBatchResponse 发送批次响应
type SendBatchResponse struct {
	BatchID         int64      `json:"batch_id"`
	PlanID          int64      `json:"plan_id"`
	TenantID        int64      `json:"tenant_id"`
	ExpectedSize    int        `json:"expected_size"`
	SendAt          time.Time  `json:"send_at"`
	Priority        int        `json:"priority"`
	Status          string     `json:"status"`
	ThrottleKey     *string    `json:"throttle_key"`
	CancelableUntil *time.Time `json:"cancelable_until"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

// AudienceSnapshotResponse 受众快照响应
type AudienceSnapshotResponse struct {
	SnapshotID     int64                  `json:"snapshot_id"`
	PlanID         int64                  `json:"plan_id"`
	TenantID       int64                  `json:"tenant_id"`
	SegmentQuery   map[string]interface{} `json:"segment_query"`
	MaterializedAt time.Time              `json:"materialized_at"`
	TotalCount     int                    `json:"total_count"`
	CreatedAt      time.Time              `json:"created_at"`
}

// SendRecordResponse 发送记录响应
type SendRecordResponse struct {
	RecordID      int64                  `json:"record_id"`
	BatchID       int64                  `json:"batch_id"`
	PlanID        int64                  `json:"plan_id"`
	TenantID      int64                  `json:"tenant_id"`
	ContactID     int64                  `json:"contact_id"`
	EmailAddress  string                 `json:"email_address"`
	TemplateID    string                 `json:"template_id"`
	Status        string                 `json:"status"`
	AttemptCount  int                    `json:"attempt_count"`
	LastAttemptAt *time.Time             `json:"last_attempt_at"`
	SentAt        *time.Time             `json:"sent_at"`
	DeliveredAt   *time.Time             `json:"delivered_at"`
	BouncedAt     *time.Time             `json:"bounced_at"`
	FailedAt      *time.Time             `json:"failed_at"`
	ErrorMessage  *string                `json:"error_message"`
	Metadata      map[string]interface{} `json:"metadata"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
}

// SendStatisticsResponse 发送统计响应
type SendStatisticsResponse struct {
	PlanID         int64            `json:"plan_id"`
	TotalCount     int64            `json:"total_count"`
	PendingCount   int64            `json:"pending_count"`
	SentCount      int64            `json:"sent_count"`
	DeliveredCount int64            `json:"delivered_count"`
	BouncedCount   int64            `json:"bounced_count"`
	FailedCount    int64            `json:"failed_count"`
	SkippedCount   int64            `json:"skipped_count"`
	StatusCounts   map[string]int64 `json:"status_counts"`
}

// Validate 验证创建发送计划请求
func (req *CreateSendPlanRequest) Validate() error {
	if req.PlanType == "" {
		return entity.NewSendPlanValidationError("plan_type", "计划类型不能为空")
	}

	if req.DisplayName == "" {
		return entity.NewSendPlanValidationError("display_name", "显示名称不能为空")
	}

	if req.TemplateID == "" {
		return entity.NewSendPlanValidationError("template_id", "模板ID不能为空")
	}

	if req.Priority <= 0 {
		req.Priority = entity.PlanType(req.PlanType).GetDefaultPriority()
	}

	// 验证时间窗口
	if req.ScheduleFrom != nil && req.ScheduleTo != nil {
		if req.ScheduleTo.Before(*req.ScheduleFrom) {
			return entity.NewSendPlanValidationError("schedule_to", "结束时间不能早于开始时间")
		}
	}

	// 验证截止时间
	if req.Deadline != nil {
		if req.ScheduleFrom != nil && req.Deadline.Before(*req.ScheduleFrom) {
			return entity.NewSendPlanValidationError("deadline", "截止时间不能早于开始时间")
		}
	}

	// 验证重试策略
	if req.RetryPolicy != nil {
		if err := req.RetryPolicy.Validate(); err != nil {
			return err
		}
	}

	return nil
}

// Validate 验证更新发送计划请求
func (req *UpdateSendPlanRequest) Validate() error {
	if req.Priority < 0 || req.Priority > 10 {
		return entity.NewSendPlanValidationError("priority", "优先级必须在1-10之间")
	}

	// 验证时间窗口
	if req.ScheduleFrom != nil && req.ScheduleTo != nil {
		if req.ScheduleTo.Before(*req.ScheduleFrom) {
			return entity.NewSendPlanValidationError("schedule_to", "结束时间不能早于开始时间")
		}
	}

	// 验证截止时间
	if req.Deadline != nil {
		if req.ScheduleFrom != nil && req.Deadline.Before(*req.ScheduleFrom) {
			return entity.NewSendPlanValidationError("deadline", "截止时间不能早于开始时间")
		}
	}

	// 验证重试策略
	if req.RetryPolicy != nil {
		if err := req.RetryPolicy.Validate(); err != nil {
			return err
		}
	}

	return nil
}

// Validate 验证分页查询发送计划请求
func (req *ListSendPlansRequest) Validate() error {
	if req.Page <= 0 {
		req.Page = 1
	}

	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	if req.PageSize > 100 {
		req.PageSize = 100
	}

	return nil
}

// Validate 验证重试策略请求
func (req *RetryPolicyRequest) Validate() error {
	validModes := []string{"NONE", "RETRY_LINEAR", "RETRY_EXPONENTIAL"}
	validMode := false
	for _, mode := range validModes {
		if req.Mode == mode {
			validMode = true
			break
		}
	}
	if !validMode {
		return entity.NewSendPlanValidationError("retry_policy.mode", "无效的重试模式")
	}

	if req.MaxAttempts < 1 || req.MaxAttempts > 10 {
		return entity.NewSendPlanValidationError("retry_policy.max_attempts", "最大重试次数必须在1-10之间")
	}

	if req.InitialBackoffMs < 0 {
		return entity.NewSendPlanValidationError("retry_policy.initial_backoff_ms", "初始退避时间不能为负数")
	}

	if req.MaxBackoffMs < req.InitialBackoffMs {
		return entity.NewSendPlanValidationError("retry_policy.max_backoff_ms", "最大退避时间不能小于初始退避时间")
	}

	return nil
}

// ToSendPlanResponse 转换为发送计划响应
func ToSendPlanResponse(plan *entity.SendPlan) *SendPlanResponse {
	response := &SendPlanResponse{
		PlanID:       plan.PlanID,
		TenantID:     plan.TenantID,
		PlanType:     string(plan.PlanType),
		DisplayName:  plan.DisplayName,
		TemplateID:   plan.TemplateID,
		Priority:     plan.Priority,
		ScheduleFrom: plan.ScheduleFrom,
		ScheduleTo:   plan.ScheduleTo,
		Deadline:     plan.Deadline,
		Status:       string(plan.Status),
		CreatedAt:    plan.CreatedAt,
		UpdatedAt:    plan.UpdatedAt,
	}

	if plan.RetryPolicy != nil {
		response.RetryPolicy = &RetryPolicyResponse{
			Mode:             plan.RetryPolicy.Mode,
			MaxAttempts:      plan.RetryPolicy.MaxAttempts,
			InitialBackoffMs: plan.RetryPolicy.InitialBackoffMs,
			MaxBackoffMs:     plan.RetryPolicy.MaxBackoffMs,
			RetryOn:          plan.RetryPolicy.RetryOn,
		}
	}

	return response
}

// ToSendBatchResponse 转换为发送批次响应
func ToSendBatchResponse(batch *entity.SendBatch) *SendBatchResponse {
	return &SendBatchResponse{
		BatchID:         batch.BatchID,
		PlanID:          batch.PlanID,
		TenantID:        batch.TenantID,
		ExpectedSize:    batch.ExpectedSize,
		SendAt:          batch.SendAt,
		Priority:        batch.Priority,
		Status:          string(batch.Status),
		ThrottleKey:     batch.ThrottleKey,
		CancelableUntil: batch.CancelableUntil,
		CreatedAt:       batch.CreatedAt,
		UpdatedAt:       batch.UpdatedAt,
	}
}

// ToAudienceSnapshotResponse 转换为受众快照响应
func ToAudienceSnapshotResponse(snapshot *entity.AudienceSnapshot) *AudienceSnapshotResponse {
	return &AudienceSnapshotResponse{
		SnapshotID:     snapshot.SnapshotID,
		PlanID:         snapshot.PlanID,
		TenantID:       snapshot.TenantID,
		SegmentQuery:   snapshot.SegmentQuery,
		MaterializedAt: snapshot.MaterializedAt,
		TotalCount:     snapshot.TotalCount,
		CreatedAt:      snapshot.CreatedAt,
	}
}

// ToSendRecordResponse 转换为发送记录响应
func ToSendRecordResponse(record *entity.SendRecord) *SendRecordResponse {
	return &SendRecordResponse{
		RecordID:      record.RecordID,
		BatchID:       record.BatchID,
		PlanID:        record.PlanID,
		TenantID:      record.TenantID,
		ContactID:     record.ContactID,
		EmailAddress:  record.EmailAddress,
		TemplateID:    record.TemplateID,
		Status:        string(record.Status),
		AttemptCount:  record.AttemptCount,
		LastAttemptAt: record.LastAttemptAt,
		SentAt:        record.SentAt,
		DeliveredAt:   record.DeliveredAt,
		BouncedAt:     record.BouncedAt,
		FailedAt:      record.FailedAt,
		ErrorMessage:  record.ErrorMessage,
		Metadata:      record.Metadata,
		CreatedAt:     record.CreatedAt,
		UpdatedAt:     record.UpdatedAt,
	}
}
