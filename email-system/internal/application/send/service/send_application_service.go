package service

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/email-system/internal/application/send/dto"
	"gitee.com/heiyee/platforms/email-system/internal/domain/send/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/send/repository"
	"gitee.com/heiyee/platforms/email-system/internal/domain/send/service"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// SendApplicationService 发送应用服务
type SendApplicationService struct {
	logger               logiface.Logger
	sendPlanRepo         repository.SendPlanRepository
	sendBatchRepo        repository.SendBatchRepository
	audienceSnapshotRepo repository.AudienceSnapshotRepository
	sendRecordRepo       repository.SendRecordRepository
	sendScheduler        *service.SendScheduler
}

// NewSendApplicationService 创建发送应用服务
func NewSendApplicationService(
	logger logiface.Logger,
	sendPlanRepo repository.SendPlanRepository,
	sendBatchRepo repository.SendBatchRepository,
	audienceSnapshotRepo repository.AudienceSnapshotRepository,
	sendRecordRepo repository.SendRecordRepository,
	sendScheduler *service.SendScheduler,
) *SendApplicationService {
	return &SendApplicationService{
		logger:               logger,
		sendPlanRepo:         sendPlanRepo,
		sendBatchRepo:        sendBatchRepo,
		audienceSnapshotRepo: audienceSnapshotRepo,
		sendRecordRepo:       sendRecordRepo,
		sendScheduler:        sendScheduler,
	}
}

// CreateSendPlan 创建发送计划
func (s *SendApplicationService) CreateSendPlan(ctx context.Context, req *dto.CreateSendPlanRequest, tenantID int64) (*dto.SendPlanResponse, error) {
	s.logger.Info(ctx, "Creating send plan",
		logiface.String("display_name", req.DisplayName),
		logiface.String("plan_type", req.PlanType),
		logiface.Int64("tenant_id", tenantID))

	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 创建发送计划实体
	plan := &entity.SendPlan{
		TenantID:     tenantID,
		PlanType:     entity.PlanType(req.PlanType),
		DisplayName:  req.DisplayName,
		TemplateID:   req.TemplateID,
		Priority:     req.Priority,
		ScheduleFrom: req.ScheduleFrom,
		ScheduleTo:   req.ScheduleTo,
		Deadline:     req.Deadline,
		Status:       entity.PlanStatusDraft,
	}

	// 设置重试策略
	if req.RetryPolicy != nil {
		retryPolicy := &entity.RetryPolicy{
			Mode:             req.RetryPolicy.Mode,
			MaxAttempts:      req.RetryPolicy.MaxAttempts,
			InitialBackoffMs: req.RetryPolicy.InitialBackoffMs,
			MaxBackoffMs:     req.RetryPolicy.MaxBackoffMs,
			RetryOn:          req.RetryPolicy.RetryOn,
		}
		plan.RetryPolicy = retryPolicy
	}

	// 验证实体
	if err := plan.Validate(); err != nil {
		return nil, err
	}

	// 保存到数据库
	if err := s.sendPlanRepo.Create(ctx, plan); err != nil {
		s.logger.Error(ctx, "Failed to create send plan",
			logiface.Error(err),
			logiface.String("display_name", req.DisplayName))
		return nil, entity.NewSendPlanCreateFailedError(fmt.Sprintf("create send plan: %v", err))
	}

	s.logger.Info(ctx, "Send plan created successfully",
		logiface.Int64("plan_id", plan.PlanID),
		logiface.String("display_name", plan.DisplayName))

	return dto.ToSendPlanResponse(plan), nil
}

// UpdateSendPlan 更新发送计划
func (s *SendApplicationService) UpdateSendPlan(ctx context.Context, planID int64, req *dto.UpdateSendPlanRequest, tenantID int64) (*dto.SendPlanResponse, error) {
	s.logger.Info(ctx, "Updating send plan",
		logiface.Int64("plan_id", planID),
		logiface.Int64("tenant_id", tenantID))

	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 查找发送计划
	plan, err := s.sendPlanRepo.FindByID(ctx, tenantID, planID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find send plan",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return nil, entity.NewSendPlanQueryFailedError(fmt.Sprintf("find send plan: %v", err))
	}

	if plan == nil {
		return nil, entity.NewSendPlanNotFoundError(fmt.Sprintf("plan_id: %d", planID))
	}

	// 更新字段
	if req.DisplayName != "" {
		plan.DisplayName = req.DisplayName
	}
	if req.TemplateID != "" {
		plan.TemplateID = req.TemplateID
	}
	if req.Priority > 0 {
		plan.Priority = req.Priority
	}
	if req.ScheduleFrom != nil {
		plan.ScheduleFrom = req.ScheduleFrom
	}
	if req.ScheduleTo != nil {
		plan.ScheduleTo = req.ScheduleTo
	}
	if req.Deadline != nil {
		plan.Deadline = req.Deadline
	}
	if req.RetryPolicy != nil {
		retryPolicy := &entity.RetryPolicy{
			Mode:             req.RetryPolicy.Mode,
			MaxAttempts:      req.RetryPolicy.MaxAttempts,
			InitialBackoffMs: req.RetryPolicy.InitialBackoffMs,
			MaxBackoffMs:     req.RetryPolicy.MaxBackoffMs,
			RetryOn:          req.RetryPolicy.RetryOn,
		}
		plan.RetryPolicy = retryPolicy
	}

	// 验证更新后的实体
	if err := plan.Validate(); err != nil {
		return nil, err
	}

	// 保存更新
	if err := s.sendPlanRepo.Update(ctx, plan); err != nil {
		s.logger.Error(ctx, "Failed to update send plan",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return nil, entity.NewSendPlanUpdateFailedError(fmt.Sprintf("update send plan: %v", err))
	}

	s.logger.Info(ctx, "Send plan updated successfully", logiface.Int64("plan_id", planID))

	return dto.ToSendPlanResponse(plan), nil
}

// GetSendPlan 获取发送计划
func (s *SendApplicationService) GetSendPlan(ctx context.Context, planID, tenantID int64) (*dto.SendPlanResponse, error) {
	s.logger.Debug(ctx, "Getting send plan",
		logiface.Int64("plan_id", planID),
		logiface.Int64("tenant_id", tenantID))

	plan, err := s.sendPlanRepo.FindByID(ctx, tenantID, planID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find send plan",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return nil, entity.NewSendPlanQueryFailedError(fmt.Sprintf("find send plan: %v", err))
	}

	if plan == nil {
		return nil, entity.NewSendPlanNotFoundError(fmt.Sprintf("plan_id: %d", planID))
	}

	return dto.ToSendPlanResponse(plan), nil
}

// ListSendPlans 分页查询发送计划
func (s *SendApplicationService) ListSendPlans(ctx context.Context, req *dto.ListSendPlansRequest, tenantID int64) (*dto.ListSendPlansResponse, error) {
	s.logger.Debug(ctx, "Listing send plans",
		logiface.Int("page", req.Page),
		logiface.Int("page_size", req.PageSize),
		logiface.Int64("tenant_id", tenantID))

	// 验证请求
	if err := req.Validate(); err != nil {
		return nil, err
	}

	offset := (req.Page - 1) * req.PageSize

	// 查询发送计划
	plans, total, err := s.sendPlanRepo.List(ctx, tenantID, offset, req.PageSize)
	if err != nil {
		s.logger.Error(ctx, "Failed to list send plans",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, entity.NewSendPlanQueryFailedError(fmt.Sprintf("list send plans: %v", err))
	}

	// 转换为响应格式
	planResponses := make([]*dto.SendPlanResponse, 0, len(plans))
	for _, plan := range plans {
		planResponses = append(planResponses, dto.ToSendPlanResponse(plan))
	}

	return &dto.ListSendPlansResponse{
		Plans:    planResponses,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// StartSendPlan 启动发送计划
func (s *SendApplicationService) StartSendPlan(ctx context.Context, planID, tenantID int64) error {
	s.logger.Info(ctx, "Starting send plan",
		logiface.Int64("plan_id", planID),
		logiface.Int64("tenant_id", tenantID))

	// 查找发送计划
	plan, err := s.sendPlanRepo.FindByID(ctx, tenantID, planID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find send plan",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return entity.NewSendPlanQueryFailedError(fmt.Sprintf("find send plan: %v", err))
	}

	if plan == nil {
		return entity.NewSendPlanNotFoundError(fmt.Sprintf("plan_id: %d", planID))
	}

	// 启动计划
	if err := plan.Start(); err != nil {
		return err
	}

	// 保存状态更新
	if err := s.sendPlanRepo.Update(ctx, plan); err != nil {
		s.logger.Error(ctx, "Failed to update send plan status",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return entity.NewSendPlanUpdateFailedError(fmt.Sprintf("update send plan status: %v", err))
	}

	s.logger.Info(ctx, "Send plan started successfully", logiface.Int64("plan_id", planID))

	return nil
}

// PauseSendPlan 暂停发送计划
func (s *SendApplicationService) PauseSendPlan(ctx context.Context, planID, tenantID int64) error {
	s.logger.Info(ctx, "Pausing send plan",
		logiface.Int64("plan_id", planID),
		logiface.Int64("tenant_id", tenantID))

	// 查找发送计划
	plan, err := s.sendPlanRepo.FindByID(ctx, tenantID, planID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find send plan",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return entity.NewSendPlanQueryFailedError(fmt.Sprintf("find send plan: %v", err))
	}

	if plan == nil {
		return entity.NewSendPlanNotFoundError(fmt.Sprintf("plan_id: %d", planID))
	}

	// 暂停计划
	if err := plan.Pause(); err != nil {
		return err
	}

	// 保存状态更新
	if err := s.sendPlanRepo.Update(ctx, plan); err != nil {
		s.logger.Error(ctx, "Failed to update send plan status",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return entity.NewSendPlanUpdateFailedError(fmt.Sprintf("update send plan status: %v", err))
	}

	s.logger.Info(ctx, "Send plan paused successfully", logiface.Int64("plan_id", planID))

	return nil
}

// CancelSendPlan 取消发送计划
func (s *SendApplicationService) CancelSendPlan(ctx context.Context, planID, tenantID int64) error {
	s.logger.Info(ctx, "Canceling send plan",
		logiface.Int64("plan_id", planID),
		logiface.Int64("tenant_id", tenantID))

	// 查找发送计划
	plan, err := s.sendPlanRepo.FindByID(ctx, tenantID, planID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find send plan",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return entity.NewSendPlanQueryFailedError(fmt.Sprintf("find send plan: %v", err))
	}

	if plan == nil {
		return entity.NewSendPlanNotFoundError(fmt.Sprintf("plan_id: %d", planID))
	}

	// 取消计划
	if err := plan.Cancel(); err != nil {
		return err
	}

	// 保存状态更新
	if err := s.sendPlanRepo.Update(ctx, plan); err != nil {
		s.logger.Error(ctx, "Failed to update send plan status",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return entity.NewSendPlanUpdateFailedError(fmt.Sprintf("update send plan status: %v", err))
	}

	s.logger.Info(ctx, "Send plan canceled successfully", logiface.Int64("plan_id", planID))

	return nil
}

// DeleteSendPlan 删除发送计划
func (s *SendApplicationService) DeleteSendPlan(ctx context.Context, planID, tenantID int64) error {
	s.logger.Info(ctx, "Deleting send plan",
		logiface.Int64("plan_id", planID),
		logiface.Int64("tenant_id", tenantID))

	// 查找发送计划
	plan, err := s.sendPlanRepo.FindByID(ctx, tenantID, planID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find send plan",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return entity.NewSendPlanQueryFailedError(fmt.Sprintf("find send plan: %v", err))
	}

	if plan == nil {
		return entity.NewSendPlanNotFoundError(fmt.Sprintf("plan_id: %d", planID))
	}

	// 检查是否可以删除（只有草稿、已完成、已取消、已过期状态可以删除）
	if plan.Status != entity.PlanStatusDraft &&
		plan.Status != entity.PlanStatusCompleted &&
		plan.Status != entity.PlanStatusCanceled &&
		plan.Status != entity.PlanStatusExpired {
		return entity.NewSendPlanStateError("只有草稿、已完成、已取消或已过期的计划可以删除")
	}

	// 删除发送计划
	if err := s.sendPlanRepo.Delete(ctx, tenantID, planID); err != nil {
		s.logger.Error(ctx, "Failed to delete send plan",
			logiface.Error(err),
			logiface.Int64("plan_id", planID))
		return entity.NewSendPlanDeleteFailedError(fmt.Sprintf("delete send plan: %v", err))
	}

	s.logger.Info(ctx, "Send plan deleted successfully", logiface.Int64("plan_id", planID))

	return nil
}
