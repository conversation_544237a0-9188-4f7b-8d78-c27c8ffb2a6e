package service

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/email-system/internal/application/template/dto"
	"gitee.com/heiyee/platforms/email-system/internal/domain/template/entity"
	"gitee.com/heiyee/platforms/email-system/internal/domain/template/repository"
	templateService "gitee.com/heiyee/platforms/email-system/internal/domain/template/service"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// TemplateApplicationService 模板应用服务
type TemplateApplicationService struct {
	logger                  logiface.Logger
	templateLocaleRepo      repository.TemplateLocaleRepository
	templateTrackingService *templateService.TemplateTrackingService
}

// NewTemplateApplicationService 创建模板应用服务
func NewTemplateApplicationService(
	logger logiface.Logger,
	templateLocaleRepo repository.TemplateLocaleRepository,
	templateTrackingService *templateService.TemplateTrackingService,
) *TemplateApplicationService {
	return &TemplateApplicationService{
		logger:                  logger,
		templateLocaleRepo:      templateLocaleRepo,
		templateTrackingService: templateTrackingService,
	}
}

// CreateTemplateLocale 创建模板多语言版本
func (s *TemplateApplicationService) CreateTemplateLocale(ctx context.Context, tenantID int64, req *dto.TemplateLocaleCreateRequest) (*dto.TemplateLocaleResponse, error) {
	s.logger.Info(ctx, "Creating template locale",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", req.TemplateID),
		logiface.String("locale", req.Locale))

	// 创建实体
	templateLocale := &entity.TemplateLocale{
		TenantID:    tenantID,
		TemplateID:  req.TemplateID,
		Locale:      req.Locale,
		Name:        req.Name,
		Subject:     req.Subject,
		PreHeader:   req.PreHeader,
		HtmlContent: req.HtmlContent,
		TextContent: req.TextContent,
		Status:      entity.TemplateLocaleStatusDraft,
		Version:     1,
	}

	// 设置追踪配置
	if req.TrackingOptions != nil {
		if err := templateLocale.SetTrackingOptions(req.TrackingOptions.ToTrackingOptions()); err != nil {
			s.logger.Error(ctx, "Failed to set tracking options",
				logiface.Error(err),
				logiface.Int64("template_id", req.TemplateID))
			return nil, fmt.Errorf("set tracking options: %w", err)
		}
	}

	// 设置变量
	if req.Variables != nil {
		if err := templateLocale.SetVariables(req.Variables); err != nil {
			s.logger.Error(ctx, "Failed to set variables",
				logiface.Error(err),
				logiface.Int64("template_id", req.TemplateID))
			return nil, fmt.Errorf("set variables: %w", err)
		}
	}

	// 设置元数据
	if req.Metadata != nil {
		if err := templateLocale.SetMetadata(req.Metadata); err != nil {
			s.logger.Error(ctx, "Failed to set metadata",
				logiface.Error(err),
				logiface.Int64("template_id", req.TemplateID))
			return nil, fmt.Errorf("set metadata: %w", err)
		}
	}

	// 验证
	if err := templateLocale.Validate(); err != nil {
		s.logger.Error(ctx, "Template locale validation failed",
			logiface.Error(err),
			logiface.Int64("template_id", req.TemplateID))
		return nil, fmt.Errorf("validate template locale: %w", err)
	}

	// 保存到数据库
	if err := s.templateLocaleRepo.Create(ctx, templateLocale); err != nil {
		s.logger.Error(ctx, "Failed to create template locale",
			logiface.Error(err),
			logiface.Int64("template_id", req.TemplateID))
		return nil, fmt.Errorf("create template locale: %w", err)
	}

	s.logger.Info(ctx, "Template locale created successfully",
		logiface.Int64("template_locale_id", templateLocale.ID),
		logiface.Int64("template_id", req.TemplateID),
		logiface.String("locale", req.Locale))

	return dto.ToTemplateLocaleResponse(templateLocale), nil
}

// UpdateTemplateLocale 更新模板多语言版本
func (s *TemplateApplicationService) UpdateTemplateLocale(ctx context.Context, tenantID, templateLocaleID int64, req *dto.TemplateLocaleUpdateRequest) (*dto.TemplateLocaleResponse, error) {
	s.logger.Info(ctx, "Updating template locale",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_locale_id", templateLocaleID))

	// 查找现有记录
	templateLocale, err := s.templateLocaleRepo.FindByID(ctx, tenantID, templateLocaleID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find template locale",
			logiface.Error(err),
			logiface.Int64("template_locale_id", templateLocaleID))
		return nil, fmt.Errorf("find template locale: %w", err)
	}

	if templateLocale == nil {
		return nil, entity.NewTemplateNotFoundError(tenantID, 0, "")
	}

	// 更新字段
	if req.Name != "" {
		templateLocale.Name = req.Name
	}
	if req.Subject != "" {
		templateLocale.Subject = req.Subject
	}
	if req.PreHeader != "" {
		templateLocale.PreHeader = req.PreHeader
	}
	if req.HtmlContent != "" {
		templateLocale.HtmlContent = req.HtmlContent
	}
	if req.TextContent != "" {
		templateLocale.TextContent = req.TextContent
	}
	if req.Status != "" {
		templateLocale.Status = req.Status
	}

	// 更新追踪配置
	if req.TrackingOptions != nil {
		if err := templateLocale.SetTrackingOptions(req.TrackingOptions.ToTrackingOptions()); err != nil {
			s.logger.Error(ctx, "Failed to update tracking options",
				logiface.Error(err),
				logiface.Int64("template_locale_id", templateLocaleID))
			return nil, fmt.Errorf("set tracking options: %w", err)
		}
	}

	// 更新变量
	if req.Variables != nil {
		if err := templateLocale.SetVariables(req.Variables); err != nil {
			s.logger.Error(ctx, "Failed to update variables",
				logiface.Error(err),
				logiface.Int64("template_locale_id", templateLocaleID))
			return nil, fmt.Errorf("set variables: %w", err)
		}
	}

	// 更新元数据
	if req.Metadata != nil {
		if err := templateLocale.SetMetadata(req.Metadata); err != nil {
			s.logger.Error(ctx, "Failed to update metadata",
				logiface.Error(err),
				logiface.Int64("template_locale_id", templateLocaleID))
			return nil, fmt.Errorf("set metadata: %w", err)
		}
	}

	// 验证
	if err := templateLocale.Validate(); err != nil {
		s.logger.Error(ctx, "Template locale validation failed",
			logiface.Error(err),
			logiface.Int64("template_locale_id", templateLocaleID))
		return nil, fmt.Errorf("validate template locale: %w", err)
	}

	// 保存更新
	if err := s.templateLocaleRepo.Update(ctx, templateLocale); err != nil {
		s.logger.Error(ctx, "Failed to update template locale",
			logiface.Error(err),
			logiface.Int64("template_locale_id", templateLocaleID))
		return nil, fmt.Errorf("update template locale: %w", err)
	}

	s.logger.Info(ctx, "Template locale updated successfully",
		logiface.Int64("template_locale_id", templateLocaleID))

	return dto.ToTemplateLocaleResponse(templateLocale), nil
}

// GetTemplateLocale 获取模板多语言版本
func (s *TemplateApplicationService) GetTemplateLocale(ctx context.Context, tenantID, templateLocaleID int64) (*dto.TemplateLocaleResponse, error) {
	s.logger.Debug(ctx, "Getting template locale",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_locale_id", templateLocaleID))

	templateLocale, err := s.templateLocaleRepo.FindByID(ctx, tenantID, templateLocaleID)
	if err != nil {
		s.logger.Error(ctx, "Failed to find template locale",
			logiface.Error(err),
			logiface.Int64("template_locale_id", templateLocaleID))
		return nil, fmt.Errorf("find template locale: %w", err)
	}

	if templateLocale == nil {
		return nil, entity.NewTemplateNotFoundError(tenantID, 0, "")
	}

	return dto.ToTemplateLocaleResponse(templateLocale), nil
}

// ListTemplateLocales 获取模板多语言版本列表
func (s *TemplateApplicationService) ListTemplateLocales(ctx context.Context, tenantID int64, req *dto.TemplateLocaleListRequest) (*dto.TemplateLocaleListResponse, error) {
	s.logger.Debug(ctx, "Listing template locales",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", req.TemplateID))

	// 构建查询参数
	params := repository.NewListParams(tenantID).
		WithTemplateID(req.TemplateID).
		WithPagination(req.Page, req.Size).
		WithSort(req.SortField, req.SortOrder)

	// 设置过滤条件
	filters := &repository.ListFilters{
		Locale:          req.Locale,
		Status:          req.Status,
		Name:            req.Name,
		TrackingEnabled: req.TrackingEnabled,
		CreatedAfter:    &req.CreatedAfter,
		CreatedBefore:   &req.CreatedBefore,
	}
	params.WithFilters(filters)

	// 查询数据
	result, err := s.templateLocaleRepo.List(ctx, params)
	if err != nil {
		s.logger.Error(ctx, "Failed to list template locales",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return nil, fmt.Errorf("list template locales: %w", err)
	}

	// 转换响应
	responses := make([]*dto.TemplateLocaleResponse, len(result.TemplateLocales))
	for i, templateLocale := range result.TemplateLocales {
		responses[i] = dto.ToTemplateLocaleResponse(templateLocale)
	}

	return &dto.TemplateLocaleListResponse{
		TemplateLocales: responses,
		Total:           result.Total,
		Page:            result.Page,
		Size:            result.Size,
		TotalPages:      result.TotalPages,
	}, nil
}

// DeleteTemplateLocale 删除模板多语言版本
func (s *TemplateApplicationService) DeleteTemplateLocale(ctx context.Context, tenantID, templateID int64, locale string) error {
	s.logger.Info(ctx, "Deleting template locale",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID),
		logiface.String("locale", locale))

	if err := s.templateLocaleRepo.Delete(ctx, tenantID, templateID, locale); err != nil {
		s.logger.Error(ctx, "Failed to delete template locale",
			logiface.Error(err),
			logiface.Int64("template_id", templateID),
			logiface.String("locale", locale))
		return fmt.Errorf("delete template locale: %w", err)
	}

	s.logger.Info(ctx, "Template locale deleted successfully",
		logiface.Int64("template_id", templateID),
		logiface.String("locale", locale))

	return nil
}

// UpdateTrackingOptions 更新追踪配置
func (s *TemplateApplicationService) UpdateTrackingOptions(ctx context.Context, tenantID, templateID int64, locale string, req *dto.UpdateTrackingOptionsRequest) (*dto.TrackingOptionsDTO, error) {
	s.logger.Info(ctx, "Updating tracking options",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID),
		logiface.String("locale", locale))

	// 转换为领域模型
	options := req.TrackingOptions.ToTrackingOptions()

	// 调用领域服务更新配置
	if err := s.templateTrackingService.UpdateTrackingOptions(ctx, tenantID, templateID, locale, options); err != nil {
		s.logger.Error(ctx, "Failed to update tracking options via domain service",
			logiface.Error(err),
			logiface.Int64("template_id", templateID))
		return nil, fmt.Errorf("update tracking options: %w", err)
	}

	return req.TrackingOptions, nil
}

// GetTrackingConfiguration 获取追踪配置
func (s *TemplateApplicationService) GetTrackingConfiguration(ctx context.Context, tenantID, templateID int64, locale string) (*dto.TrackingConfigurationResponse, error) {
	s.logger.Debug(ctx, "Getting tracking configuration",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID),
		logiface.String("locale", locale))

	config, err := s.templateTrackingService.GetTrackingConfiguration(ctx, tenantID, templateID, locale)
	if err != nil {
		s.logger.Error(ctx, "Failed to get tracking configuration",
			logiface.Error(err),
			logiface.Int64("template_id", templateID))
		return nil, fmt.Errorf("get tracking configuration: %w", err)
	}

	return &dto.TrackingConfigurationResponse{
		Options:        dto.FromTrackingOptions(config.Options),
		TrackingDomain: config.TrackingDomain,
		IsEnabled:      config.IsEnabled,
		DefaultLocale:  config.DefaultLocale,
	}, nil
}

// ResetTrackingToDefaults 重置追踪配置为默认值
func (s *TemplateApplicationService) ResetTrackingToDefaults(ctx context.Context, tenantID, templateID int64, locale string) (*dto.TrackingOptionsDTO, error) {
	s.logger.Info(ctx, "Resetting tracking options to defaults",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID),
		logiface.String("locale", locale))

	if err := s.templateTrackingService.ResetToDefaults(ctx, tenantID, templateID, locale); err != nil {
		s.logger.Error(ctx, "Failed to reset tracking options to defaults",
			logiface.Error(err),
			logiface.Int64("template_id", templateID))
		return nil, fmt.Errorf("reset tracking options: %w", err)
	}

	// 获取重置后的配置
	options, err := s.templateTrackingService.GetTrackingOptions(ctx, tenantID, templateID, locale)
	if err != nil {
		s.logger.Error(ctx, "Failed to get tracking options after reset",
			logiface.Error(err),
			logiface.Int64("template_id", templateID))
		return nil, fmt.Errorf("get tracking options after reset: %w", err)
	}

	return dto.FromTrackingOptions(options), nil
}

// BulkUpdateStatus 批量更新状态
func (s *TemplateApplicationService) BulkUpdateStatus(ctx context.Context, tenantID int64, req *dto.BulkUpdateStatusRequest) error {
	s.logger.Info(ctx, "Bulk updating template locale status",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("count", len(req.IDs)),
		logiface.String("status", string(req.Status)))

	if err := s.templateLocaleRepo.BulkUpdateStatus(ctx, tenantID, req.IDs, req.Status); err != nil {
		s.logger.Error(ctx, "Failed to bulk update status",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		return fmt.Errorf("bulk update status: %w", err)
	}

	s.logger.Info(ctx, "Bulk status update completed successfully",
		logiface.Int("count", len(req.IDs)))

	return nil
}

// ValidateTrackingDomain 验证追踪域名
func (s *TemplateApplicationService) ValidateTrackingDomain(ctx context.Context, req *dto.ValidateTrackingDomainRequest) (*dto.ValidateTrackingDomainResponse, error) {
	s.logger.Debug(ctx, "Validating tracking domain",
		logiface.String("domain", req.Domain))

	status, err := s.templateTrackingService.ValidateTrackingDomain(ctx, req.Domain)
	if err != nil {
		s.logger.Error(ctx, "Failed to validate tracking domain",
			logiface.Error(err),
			logiface.String("domain", req.Domain))
		return nil, fmt.Errorf("validate tracking domain: %w", err)
	}

	return &dto.ValidateTrackingDomainResponse{
		Domain: req.Domain,
		Status: *status,
		Valid:  *status == entity.DomainStatusVerified,
	}, nil
}