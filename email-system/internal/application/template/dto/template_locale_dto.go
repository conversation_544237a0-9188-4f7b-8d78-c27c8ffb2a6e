package dto

import (
	"gitee.com/heiyee/platforms/email-system/internal/domain/template/entity"
)

// TemplateLocaleCreateRequest 创建模板多语言版本请求
type TemplateLocaleCreateRequest struct {
	TemplateID      int64                       `json:"template_id" binding:"required,min=1"`
	Locale          string                      `json:"locale" binding:"required,min=2,max=16"`
	Name            string                      `json:"name" binding:"required,min=1,max=255"`
	Subject         string                      `json:"subject" binding:"required,min=1,max=512"`
	PreHeader       string                      `json:"pre_header,omitempty"`
	HtmlContent     string                      `json:"html_content,omitempty"`
	TextContent     string                      `json:"text_content,omitempty"`
	TrackingOptions *TrackingOptionsDTO         `json:"tracking_options,omitempty"`
	Variables       map[string]interface{}      `json:"variables,omitempty"`
	Metadata        map[string]interface{}      `json:"metadata,omitempty"`
}

// TemplateLocaleUpdateRequest 更新模板多语言版本请求
type TemplateLocaleUpdateRequest struct {
	Name            string                      `json:"name,omitempty"`
	Subject         string                      `json:"subject,omitempty"`
	PreHeader       string                      `json:"pre_header,omitempty"`
	HtmlContent     string                      `json:"html_content,omitempty"`
	TextContent     string                      `json:"text_content,omitempty"`
	Status          entity.TemplateLocaleStatus `json:"status,omitempty"`
	TrackingOptions *TrackingOptionsDTO         `json:"tracking_options,omitempty"`
	Variables       map[string]interface{}      `json:"variables,omitempty"`
	Metadata        map[string]interface{}      `json:"metadata,omitempty"`
}

// TrackingOptionsDTO 追踪配置DTO
type TrackingOptionsDTO struct {
	Enabled                bool                 `json:"enabled"`
	UTM                    *UTMOptionsDTO       `json:"utm,omitempty"`
	Attribution            *AttributionDTO      `json:"attribution,omitempty"`
	Pixel                  *PixelOptionsDTO     `json:"pixel,omitempty"`
	Redirect               *RedirectOptionsDTO  `json:"redirect,omitempty"`
	Domain                 *DomainOptionsDTO    `json:"domain,omitempty"`
	ConversionWindowHours  int                  `json:"conversion_window_hours,omitempty"`
	LinkDomainWhitelist    []string             `json:"link_domain_whitelist,omitempty"`
}

// UTMOptionsDTO UTM配置DTO
type UTMOptionsDTO struct {
	AutoAppend bool   `json:"auto_append"`
	Campaign   string `json:"campaign,omitempty"`
	Content    string `json:"content,omitempty"`
	Source     string `json:"source,omitempty"`
	Medium     string `json:"medium,omitempty"`
	Term       string `json:"term,omitempty"`
}

// AttributionDTO 归因配置DTO
type AttributionDTO struct {
	WindowHours int `json:"window_hours"`
}

// PixelOptionsDTO 像素追踪配置DTO
type PixelOptionsDTO struct {
	EnablePixel  bool `json:"enable_pixel"`
	EnableBeacon bool `json:"enable_beacon"`
	EnableAMP    bool `json:"enable_amp"`
}

// RedirectOptionsDTO 重定向配置DTO
type RedirectOptionsDTO struct {
	Whitelist []string `json:"whitelist"`
}

// DomainOptionsDTO 域名配置DTO
type DomainOptionsDTO struct {
	TrackingDomain string                `json:"tracking_domain"`
	Status         entity.DomainStatus   `json:"status"`
}

// TemplateLocaleResponse 模板多语言版本响应
type TemplateLocaleResponse struct {
	ID              int64                       `json:"id"`
	TenantID        int64                       `json:"tenant_id"`
	TemplateID      int64                       `json:"template_id"`
	Locale          string                      `json:"locale"`
	Name            string                      `json:"name"`
	Subject         string                      `json:"subject"`
	PreHeader       string                      `json:"pre_header,omitempty"`
	HtmlContent     string                      `json:"html_content,omitempty"`
	TextContent     string                      `json:"text_content,omitempty"`
	Status          entity.TemplateLocaleStatus `json:"status"`
	Version         int                         `json:"version"`
	TrackingOptions *TrackingOptionsDTO         `json:"tracking_options,omitempty"`
	Variables       map[string]interface{}      `json:"variables,omitempty"`
	Metadata        map[string]interface{}      `json:"metadata,omitempty"`
	CreatedAt       string                      `json:"created_at"`
	UpdatedAt       string                      `json:"updated_at"`
}

// TemplateLocaleListRequest 模板多语言版本列表请求
type TemplateLocaleListRequest struct {
	TemplateID      int64                       `form:"template_id"`
	Locale          string                      `form:"locale"`
	Status          entity.TemplateLocaleStatus `form:"status"`
	Name            string                      `form:"name"`
	TrackingEnabled *bool                       `form:"tracking_enabled"`
	CreatedAfter    string                      `form:"created_after"`
	CreatedBefore   string                      `form:"created_before"`
	Page            int                         `form:"page,default=1"`
	Size            int                         `form:"size,default=20"`
	SortField       string                      `form:"sort_field,default=created_at"`
	SortOrder       string                      `form:"sort_order,default=desc"`
}

// TemplateLocaleListResponse 模板多语言版本列表响应
type TemplateLocaleListResponse struct {
	TemplateLocales []*TemplateLocaleResponse `json:"template_locales"`
	Total           int64                     `json:"total"`
	Page            int                       `json:"page"`
	Size            int                       `json:"size"`
	TotalPages      int                       `json:"total_pages"`
}

// UpdateTrackingOptionsRequest 更新追踪配置请求
type UpdateTrackingOptionsRequest struct {
	TrackingOptions *TrackingOptionsDTO `json:"tracking_options" binding:"required"`
}

// TrackingConfigurationResponse 追踪配置响应
type TrackingConfigurationResponse struct {
	Options        *TrackingOptionsDTO `json:"options"`
	TrackingDomain string              `json:"tracking_domain"`
	IsEnabled      bool                `json:"is_enabled"`
	DefaultLocale  string              `json:"default_locale"`
}

// BulkUpdateStatusRequest 批量更新状态请求
type BulkUpdateStatusRequest struct {
	IDs    []int64                     `json:"ids" binding:"required,min=1"`
	Status entity.TemplateLocaleStatus `json:"status" binding:"required"`
}

// ValidateTrackingDomainRequest 验证追踪域名请求
type ValidateTrackingDomainRequest struct {
	Domain string `json:"domain" binding:"required,min=1"`
}

// ValidateTrackingDomainResponse 验证追踪域名响应
type ValidateTrackingDomainResponse struct {
	Domain string              `json:"domain"`
	Status entity.DomainStatus `json:"status"`
	Valid  bool                `json:"valid"`
}

// ToTrackingOptions 转换为领域模型
func (dto *TrackingOptionsDTO) ToTrackingOptions() *entity.TrackingOptions {
	if dto == nil {
		return nil
	}

	options := &entity.TrackingOptions{
		Enabled:                dto.Enabled,
		ConversionWindowHours:  dto.ConversionWindowHours,
		LinkDomainWhitelist:    dto.LinkDomainWhitelist,
	}

	if dto.UTM != nil {
		options.UTM = &entity.UTMOptions{
			AutoAppend: dto.UTM.AutoAppend,
			Campaign:   dto.UTM.Campaign,
			Content:    dto.UTM.Content,
			Source:     dto.UTM.Source,
			Medium:     dto.UTM.Medium,
			Term:       dto.UTM.Term,
		}
	}

	if dto.Attribution != nil {
		options.Attribution = &entity.AttributionOptions{
			WindowHours: dto.Attribution.WindowHours,
		}
	}

	if dto.Pixel != nil {
		options.Pixel = &entity.PixelOptions{
			EnablePixel:  dto.Pixel.EnablePixel,
			EnableBeacon: dto.Pixel.EnableBeacon,
			EnableAMP:    dto.Pixel.EnableAMP,
		}
	}

	if dto.Redirect != nil {
		options.Redirect = &entity.RedirectOptions{
			Whitelist: dto.Redirect.Whitelist,
		}
	}

	if dto.Domain != nil {
		options.Domain = &entity.DomainOptions{
			TrackingDomain: dto.Domain.TrackingDomain,
			Status:         dto.Domain.Status,
		}
	}

	return options
}

// FromTrackingOptions 从领域模型转换
func FromTrackingOptions(options *entity.TrackingOptions) *TrackingOptionsDTO {
	if options == nil {
		return nil
	}

	dto := &TrackingOptionsDTO{
		Enabled:                options.Enabled,
		ConversionWindowHours:  options.ConversionWindowHours,
		LinkDomainWhitelist:    options.LinkDomainWhitelist,
	}

	if options.UTM != nil {
		dto.UTM = &UTMOptionsDTO{
			AutoAppend: options.UTM.AutoAppend,
			Campaign:   options.UTM.Campaign,
			Content:    options.UTM.Content,
			Source:     options.UTM.Source,
			Medium:     options.UTM.Medium,
			Term:       options.UTM.Term,
		}
	}

	if options.Attribution != nil {
		dto.Attribution = &AttributionDTO{
			WindowHours: options.Attribution.WindowHours,
		}
	}

	if options.Pixel != nil {
		dto.Pixel = &PixelOptionsDTO{
			EnablePixel:  options.Pixel.EnablePixel,
			EnableBeacon: options.Pixel.EnableBeacon,
			EnableAMP:    options.Pixel.EnableAMP,
		}
	}

	if options.Redirect != nil {
		dto.Redirect = &RedirectOptionsDTO{
			Whitelist: options.Redirect.Whitelist,
		}
	}

	if options.Domain != nil {
		dto.Domain = &DomainOptionsDTO{
			TrackingDomain: options.Domain.TrackingDomain,
			Status:         options.Domain.Status,
		}
	}

	return dto
}

// ToTemplateLocaleResponse 转换为响应DTO
func ToTemplateLocaleResponse(templateLocale *entity.TemplateLocale) *TemplateLocaleResponse {
	if templateLocale == nil {
		return nil
	}

	return &TemplateLocaleResponse{
		ID:              templateLocale.ID,
		TenantID:        templateLocale.TenantID,
		TemplateID:      templateLocale.TemplateID,
		Locale:          templateLocale.Locale,
		Name:            templateLocale.Name,
		Subject:         templateLocale.Subject,
		PreHeader:       templateLocale.PreHeader,
		HtmlContent:     templateLocale.HtmlContent,
		TextContent:     templateLocale.TextContent,
		Status:          templateLocale.Status,
		Version:         templateLocale.Version,
		TrackingOptions: FromTrackingOptions(templateLocale.GetTrackingOptions()),
		Variables:       templateLocale.GetVariables(),
		Metadata:        templateLocale.GetMetadata(),
		CreatedAt:       templateLocale.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:       templateLocale.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}
}