package routes

import (
	"context"

	"gitee.com/heiyee/platforms/email-system/internal/infrastructure/container"
	"gitee.com/heiyee/platforms/pkg/common"
	"gitee.com/heiyee/platforms/pkg/grpcregistry"
	"gitee.com/heiyee/platforms/pkg/httpmiddleware"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/api/userpb"

	"github.com/gin-gonic/gin"
	"google.golang.org/grpc"
)

// Context 键常量
const (
	ContextKeyGinContext = "gin_context"
	UserServiceName      = "platforms-user"
)

// UserInfoProviderAdapter 用户信息提供者适配器
type UserInfoProviderAdapter struct {
	container *container.DependencyContainer
}

// GetUserInfo 实现 UserInfoProvider 接口
func (a *UserInfoProviderAdapter) GetUserInfo(ctx context.Context, token string, headers map[string]string) *httpmiddleware.AuthedUser {
	logger := a.container.GetLogger()

	// 使用gRPC客户端获取用户信息
	client, err := grpcregistry.GetClientGlobal(ctx, UserServiceName, func(conn *grpc.ClientConn) interface{} {
		return userpb.NewUserServiceClient(conn)
	})
	if err != nil {
		logger.Warn(ctx, "Failed to get user service client", logiface.Error(err))
		return nil
	}

	userClient, ok := client.(userpb.UserServiceClient)
	if !ok {
		logger.Error(ctx, "Failed to cast to UserServiceClient")
		return nil
	}

	// 从 context 中获取 appId - 支持多种协议（HTTP、gRPC等）
	appId := getAppIdFromContext(ctx)

	resp, err := userClient.GetUserInfoByToken(ctx, &userpb.GetUserInfoByTokenRequest{
		Token: token,
		AppId: appId,
	})
	if err != nil {
		logger.Warn(ctx, "Failed to get user info by token", logiface.Error(err))
		return nil
	}

	if resp == nil || resp.Code != 0 || resp.Data == nil {
		logger.Warn(ctx, "Invalid or expired token")
		return nil
	}

	return &httpmiddleware.AuthedUser{
		UserId:   resp.Data.UserId,
		Username: resp.Data.Username,
		RealName: resp.Data.RealName,
		Email:    resp.Data.Email,
		TenantId: resp.Data.TenantId,
	}
}

// AppInfoProviderAdapter 应用信息提供者适配器
type AppInfoProviderAdapter struct {
	container *container.DependencyContainer
}

// GetAppInfo 实现 AppInfoProvider 接口
func (a *AppInfoProviderAdapter) GetAppInfo(ctx context.Context, appId string) *httpmiddleware.AppInfo {
	logger := a.container.GetLogger()

	// 使用gRPC客户端获取应用信息
	client, err := grpcregistry.GetClientGlobal(ctx, UserServiceName, func(conn *grpc.ClientConn) interface{} {
		return userpb.NewUserServiceClient(conn)
	})
	if err != nil {
		logger.Warn(ctx, "Failed to get user service client", logiface.Error(err))
		return nil
	}

	userClient, ok := client.(userpb.UserServiceClient)
	if !ok {
		logger.Error(ctx, "Failed to cast to UserServiceClient")
		return nil
	}

	resp, err := userClient.GetAppInfo(ctx, &userpb.GetAppInfoRequest{
		InternalAppId: 0, // 使用 appId 参数
		AppId:         appId,
	})
	if err != nil {
		logger.Warn(ctx, "Failed to get app info", logiface.Error(err))
		return nil
	}

	if resp == nil || resp.Code != 0 || resp.Data == nil {
		logger.Warn(ctx, "Invalid app info")
		return nil
	}

	return &httpmiddleware.AppInfo{
		TenantId:      resp.Data.TenantId,
		AppId:         resp.Data.AppId,
		InternalAppId: resp.Data.InternalAppId,
	}
}

// getAppIdFromContext 从 context 中获取 appId - 支持多种协议
func getAppIdFromContext(ctx context.Context) string {
	// 方法1：从 context 中直接获取（由中间件注入）
	if appID, ok := ctx.Value(common.HeaderAppId).(string); ok && appID != "" {
		return appID
	}

	// 方法2：从 metadata 中获取（gRPC场景）
	if md, ok := ctx.Value(common.ContextKeyMetadata).(map[string]string); ok {
		if appID, exists := md[common.HeaderAppId]; exists && appID != "" {
			return appID
		}
	}

	// 方法3：从 HTTP header 中获取（如果 context 中有 gin.Context）
	if ginCtx, ok := ctx.Value(ContextKeyGinContext).(*gin.Context); ok {
		if appID := ginCtx.GetHeader("X-App-Id"); appID != "" {
			return appID
		}
	}

	return ""
}