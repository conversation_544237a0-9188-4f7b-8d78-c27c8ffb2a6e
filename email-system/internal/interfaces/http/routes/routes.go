package routes

import (
	"gitee.com/heiyee/platforms/email-system/internal/infrastructure/container"
	"gitee.com/heiyee/platforms/email-system/pkg/config"
	"gitee.com/heiyee/platforms/pkg/httpmiddleware"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"github.com/gin-gonic/gin"
	"time"
)

// SetupRoutes 设置路由
func SetupRoutes(engine *gin.Engine, container *container.DependencyContainer, appConfig *config.AppConfig, logger logiface.Logger) {

	// 设置API路由
	setupAPIRoutes(engine, container)

	// 设置追踪路由
	setupTrackingRoutes(engine, container)

	// 设置退订和同意路由
	setupUnsubscribeRoutes(engine, container, appConfig)
	// 设置健康检查路由
	setupHealthRoutes(engine)
}

// setupAPIRoutes 设置API路由
func setupAPIRoutes(engine *gin.Engine, container *container.DependencyContainer) {
	// API v1 路由组
	v1 := engine.Group("/api/email-system/v1")
	serviceName := "email-system"

	// 创建适配器
	userInfoProvider := &UserInfoProviderAdapter{container: container}
	appInfoProvider := &AppInfoProviderAdapter{container: container}

	// 创建路由组
	auth := v1.Group("")

	// 使用统一中间件配置
	httpmiddleware.SetupCommonMiddleware(auth, &httpmiddleware.MiddlewareConfig{
		ServiceName:           serviceName,
		EnableRequestID:       true,
		EnableSecurityHeaders: true,
		EnableRecovery:        true,
		EnableMetrics:         true,
		EnableRequestSize:     true,
		MaxRequestSize:        10 << 20, // 10MB
		EnableAPIVersion:      false,
		EnableTimeout:         true,
		RequestTimeout:        10 * time.Second,
		EnableTraceID:         true,
		Logger:                container.GetLogger(),
		EnableUserInfo:        true,
		UserInfoProvider:      userInfoProvider,
		AppInfoProvider:       appInfoProvider,
		EnableAccessLog:       true,
	})
	{
		// 联系人路由
		setupContactRoutes(auth, container)

		// 标签路由
		setupTagRoutes(auth, container)

		// 人群圈选路由
		setupSegmentRoutes(auth, container)

		// 分析路由
		setupAnalyticsRoutes(auth, container)

		// 导入路由
		setupImportRoutes(auth, container)

		// 设置规则路由
		setupRuleRoutes(auth, container)

		// 设置发送路由
		setupSendRoutes(auth, container)
	}
}

// setupContactRoutes 设置联系人路由
func setupContactRoutes(rg *gin.RouterGroup, container *container.DependencyContainer) {
	contactHandler := container.GetContactHandler()
	contacts := rg.Group("/contacts")
	{
		// 基础CRUD操作 - 仅使用GET/POST方法，无path参数
		contacts.POST("/create", contactHandler.Create)              // 创建联系人
		contacts.POST("/update", contactHandler.Update)              // 更新联系人
		contacts.GET("/detail", contactHandler.Get)                  // 获取联系人详情（通过查询参数）
		contacts.POST("/search", contactHandler.Search)              // 搜索联系人
		contacts.POST("/delete", contactHandler.Delete)              // 删除联系人
		contacts.POST("/batch-create", contactHandler.BatchCreate)   // 批量创建联系人
		contacts.POST("/batch-update", contactHandler.BatchUpdate)   // 批量更新联系人
		contacts.POST("/update-status", contactHandler.UpdateStatus) // 批量更新状态
	}
}

// setupTagRoutes 设置标签路由
func setupTagRoutes(rg *gin.RouterGroup, container *container.DependencyContainer) {
	tagHandler := container.GetTagHandler()
	tags := rg.Group("/tags")
	{
		// 基础CRUD操作 - 仅使用GET/POST方法，无path参数
		tags.POST("/create", tagHandler.Create)   // 创建标签
		tags.POST("/update", tagHandler.Update)   // 更新标签
		tags.GET("/detail", tagHandler.Get)       // 获取标签详情（通过查询参数）
		tags.GET("/list", tagHandler.List)        // 获取标签列表
		tags.POST("/delete", tagHandler.Delete)   // 删除标签
		tags.POST("/assign", tagHandler.Assign)   // 分配标签
		tags.POST("/refresh", tagHandler.Refresh) // 刷新标签
		tags.POST("/merge", tagHandler.Merge)     // 合并标签
		tags.POST("/rename", tagHandler.Rename)   // 重命名标签
		tags.GET("/popular", tagHandler.Popular)  // 获取热门标签
		tags.GET("/recent", tagHandler.Recent)    // 获取最近使用的标签
	}
}

// setupSegmentRoutes 设置人群圈选路由
func setupSegmentRoutes(rg *gin.RouterGroup, container *container.DependencyContainer) {
	segmentHandler := container.GetSegmentHandler()
	segments := rg.Group("/segments")
	{
		// 基础CRUD操作 - 仅使用GET/POST方法，无path参数
		segments.POST("/create", segmentHandler.Create)        // 创建人群圈选
		segments.POST("/update", segmentHandler.Update)        // 更新人群圈选
		segments.GET("/detail", segmentHandler.Get)            // 获取人群圈选详情（通过查询参数）
		segments.GET("/list", segmentHandler.List)             // 获取人群圈选列表
		segments.POST("/delete", segmentHandler.Delete)        // 删除人群圈选
		segments.POST("/preview", segmentHandler.Preview)      // 预览人群圈选
		segments.POST("/rebuild", segmentHandler.Rebuild)      // 重建人群圈选
		segments.GET("/export", segmentHandler.Export)         // 导出人群圈选
		segments.GET("/job-status", segmentHandler.GetJob)     // 获取任务状态（通过查询参数）
		segments.POST("/cancel-job", segmentHandler.CancelJob) // 取消任务
	}
}

// setupHealthRoutes 设置健康检查路由
func setupHealthRoutes(engine *gin.Engine) {
	engine.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"service": "email-system",
		})
	})

	engine.GET("/ready", func(c *gin.Context) {
		// TODO: 检查依赖服务状态
		c.JSON(200, gin.H{
			"status": "ready",
		})
	})
}

// setupUnsubscribeRoutes 设置退订和同意路由
func setupUnsubscribeRoutes(engine *gin.Engine, container *container.DependencyContainer, appConfig *config.AppConfig) {
	// 直接使用容器中的退订处理器
	unsubscribeHandler := container.GetUnsubscribeHandler()

	// 公开路由（不需要认证）
	public := engine.Group("/api/email-system/")
	{
		// 退订页面和处理
		public.GET("/unsubscribe", unsubscribeHandler.ShowUnsubscribePage)
		public.POST("/unsubscribe", unsubscribeHandler.ProcessUnsubscribe)

		// 同意页面和处理
		public.GET("/consent", unsubscribeHandler.ShowConsentPage)
		public.POST("/consent", unsubscribeHandler.ProcessConsentForm)
	}

	// API路由
	api := engine.Group("/api/v1")
	{
		// 退订和同意API
		api.POST("/unsubscribe", unsubscribeHandler.ProcessUnsubscribe)
		api.POST("/consent/grant", unsubscribeHandler.GrantConsent)
		api.POST("/consent/withdraw", unsubscribeHandler.WithdrawConsent)
		api.GET("/consent/status", unsubscribeHandler.GetConsentStatus)
		api.POST("/unsubscribe/generate-link", unsubscribeHandler.GenerateUnsubscribeLink)
	}
}

// setupRuleRoutes 设置规则路由
func setupRuleRoutes(api *gin.RouterGroup, container *container.DependencyContainer) {
	ruleHandler := container.GetRuleHandler()
	{
		// 规则相关路由
		ruleGroup := api.Group("/rules")
		{
			ruleGroup.POST("/validate", ruleHandler.ValidateRule)          // 验证规则
			ruleGroup.POST("/evaluate", ruleHandler.EvaluateRule)          // 评估规则
			ruleGroup.GET("/templates", ruleHandler.GetRuleTemplates)      // 获取规则模板
			ruleGroup.POST("/parse", ruleHandler.ParseRule)                // 解析规则
			ruleGroup.POST("/build-contact", ruleHandler.BuildContactRule) // 构建联系人规则
			ruleGroup.GET("/operators", ruleHandler.GetRuleOperators)      // 获取规则操作符
			ruleGroup.GET("/field-types", ruleHandler.GetFieldTypes)       // 获取字段类型
		}

		// 标签规则相关路由
		tagGroup := api.Group("/tags")
		{
			tagGroup.POST("/refresh-tag", container.GetTagHandler().RefreshTag)        // 刷新标签（通过请求体传递id）
			tagGroup.POST("/preview-rule", container.GetTagHandler().PreviewTagRule)   // 预览标签规则
			tagGroup.POST("/validate-rule", container.GetTagHandler().ValidateTagRule) // 验证标签规则
			tagGroup.GET("/rule-fields", container.GetTagHandler().GetTagRuleFields)   // 获取标签规则字段
		}

		// 人群圈选规则相关路由
		segmentGroup := api.Group("/segments")
		{
			segmentGroup.POST("/preview-rule", container.GetSegmentHandler().PreviewSegmentRule)   // 预览人群圈选规则
			segmentGroup.POST("/validate-rule", container.GetSegmentHandler().ValidateSegmentRule) // 验证人群圈选规则
			segmentGroup.POST("/rebuild-segment", container.GetSegmentHandler().RebuildSegment)    // 重建人群圈选（通过请求体传递id）
		}
	}
}

// setupSendRoutes 设置发送路由
func setupSendRoutes(api *gin.RouterGroup, container *container.DependencyContainer) {
	sendHandler := container.GetSendHandler()
	{
		// 发送计划路由
		sendPlanGroup := api.Group("/send-plans")
		{
			sendPlanGroup.POST("/create", sendHandler.CreateSendPlan) // 创建发送计划
			sendPlanGroup.GET("/list", sendHandler.ListSendPlans)     // 获取发送计划列表
			sendPlanGroup.GET("/detail", sendHandler.GetSendPlan)     // 获取发送计划详情（通过查询参数id）
			sendPlanGroup.POST("/update", sendHandler.UpdateSendPlan) // 更新发送计划
			sendPlanGroup.POST("/delete", sendHandler.DeleteSendPlan) // 删除发送计划
			sendPlanGroup.POST("/start", sendHandler.StartSendPlan)   // 开始发送计划
			sendPlanGroup.POST("/pause", sendHandler.PauseSendPlan)   // 暂停发送计划
			sendPlanGroup.POST("/cancel", sendHandler.CancelSendPlan) // 取消发送计划
		}
	}
}

// setupTrackingRoutes 设置追踪路由
func setupTrackingRoutes(engine *gin.Engine, container *container.DependencyContainer) {
	trackingHandler := container.GetTrackingHandler()

	// 追踪路由（公开访问，无需认证）
	tracking := engine.Group("/api/email-system/tracking")
	{
		// 像素追踪
		tracking.GET("/open", trackingHandler.HandleOpen)
		tracking.GET("/beacon", trackingHandler.HandleBeacon)
		tracking.GET("/amp/open", trackingHandler.HandleAMPOpen)

		// 点击追踪
		tracking.GET("/redirect", trackingHandler.HandleClick)

		// 转化追踪
		tracking.POST("/conversion", trackingHandler.HandleConversion)

		// 入站回复追踪
		tracking.POST("/inbound", trackingHandler.HandleInbound)

		// 批量事件处理（用于边缘节点）
		tracking.POST("/batch", trackingHandler.HandleBatchEvents)
	}
}

// setupAnalyticsRoutes 设置分析路由
func setupAnalyticsRoutes(rg *gin.RouterGroup, container *container.DependencyContainer) {
	analyticsHandler := container.GetAnalyticsHandler()

	// 分析路由 - 仅使用GET/POST方法，无path参数
	analytics := rg.Group("/analytics")
	{
		// 活动统计
		analytics.GET("/campaign-stats", analyticsHandler.GetCampaignStats)    // 获取活动统计（通过查询参数campaignId）
		analytics.GET("/campaign-realtime", analyticsHandler.GetRealtimeStats) // 获取实时统计（通过查询参数campaignId）

		// 报表生成
		analytics.POST("/generate-report", analyticsHandler.GenerateReport) // 生成报表
		analytics.GET("/report-detail", analyticsHandler.GetReport)         // 获取报表详情（通过查询参数reportId）
		analytics.GET("/download-report", analyticsHandler.DownloadReport)  // 下载报表（通过查询参数reportId）

		// 数据导出
		analytics.POST("/create-export", analyticsHandler.CreateExport)    // 创建导出任务
		analytics.GET("/export-status", analyticsHandler.GetExportStatus)  // 获取导出状态（通过查询参数exportId）
		analytics.GET("/download-export", analyticsHandler.DownloadExport) // 下载导出文件（通过查询参数exportId）

		// 链接分析
		analytics.GET("/campaign-links", analyticsHandler.GetLinkAnalytics) // 获取链接分析（通过查询参数campaignId）
		analytics.GET("/campaign-heatmap", analyticsHandler.GetHeatmap)     // 获取热力图（通过查询参数campaignId）

		// 用户旅程
		analytics.GET("/subscriber-journey", analyticsHandler.GetUserJourney)     // 获取用户旅程（通过查询参数subscriberId）
		analytics.GET("/conversion-funnel", analyticsHandler.GetConversionFunnel) // 获取转化漏斗（通过查询参数campaignId）

		// 实时指标
		analytics.GET("/realtime-overview", analyticsHandler.GetRealtimeOverview) // 获取实时概览
		analytics.GET("/realtime-events", analyticsHandler.GetRealtimeEvents)     // 获取实时事件
	}
}

// setupImportRoutes 设置导入路由
func setupImportRoutes(rg *gin.RouterGroup, container *container.DependencyContainer) {
	importHandler := container.GetImportHandler()
	imports := rg.Group("/import")
	{
		// 导入任务管理 - 使用明确的业务语义路径名称
		imports.POST("/create", importHandler.CreateImportJob)       // 创建导入任务
		imports.GET("/job-list", importHandler.ListImportJobs)       // 获取导入任务列表
		imports.GET("/job-status", importHandler.GetImportJobStatus) // 获取任务状态
		imports.POST("/process-job", importHandler.ProcessImportJob) // 开始处理任务
		imports.POST("/cancel-job", importHandler.CancelImportJob)   // 取消任务
		imports.POST("/retry-job", importHandler.RetryImportJob)     // 重试任务
		imports.POST("/delete-job", importHandler.DeleteImportJob)   // 删除任务

		// 错误和批次管理
		imports.GET("/job-errors", importHandler.GetImportErrors)   // 获取错误列表
		imports.GET("/job-batches", importHandler.GetImportBatches) // 获取批次列表
	}
}
