package handlers

import (
	"strconv"

	"gitee.com/heiyee/platforms/email-system/internal/application/send/dto"
	"gitee.com/heiyee/platforms/email-system/internal/application/send/service"
	"gitee.com/heiyee/platforms/email-system/internal/domain/send/entity"
	responsepkg "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"github.com/gin-gonic/gin"
)

// SendHandler 发送处理器
type SendHandler struct {
	logger      logiface.Logger
	sendService *service.SendApplicationService
}

// NewSendHandler 创建发送处理器
func NewSendHandler(
	logger logiface.Logger,
	sendService *service.SendApplicationService,
) *SendHandler {
	return &SendHandler{
		logger:      logger,
		sendService: sendService,
	}
}

// CreateSendPlan 创建发送计划
func (h *SendHandler) CreateSendPlan(c *gin.Context) {
	var req dto.CreateSendPlanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 创建发送计划
	result, err := h.sendService.CreateSendPlan(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleSendError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// UpdateSendPlan 更新发送计划
func (h *SendHandler) UpdateSendPlan(c *gin.Context) {
	planID, err := parseIDParam(c, "id")
	if err != nil {
		return
	}

	var req dto.UpdateSendPlanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 更新发送计划
	result, err := h.sendService.UpdateSendPlan(c.Request.Context(), planID, &req, tenantID)
	if err != nil {
		HandleSendError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// GetSendPlan 获取发送计划
func (h *SendHandler) GetSendPlan(c *gin.Context) {
	planID, err := parseIDParam(c, "id")
	if err != nil {
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 获取发送计划
	result, err := h.sendService.GetSendPlan(c.Request.Context(), planID, tenantID)
	if err != nil {
		HandleSendError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// ListSendPlans 分页查询发送计划
func (h *SendHandler) ListSendPlans(c *gin.Context) {
	var req dto.ListSendPlansRequest

	// 解析查询参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil {
			req.Page = page
		}
	}
	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil {
			req.PageSize = pageSize
		}
	}
	req.Status = c.Query("status")
	req.PlanType = c.Query("plan_type")

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 查询发送计划
	result, err := h.sendService.ListSendPlans(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleSendError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// StartSendPlan 启动发送计划
func (h *SendHandler) StartSendPlan(c *gin.Context) {
	planID, err := parseIDParam(c, "id")
	if err != nil {
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 启动发送计划
	if err := h.sendService.StartSendPlan(c.Request.Context(), planID, tenantID); err != nil {
		HandleSendError(c, err)
		return
	}

	responsepkg.Success(c, gin.H{
		"message": "发送计划启动成功",
	})
}

// PauseSendPlan 暂停发送计划
func (h *SendHandler) PauseSendPlan(c *gin.Context) {
	planID, err := parseIDParam(c, "id")
	if err != nil {
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 暂停发送计划
	if err := h.sendService.PauseSendPlan(c.Request.Context(), planID, tenantID); err != nil {
		HandleSendError(c, err)
		return
	}

	responsepkg.Success(c, gin.H{
		"message": "发送计划暂停成功",
	})
}

// CancelSendPlan 取消发送计划
func (h *SendHandler) CancelSendPlan(c *gin.Context) {
	planID, err := parseIDParam(c, "id")
	if err != nil {
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 取消发送计划
	if err := h.sendService.CancelSendPlan(c.Request.Context(), planID, tenantID); err != nil {
		HandleSendError(c, err)
		return
	}

	responsepkg.Success(c, gin.H{
		"message": "发送计划取消成功",
	})
}

// DeleteSendPlan 删除发送计划
func (h *SendHandler) DeleteSendPlan(c *gin.Context) {
	planID, err := parseIDParam(c, "id")
	if err != nil {
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 删除发送计划
	if err := h.sendService.DeleteSendPlan(c.Request.Context(), planID, tenantID); err != nil {
		HandleSendError(c, err)
		return
	}

	responsepkg.Success(c, gin.H{
		"message": "发送计划删除成功",
	})
}

// HandleSendError 处理发送相关错误
func HandleSendError(c *gin.Context, err error) {
	if sendErr, ok := err.(*entity.SendError); ok {
		switch sendErr.Code {
		case entity.CodeSendPlanValidationError,
			entity.CodeSendBatchValidationError,
			entity.CodeAudienceSnapshotValidationError,
			entity.CodeSendRecordValidationError:
			responsepkg.Error(c, responsepkg.CodeValidationError, sendErr.Message)
		case entity.CodeSendPlanNotFoundError,
			entity.CodeSendBatchNotFoundError,
			entity.CodeAudienceSnapshotNotFoundError,
			entity.CodeSendRecordNotFoundError:
			responsepkg.NotFound(c, sendErr.Message)
		case entity.CodeSendPlanStateError,
			entity.CodeSendBatchStateError:
			responsepkg.Error(c, responsepkg.CodeInvalidRequest, sendErr.Message)
		case entity.CodeSendPlanCreateFailedError,
			entity.CodeSendPlanUpdateFailedError,
			entity.CodeSendPlanDeleteFailedError,
			entity.CodeSendPlanQueryFailedError,
			entity.CodeSendBatchCreateFailedError,
			entity.CodeSendBatchUpdateFailedError,
			entity.CodeSendBatchDeleteFailedError,
			entity.CodeSendBatchQueryFailedError,
			entity.CodeAudienceSnapshotCreateFailedError,
			entity.CodeAudienceSnapshotUpdateFailedError,
			entity.CodeAudienceSnapshotDeleteFailedError,
			entity.CodeAudienceSnapshotQueryFailedError,
			entity.CodeSendRecordCreateFailedError,
			entity.CodeSendRecordUpdateFailedError,
			entity.CodeSendRecordDeleteFailedError,
			entity.CodeSendRecordQueryFailedError,
			entity.CodeSendScheduleError,
			entity.CodeSendExecutionError,
			entity.CodeSendThrottleError,
			entity.CodeSendRetryError:
			responsepkg.InternalError(c, sendErr)
		default:
			responsepkg.InternalError(c, sendErr)
		}
	} else {
		responsepkg.InternalError(c, err)
	}
}
