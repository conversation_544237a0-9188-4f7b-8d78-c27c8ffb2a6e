package handlers

import (
	"strconv"

	commonResponse "gitee.com/heiyee/platforms/pkg/common/response"

	"gitee.com/heiyee/platforms/email-system/internal/application/template/dto"
	"gitee.com/heiyee/platforms/email-system/internal/application/template/service"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"github.com/gin-gonic/gin"
)

// TemplateHandler 模板处理器
type TemplateHandler struct {
	logger                     logiface.Logger
	templateApplicationService *service.TemplateApplicationService
}

// NewTemplateHandler 创建模板处理器
func NewTemplateHandler(
	logger logiface.Logger,
	templateApplicationService *service.TemplateApplicationService,
) *TemplateHandler {
	return &TemplateHandler{
		logger:                     logger,
		templateApplicationService: templateApplicationService,
	}
}

// CreateTemplateLocale 创建模板多语言版本
func (h *TemplateHandler) CreateTemplateLocale(c *gin.Context) {
	var req dto.TemplateLocaleCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "create template locale request binding failed",
			logiface.Error(err),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	tenantID := getTenantIDFromContext(c)

	result, err := h.templateApplicationService.CreateTemplateLocale(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "create template locale failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int64("template_id", req.TemplateID))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "create template locale success",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", req.TemplateID),
		logiface.String("locale", req.Locale))

	commonResponse.Success(c, result)
}

// UpdateTemplateLocale 更新模板多语言版本
func (h *TemplateHandler) UpdateTemplateLocale(c *gin.Context) {
	templateLocaleIDStr := c.Query("template_locale_id")
	if templateLocaleIDStr == "" {
		h.logger.Warn(c.Request.Context(), "template_locale_id parameter is required")
		commonResponse.BadRequest(c, "template_locale_id is required")
		return
	}

	templateLocaleID, err := strconv.ParseInt(templateLocaleIDStr, 10, 64)
	if err != nil {
		h.logger.Warn(c.Request.Context(), "invalid template_locale_id",
			logiface.String("template_locale_id", templateLocaleIDStr),
			logiface.Error(err))
		commonResponse.BadRequest(c, "invalid template_locale_id")
		return
	}

	var req dto.TemplateLocaleUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "update template locale request binding failed",
			logiface.Error(err),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	tenantID := getTenantIDFromContext(c)

	result, err := h.templateApplicationService.UpdateTemplateLocale(c.Request.Context(), tenantID, templateLocaleID, &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "update template locale failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int64("template_locale_id", templateLocaleID))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "update template locale success",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_locale_id", templateLocaleID))

	commonResponse.Success(c, result)
}

// GetTemplateLocale 获取模板多语言版本
func (h *TemplateHandler) GetTemplateLocale(c *gin.Context) {
	templateLocaleIDStr := c.Query("template_locale_id")
	if templateLocaleIDStr == "" {
		h.logger.Warn(c.Request.Context(), "template_locale_id parameter is required")
		commonResponse.BadRequest(c, "template_locale_id is required")
		return
	}

	templateLocaleID, err := strconv.ParseInt(templateLocaleIDStr, 10, 64)
	if err != nil {
		h.logger.Warn(c.Request.Context(), "invalid template_locale_id",
			logiface.String("template_locale_id", templateLocaleIDStr),
			logiface.Error(err))
		commonResponse.BadRequest(c, "invalid template_locale_id")
		return
	}

	tenantID := getTenantIDFromContext(c)

	result, err := h.templateApplicationService.GetTemplateLocale(c.Request.Context(), tenantID, templateLocaleID)
	if err != nil {
		h.logger.Error(c.Request.Context(), "get template locale failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int64("template_locale_id", templateLocaleID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// ListTemplateLocales 获取模板多语言版本列表
func (h *TemplateHandler) ListTemplateLocales(c *gin.Context) {
	var req dto.TemplateLocaleListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "list template locales request binding failed",
			logiface.Error(err),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	tenantID := getTenantIDFromContext(c)

	result, err := h.templateApplicationService.ListTemplateLocales(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "list template locales failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// DeleteTemplateLocale 删除模板多语言版本
func (h *TemplateHandler) DeleteTemplateLocale(c *gin.Context) {
	templateIDStr := c.Query("template_id")
	if templateIDStr == "" {
		h.logger.Warn(c.Request.Context(), "template_id parameter is required")
		commonResponse.BadRequest(c, "template_id is required")
		return
	}

	templateID, err := strconv.ParseInt(templateIDStr, 10, 64)
	if err != nil {
		h.logger.Warn(c.Request.Context(), "invalid template_id",
			logiface.String("template_id", templateIDStr),
			logiface.Error(err))
		commonResponse.BadRequest(c, "invalid template_id")
		return
	}

	locale := c.Query("locale")
	if locale == "" {
		h.logger.Warn(c.Request.Context(), "locale parameter is required")
		commonResponse.BadRequest(c, "locale is required")
		return
	}

	tenantID := getTenantIDFromContext(c)

	err = h.templateApplicationService.DeleteTemplateLocale(c.Request.Context(), tenantID, templateID, locale)
	if err != nil {
		h.logger.Error(c.Request.Context(), "delete template locale failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int64("template_id", templateID),
			logiface.String("locale", locale))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "delete template locale success",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID),
		logiface.String("locale", locale))

	commonResponse.Success(c, gin.H{"message": "template locale deleted successfully"})
}

// UpdateTrackingOptions 更新追踪配置
func (h *TemplateHandler) UpdateTrackingOptions(c *gin.Context) {
	templateIDStr := c.Query("template_id")
	if templateIDStr == "" {
		h.logger.Warn(c.Request.Context(), "template_id parameter is required")
		commonResponse.BadRequest(c, "template_id is required")
		return
	}

	templateID, err := strconv.ParseInt(templateIDStr, 10, 64)
	if err != nil {
		h.logger.Warn(c.Request.Context(), "invalid template_id",
			logiface.String("template_id", templateIDStr),
			logiface.Error(err))
		commonResponse.BadRequest(c, "invalid template_id")
		return
	}

	locale := c.Query("locale")
	if locale == "" {
		locale = "en-US" // 默认语言
	}

	var req dto.UpdateTrackingOptionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "update tracking options request binding failed",
			logiface.Error(err),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	tenantID := getTenantIDFromContext(c)

	result, err := h.templateApplicationService.UpdateTrackingOptions(c.Request.Context(), tenantID, templateID, locale, &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "update tracking options failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int64("template_id", templateID),
			logiface.String("locale", locale))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "update tracking options success",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID),
		logiface.String("locale", locale))

	commonResponse.Success(c, result)
}

// GetTrackingConfiguration 获取追踪配置
func (h *TemplateHandler) GetTrackingConfiguration(c *gin.Context) {
	templateIDStr := c.Query("template_id")
	if templateIDStr == "" {
		h.logger.Warn(c.Request.Context(), "template_id parameter is required")
		commonResponse.BadRequest(c, "template_id is required")
		return
	}

	templateID, err := strconv.ParseInt(templateIDStr, 10, 64)
	if err != nil {
		h.logger.Warn(c.Request.Context(), "invalid template_id",
			logiface.String("template_id", templateIDStr),
			logiface.Error(err))
		commonResponse.BadRequest(c, "invalid template_id")
		return
	}

	locale := c.Query("locale")
	if locale == "" {
		locale = "en-US" // 默认语言
	}

	tenantID := getTenantIDFromContext(c)

	result, err := h.templateApplicationService.GetTrackingConfiguration(c.Request.Context(), tenantID, templateID, locale)
	if err != nil {
		h.logger.Error(c.Request.Context(), "get tracking configuration failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int64("template_id", templateID),
			logiface.String("locale", locale))
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// ResetTrackingToDefaults 重置追踪配置为默认值
func (h *TemplateHandler) ResetTrackingToDefaults(c *gin.Context) {
	templateIDStr := c.Query("template_id")
	if templateIDStr == "" {
		h.logger.Warn(c.Request.Context(), "template_id parameter is required")
		commonResponse.BadRequest(c, "template_id is required")
		return
	}

	templateID, err := strconv.ParseInt(templateIDStr, 10, 64)
	if err != nil {
		h.logger.Warn(c.Request.Context(), "invalid template_id",
			logiface.String("template_id", templateIDStr),
			logiface.Error(err))
		commonResponse.BadRequest(c, "invalid template_id")
		return
	}

	locale := c.Query("locale")
	if locale == "" {
		locale = "en-US" // 默认语言
	}

	tenantID := getTenantIDFromContext(c)

	result, err := h.templateApplicationService.ResetTrackingToDefaults(c.Request.Context(), tenantID, templateID, locale)
	if err != nil {
		h.logger.Error(c.Request.Context(), "reset tracking to defaults failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int64("template_id", templateID),
			logiface.String("locale", locale))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "reset tracking to defaults success",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("template_id", templateID),
		logiface.String("locale", locale))

	commonResponse.Success(c, result)
}

// BulkUpdateStatus 批量更新状态
func (h *TemplateHandler) BulkUpdateStatus(c *gin.Context) {
	var req dto.BulkUpdateStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "bulk update status request binding failed",
			logiface.Error(err),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	tenantID := getTenantIDFromContext(c)

	err := h.templateApplicationService.BulkUpdateStatus(c.Request.Context(), tenantID, &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "bulk update status failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Int("count", len(req.IDs)))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "bulk update status success",
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("count", len(req.IDs)),
		logiface.String("status", string(req.Status)))

	commonResponse.Success(c, gin.H{"message": "bulk status update completed successfully"})
}

// ValidateTrackingDomain 验证追踪域名
func (h *TemplateHandler) ValidateTrackingDomain(c *gin.Context) {
	var req dto.ValidateTrackingDomainRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "validate tracking domain request binding failed",
			logiface.Error(err),
			logiface.String("ip", c.ClientIP()))
		commonResponse.GinValidationError(c, err)
		return
	}

	result, err := h.templateApplicationService.ValidateTrackingDomain(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "validate tracking domain failed",
			logiface.Error(err),
			logiface.String("domain", req.Domain))
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Debug(c.Request.Context(), "validate tracking domain completed",
		logiface.String("domain", req.Domain),
		logiface.Bool("valid", result.Valid))

	commonResponse.Success(c, result)
}

// getTenantIDFromContext 从上下文获取租户ID
func getTenantIDFromContext(c *gin.Context) int64 {
	// 这里应该从JWT或中间件中获取租户ID
	// 暂时返回固定值，实际使用时需要实现
	if tenantID, exists := c.Get("tenant_id"); exists {
		if id, ok := tenantID.(int64); ok {
			return id
		}
	}
	return 1 // 默认租户ID，实际使用时应该抛出错误
}
