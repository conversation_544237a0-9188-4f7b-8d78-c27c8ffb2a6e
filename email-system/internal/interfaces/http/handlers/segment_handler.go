package handlers

import (
	"strconv"

	"gitee.com/heiyee/platforms/email-system/internal/application/segment/dto"
	"gitee.com/heiyee/platforms/email-system/internal/application/segment/service"
	responsepkg "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"github.com/gin-gonic/gin"
)

// SegmentHandler 人群圈选处理器
type SegmentHandler struct {
	logger         logiface.Logger
	segmentService *service.SegmentApplicationService
}

// NewSegmentHandler 创建人群圈选处理器
func NewSegmentHandler(
	logger logiface.Logger,
	segmentService *service.SegmentApplicationService,
) *SegmentHandler {
	return &SegmentHandler{
		logger:         logger,
		segmentService: segmentService,
	}
}

// Create 创建人群圈选
func (h *SegmentHandler) Create(c *gin.Context) {
	var req dto.CreateSegmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 创建人群圈选
	result, err := h.segmentService.CreateSegment(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleSegmentError(c, err)
		return
	}

	responsepkg.Created(c, result)
}

// Update 更新人群圈选
func (h *SegmentHandler) Update(c *gin.Context) {
	var req dto.UpdateSegmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 更新人群圈选
	result, err := h.segmentService.UpdateSegment(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleSegmentError(c, err)
		return
	}

	responsepkg.Updated(c, result)
}

// Get 获取人群圈选
func (h *SegmentHandler) Get(c *gin.Context) {
	// 从路径参数获取ID
	idStr := c.Param("id")
	if idStr == "" {
		responsepkg.Error(c, responsepkg.CodeValidationError, "人群圈选ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		responsepkg.Error(c, responsepkg.CodeValidationError, "无效的人群圈选ID")
		return
	}

	req := dto.GetSegmentRequest{ID: id}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 获取人群圈选
	result, err := h.segmentService.GetSegment(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleSegmentError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// List 获取人群圈选列表
func (h *SegmentHandler) List(c *gin.Context) {
	var req dto.ListSegmentsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}
	if req.Size > 1000 {
		req.Size = 1000
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 获取人群圈选列表
	result, err := h.segmentService.ListSegments(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleSegmentError(c, err)
		return
	}

	responsepkg.Paginated(c, result.Segments, result.Page, result.Size, result.Total)
}

// Delete 删除人群圈选
func (h *SegmentHandler) Delete(c *gin.Context) {
	// 从路径参数获取ID
	idStr := c.Param("id")
	if idStr == "" {
		responsepkg.Error(c, responsepkg.CodeValidationError, "人群圈选ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		responsepkg.Error(c, responsepkg.CodeValidationError, "无效的人群圈选ID")
		return
	}

	req := dto.DeleteSegmentRequest{ID: id}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 删除人群圈选
	if err := h.segmentService.DeleteSegment(c.Request.Context(), &req, tenantID); err != nil {
		HandleSegmentError(c, err)
		return
	}

	responsepkg.Deleted(c)
}

// Preview 预览人群圈选
func (h *SegmentHandler) Preview(c *gin.Context) {
	var req dto.PreviewSegmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 预览人群圈选
	result, err := h.segmentService.PreviewSegment(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleSegmentError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// Rebuild 重建人群圈选
func (h *SegmentHandler) Rebuild(c *gin.Context) {
	// 从路径参数获取ID
	idStr := c.Param("id")
	if idStr == "" {
		responsepkg.Error(c, responsepkg.CodeValidationError, "人群圈选ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		responsepkg.Error(c, responsepkg.CodeValidationError, "无效的人群圈选ID")
		return
	}

	req := dto.RebuildSegmentRequest{ID: id}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 重建人群圈选
	result, err := h.segmentService.RebuildSegment(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleSegmentError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// Export 导出人群圈选
func (h *SegmentHandler) Export(c *gin.Context) {
	// 从路径参数获取ID
	idStr := c.Param("id")
	if idStr == "" {
		responsepkg.Error(c, responsepkg.CodeValidationError, "人群圈选ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		responsepkg.Error(c, responsepkg.CodeValidationError, "无效的人群圈选ID")
		return
	}

	// 获取导出格式
	format := c.Query("format")
	if format == "" {
		format = "csv"
	}

	req := dto.ExportSegmentRequest{
		ID:     id,
		Format: format,
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 导出人群圈选
	exportPath, err := h.segmentService.ExportSegment(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleSegmentError(c, err)
		return
	}

	responsepkg.Success(c, gin.H{
		"export_path": exportPath,
		"format":      format,
	})
}

// GetJob 获取人群圈选任务
func (h *SegmentHandler) GetJob(c *gin.Context) {
	// 从路径参数获取任务ID
	idStr := c.Param("jobId")
	if idStr == "" {
		responsepkg.Error(c, responsepkg.CodeValidationError, "任务ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		responsepkg.Error(c, responsepkg.CodeValidationError, "无效的任务ID")
		return
	}

	req := dto.GetSegmentJobRequest{ID: id}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 获取人群圈选任务
	result, err := h.segmentService.GetSegmentJob(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleSegmentError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// CancelJob 取消人群圈选任务
func (h *SegmentHandler) CancelJob(c *gin.Context) {
	// 从路径参数获取任务ID
	idStr := c.Param("jobId")
	if idStr == "" {
		responsepkg.Error(c, responsepkg.CodeValidationError, "任务ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		responsepkg.Error(c, responsepkg.CodeValidationError, "无效的任务ID")
		return
	}

	req := dto.CancelSegmentJobRequest{ID: id}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 取消人群圈选任务
	if err := h.segmentService.CancelSegmentJob(c.Request.Context(), &req, tenantID); err != nil {
		HandleSegmentError(c, err)
		return
	}

	responsepkg.Success(c, nil)
}

// PreviewSegmentRule 预览人群圈选规则
func (h *SegmentHandler) PreviewSegmentRule(c *gin.Context) {
	var req dto.PreviewSegmentRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 预览人群圈选规则
	result, err := h.segmentService.PreviewSegmentRule(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleSegmentError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// ValidateSegmentRule 验证人群圈选规则
func (h *SegmentHandler) ValidateSegmentRule(c *gin.Context) {
	var req dto.ValidateSegmentRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 验证人群圈选规则
	result, err := h.segmentService.ValidateSegmentRule(c.Request.Context(), &req)
	if err != nil {
		HandleSegmentError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// RebuildSegment 重建人群圈选
func (h *SegmentHandler) RebuildSegment(c *gin.Context) {
	segmentID, err := parseIDParam(c, "id")
	if err != nil {
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 重建人群圈选
	if err := h.segmentService.RebuildSegmentByID(c.Request.Context(), segmentID, tenantID); err != nil {
		HandleSegmentError(c, err)
		return
	}

	responsepkg.Success(c, gin.H{
		"message": "人群圈选重建成功",
	})
}
