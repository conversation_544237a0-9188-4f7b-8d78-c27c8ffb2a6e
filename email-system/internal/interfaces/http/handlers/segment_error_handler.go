package handlers

import (
	"errors"

	"gitee.com/heiyee/platforms/email-system/internal/domain/segment/entity"
	responsepkg "gitee.com/heiyee/platforms/pkg/common/response"
	"github.com/gin-gonic/gin"
)

// HandleSegmentError 处理人群圈选相关错误
func HandleSegmentError(c *gin.Context, err error) {
	var segmentErr *entity.SegmentError
	if errors.As(err, &segmentErr) {
		switch segmentErr.Code {
		// 人群圈选验证错误
		case entity.CodeSegmentValidationError:
			if segmentErr.Field != "" {
				responsepkg.FieldError(c, segmentErr.Field, segmentErr.Message)
			} else {
				responsepkg.Error(c, responsepkg.CodeValidationError, segmentErr.Message)
			}
		case entity.CodeSegmentNameInvalid:
			responsepkg.FieldError(c, "name", segmentErr.Message)
		case entity.CodeSegmentNameExists:
			responsepkg.FieldError(c, "name", segmentErr.Message)
		case entity.CodeSegmentNotFound:
			responsepkg.NotFound(c, segmentErr.Message)
		case entity.CodeSegmentTypeInvalid:
			responsepkg.FieldError(c, "type", segmentErr.Message)
		case entity.CodeSegmentStatusInvalid:
			responsepkg.FieldError(c, "status", segmentErr.Message)
		case entity.CodeSegmentRuleInvalid:
			responsepkg.FieldError(c, "rule_json", segmentErr.Message)
		case entity.CodeSegmentPolicyInvalid:
			responsepkg.FieldError(c, "refresh_policy", segmentErr.Message)
		case entity.CodeSegmentTagInvalid:
			responsepkg.FieldError(c, "tag_id", segmentErr.Message)

		// 人群圈选操作错误
		case entity.CodeSegmentCreateFailed:
			responsepkg.InternalError(c, errors.New("创建人群圈选失败"))
		case entity.CodeSegmentUpdateFailed:
			responsepkg.InternalError(c, errors.New("更新人群圈选失败"))
		case entity.CodeSegmentDeleteFailed:
			responsepkg.InternalError(c, errors.New("删除人群圈选失败"))
		case entity.CodeSegmentQueryFailed:
			responsepkg.InternalError(c, errors.New("查询人群圈选失败"))
		case entity.CodeSegmentRebuildFailed:
			responsepkg.InternalError(c, errors.New("重建人群圈选失败"))
		case entity.CodeSegmentPreviewFailed:
			responsepkg.InternalError(c, errors.New("预览人群圈选失败"))
		case entity.CodeSegmentExportFailed:
			responsepkg.InternalError(c, errors.New("导出人群圈选失败"))

		// 人群圈选规则错误
		case entity.CodeSegmentRuleParseError:
			responsepkg.FieldError(c, "rule_json", segmentErr.Message)
		case entity.CodeSegmentRuleExecuteError:
			responsepkg.InternalError(c, errors.New("人群圈选规则执行失败"))
		case entity.CodeSegmentRuleValidateError:
			responsepkg.FieldError(c, "rule_json", segmentErr.Message)
		case entity.CodeSegmentRuleConditionError:
			responsepkg.FieldError(c, "rule_json", segmentErr.Message)
		case entity.CodeSegmentRuleOperatorError:
			responsepkg.FieldError(c, "rule_json", segmentErr.Message)
		case entity.CodeSegmentRuleFieldError:
			responsepkg.FieldError(c, "rule_json", segmentErr.Message)
		case entity.CodeSegmentRuleValueError:
			responsepkg.FieldError(c, "rule_json", segmentErr.Message)

		// 人群圈选任务错误
		case entity.CodeSegmentJobNotFound:
			responsepkg.NotFound(c, segmentErr.Message)
		case entity.CodeSegmentJobCreateFailed:
			responsepkg.InternalError(c, errors.New("创建人群圈选任务失败"))
		case entity.CodeSegmentJobUpdateFailed:
			responsepkg.InternalError(c, errors.New("更新人群圈选任务失败"))
		case entity.CodeSegmentJobCancelFailed:
			responsepkg.InternalError(c, errors.New("取消人群圈选任务失败"))
		case entity.CodeSegmentJobStatusError:
			responsepkg.Error(c, responsepkg.CodeValidationError, segmentErr.Message)
		case entity.CodeSegmentJobTimeoutError:
			responsepkg.InternalError(c, errors.New("人群圈选任务执行超时"))
		case entity.CodeSegmentJobResultError:
			responsepkg.InternalError(c, errors.New("人群圈选任务结果错误"))

		// 人群圈选快照错误
		case entity.CodeSegmentSnapshotNotFound:
			responsepkg.NotFound(c, segmentErr.Message)
		case entity.CodeSegmentSnapshotCreateFailed:
			responsepkg.InternalError(c, errors.New("创建人群圈选快照失败"))
		case entity.CodeSegmentSnapshotDeleteFailed:
			responsepkg.InternalError(c, errors.New("删除人群圈选快照失败"))
		case entity.CodeSegmentSnapshotExpired:
			responsepkg.Error(c, responsepkg.CodeValidationError, segmentErr.Message)
		case entity.CodeSegmentSnapshotInvalid:
			responsepkg.Error(c, responsepkg.CodeValidationError, segmentErr.Message)

		default:
			responsepkg.InternalError(c, errors.New("人群圈选操作失败"))
		}
		return
	}

	// 处理其他类型的错误
	responsepkg.InternalError(c, errors.New("系统内部错误"))
}
