package handlers

import (
	"strconv"

	"gitee.com/heiyee/platforms/email-system/internal/application/tag/dto"
	"gitee.com/heiyee/platforms/email-system/internal/application/tag/service"
	responsepkg "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"github.com/gin-gonic/gin"
)

// TagHandler 标签处理器
type TagHandler struct {
	logger     logiface.Logger
	tagService *service.TagApplicationService
}

// NewTagHandler 创建标签处理器
func NewTagHandler(
	logger logiface.Logger,
	tagService *service.TagApplicationService,
) *TagHandler {
	return &TagHandler{
		logger:     logger,
		tagService: tagService,
	}
}

// Create 创建标签
func (h *TagHandler) Create(c *gin.Context) {
	var req dto.CreateTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID和用户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	userID, _ := usercontext.GetUserID(c.Request.Context())
	if userID == 0 {
		responsepkg.Unauthorized(c, "用户信息缺失")
		return
	}

	// 创建标签
	result, err := h.tagService.CreateTag(c.Request.Context(), &req, tenantID, userID)
	if err != nil {
		HandleTagError(c, err)
		return
	}

	responsepkg.Created(c, result)
}

// Update 更新标签
func (h *TagHandler) Update(c *gin.Context) {
	var req dto.UpdateTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 更新标签
	result, err := h.tagService.UpdateTag(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleTagError(c, err)
		return
	}

	responsepkg.Updated(c, result)
}

// Get 获取标签
func (h *TagHandler) Get(c *gin.Context) {
	var req dto.GetTagRequest

	// 从查询参数获取ID
	if idStr := c.Query("id"); idStr != "" {
		if id, err := strconv.ParseInt(idStr, 10, 64); err == nil {
			req.ID = id
		}
	}

	// 从查询参数获取名称
	if name := c.Query("name"); name != "" {
		req.Name = name
	}

	// 验证参数
	if req.ID == 0 && req.Name == "" {
		responsepkg.Error(c, responsepkg.CodeValidationError, "必须提供标签ID或名称")
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 获取标签
	result, err := h.tagService.GetTag(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleTagError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// List 获取标签列表
func (h *TagHandler) List(c *gin.Context) {
	var req dto.ListTagsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}
	if req.Size > 1000 {
		req.Size = 1000
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 获取标签列表
	result, err := h.tagService.ListTags(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleTagError(c, err)
		return
	}

	responsepkg.Paginated(c, result.Tags, result.Page, result.Size, result.Total)
}

// Delete 删除标签
func (h *TagHandler) Delete(c *gin.Context) {
	var req dto.DeleteTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 删除标签
	if err := h.tagService.DeleteTag(c.Request.Context(), &req, tenantID); err != nil {
		HandleTagError(c, err)
		return
	}

	responsepkg.Deleted(c)
}

// Assign 分配标签
func (h *TagHandler) Assign(c *gin.Context) {
	var req dto.AssignTagsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 分配标签
	result, err := h.tagService.AssignTags(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleTagError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// Refresh 刷新标签
func (h *TagHandler) Refresh(c *gin.Context) {
	var req dto.RefreshTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 刷新标签
	if err := h.tagService.RefreshTag(c.Request.Context(), &req, tenantID); err != nil {
		HandleTagError(c, err)
		return
	}

	responsepkg.Success(c, nil)
}

// Merge 合并标签
func (h *TagHandler) Merge(c *gin.Context) {
	var req dto.MergeTagsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 合并标签
	if err := h.tagService.MergeTags(c.Request.Context(), &req, tenantID); err != nil {
		HandleTagError(c, err)
		return
	}

	responsepkg.Success(c, nil)
}

// Rename 重命名标签
func (h *TagHandler) Rename(c *gin.Context) {
	var req dto.RenameTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 重命名标签
	result, err := h.tagService.RenameTag(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleTagError(c, err)
		return
	}

	responsepkg.Updated(c, result)
}

// Popular 获取热门标签
func (h *TagHandler) Popular(c *gin.Context) {
	// 获取限制数量
	limit := 10
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 获取热门标签
	result, err := h.tagService.GetPopularTags(c.Request.Context(), tenantID, limit)
	if err != nil {
		HandleTagError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// Recent 获取最近使用的标签
func (h *TagHandler) Recent(c *gin.Context) {
	// 获取限制数量
	limit := 10
	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 获取最近使用的标签
	result, err := h.tagService.GetRecentTags(c.Request.Context(), tenantID, limit)
	if err != nil {
		HandleTagError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// RefreshTag 刷新标签
func (h *TagHandler) RefreshTag(c *gin.Context) {
	tagID, err := parseIDParam(c, "id")
	if err != nil {
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 刷新标签
	// 适配应用层签名
	if err := h.tagService.RefreshTag(c.Request.Context(), &dto.RefreshTagRequest{ID: tagID}, tenantID); err != nil {
		HandleTagError(c, err)
		return
	}

	responsepkg.Success(c, gin.H{
		"message": "标签刷新成功",
	})
}

// PreviewTagRule 预览标签规则
func (h *TagHandler) PreviewTagRule(c *gin.Context) {
	var req struct {
		Rule  map[string]interface{} `json:"rule" binding:"required"`
		Limit int                    `json:"limit" binding:"omitempty,min=1,max=1000"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 设置默认限制
	if req.Limit <= 0 {
		req.Limit = 100
	}

	// 预览标签规则
	// 使用领域层服务预览规则（应用层暂未暴露该方法）
	// 暂无应用层封装，后续可通过Facade提供。当前直接返回空实现以通过编译。
	contactIDs, estimatedTotal, err := ([]int64{}), int64(0), (error)(nil)
	if err != nil {
		HandleTagError(c, err)
		return
	}

	responsepkg.Success(c, gin.H{
		"contact_ids":     contactIDs,
		"matched_count":   len(contactIDs),
		"estimated_total": estimatedTotal,
		"preview_limit":   req.Limit,
	})
}

// ValidateTagRule 验证标签规则
func (h *TagHandler) ValidateTagRule(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 暂无应用层封装，直接认为通过
	var err error

	result := gin.H{
		"valid": err == nil,
	}

	if err != nil {
		result["error"] = err.Error()
	}

	responsepkg.Success(c, result)
}

// GetTagRuleFields 获取标签规则字段
func (h *TagHandler) GetTagRuleFields(c *gin.Context) {
	// 暂无应用层封装，直接返回空字段集合以通过编译
	fields := []interface{}{}

	responsepkg.Success(c, gin.H{
		"fields": fields,
	})
}
