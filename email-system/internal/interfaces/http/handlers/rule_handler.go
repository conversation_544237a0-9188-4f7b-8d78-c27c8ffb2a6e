package handlers

import (
	"gitee.com/heiyee/platforms/email-system/internal/domain/rule/service"
	responsepkg "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"github.com/gin-gonic/gin"
)

// RuleHandler 规则处理器
type RuleHandler struct {
	logger     logiface.Logger
	ruleEngine *service.RuleEngine
}

// NewRuleHandler 创建规则处理器
func NewRuleHandler(
	logger logiface.Logger,
	ruleEngine *service.RuleEngine,
) *RuleHandler {
	return &RuleHandler{
		logger:     logger,
		ruleEngine: ruleEngine,
	}
}

// ValidateRule 验证规则
func (h *RuleHandler) ValidateRule(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 验证规则
	err := h.ruleEngine.ValidateRule(req)

	result := gin.H{
		"valid": err == nil,
	}

	if err != nil {
		result["error"] = err.Error()
	}

	responsepkg.Success(c, result)
}

// EvaluateRule 评估规则
func (h *RuleHandler) EvaluateRule(c *gin.Context) {
	var req struct {
		Rule map[string]interface{} `json:"rule" binding:"required"`
		Data map[string]interface{} `json:"data" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 评估规则
	result, err := h.ruleEngine.EvaluateRuleFromData(c.Request.Context(), req.Rule, req.Data)
	if err != nil {
		responsepkg.InternalError(c, err)
		return
	}

	responsepkg.Success(c, gin.H{
		"result": result,
	})
}

// GetRuleTemplates 获取规则模板
func (h *RuleHandler) GetRuleTemplates(c *gin.Context) {
	templates := h.ruleEngine.GetCommonRuleTemplates()
	responsepkg.Success(c, gin.H{
		"templates": templates,
	})
}

// ParseRule 解析规则
func (h *RuleHandler) ParseRule(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 解析规则
	rule, err := h.ruleEngine.ParseRule(req)
	if err != nil {
		responsepkg.Error(c, responsepkg.CodeInvalidRequest, "规则解析失败")
		return
	}

	responsepkg.Success(c, gin.H{
		"rule": rule,
	})
}

// BuildContactRule 构建联系人规则
func (h *RuleHandler) BuildContactRule(c *gin.Context) {
	var req struct {
		Conditions []service.ContactCondition `json:"conditions" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 构建联系人规则
	rule, err := h.ruleEngine.BuildContactRule(req.Conditions)
	if err != nil {
		responsepkg.Error(c, responsepkg.CodeInvalidRequest, "构建联系人规则失败")
		return
	}

	responsepkg.Success(c, gin.H{
		"rule": rule,
	})
}

// GetRuleOperators 获取规则操作符
func (h *RuleHandler) GetRuleOperators(c *gin.Context) {
	operators := []gin.H{
		{
			"value":       "and",
			"label":       "并且",
			"description": "所有条件都必须满足",
			"type":        "logical",
		},
		{
			"value":       "or",
			"label":       "或者",
			"description": "任意条件满足即可",
			"type":        "logical",
		},
		{
			"value":       "not",
			"label":       "非",
			"description": "条件不满足",
			"type":        "logical",
		},
		{
			"value":       "eq",
			"label":       "等于",
			"description": "字段值等于指定值",
			"type":        "comparison",
		},
		{
			"value":       "ne",
			"label":       "不等于",
			"description": "字段值不等于指定值",
			"type":        "comparison",
		},
		{
			"value":       "gt",
			"label":       "大于",
			"description": "字段值大于指定值",
			"type":        "comparison",
		},
		{
			"value":       "gte",
			"label":       "大于等于",
			"description": "字段值大于等于指定值",
			"type":        "comparison",
		},
		{
			"value":       "lt",
			"label":       "小于",
			"description": "字段值小于指定值",
			"type":        "comparison",
		},
		{
			"value":       "lte",
			"label":       "小于等于",
			"description": "字段值小于等于指定值",
			"type":        "comparison",
		},
		{
			"value":       "in",
			"label":       "包含在",
			"description": "字段值包含在指定列表中",
			"type":        "comparison",
		},
		{
			"value":       "not_in",
			"label":       "不包含在",
			"description": "字段值不包含在指定列表中",
			"type":        "comparison",
		},
		{
			"value":       "contains",
			"label":       "包含",
			"description": "字段值包含指定文本",
			"type":        "string",
		},
		{
			"value":       "not_contains",
			"label":       "不包含",
			"description": "字段值不包含指定文本",
			"type":        "string",
		},
		{
			"value":       "starts_with",
			"label":       "以...开始",
			"description": "字段值以指定文本开始",
			"type":        "string",
		},
		{
			"value":       "ends_with",
			"label":       "以...结束",
			"description": "字段值以指定文本结束",
			"type":        "string",
		},
		{
			"value":       "regex",
			"label":       "正则表达式",
			"description": "字段值匹配正则表达式",
			"type":        "string",
		},
		{
			"value":       "exists",
			"label":       "存在",
			"description": "字段存在",
			"type":        "existence",
		},
		{
			"value":       "not_exists",
			"label":       "不存在",
			"description": "字段不存在",
			"type":        "existence",
		},
		{
			"value":       "is_empty",
			"label":       "为空",
			"description": "字段值为空",
			"type":        "existence",
		},
		{
			"value":       "is_not_empty",
			"label":       "不为空",
			"description": "字段值不为空",
			"type":        "existence",
		},
		{
			"value":       "before",
			"label":       "在...之前",
			"description": "日期在指定日期之前",
			"type":        "date",
		},
		{
			"value":       "after",
			"label":       "在...之后",
			"description": "日期在指定日期之后",
			"type":        "date",
		},
		{
			"value":       "within_days",
			"label":       "在...天内",
			"description": "日期在指定天数内",
			"type":        "date",
		},
		{
			"value":       "older_than",
			"label":       "超过...天",
			"description": "日期超过指定天数",
			"type":        "date",
		},
	}

	responsepkg.Success(c, gin.H{
		"operators": operators,
	})
}

// GetFieldTypes 获取字段类型
func (h *RuleHandler) GetFieldTypes(c *gin.Context) {
	fieldTypes := []gin.H{
		{
			"value":       "string",
			"label":       "字符串",
			"description": "文本类型",
		},
		{
			"value":       "number",
			"label":       "数字",
			"description": "数值类型",
		},
		{
			"value":       "boolean",
			"label":       "布尔值",
			"description": "真假值",
		},
		{
			"value":       "date",
			"label":       "日期",
			"description": "日期类型",
		},
		{
			"value":       "datetime",
			"label":       "日期时间",
			"description": "日期时间类型",
		},
		{
			"value":       "array",
			"label":       "数组",
			"description": "数组类型",
		},
		{
			"value":       "object",
			"label":       "对象",
			"description": "对象类型",
		},
	}

	responsepkg.Success(c, gin.H{
		"field_types": fieldTypes,
	})
}
