package handlers

import (
	"errors"

	"gitee.com/heiyee/platforms/email-system/internal/domain/contact/entity"
	responsepkg "gitee.com/heiyee/platforms/pkg/common/response"
	"github.com/gin-gonic/gin"
)

// HandleContactError 处理联系人相关错误
func HandleContactError(c *gin.Context, err error) {
	var contactErr *entity.ContactError
	if errors.As(err, &contactErr) {
		switch contactErr.Code {
		// 联系人验证错误
		case entity.CodeContactValidationError:
			if contactErr.Field != "" {
				responsepkg.FieldError(c, contactErr.Field, contactErr.Message)
			} else {
				responsepkg.Error(c, responsepkg.CodeValidationError, contactErr.Message)
			}
		case entity.CodeContactEmailInvalid:
			responsepkg.FieldError(c, "email", contactErr.Message)
		case entity.CodeContactEmailExists:
			responsepkg.FieldError(c, "email", contactErr.Message)
		case entity.CodeContactNotFound:
			responsepkg.NotFound(c, contactErr.Message)
		case entity.CodeContactStatusInvalid:
			responsepkg.FieldError(c, "status", contactErr.Message)
		case entity.CodeContactLanguageInvalid:
			responsepkg.FieldError(c, "preferred_language", contactErr.Message)
		case entity.CodeContactTimezoneInvalid:
			responsepkg.FieldError(c, "timezone", contactErr.Message)
		case entity.CodeContactAttributeError:
			if contactErr.Field != "" {
				responsepkg.FieldError(c, contactErr.Field, contactErr.Message)
			} else {
				// 使用统一错误码返回验证失败
				responsepkg.Error(c, responsepkg.CodeValidationError, contactErr.Message)
			}

			// 联系人操作错误
		case entity.CodeContactCreateFailed:
			responsepkg.InternalError(c, errors.New("创建联系人失败"))
		case entity.CodeContactUpdateFailed:
			responsepkg.InternalError(c, errors.New("更新联系人失败"))
		case entity.CodeContactDeleteFailed:
			responsepkg.InternalError(c, errors.New("删除联系人失败"))
		case entity.CodeContactQueryFailed:
			responsepkg.InternalError(c, errors.New("查询联系人失败"))

			// 联系人导入错误
		case entity.CodeContactImportFailed:
			responsepkg.InternalError(c, errors.New("导入联系人失败"))
		case entity.CodeContactImportFileInvalid:
			responsepkg.Error(c, responsepkg.CodeValidationError, contactErr.Message)
		case entity.CodeContactImportDataInvalid:
			responsepkg.Error(c, responsepkg.CodeValidationError, contactErr.Message)
		case entity.CodeContactImportJobNotFound:
			responsepkg.NotFound(c, contactErr.Message)

			// 联系人属性错误
		case entity.CodeContactAttributeNotFound:
			responsepkg.FieldError(c, contactErr.Field, contactErr.Message)
		case entity.CodeContactAttributeTypeInvalid:
			responsepkg.FieldError(c, contactErr.Field, contactErr.Message)
		case entity.CodeContactAttributeValueInvalid:
			responsepkg.FieldError(c, contactErr.Field, contactErr.Message)

		default:
			responsepkg.InternalError(c, errors.New("联系人操作失败"))
		}
		return
	}

	// 处理其他类型的错误
	responsepkg.InternalError(c, errors.New("系统内部错误"))
}
