package handlers

import (
	"strconv"

	"gitee.com/heiyee/platforms/email-system/internal/application/contact/dto"
	"gitee.com/heiyee/platforms/email-system/internal/application/contact/service"
	responsepkg "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"github.com/gin-gonic/gin"
)

// ContactHandler 联系人处理器
type ContactHandler struct {
	logger         logiface.Logger
	contactService *service.ContactApplicationService
}

// NewContactHandler 创建联系人处理器
func NewContactHandler(
	logger logiface.Logger,
	contactService *service.ContactApplicationService,
) *ContactHandler {
	return &ContactHandler{
		logger:         logger,
		contactService: contactService,
	}
}

// Create 创建联系人
func (h *ContactHandler) Create(c *gin.Context) {
	var req dto.CreateContactRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 创建联系人
	result, err := h.contactService.CreateContact(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleContactError(c, err)
		return
	}

	responsepkg.Created(c, result)
}

// Update 更新联系人
func (h *ContactHandler) Update(c *gin.Context) {
	var req dto.UpdateContactRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 更新联系人
	result, err := h.contactService.UpdateContact(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleContactError(c, err)
		return
	}

	responsepkg.Updated(c, result)
}

// Get 获取联系人
func (h *ContactHandler) Get(c *gin.Context) {
	var req dto.GetContactRequest

	// 从查询参数获取ID
	if idStr := c.Query("id"); idStr != "" {
		if id, err := strconv.ParseInt(idStr, 10, 64); err == nil {
			req.ID = id
		}
	}

	// 从查询参数获取邮箱
	if email := c.Query("email"); email != "" {
		req.Email = email
	}

	// 从查询参数获取字段列表
	if fields := c.Query("fields"); fields != "" {
		req.Fields = fields
	}

	// 验证参数
	if req.ID == 0 && req.Email == "" {
		responsepkg.Error(c, responsepkg.CodeValidationError, "必须提供联系人ID或邮箱地址")
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 获取联系人
	result, err := h.contactService.GetContact(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleContactError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// Search 搜索联系人
func (h *ContactHandler) Search(c *gin.Context) {
	var req dto.SearchContactsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}
	if req.Size > 1000 {
		req.Size = 1000
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 搜索联系人
	result, err := h.contactService.SearchContacts(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleContactError(c, err)
		return
	}

	responsepkg.Paginated(c, result.Contacts, result.Page, result.Size, result.Total)
}

// Delete 删除联系人
func (h *ContactHandler) Delete(c *gin.Context) {
	var req dto.DeleteContactRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 删除联系人
	if err := h.contactService.DeleteContact(c.Request.Context(), &req, tenantID); err != nil {
		HandleContactError(c, err)
		return
	}

	responsepkg.Deleted(c)
}

// BatchCreate 批量创建联系人
func (h *ContactHandler) BatchCreate(c *gin.Context) {
	var req dto.BatchCreateContactsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 批量创建联系人
	result, err := h.contactService.BatchCreateContacts(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleContactError(c, err)
		return
	}

	responsepkg.Created(c, result)
}

// BatchUpdate 批量更新联系人
func (h *ContactHandler) BatchUpdate(c *gin.Context) {
	var req dto.BatchUpdateContactsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 批量更新联系人
	result, err := h.contactService.BatchUpdateContacts(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleContactError(c, err)
		return
	}

	responsepkg.Updated(c, result)
}

// UpdateStatus 更新联系人状态
func (h *ContactHandler) UpdateStatus(c *gin.Context) {
	var req dto.UpdateContactStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 更新联系人状态
	if err := h.contactService.UpdateContactStatus(c.Request.Context(), &req, tenantID); err != nil {
		HandleContactError(c, err)
		return
	}

	responsepkg.Updated(c, nil)
}
