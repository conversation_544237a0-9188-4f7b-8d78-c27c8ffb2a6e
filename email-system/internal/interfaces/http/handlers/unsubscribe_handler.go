package handlers

import (
	"net/http"

	"gitee.com/heiyee/platforms/email-system/internal/application/contact/dto"
	"gitee.com/heiyee/platforms/email-system/internal/application/contact/service"
	responsepkg "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"github.com/gin-gonic/gin"
)

// UnsubscribeHandler 退订处理器
type UnsubscribeHandler struct {
	logger         logiface.Logger
	contactService *service.ContactApplicationService
	baseURL        string
}

// NewUnsubscribeHandler 创建退订处理器
func NewUnsubscribeHandler(
	logger logiface.Logger,
	contactService *service.ContactApplicationService,
	baseURL string,
) *UnsubscribeHandler {
	return &UnsubscribeHandler{
		logger:         logger,
		contactService: contactService,
		baseURL:        baseURL,
	}
}

// ShowUnsubscribePage 显示退订页面
func (h *UnsubscribeHandler) ShowUnsubscribePage(c *gin.Context) {
	token := c.Query("token")
	if token == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "无效的退订链接",
		})
		return
	}

	// 获取租户ID（可以从域名或其他方式获取）
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		tenantID = 1 // 默认租户，实际应用中应该有更好的处理方式
	}

	// 获取退订页面数据
	pageData, err := h.contactService.GetUnsubscribePageData(c.Request.Context(), token, tenantID)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to get unsubscribe page data",
			logiface.Error(err),
			logiface.String("token", token))
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "系统错误，请稍后重试",
		})
		return
	}

	if !pageData.ContactExists {
		c.HTML(http.StatusNotFound, "error.html", gin.H{
			"error": "无效的退订链接或链接已过期",
		})
		return
	}

	// 渲染退订页面
	c.HTML(http.StatusOK, "unsubscribe.html", gin.H{
		"token":                pageData.Token,
		"email":                pageData.Email,
		"already_unsubscribed": pageData.AlreadyUnsubscribed,
		"preferred_language":   pageData.PreferredLanguage,
	})
}

// ProcessUnsubscribe 处理退订请求
func (h *UnsubscribeHandler) ProcessUnsubscribe(c *gin.Context) {
	var req dto.UnsubscribeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		tenantID = 1 // 默认租户
	}

	// 处理退订
	result, err := h.contactService.Unsubscribe(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleContactError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// GrantConsent 授予同意
func (h *UnsubscribeHandler) GrantConsent(c *gin.Context) {
	var req dto.ConsentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取客户端IP和User-Agent
	if req.IP == "" {
		req.IP = c.ClientIP()
	}
	if req.UserAgent == "" {
		req.UserAgent = c.GetHeader("User-Agent")
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 授予同意
	result, err := h.contactService.GrantConsent(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleContactError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// WithdrawConsent 撤回同意
func (h *UnsubscribeHandler) WithdrawConsent(c *gin.Context) {
	var req dto.WithdrawConsentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 撤回同意
	result, err := h.contactService.WithdrawConsent(c.Request.Context(), &req, tenantID)
	if err != nil {
		HandleContactError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// GenerateUnsubscribeLink 生成退订链接
func (h *UnsubscribeHandler) GenerateUnsubscribeLink(c *gin.Context) {
	var req dto.GenerateUnsubscribeLinkRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 生成退订链接
	result, err := h.contactService.GenerateUnsubscribeLink(c.Request.Context(), &req, tenantID, h.baseURL)
	if err != nil {
		HandleContactError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// GetConsentStatus 获取同意状态
func (h *UnsubscribeHandler) GetConsentStatus(c *gin.Context) {
	email := c.Query("email")
	if email == "" {
		responsepkg.Error(c, responsepkg.CodeValidationError, "邮箱地址不能为空")
		return
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Unauthorized(c, "租户信息缺失")
		return
	}

	// 获取同意状态
	result, err := h.contactService.GetConsentStatus(c.Request.Context(), email, tenantID)
	if err != nil {
		HandleContactError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// ShowConsentPage 显示同意页面
func (h *UnsubscribeHandler) ShowConsentPage(c *gin.Context) {
	email := c.Query("email")
	source := c.Query("source")

	// 渲染同意页面
	c.HTML(http.StatusOK, "consent.html", gin.H{
		"email":  email,
		"source": source,
	})
}

// ProcessConsentForm 处理同意表单提交
func (h *UnsubscribeHandler) ProcessConsentForm(c *gin.Context) {
	email := c.PostForm("email")
	source := c.PostForm("source")

	if email == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "邮箱地址不能为空",
		})
		return
	}

	req := dto.ConsentRequest{
		Email:     email,
		Source:    source,
		IP:        c.ClientIP(),
		UserAgent: c.GetHeader("User-Agent"),
	}

	// 获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		tenantID = 1 // 默认租户
	}

	// 授予同意
	result, err := h.contactService.GrantConsent(c.Request.Context(), &req, tenantID)
	if err != nil {
		h.logger.Error(c.Request.Context(), "Failed to grant consent",
			logiface.Error(err),
			logiface.String("email", email))
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "系统错误，请稍后重试",
		})
		return
	}

	// 渲染成功页面
	c.HTML(http.StatusOK, "consent_success.html", gin.H{
		"email":   email,
		"success": result.Success,
		"message": result.Message,
	})
}
