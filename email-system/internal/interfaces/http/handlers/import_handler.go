package handlers

import (
	"gitee.com/heiyee/platforms/email-system/internal/application/import/service"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/entity"
	responsepkg "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/usercontext"

	"github.com/gin-gonic/gin"
)

// ImportHandler 导入处理器
type ImportHandler struct {
	importService service.ImportApplicationService
}

// NewImportHandler 创建导入处理器实例
func NewImportHandler(importService service.ImportApplicationService) *ImportHandler {
	return &ImportHandler{
		importService: importService,
	}
}

// CreateImportJobRequest 创建导入任务请求
type CreateImportJobRequest struct {
	BusinessScenario string                 `json:"business_scenario" binding:"required"`
	FileName         string                 `json:"file_name" binding:"required"`
	FileURL          string                 `json:"file_url" binding:"required"`
	FileType         string                 `json:"file_type" binding:"required"`
	FileSize         int64                  `json:"file_size"`
	MappingConfig    map[string]interface{} `json:"mapping_config" binding:"required"`
	ValidationRules  map[string]interface{} `json:"validation_rules,omitempty"`
	ScenarioConfig   map[string]interface{} `json:"scenario_config,omitempty"`
	TargetEntityType *string                `json:"target_entity_type,omitempty"`
	TargetEntityID   *int64                 `json:"target_entity_id,omitempty"`
	BusinessMetadata map[string]interface{} `json:"business_metadata,omitempty"`
}

// ListImportJobsRequest 列表查询请求
type ListImportJobsRequest struct {
	Status           string `form:"status"`
	BusinessScenario string `form:"business_scenario"`
	Limit            int    `form:"limit,default=20"`
	Offset           int    `form:"offset,default=0"`
}

// GetImportJobRequest 查询单个任务请求
type GetImportJobRequest struct {
	JobID int64 `form:"job_id" binding:"required"`
}

// ProcessImportJobRequest 处理任务请求
type ProcessImportJobRequest struct {
	JobID int64 `json:"job_id" binding:"required"`
}

// CancelImportJobRequest 取消任务请求
type CancelImportJobRequest struct {
	JobID int64 `json:"job_id" binding:"required"`
}

// RetryImportJobRequest 重试任务请求
type RetryImportJobRequest struct {
	JobID int64 `json:"job_id" binding:"required"`
}

// DeleteImportJobRequest 删除任务请求
type DeleteImportJobRequest struct {
	JobID int64 `json:"job_id" binding:"required"`
}

// GetImportErrorsRequest 查询错误请求
type GetImportErrorsRequest struct {
	JobID   int64 `form:"job_id" binding:"required"`
	BatchNo *int  `form:"batch_no"`
	Limit   int   `form:"limit,default=20"`
	Offset  int   `form:"offset,default=0"`
}

// GetImportBatchesRequest 查询批次请求
type GetImportBatchesRequest struct {
	JobID int64 `form:"job_id" binding:"required"`
}

// CreateImportJob 创建导入任务
// @Summary 创建导入任务
// @Description 创建新的导入任务
// @Tags Import
// @Accept json
// @Produce json
// @Param request body CreateImportJobRequest true "创建导入任务请求"
// @Success 200 {object} responsepkg.Response{data=service.ImportJobResponse}
// @Failure 400 {object} responsepkg.Response
// @Failure 500 {object} responsepkg.Response
// @Router /api/import/create [post]
func (h *ImportHandler) CreateImportJob(c *gin.Context) {
	var req CreateImportJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID和用户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Error(c, entity.CodeImportPermissionDenied, "租户信息缺失")
		return
	}

	userID, _ := usercontext.GetUserID(c.Request.Context())
	if userID == 0 {
		responsepkg.Error(c, entity.CodeImportUserUnauthorized, "用户信息缺失")
		return
	}

	// 验证业务场景
	businessScenario := entity.BusinessScenario(req.BusinessScenario)
	if !businessScenario.IsValid() {
		responsepkg.Error(c, entity.CodeImportBusinessScenarioInvalid, "不支持的业务场景")
		return
	}

	// 验证文件类型
	fileType := entity.FileType(req.FileType)
	if !fileType.IsValid() {
		responsepkg.Error(c, entity.CodeImportFileTypeInvalid, "不支持的文件类型")
		return
	}

	// 构建服务请求
	serviceReq := &service.CreateImportJobRequest{
		TenantID:         tenantID,
		BusinessScenario: businessScenario,
		FileName:         req.FileName,
		FileURL:          req.FileURL,
		FileType:         fileType,
		FileSize:         req.FileSize,
		MappingConfig:    req.MappingConfig,
		ValidationRules:  req.ValidationRules,
		ScenarioConfig:   req.ScenarioConfig,
		TargetEntityType: req.TargetEntityType,
		TargetEntityID:   req.TargetEntityID,
		BusinessMetadata: req.BusinessMetadata,
		CreatedBy:        userID,
	}

	// 调用服务
	result, err := h.importService.CreateImportJob(c.Request.Context(), serviceReq)
	if err != nil {
		HandleImportError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// GetImportJobStatus 获取导入任务状态
// @Summary 获取导入任务状态
// @Description 根据任务ID获取导入任务状态
// @Tags Import
// @Produce json
// @Param job_id query int true "任务ID"
// @Success 200 {object} responsepkg.Response{data=service.ImportJobStatusResponse}
// @Failure 400 {object} responsepkg.Response
// @Failure 404 {object} responsepkg.Response
// @Failure 500 {object} responsepkg.Response
// @Router /api/import/job-status [get]
func (h *ImportHandler) GetImportJobStatus(c *gin.Context) {
	var req GetImportJobRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Error(c, entity.CodeImportPermissionDenied, "租户信息缺失")
		return
	}

	// 调用服务
	result, err := h.importService.GetImportJobStatus(c.Request.Context(), req.JobID, tenantID)
	if err != nil {
		HandleImportError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// ListImportJobs 获取导入任务列表
// @Summary 获取导入任务列表
// @Description 分页获取导入任务列表
// @Tags Import
// @Produce json
// @Param status query string false "任务状态"
// @Param business_scenario query string false "业务场景"
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {object} responsepkg.Response{data=service.ListImportJobsResponse}
// @Failure 400 {object} responsepkg.Response
// @Failure 500 {object} responsepkg.Response
// @Router /api/import/job-list [get]
func (h *ImportHandler) ListImportJobs(c *gin.Context) {
	var req ListImportJobsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Error(c, entity.CodeImportPermissionDenied, "租户信息缺失")
		return
	}

	// 构建服务请求
	serviceReq := &service.ListImportJobsRequest{
		TenantID: tenantID,
		Limit:    req.Limit,
		Offset:   req.Offset,
	}

	if req.Status != "" {
		serviceReq.Status = entity.ImportJobStatus(req.Status)
	}

	if req.BusinessScenario != "" {
		serviceReq.BusinessScenario = entity.BusinessScenario(req.BusinessScenario)
	}

	// 调用服务
	result, err := h.importService.ListImportJobs(c.Request.Context(), serviceReq)
	if err != nil {
		HandleImportError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// CancelImportJob 取消导入任务
// @Summary 取消导入任务
// @Description 取消指定的导入任务
// @Tags Import
// @Accept json
// @Produce json
// @Param request body CancelImportJobRequest true "取消导入任务请求"
// @Success 200 {object} responsepkg.Response
// @Failure 400 {object} responsepkg.Response
// @Failure 404 {object} responsepkg.Response
// @Failure 500 {object} responsepkg.Response
// @Router /api/import/cancel-job [post]
func (h *ImportHandler) CancelImportJob(c *gin.Context) {
	var req CancelImportJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Error(c, entity.CodeImportPermissionDenied, "租户信息缺失")
		return
	}

	// 调用服务
	err := h.importService.CancelImportJob(c.Request.Context(), req.JobID, tenantID)
	if err != nil {
		HandleImportError(c, err)
		return
	}

	responsepkg.Success(c, gin.H{"message": "任务取消成功"})
}

// RetryImportJob 重试导入任务
// @Summary 重试导入任务
// @Description 重试失败的导入任务
// @Tags Import
// @Accept json
// @Produce json
// @Param request body RetryImportJobRequest true "重试导入任务请求"
// @Success 200 {object} responsepkg.Response
// @Failure 400 {object} responsepkg.Response
// @Failure 404 {object} responsepkg.Response
// @Failure 500 {object} responsepkg.Response
// @Router /api/import/retry-job [post]
func (h *ImportHandler) RetryImportJob(c *gin.Context) {
	var req RetryImportJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Error(c, entity.CodeImportPermissionDenied, "租户信息缺失")
		return
	}

	// 调用服务
	err := h.importService.RetryImportJob(c.Request.Context(), req.JobID, tenantID)
	if err != nil {
		HandleImportError(c, err)
		return
	}

	responsepkg.Success(c, gin.H{"message": "任务重试成功"})
}

// DeleteImportJob 删除导入任务
// @Summary 删除导入任务
// @Description 删除指定的导入任务及相关数据
// @Tags Import
// @Accept json
// @Produce json
// @Param request body DeleteImportJobRequest true "删除导入任务请求"
// @Success 200 {object} responsepkg.Response
// @Failure 400 {object} responsepkg.Response
// @Failure 404 {object} responsepkg.Response
// @Failure 500 {object} responsepkg.Response
// @Router /api/import/delete-job [post]
func (h *ImportHandler) DeleteImportJob(c *gin.Context) {
	var req DeleteImportJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Error(c, entity.CodeImportPermissionDenied, "租户信息缺失")
		return
	}

	// 调用服务
	err := h.importService.DeleteImportJob(c.Request.Context(), req.JobID, tenantID)
	if err != nil {
		HandleImportError(c, err)
		return
	}

	responsepkg.Success(c, gin.H{"message": "任务删除成功"})
}

// ProcessImportJob 处理导入任务
// @Summary 处理导入任务
// @Description 开始处理排队中的导入任务
// @Tags Import
// @Accept json
// @Produce json
// @Param request body ProcessImportJobRequest true "处理导入任务请求"
// @Success 200 {object} responsepkg.Response
// @Failure 400 {object} responsepkg.Response
// @Failure 404 {object} responsepkg.Response
// @Failure 500 {object} responsepkg.Response
// @Router /api/import/process-job [post]
func (h *ImportHandler) ProcessImportJob(c *gin.Context) {
	var req ProcessImportJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Error(c, entity.CodeImportPermissionDenied, "租户信息缺失")
		return
	}

	// 调用服务（异步处理）
	go func() {
		if err := h.importService.ProcessImportJob(c.Request.Context(), req.JobID, tenantID); err != nil {
			// 这里应该记录到日志系统，简化处理
			// logger.Error("异步处理导入任务失败", "job_id", req.JobID, "error", err)
		}
	}()

	responsepkg.Success(c, gin.H{
		"message": "导入任务已开始处理",
		"job_id":  req.JobID,
	})
}

// GetImportErrors 获取导入错误列表
// @Summary 获取导入错误列表
// @Description 根据任务ID获取导入错误列表
// @Tags Import
// @Produce json
// @Param job_id query int true "任务ID"
// @Param batch_no query int false "批次号"
// @Param limit query int false "每页数量" default(20)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {object} responsepkg.Response{data=service.GetImportErrorsResponse}
// @Failure 400 {object} responsepkg.Response
// @Failure 500 {object} responsepkg.Response
// @Router /api/import/job-errors [get]
func (h *ImportHandler) GetImportErrors(c *gin.Context) {
	var req GetImportErrorsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Error(c, entity.CodeImportPermissionDenied, "租户信息缺失")
		return
	}

	// 构建服务请求
	serviceReq := &service.GetImportErrorsRequest{
		JobID:    req.JobID,
		TenantID: tenantID,
		BatchNo:  req.BatchNo,
		Limit:    req.Limit,
		Offset:   req.Offset,
	}

	// 调用服务
	result, err := h.importService.GetImportErrors(c.Request.Context(), serviceReq)
	if err != nil {
		HandleImportError(c, err)
		return
	}

	responsepkg.Success(c, result)
}

// GetImportBatches 获取导入批次列表
// @Summary 获取导入批次列表
// @Description 根据任务ID获取导入批次列表
// @Tags Import
// @Produce json
// @Param job_id query int true "任务ID"
// @Success 200 {object} responsepkg.Response{data=[]service.ImportBatchResponse}
// @Failure 400 {object} responsepkg.Response
// @Failure 500 {object} responsepkg.Response
// @Router /api/import/job-batches [get]
func (h *ImportHandler) GetImportBatches(c *gin.Context) {
	var req GetImportBatchesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		responsepkg.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantID, _ := usercontext.GetTenantID(c.Request.Context())
	if tenantID == 0 {
		responsepkg.Error(c, entity.CodeImportPermissionDenied, "租户信息缺失")
		return
	}

	// 调用服务
	result, err := h.importService.GetImportBatches(c.Request.Context(), req.JobID, tenantID)
	if err != nil {
		HandleImportError(c, err)
		return
	}

	responsepkg.Success(c, result)
}
