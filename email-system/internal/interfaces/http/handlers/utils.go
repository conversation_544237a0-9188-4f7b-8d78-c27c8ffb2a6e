package handlers

import (
	"strconv"

	responsepkg "gitee.com/heiyee/platforms/pkg/common/response"
	"github.com/gin-gonic/gin"
)

// parseIDParam 从路由参数解析 int64 ID，统一错误响应
func parseIDParam(c *gin.Context, name string) (int64, error) {
	idStr := c.Param(name)
	if idStr == "" {
		responsepkg.Error(c, responsepkg.CodeValidationError, name+" 不能为空")
		return 0, ErrBadParam
	}
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || id <= 0 {
		responsepkg.Error(c, responsepkg.CodeValidationError, "无效的 "+name)
		return 0, ErrBadParam
	}
	return id, nil
}

// ErrBadParam 是一个轻量错误用于中断handler流程
var ErrBadParam = &badParamError{}

type badParamError struct{}

func (e *badParamError) Error() string { return "bad param" }
