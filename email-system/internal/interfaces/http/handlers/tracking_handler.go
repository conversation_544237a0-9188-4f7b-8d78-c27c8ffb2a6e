package handlers

import (
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"gitee.com/heiyee/platforms/email-system/internal/application/tracking/service"
	"gitee.com/heiyee/platforms/email-system/internal/domain/tracking/entity"
	trackingService "gitee.com/heiyee/platforms/email-system/internal/domain/tracking/service"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"github.com/gin-gonic/gin"
)

// TrackingHandler 追踪处理器
type TrackingHandler struct {
	trackingService    *service.TrackingApplicationService
	verifier           SignatureVerifier
	classifier         *trackingService.EventClassifier
	rateLimiter        RateLimiter
	geoLocationService GeoLocationService
}

// SignatureVerifier 签名验证器接口
type SignatureVerifier interface {
	VerifySignature(params TrackingParams, tenantID int64) bool
	VerifyAndDecryptDestination(encryptedDest string, tenantID int64) (string, error)
	IsAllowedDomain(destURL string, tenantID int64) bool
}

// RateLimiter 限流器接口
type RateLimiter interface {
	Allow(key string) bool
	AllowWithBurst(key string, burst int) bool
}

// GeoLocationService 地理位置服务接口
type GeoLocationService interface {
	GetLocation(ipAddress string) (*entity.GeoLocation, error)
}

// TrackingParams 追踪参数
type TrackingParams struct {
	CID   string `form:"cid" json:"cid"`     // Campaign ID
	SID   string `form:"sid" json:"sid"`     // Subscriber ID
	MID   string `form:"mid" json:"mid"`     // Message ID
	LID   string `form:"lid" json:"lid"`     // Link ID (for clicks)
	TS    int64  `form:"ts" json:"ts"`       // Timestamp
	Nonce string `form:"nonce" json:"nonce"` // Random nonce
	Sig   string `form:"sig" json:"sig"`     // HMAC signature
	Dest  string `form:"dest" json:"dest"`   // Encrypted destination (for clicks)
}

// NewTrackingHandler 创建追踪处理器 (简化版)
func NewTrackingHandler(
	logger logiface.Logger,
	trackingService *service.TrackingApplicationService,
) *TrackingHandler {
	return &TrackingHandler{
		trackingService: trackingService,
		// TODO: 初始化其他依赖
	}
}

// HandleOpen 处理打开事件
func (h *TrackingHandler) HandleOpen(c *gin.Context) {
	h.handlePixelEvent(c, entity.EventTypeOpen)
}

// HandleBeacon 处理Beacon事件
func (h *TrackingHandler) HandleBeacon(c *gin.Context) {
	h.handlePixelEvent(c, entity.EventTypeBeacon)
}

// HandleAMPOpen 处理AMP打开事件
func (h *TrackingHandler) HandleAMPOpen(c *gin.Context) {
	h.handlePixelEvent(c, entity.EventTypeAMPOpen)
}

// handlePixelEvent 处理像素事件的通用方法
func (h *TrackingHandler) handlePixelEvent(c *gin.Context, eventType entity.EventType) {
	// 1. 设置反缓存头部
	c.Header("Cache-Control", "no-store, max-age=0")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")

	// 2. 参数解析
	params, err := h.parseTrackingParams(c)
	if err != nil {
		h.returnPixelResponse(c)
		return
	}

	// 3. 提取租户ID（从域名或其他方式）
	tenantID, err := h.extractTenantID(c)
	if err != nil {
		h.returnPixelResponse(c)
		return
	}

	// 4. 签名验证
	if !h.verifier.VerifySignature(params, tenantID) {
		h.logSecurityEvent("invalid_signature", params, c)
		h.returnPixelResponse(c)
		return
	}

	// 5. 速率限制
	rateLimitKey := fmt.Sprintf("tracking:%d:%s", tenantID, c.ClientIP())
	if !h.rateLimiter.AllowWithBurst(rateLimitKey, 100) {
		h.returnPixelResponse(c)
		return
	}

	// 6. 构建追踪事件
	event := h.buildTrackingEvent(eventType, tenantID, params, c)

	// 7. 事件分类（仅对打开事件）
	if event.IsOpenEvent() {
		event.OpenClass = h.classifier.ClassifyOpenEvent(event)
	}

	// 8. 异步处理事件
	go func() {
		if err := h.trackingService.ProcessTrackingEvent(c.Request.Context(), event); err != nil {
			h.logError("failed_to_process_event", err, event)
		}
	}()

	// 9. 返回像素响应
	h.returnPixelResponse(c)
}

// HandleClick 处理点击事件
func (h *TrackingHandler) HandleClick(c *gin.Context) {
	// 1. 设置反缓存头部
	c.Header("Cache-Control", "no-store, max-age=0")

	// 2. 参数解析
	params, err := h.parseClickParams(c)
	if err != nil {
		c.Redirect(http.StatusTemporaryRedirect, "about:blank")
		return
	}

	// 3. 提取租户ID
	tenantID, err := h.extractTenantID(c)
	if err != nil {
		c.Redirect(http.StatusTemporaryRedirect, "about:blank")
		return
	}

	// 4. 签名验证与目标URL解密
	destURL, err := h.verifier.VerifyAndDecryptDestination(params.Dest, tenantID)
	if err != nil {
		h.logSecurityEvent("invalid_destination", params, c)
		c.Redirect(http.StatusTemporaryRedirect, "about:blank")
		return
	}

	// 5. 域名白名单检查
	if !h.verifier.IsAllowedDomain(destURL, tenantID) {
		h.logSecurityEvent("blocked_domain", params, c)
		c.Redirect(http.StatusTemporaryRedirect, "about:blank")
		return
	}

	// 6. 速率限制
	rateLimitKey := fmt.Sprintf("click:%d:%s", tenantID, c.ClientIP())
	if !h.rateLimiter.Allow(rateLimitKey) {
		c.Redirect(http.StatusFound, destURL)
		return
	}

	// 7. 构建点击事件
	event := h.buildClickEvent(tenantID, params, destURL, c)

	// 8. 异步处理事件
	go func() {
		if err := h.trackingService.ProcessTrackingEvent(c.Request.Context(), event); err != nil {
			h.logError("failed_to_process_click", err, event)
		}
	}()

	// 9. 重定向到目标URL
	c.Redirect(http.StatusFound, destURL)
}

// HandleConversion 处理转化事件
func (h *TrackingHandler) HandleConversion(c *gin.Context) {
	var conversionData ConversionData
	if err := c.ShouldBindJSON(&conversionData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400001,
			"message": "Invalid conversion data",
		})
		return
	}

	// 1. 提取租户ID
	tenantID, err := h.extractTenantID(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400002,
			"message": "Invalid tenant",
		})
		return
	}

	// 2. 签名验证
	params := TrackingParams{
		CID:   conversionData.CID,
		SID:   conversionData.SID,
		MID:   conversionData.MID,
		TS:    conversionData.TS,
		Nonce: conversionData.Nonce,
		Sig:   conversionData.Sig,
	}

	if !h.verifier.VerifySignature(params, tenantID) {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400003,
			"message": "Invalid signature",
		})
		return
	}

	// 3. 构建转化事件
	event := h.buildConversionEvent(tenantID, conversionData, c)

	// 4. 异步处理事件
	go func() {
		if err := h.trackingService.ProcessTrackingEvent(c.Request.Context(), event); err != nil {
			h.logError("failed_to_process_conversion", err, event)
		}
	}()

	// 5. 返回成功响应
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data": gin.H{
			"event_id": event.ID,
		},
	})
}

// HandleInbound 入站邮件/回复（占位，保持路由编译）
func (h *TrackingHandler) HandleInbound(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"code": 0, "message": "accepted"})
}

// HandleBatchEvents 边缘批量事件（占位，保持路由编译）
func (h *TrackingHandler) HandleBatchEvents(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"code": 0, "message": "accepted"})
}

// ConversionData 转化数据
type ConversionData struct {
	CID      string  `json:"cid"`
	SID      string  `json:"sid"`
	MID      string  `json:"mid"`
	Event    string  `json:"event"`
	OrderID  string  `json:"order_id,omitempty"`
	Value    float64 `json:"value,omitempty"`
	Currency string  `json:"currency,omitempty"`
	TS       int64   `json:"ts"`
	Nonce    string  `json:"nonce"`
	Sig      string  `json:"sig"`
}

// parseTrackingParams 解析追踪参数
func (h *TrackingHandler) parseTrackingParams(c *gin.Context) (TrackingParams, error) {
	var params TrackingParams

	params.CID = c.Query("cid")
	params.SID = c.Query("sid")
	params.MID = c.Query("mid")
	params.LID = c.Query("lid")
	params.Nonce = c.Query("nonce")
	params.Sig = c.Query("sig")

	tsStr := c.Query("ts")
	if tsStr != "" {
		if ts, err := strconv.ParseInt(tsStr, 10, 64); err == nil {
			params.TS = ts
		}
	}

	// 验证必需参数
	if params.CID == "" || params.SID == "" || params.MID == "" || params.Sig == "" {
		return params, fmt.Errorf("missing required parameters")
	}

	return params, nil
}

// parseClickParams 解析点击参数
func (h *TrackingHandler) parseClickParams(c *gin.Context) (TrackingParams, error) {
	params, err := h.parseTrackingParams(c)
	if err != nil {
		return params, err
	}

	params.Dest = c.Query("dest")
	if params.Dest == "" || params.LID == "" {
		return params, fmt.Errorf("missing click parameters")
	}

	return params, nil
}

// buildTrackingEvent 构建追踪事件
func (h *TrackingHandler) buildTrackingEvent(eventType entity.EventType, tenantID int64, params TrackingParams, c *gin.Context) *entity.TrackingEvent {
	event := entity.NewTrackingEvent(eventType, tenantID)
	event.CampaignID = params.CID
	event.MessageID = params.MID
	event.SubscriberID = params.SID
	event.UserAgent = c.GetHeader("User-Agent")
	event.IPAddress = c.ClientIP()
	event.Headers = h.extractHeaders(c)
	event.SignatureValid = true
	event.Timestamp = params.TS
	event.Nonce = params.Nonce

	// 获取地理位置信息
	if geoLocation, err := h.geoLocationService.GetLocation(event.IPAddress); err == nil {
		event.GeoLocation = geoLocation
	}

	return event
}

// buildClickEvent 构建点击事件
func (h *TrackingHandler) buildClickEvent(tenantID int64, params TrackingParams, destURL string, c *gin.Context) *entity.TrackingEvent {
	event := h.buildTrackingEvent(entity.EventTypeClick, tenantID, params, c)
	event.LinkID = params.LID
	event.OriginalURL = destURL

	// 解析目标域名
	if parsedURL, err := url.Parse(destURL); err == nil {
		event.DestinationHost = parsedURL.Host
	}

	return event
}

// buildConversionEvent 构建转化事件
func (h *TrackingHandler) buildConversionEvent(tenantID int64, data ConversionData, c *gin.Context) *entity.TrackingEvent {
	event := entity.NewTrackingEvent(entity.EventTypeConversion, tenantID)
	event.CampaignID = data.CID
	event.MessageID = data.MID
	event.SubscriberID = data.SID
	event.ConversionType = data.Event
	event.OrderID = data.OrderID
	event.Value = data.Value
	event.Currency = data.Currency
	event.UserAgent = c.GetHeader("User-Agent")
	event.IPAddress = c.ClientIP()
	event.Headers = h.extractHeaders(c)
	event.SignatureValid = true
	event.Timestamp = data.TS
	event.Nonce = data.Nonce

	// 获取地理位置信息
	if geoLocation, err := h.geoLocationService.GetLocation(event.IPAddress); err == nil {
		event.GeoLocation = geoLocation
	}

	return event
}

// extractTenantID 从请求中提取租户ID
func (h *TrackingHandler) extractTenantID(c *gin.Context) (int64, error) {
	// 1. 尝试从Host头部提取（如track.tenant123.example.com）
	host := c.GetHeader("Host")
	if tenantID := h.extractTenantFromHost(host); tenantID > 0 {
		return tenantID, nil
	}

	// 2. 尝试从Header提取
	if tenantIDStr := c.GetHeader("X-Tenant-Id"); tenantIDStr != "" {
		if tenantID, err := strconv.ParseInt(tenantIDStr, 10, 64); err == nil {
			return tenantID, nil
		}
	}

	// 3. 尝试从查询参数提取
	if tenantIDStr := c.Query("tenant_id"); tenantIDStr != "" {
		if tenantID, err := strconv.ParseInt(tenantIDStr, 10, 64); err == nil {
			return tenantID, nil
		}
	}

	return 0, fmt.Errorf("tenant ID not found")
}

// extractTenantFromHost 从Host中提取租户ID
func (h *TrackingHandler) extractTenantFromHost(host string) int64 {
	// 解析如track.tenant123.example.com格式
	parts := strings.Split(host, ".")
	if len(parts) >= 2 && strings.HasPrefix(parts[1], "tenant") {
		tenantIDStr := strings.TrimPrefix(parts[1], "tenant")
		if tenantID, err := strconv.ParseInt(tenantIDStr, 10, 64); err == nil {
			return tenantID
		}
	}
	return 0
}

// extractHeaders 提取请求头部
func (h *TrackingHandler) extractHeaders(c *gin.Context) map[string]string {
	headers := make(map[string]string)

	importantHeaders := []string{
		"Accept", "Accept-Language", "Accept-Encoding",
		"User-Agent", "Referer", "Connection",
		"X-Forwarded-For", "X-Real-IP", "Via",
		"Cache-Control", "Pragma",
	}

	for _, headerName := range importantHeaders {
		if value := c.GetHeader(headerName); value != "" {
			headers[strings.ToLower(headerName)] = value
		}
	}

	return headers
}

// returnPixelResponse 返回像素响应
func (h *TrackingHandler) returnPixelResponse(c *gin.Context) {
	// 返回1x1透明像素GIF
	pixel := []byte{
		0x47, 0x49, 0x46, 0x38, 0x39, 0x61, 0x01, 0x00, 0x01, 0x00, 0x80, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x21, 0xF9, 0x04, 0x01, 0x00, 0x00, 0x00,
		0x00, 0x2C, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x02, 0x02,
		0x0C, 0x0A, 0x00, 0x3B,
	}

	c.Header("Content-Type", "image/gif")
	c.Header("Content-Length", fmt.Sprintf("%d", len(pixel)))
	c.Data(http.StatusOK, "image/gif", pixel)
}

// logSecurityEvent 记录安全事件
func (h *TrackingHandler) logSecurityEvent(eventType string, params TrackingParams, c *gin.Context) {
	// 实现安全事件日志记录
	// 这里可以集成到统一的安全审计系统
}

// logError 记录错误
func (h *TrackingHandler) logError(message string, err error, event *entity.TrackingEvent) {
	// 实现错误日志记录
	// 这里可以集成到统一的日志系统
}
