package handlers

import (
	"errors"

	"gitee.com/heiyee/platforms/email-system/internal/domain/tag/entity"
	responsepkg "gitee.com/heiyee/platforms/pkg/common/response"
	"github.com/gin-gonic/gin"
)

// HandleTagError 处理标签相关错误
func HandleTagError(c *gin.Context, err error) {
	var tagErr *entity.TagError
	if errors.As(err, &tagErr) {
		switch tagErr.Code {
		// 标签验证错误
		case entity.CodeTagValidationError:
			if tagErr.Field != "" {
				responsepkg.FieldError(c, tagErr.Field, tagErr.Message)
			} else {
				responsepkg.Error(c, responsepkg.CodeValidationError, tagErr.Message)
			}
		case entity.CodeTagNameInvalid:
			responsepkg.FieldError(c, "name", tagErr.Message)
		case entity.CodeTagNameExists:
			responsepkg.FieldError(c, "name", tagErr.Message)
		case entity.CodeTagNotFound:
			responsepkg.NotFound(c, tagErr.Message)
		case entity.CodeTagTypeInvalid:
			responsepkg.FieldError(c, "type", tagErr.Message)
		case entity.CodeTagColorInvalid:
			responsepkg.FieldError(c, "color", tagErr.Message)
		case entity.CodeTagRuleInvalid:
			responsepkg.FieldError(c, "rule_tree_json", tagErr.Message)
		case entity.CodeTagPolicyInvalid:
			responsepkg.FieldError(c, "refresh_policy", tagErr.Message)

		// 标签操作错误
		case entity.CodeTagCreateFailed:
			responsepkg.InternalError(c, errors.New("创建标签失败"))
		case entity.CodeTagUpdateFailed:
			responsepkg.InternalError(c, errors.New("更新标签失败"))
		case entity.CodeTagDeleteFailed:
			responsepkg.InternalError(c, errors.New("删除标签失败"))
		case entity.CodeTagQueryFailed:
			responsepkg.InternalError(c, errors.New("查询标签失败"))
		case entity.CodeTagRefreshFailed:
			responsepkg.InternalError(c, errors.New("刷新标签失败"))
		case entity.CodeTagMergeFailed:
			responsepkg.InternalError(c, errors.New("合并标签失败"))
		case entity.CodeTagRenameFailed:
			responsepkg.InternalError(c, errors.New("重命名标签失败"))

			// 标签赋值错误
		case entity.CodeTagAssignFailed:
			responsepkg.InternalError(c, errors.New("标签赋值失败"))
		case entity.CodeTagUnassignFailed:
			responsepkg.InternalError(c, errors.New("取消标签赋值失败"))
		case entity.CodeTagBatchAssignFailed:
			responsepkg.InternalError(c, errors.New("批量标签赋值失败"))
		case entity.CodeTagBatchUnassignFailed:
			responsepkg.InternalError(c, errors.New("批量取消标签赋值失败"))
		case entity.CodeTagAssignmentExists:
			responsepkg.Error(c, responsepkg.CodeValidationError, tagErr.Message)
		case entity.CodeTagAssignmentNotFound:
			responsepkg.NotFound(c, tagErr.Message)

			// 标签规则错误
		case entity.CodeTagRuleParseError:
			responsepkg.FieldError(c, "rule_tree_json", tagErr.Message)
		case entity.CodeTagRuleExecuteError:
			responsepkg.InternalError(c, errors.New("标签规则执行失败"))
		case entity.CodeTagRuleValidateError:
			responsepkg.FieldError(c, "rule_tree_json", tagErr.Message)
		case entity.CodeTagRuleConditionError:
			responsepkg.FieldError(c, "rule_tree_json", tagErr.Message)

			// 标签权限错误
		case entity.CodeTagPermissionDenied:
			responsepkg.Forbidden(c, tagErr.Message)
		case entity.CodeTagAccessDenied:
			responsepkg.Forbidden(c, tagErr.Message)
		case entity.CodeTagOwnershipError:
			responsepkg.Forbidden(c, tagErr.Message)

		default:
			responsepkg.InternalError(c, errors.New("标签操作失败"))
		}
		return
	}

	// 处理其他类型的错误
	responsepkg.InternalError(c, errors.New("系统内部错误"))
}
