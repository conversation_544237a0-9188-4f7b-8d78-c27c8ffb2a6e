package handlers

import (
	"errors"
	"gitee.com/heiyee/platforms/email-system/internal/domain/import/entity"
	responsepkg "gitee.com/heiyee/platforms/pkg/common/response"
	"github.com/gin-gonic/gin"
)

// HandleImportError 处理导入相关错误
func HandleImportError(c *gin.Context, err error) {
	if err == nil {
		return
	}

	// 尝试解析为标准化的ImportServiceError
	var importErr *entity.ImportServiceError
	if errors.As(err, &importErr) {
		responsepkg.Error(c, importErr.Code, importErr.Message)
		return
	}

	// 处理原有的错误类型以保持兼容性
	switch e := err.(type) {
	case entity.ImportJobValidationError:
		responsepkg.Error(c, entity.CodeImportValidationError, e.Message)
		return

	case entity.ImportNotFoundError:
		responsepkg.Error(c, entity.CodeImportJobNotFound, "导入任务不存在")
		return

	case entity.ImportScenarioValidationError:
		responsepkg.Error(c, entity.CodeImportConfigInvalid, e.Message)
		return

	case entity.ImportScenarioNotFoundError:
		responsepkg.Error(c, entity.CodeImportResourceNotFound, "导入场景配置不存在")
		return

	case entity.ImportProcessingError:
		switch e.Phase {
		case "parsing":
			responsepkg.Error(c, entity.CodeImportFileParsingFailed, "文件解析失败")
		case "validation":
			responsepkg.Error(c, entity.CodeImportFileValidationFailed, "数据验证失败")
		case "processing":
			responsepkg.Error(c, entity.CodeImportFileProcessingFailed, "数据处理失败")
		default:
			responsepkg.Error(c, entity.CodeImportJobProcessFailed, "任务处理失败")
		}
		return

	case entity.ImportFileError:
		responsepkg.Error(c, entity.CodeImportFileNotAccessible, "文件访问失败")
		return

	case entity.ImportDataValidationError:
		responsepkg.Error(c, entity.CodeImportFileValidationFailed, "数据格式错误")
		return

	case entity.ImportBusinessError:
		responsepkg.Error(c, entity.CodeImportJobProcessFailed, "业务处理失败")
		return

	default:
		// 对于未知错误，统一返回内部错误码，不暴露具体错误信息
		responsepkg.Error(c, responsepkg.CodeInternalError, "系统内部错误")
		return
	}
}
