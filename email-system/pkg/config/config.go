package config

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	common_database "gitee.com/heiyee/platforms/pkg/db"
	"gitee.com/heiyee/platforms/pkg/grpcregistry"
	"gitee.com/heiyee/platforms/pkg/nacosconfig"

	"github.com/pelletier/go-toml/v2"
	"gopkg.in/yaml.v3"
)

// AppConfig 应用配置
type AppConfig struct {
	Server   ServerConfig   `json:"server" yaml:"server" toml:"server"`
	Database DatabaseConfig `json:"database" yaml:"database" toml:"database"`
	Redis    RedisConfig    `json:"redis" yaml:"redis" toml:"redis"`
	Log      LogConfig      `json:"log" yaml:"log" toml:"log"`
	Otel     OtelConfig     `json:"otel" yaml:"otel" toml:"otel"`
	GRPC     GRPCConfig     `json:"grpc" yaml:"grpc" toml:"grpc"`
	Email    EmailConfig    `json:"email" yaml:"email" toml:"email"`
	Storage  StorageConfig  `json:"storage" yaml:"storage" toml:"storage"`
	// gRPC客户端订阅配置
	GRPCSubscriptions []*grpcregistry.ClientManagerConfig `json:"grpcSubscriptions" yaml:"grpcSubscriptions" toml:"grpcSubscriptions"`
}

// ServerConfig HTTP服务器配置
type ServerConfig struct {
	Port         int    `json:"port" yaml:"port" toml:"port"`
	Mode         string `json:"mode" yaml:"mode" toml:"mode"`
	BaseURL      string `json:"base_url" yaml:"base_url" toml:"base_url"`
	ReadTimeout  string `json:"read_timeout" yaml:"read_timeout" toml:"read_timeout"`
	WriteTimeout string `json:"write_timeout" yaml:"write_timeout" toml:"write_timeout"`
	IdleTimeout  string `json:"idle_timeout" yaml:"idle_timeout" toml:"idle_timeout"`
	Env          string `json:"env" yaml:"env" toml:"env"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	MySQL common_database.MySQLConfig `json:"mysql" yaml:"mysql" toml:"mysql"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `json:"host" yaml:"host" toml:"host"`
	Port     int    `json:"port" yaml:"port" toml:"port"`
	Password string `json:"password" yaml:"password" toml:"password"`
	Database int    `json:"database" yaml:"database" toml:"database"`
	PoolSize int    `json:"pool_size" yaml:"pool_size" toml:"pool_size"`
	// 兼容旧格式
	Addr string `yaml:"addr"`
	DB   int    `yaml:"db"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `json:"level" yaml:"level" toml:"level"`
	Format     string `json:"format" yaml:"format" toml:"format"`
	Output     string `json:"output" yaml:"output" toml:"output"`
	TimeFormat string `json:"time_format" yaml:"time_format" toml:"time_format"`
	MaxSize    int    `json:"max_size" yaml:"max_size" toml:"max_size"`
	MaxBackups int    `json:"max_backups" yaml:"max_backups" toml:"max_backups"`
	MaxAge     int    `json:"max_age" yaml:"max_age" toml:"max_age"`
	Compress   bool   `json:"compress" yaml:"compress" toml:"compress"`
	Caller     bool   `json:"caller" yaml:"caller" toml:"caller"`
	Stacktrace bool   `json:"stacktrace" yaml:"stacktrace" toml:"stacktrace"`
}

// OtelConfig OpenTelemetry配置
type OtelConfig struct {
	Endpoint string `json:"endpoint" yaml:"endpoint" toml:"endpoint"`
}

// GRPCConfig gRPC配置
type GRPCConfig struct {
	Port           int               `json:"port" yaml:"port" toml:"port"`
	ServiceName    string            `json:"service_name" yaml:"service_name" toml:"service_name"`
	Group          string            `json:"group" yaml:"group" toml:"group"`
	Namespace      string            `json:"namespace" yaml:"namespace" toml:"namespace"`
	Weight         float64           `json:"weight" yaml:"weight" toml:"weight"`
	EnableRegister bool              `json:"enable_register" yaml:"enable_register" toml:"enable_register"`
	Metadata       map[string]string `json:"metadata" yaml:"metadata" toml:"metadata"`
	LocalIP        string            `json:"local_ip,omitempty" yaml:"local_ip,omitempty" toml:"local_ip,omitempty"`
}

// EmailConfig 邮件模块配置
type EmailConfig struct {
	ModuleEndpoint string `json:"module_endpoint" yaml:"module_endpoint" toml:"module_endpoint"`
	InternalAppID  int64  `json:"internal_app_id" yaml:"internal_app_id" toml:"internal_app_id"`
	Timeout        int    `json:"timeout" yaml:"timeout" toml:"timeout"`
}

// StorageConfig 存储配置
type StorageConfig struct {
	Type      string            `json:"type" yaml:"type" toml:"type"`
	LocalPath string            `json:"local_path" yaml:"local_path" toml:"local_path"`
	S3        S3Config          `json:"s3" yaml:"s3" toml:"s3"`
	Options   map[string]string `json:"options" yaml:"options" toml:"options"`
}

// S3Config S3存储配置
type S3Config struct {
	Region    string `json:"region" yaml:"region" toml:"region"`
	Bucket    string `json:"bucket" yaml:"bucket" toml:"bucket"`
	AccessKey string `json:"access_key" yaml:"access_key" toml:"access_key"`
	SecretKey string `json:"secret_key" yaml:"secret_key" toml:"secret_key"`
	Endpoint  string `json:"endpoint" yaml:"endpoint" toml:"endpoint"`
}

// GlobalConfig 全局配置实例
var (
	globalConfig *AppConfig
	configMutex  sync.RWMutex
)

// GetConfig 获取全局配置
func GetConfig() *AppConfig {
	configMutex.RLock()
	defer configMutex.RUnlock()
	return globalConfig
}

// SetConfig 设置全局配置
func SetConfig(config *AppConfig) {
	configMutex.Lock()
	defer configMutex.Unlock()
	globalConfig = config
}

// LoadAppConfig 加载应用配置，支持Nacos和本地文件，默认使用本地文件
func LoadAppConfig(serviceName string) *AppConfig {
	// 优先尝试从Nacos加载
	if err := LoadConfigFromNacos(serviceName); err != nil {
		fmt.Printf("Failed to load config from nacos: %v\n", err)
		// 回退到本地文件
		if err := loadConfigFromLocal(); err != nil {
			fmt.Printf("Failed to load config from local file: %v\n", err)
			return nil
		}
	}
	return GetConfig()
}

// LoadConfigFromNacos 从Nacos加载配置
func LoadConfigFromNacos(dataId string) error {
	cfg := nacosconfig.NewNacosConfigFromEnv(dataId)
	nacosClient, err := nacosconfig.NewNacosClient(cfg)
	if err != nil {
		return fmt.Errorf("failed to create nacos client: %w", err)
	}

	content, err := nacosClient.GetConfig()
	if err != nil {
		return fmt.Errorf("failed to get config from nacos: %w", err)
	}

	var config AppConfig
	// 检测配置格式并解析
	content = strings.TrimSpace(content)
	if strings.HasPrefix(content, "#") || strings.Contains(content, "[") {
		// TOML格式
		if err := toml.Unmarshal([]byte(content), &config); err != nil {
			return fmt.Errorf("failed to unmarshal toml config from nacos: %w", err)
		}
		fmt.Println("Successfully parsed TOML config from Nacos")
	} else {
		// JSON格式
		if err := json.Unmarshal([]byte(content), &config); err != nil {
			return fmt.Errorf("failed to unmarshal json config from nacos: %w", err)
		}
		fmt.Println("Successfully parsed JSON config from Nacos")
	}

	// 设置默认值
	setDefaultValues(&config)
	SetConfig(&config)
	config.Server.Env = cfg.Env
	fmt.Printf("Successfully loaded config from Nacos, server_port=%d, env=%s\n",
		config.Server.Port, config.Server.Env)
	return nil
}

// loadConfigFromLocal 加载本地配置
func loadConfigFromLocal() error {
	// 尝试多个可能的配置文件路径
	paths := []string{
		"configs/app.yaml",
		"email-system/configs/app.yaml",
		"./configs/app.yaml",
	}

	var lastErr error
	for _, path := range paths {
		if _, err := os.Stat(path); err == nil {
			return loadConfigFromFile(path)
		} else {
			lastErr = err
		}
	}

	return fmt.Errorf("no config file found in any of the expected paths: %v", lastErr)
}

// loadConfigFromFile 从指定文件加载配置
func loadConfigFromFile(configPath string) error {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	var config AppConfig
	content := strings.TrimSpace(string(data))

	// 根据文件扩展名或内容格式解析
	if strings.HasSuffix(configPath, ".yaml") || strings.HasSuffix(configPath, ".yml") {
		if err := yaml.Unmarshal(data, &config); err != nil {
			return fmt.Errorf("failed to unmarshal yaml config: %w", err)
		}
		fmt.Printf("Successfully parsed YAML config from %s\n", configPath)
	} else if strings.HasPrefix(content, "#") || strings.Contains(content, "[") {
		// TOML格式
		if err := toml.Unmarshal(data, &config); err != nil {
			return fmt.Errorf("failed to unmarshal toml config: %w", err)
		}
		fmt.Printf("Successfully parsed TOML config from %s\n", configPath)
	} else {
		// JSON格式
		if err := json.Unmarshal(data, &config); err != nil {
			return fmt.Errorf("failed to unmarshal json config: %w", err)
		}
		fmt.Printf("Successfully parsed JSON config from %s\n", configPath)
	}

	setDefaultValues(&config)
	SetConfig(&config)
	fmt.Printf("Successfully loaded config from local file %s, server_port=%d\n",
		configPath, config.Server.Port)
	return nil
}

// ListenNacosConfigChange 启动Nacos配置变更监听
func ListenNacosConfigChange(dataId string) error {
	cfg := nacosconfig.NewNacosConfigFromEnv(dataId)
	nacosClient, err := nacosconfig.NewNacosClient(cfg)
	if err != nil {
		return fmt.Errorf("failed to create nacos client: %w", err)
	}
	return nacosClient.ListenConfig(func(content string) {
		fmt.Println("Nacos config changed, reloading...")
		var config AppConfig
		content = strings.TrimSpace(content)
		if strings.HasPrefix(content, "#") || strings.Contains(content, "[") {
			if err := toml.Unmarshal([]byte(content), &config); err != nil {
				fmt.Println("Failed to unmarshal toml config on change")
				return
			}
			fmt.Println("Successfully parsed TOML config from Nacos (on change)")
		} else {
			if err := json.Unmarshal([]byte(content), &config); err != nil {
				fmt.Println("Failed to unmarshal json config on change")
				return
			}
			fmt.Println("Successfully parsed JSON config from Nacos (on change)")
		}
		setDefaultValues(&config)
		SetConfig(&config)
		fmt.Printf("Successfully reloaded config from Nacos (on change), server_port=%d\n",
			config.Server.Port)
	})
}

// setDefaultValues 设置默认值
func setDefaultValues(config *AppConfig) {
	// 服务器默认值
	if config.Server.Port == 0 {
		config.Server.Port = 8085
	}
	if config.Server.Mode == "" {
		config.Server.Mode = "debug"
	}
	if config.Server.ReadTimeout == "" {
		config.Server.ReadTimeout = "30s"
	}
	if config.Server.WriteTimeout == "" {
		config.Server.WriteTimeout = "30s"
	}
	if config.Server.IdleTimeout == "" {
		config.Server.IdleTimeout = "60s"
	}

	// 数据库默认值 - 兼容旧格式并转换为MySQL配置
	if config.Database.MySQL.Host == "" {
		config.Database.MySQL.Host = "localhost"
	}
	if config.Database.MySQL.Port == 0 {
		config.Database.MySQL.Port = 3306
	}
	if config.Database.MySQL.Database == "" {
		config.Database.MySQL.Database = "email-system"
	}
	if config.Database.MySQL.Username == "" {
		config.Database.MySQL.Username = "root"
	}
	if config.Database.MySQL.Password == "" {
		config.Database.MySQL.Password = "12345678"
	}
	if config.Database.MySQL.Charset == "" {
		config.Database.MySQL.Charset = "utf8mb4"
	}
	if !config.Database.MySQL.ParseTime {
		config.Database.MySQL.ParseTime = true
	}
	if config.Database.MySQL.Loc == "" {
		config.Database.MySQL.Loc = "Local"
	}
	if config.Database.MySQL.MaxOpenConns == 0 {
		config.Database.MySQL.MaxOpenConns = 100
	}
	if config.Database.MySQL.MaxIdleConns == 0 {
		config.Database.MySQL.MaxIdleConns = 10
	}
	if config.Database.MySQL.ConnMaxLifetime == "" {
		config.Database.MySQL.ConnMaxLifetime = "3600s"
	}
	if config.Database.MySQL.ConnMaxIdleTime == "" {
		config.Database.MySQL.ConnMaxIdleTime = "600s"
	}

	// Redis默认值 - 兼容旧格式
	if config.Redis.Host == "" && config.Redis.Addr != "" {
		// 从addr解析host和port
		parts := strings.Split(config.Redis.Addr, ":")
		if len(parts) == 2 {
			config.Redis.Host = parts[0]
			// port会在下面设置默认值
		}
	}
	if config.Redis.Host == "" {
		config.Redis.Host = "localhost"
	}
	if config.Redis.Port == 0 {
		config.Redis.Port = 6379
	}
	if config.Redis.Database == 0 && config.Redis.DB != 0 {
		config.Redis.Database = config.Redis.DB
	}
	if config.Redis.PoolSize == 0 {
		config.Redis.PoolSize = 10
	}

	// 日志默认值
	if config.Log.Level == "" {
		config.Log.Level = "info"
	}
	if config.Log.Format == "" {
		config.Log.Format = "json"
	}
	if config.Log.Output == "" {
		config.Log.Output = "stdout"
	}
	if config.Log.TimeFormat == "" {
		config.Log.TimeFormat = "2006-01-02T15:04:05.000Z07:00"
	}
	if config.Log.MaxSize == 0 {
		config.Log.MaxSize = 100
	}
	if config.Log.MaxBackups == 0 {
		config.Log.MaxBackups = 3
	}
	if config.Log.MaxAge == 0 {
		config.Log.MaxAge = 28
	}

	// OpenTelemetry默认值
	if config.Otel.Endpoint == "" {
		// 留空表示不启用
	}

	// gRPC默认值
	if config.GRPC.Port == 0 {
		config.GRPC.Port = 0 // 自动选择
	}
	if config.GRPC.ServiceName == "" {
		config.GRPC.ServiceName = "email-system"
	}
	if config.GRPC.Group == "" {
		config.GRPC.Group = "DEFAULT_GROUP"
	}
	if config.GRPC.Weight == 0 {
		config.GRPC.Weight = 1.0
	}

	// 邮件配置默认值
	if config.Email.ModuleEndpoint == "" {
		config.Email.ModuleEndpoint = "http://localhost:8084"
	}
	if config.Email.InternalAppID == 0 {
		config.Email.InternalAppID = 1000
	}
	if config.Email.Timeout == 0 {
		config.Email.Timeout = 30
	}

	// 存储配置默认值
	if config.Storage.Type == "" {
		config.Storage.Type = "local"
	}
	if config.Storage.LocalPath == "" {
		config.Storage.LocalPath = "./uploads"
	}
}

// GetServerReadTimeout 获取服务器读取超时时间
func (c *AppConfig) GetServerReadTimeout() time.Duration {
	duration, err := time.ParseDuration(c.Server.ReadTimeout)
	if err != nil {
		return 30 * time.Second
	}
	return duration
}

// GetServerWriteTimeout 获取服务器写入超时时间
func (c *AppConfig) GetServerWriteTimeout() time.Duration {
	duration, err := time.ParseDuration(c.Server.WriteTimeout)
	if err != nil {
		return 30 * time.Second
	}
	return duration
}

// GetServerIdleTimeout 获取服务器空闲超时时间
func (c *AppConfig) GetServerIdleTimeout() time.Duration {
	duration, err := time.ParseDuration(c.Server.IdleTimeout)
	if err != nil {
		return 60 * time.Second
	}
	return duration
}
