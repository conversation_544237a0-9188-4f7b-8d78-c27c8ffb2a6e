# 细分（Segments） API

- 统一响应 { code, message, data, meta }
- 仅 GET/POST

## 1. 创建细分
接口: POST /api/segments/create

请求:
```json
{
  "name": "活跃VIP",
  "description": "最近30天活跃且为VIP",
  "rules": {
    "and": [
      { "field": "tags", "op": "contains", "value": "VIP" },
      { "field": "last_open_at", "op": ">=", "value": "now-30d" }
    ]
  }
}
```
响应: `{ "code": 0, "message": "success", "data": { "segment_id": 3001 } }`

## 2. 更新细分
接口: POST /api/segments/update

请求:
```json
{ "segment_id": 3001, "name": "活跃VIP-更新", "rules": { "and": [ { "field": "tags", "op": "contains", "value": "VIP" } ] } }
```
响应: `{ "code": 0, "message": "success" }`

## 3. 获取详情
接口: GET /api/segments/get

请求: `?segment_id=3001`

响应(示例): `{ "code": 0, "message": "success", "data": { "segment": { "id": 3001, "name": "活跃VIP", "rule_summary": "VIP 且 近30天活跃" } } }`

## 4. 列表
接口: GET /api/segments/list

请求: `?page=1&size=20&search=VIP`

响应: `{ "code": 0, "message": "success", "data": { "segments": [ { "id": 3001, "name": "活跃VIP" } ], "total": 1 } }`

## 5. 预览
接口: POST /api/segments/preview

请求:
```json
{ "segment_id": 3001, "sample_size": 100 }
```
响应:
```json
{ "code": 0, "message": "success", "data": { "sample": [ { "id": 12345, "email": "<EMAIL>" } ], "estimated_total": 15000 } }
```

## 6. 删除
接口: POST /api/segments/delete

请求: `{ "segment_id": 3001 }`

响应: `{ "code": 0, "message": "success" }`

## 7. 错误码
| 码 | 含义 |
|---|---|
| 45001 | 规则不合法 |
| 45002 | 细分不存在 |
| 45003 | 无法预估计数 |
