syntax = "proto3";

package email_system;

option go_package = "github.com/platforms/email-system/api/email_system_pb";

// Email-System 追踪服务
service TrackingService {
    // 处理追踪事件
    rpc ProcessTrackingEvent(ProcessTrackingEventRequest) returns (ProcessTrackingEventResponse);
    
    // 批量处理追踪事件
    rpc BatchProcessTrackingEvents(BatchProcessTrackingEventsRequest) returns (BatchProcessTrackingEventsResponse);
    
    // 获取活动统计
    rpc GetCampaignStats(GetCampaignStatsRequest) returns (GetCampaignStatsResponse);
    
    // 获取实时指标
    rpc GetRealtimeMetrics(GetRealtimeMetricsRequest) returns (GetRealtimeMetricsResponse);
}

// 分析服务
service AnalyticsService {
    // 生成报表
    rpc GenerateReport(GenerateReportRequest) returns (GenerateReportResponse);
    
    // 导出数据
    rpc ExportData(ExportDataRequest) returns (ExportDataResponse);
    
    // 获取链接分析
    rpc GetLinkAnalytics(GetLinkAnalyticsRequest) returns (GetLinkAnalyticsResponse);
    
    // 获取用户旅程
    rpc GetUserJourney(GetUserJourneyRequest) returns (GetUserJourneyResponse);
}

// 处理追踪事件请求
message ProcessTrackingEventRequest {
    // 租户ID
    int64 tenant_id = 1;
    // 追踪事件
    TrackingEvent event = 2;
}

// 处理追踪事件响应
message ProcessTrackingEventResponse {
    // 状态码
    int32 code = 1;
    // 消息
    string message = 2;
    // 事件ID
    string event_id = 3;
    // 是否为新事件
    bool is_new_event = 4;
}

// 批量处理追踪事件请求
message BatchProcessTrackingEventsRequest {
    // 租户ID
    int64 tenant_id = 1;
    // 事件列表
    repeated TrackingEvent events = 2;
}

// 批量处理追踪事件响应
message BatchProcessTrackingEventsResponse {
    // 状态码
    int32 code = 1;
    // 消息
    string message = 2;
    // 处理结果
    repeated ProcessResult results = 3;
}

// 处理结果
message ProcessResult {
    // 事件ID
    string event_id = 1;
    // 是否成功
    bool success = 2;
    // 错误信息
    string error = 3;
}

// 追踪事件
message TrackingEvent {
    // 事件ID
    string id = 1;
    // 租户ID
    int64 tenant_id = 2;
    // 事件类型
    EventType event_type = 3;
    // 活动ID
    string campaign_id = 4;
    // 消息ID
    string message_id = 5;
    // 订阅者ID
    string subscriber_id = 6;
    // 链接ID（点击事件）
    string link_id = 7;
    
    // 分类信息
    OpenClassification open_class = 8;
    EventSource event_source = 9;
    bool is_unique = 10;
    bool is_first_hit = 11;
    
    // 请求信息
    string user_agent = 12;
    string ip_address = 13;
    GeoLocation geo_location = 14;
    map<string, string> headers = 15;
    
    // 安全信息
    bool signature_valid = 16;
    int64 timestamp = 17;
    string nonce = 18;
    
    // 转化信息
    string conversion_type = 19;
    string order_id = 20;
    double value = 21;
    string currency = 22;
    
    // 点击信息
    string destination_host = 23;
    string original_url = 24;
    
    // 时间戳
    int64 occurred_at = 25;
    int64 created_at = 26;
}

// 事件类型
enum EventType {
    EVENT_TYPE_UNKNOWN = 0;
    EVENT_TYPE_OPEN = 1;
    EVENT_TYPE_CLICK = 2;
    EVENT_TYPE_CONVERSION = 3;
    EVENT_TYPE_REPLY = 4;
    EVENT_TYPE_BEACON = 5;
    EVENT_TYPE_AMP_OPEN = 6;
}

// 打开分类
enum OpenClassification {
    OPEN_CLASS_UNKNOWN = 0;
    OPEN_CLASS_HUMAN = 1;
    OPEN_CLASS_PREFETCH = 2;
    OPEN_CLASS_MPP = 3;
    OPEN_CLASS_PROXY = 4;
    OPEN_CLASS_SCANNER = 5;
}

// 事件来源
enum EventSource {
    EVENT_SOURCE_UNKNOWN = 0;
    EVENT_SOURCE_DIRECT = 1;
    EVENT_SOURCE_APPLE_MPP = 2;
    EVENT_SOURCE_GMAIL_PROXY = 3;
    EVENT_SOURCE_SCANNER = 4;
    EVENT_SOURCE_EDGE = 5;
}

// 地理位置
message GeoLocation {
    // 国家代码
    string country = 1;
    // 地区
    string region = 2;
    // 城市
    string city = 3;
    // 纬度
    double lat = 4;
    // 经度
    double lon = 5;
}

// 获取活动统计请求
message GetCampaignStatsRequest {
    // 租户ID
    int64 tenant_id = 1;
    // 活动ID
    string campaign_id = 2;
    // 时间范围
    TimeRange time_range = 3;
}

// 获取活动统计响应
message GetCampaignStatsResponse {
    // 状态码
    int32 code = 1;
    // 消息
    string message = 2;
    // 统计数据
    CampaignStats data = 3;
}

// 活动统计
message CampaignStats {
    // 基础指标
    int64 sends = 1;
    int64 delivered = 2;
    int64 bounces = 3;
    int64 complaints = 4;
    int64 unsubscribes = 5;
    
    // 打开指标
    int64 gross_opens = 6;
    int64 unique_gross_opens = 7;
    int64 estimated_human_opens = 8;
    int64 unique_human_opens = 9;
    
    // 点击指标
    int64 clicks = 10;
    int64 unique_clicks = 11;
    
    // 转化指标
    int64 conversions = 12;
    double revenue = 13;
    string currency = 14;
    
    // 计算指标
    double delivery_rate = 15;
    double gross_open_rate = 16;
    double human_open_rate = 17;
    double click_through_rate = 18;
    double conversion_rate = 19;
    
    // 分类统计
    ClassificationStats classification_stats = 20;
    
    // 更新时间
    int64 updated_at = 21;
}

// 分类统计
message ClassificationStats {
    int64 mpp_opens = 1;
    int64 proxy_opens = 2;
    int64 scanner_opens = 3;
    int64 direct_opens = 4;
}

// 时间范围
message TimeRange {
    // 开始时间
    int64 start_time = 1;
    // 结束时间
    int64 end_time = 2;
}

// 获取实时指标请求
message GetRealtimeMetricsRequest {
    // 租户ID
    int64 tenant_id = 1;
    // 活动ID列表
    repeated string campaign_ids = 2;
    // 时间窗口（分钟）
    int32 time_window_minutes = 3;
}

// 获取实时指标响应
message GetRealtimeMetricsResponse {
    // 状态码
    int32 code = 1;
    // 消息
    string message = 2;
    // 实时指标
    repeated RealtimeMetric metrics = 3;
}

// 实时指标
message RealtimeMetric {
    // 活动ID
    string campaign_id = 1;
    // 时间窗口开始时间
    int64 window_start = 2;
    // 事件计数
    int64 event_count = 3;
    // 唯一用户数
    int64 unique_users = 4;
    // 事件类型分布
    map<string, int64> event_type_counts = 5;
}

// 生成报表请求
message GenerateReportRequest {
    // 租户ID
    int64 tenant_id = 1;
    // 报表类型
    ReportType report_type = 2;
    // 维度
    repeated string dimensions = 3;
    // 指标
    repeated string metrics = 4;
    // 过滤条件
    repeated ReportFilter filters = 5;
    // 时间范围
    TimeRange time_range = 6;
    // 分页
    Pagination pagination = 7;
}

// 生成报表响应
message GenerateReportResponse {
    // 状态码
    int32 code = 1;
    // 消息
    string message = 2;
    // 报表数据
    ReportData data = 3;
}

// 报表类型
enum ReportType {
    REPORT_TYPE_CAMPAIGN = 0;
    REPORT_TYPE_SUBSCRIBER = 1;
    REPORT_TYPE_DOMAIN = 2;
    REPORT_TYPE_LINK = 3;
    REPORT_TYPE_TIME_SERIES = 4;
}

// 报表过滤条件
message ReportFilter {
    // 字段名
    string field = 1;
    // 操作符
    string operator = 2;
    // 值
    repeated string values = 3;
}

// 分页
message Pagination {
    // 页码（从1开始）
    int32 page = 1;
    // 页大小
    int32 page_size = 2;
}

// 报表数据
message ReportData {
    // 列定义
    repeated ReportColumn columns = 1;
    // 数据行
    repeated ReportRow rows = 2;
    // 总数
    int64 total_count = 3;
    // 汇总信息
    ReportSummary summary = 4;
}

// 报表列
message ReportColumn {
    // 列名
    string name = 1;
    // 显示名
    string display_name = 2;
    // 数据类型
    string data_type = 3;
}

// 报表行
message ReportRow {
    // 值列表
    repeated ReportValue values = 1;
}

// 报表值
message ReportValue {
    // 字符串值
    string string_value = 1;
    // 数值
    double number_value = 2;
    // 布尔值
    bool bool_value = 3;
    // 时间戳
    int64 timestamp_value = 4;
}

// 报表汇总
message ReportSummary {
    // 汇总指标
    map<string, double> aggregated_metrics = 1;
    // 生成时间
    int64 generated_at = 2;
}