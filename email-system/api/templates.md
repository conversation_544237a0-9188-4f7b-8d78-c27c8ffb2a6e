# 模板与内容模块 API 接口文档

## 模块概述

模板与内容模块负责管理邮件模板的创建、编辑、版本控制、多语言支持、审批流程等功能，为邮件营销活动提供专业的内容制作和管理能力。

## 接口规范

### 基础响应格式
所有接口遵循统一的响应格式：
```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "errors": [],
  "meta": {}
}
```

### 通用参数
- `tenant_id`: 租户ID（通过请求头或中间件注入）
- `page`: 页码，从1开始
- `size`: 每页大小，默认20，最大100
- `trace_id`: 链路追踪ID（可选）

## 1. 模板管理

### 1.1 创建模板
**接口**: `POST /api/templates/create`

**描述**: 创建新的邮件模板

**请求参数**:
```json
{
  "name": "新年促销模板",
  "description": "新年特惠活动邮件模板",
  "editor_type": "mjml",
  "category": "promotion",
  "variables_json": {
    "customer_name": {
      "type": "string",
      "required": true,
      "default": "亲爱的客户"
    },
    "discount_rate": {
      "type": "number",
      "required": true,
      "min": 0,
      "max": 100
    },
    "expiry_date": {
      "type": "date",
      "required": true,
      "format": "YYYY-MM-DD"
    }
  },
  "partials_json": {
    "header": {
      "name": "通用页头",
      "content": "<mj-section>...</mj-section>"
    },
    "footer": {
      "name": "通用页脚",
      "content": "<mj-section>...</mj-section>"
    }
  },
  "settings": {
    "track_opens": true,
    "track_clicks": true,
    "unsubscribe_link": true,
    "preview_text": "新年特惠，限时抢购！"
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "新年促销模板",
    "editor_type": "mjml",
    "status": "draft",
    "current_version": null,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 1.2 更新模板
**接口**: `POST /api/templates/update`

**描述**: 更新模板基本信息

**请求参数**:
```json
{
  "id": 1,
  "name": "新年促销模板-更新",
  "description": "更新后的描述",
  "category": "promotion",
  "settings": {
    "track_opens": false,
    "preview_text": "更新后的预览文本"
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true
  }
}
```

### 1.3 获取模板详情
**接口**: `GET /api/templates/get`

**描述**: 获取模板详细信息

**请求参数**:
```
?id=1&include_content=true&include_versions=true
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "tenant_id": 1001,
    "name": "新年促销模板",
    "description": "新年特惠活动邮件模板",
    "editor_type": "mjml",
    "category": "promotion",
    "status": "published",
    "current_version": 2,
    "variables_json": {
      "customer_name": {
        "type": "string",
        "required": true,
        "default": "亲爱的客户"
      }
    },
    "partials_json": {
      "header": {
        "name": "通用页头",
        "content": "<mj-section>...</mj-section>"
      }
    },
    "settings": {
      "track_opens": true,
      "track_clicks": true,
      "unsubscribe_link": true,
      "preview_text": "新年特惠，限时抢购！"
    },
    "stats": {
      "total_sends": 15000,
      "open_rate": "78.5%",
      "click_rate": "12.3%",
      "unsubscribe_rate": "0.8%"
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 1.4 获取模板列表
**接口**: `GET /api/templates/list`

**描述**: 获取租户的模板列表

**请求参数**:
```
?page=1&size=20&status=published&category=promotion&search=新年
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "templates": [
      {
        "id": 1,
        "name": "新年促销模板",
        "description": "新年特惠活动邮件模板",
        "editor_type": "mjml",
        "category": "promotion",
        "status": "published",
        "current_version": 2,
        "created_at": "2024-01-01T00:00:00Z",
        "stats": {
          "total_sends": 15000,
          "open_rate": "78.5%"
        }
      }
    ],
    "total": 25,
    "page": 1,
    "size": 20
  }
}
```

### 1.5 删除模板
**接口**: `DELETE /api/templates/delete`

**描述**: 删除指定的模板

**请求参数**:
```
?id=1&force=false
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "deleted": true
  }
}
```

### 1.6 复制模板
**接口**: `POST /api/templates/copy`

**描述**: 复制现有模板

**请求参数**:
```json
{
  "template_id": 1,
  "new_name": "新年促销模板-副本",
  "include_versions": true
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 2,
    "name": "新年促销模板-副本",
    "copied_from": 1,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

## 2. 模板内容管理

### 2.1 创建模板版本
**接口**: `POST /api/templates/versions/create`

**描述**: 创建新的模板版本

**请求参数**:
```json
{
  "template_id": 1,
  "version_no": 1,
  "content_snapshot": {
    "zh-CN": {
      "subject": "新年特惠，限时抢购！",
      "html_content": "<!DOCTYPE html>...",
      "mjml_content": "<mjml>...",
      "text_content": "新年特惠活动...",
      "preview_text": "新年特惠，限时抢购！"
    },
    "en-US": {
      "subject": "New Year Special Offer!",
      "html_content": "<!DOCTYPE html>...",
      "mjml_content": "<mjml>...",
      "text_content": "New Year special offer...",
      "preview_text": "New Year Special Offer!"
    }
  },
  "variables_schema": {
    "customer_name": {
      "type": "string",
      "required": true,
      "default": "亲爱的客户"
    }
  },
  "partials_json": {
    "header": {
      "name": "通用页头",
      "content": "<mj-section>...</mj-section>"
    }
  },
  "change_log": "初始版本创建"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 100,
    "template_id": 1,
    "version_no": 1,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2.2 更新模板版本
**接口**: `POST /api/templates/versions/update`

**描述**: 更新现有模板版本

**请求参数**:
```json
{
  "id": 100,
  "content_snapshot": {
    "zh-CN": {
      "subject": "新年特惠，限时抢购！-更新",
      "html_content": "<!DOCTYPE html>...",
      "mjml_content": "<mjml>...",
      "text_content": "新年特惠活动...",
      "preview_text": "新年特惠，限时抢购！-更新"
    }
  },
  "change_log": "更新主题和内容"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true
  }
}
```

### 2.3 获取模板版本
**接口**: `GET /api/templates/versions/get`

**描述**: 获取模板版本详情

**请求参数**:
```
?id=100&include_content=true
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 100,
    "template_id": 1,
    "version_no": 1,
    "content_snapshot": {
      "zh-CN": {
        "subject": "新年特惠，限时抢购！",
        "html_content": "<!DOCTYPE html>...",
        "mjml_content": "<mjml>...",
        "text_content": "新年特惠活动...",
        "preview_text": "新年特惠，限时抢购！"
      }
    },
    "variables_schema": {
      "customer_name": {
        "type": "string",
        "required": true,
        "default": "亲爱的客户"
      }
    },
    "partials_json": {
      "header": {
        "name": "通用页头",
        "content": "<mj-section>...</mj-section>"
      }
    },
    "published_at": null,
    "created_by": 1001,
    "created_at": "2024-01-01T00:00:00Z",
    "change_log": "初始版本创建"
  }
}
```

### 2.4 获取模板版本列表
**接口**: `GET /api/templates/versions/list`

**描述**: 获取模板的所有版本

**请求参数**:
```
?template_id=1&page=1&size=20&include_content=false
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "versions": [
      {
        "id": 100,
        "version_no": 1,
        "published_at": null,
        "created_by": 1001,
        "created_at": "2024-01-01T00:00:00Z",
        "change_log": "初始版本创建"
      },
      {
        "id": 101,
        "version_no": 2,
        "published_at": "2024-01-01T10:00:00Z",
        "created_by": 1001,
        "created_at": "2024-01-01T09:00:00Z",
        "change_log": "更新主题和内容"
      }
    ],
    "total": 2,
    "page": 1,
    "size": 20
  }
}
```

### 2.5 发布模板版本
**接口**: `POST /api/templates/versions/publish`

**描述**: 发布指定的模板版本

**请求参数**:
```json
{
  "id": 100,
  "publish_note": "发布新年促销模板"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "published": true,
    "published_at": "2024-01-01T10:00:00Z"
  }
}
```

### 2.6 回滚模板版本
**接口**: `POST /api/templates/versions/rollback`

**描述**: 回滚到指定的历史版本

**请求参数**:
```json
{
  "template_id": 1,
  "target_version_id": 100,
  "rollback_note": "回滚到稳定版本"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "rolled_back": true,
    "current_version": 100
  }
}
```

## 3. 多语言内容管理

### 3.1 创建语言变体
**接口**: `POST /api/templates/locales/create`

**描述**: 为模板创建多语言变体

**请求参数**:
```json
{
  "template_id": 1,
  "version_id": 100,
  "locale": "en-US",
  "subject": "New Year Special Offer!",
  "html_content": "<!DOCTYPE html>...",
  "mjml_content": "<mjml>...",
  "text_content": "New Year special offer...",
  "preview_text": "New Year Special Offer!",
  "variables_overrides": {
    "customer_name": {
      "default": "Dear Customer"
    }
  },
  "assets_overrides": {
    "logo_url": "https://example.com/en/logo.png",
    "banner_url": "https://example.com/en/banner.jpg"
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 200,
    "template_id": 1,
    "version_id": 100,
    "locale": "en-US",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3.2 更新语言变体
**接口**: `POST /api/templates/locales/update`

**描述**: 更新模板的多语言变体

**请求参数**:
```json
{
  "id": 200,
  "subject": "New Year Special Offer! - Updated",
  "html_content": "<!DOCTYPE html>...",
  "mjml_content": "<mjml>...",
  "text_content": "New Year special offer...",
  "preview_text": "New Year Special Offer! - Updated"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true
  }
}
```

### 3.3 获取语言变体
**接口**: `GET /api/templates/locales/get`

**描述**: 获取模板的多语言变体详情

**请求参数**:
```
?id=200&include_content=true
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 200,
    "template_id": 1,
    "version_id": 100,
    "locale": "en-US",
    "subject": "New Year Special Offer!",
    "html_content": "<!DOCTYPE html>...",
    "mjml_content": "<mjml>...",
    "text_content": "New Year special offer...",
    "preview_text": "New Year Special Offer!",
    "variables_overrides": {
      "customer_name": {
        "default": "Dear Customer"
      }
    },
    "assets_overrides": {
      "logo_url": "https://example.com/en/logo.png"
    },
    "is_default": false,
    "created_by": 1001,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3.4 获取语言变体列表
**接口**: `GET /api/templates/locales/list`

**描述**: 获取模板的所有语言变体

**请求参数**:
```
?template_id=1&version_id=100&page=1&size=20
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "locales": [
      {
        "id": 199,
        "locale": "zh-CN",
        "subject": "新年特惠，限时抢购！",
        "is_default": true,
        "created_at": "2024-01-01T00:00:00Z"
      },
      {
        "id": 200,
        "locale": "en-US",
        "subject": "New Year Special Offer!",
        "is_default": false,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 2,
    "page": 1,
    "size": 20
  }
}
```

### 3.5 设置默认语言
**接口**: `POST /api/templates/locales/set-default`

**描述**: 设置模板的默认语言

**请求参数**:
```json
{
  "template_id": 1,
  "version_id": 100,
  "locale_id": 200
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "default_locale": "en-US"
  }
}
```

## 4. 模板审批流程

### 4.1 提交审批
**接口**: `POST /api/templates/approvals/submit`

**描述**: 提交模板版本进行审批

**请求参数**:
```json
{
  "template_id": 1,
  "version_id": 100,
  "approval_type": "content_review",
  "priority": "normal",
  "notes": "请审核新年促销模板的内容和设计"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "approval_id": 300,
    "status": "pending",
    "submitted_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4.2 审批处理
**接口**: `POST /api/templates/approvals/process`

**描述**: 处理模板审批请求

**请求参数**:
```json
{
  "approval_id": 300,
  "action": "approve",
  "comment": "内容审核通过，设计符合品牌规范",
  "next_reviewer_id": null
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "processed": true,
    "status": "approved",
    "processed_at": "2024-01-01T10:00:00Z"
  }
}
```

### 4.3 获取审批详情
**接口**: `GET /api/templates/approvals/get`

**描述**: 获取审批请求详情

**请求参数**:
```
?approval_id=300
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 300,
    "template_id": 1,
    "version_id": 100,
    "approval_type": "content_review",
    "status": "approved",
    "priority": "normal",
    "notes": "请审核新年促销模板的内容和设计",
    "comment": "内容审核通过，设计符合品牌规范",
    "submitted_by": 1001,
    "reviewer_id": 1002,
    "submitted_at": "2024-01-01T00:00:00Z",
    "decided_at": "2024-01-01T10:00:00Z"
  }
}
```

### 4.4 获取审批列表
**接口**: `GET /api/templates/approvals/list`

**描述**: 获取审批请求列表

**请求参数**:
```
?page=1&size=20&status=pending&template_id=1
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "approvals": [
      {
        "id": 300,
        "template_id": 1,
        "template_name": "新年促销模板",
        "version_no": 1,
        "approval_type": "content_review",
        "status": "approved",
        "priority": "normal",
        "submitted_by": 1001,
        "submitted_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 15,
    "page": 1,
    "size": 20
  }
}
```

## 5. 模板预览和测试

### 5.1 预览模板
**接口**: `POST /api/templates/preview`

**描述**: 预览模板渲染效果

**请求参数**:
```json
{
  "template_id": 1,
  "version_id": 100,
  "locale": "zh-CN",
  "variables": {
    "customer_name": "张三",
    "discount_rate": 20,
    "expiry_date": "2024-12-31"
  },
  "preview_type": "html"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "preview_url": "https://preview.example.com/template/12345",
    "html_content": "<!DOCTYPE html>...",
    "text_content": "新年特惠活动...",
    "subject": "新年特惠，限时抢购！",
    "preview_text": "新年特惠，限时抢购！"
  }
}
```

### 5.2 发送测试邮件
**接口**: `POST /api/templates/test-send`

**描述**: 发送测试邮件

**请求参数**:
```json
{
  "template_id": 1,
  "version_id": 100,
  "locale": "zh-CN",
  "test_emails": ["<EMAIL>", "<EMAIL>"],
  "variables": {
    "customer_name": "测试用户",
    "discount_rate": 20,
    "expiry_date": "2024-12-31"
  },
  "test_settings": {
    "track_opens": true,
    "track_clicks": true
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "test_id": "test_12345",
    "sent_count": 2,
    "failed_count": 0,
    "test_url": "/api/templates/test-status?test_id=test_12345"
  }
}
```

### 5.3 获取测试状态
**接口**: `GET /api/templates/test-status`

**描述**: 获取测试邮件发送状态

**请求参数**:
```
?test_id=test_12345
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "test_id": "test_12345",
    "status": "completed",
    "sent_count": 2,
    "failed_count": 0,
    "delivered_count": 2,
    "opened_count": 1,
    "clicked_count": 0,
    "bounced_count": 0,
    "test_emails": [
      {
        "email": "<EMAIL>",
        "status": "delivered",
        "opened": true,
        "clicked": false,
        "bounced": false
      }
    ]
  }
}
```

## 6. 模板片段管理

### 6.1 创建片段
**接口**: `POST /api/templates/partials/create`

**描述**: 创建可复用的模板片段

**请求参数**:
```json
{
  "name": "通用页头",
  "description": "包含logo和导航的通用页头",
  "category": "header",
  "content": "<mj-section>...</mj-section>",
  "variables": {
    "logo_url": {
      "type": "string",
      "required": true
    },
    "nav_links": {
      "type": "array",
      "required": false
    }
  },
  "tags": ["header", "navigation"]
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "通用页头",
    "category": "header",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 6.2 更新片段
**接口**: `POST /api/templates/partials/update`

**描述**: 更新模板片段

**请求参数**:
```json
{
  "id": 1,
  "name": "通用页头-更新",
  "content": "<mj-section>...</mj-section>",
  "variables": {
    "logo_url": {
      "type": "string",
      "required": true
    }
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true
  }
}
```

### 6.3 获取片段详情
**接口**: `GET /api/templates/partials/get`

**描述**: 获取模板片段详情

**请求参数**:
```
?id=1
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "tenant_id": 1001,
    "name": "通用页头",
    "description": "包含logo和导航的通用页头",
    "category": "header",
    "content": "<mj-section>...</mj-section>",
    "variables": {
      "logo_url": {
        "type": "string",
        "required": true
      }
    },
    "tags": ["header", "navigation"],
    "usage_count": 15,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 6.4 获取片段列表
**接口**: `GET /api/templates/partials/list`

**描述**: 获取模板片段列表

**请求参数**:
```
?page=1&size=50&category=header&search=页头
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "partials": [
      {
        "id": 1,
        "name": "通用页头",
        "category": "header",
        "usage_count": 15,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 25,
    "page": 1,
    "size": 50
  }
}
```

### 6.5 删除片段
**接口**: `DELETE /api/templates/partials/delete`

**描述**: 删除模板片段

**请求参数**:
```
?id=1&force=false
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "deleted": true,
    "affected_templates": 5
  }
}
```

## 7. 统计和报表

### 7.1 模板统计
**接口**: `GET /api/templates/stats`

**描述**: 获取模板的统计信息

**请求参数**:
```
?time_range=30d&template_id=1
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "overview": {
      "total_templates": 25,
      "published_templates": 20,
      "draft_templates": 5,
      "total_sends": 150000
    },
    "template_performance": [
      {
        "template_id": 1,
        "name": "新年促销模板",
        "total_sends": 15000,
        "open_rate": "78.5%",
        "click_rate": "12.3%",
        "unsubscribe_rate": "0.8%"
      }
    ],
    "category_performance": {
      "promotion": {
        "count": 10,
        "avg_open_rate": "75.2%",
        "avg_click_rate": "11.8%"
      },
      "newsletter": {
        "count": 8,
        "avg_open_rate": "68.5%",
        "avg_click_rate": "8.9%"
      }
    }
  }
}
```

### 7.2 版本统计
**接口**: `GET /api/templates/versions/stats`

**描述**: 获取模板版本的统计信息

**请求参数**:
```
?template_id=1&time_range=30d
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "template_id": 1,
    "template_name": "新年促销模板",
    "total_versions": 5,
    "published_versions": 3,
    "draft_versions": 2,
    "version_performance": [
      {
        "version_no": 2,
        "total_sends": 15000,
        "open_rate": "78.5%",
        "click_rate": "12.3%",
        "published_at": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

## 8. 错误码定义

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 40001 | 模板名称已存在 | 使用不同的名称 |
| 40002 | 版本号已存在 | 使用递增的版本号 |
| 40003 | 语言代码不支持 | 使用支持的语言代码 |
| 40004 | 变量定义无效 | 检查变量配置 |
| 40005 | 内容格式错误 | 检查HTML/MJML语法 |
| 40006 | 片段名称已存在 | 使用不同的名称 |
| 40007 | 审批流程配置错误 | 检查审批配置 |
| 50001 | 模板渲染失败 | 检查模板语法 |
| 50002 | 测试邮件发送失败 | 检查邮件配置 |
| 50003 | 版本发布失败 | 稍后重试 |

## 9. 性能说明

### 9.1 接口性能指标
- 模板查询：P95 < 100ms
- 版本查询：P95 < 80ms
- 内容渲染：P95 < 200ms
- 测试发送：P95 < 2秒

### 9.2 内容限制
- 单模板最大大小：2MB
- 单版本最大大小：5MB
- 支持的语言数量：无限制
- 变量数量：最大100个

### 9.3 缓存策略
- 模板内容：Redis缓存，TTL 1小时
- 渲染结果：Redis缓存，TTL 30分钟
- 统计数据：Redis缓存，TTL 5分钟

## 10. 最佳实践

### 10.1 模板设计
- 使用响应式设计，适配各种设备
- 合理使用变量，提高复用性
- 遵循邮件客户端兼容性要求
- 优化图片大小和加载

### 10.2 版本管理
- 使用语义化版本号
- 记录详细的变更日志
- 定期清理旧版本
- 重要版本设置保护

### 10.3 多语言支持
- 使用标准语言代码
- 考虑文化差异和本地化
- 测试各种语言环境
- 提供语言回退机制

### 10.4 审批流程
- 建立清晰的审批规则
- 设置合理的审批时限
- 记录详细的审批意见
- 支持多级审批流程
