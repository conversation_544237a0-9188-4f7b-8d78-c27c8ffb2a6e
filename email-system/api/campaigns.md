# 活动与A/B测试模块 API 接口文档

## 模块概述

活动与A/B测试模块负责管理邮件营销活动的创建、配置、发送、A/B测试等功能，支持定时、触发、循环等多种活动类型，为邮件营销提供完整的活动管理能力。

## 接口规范

### 基础响应格式
所有接口遵循统一的响应格式：
```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "errors": [],
  "meta": {}
}
```

### 通用参数
- `tenant_id`: 租户ID（通过请求头或中间件注入）
- `page`: 页码，从1开始
- `size`: 每页大小，默认20，最大100
- `trace_id`: 链路追踪ID（可选）

## 1. 活动管理

### 1.1 创建活动
**接口**: `POST /api/campaigns/create`

**描述**: 创建新的邮件营销活动，支持使用临时圈选草稿或直接配置标签

**请求参数**:
```json
{
  "name": "新年促销活动",
  "description": "新年特惠活动，限时抢购",
  "type": "scheduled",
  "channel_id": 1,
  "template_id": 1,
  "audience_json": {
    "include": {
      "tags": [1, 2, 3]
    },
    "exclude": {
      "tags": [4, 5]
    }
  },
  "settings_json": {
    "subject": "新年特惠，限时抢购！",
    "from_name": "营销团队",
    "from_email": "<EMAIL>",
    "reply_to": "<EMAIL>",
    "track_opens": true,
    "track_clicks": true,
    "unsubscribe_link": true,
    "utm_params": {
      "utm_source": "email",
      "utm_medium": "campaign",
      "utm_campaign": "new_year_2024"
    }
  },
  "locale_strategy": "auto",
  "force_locale": null,
  "multilingual_config": {
    "default_locale": "zh-CN",
    "fallback_locale": "en-US"
  },
  "schedule_time": "2024-01-01T10:00:00Z",
  "timezone": "Asia/Shanghai",
  "ab_enabled": true,
  "ab_experiment_id": null,
  "audience_draft_key": "draft_12345"
}
```

**说明**：
- `audience_json`：直接配置标签选择，适用于简单的标签组合
- `audience_draft_key`：使用临时圈选草稿，适用于复杂的圈选规则，创建活动后会自动创建标签并绑定关系
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "新年促销活动",
    "type": "scheduled",
    "status": "draft",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 1.2 更新活动
**接口**: `POST /api/campaigns/update`

**描述**: 更新活动配置信息

**请求参数**:
```json
{
  "id": 1,
  "name": "新年促销活动-更新",
  "description": "更新后的活动描述",
  "audience_json": {
    "include": {
      "lists": [1, 2, 4]
    }
  },
  "settings_json": {
    "subject": "新年特惠，限时抢购！-更新",
    "utm_params": {
      "utm_campaign": "new_year_2024_v2"
    }
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true
  }
}
```

### 1.3 获取活动详情
**接口**: `GET /api/campaigns/get`

**描述**: 获取活动详细信息

**请求参数**:
```
?id=1&include_stats=true
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "tenant_id": 1001,
    "name": "新年促销活动",
    "description": "新年特惠活动，限时抢购",
    "type": "scheduled",
    "channel_id": 1,
    "template_id": 1,
      "audience_json": {
    "include": {
      "tags": [1, 2]
    }
  },
    "settings_json": {
      "subject": "新年特惠，限时抢购！",
      "from_name": "营销团队",
      "track_opens": true
    },
    "locale_strategy": "auto",
    "schedule_time": "2024-01-01T10:00:00Z",
    "timezone": "Asia/Shanghai",
    "ab_enabled": true,
    "ab_experiment_id": 100,
    "status": "scheduled",
    "created_by": 1001,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "stats": {
      "total_recipients": 15000,
      "sent_count": 15000,
      "delivered_count": 14800,
      "opened_count": 11800,
      "clicked_count": 1800,
      "bounced_count": 200,
      "unsubscribed_count": 150,
      "complained_count": 5
    }
  }
}
```

### 1.4 获取活动列表
**接口**: `GET /api/campaigns/list`

**描述**: 获取租户的活动列表

**请求参数**:
```
?page=1&size=20&status=scheduled&type=scheduled&search=新年
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "campaigns": [
      {
        "id": 1,
        "name": "新年促销活动",
        "type": "scheduled",
        "status": "scheduled",
        "schedule_time": "2024-01-01T10:00:00Z",
        "created_at": "2024-01-01T00:00:00Z",
        "stats": {
          "total_recipients": 15000,
          "sent_count": 15000,
          "open_rate": "78.7%"
        }
      }
    ],
    "total": 25,
    "page": 1,
    "size": 20
  }
}
```

### 1.5 删除活动
**接口**: `DELETE /api/campaigns/delete`

**描述**: 删除指定的活动

**请求参数**:
```
?id=1&force=false
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "deleted": true
  }
}
```

### 1.6 复制活动
**接口**: `POST /api/campaigns/copy`

**描述**: 复制现有活动

**请求参数**:
```json
{
  "campaign_id": 1,
  "new_name": "新年促销活动-副本",
  "include_audience": true,
  "include_settings": true
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 2,
    "name": "新年促销活动-副本",
    "copied_from": 1,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

## 2. 活动状态管理

### 2.1 发布活动
**接口**: `POST /api/campaigns/publish`

**描述**: 发布活动，开始发送

**请求参数**:
```json
{
  "id": 1,
  "publish_note": "发布新年促销活动"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "published": true,
    "published_at": "2024-01-01T10:00:00Z",
    "estimated_duration": "2-3小时"
  }
}
```

### 2.2 暂停活动
**接口**: `POST /api/campaigns/pause`

**描述**: 暂停正在进行的活动

**请求参数**:
```json
{
  "id": 1,
  "pause_note": "暂停活动进行优化"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "paused": true,
    "paused_at": "2024-01-01T11:00:00Z"
  }
}
```

### 2.3 恢复活动
**接口**: `POST /api/campaigns/resume`

**描述**: 恢复暂停的活动

**请求参数**:
```json
{
  "id": 1,
  "resume_note": "恢复活动发送"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "resumed": true,
    "resumed_at": "2024-01-01T12:00:00Z"
  }
}
```

### 2.4 取消活动
**接口**: `POST /api/campaigns/cancel`

**描述**: 取消未开始或暂停的活动

**请求参数**:
```json
{
  "id": 1,
  "cancel_note": "取消活动，重新设计"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "canceled": true,
    "canceled_at": "2024-01-01T13:00:00Z"
  }
}
```

## 3. A/B测试管理

### 3.1 创建A/B测试
**接口**: `POST /api/campaigns/ab/create`

**描述**: 为活动创建A/B测试

**请求参数**:
```json
{
  "campaign_id": 1,
  "name": "主题行A/B测试",
  "test_type": "subject",
  "variants": [
    {
      "name": "版本A",
      "content": "新年特惠，限时抢购！",
      "weight": 50
    },
    {
      "name": "版本B", 
      "content": "新年大促，折扣高达80%！",
      "weight": 50
    }
  ],
  "test_duration_hours": 24,
  "winner_criteria": "open_rate",
  "auto_select_winner": true
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "experiment_id": 100,
    "name": "主题行A/B测试",
    "status": "running",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3.2 获取A/B测试详情
**接口**: `GET /api/campaigns/ab/get`

**描述**: 获取A/B测试详细信息

**请求参数**:
```
?experiment_id=100
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 100,
    "campaign_id": 1,
    "name": "主题行A/B测试",
    "test_type": "subject",
    "status": "running",
    "variants": [
      {
        "id": 1,
        "name": "版本A",
        "content": "新年特惠，限时抢购！",
        "weight": 50,
        "sent_count": 7500,
        "open_count": 6000,
        "open_rate": "80.0%",
        "click_count": 900,
        "click_rate": "15.0%"
      },
      {
        "id": 2,
        "name": "版本B",
        "content": "新年大促，折扣高达80%！",
        "weight": 50,
        "sent_count": 7500,
        "open_count": 6300,
        "open_rate": "84.0%",
        "click_count": 945,
        "click_rate": "15.0%"
      }
    ],
    "test_duration_hours": 24,
    "winner_criteria": "open_rate",
    "auto_select_winner": true,
    "started_at": "2024-01-01T10:00:00Z",
    "estimated_completion": "2024-01-02T10:00:00Z",
    "current_winner": null
  }
}
```

### 3.3 手动选择获胜者
**接口**: `POST /api/campaigns/ab/select-winner`

**描述**: 手动选择A/B测试获胜者

**请求参数**:
```json
{
  "experiment_id": 100,
  "winner_variant_id": 2,
  "reason": "版本B在打开率和点击率方面表现更好"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "winner_selected": true,
    "winner_variant_id": 2,
    "selected_at": "2024-01-01T15:00:00Z"
  }
}
```

### 3.4 获取A/B测试列表
**接口**: `GET /api/campaigns/ab/list`

**描述**: 获取活动的A/B测试列表

**请求参数**:
```
?campaign_id=1&page=1&size=20&status=running
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "experiments": [
      {
        "id": 100,
        "name": "主题行A/B测试",
        "test_type": "subject",
        "status": "running",
        "started_at": "2024-01-01T10:00:00Z",
        "current_winner": null
      }
    ],
    "total": 5,
    "page": 1,
    "size": 20
  }
}
```

## 4. 活动发送管理

### 4.1 获取发送状态
**接口**: `GET /api/campaigns/send-status`

**描述**: 获取活动的发送状态和进度

**请求参数**:
```
?campaign_id=1
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "campaign_id": 1,
    "status": "sending",
    "progress": 65,
    "total_recipients": 15000,
    "sent_count": 9750,
    "delivered_count": 9600,
    "bounced_count": 150,
    "estimated_completion": "30分钟",
    "current_batch": 15,
    "total_batches": 23,
    "send_rate": "500 emails/min",
    "started_at": "2024-01-01T10:00:00Z",
    "last_activity": "2024-01-01T10:30:00Z"
  }
}
```

### 4.2 暂停发送
**接口**: `POST /api/campaigns/send/pause`

**描述**: 暂停活动的发送

**请求参数**:
```json
{
  "campaign_id": 1,
  "pause_note": "暂停发送进行优化"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "paused": true,
    "paused_at": "2024-01-01T10:35:00Z"
  }
}
```

### 4.3 恢复发送
**接口**: `POST /api/campaigns/send/resume`

**描述**: 恢复活动的发送

**请求参数**:
```json
{
  "campaign_id": 1,
  "resume_note": "恢复发送"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "resumed": true,
    "resumed_at": "2024-01-01T10:40:00Z"
  }
}
```

## 5. 活动受众管理

### 5.1 创建临时圈选草稿
**接口**: `POST /api/campaigns/audience/draft/create`

**描述**: 在创建活动时创建临时圈选草稿，支持嵌入圈选页面

**请求参数**:
```json
{
  "audience_config": {
    "include": {
      "tags": [1, 2],
      "custom_rules": {
        "field": "company",
        "operator": "equals",
        "value": "示例公司"
      }
    },
    "exclude": {
      "tags": [5]
    }
  },
  "estimated_count": 15000,
  "preview_contacts": [
    {
      "id": 12345,
      "email": "<EMAIL>",
      "first_name": "张三",
      "company": "示例公司"
    }
  ]
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "draft_key": "draft_12345",
    "audience_config": {
      "include": {
        "tags": [1, 2],
        "custom_rules": {
          "field": "company",
          "operator": "equals",
          "value": "示例公司"
        }
      },
      "exclude": {
        "tags": [5]
      }
    },
    "estimated_count": 15000,
    "expires_at": "2024-01-02T00:00:00Z"
  }
}
```

### 5.2 获取临时圈选草稿
**接口**: `GET /api/campaigns/audience/draft/get`

**描述**: 获取临时圈选草稿详情

**请求参数**:
```
?draft_key=draft_12345
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "draft_key": "draft_12345",
    "audience_config": {
      "include": {
        "tags": [1, 2],
        "custom_rules": {
          "field": "company",
          "operator": "equals",
          "value": "示例公司"
        }
      },
      "exclude": {
        "tags": [5]
      }
    },
    "estimated_count": 15000,
    "preview_contacts": [...],
    "expires_at": "2024-01-02T00:00:00Z",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 5.3 预览受众
**接口**: `POST /api/campaigns/audience/preview`

**描述**: 预览活动的目标受众

**请求参数**:
```json
{
  "campaign_id": 1,
  "sample_size": 100
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total_recipients": 15000,
    "sample_recipients": [
      {
        "id": 12345,
        "email": "<EMAIL>",
        "first_name": "张三",
        "lists": ["VIP客户", "活跃用户"],
        "tags": ["VIP", "企业客户"]
      }
    ],
    "audience_breakdown": {
      "by_tag": {
        "VIP客户": 8000,
        "活跃用户": 7000,
        "企业客户": 6000
      },
      "by_language": {
        "zh-CN": 12000,
        "en-US": 3000
      }
    }
  }
}
```

### 5.2 更新受众
**接口**: `POST /api/campaigns/audience/update`

**描述**: 更新活动的目标受众

**请求参数**:
```json
{
  "campaign_id": 1,
  "audience_json": {
    "include": {
      "tags": [1, 2, 4]
    },
    "exclude": {
      "tags": [5, 6]
    }
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true,
    "new_total_recipients": 18000
  }
}
```

## 6. 活动内容管理

### 6.1 更新活动内容
**接口**: `POST /api/campaigns/content/update`

**描述**: 更新活动的内容设置

**请求参数**:
```json
{
  "campaign_id": 1,
  "template_id": 2,
  "settings_json": {
    "subject": "新年特惠，限时抢购！-更新",
    "from_name": "营销团队-更新",
    "utm_params": {
      "utm_campaign": "new_year_2024_v2"
    }
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true
  }
}
```

### 6.2 预览活动内容
**接口**: `POST /api/campaigns/content/preview`

**描述**: 预览活动的邮件内容

**请求参数**:
```json
{
  "campaign_id": 1,
  "locale": "zh-CN",
  "sample_contact_id": 12345
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "preview_url": "https://preview.example.com/campaign/12345",
    "subject": "新年特惠，限时抢购！",
    "from_name": "营销团队",
    "from_email": "<EMAIL>",
    "html_content": "<!DOCTYPE html>...",
    "text_content": "新年特惠活动...",
    "preview_text": "新年特惠，限时抢购！"
  }
}
```

## 7. 统计和报表

### 7.1 活动统计
**接口**: `GET /api/campaigns/stats`

**描述**: 获取活动的统计信息

**请求参数**:
```
?campaign_id=1&time_range=7d&group_by=hour
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "overview": {
      "total_recipients": 15000,
      "sent_count": 15000,
      "delivered_count": 14800,
      "bounced_count": 200,
      "delivery_rate": "98.7%"
    },
    "engagement": {
      "opened_count": 11800,
      "clicked_count": 1800,
      "open_rate": "78.7%",
      "click_rate": "12.2%",
      "click_to_open_rate": "15.5%"
    },
    "unsubscribes": {
      "unsubscribed_count": 150,
      "unsubscribe_rate": "1.0%"
    },
    "complaints": {
      "complained_count": 5,
      "complaint_rate": "0.03%"
    },
    "time_series": [
      {
        "hour": "2024-01-01T10:00:00Z",
        "sent": 500,
        "delivered": 495,
        "opened": 400,
        "clicked": 60
      }
    ]
  }
}
```

### 7.2 A/B测试统计
**接口**: `GET /api/campaigns/ab/stats`

**描述**: 获取A/B测试的统计信息

**请求参数**:
```
?experiment_id=100&time_range=24h
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "experiment_id": 100,
    "name": "主题行A/B测试",
    "status": "running",
    "total_sent": 15000,
    "variants_stats": [
      {
        "variant_id": 1,
        "name": "版本A",
        "sent_count": 7500,
        "delivered_count": 7400,
        "opened_count": 6000,
        "clicked_count": 900,
        "open_rate": "80.0%",
        "click_rate": "15.0%",
        "conversion_rate": "2.1%"
      },
      {
        "variant_id": 2,
        "name": "版本B",
        "sent_count": 7500,
        "delivered_count": 7400,
        "opened_count": 6300,
        "clicked_count": 945,
        "open_rate": "84.0%",
        "click_rate": "15.0%",
        "conversion_rate": "2.3%"
      }
    ],
    "significance_test": {
      "is_significant": true,
      "confidence_level": "95%",
      "p_value": 0.02,
      "recommended_winner": "版本B"
    }
  }
}
```

## 8. 错误码定义

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 40001 | 活动名称已存在 | 使用不同的名称 |
| 40002 | 模板不存在 | 检查模板ID |
| 40003 | 发件渠道不存在 | 检查渠道ID |
| 40004 | 受众配置无效 | 检查受众配置 |
| 40005 | 发送时间已过 | 设置未来的发送时间 |
| 40006 | A/B测试配置错误 | 检查测试配置 |
| 40007 | 活动状态不允许操作 | 检查活动当前状态 |
| 40008 | 受众数量超出限制 | 减少受众范围或联系管理员 |
| 50001 | 活动发布失败 | 稍后重试 |
| 50002 | 发送任务创建失败 | 稍后重试 |
| 50003 | 数据库操作失败 | 稍后重试 |

## 9. 性能说明

### 9.1 接口性能指标
- 活动查询：P95 < 100ms
- 活动创建：P95 < 200ms
- 统计查询：P95 < 300ms
- 受众预览：P95 < 500ms

### 9.2 发送性能
- 单活动最大受众：100万
- 发送速率：1000+ emails/min
- 并发活动数：10个
- A/B测试最大变体：5个

### 9.3 限制说明
- 单租户最大活动数：1000个
- 单活动最大变体数：5个
- 受众筛选条件：最大10层嵌套
- 发送时间精度：分钟级

## 10. 最佳实践

### 10.1 活动设计
- 使用有吸引力的主题行
- 设计响应式邮件模板
- 设置合理的发送时间
- 避免过度营销

### 10.2 A/B测试
- 一次只测试一个变量
- 设置足够的测试样本
- 选择合适的获胜标准
- 及时分析测试结果

### 10.3 受众管理
- 使用标签统一管理人群圈选结果
- 在创建活动时支持嵌入圈选页面
- 复杂圈选规则使用临时草稿，自动创建标签
- 定期清理无效联系人
- 避免向退订用户发送
- 监控投诉率

### 10.4 发送优化
- 分时段发送提高打开率
- 监控发送声誉
- 优化发送频率
- 及时处理退信
