# 自动化旅程模块 API 接口文档

## 模块概述

自动化旅程模块负责管理客户旅程的设计、配置、执行和监控，支持基于事件的触发机制、条件分支、延迟设置等复杂流程，为邮件营销提供智能化的客户生命周期管理。

## 接口规范

### 基础响应格式
所有接口遵循统一的响应格式：
```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "errors": [],
  "meta": {}
}
```

## 1. 旅程管理

### 1.1 创建旅程
**接口**: `POST /api/journeys/create`

**描述**: 创建新的自动化旅程

**请求参数**:
```json
{
  "name": "新用户欢迎旅程",
  "description": "新用户注册后的欢迎和引导流程",
  "trigger_type": "event",
  "trigger_config": {
    "event_name": "user_registered",
    "conditions": {
      "user_type": "new",
      "source": "website"
    }
  },
  "entry_criteria": {
    "tags": [1, 2]
  },
  "exit_criteria": {
    "max_duration_days": 30,
    "max_emails": 10,
    "unsubscribed": true
  },
  "settings": {
    "timezone": "Asia/Shanghai",
    "max_concurrent_contacts": 1000,
    "send_time_optimization": true
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "新用户欢迎旅程",
    "status": "draft",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 1.2 更新旅程
**接口**: `POST /api/journeys/update`

**描述**: 更新旅程配置

**请求参数**:
```json
{
  "id": 1,
  "name": "新用户欢迎旅程-更新",
  "description": "更新后的旅程描述",
  "entry_criteria": {
    "tags": [1, 2, 3]
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true
  }
}
```

### 1.3 获取旅程详情
**接口**: `GET /api/journeys/get`

**描述**: 获取旅程详细信息

**请求参数**:
```
?id=1&include_flow=true&include_stats=true
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "tenant_id": 1001,
    "name": "新用户欢迎旅程",
    "description": "新用户注册后的欢迎和引导流程",
    "trigger_type": "event",
    "trigger_config": {
      "event_name": "user_registered",
      "conditions": {
        "user_type": "new",
        "source": "website"
      }
    },
    "entry_criteria": {
      "tags": [1, 2]
    },
    "exit_criteria": {
      "max_duration_days": 30,
      "max_emails": 10
    },
    "status": "active",
    "flow_json": {
      "nodes": [...],
      "edges": [...]
    },
    "stats": {
      "total_entries": 5000,
      "active_contacts": 1200,
      "completed_contacts": 3000,
      "exited_contacts": 800
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 1.4 获取旅程列表
**接口**: `GET /api/journeys/list`

**描述**: 获取租户的旅程列表

**请求参数**:
```
?page=1&size=20&status=active&trigger_type=event
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "journeys": [
      {
        "id": 1,
        "name": "新用户欢迎旅程",
        "trigger_type": "event",
        "status": "active",
        "total_entries": 5000,
        "active_contacts": 1200,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 15,
    "page": 1,
    "size": 20
  }
}
```

## 2. 旅程流程设计

### 2.1 更新旅程流程
**接口**: `POST /api/journeys/flow/update`

**描述**: 更新旅程的流程设计

**请求参数**:
```json
{
  "journey_id": 1,
  "flow_json": {
    "nodes": [
      {
        "id": "start",
        "type": "trigger",
        "position": {"x": 100, "y": 100},
        "data": {
          "event_name": "user_registered"
        }
      },
      {
        "id": "welcome_email",
        "type": "email",
        "position": {"x": 300, "y": 100},
        "data": {
          "template_id": 1,
          "delay_minutes": 0,
          "subject": "欢迎加入我们！"
        }
      },
      {
        "id": "wait_3_days",
        "type": "delay",
        "position": {"x": 500, "y": 100},
        "data": {
          "delay_days": 3
        }
      },
      {
        "id": "follow_up_email",
        "type": "email",
        "position": {"x": 700, "y": 100},
        "data": {
          "template_id": 2,
          "delay_minutes": 0,
          "subject": "开始探索我们的产品"
        }
      }
    ],
    "edges": [
      {
        "id": "e1",
        "source": "start",
        "target": "welcome_email"
      },
      {
        "id": "e2",
        "source": "welcome_email",
        "target": "wait_3_days"
      },
      {
        "id": "e3",
        "source": "wait_3_days",
        "target": "follow_up_email"
      }
    ]
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true,
    "flow_version": 2
  }
}
```

### 2.2 验证旅程流程
**接口**: `POST /api/journeys/flow/validate`

**描述**: 验证旅程流程配置的正确性

**请求参数**:
```json
{
  "journey_id": 1,
  "flow_json": {
    "nodes": [...],
    "edges": [...]
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "valid": true,
    "warnings": [],
    "errors": [],
    "flow_analysis": {
      "total_nodes": 4,
      "total_emails": 2,
      "estimated_duration": "3天",
      "complexity": "simple"
    }
  }
}
```

## 3. 旅程状态管理

### 3.1 激活旅程
**接口**: `POST /api/journeys/activate`

**描述**: 激活旅程，开始执行

**请求参数**:
```json
{
  "id": 1,
  "activate_note": "激活新用户欢迎旅程"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "activated": true,
    "activated_at": "2024-01-01T10:00:00Z"
  }
}
```

### 3.2 暂停旅程
**接口**: `POST /api/journeys/pause`

**描述**: 暂停旅程执行

**请求参数**:
```json
{
  "id": 1,
  "pause_note": "暂停旅程进行优化"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "paused": true,
    "paused_at": "2024-01-01T11:00:00Z"
  }
}
```

### 3.3 停止旅程
**接口**: `POST /api/journeys/stop`

**描述**: 停止旅程，不再接受新联系人

**请求参数**:
```json
{
  "id": 1,
  "stop_note": "停止旅程，重新设计"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "stopped": true,
    "stopped_at": "2024-01-01T12:00:00Z"
  }
}
```

## 4. 旅程执行监控

### 4.1 获取旅程执行状态
**接口**: `GET /api/journeys/execution/status`

**描述**: 获取旅程的执行状态

**请求参数**:
```
?journey_id=1
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "journey_id": 1,
    "status": "active",
    "execution_stats": {
      "total_entries": 5000,
      "active_contacts": 1200,
      "completed_contacts": 3000,
      "exited_contacts": 800,
      "current_node": "follow_up_email"
    },
    "performance": {
      "emails_sent_today": 150,
      "emails_delivered": 148,
      "emails_opened": 118,
      "emails_clicked": 18
    },
    "last_activity": "2024-01-01T10:30:00Z"
  }
}
```

### 4.2 获取联系人旅程状态
**接口**: `GET /api/journeys/contacts/status`

**描述**: 获取特定联系人在旅程中的状态

**请求参数**:
```
?journey_id=1&contact_id=12345
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "journey_id": 1,
    "contact_id": 12345,
    "status": "active",
    "current_node": "follow_up_email",
    "entry_time": "2024-01-01T10:00:00Z",
    "last_activity": "2024-01-01T10:30:00Z",
    "emails_sent": 1,
    "emails_opened": 1,
    "emails_clicked": 0,
    "next_action": {
      "type": "email",
      "scheduled_time": "2024-01-04T10:00:00Z",
      "template_id": 2
    }
  }
}
```

### 4.3 手动触发旅程
**接口**: `POST /api/journeys/trigger/manual`

**描述**: 手动触发联系人进入旅程

**请求参数**:
```json
{
  "journey_id": 1,
  "contact_ids": [12345, 12346],
  "trigger_data": {
    "source": "manual",
    "reason": "测试旅程流程"
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "triggered": true,
    "triggered_contacts": 2,
    "failed_contacts": 0,
    "triggered_at": "2024-01-01T10:00:00Z"
  }
}
```

## 5. 旅程分析

### 5.1 旅程性能分析
**接口**: `GET /api/journeys/analytics/performance`

**描述**: 获取旅程的性能分析数据

**请求参数**:
```
?journey_id=1&time_range=30d&group_by=day
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "journey_id": 1,
    "overview": {
      "total_entries": 5000,
      "completion_rate": "60.0%",
      "avg_duration_days": 15.5,
      "conversion_rate": "8.5%"
    },
    "node_performance": [
      {
        "node_id": "welcome_email",
        "name": "欢迎邮件",
        "contacts_reached": 5000,
        "emails_sent": 5000,
        "emails_opened": 4000,
        "open_rate": "80.0%",
        "emails_clicked": 600,
        "click_rate": "15.0%"
      }
    ],
    "time_series": [
      {
        "date": "2024-01-01",
        "new_entries": 150,
        "completions": 90,
        "exits": 20
      }
    ]
  }
}
```

### 5.2 旅程漏斗分析
**接口**: `GET /api/journeys/analytics/funnel`

**描述**: 获取旅程的漏斗分析数据

**请求参数**:
```
?journey_id=1&time_range=30d
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "journey_id": 1,
    "funnel": [
      {
        "step": 1,
        "node_name": "欢迎邮件",
        "contacts_reached": 5000,
        "conversion_rate": "100%"
      },
      {
        "step": 2,
        "node_name": "等待3天",
        "contacts_reached": 4800,
        "conversion_rate": "96%"
      },
      {
        "step": 3,
        "node_name": "跟进邮件",
        "contacts_reached": 4600,
        "conversion_rate": "92%"
      }
    ],
    "drop_off_analysis": [
      {
        "from_step": 1,
        "to_step": 2,
        "drop_off_count": 200,
        "drop_off_rate": "4%",
        "main_reasons": ["退订", "投诉", "硬退"]
      }
    ]
  }
}
```

## 6. 错误码定义

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 40001 | 旅程名称已存在 | 使用不同的名称 |
| 40002 | 流程配置无效 | 检查流程节点和连接 |
| 40003 | 触发配置错误 | 检查触发事件配置 |
| 40004 | 入口条件无效 | 检查受众配置 |
| 40005 | 模板不存在 | 检查邮件模板ID |
| 40006 | 延迟设置无效 | 检查延迟时间配置 |
| 40007 | 旅程状态不允许操作 | 检查当前状态 |
| 50001 | 旅程激活失败 | 稍后重试 |
| 50002 | 流程执行失败 | 检查流程配置 |
| 50003 | 数据库操作失败 | 稍后重试 |

## 7. 性能说明

### 7.1 接口性能指标
- 旅程查询：P95 < 100ms
- 流程更新：P95 < 200ms
- 执行状态查询：P95 < 150ms
- 分析数据查询：P95 < 500ms

### 7.2 执行性能
- 单旅程最大联系人：10万
- 并发执行能力：1000+ 联系人
- 最大流程节点：50个
- 最大嵌套层级：10层

### 7.3 限制说明
- 单租户最大旅程数：100个
- 单旅程最大变体数：5个
- 流程复杂度：中等
- 执行频率：分钟级

## 8. 最佳实践

### 8.1 旅程设计
- 保持流程简洁明了
- 通过标签精准选择目标受众
- 设置合理的延迟时间
- 避免过度营销
- 提供退出机制

### 8.2 触发机制
- 选择合适的事件触发
- 设置合理的触发条件
- 通过标签选择目标受众
- 避免重复触发
- 监控触发频率

### 8.3 流程优化
- 定期分析流程性能
- 优化低转化节点
- A/B测试关键节点
- 及时调整流程

### 8.4 监控管理
- 实时监控执行状态
- 设置告警机制
- 定期检查性能
- 及时处理异常
