# 联系人模块 API 接口文档

## 模块概述

联系人模块是邮件营销系统的核心组件，负责管理所有联系人的基础信息、自定义属性、导入导出、标签管理等功能。

## 接口规范

### 基础响应格式
所有接口遵循统一的响应格式：
```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "errors": [],
  "meta": {}
}
```

### 通用参数
- `tenant_id`: 租户ID（通过请求头或中间件注入）
- `page`: 页码，从1开始
- `size`: 每页大小，默认20，最大100
- `trace_id`: 链路追踪ID（可选）

## 1. 联系人基础管理

### 1.1 创建联系人
**接口**: `POST /api/contacts/create`

**描述**: 创建新联系人，如果邮箱已存在则返回错误

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "status": "active",
  "preferred_language": "zh-CN",
  "country_code": "CN",
  "timezone": "Asia/Shanghai",
  "notes": "高价值客户，对产品很感兴趣",
  "attributes": {
    "first_name": "张三",
    "last_name": "李",
    "phone": "13800138000",
    "company": "示例公司",
    "region": "Beijing",
    "city": "Beijing"
  },
  "lists": [1, 2],
  "tags": ["VIP", "新客户"]
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 12345,
    "email": "<EMAIL>",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 1.2 更新联系人
**接口**: `POST /api/contacts/update`

**描述**: 更新联系人信息，支持合并策略

**请求参数**:
```json
{
  "id": 12345,
  "email": "<EMAIL>",
  "attributes": {
    "first_name": "张三",
    "company": "新公司名称"
  },
  "notes": "客户反馈：对产品很满意，建议增加更多功能",
  "status": "active",
  "merge_policy": "new_overwrite"
}
```

**合并策略说明**:
- `keep_existing`: 保留现有值，不覆盖
- `new_overwrite`: 新值覆盖旧值
- `most_recent`: 按时间戳选择最新值
- `concat`: 字符串类型进行拼接

### 1.3 获取联系人详情
**接口**: `GET /api/contacts/get`

**描述**: 根据ID或邮箱获取联系人详细信息

**请求参数**:
```
?id=12345
或
?email=<EMAIL>
&fields=first_name,last_name,company,preferred_language,location
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
    "data": {
    "id": 12345,
    "tenant_id": 1001,
    "email": "<EMAIL>",
    "status": "active",
    "preferred_language": "zh-CN",
    "country_code": "CN",
    "timezone": "Asia/Shanghai",
    "notes": "高价值客户，对产品很感兴趣",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "last_activity_at": "2024-01-01T00:00:00Z",
    "attributes": {
      "first_name": "张三",
      "last_name": "李",
      "company": "示例公司",
      "region": "Beijing",
      "city": "Beijing"
    },
    "lists": [
      {
        "id": 1,
        "name": "VIP客户"
      }
    ],
    "tags": [
      {
        "id": 1,
        "name": "VIP",
        "color": "#FF6B6B"
      }
    ]
  }
}
```

### 1.4 搜索联系人
**接口**: `POST /api/contacts/search`

**描述**: 支持多条件搜索和筛选的联系人列表

**请求参数**:
```json
{
      "filters": {
      "status": "active",
      "email": "<EMAIL>",
      "lists": [1, 2],
      "tags": ["VIP"],
      "preferred_language": "zh-CN",
      "country_code": "CN",
      "attributes": {
        "company": "示例公司",
        "region": "Beijing"
      },
      "created_at_range": {
        "start": "2024-01-01T00:00:00Z",
        "end": "2024-12-31T23:59:59Z"
      },
      "last_activity_range": {
        "start": "2024-01-01T00:00:00Z",
        "end": "2024-12-31T23:59:59Z"
      }
    },
  "sort": {
    "field": "created_at",
    "order": "desc"
  },
  "page": 1,
  "size": 20
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "contacts": [
      {
        "id": 12345,
        "email": "<EMAIL>",
        "status": "active",
        "first_name": "张三",
        "company": "示例公司",
        "created_at": "2024-01-01T00:00:00Z",
        "last_activity_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 1000,
    "page": 1,
    "size": 20
  },
  "meta": {
    "filter_summary": {
      "total_contacts": 1000,
      "active_contacts": 950,
      "suppressed_contacts": 50
    }
  }
}
```

## 2. 批量导入系统

### 2.1 导入计划分析
**接口**: `POST /api/contacts/import/plan`

**描述**: 分析上传文件，生成字段映射建议和预校验报告

**请求参数**:
```json
{
  "file_url": "https://oss.example.com/contacts.csv",
  "sample_size": 200,
  "file_type": "csv",
  "sheet_name": "Sheet1",
  "header_row": 1
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "job_id": "imp_12345",
    "suggested_mapping": [
      {
        "source": "邮箱",
        "target": "email",
        "confidence": 0.95,
        "type_guess": "email",
        "sample_values": ["<EMAIL>", "<EMAIL>"]
      },
      {
        "source": "姓名",
        "target": "first_name",
        "confidence": 0.90,
        "type_guess": "text",
        "sample_values": ["张三", "李四"]
      },
      {
        "source": "语言",
        "target": "preferred_language",
        "confidence": 0.85,
        "type_guess": "enum",
        "sample_values": ["zh-CN", "en-US"]
      },
      {
        "source": "地址",
        "target": "location",
        "confidence": 0.80,
        "type_guess": "location",
        "sample_values": ["中国北京", "美国纽约"]
      }
    ],
    "sample_preview": {
      "rows": [
        {
          "row_number": 1,
          "data": {
            "邮箱": "<EMAIL>",
            "姓名": "张三",
            "语言": "zh-CN",
            "地址": "中国北京"
          }
        }
      ],
      "issues": [
        {
          "row_number": 5,
          "field": "邮箱",
          "issue": "invalid_email_format",
          "message": "邮箱格式不正确"
        }
      ]
    },
    "validator_version": "1.2.0",
    "estimated_total_rows": 10000,
    "file_analysis": {
      "encoding": "UTF-8",
      "delimiter": ",",
      "has_header": true,
      "column_count": 8
    }
  }
}
```

### 2.2 导入预校验
**接口**: `POST /api/contacts/import/preview`

**描述**: 基于映射配置进行预校验，生成详细报告

**请求参数**:
```json
{
  "file_url": "https://oss.example.com/contacts.csv",
  "mapping": {
    "邮箱": "email",
    "姓名": "first_name",
    "语言": "preferred_language",
    "地址": "location"
  },
  "merge_policy": "new_overwrite",
  "dedupe_mode": "merge",
  "sample_size": 500,
  "assign": {
    "lists": [1],
    "tags": ["导入"]
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "validation_summary": {
      "total_rows": 10000,
      "valid_rows": 9850,
      "error_rows": 150,
      "duplicate_rows": 200,
      "estimated_inserts": 8000,
      "estimated_updates": 1850
    },
    "issues_by_type": {
      "invalid_email": 80,
      "duplicate_email": 200,
      "invalid_language": 30,
      "invalid_location": 40
    },
    "first_rows_with_marks": [
      {
        "row_number": 1,
        "data": {
          "邮箱": "<EMAIL>",
          "姓名": "张三",
          "语言": "zh-CN",
          "地址": "中国北京"
        },
        "marks": ["valid", "new_contact"]
      },
      {
        "row_number": 5,
        "data": {
          "邮箱": "<EMAIL>",
          "姓名": "李四",
          "语言": "zh-CN",
          "地址": "中国上海"
        },
        "marks": ["valid", "duplicate", "will_update"]
      }
    ],
    "field_analysis": {
      "email": {
        "unique_count": 9800,
        "duplicate_count": 200,
        "invalid_count": 80
      },
      "preferred_language": {
        "supported_values": ["zh-CN", "en-US", "ja-JP"],
        "unsupported_values": ["中文", "English"]
      }
    }
  }
}
```

### 2.3 提交导入任务
**接口**: `POST /api/contacts/import`

**描述**: 提交导入任务，开始异步处理

**请求参数**:
```json
{
  "file_url": "https://oss.example.com/contacts.csv",
  "mapping": {
    "邮箱": "email",
    "姓名": "first_name",
    "语言": "preferred_language",
    "地址": "location"
  },
  "dedupe_mode": "merge",
  "merge_policy": "new_overwrite",
  "assign": {
    "lists": [1],
    "tags": ["导入"]
  },
  "settings": {
    "batch_size": 1000,
    "max_workers": 3,
    "skip_errors": false
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "job_id": "imp_12345",
    "status": "queued",
    "estimated_duration": "5-10分钟",
    "progress_url": "/api/contacts/import/status?job_id=imp_12345"
  }
}
```

### 2.4 查询导入状态
**接口**: `GET /api/contacts/import/status`

**描述**: 查询导入任务的处理状态和进度

**请求参数**:
```
?job_id=imp_12345
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "job_id": "imp_12345",
    "status": "importing",
    "phase": "importing",
    "progress": 65,
    "shard_count": 3,
    "total_rows": 10000,
    "processed_rows": 6500,
    "error_count": 150,
    "created_count": 5200,
    "updated_count": 1300,
    "skipped_count": 0,
    "current_shard": 2,
    "estimated_remaining_time": "3分钟",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:05:00Z",
    "worker_info": {
      "active_workers": 3,
      "queue_length": 1500,
      "processing_rate": "1200 rows/min"
    }
  }
}
```

### 2.5 下载错误报告
**接口**: `GET /api/contacts/import/errors/download`

**描述**: 下载导入任务的错误行报告

**请求参数**:
```
?job_id=imp_12345&format=csv
```

**响应**: 返回CSV文件下载链接或直接返回CSV内容

**错误报告格式**:
```csv
行号,原始行内容,错误类型,错误描述,建议修复
5,"<EMAIL>,李四,中文,中国上海","invalid_language","不支持的语言代码","请使用zh-CN、en-US等标准代码"
```

### 2.6 重试导入任务
**接口**: `POST /api/contacts/import/retry`

**描述**: 重试失败的导入任务或仅重试错误行

**请求参数**:
```json
{
  "job_id": "imp_12345",
  "retry_mode": "errors_only",
  "fixes": {
    "5": {
      "语言": "zh-CN"
    }
  }
}
```

## 3. 列表和标签管理

### 3.1 批量更新标签
**接口**: `POST /api/contacts/tags/update`

**描述**: 批量添加或移除联系人标签

**请求参数**:
```json
{
  "contact_ids": [12345, 12346, 12347],
  "emails": ["<EMAIL>", "<EMAIL>"],
  "add": ["VIP", "新客户"],
  "remove": ["测试标签"]
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "affected_contacts": 5,
    "added_tags": 2,
    "removed_tags": 1,
    "summary": {
      "success": 5,
      "failed": 0,
      "errors": []
    }
  }
}
```

### 3.2 批量更新列表
**接口**: `POST /api/contacts/lists/update`

**描述**: 批量添加或移除联系人列表

**请求参数**:
```json
{
  "contact_ids": [12345, 12346, 12347],
  "emails": ["<EMAIL>", "<EMAIL>"],
  "add": [1, 2],
  "remove": [3]
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "affected_contacts": 5,
    "added_to_lists": 2,
    "removed_from_lists": 1,
    "summary": {
      "success": 5,
      "failed": 0,
      "errors": []
    }
  }
}
```

### 3.3 基于筛选条件的批量操作
**接口**: `POST /api/contacts/bulk/operation`

**描述**: 基于筛选条件执行批量操作

**请求参数**:
```json
{
      "scope": {
      "filter": {
        "status": "active",
        "lists": [1],
        "preferred_language": "zh-CN",
        "country_code": "CN",
        "attributes": {
          "company": "示例公司"
        }
      }
    },
  "operation": "add_tags",
  "params": {
    "tags": ["VIP", "企业客户"]
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "task_id": "bulk_12345",
    "estimated_affected": 1500,
    "status": "queued",
    "progress_url": "/api/contacts/bulk/status?task_id=bulk_12345"
  }
}
```

## 4. 联系人备注

联系人备注信息直接存储在 `contacts` 主表的 `notes` 字段中，支持长文本格式。

### 4.1 添加备注
通过创建或更新联系人接口添加备注：

```json
POST /api/contacts/update
{
  "id": 12345,
  "notes": "客户反馈：对产品很满意，建议增加更多功能"
}
```

### 4.2 备注格式建议
- 备注内容：纯文本格式，支持长文本
- 存储方式：直接存储在 `contacts.notes` 字段中
- 更新策略：支持追加或覆盖模式
- 建议格式：包含时间戳和操作人信息，便于追踪

### 4.3 备注查询
备注信息会通过获取联系人详情接口直接返回，作为主表字段的一部分。

## 4. 时间线事件

### 4.1 查询时间线
**接口**: `GET /api/contacts/timeline`

**描述**: 获取联系人的行为时间线

**请求参数**:
```
?contact_id=12345&page=1&size=50&event_types=send,open,click&start_date=2024-01-01&end_date=2024-12-31
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "events": [
      {
        "id": 11111,
        "event_type": "send",
        "message_id": 22222,
        "properties": {
          "campaign_id": 33333,
          "campaign_name": "新年促销",
          "template_id": 44444,
          "subject": "新年特惠，限时抢购！"
        },
        "occurred_at": "2024-01-01T10:00:00Z"
      },
      {
        "id": 11112,
        "event_type": "open",
        "message_id": 22222,
        "properties": {
          "campaign_id": 33333,
          "user_agent": "Mozilla/5.0...",
          "ip_address": "***********"
        },
        "occurred_at": "2024-01-01T10:05:00Z"
      }
    ],
    "total": 25,
    "page": 1,
    "size": 50,
    "summary": {
      "total_sends": 15,
      "total_opens": 8,
      "total_clicks": 2,
      "open_rate": "53.3%",
      "click_rate": "13.3%"
    }
  }
}
```

## 5. 自定义字段管理

### 6.1 获取字段定义
**接口**: `GET /api/contacts/fields`

**描述**: 获取租户的自定义字段定义

**请求参数**:
```
?page=1&size=50&field_type=text
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "fields": [
      {
        "id": 1,
        "field_key": "first_name",
        "field_type": "text",
        "options_json": {
          "max_length": 50,
          "required": true,
          "validation": "alpha_space"
        },
        "created_at": "2024-01-01T00:00:00Z",
        "usage_count": 1500
      }
    ],
    "total": 25,
    "page": 1,
    "size": 50
  }
}
```

### 6.2 创建字段定义
**接口**: `POST /api/contacts/fields/create`

**描述**: 创建新的自定义字段定义

**请求参数**:
```json
{
  "field_key": "birthday",
  "field_type": "date",
  "options_json": {
    "required": false,
    "format": "YYYY-MM-DD",
    "min_date": "1900-01-01",
    "max_date": "2024-12-31"
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 26,
    "field_key": "birthday",
    "field_type": "date",
    "created_at": "2024-01-01T10:00:00Z"
  }
}
```

## 7. 统计和报表

### 7.1 联系人统计
**接口**: `GET /api/contacts/stats`

**描述**: 获取联系人的统计信息

**请求参数**:
```
?time_range=30d&group_by=day
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "overview": {
      "total_contacts": 15000,
      "active_contacts": 14200,
      "suppressed_contacts": 800,
      "new_contacts_this_month": 1200,
      "growth_rate": "8.6%"
    },
    "trends": {
      "daily_growth": [
        {
          "date": "2024-01-01",
          "new_contacts": 45,
          "total_contacts": 14855
        }
      ]
    },
    "demographics": {
      "by_language": {
        "zh-CN": 12000,
        "en-US": 2500,
        "ja-JP": 500
      },
      "by_country": {
        "CN": 13000,
        "US": 1500,
        "JP": 500
      },
      "by_timezone": {
        "Asia/Shanghai": 12000,
        "America/New_York": 1500,
        "Asia/Tokyo": 500
      }
    }
  }
}
```

## 8. 错误码定义

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 40001 | 邮箱格式不正确 | 检查邮箱格式 |
| 40002 | 邮箱已存在 | 使用更新接口或指定合并策略 |
| 40003 | 必填字段缺失 | 补充必填字段 |
| 40004 | 字段值超出长度限制 | 缩短字段值 |
| 40005 | 不支持的语言代码 | 使用标准ISO 639-1 + 国家代码格式（如zh-CN、en-US） |
| 40006 | 无效的国家代码 | 使用标准ISO 3166-1 alpha-2国家代码（如CN、US） |
| 40007 | 无效的时区标识符 | 使用标准IANA时区标识符（如Asia/Shanghai、America/New_York） |
| 40008 | 导入文件过大 | 分拆文件或联系管理员 |
| 40009 | 字段映射配置错误 | 检查映射配置 |
| 50001 | 导入任务创建失败 | 稍后重试 |
| 50002 | 文件处理超时 | 检查文件大小和格式 |
| 50003 | 数据库写入失败 | 稍后重试 |

## 9. 性能说明

### 9.1 接口性能指标
- 联系人查询：P95 < 100ms
- 搜索接口：P95 < 200ms
- 导入状态查询：P95 < 50ms
- 批量操作：P95 < 500ms

### 9.2 导入性能
- 小文件（<1MB）：同步处理，<30秒
- 中等文件（1-100MB）：异步处理，5-30分钟
- 大文件（>100MB）：分片处理，30分钟-2小时

### 9.3 限制说明
- 单次导入最大行数：100万行
- 单次批量操作最大联系人：10万个
- 单联系人最大自定义字段：200个
- 单字段最大长度：2KB（文本）、1KB（JSON）

## 10. 最佳实践

### 10.1 导入优化
- 使用CSV格式而非Excel，提高处理速度
- 合理设置字段映射，减少类型推断开销
- 分批导入大文件，避免超时
- 及时处理错误行，提高成功率

### 10.2 查询优化
- 使用索引字段进行筛选（如preferred_language、country_code）
- 避免复杂的嵌套属性查询
- 合理使用分页，避免一次性查询大量数据
- 缓存常用查询结果
- 优先使用主表字段进行筛选，减少JOIN操作

### 10.3 批量操作
- 优先使用筛选条件批量操作
- 合理设置批次大小，平衡性能和资源消耗
- 监控批量任务进度，及时处理异常
