# 投递与可达性模块 API 接口文档

## 模块概述

投递与可达性模块负责管理发件渠道、投递优化、声誉管理、退信处理等功能，确保邮件能够高效、安全地送达收件人，维护良好的发件声誉。

## 接口规范

### 基础响应格式
所有接口遵循统一的响应格式：
```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "errors": [],
  "meta": {}
}
```

## 1. 发件渠道管理

### 1.1 创建发件渠道
**接口**: `POST /api/channels/create`

**描述**: 创建新的发件渠道

**请求参数**:
```json
{
  "name": "主发件渠道",
  "description": "主要的企业邮箱发件渠道",
  "type": "smtp",
  "provider": "custom",
  "config": {
    "host": "smtp.example.com",
    "port": 587,
    "username": "<EMAIL>",
    "encryption": "tls",
    "auth_method": "password"
  },
  "settings": {
    "daily_send_limit": 100000,
    "hourly_send_limit": 5000,
    "max_connections": 10,
    "connection_timeout": 30,
    "retry_attempts": 3
  },
  "reputation": {
    "initial_score": 50,
    "warmup_enabled": true,
    "warmup_schedule": {
      "start_date": "2024-01-01",
      "daily_increase": 1000,
      "max_daily": 50000
    }
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "主发件渠道",
    "type": "smtp",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 1.2 更新发件渠道
**接口**: `POST /api/channels/update`

**描述**: 更新发件渠道配置

**请求参数**:
```json
{
  "id": 1,
  "name": "主发件渠道-更新",
  "settings": {
    "daily_send_limit": 150000,
    "hourly_send_limit": 8000
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true
  }
}
```

### 1.3 获取发件渠道详情
**接口**: `GET /api/channels/get`

**描述**: 获取发件渠道详细信息

**请求参数**:
```
?id=1&include_stats=true
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "tenant_id": 1001,
    "name": "主发件渠道",
    "description": "主要的企业邮箱发件渠道",
    "type": "smtp",
    "provider": "custom",
    "status": "active",
    "config": {
      "host": "smtp.example.com",
      "port": 587,
      "encryption": "tls"
    },
    "settings": {
      "daily_send_limit": 150000,
      "hourly_send_limit": 8000,
      "max_connections": 10
    },
    "reputation": {
      "current_score": 85,
      "warmup_enabled": true,
      "warmup_status": "completed"
    },
    "stats": {
      "total_sent": 5000000,
      "delivered_count": 4950000,
      "bounced_count": 50000,
      "delivery_rate": "99.0%"
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 1.4 获取发件渠道列表
**接口**: `GET /api/channels/list`

**描述**: 获取租户的发件渠道列表

**请求参数**:
```
?page=1&size=20&type=smtp&status=active
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "channels": [
      {
        "id": 1,
        "name": "主发件渠道",
        "type": "smtp",
        "status": "active",
        "current_score": 85,
        "delivery_rate": "99.0%",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 5,
    "page": 1,
    "size": 20
  }
}
```

### 1.5 测试发件渠道
**接口**: `POST /api/channels/test`

**描述**: 测试发件渠道连接和配置

**请求参数**:
```json
{
  "id": 1,
  "test_emails": ["<EMAIL>", "<EMAIL>"],
  "test_type": "connection_and_send"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "test_id": "test_12345",
    "connection_test": "passed",
    "authentication_test": "passed",
    "send_test": "passed",
    "test_emails": [
      {
        "email": "<EMAIL>",
        "status": "sent",
        "message_id": "msg_12345"
      }
    ]
  }
}
```

## 2. 投递优化

### 2.1 设置发送速率
**接口**: `POST /api/channels/rate-limit/set`

**描述**: 设置发件渠道的发送速率限制

**请求参数**:
```json
{
  "channel_id": 1,
  "rate_limits": {
    "daily": 150000,
    "hourly": 8000,
    "per_minute": 150,
    "per_second": 3
  },
  "throttling": {
    "enabled": true,
    "algorithm": "token_bucket",
    "burst_size": 50
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true,
    "effective_limits": {
      "daily": 150000,
      "hourly": 8000,
      "per_minute": 150,
      "per_second": 3
    }
  }
}
```

### 2.2 获取发送速率状态
**接口**: `GET /api/channels/rate-limit/status`

**描述**: 获取发件渠道的发送速率状态

**请求参数**:
```
?channel_id=1
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "channel_id": 1,
    "current_usage": {
      "daily": 45000,
      "hourly": 1200,
      "per_minute": 25,
      "per_second": 1
    },
    "limits": {
      "daily": 150000,
      "hourly": 8000,
      "per_minute": 150,
      "per_second": 3
    },
    "remaining": {
      "daily": 105000,
      "hourly": 6800,
      "per_minute": 125,
      "per_second": 2
    },
    "throttling": {
      "enabled": true,
      "current_delay_ms": 0,
      "queue_length": 0
    }
  }
}
```

### 2.3 设置发送时间优化
**接口**: `POST /api/channels/send-time/set`

**描述**: 设置发件渠道的发送时间优化策略

**请求参数**:
```json
{
  "channel_id": 1,
  "optimization": {
    "enabled": true,
    "strategy": "engagement_based",
    "time_windows": [
      {
        "start_hour": 9,
        "end_hour": 11,
        "weight": 0.4
      },
      {
        "start_hour": 14,
        "end_hour": 16,
        "weight": 0.3
      },
      {
        "start_hour": 19,
        "end_hour": 21,
        "weight": 0.3
      }
    ],
    "timezone_handling": "recipient_local",
    "max_delay_hours": 24
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true,
    "optimization_enabled": true
  }
}
```

## 3. 声誉管理

### 3.1 获取声誉状态
**接口**: `GET /api/channels/reputation/status`

**描述**: 获取发件渠道的声誉状态

**请求参数**:
```
?channel_id=1&time_range=30d
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "channel_id": 1,
    "current_score": 85,
    "score_trend": "improving",
    "score_history": [
      {
        "date": "2024-01-01",
        "score": 85,
        "change": "+2"
      }
    ],
    "metrics": {
      "delivery_rate": "99.0%",
      "bounce_rate": "0.8%",
      "complaint_rate": "0.02%",
      "spam_trap_hits": 0,
      "blacklist_status": "clean"
    },
    "warnings": [],
    "recommendations": [
      "继续监控退信率",
      "优化发送时间",
      "清理无效地址"
    ]
  }
}
```

### 3.2 设置声誉监控
**接口**: `POST /api/channels/reputation/monitoring/set`

**描述**: 设置声誉监控规则和告警

**请求参数**:
```json
{
  "channel_id": 1,
  "monitoring": {
    "enabled": true,
    "alerts": {
      "bounce_rate_threshold": 2.0,
      "complaint_rate_threshold": 0.1,
      "delivery_rate_threshold": 95.0,
      "score_drop_threshold": 10
    },
    "notifications": {
      "email": ["<EMAIL>"],
      "webhook": "https://webhook.example.com/reputation",
      "slack": "#email-alerts"
    },
    "auto_actions": {
      "pause_on_critical": true,
      "reduce_volume_on_warning": true,
      "auto_warmup_on_improvement": true
    }
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true,
    "monitoring_enabled": true
  }
}
```

### 3.3 获取声誉报告
**接口**: `GET /api/channels/reputation/report`

**描述**: 获取发件渠道的详细声誉报告

**请求参数**:
```
?channel_id=1&time_range=90d&format=pdf
```

**响应**: 返回PDF报告文件或报告数据

## 4. 退信处理

### 4.1 获取退信列表
**接口**: `GET /api/bounces/list`

**描述**: 获取退信记录列表

**请求参数**:
```
?page=1&size=50&channel_id=1&bounce_type=hard&time_range=7d
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "bounces": [
      {
        "id": 1,
        "email": "<EMAIL>",
        "bounce_type": "hard",
        "bounce_reason": "User unknown",
        "bounce_code": "5.1.1",
        "channel_id": 1,
        "campaign_id": 1,
        "message_id": "msg_12345",
        "bounced_at": "2024-01-01T10:00:00Z",
        "processed": false
      }
    ],
    "total": 150,
    "page": 1,
    "size": 50
  }
}
```

### 4.2 处理退信
**接口**: `POST /api/bounces/process`

**描述**: 处理退信记录

**请求参数**:
```json
{
  "bounce_ids": [1, 2, 3],
  "action": "suppress",
  "suppression_duration": "permanent",
  "notes": "硬退信，永久抑制"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "processed": true,
    "processed_count": 3,
    "suppressed_emails": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
  }
}
```

### 4.3 获取退信统计
**接口**: `GET /api/bounces/stats`

**描述**: 获取退信统计信息

**请求参数**:
```
?channel_id=1&time_range=30d&group_by=day
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "overview": {
      "total_bounces": 1500,
      "hard_bounces": 800,
      "soft_bounces": 700,
      "bounce_rate": "1.5%"
    },
    "by_type": {
      "hard": {
        "count": 800,
        "rate": "0.8%",
        "top_reasons": ["User unknown", "Mailbox full", "Domain not found"]
      },
      "soft": {
        "count": 700,
        "rate": "0.7%",
        "top_reasons": ["Temporary failure", "Quota exceeded", "Connection timeout"]
      }
    },
    "time_series": [
      {
        "date": "2024-01-01",
        "total_bounces": 50,
        "hard_bounces": 25,
        "soft_bounces": 25
      }
    ]
  }
}
```

## 5. 投诉处理

### 5.1 获取投诉列表
**接口**: `GET /api/complaints/list`

**描述**: 获取投诉记录列表

**请求参数**:
```
?page=1&size=50&channel_id=1&time_range=7d
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "complaints": [
      {
        "id": 1,
        "email": "<EMAIL>",
        "complaint_type": "spam",
        "complaint_reason": "Not requested",
        "channel_id": 1,
        "campaign_id": 1,
        "message_id": "msg_12345",
        "complained_at": "2024-01-01T10:00:00Z",
        "processed": false
      }
    ],
    "total": 25,
    "page": 1,
    "size": 50
  }
}
```

### 5.2 处理投诉
**接口**: `POST /api/complaints/process`

**描述**: 处理投诉记录

**请求参数**:
```json
{
  "complaint_ids": [1, 2],
  "action": "suppress",
  "suppression_duration": "permanent",
  "response": "已处理投诉，永久抑制相关地址",
  "notes": "用户明确表示不接收邮件"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "processed": true,
    "processed_count": 2,
    "suppressed_emails": ["<EMAIL>", "<EMAIL>"]
  }
}
```

## 6. 预热管理

### 6.1 创建预热计划
**接口**: `POST /api/channels/warmup/create`

**描述**: 为发件渠道创建预热计划

**请求参数**:
```json
{
  "channel_id": 1,
  "plan_name": "新渠道预热计划",
  "start_date": "2024-01-01",
  "duration_days": 30,
  "initial_volume": 100,
  "daily_increase": 500,
    "max_daily_volume": 15000,
    "target_volume": 50000,
    "quality_metrics": {
      "min_delivery_rate": 95.0,
      "max_bounce_rate": 2.0,
      "max_complaint_rate": 0.1
    },
    "pause_conditions": {
      "bounce_rate_threshold": 3.0,
      "complaint_rate_threshold": 0.2,
      "delivery_rate_threshold": 90.0
    }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "plan_id": 1,
    "plan_name": "新渠道预热计划",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 6.2 获取预热状态
**接口**: `GET /api/channels/warmup/status`

**描述**: 获取发件渠道的预热状态

**请求参数**:
```
?channel_id=1
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "channel_id": 1,
    "warmup_enabled": true,
    "current_plan": {
      "plan_id": 1,
      "plan_name": "新渠道预热计划",
      "status": "active",
      "start_date": "2024-01-01",
      "current_day": 15,
      "current_volume": 7600,
      "target_volume": 15000
    },
    "progress": {
      "days_completed": 15,
      "days_remaining": 15,
      "volume_completed": 7600,
      "volume_remaining": 7400,
      "completion_percentage": 50.7
    },
    "metrics": {
      "current_delivery_rate": "97.5%",
      "current_bounce_rate": "1.8%",
      "current_complaint_rate": "0.05%",
      "reputation_score": 75
    }
  }
}
```

## 7. 错误码定义

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 40001 | 渠道名称已存在 | 使用不同的名称 |
| 40002 | SMTP配置无效 | 检查SMTP服务器配置 |
| 40003 | 发送限制设置无效 | 检查速率限制配置 |
| 40004 | 预热计划配置错误 | 检查预热参数 |
| 40005 | 声誉监控配置无效 | 检查监控规则 |
| 40006 | 渠道状态不允许操作 | 检查当前状态 |
| 50001 | 渠道测试失败 | 检查网络和认证 |
| 50002 | 预热计划创建失败 | 稍后重试 |
| 50003 | 数据库操作失败 | 稍后重试 |

## 8. 性能说明

### 8.1 接口性能指标
- 渠道查询：P95 < 100ms
- 配置更新：P95 < 200ms
- 状态查询：P95 < 150ms
- 统计查询：P95 < 300ms

### 8.2 发送性能
- 单渠道最大并发：100连接
- 单渠道最大吞吐：10000 emails/min
- 预热最大时长：90天
- 声誉评分更新：实时

### 8.3 限制说明
- 单租户最大渠道数：20个
- 单渠道最大日发送：100万
- 预热最大日增量：10000
- 声誉评分范围：0-100

## 9. 最佳实践

### 9.1 渠道配置
- 使用专用的发件IP
- 配置正确的SPF/DKIM记录
- 设置合理的发送速率
- 监控渠道健康状态

### 9.2 声誉管理
- 定期清理无效地址
- 监控关键指标变化
- 及时处理投诉和退信
- 实施渐进式预热

### 9.3 投递优化
- 优化发送时间
- 个性化邮件内容
- 维护干净的地址列表
- 避免垃圾邮件特征

### 9.4 监控告警
- 设置关键指标告警
- 实时监控渠道状态
- 建立应急响应流程
- 定期生成分析报告
