# 分析与报表 API（Analytics）

- 统一响应 { code, message, data, meta }
- 仅 GET/POST

## 1. 总览

### 1.1 概览指标
接口: GET /api/analytics/overview

请求: `?time_range=7d&granularity=day`

响应:
```json
{ "code": 0, "message": "success", "data": { "sent": 120000, "delivered": 118000, "open_rate": "32.1%", "click_rate": "4.8%", "bounce_rate": "1.2%" } }
```

### 1.2 趋势序列
接口: GET /api/analytics/timeseries

请求: `?metric=open_rate&time_range=30d&granularity=day`

响应(示例):
```json
{ "code": 0, "message": "success", "data": { "points": [ { "ts": "2024-01-01", "value": 0.31 } ] } }
```

## 2. 活动分析

### 2.1 活动指标
接口: GET /api/analytics/campaign

请求: `?campaign_id=1&include_variants=true`

响应(示例):
```json
{ "code": 0, "message": "success", "data": { "sent": 15000, "delivered": 14800, "opened": 11800, "clicked": 1800, "unsubscribed": 150, "complained": 5 } }
```

## 3. 渠道分析

### 3.1 渠道指标
接口: GET /api/analytics/channel

请求: `?channel_id=1001&time_range=7d`

响应: `{ "code": 0, "message": "success", "data": { "sent": 50000, "delivery_rate": "98.9%", "open_rate": "30.5%" } }`

## 4. 分群分析

### 4.1 分群维度拆分
接口: POST /api/analytics/breakdown

请求:
```json
{ "dimension": "tag", "time_range": "7d", "top": 10 }
```
响应:
```json
{ "code": 0, "message": "success", "data": { "items": [ { "key": "VIP", "open_rate": "45%", "sent": 10000 } ] } }
```

## 5. 导出报表

### 5.1 异步导出
接口: POST /api/analytics/export

请求:
```json
{ "scope": "campaign", "campaign_id": 1, "format": "csv" }
```
响应:
```json
{ "code": 0, "message": "success", "data": { "export_id": "exp_20240101_001" } }
```

### 5.2 获取导出结果
接口: GET /api/analytics/export/get

请求: `?export_id=exp_20240101_001`

响应:
```json
{ "code": 0, "message": "success", "data": { "status": "ready", "download_url": "https://.../report.csv" } }
```

## 6. 错误码
| 码 | 含义 |
|---|---|
| 43001 | 时间区间不合法 |
| 43002 | 指标不存在 |
| 43003 | 导出任务不存在 |
