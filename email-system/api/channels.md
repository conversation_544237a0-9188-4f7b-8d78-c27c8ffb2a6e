# 发件渠道（Channels）与域名验证 API

- 统一响应：{ code, message, data, meta }
- 仅允许 GET 与 POST；不使用路径参数
- 身份/租户由网关中间件注入

## 1. 渠道管理

### 1.1 创建渠道
接口: POST /api/channels/create

请求:
```json
{
  "name": "营销主通道",
  "provider": "Aliyun",
  "from_name": "营销团队",
  "from_address": "<EMAIL>",
  "reply_to": "<EMAIL>",
  "daily_limit": 20000,
  "monthly_limit": 500000,
  "config": { "send_interval_seconds": 2, "auto_decision": true }
}
```
响应:
```json
{ "code": 0, "message": "success", "data": { "channel_id": 1001 } }
```

### 1.2 更新渠道
接口: POST /api/channels/update

请求:
```json
{ "channel_id": 1001, "name": "营销主通道-更新", "daily_limit": 30000, "config": { "auto_decision": false } }
```
响应: `{ "code": 0, "message": "success" }`

### 1.3 获取渠道详情
接口: GET /api/channels/get

请求: `?channel_id=1001&include_stats=true`

响应(示例):
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "channel_id": 1001,
    "name": "营销主通道",
    "provider": "Aliyun",
    "from_name": "营销团队",
    "from_address": "<EMAIL>",
    "status": "inactive",
    "limits": { "daily": 30000, "monthly": 500000 },
    "config": { "send_interval_seconds": 2, "auto_decision": false },
    "stats": { "sent_today": 0, "sent_this_month": 12000 }
  }
}
```

### 1.4 渠道列表
接口: GET /api/channels/list

请求: `?page=1&size=20&status=inactive&provider=Aliyun`

响应:
```json
{ "code": 0, "message": "success", "data": { "channels": [ { "channel_id": 1001, "name": "营销主通道" } ], "total": 1, "page": 1, "size": 20 } }
```

### 1.5 删除渠道
接口: POST /api/channels/delete

请求: `{ "channel_id": 1001, "force": false }`

响应: `{ "code": 0, "message": "success" }`

## 2. 域名验证（SPF/DKIM/DMARC/Tracking）

### 2.1 提交验证任务
接口: POST /api/channels/domain/verify

请求:
```json
{ "channel_id": 1001, "domain": "example.com", "verify_types": ["SPF", "DKIM", "DMARC", "TRACKING"] }
```
响应: `{ "code": 0, "message": "success", "data": { "task_id": "v-20240101-001" } }`

### 2.2 获取验证结果
接口: GET /api/channels/domain/verify/result

请求: `?task_id=v-20240101-001`

响应:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "domain": "example.com",
    "spf": { "status": "pass", "record": "v=spf1 include:spf.example.com -all" },
    "dkim": { "status": "pass", "selector": "s1", "public_key": "..." },
    "dmarc": { "status": "pass", "record": "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>" },
    "tracking": { "status": "pass", "cname": "trk.example.com" }
  }
}
```

## 3. 预热与速率控制

### 3.1 配置预热计划
接口: POST /api/channels/warmup/config

请求:
```json
{ "channel_id": 1001, "enabled": true, "start_rate_per_min": 200, "daily_increment": 100, "max_rate_per_min": 2000 }
```
响应: `{ "code": 0, "message": "success" }`

### 3.2 设置发送时间表
接口: POST /api/channels/schedule/set

请求:
```json
{ "channel_id": 1001, "timezone": "Asia/Shanghai", "workdays_only": true, "time_windows": [ { "weekday": "Mon-Fri", "from": "09:00", "to": "18:00" } ] }
```
响应: `{ "code": 0, "message": "success" }`

## 4. 错误码
| 码 | 含义 |
|---|---|
| 41001 | 渠道不存在 |
| 41002 | 域名校验失败 |
| 41003 | 预热配置不合法 |
| 41004 | 发送时间表不合法 |
