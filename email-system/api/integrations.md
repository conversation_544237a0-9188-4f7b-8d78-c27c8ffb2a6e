# 集成与 Webhooks API（Integrations & Webhooks）

- 统一响应 { code, message, data, meta }
- 仅 GET/POST

## 1. Webhooks 管理

### 1.1 创建 Webhook
接口: POST /api/webhooks/create

请求:
```json
{ "name": "事件回调", "url": "https://example.com/webhook", "events": ["delivered", "opened", "clicked", "bounced", "complained", "unsubscribed"], "secret": "****", "enabled": true }
```
响应: `{ "code": 0, "message": "success", "data": { "webhook_id": 5001 } }`

### 1.2 更新 Webhook
接口: POST /api/webhooks/update

请求:
```json
{ "webhook_id": 5001, "enabled": false, "events": ["delivered", "opened"] }
```
响应: `{ "code": 0, "message": "success" }`

### 1.3 列表
接口: GET /api/webhooks/list

请求: `?page=1&size=20&enabled=true`

响应: `{ "code": 0, "message": "success", "data": { "items": [ { "id": 5001, "name": "事件回调", "enabled": true } ], "total": 1 } }`

### 1.4 删除
接口: POST /api/webhooks/delete

请求: `{ "webhook_id": 5001 }`

响应: `{ "code": 0, "message": "success" }`

### 1.5 测试回调
接口: POST /api/webhooks/test

请求:
```json
{ "webhook_id": 5001, "event": "delivered", "sample_payload": { "message_id": "m_123", "timestamp": "2024-01-01T00:00:00Z" } }
```
响应: `{ "code": 0, "message": "success", "data": { "status": "sent" } }`

## 2. 追踪域（Tracking Domain）

### 2.1 新增追踪域
接口: POST /api/tracking-domain/add

请求:
```json
{ "domain": "trk.example.com" }
```
响应: `{ "code": 0, "message": "success", "data": { "verify_task_id": "td_20240101_001" } }`

### 2.2 验证追踪域
接口: POST /api/tracking-domain/verify

请求: `{ "task_id": "td_20240101_001" }`

响应: `{ "code": 0, "message": "success", "data": { "status": "pass" } }`

### 2.3 列表
接口: GET /api/tracking-domain/list

响应:
```json
{ "code": 0, "message": "success", "data": { "domains": [ { "domain": "trk.example.com", "status": "pass" } ] } }
```

## 3. 错误码
| 码 | 含义 |
|---|---|
| 46001 | Webhook 地址无效 |
| 46002 | 事件集不合法 |
| 46003 | 验证失败 |
