# 列表与标签模块 API 接口文档

## 模块概述

列表与标签模块负责管理联系人的分组和标记功能，支持静态列表、动态细分、标签管理等，为邮件营销活动提供精准的受众管理能力。

## 接口规范

### 基础响应格式
所有接口遵循统一的响应格式：
```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "errors": [],
  "meta": {}
}
```

### 通用参数
- `tenant_id`: 租户ID（通过请求头或中间件注入）
- `page`: 页码，从1开始
- `size`: 每页大小，默认20，最大100
- `trace_id`: 链路追踪ID（可选）

## 1. 列表管理

### 1.1 创建列表
**接口**: `POST /api/lists/create`

**描述**: 创建新的联系人列表

**请求参数**:
```json
{
  "name": "VIP客户列表",
  "description": "高价值客户群体",
  "type": "static",
  "settings": {
    "allow_duplicates": false,
    "auto_cleanup": true,
    "cleanup_days": 90
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "VIP客户列表",
    "description": "高价值客户群体",
    "type": "static",
    "member_count": 0,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 1.2 更新列表
**接口**: `POST /api/lists/update`

**描述**: 更新列表信息

**请求参数**:
```json
{
  "id": 1,
  "name": "VIP客户列表-更新",
  "description": "更新后的描述",
  "settings": {
    "allow_duplicates": true,
    "auto_cleanup": false
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true
  }
}
```

### 1.3 获取列表详情
**接口**: `GET /api/lists/get`

**描述**: 获取列表详细信息

**请求参数**:
```
?id=1
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "tenant_id": 1001,
    "name": "VIP客户列表",
    "description": "高价值客户群体",
    "type": "static",
    "member_count": 1500,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "settings": {
      "allow_duplicates": false,
      "auto_cleanup": true,
      "cleanup_days": 90
    },
    "stats": {
      "active_members": 1420,
      "suppressed_members": 80,
      "last_activity": "2024-01-01T10:00:00Z"
    }
  }
}
```

### 1.4 获取列表列表
**接口**: `GET /api/lists/list`

**描述**: 获取租户的列表列表

**请求参数**:
```
?page=1&size=20&type=static&search=VIP
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "lists": [
      {
        "id": 1,
        "name": "VIP客户列表",
        "description": "高价值客户群体",
        "type": "static",
        "member_count": 1500,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 25,
    "page": 1,
    "size": 20
  }
}
```

### 1.5 删除列表
**接口**: `DELETE /api/lists/delete`

**描述**: 删除指定的列表

**请求参数**:
```
?id=1&force=false
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "deleted": true,
    "affected_members": 1500
  }
}
```

### 1.6 获取列表成员
**接口**: `GET /api/lists/members`

**描述**: 获取列表中的联系人成员

**请求参数**:
```
?list_id=1&page=1&size=50&status=active
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "members": [
      {
        "id": 12345,
        "email": "<EMAIL>",
        "first_name": "张三",
        "last_name": "李",
        "company": "示例公司",
        "added_at": "2024-01-01T00:00:00Z",
        "status": "active"
      }
    ],
    "total": 1500,
    "page": 1,
    "size": 50
  }
}
```

### 1.7 添加列表成员
**接口**: `POST /api/lists/members/add`

**描述**: 向列表添加联系人

**请求参数**:
```json
{
  "list_id": 1,
  "contact_ids": [12345, 12346, 12347],
  "emails": ["<EMAIL>", "<EMAIL>"],
  "skip_duplicates": true
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "added_count": 5,
    "skipped_count": 2,
    "errors": []
  }
}
```

### 1.8 移除列表成员
**接口**: `POST /api/lists/members/remove`

**描述**: 从列表移除联系人

**请求参数**:
```json
{
  "list_id": 1,
  "contact_ids": [12345, 12346],
  "emails": ["<EMAIL>"]
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "removed_count": 3,
    "errors": []
  }
}
```

## 2. 标签管理

### 2.1 创建标签
**接口**: `POST /api/tags/create`

**描述**: 创建新的标签

**请求参数**:
```json
{
  "name": "VIP客户",
  "color": "#FF6B6B",
  "description": "高价值客户标签",
  "category": "customer_type"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "VIP客户",
    "color": "#FF6B6B",
    "usage_count": 0,
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2.2 更新标签
**接口**: `POST /api/tags/update`

**描述**: 更新标签信息

**请求参数**:
```json
{
  "id": 1,
  "name": "VIP客户-更新",
  "color": "#FF8E8E",
  "description": "更新后的描述"
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true
  }
}
```

### 2.3 获取标签详情
**接口**: `GET /api/tags/get`

**描述**: 获取标签详细信息

**请求参数**:
```
?id=1
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "tenant_id": 1001,
    "name": "VIP客户",
    "color": "#FF6B6B",
    "description": "高价值客户标签",
    "category": "customer_type",
    "usage_count": 1500,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 2.4 获取标签列表
**接口**: `GET /api/tags/list`

**描述**: 获取租户的标签列表

**请求参数**:
```
?page=1&size=50&category=customer_type&search=VIP
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "tags": [
      {
        "id": 1,
        "name": "VIP客户",
        "color": "#FF6B6B",
        "category": "customer_type",
        "usage_count": 1500,
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 25,
    "page": 1,
    "size": 50
  }
}
```

### 2.5 删除标签
**接口**: `DELETE /api/tags/delete`

**描述**: 删除指定的标签

**请求参数**:
```
?id=1&force=false
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "deleted": true,
    "affected_contacts": 1500
  }
}
```

### 2.6 获取标签使用统计
**接口**: `GET /api/tags/stats`

**描述**: 获取标签的使用统计信息

**请求参数**:
```
?tag_id=1&time_range=30d
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "tag_id": 1,
    "tag_name": "VIP客户",
    "total_usage": 1500,
    "usage_trend": [
      {
        "date": "2024-01-01",
        "usage_count": 45,
        "new_assignments": 12
      }
    ],
    "contact_distribution": {
      "by_status": {
        "active": 1420,
        "suppressed": 80
      },
      "by_language": {
        "zh-CN": 1200,
        "en-US": 300
      }
    }
  }
}
```

## 3. 细分管理

### 3.1 创建细分
**接口**: `POST /api/segments/create`

**描述**: 创建新的动态细分

**请求参数**:
```json
{
  "name": "活跃客户细分",
  "description": "最近30天有活动的客户",
  "type": "dynamic",
  "rule_tree_json": {
    "operator": "AND",
    "rules": [
      {
        "field": "last_activity_at",
        "operator": "within_days",
        "value": 30
      },
      {
        "field": "status",
        "operator": "equals",
        "value": "active"
      }
    ]
  },
  "refresh_policy": {
    "type": "schedule",
    "cron": "0 2 * * *",
    "timezone": "Asia/Shanghai"
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "name": "活跃客户细分",
    "type": "dynamic",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3.2 更新细分
**接口**: `POST /api/segments/update`

**描述**: 更新细分配置

**请求参数**:
```json
{
  "id": 1,
  "name": "活跃客户细分-更新",
  "rule_tree_json": {
    "operator": "AND",
    "rules": [
      {
        "field": "last_activity_at",
        "operator": "within_days",
        "value": 60
      }
    ]
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "updated": true
  }
}
```

### 3.3 获取细分详情
**接口**: `GET /api/segments/get`

**描述**: 获取细分详细信息

**请求参数**:
```
?id=1
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 1,
    "tenant_id": 1001,
    "name": "活跃客户细分",
    "description": "最近30天有活动的客户",
    "type": "dynamic",
    "rule_tree_json": {
      "operator": "AND",
      "rules": [
        {
          "field": "last_activity_at",
          "operator": "within_days",
          "value": 30
        }
      ]
    },
    "refresh_policy": {
      "type": "schedule",
      "cron": "0 2 * * *",
      "timezone": "Asia/Shanghai"
    },
    "status": "active",
    "snapshot_id": 100,
    "member_count": 2500,
    "last_refreshed": "2024-01-01T02:00:00Z",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3.4 获取细分列表
**接口**: `GET /api/segments/list`

**描述**: 获取租户的细分列表

**请求参数**:
```
?page=1&size=20&type=dynamic&status=active
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "segments": [
      {
        "id": 1,
        "name": "活跃客户细分",
        "type": "dynamic",
        "status": "active",
        "member_count": 2500,
        "last_refreshed": "2024-01-01T02:00:00Z",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 15,
    "page": 1,
    "size": 20
  }
}
```

### 3.5 删除细分
**接口**: `DELETE /api/segments/delete`

**描述**: 删除指定的细分

**请求参数**:
```
?id=1
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "deleted": true
  }
}
```

### 3.6 手动刷新细分
**接口**: `POST /api/segments/refresh`

**描述**: 手动触发细分刷新

**请求参数**:
```json
{
  "id": 1,
  "force": false
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "job_id": "seg_12345",
    "status": "queued",
    "estimated_duration": "2-5分钟"
  }
}
```

### 3.7 获取细分成员
**接口**: `GET /api/segments/members`

**描述**: 获取细分中的联系人成员

**请求参数**:
```
?segment_id=1&page=1&size=50&snapshot_id=100
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "members": [
      {
        "id": 12345,
        "email": "<EMAIL>",
        "first_name": "张三",
        "last_name": "李",
        "company": "示例公司",
        "matched_rules": [
          "last_activity_at within 30 days",
          "status equals active"
        ]
      }
    ],
    "total": 2500,
    "page": 1,
    "size": 50,
    "snapshot_id": 100,
    "snapshot_time": "2024-01-01T02:00:00Z"
  }
}
```

### 3.8 细分预览
**接口**: `POST /api/segments/preview`

**描述**: 预览细分规则匹配的结果

**请求参数**:
```json
{
  "rule_tree_json": {
    "operator": "AND",
    "rules": [
      {
        "field": "last_activity_at",
        "operator": "within_days",
        "value": 30
      }
    ]
  },
  "sample_size": 100
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "estimated_count": 2500,
    "sample_members": [
      {
        "id": 12345,
        "email": "<EMAIL>",
        "first_name": "张三",
        "matched_rules": [
          "last_activity_at within 30 days"
        ]
      }
    ],
    "rule_analysis": {
      "complexity": "simple",
      "estimated_performance": "fast"
    }
  }
}
```

## 4. 批量操作

### 4.1 批量标签操作
**接口**: `POST /api/bulk/tags`

**描述**: 基于筛选条件批量操作标签

**请求参数**:
```json
{
  "scope": {
    "type": "filter",
    "filter": {
      "lists": [1],
      "attributes": {
        "company": "示例公司"
      }
    }
  },
  "operation": "add_tags",
  "params": {
    "tags": ["VIP", "企业客户"]
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "task_id": "bulk_12345",
    "estimated_affected": 1500,
    "status": "queued",
    "progress_url": "/api/bulk/status?task_id=bulk_12345"
  }
}
```

### 4.2 批量列表操作
**接口**: `POST /api/bulk/lists`

**描述**: 基于筛选条件批量操作列表

**请求参数**:
```json
{
  "scope": {
    "type": "filter",
    "filter": {
      "tags": ["VIP"],
      "attributes": {
        "preferred_language": "zh-CN"
      }
    }
  },
  "operation": "add_to_lists",
  "params": {
    "lists": [1, 2]
  }
}
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "task_id": "bulk_12346",
    "estimated_affected": 800,
    "status": "queued",
    "progress_url": "/api/bulk/status?task_id=bulk_12346"
  }
}
```

### 4.3 查询批量任务状态
**接口**: `GET /api/bulk/status`

**描述**: 查询批量操作任务的状态

**请求参数**:
```
?task_id=bulk_12345
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "task_id": "bulk_12345",
    "operation": "add_tags",
    "status": "processing",
    "progress": 65,
    "affected_count": 975,
    "estimated_total": 1500,
    "errors": [],
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:05:00Z"
  }
}
```

## 5. 统计和报表

### 5.1 列表统计
**接口**: `GET /api/lists/stats`

**描述**: 获取列表的统计信息

**请求参数**:
```
?time_range=30d&list_id=1
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "overview": {
      "total_lists": 25,
      "static_lists": 20,
      "dynamic_lists": 5,
      "total_members": 50000
    },
    "list_performance": [
      {
        "list_id": 1,
        "name": "VIP客户列表",
        "member_count": 1500,
        "growth_rate": "5.2%",
        "engagement_rate": "78.5%"
      }
    ],
    "member_trends": {
      "daily_growth": [
        {
          "date": "2024-01-01",
          "new_members": 45,
          "total_members": 49555
        }
      ]
    }
  }
}
```

### 5.2 标签统计
**接口**: `GET /api/tags/stats`

**描述**: 获取标签的统计信息

**请求参数**:
```
?time_range=30d&category=customer_type
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "overview": {
      "total_tags": 50,
      "total_usage": 100000,
      "most_used_tag": "VIP客户",
      "usage_count": 1500
    },
    "tag_performance": [
      {
        "tag_id": 1,
        "name": "VIP客户",
        "usage_count": 1500,
        "growth_rate": "8.5%",
        "engagement_rate": "82.3%"
      }
    ],
    "usage_trends": {
      "daily_usage": [
        {
          "date": "2024-01-01",
          "new_assignments": 45,
          "total_usage": 99555
        }
      ]
    }
  }
}
```

### 5.3 细分统计
**接口**: `GET /api/segments/stats`

**描述**: 获取细分的统计信息

**请求参数**:
```
?time_range=30d&type=dynamic
```

**响应**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "overview": {
      "total_segments": 15,
      "dynamic_segments": 10,
      "static_segments": 5,
      "total_members": 30000
    },
    "segment_performance": [
      {
        "segment_id": 1,
        "name": "活跃客户细分",
        "type": "dynamic",
        "member_count": 2500,
        "refresh_frequency": "daily",
        "last_refresh": "2024-01-01T02:00:00Z"
      }
    ],
    "refresh_stats": {
      "successful_refreshes": 28,
      "failed_refreshes": 2,
      "average_duration": "3.2分钟"
    }
  }
}
```

## 6. 错误码定义

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 40001 | 列表名称已存在 | 使用不同的名称 |
| 40002 | 标签名称已存在 | 使用不同的名称 |
| 40003 | 细分名称已存在 | 使用不同的名称 |
| 40004 | 规则配置无效 | 检查规则语法 |
| 40005 | 列表类型不支持 | 使用正确的类型 |
| 40006 | 刷新策略配置错误 | 检查cron表达式 |
| 40007 | 批量操作范围过大 | 缩小筛选范围 |
| 50001 | 细分刷新失败 | 检查规则配置 |
| 50002 | 批量任务创建失败 | 稍后重试 |
| 50003 | 数据库操作失败 | 稍后重试 |

## 7. 性能说明

### 7.1 接口性能指标
- 列表查询：P95 < 100ms
- 标签查询：P95 < 80ms
- 细分查询：P95 < 150ms
- 批量操作：P95 < 500ms

### 7.2 细分性能
- 简单规则：< 1秒
- 复杂规则：1-10秒
- 大范围筛选：10-60秒

### 7.3 限制说明
- 单列表最大成员数：100万
- 单标签最大使用数：50万
- 单细分最大成员数：100万
- 规则嵌套深度：最大5层

## 8. 最佳实践

### 8.1 列表管理
- 合理命名，便于识别和管理
- 定期清理无效成员
- 使用描述字段说明用途
- 避免创建过多小列表

### 8.2 标签管理
- 建立标签分类体系
- 使用有意义的颜色区分
- 定期清理未使用的标签
- 避免创建过多相似标签

### 8.3 细分管理
- 优先使用简单规则
- 合理设置刷新频率
- 监控细分性能
- 定期优化复杂规则

### 8.4 批量操作
- 使用筛选条件而非全量操作
- 合理设置批次大小
- 监控任务进度
- 及时处理错误
