# 邮件营销系统 API 接口设计

## 概述

本文档描述了邮件营销系统的完整API接口设计，系统采用模块化架构，每个功能模块提供独立的API接口集合。

## 系统架构

### 技术栈
- **后端框架**: Go + Gin
- **数据库**: MySQL 8.0 + Redis
- **消息队列**: Redis Streams
- **对象存储**: S3/OSS
- **认证授权**: JWT + 外部用户系统

### 设计原则
- **RESTful API**: 遵循REST设计规范
- **统一响应格式**: 所有接口使用标准响应结构
- **多租户隔离**: 通过tenant_id实现租户间数据隔离
- **异步处理**: 大文件导入等耗时操作使用队列异步处理
- **幂等性**: 关键操作支持幂等执行
- **版本控制**: 支持API版本管理和向后兼容

## 模块概览

### 1. 联系人模块 (`contacts.md`)
**功能**: 联系人管理、批量导入、自定义字段、备注、时间线
**核心特性**:
- 支持CSV/XLSX大文件导入（百万级）
- 智能字段映射和预校验
- 异步导入处理，支持错误回放
- 多语言和地理位置支持
- 联系人备注和时间线事件

**主要接口**:
- 联系人CRUD操作
- 批量导入（plan/preview/import/status）
- 列表和标签管理
- 联系人备注
- 时间线查询

### 2. 列表与标签模块 (`lists-tags.md`)
**功能**: 静态列表、动态细分、标签管理、批量操作、统一人群圈选
**核心特性**:
- 支持静态列表和动态细分
- 基于规则的智能人群圈选
- 标签分类体系和颜色管理（支持规则圈选和静态列表）
- 基于筛选条件的批量操作
- 细分自动刷新和性能优化
- 统一管理人群圈选结果，为活动、A/B测试、自动化旅程提供目标受众选择

**主要接口**:
- 列表管理（创建/更新/删除/成员管理）
- 标签管理（创建/更新/删除/统计）
- 细分管理（规则配置/自动刷新/成员查询）
- 批量操作（标签/列表批量赋值）
- 统计报表

### 3. 模板与内容模块 (`templates.md`)
**功能**: 邮件模板、版本控制、多语言、审批流程
**核心特性**:
- 支持MJML、HTML、纯文本格式
- 完整的版本控制和发布流程
- 多语言内容管理
- 模板审批工作流
- 可复用片段管理

**主要接口**:
- 模板管理（创建/更新/删除/复制）
- 版本控制（创建/发布/回滚）
- 多语言变体管理
- 审批流程（提交/处理/查询）
- 预览和测试发送

### 4. 活动与A/B测试模块 (`campaigns.md`)
**功能**: 邮件活动、A/B测试、发送策略、受众管理、临时圈选
**核心特性**:
- 支持定时、触发、循环活动
- 完整的A/B测试框架
- 智能发送时间优化
- 多语言活动策略
- 实时发送状态监控
- 支持创建时嵌入圈选页面，自动创建标签并绑定关系

### 5. 自动化旅程模块 (`journeys.md`)
**功能**: 客户旅程设计、触发规则、自动化流程、标签受众选择
**核心特性**:
- 可视化旅程设计器
- 基于事件的触发机制
- 条件分支和延迟设置
- 旅程性能分析
- 实时执行监控
- 通过标签选择目标受众，支持规则圈选和静态列表

### 6. 投递与可达性模块 (`deliverability.md`)
**功能**: 发件渠道、投递优化、声誉管理
**核心特性**:
- 多渠道发件支持
- 智能发送速率控制
- 投递声誉监控
- 退信处理和抑制管理
- 预热和验证流程

### 7. 追踪与分析模块 (`analytics.md`)
**功能**: 邮件追踪、行为分析、报表统计
**核心特性**:
- 打开、点击、退订追踪
- 实时数据统计
- 多维度分析报表
- 数据导出和API
- 自定义指标配置

### 8. 表单与偏好中心模块 (`forms.md`)
**功能**: 订阅表单、偏好管理、同意记录
**核心特性**:
- 拖拽式表单设计器
- 多语言表单支持
- 偏好中心管理
- GDPR合规同意记录
- 表单转化分析

### 9. 开放平台与集成模块 (`integrations.md`)
**功能**: Webhook、API集成、第三方连接
**核心特性**:
- RESTful API接口
- Webhook事件推送
- 第三方平台集成
- 数据同步和映射
- 集成监控和日志

### 10. 运维与管理模块 (`operations.md`)
**功能**: 系统监控、日志管理、配置管理
**核心特性**:
- 实时系统监控
- 日志收集和分析
- 性能指标统计
- 告警和通知
- 配置热更新

### 11. 扩展功能模块 (`extensions.md`)
**功能**: 插件系统、自定义扩展、高级功能
**核心特性**:
- 插件架构设计
- 自定义工作流
- 高级分析功能
- 第三方服务集成
- 扩展开发SDK

### 12. 计费与额度模块 (`billing.md`)
**功能**: 套餐管理、用量统计、配额控制
**核心特性**:
- 灵活的套餐配置
- 实时用量统计
- 智能配额控制
- 计费规则引擎
- 支付集成

### 13. 安全与合规模块 (`security.md`)
**功能**: 数据加密、访问控制、合规检查
**核心特性**:
- 端到端数据加密
- 细粒度权限控制
- GDPR合规检查
- 安全审计日志
- 威胁检测

## 通用接口规范

### 响应格式
```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "errors": [],
  "meta": {}
}
```

### 分页参数
- `page`: 页码，从1开始
- `size`: 每页大小，默认20，最大100

### 通用查询参数
- `tenant_id`: 租户ID（中间件注入）
- `trace_id`: 链路追踪ID
- `time_range`: 时间范围（如：7d, 30d, 90d）
- `status`: 状态筛选
- `search`: 关键词搜索

### 错误处理
- `code`: 0表示成功，非0表示错误
- `message`: 错误描述
- `errors`: 详细错误信息数组
- `meta`: 元数据信息

## 认证与授权

### 认证方式
- JWT Token认证
- API Key认证（可选）
- OAuth 2.0集成（可选）

### 权限控制
- 基于角色的访问控制（RBAC）
- 租户级数据隔离
- 功能级权限控制
- 资源级权限控制

### 安全措施
- HTTPS强制使用
- 请求频率限制
- 输入验证和过滤
- SQL注入防护
- XSS防护

## 性能与扩展性

### 性能指标
- API响应时间：P95 < 200ms
- 并发处理：支持1000+并发请求
- 数据处理：百万级记录查询优化

### 扩展策略
- 水平扩展：多实例部署
- 数据库分片：按租户分片
- 缓存策略：Redis多级缓存
- 队列处理：异步任务处理

### 监控告警
- 实时性能监控
- 错误率告警
- 资源使用监控
- 业务指标监控

## 开发指南

### 环境要求
- Go 1.21+
- MySQL 8.0+
- Redis 6.0+
- 对象存储服务

### 本地开发
```bash
# 克隆项目
git clone <repository-url>

# 安装依赖
go mod download

# 配置环境变量
cp .env.example .env

# 启动服务
go run cmd/main.go
```

### API测试
- 使用Postman或类似工具
- 提供完整的测试用例
- 支持自动化测试
- 性能测试脚本

### 文档维护
- 接口变更及时更新
- 提供示例代码
- 错误码说明完整
- 最佳实践指南

## 版本管理

### 版本策略
- 主版本号：不兼容的API变更
- 次版本号：向后兼容的功能新增
- 修订版本号：向后兼容的问题修正

### 兼容性
- 支持多版本API并存
- 提供版本迁移指南
- 废弃接口提前通知
- 向后兼容性保证

## 支持与反馈

### 技术支持
- 技术文档：完整的中英文文档
- 示例代码：各语言SDK示例
- 社区支持：技术论坛和问答
- 专业支持：企业级技术支持

### 反馈渠道
- GitHub Issues：问题反馈
- 邮件支持：技术支持邮箱
- 在线客服：实时技术支持
- 用户社区：用户交流平台

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 核心功能模块完成
- 基础API接口实现
- 文档和示例完善

### 后续版本
- 持续功能优化
- 性能提升
- 新特性添加
- 安全加固

---

本文档将随着系统开发持续更新，请关注最新版本获取完整信息。
