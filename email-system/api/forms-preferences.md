# 表单与偏好中心 API（Forms & Preferences）

- 统一响应 { code, message, data, meta }
- 仅 GET/POST

## 1. 订阅表单

### 1.1 创建表单
接口: POST /api/forms/create

请求:
```json
{ "name": "网站订阅", "fields": [ { "name": "email", "type": "email", "required": true }, { "name": "first_name", "type": "text" } ], "double_opt_in": true }
```
响应: `{ "code": 0, "message": "success", "data": { "form_id": 2001 } }`

### 1.2 获取表单
接口: GET /api/forms/get

请求: `?form_id=2001`

响应(示例): `{ "code": 0, "message": "success", "data": { "form": { "id": 2001, "name": "网站订阅", "fields": [ ... ] } } }`

### 1.3 提交表单（公开）
接口: POST /api/forms/submit

请求:
```json
{ "form_id": 2001, "payload": { "email": "<EMAIL>", "first_name": "张三" } }
```
响应: `{ "code": 0, "message": "success", "data": { "pending_confirm": true } }`

### 1.4 确认订阅（DOI）
接口: POST /api/forms/confirm

请求:
```json
{ "token": "signed-token" }
```
响应: `{ "code": 0, "message": "success" }`

## 2. 偏好中心

### 2.1 获取偏好项定义
接口: GET /api/preferences/definition

响应(示例):
```json
{ "code": 0, "message": "success", "data": { "topics": ["产品更新", "活动优惠"], "channels": ["email", "sms"] } }
```

### 2.2 获取用户偏好
接口: GET /api/preferences/get

请求: `?contact_id=12345`

响应(示例):
```json
{ "code": 0, "message": "success", "data": { "contact_id": 12345, "topics": ["产品更新"], "channels": { "email": true } } }
```

### 2.3 更新用户偏好
接口: POST /api/preferences/update

请求:
```json
{ "contact_id": 12345, "topics": ["产品更新"], "channels": { "email": true, "sms": false } }
```
响应: `{ "code": 0, "message": "success" }`

## 3. 错误码
| 码 | 含义 |
|---|---|
| 44001 | 表单不存在 |
| 44002 | 提交数据不合法 |
| 44003 | 确认令牌无效 |
