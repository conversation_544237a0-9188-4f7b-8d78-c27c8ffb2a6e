# 抑制与同意管理 API（Suppression & Consent）

- 统一响应 { code, message, data, meta }
- 仅 GET/POST

## 1. 抑制列表（退订/硬退/投诉/手动）

### 1.1 添加到抑制列表
接口: POST /api/suppression/add

请求:
```json
{ "email": "<EMAIL>", "reason": "unsubscribe", "note": "用户主动退订" }
```
响应: `{ "code": 0, "message": "success" }`

### 1.2 从抑制列表移除
接口: POST /api/suppression/remove

请求:
```json
{ "email": "<EMAIL>" }
```
响应: `{ "code": 0, "message": "success" }`

### 1.3 查询抑制列表
接口: GET /api/suppression/list

请求: `?page=1&size=50&reason=unsubscribe&keyword=user`

响应(示例):
```json
{ "code": 0, "message": "success", "data": { "items": [ { "email": "<EMAIL>", "reason": "unsubscribe", "created_at": "..." } ], "total": 1 } }
```

## 2. 同意与偏好（Consent & Preferences）

### 2.1 更新同意/偏好
接口: POST /api/consent/update

请求:
```json
{
  "contact_id": 12345,
  "consent": { "marketing": true, "transactional": true, "newsletter": false, "channels": { "email": true, "sms": false } },
  "preferences": { "locale": "zh-CN", "topics": ["产品更新", "活动优惠"] }
}
```
响应: `{ "code": 0, "message": "success" }`

### 2.2 获取同意/偏好
接口: GET /api/consent/get

请求: `?contact_id=12345`

响应:
```json
{ "code": 0, "message": "success", "data": { "contact_id": 12345, "consent": { "marketing": true, "transactional": true, "newsletter": false, "channels": { "email": true } }, "preferences": { "locale": "zh-CN", "topics": ["产品更新"] } } }
```

## 3. 公开链接（退订/管理偏好）

### 3.1 退订
接口: POST /api/public/unsubscribe

请求:
```json
{ "email": "<EMAIL>", "token": "signed-token" }
```
响应: `{ "code": 0, "message": "success" }`

### 3.2 提交偏好中心
接口: POST /api/public/preferences/submit

请求:
```json
{ "email": "<EMAIL>", "token": "signed-token", "preferences": { "topics": ["产品更新"] } }
```
响应: `{ "code": 0, "message": "success" }`

## 4. 错误码
| 码 | 含义 |
|---|---|
| 42001 | 邮箱格式错误 |
| 42002 | 不在联系人库中 |
| 42003 | 签名无效或过期 |
| 42004 | 偏好数据不合法 |
