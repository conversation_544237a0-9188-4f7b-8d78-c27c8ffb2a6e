# Email System 服务端

基于Users模块规范的Email System后端服务，实现联系人管理、标签管理、人群圈选等核心功能。

## 功能特性

### 联系人管理
- ✅ 联系人CRUD操作
- ✅ 批量创建和更新联系人
- ✅ 联系人状态管理
- ✅ 自定义属性支持
- ✅ 多语言和地理位置支持
- ✅ 高级搜索和过滤

### 标签管理
- ✅ 标签CRUD操作
- ✅ 规则圈选和静态列表两种类型
- ✅ 标签分配和批量操作
- ✅ 标签刷新和合并
- ✅ 热门标签和最近使用标签

### 人群圈选
- ✅ 动态和静态人群圈选
- ✅ 复杂规则构建
- ✅ 预览和预估功能
- ✅ 异步重建任务
- ✅ 导出功能

## 项目结构

```
email-system/
├── cmd/                    # 应用入口
│   └── main.go
├── configs/                # 配置文件
│   └── app.yaml
├── internal/               # 内部代码
│   ├── application/        # 应用层
│   │   ├── contact/        # 联系人应用服务
│   │   ├── tag/           # 标签应用服务
│   │   └── segment/       # 人群圈选应用服务
│   ├── domain/            # 领域层
│   │   ├── contact/       # 联系人领域
│   │   ├── tag/          # 标签领域
│   │   └── segment/      # 人群圈选领域
│   ├── infrastructure/    # 基础设施层
│   │   ├── container/     # 依赖注入容器
│   │   └── persistence/   # 数据持久化
│   └── interfaces/        # 接口层
│       └── http/          # HTTP接口
├── pkg/                   # 公共包
│   └── config/           # 配置管理
└── go.mod
```

## 技术栈

- Go 1.21+
- Gin Web 框架
- GORM 数据库 ORM
- Clean Architecture + DDD 架构
- MySQL 8.0+
- Redis 6.0+ (可选)

## 开发规范

- 遵循 Clean Architecture 分层：接口层、应用层、领域层、基础设施层
- 仅允许 GET/POST 方法；路由不使用 path 参数
- 统一响应结构，严禁返回内部错误细节
- internalAppId 做隔离、tenantId 做归属
- 构造函数注入，禁止运行时 nil 检查
- 所有 DB 调用使用 WithContext(ctx) 且无自动预加载
- 关联查询批量加载，避免 N+1
- DTO 校验 + 统一错误翻译
- 外部调用具备超时/重试/熔断/限流
- 日志结构化 + trace 贯穿 + 敏感字段脱敏

## 启动服务

```bash
# 编译
go build -o email-system ./cmd

# 运行
./email-system
```

## 配置

配置文件位于 `configs/` 目录下，支持从 Nacos 配置中心加载配置。

## API 文档

TODO: 添加 API 文档链接

## 错误码

邮件系统错误码区间：
- 邮件账户/发件配置：120000-120099
- 邮件模板：120100-120199
- 邮件发送/任务：120200-120299
- 附件与存储：120300-120399
- 第三方服务：120800-120899
- 系统错误：120900-120999
