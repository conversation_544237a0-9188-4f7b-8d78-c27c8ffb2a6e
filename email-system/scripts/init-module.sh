#!/bin/bash

# Navigate to the email-system directory
cd "$(dirname "$0")/.."

# Run go mod tidy to update dependencies
echo "Running go mod tidy..."
go mod tidy

# Create a simple build script
echo "Creating build script..."
cat > scripts/build.sh << 'EOF'
#!/bin/bash

# Navigate to the email-system directory
cd "$(dirname "$0")/.."

# Build the binary
echo "Building email-system binary..."
go build -o bin/email-system ./cmd

echo "Build complete. Binary is at bin/email-system"
EOF

# Make the build script executable
chmod +x scripts/build.sh

# Create a simple run script
echo "Creating run script..."
cat > scripts/run.sh << 'EOF'
#!/bin/bash

# Navigate to the email-system directory
cd "$(dirname "$0")/.."

# Run the binary
echo "Running email-system..."
./bin/email-system
EOF

# Make the run script executable
chmod +x scripts/run.sh

echo "Initialization complete!"
