# 资源API分离设计方案文档

## 1. 设计概述

### 1.1 设计目标
基于现有的resource设计进行优化，将页面、API、按钮等不同类型的资源进行合理分离，提升系统的可维护性和扩展性。

### 1.2 核心改动
1. **resource表拆分**：将API相关字段从resource表中分离出来，创建专门的api_resource表
2. **增加权限关联**：为resource和api_resource表增加permission_id字段
3. **利用现有机制**：使用resource_app_assignments表记录租户拥有哪些资源

## 2. 数据库设计变更

### 2.1 修改resource表
```sql
-- 1. 为resource表添加permission_id字段
ALTER TABLE `resource`
ADD COLUMN `permission_id` bigint DEFAULT NULL COMMENT '关联的权限ID' AFTER `resource_type`,
ADD KEY `idx_resource_permission_id` (`permission_id`),
ADD CONSTRAINT `fk_resource_permission_id` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE SET NULL;

-- 2. 移除API相关字段（数据迁移后）
-- 注意：这些字段将在数据迁移完成后移除
-- service_name, request_type, response_type, api_method, content_type
```

### 2.1.1 权限关联设计说明
- **resource表的permission_id**：用于记录页面、菜单、按钮等资源使用的权限
- **api_resource表的permission_id**：用于记录API接口使用的权限
- **权限继承机制**：子资源可以继承父资源的权限，也可以单独设置权限
- **权限优先级**：子资源的权限优先级高于父资源权限

### 2.1.2 resource_app_assignments表的作用
现有的`resource_app_assignments`表将继续发挥重要作用：
- **租户资源控制**：记录哪些租户可以访问哪些资源
- **应用资源分配**：控制不同应用对资源的访问权限
- **资源生命周期管理**：通过expires_at字段控制资源访问的有效期
- **激活状态控制**：通过is_active字段控制资源的启用/禁用状态

### 2.2 创建api_resource表
```sql
CREATE TABLE `api_resource` (
  `id` bigint NOT NULL COMMENT '分布式ID',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL DEFAULT '0' COMMENT '内部应用ID',
  `name` varchar(100) NOT NULL COMMENT 'API资源名称',
  `description` varchar(255) DEFAULT NULL COMMENT 'API资源描述',
  `permission_id` bigint DEFAULT NULL COMMENT '关联的权限ID',
  `service_name` varchar(100) NOT NULL COMMENT '应用服务名称',
  `api_method` varchar(10) NOT NULL COMMENT 'HTTP方法: GET, POST, PUT, DELETE, PATCH',
  `path` varchar(255) NOT NULL COMMENT 'API路径',
  `request_type` varchar(50) DEFAULT 'json' COMMENT '请求数据类型',
  `response_type` varchar(50) DEFAULT 'json' COMMENT '响应数据类型',
  `content_type` varchar(100) DEFAULT NULL COMMENT 'Content-Type',
  `is_public` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否公开访问',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_api_resource_tenant_app` (`tenant_id`, `internal_app_id`),
  KEY `idx_api_resource_permission_id` (`permission_id`),
  KEY `idx_api_resource_service_method` (`service_name`, `api_method`),
  KEY `idx_api_resource_path` (`path`),
  KEY `idx_api_resource_name` (`name`),
  UNIQUE KEY `uk_api_resource_tenant_path_method` (`tenant_id`, `path`, `api_method`),
  CONSTRAINT `fk_api_resource_permission_id` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API资源表';
```

### 2.3 创建resource_api_relations表（页面与API关联表）
```sql
CREATE TABLE `resource_api_relations` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `resource_id` bigint NOT NULL COMMENT '页面资源ID',
  `api_resource_id` bigint NOT NULL COMMENT 'API资源ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_resource_api_tenant` (`resource_id`),
  KEY `idx_resource_api_api_id` (`api_resource_id`),
  UNIQUE KEY `uk_resource_api_relation` ( `resource_id`, `api_resource_id`),
  CONSTRAINT `fk_resource_api_resource_id` FOREIGN KEY (`resource_id`) REFERENCES `resource` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_resource_api_api_resource_id` FOREIGN KEY (`api_resource_id`) REFERENCES `api_resource` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='页面与API关联表';
```

### 2.4 数据迁移脚本
```sql
-- 1. 迁移现有API资源到新的api_resource表
INSERT INTO api_resource (
    id, tenant_id, internal_app_id, name, display_name, description,
    service_name, api_method, path, request_type, response_type,
    content_type, is_public, status, created_at, updated_at
)
SELECT
    id, tenant_id, internal_app_id, name, display_name, description,
    service_name, api_method, path, request_type, response_type,
    content_type, is_public, 'active' as status, created_at, updated_at
FROM resource
WHERE resource_type = 'api'
AND service_name IS NOT NULL;

-- 2. 删除原有的API类型资源记录（因为已经迁移到api_resource表）
DELETE FROM resource WHERE resource_type = 'api';

-- 3. 清理resource表中的API相关字段（在确认迁移成功后执行）
ALTER TABLE `resource`
DROP COLUMN `service_name`,
DROP COLUMN `request_type`,
DROP COLUMN `response_type`,
DROP COLUMN `api_method`,
DROP COLUMN `content_type`;

-- 4. 更新resource_type的约束（移除api类型）
-- ALTER TABLE `resource`
-- ADD CONSTRAINT `chk_resource_type` CHECK (`resource_type` IN ('menu', 'page', 'button'));
```

## 3. 后端代码修改范围

### 3.1 领域层修改
**需要修改的文件：**
- `users/internal/domain/user/entity/resource.go` - 移除API相关字段
- `users/internal/domain/user/entity/api_resource.go` - 新增API资源实体
- `users/internal/domain/user/entity/resource_api_relation.go` - 新增页面API关联实体
- `users/internal/domain/user/repository/api_resource_repository.go` - 新增API资源仓储接口
- `users/internal/domain/user/repository/resource_api_relation_repository.go` - 新增关联关系仓储接口

**新增实体结构：**
```go
// APIResource API资源实体
type APIResource struct {
    ID            int64
    TenantID      int64
    InternalAppID int64
    Name          string
    DisplayName   string
    Description   string
    PermissionID  *int64
    ServiceName   string
    APIMethod     string
    Path          string
    RequestType   string
    ResponseType  string
    ContentType   string
    IsPublic      bool
    Status        string
    CreatedAt     time.Time
    UpdatedAt     time.Time
    DeletedAt     *time.Time
}

// ResourceAPIRelation 页面与API关联实体
type ResourceAPIRelation struct {
    ID            int64
    TenantID      int64
    InternalAppID int64
    ResourceID    int64
    APIResourceID int64
    IsRequired    bool
    Description   string
    CreatedAt     time.Time
    UpdatedAt     time.Time
}
```

### 3.2 基础设施层修改
**需要修改的文件：**
- `users/internal/infrastructure/persistence/models/resource.go` - 移除API相关字段
- `users/internal/infrastructure/persistence/models/api_resource.go` - 新增API资源模型
- `users/internal/infrastructure/persistence/models/resource_api_relation.go` - 新增关联关系模型
- `users/internal/infrastructure/persistence/repository/api_resource_repository_impl.go` - 新增API资源仓储实现
- `users/internal/infrastructure/persistence/repository/resource_api_relation_repository_impl.go` - 新增关联关系仓储实现

### 3.3 应用层修改
**需要修改的文件：**
- `users/internal/application/user/dto/resource_dto.go` - 修改资源DTO，移除API字段
- `users/internal/application/user/dto/api_resource_dto.go` - 新增API资源DTO
- `users/internal/application/user/dto/resource_api_relation_dto.go` - 新增关联关系DTO
- `users/internal/application/user/service/resource_application_service.go` - 修改资源服务
- `users/internal/application/user/service/api_resource_application_service.go` - 新增API资源服务
- `users/internal/application/user/service/resource_api_relation_service.go` - 新增关联关系服务

### 3.4 接口层修改
**需要修改的文件：**
- `users/internal/interfaces/http/handlers/resource_handler.go` - 修改资源处理器
- `users/internal/interfaces/http/handlers/api_resource_handler.go` - 新增API资源处理器
- `users/internal/interfaces/http/handlers/resource_api_relation_handler.go` - 新增关联关系处理器
- `users/internal/interfaces/http/routes/routes.go` - 添加API资源和关联关系路由

## 4. 前端代码修改范围

### 4.1 服务层修改
**需要修改的文件：**
- `frontend/src/services/resource.ts` - 移除API相关字段和接口
- `frontend/src/services/api_resource.ts` - 新增API资源服务

**新增接口结构：**
```typescript
export interface APIResource {
    id: number;
    tenant_id: number;
    internal_app_id: number;
    name: string;
    display_name: string;
    description?: string;
    permission_id?: number;
    service_name: string;
    api_method: string;
    path: string;
    request_type: string;
    response_type: string;
    content_type?: string;
    is_public: boolean;
    status: string;
    created_at: string;
    updated_at: string;
}

export interface ResourceAPIRelation {
    id: number;
    tenant_id: number;
    internal_app_id: number;
    resource_id: number;
    api_resource_id: number;
    is_required: boolean;
    description?: string;
    created_at: string;
    updated_at: string;
}
```

### 4.2 页面组件修改
**需要修改的文件：**
- `frontend/src/pages/resource/ResourcePage.tsx` - 移除API相关表单字段和显示
- `frontend/src/pages/api-management/ApiManagementPage.tsx` - 修改为使用新的API资源服务
- `frontend/src/components/resource/ResourceForm.tsx` - 移除API相关表单项

### 4.3 工具配置修改
**需要修改的文件：**
- `frontend/src/utils/request.ts` - 添加API资源相关的接口端点

## 5. 实施步骤

### 5.1 第一阶段：数据库准备
1. 创建api_resource表
2. 为resource表添加permission_id字段
3. 执行数据迁移脚本
4. 验证数据完整性

### 5.2 第二阶段：后端实现
1. 创建API资源相关的领域实体和仓储
2. 实现API资源的应用服务
3. 创建API资源的HTTP处理器和路由
4. 修改现有资源服务，移除API相关逻辑
5. 更新单元测试

### 5.3 第三阶段：前端适配
1. 创建API资源服务
2. 修改资源管理页面，移除API相关功能
3. 更新API管理页面，使用新的API资源服务
4. 测试前端功能完整性

### 5.4 第四阶段：清理和优化
1. 移除resource表中的API相关字段
2. 清理无用的代码和注释
3. 更新文档和接口说明
4. 性能测试和优化

### 5.5 实施时间表
| 阶段 | 任务 | 预计时间 | 负责人 | 检查点 |
|------|------|----------|--------|--------|
| 第一阶段 | 数据库设计和迁移 | 3天 | 后端开发 | 数据迁移验证完成 |
| 第二阶段 | 后端代码实现 | 5天 | 后端开发 | API接口测试通过 |
| 第三阶段 | 前端代码适配 | 4天 | 前端开发 | 页面功能测试通过 |
| 第四阶段 | 清理和优化 | 2天 | 全栈开发 | 性能测试达标 |
| **总计** | **完整实施** | **14天** | **开发团队** | **生产环境部署** |

### 5.6 检查清单

#### 数据库检查清单
- [ ] api_resource表创建成功
- [ ] resource表添加permission_id字段
- [ ] 外键约束创建成功
- [ ] 索引创建完成
- [ ] 数据迁移脚本执行成功
- [ ] 数据完整性验证通过
- [ ] 备份数据可恢复

#### 后端检查清单
- [ ] APIResource实体创建
- [ ] APIResourceRepository接口和实现
- [ ] APIResourceService服务层实现
- [ ] APIResourceHandler控制器实现
- [ ] 路由配置更新
- [ ] 权限验证逻辑实现
- [ ] 单元测试编写完成
- [ ] 集成测试通过

#### 前端检查清单
- [ ] APIResource TypeScript接口定义
- [ ] API资源服务函数实现
- [ ] 资源管理页面修改完成
- [ ] API管理页面更新完成
- [ ] 权限配置界面实现
- [ ] 错误处理机制完善
- [ ] 用户体验测试通过

## 6. 风险评估与应对

### 6.1 数据迁移风险
**风险**：数据迁移过程中可能出现数据丢失或不一致
**应对**：
- 在生产环境执行前，先在测试环境完整验证
- 备份原始数据
- 分步骤执行，每步都进行验证

### 6.2 兼容性风险
**风险**：现有API调用可能因为接口变更而失效
**应对**：
- 保持向后兼容，逐步废弃旧接口
- 提供迁移指南和过渡期支持
- 充分的集成测试

### 6.3 性能影响
**风险**：表拆分可能影响查询性能
**应对**：
- 合理设计索引
- 优化查询语句
- 监控性能指标

## 7. 详细技术实现

### 7.1 后端核心代码示例

#### API资源实体定义
```go
// APIResource API资源实体
type APIResource struct {
    ID            int64     `json:"id"`
    TenantID      int64     `json:"tenant_id"`
    InternalAppID int64     `json:"internal_app_id"`
    Name          string    `json:"name"`
    DisplayName   string    `json:"display_name"`
    Description   string    `json:"description"`
    PermissionID  *int64    `json:"permission_id"`
    ServiceName   string    `json:"service_name"`
    APIMethod     string    `json:"api_method"`
    Path          string    `json:"path"`
    RequestType   string    `json:"request_type"`
    ResponseType  string    `json:"response_type"`
    ContentType   string    `json:"content_type"`
    IsPublic      bool      `json:"is_public"`
    Status        string    `json:"status"`
    CreatedAt     time.Time `json:"created_at"`
    UpdatedAt     time.Time `json:"updated_at"`
    DeletedAt     *time.Time `json:"deleted_at"`
}

// ResourceAPIRelation 页面与API关联实体
type ResourceAPIRelation struct {
    ID            int64     `json:"id"`
    TenantID      int64     `json:"tenant_id"`
    InternalAppID int64     `json:"internal_app_id"`
    ResourceID    int64     `json:"resource_id"`
    APIResourceID int64     `json:"api_resource_id"`
    IsRequired    bool      `json:"is_required"`
    Description   string    `json:"description"`
    CreatedAt     time.Time `json:"created_at"`
    UpdatedAt     time.Time `json:"updated_at"`
}
```

#### API资源服务接口
```go
type APIResourceService interface {
    CreateAPIResource(ctx context.Context, req *dto.CreateAPIResourceRequest) (*entity.APIResource, error)
    UpdateAPIResource(ctx context.Context, req *dto.UpdateAPIResourceRequest) error
    DeleteAPIResource(ctx context.Context, id int64, tenantID int64) error
    GetAPIResource(ctx context.Context, id int64, tenantID int64) (*entity.APIResource, error)
    ListAPIResources(ctx context.Context, req *dto.ListAPIResourcesRequest) (*dto.APIResourceListResponse, error)
    AssignPermissionToAPIResource(ctx context.Context, apiResourceID int64, permissionID int64, tenantID int64) error
}

// 页面与API关联服务接口
type ResourceAPIRelationService interface {
    CreateRelation(ctx context.Context, req *dto.CreateResourceAPIRelationRequest) (*entity.ResourceAPIRelation, error)
    DeleteRelation(ctx context.Context, resourceID int64, apiResourceID int64, tenantID int64) error
    GetResourceAPIs(ctx context.Context, resourceID int64, tenantID int64) ([]*entity.APIResource, error)
    GetAPIResources(ctx context.Context, apiResourceID int64, tenantID int64) ([]*entity.Resource, error)
    BatchAssignAPIsToResource(ctx context.Context, resourceID int64, apiResourceIDs []int64, tenantID int64) error
}
```

### 7.2 前端核心代码示例

#### API资源服务
```typescript
// API资源相关接口
export const API_ENDPOINTS = {
  API_RESOURCE: {
    LIST: `${API_CONFIG.PREFIXES.RESOURCE}/api-resource/list`,
    CREATE: `${API_CONFIG.PREFIXES.RESOURCE}/api-resource/create`,
    GET: `${API_CONFIG.PREFIXES.RESOURCE}/api-resource/get`,
    UPDATE: `${API_CONFIG.PREFIXES.RESOURCE}/api-resource/update`,
    DELETE: `${API_CONFIG.PREFIXES.RESOURCE}/api-resource/delete`,
    ASSIGN_PERMISSION: `${API_CONFIG.PREFIXES.RESOURCE}/api-resource/assign-permission`,
  },
};

// API资源服务函数
export async function createAPIResource(data: CreateAPIResourceRequest): Promise<ApiResponse<APIResource>> {
    return await apiService.post<APIResource>(API_ENDPOINTS.API_RESOURCE.CREATE, data);
}

export async function listAPIResources(params: ListAPIResourcesRequest): Promise<ApiResponse<APIResource[]>> {
    return await apiService.post<APIResource[]>(API_ENDPOINTS.API_RESOURCE.LIST, params);
}
```

### 7.3 权限验证流程设计

#### 权限验证逻辑
```go
// 权限验证服务
type PermissionValidationService struct {
    resourceRepo    ResourceRepository
    apiResourceRepo APIResourceRepository
    permissionRepo  PermissionRepository
    assignmentRepo  ResourceAppAssignmentRepository
}

// 验证用户对资源的访问权限
func (s *PermissionValidationService) ValidateResourceAccess(
    ctx context.Context,
    userID int64,
    tenantID int64,
    resourceID int64,
    action string,
) (bool, error) {
    // 1. 检查资源是否分配给租户的应用
    hasAssignment, err := s.assignmentRepo.HasActiveAssignment(ctx, tenantID, resourceID)
    if err != nil || !hasAssignment {
        return false, err
    }

    // 2. 获取资源关联的权限
    resource, err := s.resourceRepo.GetByID(ctx, resourceID)
    if err != nil {
        return false, err
    }

    // 3. 验证用户是否具有相应权限
    if resource.PermissionID != nil {
        return s.checkUserPermission(ctx, userID, *resource.PermissionID, action)
    }

    // 4. 如果资源没有直接权限，检查父资源权限（权限继承）
    return s.checkParentResourcePermission(ctx, userID, resource.ParentID, action)
}
```

### 7.4 resource与api_resource关联设计

#### 关联关系说明
```sql
-- 通过resource_api_relations表实现多对多关联
-- 这种设计的优势：
-- 1. 一个API可以被多个页面使用（复用性）
-- 2. 一个页面可以关联多个API（完整性）
-- 3. API资源独立管理，不依赖特定页面
-- 4. 支持关联关系的扩展属性（是否必需、描述等）
```

#### 关联查询示例
```go
// 获取页面及其关联的API资源
type PageWithAPIs struct {
    Resource     *entity.Resource     `json:"resource"`
    APIResources []*entity.APIResource `json:"api_resources"`
    Relations    []*entity.ResourceAPIRelation `json:"relations"`
}

// 查询页面及其API资源
func (r *ResourceRepository) GetPageWithAPIs(ctx context.Context, pageID int64) (*PageWithAPIs, error) {
    // 1. 查询页面资源
    resource, err := r.GetByID(ctx, pageID)
    if err != nil {
        return nil, err
    }

    // 2. 查询关联关系
    relations, err := r.relationRepo.GetByResourceID(ctx, pageID)
    if err != nil {
        return nil, err
    }

    // 3. 查询关联的API资源
    var apiResources []*entity.APIResource
    for _, relation := range relations {
        apiResource, err := r.apiResourceRepo.GetByID(ctx, relation.APIResourceID)
        if err != nil {
            continue
        }
        apiResources = append(apiResources, apiResource)
    }

    return &PageWithAPIs{
        Resource:     resource,
        APIResources: apiResources,
        Relations:    relations,
    }, nil
}

// 获取API资源被哪些页面使用
func (r *APIResourceRepository) GetAPIUsagePages(ctx context.Context, apiResourceID int64) ([]*entity.Resource, error) {
    relations, err := r.relationRepo.GetByAPIResourceID(ctx, apiResourceID)
    if err != nil {
        return nil, err
    }

    var resources []*entity.Resource
    for _, relation := range relations {
        resource, err := r.resourceRepo.GetByID(ctx, relation.ResourceID)
        if err != nil {
            continue
        }
        resources = append(resources, resource)
    }

    return resources, nil
}
```

#### 权限验证机制
```go
// API资源权限验证逻辑
func (s *PermissionService) ValidateAPIAccess(ctx context.Context, userID int64, apiResourceID int64) (bool, error) {
    apiResource, err := s.apiResourceRepo.GetByID(ctx, apiResourceID)
    if err != nil {
        return false, err
    }

    // 1. 优先检查API资源自身的权限
    if apiResource.PermissionID != nil {
        return s.checkUserPermission(ctx, userID, *apiResource.PermissionID)
    }

    // 2. 如果API资源没有独立权限，检查所有关联页面的权限（任一页面有权限即可访问）
    relations, err := s.relationRepo.GetByAPIResourceID(ctx, apiResourceID)
    if err != nil {
        return false, err
    }

    for _, relation := range relations {
        pageResource, err := s.resourceRepo.GetByID(ctx, relation.ResourceID)
        if err != nil {
            continue
        }

        if pageResource.PermissionID != nil {
            hasPermission, err := s.checkUserPermission(ctx, userID, *pageResource.PermissionID)
            if err == nil && hasPermission {
                return true, nil
            }
        }
    }

    // 3. 都没有权限配置，默认拒绝访问
    return false, nil
}
```

### 7.5 前端交互设计考虑

#### 是否需要修改前端交互
基于现有代码分析，前端交互**需要适度修改**，但可以保持用户体验的连续性：

#### 修改方案
1. **资源管理页面**：
   - 保持现有的树形结构展示
   - 移除API类型资源的创建入口
   - 为页面类型资源添加"关联API"功能
   - 显示页面关联的API列表

2. **API管理页面**：
   - 修改为独立的API资源管理
   - 显示API被哪些页面使用
   - 支持API的独立权限配置
   - 保持现有的CRUD操作界面

3. **新增关联管理功能**：
   - 页面详情中可以添加/移除关联的API
   - API详情中可以查看被哪些页面使用
   - 支持批量关联操作

#### 前端交互流程
```typescript
// 页面资源管理流程
1. 用户在资源管理页面创建/编辑页面资源
2. 可以为页面资源配置权限
3. 可以在页面详情中关联多个API资源
4. 查看页面关联的所有API列表

// API资源管理流程
1. 用户在API管理页面独立创建API资源
2. 可以为API资源单独配置权限
3. 可以查看API被哪些页面使用
4. 支持API的复用和独立管理

// 关联管理流程
1. 在页面详情中，可以搜索并添加API关联
2. 可以设置API是否为必需的
3. 可以批量添加/移除API关联
4. 支持关联关系的描述说明
```

#### 用户界面调整
```typescript
// 资源表单组件调整
interface ResourceFormProps {
  resourceType: 'menu' | 'page' | 'button'; // 移除'api'类型
  // ... 其他属性
}

// 新增API资源表单组件
interface APIResourceFormProps {
  permissionId?: number; // 可选的独立权限ID
  // ... API相关属性，不再需要resourceId
}

// 新增关联管理组件
interface ResourceAPIRelationProps {
  resourceId: number; // 页面资源ID
  onRelationChange: (apiResourceIds: number[]) => void;
}
```

### 7.6 数据库索引优化
```sql
-- API资源表的关键索引
CREATE INDEX idx_api_resource_tenant_app ON api_resource(tenant_id, internal_app_id);
CREATE INDEX idx_api_resource_permission_id ON api_resource(permission_id);
CREATE INDEX idx_api_resource_service_method ON api_resource(service_name, api_method);
CREATE INDEX idx_api_resource_path ON api_resource(path);
CREATE INDEX idx_api_resource_name ON api_resource(name);
CREATE INDEX idx_api_resource_status ON api_resource(status);

-- resource表的权限索引
CREATE INDEX idx_resource_permission_id ON resource(permission_id);

-- resource_api_relations表的关键索引
CREATE INDEX idx_resource_api_tenant ON resource_api_relations(tenant_id, resource_id);
CREATE INDEX idx_resource_api_api_id ON resource_api_relations(api_resource_id);
CREATE INDEX idx_resource_api_app ON resource_api_relations(internal_app_id);

-- resource_app_assignments表的优化索引
CREATE INDEX idx_assignment_tenant_resource ON resource_app_assignments(tenant_id, resource_id, is_active);
CREATE INDEX idx_assignment_app_active ON resource_app_assignments(internal_app_id, is_active);
```

## 8. 总结

本设计方案通过以下核心设计实现了需求：

### 8.1 核心实现
1. **resource表拆分**：将API相关字段分离到api_resource表，保持resource表的简洁性
2. **权限关联**：为resource和api_resource表都增加permission_id字段，实现精确权限控制
3. **租户资源管理**：继续使用resource_app_assignments表记录租户拥有的资源

### 8.2 关联设计
- **多对多关系**：通过resource_api_relations表实现页面与API的多对多关联
- **API复用性**：一个API可以被多个页面使用，提升资源利用率
- **独立管理**：API资源独立管理，不依赖特定页面
- **权限灵活性**：API资源可以有独立权限，也可以继承关联页面的权限
- **统一控制**：通过resource_app_assignments表统一管理所有资源的租户分配

### 8.3 前端交互
- **适度调整**：保持现有用户体验，仅调整资源类型管理方式
- **功能增强**：增加页面与API的关联管理功能，支持API复用
- **独立管理**：API资源独立管理，支持查看使用情况
- **向后兼容**：确保现有功能正常运行，平滑过渡

该方案通过多对多关联设计，既满足了架构优化的需求，又实现了API资源的高复用性，同时保持了系统的稳定性和用户体验的连续性。
