# 多应用管理系统设计文档

## 1. 问题分析

### 1.1 当前架构问题
- **AppID限制**：系统通过`usercontext`获取`internal_app_id`，在数据库层面自动过滤数据
- **跨应用访问受限**：管理后台无法直接访问和管理其他应用的数据
- **权限边界固化**：当前权限模型基于单一应用，无法实现跨应用管理

### 1.2 业务需求
- 管理后台需要能够管理前端应用的数据（用户、权限、配置等）
- 保持应用间数据隔离和安全边界
- 不需要跨应用用户管理（接口层面无交集）

## 2. 核心设计方案

### 2.1 应用管理关系模型

#### 2.1.1 应用管理关系表
```sql
-- 应用管理关系表
CREATE TABLE app_management_relations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL COMMENT '租户ID',
    manager_app_id BIGINT NOT NULL COMMENT '管理应用ID', 
    target_app_id BIGINT NOT NULL COMMENT '被管理应用ID',
    management_scope JSON COMMENT '管理范围：resources, users, permissions, configs',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_manager_target (tenant_id, manager_app_id, target_app_id),
    FOREIGN KEY (manager_app_id) REFERENCES applications(internal_app_id),
    FOREIGN KEY (target_app_id) REFERENCES applications(internal_app_id),
    INDEX idx_manager_app (manager_app_id),
    INDEX idx_target_app (target_app_id)
);

-- 初始化管理关系数据
INSERT INTO app_management_relations (tenant_id, manager_app_id, target_app_id, management_scope) 
SELECT 
    a1.tenant_id,
    a1.internal_app_id as manager_app_id,
    a2.internal_app_id as target_app_id,
    JSON_ARRAY('resources', 'users', 'permissions', 'configs') as management_scope
FROM applications a1
CROSS JOIN applications a2
WHERE a1.app_name LIKE '%admin%' 
  AND a2.app_name NOT LIKE '%admin%'
  AND a1.tenant_id = a2.tenant_id;
```

### 2.2 应用上下文管理服务

#### 2.2.1 应用上下文切换服务
```go
// AppContextService 应用上下文管理服务
type AppContextService struct {
    appRepo               repository.ApplicationRepository
    appManagementRepo     repository.AppManagementRepository
    logger                logiface.Logger
}

// AppContext 应用上下文
type AppContext struct {
    CurrentAppID    int64   `json:"current_app_id"`
    TargetAppID     *int64  `json:"target_app_id,omitempty"`
    TenantID        int64   `json:"tenant_id"`
    UserID          int64   `json:"user_id"`
    ManagementScope []string `json:"management_scope,omitempty"`
}

// SwitchAppContext 切换应用上下文
func (s *AppContextService) SwitchAppContext(
    ctx context.Context, 
    userID int64, 
    currentAppID int64, 
    targetAppID int64,
) (*AppContext, error) {
    // 1. 验证管理关系
    relation, err := s.appManagementRepo.GetManagementRelation(ctx, currentAppID, targetAppID)
    if err != nil {
        return nil, fmt.Errorf("failed to get management relation: %w", err)
    }
    if relation == nil {
        return nil, fmt.Errorf("no management relation found")
    }
    
    // 2. 检查用户权限
    userInfo, _ := usercontext.GetUserInfo(ctx)
    if userInfo.UserID != userID {
        return nil, fmt.Errorf("user context mismatch")
    }
    
    // 3. 构建新的应用上下文
    appCtx := &AppContext{
        CurrentAppID:    currentAppID,
        TargetAppID:     &targetAppID,
        TenantID:        userInfo.TenantID,
        UserID:          userID,
        ManagementScope: relation.ManagementScope,
    }
    
    s.logger.Info(ctx, "App context switched",
        logiface.Int64("user_id", userID),
        logiface.Int64("current_app_id", currentAppID),
        logiface.Int64("target_app_id", targetAppID))
    
    return appCtx, nil
}

// CreateAppContextMiddleware 创建应用上下文中间件
func (s *AppContextService) CreateAppContextMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 从请求头或参数中获取目标应用ID
        targetAppIDStr := c.GetHeader("X-Target-App-ID")
        if targetAppIDStr == "" {
            targetAppIDStr = c.Query("target_app_id")
        }
        
        if targetAppIDStr != "" {
            targetAppID, err := strconv.ParseInt(targetAppIDStr, 10, 64)
            if err == nil {
                // 将目标应用ID存储到上下文中
                c.Set("target_app_id", targetAppID)
            }
        }
        
        c.Next()
    }
}
```

### 2.3 增强的仓储层

#### 2.3.1 应用感知的仓储接口
```go
// AppAwareRepository 应用感知的仓储基础接口
type AppAwareRepository interface {
    // WithAppContext 设置应用上下文
    WithAppContext(appCtx *AppContext) AppAwareRepository
    
    // GetEffectiveAppID 获取有效的应用ID
    GetEffectiveAppID(ctx context.Context) int64
}

// 增强的资源仓储接口
type ResourceRepository interface {
    AppAwareRepository
    
    // 现有方法...
    GetResourceTreeByLevels(ctx context.Context, maxDepth int, resourceType *value_object.ResourceType, targetAppID *int64) ([]entity.Resource, error)
    
    // 新增跨应用方法
    GetCrossAppResourceTree(ctx context.Context, managerAppID, targetAppID int64, maxDepth int, resourceType *value_object.ResourceType) ([]entity.Resource, error)
    GetManagedApps(ctx context.Context, managerAppID int64) ([]entity.Application, error)
}
```

#### 2.3.2 增强的资源仓储实现
```go
// ResourceRepositoryImpl 增强实现
type ResourceRepositoryImpl struct {
    db      *gorm.DB
    logger  logiface.Logger
    appCtx  *AppContext  // 应用上下文
}

// WithAppContext 设置应用上下文
func (r *ResourceRepositoryImpl) WithAppContext(appCtx *AppContext) AppAwareRepository {
    return &ResourceRepositoryImpl{
        db:     r.db,
        logger: r.logger,
        appCtx: appCtx,
    }
}

// GetEffectiveAppID 获取有效的应用ID
func (r *ResourceRepositoryImpl) GetEffectiveAppID(ctx context.Context) int64 {
    // 1. 优先使用应用上下文中的目标应用ID
    if r.appCtx != nil && r.appCtx.TargetAppID != nil {
        return *r.appCtx.TargetAppID
    }
    
    // 2. 从gin上下文获取
    if c, ok := ctx.(*gin.Context); ok {
        if targetAppID, exists := c.Get("target_app_id"); exists {
            if appID, ok := targetAppID.(int64); ok {
                return appID
            }
        }
    }
    
    // 3. 从usercontext获取（兜底）
    if appID, exists := usercontext.GetInternalAppID(ctx); exists {
        return appID
    }
    
    return 0
}

// GetCrossAppResourceTree 跨应用资源树查询
func (r *ResourceRepositoryImpl) GetCrossAppResourceTree(
    ctx context.Context, 
    managerAppID, targetAppID int64, 
    maxDepth int, 
    resourceType *value_object.ResourceType,
) ([]entity.Resource, error) {
    tenantID, _ := usercontext.GetTenantID(ctx)
    
    // 1. 验证管理关系
    var relation models.AppManagementRelation
    err := r.db.WithContext(ctx).
        Where("tenant_id = ? AND manager_app_id = ? AND target_app_id = ? AND status = ?", 
              tenantID, managerAppID, targetAppID, "active").
        First(&relation).Error
    if err != nil {
        return nil, fmt.Errorf("no management relation found: %w", err)
    }
    
    // 2. 检查管理范围
    if !r.hasManagementScope(relation.ManagementScope, "resources") {
        return nil, fmt.Errorf("insufficient management scope for resources")
    }
    
    // 3. 查询目标应用的资源
    return r.getResourceTreeByApp(ctx, targetAppID, maxDepth, resourceType)
}

// getResourceTreeByApp 按应用查询资源树
func (r *ResourceRepositoryImpl) getResourceTreeByApp(
    ctx context.Context, 
    appID int64, 
    maxDepth int, 
    resourceType *value_object.ResourceType,
) ([]entity.Resource, error) {
    tenantID, _ := usercontext.GetTenantID(ctx)
    
    var allResources []entity.Resource
    
    // 查询根节点
    var rootResources []models.ResourceModel
    rootQuery := r.db.WithContext(ctx).
        Where("tenant_id = ? AND internal_app_id = ? AND parent_id = 0", tenantID, appID)
    
    if resourceType != nil {
        rootQuery = rootQuery.Where("resource_type = ?", *resourceType)
    }
    
    err := rootQuery.Order("sort_order ASC, id ASC").Find(&rootResources).Error
    if err != nil {
        return nil, err
    }
    
    // 转换为领域实体
    for _, model := range rootResources {
        allResources = append(allResources, *model.ToDomainEntity())
    }
    
    // 如果需要查询子节点（简化实现，实际应递归查询）
    if maxDepth > 1 && len(rootResources) > 0 {
        var rootIDs []int64
        for _, root := range rootResources {
            rootIDs = append(rootIDs, root.ID)
        }
        
        var childResources []models.ResourceModel
        childQuery := r.db.WithContext(ctx).
            Where("tenant_id = ? AND internal_app_id = ? AND parent_id IN ?", tenantID, appID, rootIDs)
        
        if resourceType != nil {
            childQuery = childQuery.Where("resource_type = ?", *resourceType)
        }
        
        err = childQuery.Order("parent_id ASC, sort_order ASC, id ASC").Find(&childResources).Error
        if err != nil {
            return nil, err
        }
        
        for _, model := range childResources {
            allResources = append(allResources, *model.ToDomainEntity())
        }
    }
    
    return allResources, nil
}

// hasManagementScope 检查管理范围
func (r *ResourceRepositoryImpl) hasManagementScope(managementScope interface{}, scope string) bool {
    if managementScope == nil {
        return false
    }
    
    // 解析JSON数组
    var scopes []string
    if err := json.Unmarshal([]byte(fmt.Sprintf("%v", managementScope)), &scopes); err != nil {
        return false
    }
    
    for _, s := range scopes {
        if s == scope {
            return true
        }
    }
    return false
}
```

### 2.4 应用服务层增强

#### 2.4.1 跨应用资源管理服务
```go
// CrossAppResourceService 跨应用资源管理服务
type CrossAppResourceService struct {
    resourceRepo      repository.ResourceRepository
    appContextService *AppContextService
    logger           logiface.Logger
}

// GetManagedAppResourceTree 获取被管理应用的资源树
func (s *CrossAppResourceService) GetManagedAppResourceTree(
    ctx context.Context, 
    req *dto.CrossAppResourceTreeRequest,
) ([]*dto.ResourceTreeNode, error) {
    userInfo, _ := usercontext.GetUserInfo(ctx)
    
    // 1. 切换应用上下文
    appCtx, err := s.appContextService.SwitchAppContext(
        ctx, userInfo.UserID, req.ManagerAppID, req.TargetAppID)
    if err != nil {
        return nil, fmt.Errorf("failed to switch app context: %w", err)
    }
    
    // 2. 使用增强的仓储查询
    resourceRepo := s.resourceRepo.WithAppContext(appCtx).(repository.ResourceRepository)
    
    var resourceType *value_object.ResourceType
    if req.ResourceType != "" {
        rt := value_object.ResourceType(req.ResourceType)
        if rt.IsValid() {
            resourceType = &rt
        }
    }
    
    // 3. 查询跨应用资源树
    resources, err := resourceRepo.GetCrossAppResourceTree(
        ctx, req.ManagerAppID, req.TargetAppID, req.MaxDepth, resourceType)
    if err != nil {
        return nil, fmt.Errorf("failed to get cross app resource tree: %w", err)
    }
    
    // 4. 构建树形结构
    tree := s.buildResourceTree(ctx, resources, req.MaxDepth)
    
    s.logger.Info(ctx, "Cross app resource tree retrieved",
        logiface.Int64("manager_app_id", req.ManagerAppID),
        logiface.Int64("target_app_id", req.TargetAppID),
        logiface.Int("count", len(tree)))
    
    return tree, nil
}

// GetManagedApps 获取当前用户可管理的应用列表
func (s *CrossAppResourceService) GetManagedApps(
    ctx context.Context, 
    managerAppID int64,
) ([]*dto.ManagedAppResponse, error) {
    userInfo, _ := usercontext.GetUserInfo(ctx)
    
    // 查询管理关系
    managedApps, err := s.resourceRepo.GetManagedApps(ctx, managerAppID)
    if err != nil {
        return nil, fmt.Errorf("failed to get managed apps: %w", err)
    }
    
    var response []*dto.ManagedAppResponse
    for _, app := range managedApps {
        response = append(response, &dto.ManagedAppResponse{
            InternalAppID:   app.InternalAppID,
            AppName:        app.AppName,
            AppID:          app.AppID,
            DisplayName:    app.DisplayName,
            Description:    app.Description,
            Status:         app.Status,
        })
    }
    
    s.logger.Info(ctx, "Managed apps retrieved",
        logiface.Int64("manager_app_id", managerAppID),
        logiface.Int64("user_id", userInfo.UserID),
        logiface.Int("count", len(response)))
    
    return response, nil
}
```

### 2.5 API接口设计

#### 2.5.1 跨应用管理API
```go
// CrossAppHandler 跨应用管理处理器
type CrossAppHandler struct {
    crossAppResourceService *service.CrossAppResourceService
    appContextService       *service.AppContextService
    logger                  logiface.Logger
}

// GetManagedApps 获取可管理的应用列表
// @Summary 获取可管理的应用列表
// @Description 获取当前管理应用可以管理的其他应用列表
// @Tags 跨应用管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.GetManagedAppsRequest true "获取可管理应用请求"
// @Success 200 {object} commonResponse.Response{data=[]dto.ManagedAppResponse} "获取成功"
// @Router /api/user/cross-app/managed-apps [post]
func (h *CrossAppHandler) GetManagedApps(c *gin.Context) {
    var req dto.GetManagedAppsRequest
    
    if err := c.ShouldBindJSON(&req); err != nil {
        handleGinValidationError(c, err)
        return
    }
    
    apps, err := h.crossAppResourceService.GetManagedApps(c.Request.Context(), req.ManagerAppID)
    if err != nil {
        h.logger.Error(c.Request.Context(), "get managed apps failed",
            logiface.Error(err),
            logiface.Int64("manager_app_id", req.ManagerAppID))
        commonResponse.InternalError(c, err)
        return
    }
    
    commonResponse.Success(c, apps)
}

// GetCrossAppResourceTree 获取跨应用资源树
// @Summary 获取跨应用资源树
// @Description 获取管理应用对目标应用的资源树
// @Tags 跨应用管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.CrossAppResourceTreeRequest true "跨应用资源树请求"
// @Success 200 {object} commonResponse.Response{data=[]dto.ResourceTreeNode} "获取成功"
// @Router /api/user/cross-app/resource-tree [post]
func (h *CrossAppHandler) GetCrossAppResourceTree(c *gin.Context) {
    var req dto.CrossAppResourceTreeRequest
    
    if err := c.ShouldBindJSON(&req); err != nil {
        handleGinValidationError(c, err)
        return
    }
    
    // 应用默认值
    if req.MaxDepth <= 0 {
        req.MaxDepth = 3
    }
    
    tree, err := h.crossAppResourceService.GetManagedAppResourceTree(c.Request.Context(), &req)
    if err != nil {
        h.logger.Error(c.Request.Context(), "get cross app resource tree failed",
            logiface.Error(err),
            logiface.Int64("manager_app_id", req.ManagerAppID),
            logiface.Int64("target_app_id", req.TargetAppID))
        commonResponse.InternalError(c, err)
        return
    }
    
    commonResponse.Success(c, tree)
}
```

#### 2.5.2 DTO定义
```go
// CrossAppResourceTreeRequest 跨应用资源树请求
type CrossAppResourceTreeRequest struct {
    ManagerAppID     int64  `json:"manager_app_id" binding:"required"`
    TargetAppID      int64  `json:"target_app_id" binding:"required"`
    ResourceType     string `json:"resource_type"`
    MaxDepth         int    `json:"max_depth"`
    IncludeUniversal bool   `json:"include_universal"`
}

// GetManagedAppsRequest 获取可管理应用请求
type GetManagedAppsRequest struct {
    ManagerAppID int64 `json:"manager_app_id" binding:"required"`
}

// ManagedAppResponse 可管理应用响应
type ManagedAppResponse struct {
    InternalAppID int64  `json:"internal_app_id"`
    AppName       string `json:"app_name"`
    AppID         string `json:"app_id"`
    DisplayName   string `json:"display_name"`
    Description   string `json:"description"`
    Status        string `json:"status"`
}
```

## 3. 前端集成方案

### 3.1 应用切换服务
```typescript
// 跨应用管理服务
class CrossAppManagementService {
    private currentManagerAppId: number;
    private managedApps: ManagedApp[] = [];
    
    constructor(managerAppId: number) {
        this.currentManagerAppId = managerAppId;
    }
    
    // 获取可管理的应用列表
    async getManagedApps(): Promise<ManagedApp[]> {
        const response = await api.post('/api/user/cross-app/managed-apps', {
            manager_app_id: this.currentManagerAppId
        });
        
        if (response.code === 0) {
            this.managedApps = response.data;
            return this.managedApps;
        }
        throw new Error(response.message);
    }
    
    // 获取跨应用资源树
    async getCrossAppResourceTree(targetAppId: number, options?: {
        resourceType?: string;
        maxDepth?: number;
        includeUniversal?: boolean;
    }): Promise<ResourceTreeNode[]> {
        const response = await api.post('/api/user/cross-app/resource-tree', {
            manager_app_id: this.currentManagerAppId,
            target_app_id: targetAppId,
            resource_type: options?.resourceType || '',
            max_depth: options?.maxDepth || 3,
            include_universal: options?.includeUniversal || true
        });
        
        if (response.code === 0) {
            return response.data;
        }
        throw new Error(response.message);
    }
    
    // 设置请求头中的目标应用ID
    setTargetAppContext(targetAppId: number) {
        // 为后续的API请求设置目标应用上下文
        api.defaults.headers.common['X-Target-App-ID'] = targetAppId.toString();
    }
    
    // 清除目标应用上下文
    clearTargetAppContext() {
        delete api.defaults.headers.common['X-Target-App-ID'];
    }
}

// 使用示例
const crossAppService = new CrossAppManagementService(adminAppId);

// 获取可管理的应用
const managedApps = await crossAppService.getManagedApps();

// 切换到目标应用上下文
crossAppService.setTargetAppContext(frontendAppId);

// 获取目标应用的资源树
const resourceTree = await crossAppService.getCrossAppResourceTree(frontendAppId);
```

### 3.2 前端组件增强
```typescript
// 应用切换器组件
const AppSwitcher: React.FC<{
    currentManagerApp: number;
    onTargetAppChange: (targetAppId: number) => void;
}> = ({ currentManagerApp, onTargetAppChange }) => {
    const [managedApps, setManagedApps] = useState<ManagedApp[]>([]);
    const [selectedTargetApp, setSelectedTargetApp] = useState<number | null>(null);
    const crossAppService = useMemo(() => new CrossAppManagementService(currentManagerApp), [currentManagerApp]);
    
    useEffect(() => {
        crossAppService.getManagedApps().then(setManagedApps);
    }, [crossAppService]);
    
    const handleAppChange = (targetAppId: number) => {
        setSelectedTargetApp(targetAppId);
        crossAppService.setTargetAppContext(targetAppId);
        onTargetAppChange(targetAppId);
    };
    
    return (
        <Select
            placeholder="选择要管理的应用"
            value={selectedTargetApp}
            onChange={handleAppChange}
            style={{ width: 200 }}
        >
            {managedApps.map(app => (
                <Option key={app.internal_app_id} value={app.internal_app_id}>
                    {app.display_name} ({app.app_name})
                </Option>
            ))}
        </Select>
    );
};

// 增强的资源管理页面
const EnhancedResourcePage: React.FC = () => {
    const { user } = useAuth();
    const [currentManagerApp] = useState(1); // 当前管理应用ID
    const [targetApp, setTargetApp] = useState<number | null>(null);
    const [resourceTree, setResourceTree] = useState<ResourceTreeNode[]>([]);
    const crossAppService = useMemo(() => new CrossAppManagementService(currentManagerApp), [currentManagerApp]);
    
    const handleTargetAppChange = async (targetAppId: number) => {
        setTargetApp(targetAppId);
        try {
            const tree = await crossAppService.getCrossAppResourceTree(targetAppId);
            setResourceTree(tree);
        } catch (error) {
            message.error('获取资源树失败');
        }
    };
    
    return (
        <div>
            <Card size="small" style={{ marginBottom: 16 }}>
                <Row gutter={16} align="middle">
                    <Col span={8}>
                        <Form.Item label="管理应用" style={{ marginBottom: 0 }}>
                            <Text strong>Admin Portal</Text>
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item label="目标应用" style={{ marginBottom: 0 }}>
                            <AppSwitcher 
                                currentManagerApp={currentManagerApp}
                                onTargetAppChange={handleTargetAppChange}
                            />
                        </Form.Item>
                    </Col>
                    {targetApp && (
                        <Col span={8}>
                            <Tag color="blue">
                                正在管理应用 ID: {targetApp}
                            </Tag>
                        </Col>
                    )}
                </Row>
            </Card>
            
            {targetApp && (
                <ResourceTreeComponent 
                    treeData={resourceTree}
                    crossAppMode={true}
                    targetAppId={targetApp}
                />
            )}
        </div>
    );
};
```

## 4. 部署和配置

### 4.1 数据库迁移
```sql
-- 创建应用管理关系表
CREATE TABLE app_management_relations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL,
    manager_app_id BIGINT NOT NULL,
    target_app_id BIGINT NOT NULL,
    management_scope JSON,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_manager_target (tenant_id, manager_app_id, target_app_id),
    FOREIGN KEY (manager_app_id) REFERENCES applications(internal_app_id),
    FOREIGN KEY (target_app_id) REFERENCES applications(internal_app_id)
);

-- 初始化管理关系（管理后台管理前端应用）
INSERT INTO app_management_relations (tenant_id, manager_app_id, target_app_id, management_scope)
SELECT DISTINCT
    1 as tenant_id,
    (SELECT internal_app_id FROM applications WHERE app_name = 'admin-portal' LIMIT 1) as manager_app_id,
    (SELECT internal_app_id FROM applications WHERE app_name = 'frontend-app' LIMIT 1) as target_app_id,
    JSON_ARRAY('resources', 'users', 'permissions', 'configs') as management_scope;
```

### 4.2 路由配置
```go
// 添加跨应用管理路由
func setupCrossAppRoutes(r *gin.RouterGroup, container *DependencyContainer) {
    crossAppGroup := r.Group("/cross-app")
    {
        // 应用上下文中间件
        crossAppGroup.Use(container.GetAppContextService().CreateAppContextMiddleware())
        
        // 跨应用管理接口
        crossAppGroup.POST("/managed-apps", container.GetCrossAppHandler().GetManagedApps)
        crossAppGroup.POST("/resource-tree", container.GetCrossAppHandler().GetCrossAppResourceTree)
    }
}
```

## 5. 安全和权限控制

### 5.1 访问控制
- 严格验证管理关系，只允许已配置的管理应用访问目标应用
- 基于管理范围（management_scope）控制具体的操作权限
- 所有跨应用操作都需要记录审计日志

### 5.2 数据隔离
- 应用间数据通过 internal_app_id 严格隔离
- 跨应用访问通过明确的管理关系授权
- 不允许绕过管理关系直接访问其他应用数据

## 6. 总结

此设计方案通过以下核心机制解决了多应用管理需求：

1. **应用管理关系表**：明确定义了应用间的管理关系和权限范围
2. **应用上下文服务**：动态切换应用上下文，突破 usercontext 的限制
3. **增强的仓储层**：支持跨应用数据查询，同时保持安全边界
4. **中间件支持**：通过请求头传递目标应用上下文

该方案在保持现有架构稳定性的基础上，实现了管理后台对前端应用的有效管理，同时确保了数据安全和权限控制。