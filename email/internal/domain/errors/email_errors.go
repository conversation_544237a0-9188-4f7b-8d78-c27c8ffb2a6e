package errors

import (
	"fmt"
)

// 邮件模块错误码范围: 200000-299999
const (
	// 邮件发送相关错误 (200000-200099)
	CodeEmailNotFound             = 200000 // 邮件不存在
	CodeEmailAlreadyExists        = 200001 // 邮件已存在
	CodeEmailSendFailed           = 200002 // 邮件发送失败
	CodeEmailAlreadySent          = 200003 // 邮件已发送
	CodeEmailCancelled            = 200004 // 邮件已取消
	CodeEmailScheduled            = 200005 // 邮件已调度
	CodeEmailDraft                = 200006 // 邮件草稿状态
	CodeEmailInvalidFromAddress   = 200007 // 发件人地址无效
	CodeEmailInvalidToAddress     = 200008 // 收件人地址无效
	CodeEmailInvalidSubject       = 200009 // 邮件主题无效
	CodeEmailInvalidContent       = 200010 // 邮件内容无效
	CodeEmailInvalidTemplate      = 200011 // 邮件模板无效
	CodeEmailQuotaExceeded        = 200012 // 邮件配额超限
	CodeEmailRateLimitExceeded    = 200013 // 邮件发送频率超限
	CodeEmailRetryFailed          = 200014 // 邮件重试失败
	CodeEmailMaxRetriesExceeded   = 200015 // 超过最大重试次数
	CodeEmailBounced              = 200016 // 邮件被退回
	CodeEmailSpamDetected         = 200017 // 邮件被检测为垃圾邮件
	CodeEmailBlocked              = 200018 // 邮件被阻止
	CodeEmailTimeout              = 200019 // 邮件发送超时
	CodeEmailConnectionFailed     = 200020 // 邮件连接失败
	CodeEmailAuthenticationFailed = 200021 // 邮件认证失败
	CodeEmailServerError          = 200022 // 邮件服务器错误
	CodeEmailInvalidAttachment    = 200023 // 邮件附件无效
	CodeEmailAttachmentTooLarge   = 200024 // 邮件附件过大
	CodeEmailInvalidRecipient     = 200025 // 邮件收件人无效
	CodeEmailBlacklisted          = 200026 // 邮件地址被拉黑
	CodeEmailWhitelistRequired    = 200027 // 需要白名单验证

	// 邮件模板相关错误 (200100-200199)
	CodeTemplateNotFound        = 200100 // 模板不存在
	CodeTemplateAlreadyExists   = 200101 // 模板已存在
	CodeTemplateNameExists      = 200102 // 模板名称已存在
	CodeTemplateCodeExists      = 200103 // 模板代码已存在
	CodeTemplateInvalidContent  = 200104 // 模板内容无效
	CodeTemplateInvalidType     = 200105 // 模板类型无效
	CodeTemplateInvalidStatus   = 200106 // 模板状态无效
	CodeTemplateNotPublished    = 200107 // 模板未发布
	CodeTemplateDisabled        = 200108 // 模板已禁用
	CodeTemplateDeleted         = 200109 // 模板已删除
	CodeTemplateVersionNotFound = 200110 // 模板版本不存在
	CodeTemplateDraftNotFound   = 200111 // 模板草稿不存在
	CodeTemplateVariableInvalid = 200112 // 模板变量无效
	CodeTemplateVariableMissing = 200113 // 模板变量缺失
	CodeTemplateRenderFailed    = 200114 // 模板渲染失败
	CodeTemplatePreviewFailed   = 200115 // 模板预览失败
	CodeTemplateCloneFailed     = 200116 // 模板克隆失败
	CodeTemplateRestoreFailed   = 200117 // 模板恢复失败
	CodeTemplatePublishFailed   = 200118 // 模板发布失败
	CodeTemplateDisableFailed   = 200119 // 模板禁用失败
	CodeTemplateDeleteFailed    = 200120 // 模板删除失败
	CodeTemplateTestFailed      = 200121 // 模板测试失败

	// 邮件账号相关错误 (200200-200299)
	CodeAccountNotFound             = 200200 // 账号不存在
	CodeAccountAlreadyExists        = 200201 // 账号已存在
	CodeAccountNameExists           = 200202 // 账号名称已存在
	CodeAccountInactive             = 200203 // 账号未激活
	CodeAccountDisabled             = 200204 // 账号已禁用
	CodeAccountDeleted              = 200205 // 账号已删除
	CodeAccountInvalidName          = 200206 // 账号名称无效
	CodeAccountInvalidProvider      = 200207 // 账号提供商无效
	CodeAccountInvalidHost          = 200208 // 账号主机无效
	CodeAccountInvalidPort          = 200209 // 账号端口无效
	CodeAccountInvalidUsername      = 200210 // 账号用户名无效
	CodeAccountInvalidPassword      = 200211 // 账号密码无效
	CodeAccountInvalidFromAddress   = 200212 // 账号发件人地址无效
	CodeAccountTestFailed           = 200213 // 账号测试失败
	CodeAccountConnectionFailed     = 200214 // 账号连接失败
	CodeAccountAuthenticationFailed = 200215 // 账号认证失败
	CodeAccountQuotaExceeded        = 200216 // 账号配额超限
	CodeAccountRateLimitExceeded    = 200217 // 账号频率限制超限
	CodeAccountLimitReached         = 200218 // 账号数量已达上限
	CodeAccountConfigInvalid        = 200219 // 账号配置无效
	CodeAccountSecurityInvalid      = 200220 // 账号安全设置无效
	CodeAccountSSLInvalid           = 200221 // 账号SSL设置无效
	CodeAccountTimeoutInvalid       = 200222 // 账号超时设置无效

	// 租户相关错误 (200300-200399)
	CodeTenantNotFound             = 200300 // 租户不存在
	CodeTenantDisabled             = 200301 // 租户已禁用
	CodeTenantExpired              = 200302 // 租户已过期
	CodeTenantSuspended            = 200303 // 租户已暂停
	CodeTenantConfigNotFound       = 200304 // 租户配置不存在
	CodeTenantConfigInvalid        = 200305 // 租户配置无效
	CodeTenantQuotaExceeded        = 200306 // 租户配额超限
	CodeTenantSMTPTestFailed       = 200307 // 租户SMTP测试失败
	CodeTenantEmailLimitReached    = 200308 // 租户邮件数量已达上限
	CodeTenantStorageLimitReached  = 200309 // 租户存储空间已达上限
	CodeTenantTemplateLimitReached = 200310 // 租户模板数量已达上限
	CodeTenantAccountLimitReached  = 200311 // 租户账号数量已达上限
	CodeTenantSettingsInvalid      = 200312 // 租户设置无效
	CodeTenantTimezoneInvalid      = 200313 // 租户时区无效
	CodeTenantLanguageInvalid      = 200314 // 租户语言无效
	CodeTenantCurrencyInvalid      = 200315 // 租户货币无效

	// 订阅者相关错误 (200400-200499)
	CodeSubscriberNotFound      = 200400 // 订阅者不存在
	CodeSubscriberAlreadyExists = 200401 // 订阅者已存在
	CodeSubscriberEmailExists   = 200402 // 订阅者邮箱已存在
	CodeSubscriberInvalidEmail  = 200403 // 订阅者邮箱无效
	CodeSubscriberInvalidStatus = 200404 // 订阅者状态无效
	CodeSubscriberUnsubscribed  = 200405 // 订阅者已退订
	CodeSubscriberBounced       = 200406 // 订阅者已退回
	CodeSubscriberSpam          = 200407 // 订阅者标记为垃圾邮件
	CodeSubscriberBlacklisted   = 200408 // 订阅者被拉黑
	CodeSubscriberLimitReached  = 200409 // 订阅者数量已达上限
	CodeSubscriberImportFailed  = 200410 // 订阅者导入失败
	CodeSubscriberExportFailed  = 200411 // 订阅者导出失败
	CodeSubscriberGroupNotFound = 200412 // 订阅者分组不存在
	CodeSubscriberGroupExists   = 200413 // 订阅者分组已存在
	CodeSubscriberGroupInvalid  = 200414 // 订阅者分组无效

	// 邮件列表相关错误 (200500-200599)
	CodeListNotFound           = 200500 // 邮件列表不存在
	CodeListAlreadyExists      = 200501 // 邮件列表已存在
	CodeListNameExists         = 200502 // 邮件列表名称已存在
	CodeListInvalidName        = 200503 // 邮件列表名称无效
	CodeListInvalidDescription = 200504 // 邮件列表描述无效
	CodeListInvalidStatus      = 200505 // 邮件列表状态无效
	CodeListDisabled           = 200506 // 邮件列表已禁用
	CodeListDeleted            = 200507 // 邮件列表已删除
	CodeListLimitReached       = 200508 // 邮件列表数量已达上限
	CodeListSubscriberNotFound = 200509 // 邮件列表订阅者不存在
	CodeListSubscriberExists   = 200510 // 邮件列表订阅者已存在
	CodeListImportFailed       = 200511 // 邮件列表导入失败
	CodeListExportFailed       = 200512 // 邮件列表导出失败

	// 邮件活动相关错误 (200600-200699)
	CodeCampaignNotFound         = 200600 // 邮件活动不存在
	CodeCampaignAlreadyExists    = 200601 // 邮件活动已存在
	CodeCampaignNameExists       = 200602 // 邮件活动名称已存在
	CodeCampaignInvalidName      = 200603 // 邮件活动名称无效
	CodeCampaignInvalidStatus    = 200604 // 邮件活动状态无效
	CodeCampaignNotScheduled     = 200605 // 邮件活动未调度
	CodeCampaignScheduled        = 200606 // 邮件活动已调度
	CodeCampaignSending          = 200607 // 邮件活动发送中
	CodeCampaignSent             = 200608 // 邮件活动已发送
	CodeCampaignPaused           = 200609 // 邮件活动已暂停
	CodeCampaignCancelled        = 200610 // 邮件活动已取消
	CodeCampaignDraft            = 200611 // 邮件活动草稿状态
	CodeCampaignTemplateNotFound = 200612 // 邮件活动模板不存在
	CodeCampaignListNotFound     = 200613 // 邮件活动列表不存在
	CodeCampaignAccountNotFound  = 200614 // 邮件活动账号不存在
	CodeCampaignSendFailed       = 200615 // 邮件活动发送失败
	CodeCampaignScheduleFailed   = 200616 // 邮件活动调度失败
	CodeCampaignPauseFailed      = 200617 // 邮件活动暂停失败
	CodeCampaignCancelFailed     = 200618 // 邮件活动取消失败
	CodeCampaignDeleteFailed     = 200619 // 邮件活动删除失败

	// 统计分析相关错误 (200700-200799)
	CodeStatisticsNotFound       = 200700 // 统计数据不存在
	CodeStatisticsQueryInvalid   = 200701 // 统计查询参数无效
	CodeStatisticsDateInvalid    = 200702 // 统计日期无效
	CodeStatisticsRangeInvalid   = 200703 // 统计范围无效
	CodeStatisticsTypeInvalid    = 200704 // 统计类型无效
	CodeStatisticsExportFailed   = 200705 // 统计导出失败
	CodeStatisticsReportNotFound = 200706 // 统计报告不存在
	CodeStatisticsReportFailed   = 200707 // 统计报告生成失败

	// 系统错误 (200900-200999)
	CodeSystemError           = 200900 // 系统错误
	CodeDatabaseError         = 200901 // 数据库错误
	CodeCacheError            = 200902 // 缓存错误
	CodeNetworkError          = 200903 // 网络错误
	CodeServiceUnavailable    = 200904 // 服务不可用
	CodeServiceTimeout        = 200905 // 服务超时
	CodeServiceOverload       = 200906 // 服务过载
	CodeThirdPartyError       = 200907 // 第三方服务错误
	CodeThirdPartyTimeout     = 200908 // 第三方服务超时
	CodeThirdPartyUnavailable = 200909 // 第三方服务不可用
	CodeInternalError         = 200910 // 内部错误
	CodeUnexpectedError       = 200911 // 意外错误
)

// 错误消息映射
var errorMessages = map[int]string{
	// 邮件发送相关错误
	CodeEmailNotFound:             "邮件不存在",
	CodeEmailAlreadyExists:        "邮件已存在",
	CodeEmailSendFailed:           "邮件发送失败",
	CodeEmailAlreadySent:          "邮件已发送",
	CodeEmailCancelled:            "邮件已取消",
	CodeEmailScheduled:            "邮件已调度",
	CodeEmailDraft:                "邮件草稿状态",
	CodeEmailInvalidFromAddress:   "发件人地址无效",
	CodeEmailInvalidToAddress:     "收件人地址无效",
	CodeEmailInvalidSubject:       "邮件主题无效",
	CodeEmailInvalidContent:       "邮件内容无效",
	CodeEmailInvalidTemplate:      "邮件模板无效",
	CodeEmailQuotaExceeded:        "邮件配额超限",
	CodeEmailRateLimitExceeded:    "邮件发送频率超限",
	CodeEmailRetryFailed:          "邮件重试失败",
	CodeEmailMaxRetriesExceeded:   "超过最大重试次数",
	CodeEmailBounced:              "邮件被退回",
	CodeEmailSpamDetected:         "邮件被检测为垃圾邮件",
	CodeEmailBlocked:              "邮件被阻止",
	CodeEmailTimeout:              "邮件发送超时",
	CodeEmailConnectionFailed:     "邮件连接失败",
	CodeEmailAuthenticationFailed: "邮件认证失败",
	CodeEmailServerError:          "邮件服务器错误",
	CodeEmailInvalidAttachment:    "邮件附件无效",
	CodeEmailAttachmentTooLarge:   "邮件附件过大",
	CodeEmailInvalidRecipient:     "邮件收件人无效",
	CodeEmailBlacklisted:          "邮件地址被拉黑",
	CodeEmailWhitelistRequired:    "需要白名单验证",

	// 邮件模板相关错误
	CodeTemplateNotFound:        "模板不存在",
	CodeTemplateAlreadyExists:   "模板已存在",
	CodeTemplateNameExists:      "模板名称已存在",
	CodeTemplateCodeExists:      "模板代码已存在",
	CodeTemplateInvalidContent:  "模板内容无效",
	CodeTemplateInvalidType:     "模板类型无效",
	CodeTemplateInvalidStatus:   "模板状态无效",
	CodeTemplateNotPublished:    "模板未发布",
	CodeTemplateDisabled:        "模板已禁用",
	CodeTemplateDeleted:         "模板已删除",
	CodeTemplateVersionNotFound: "模板版本不存在",
	CodeTemplateDraftNotFound:   "模板草稿不存在",
	CodeTemplateVariableInvalid: "模板变量无效",
	CodeTemplateVariableMissing: "模板变量缺失",
	CodeTemplateRenderFailed:    "模板渲染失败",
	CodeTemplatePreviewFailed:   "模板预览失败",
	CodeTemplateCloneFailed:     "模板克隆失败",
	CodeTemplateRestoreFailed:   "模板恢复失败",
	CodeTemplatePublishFailed:   "模板发布失败",
	CodeTemplateDisableFailed:   "模板禁用失败",
	CodeTemplateDeleteFailed:    "模板删除失败",
	CodeTemplateTestFailed:      "模板测试失败",

	// 邮件账号相关错误
	CodeAccountNotFound:             "账号不存在",
	CodeAccountAlreadyExists:        "账号已存在",
	CodeAccountNameExists:           "账号名称已存在",
	CodeAccountInactive:             "账号未激活",
	CodeAccountDisabled:             "账号已禁用",
	CodeAccountDeleted:              "账号已删除",
	CodeAccountInvalidName:          "账号名称无效",
	CodeAccountInvalidProvider:      "账号提供商无效",
	CodeAccountInvalidHost:          "账号主机无效",
	CodeAccountInvalidPort:          "账号端口无效",
	CodeAccountInvalidUsername:      "账号用户名无效",
	CodeAccountInvalidPassword:      "账号密码无效",
	CodeAccountInvalidFromAddress:   "账号发件人地址无效",
	CodeAccountTestFailed:           "账号测试失败",
	CodeAccountConnectionFailed:     "账号连接失败",
	CodeAccountAuthenticationFailed: "账号认证失败",
	CodeAccountQuotaExceeded:        "账号配额超限",
	CodeAccountRateLimitExceeded:    "账号频率限制超限",
	CodeAccountLimitReached:         "账号数量已达上限",
	CodeAccountConfigInvalid:        "账号配置无效",
	CodeAccountSecurityInvalid:      "账号安全设置无效",
	CodeAccountSSLInvalid:           "账号SSL设置无效",
	CodeAccountTimeoutInvalid:       "账号超时设置无效",

	// 租户相关错误
	CodeTenantNotFound:             "租户不存在",
	CodeTenantDisabled:             "租户已禁用",
	CodeTenantExpired:              "租户已过期",
	CodeTenantSuspended:            "租户已暂停",
	CodeTenantConfigNotFound:       "租户配置不存在",
	CodeTenantConfigInvalid:        "租户配置无效",
	CodeTenantQuotaExceeded:        "租户配额超限",
	CodeTenantSMTPTestFailed:       "租户SMTP测试失败",
	CodeTenantEmailLimitReached:    "租户邮件数量已达上限",
	CodeTenantStorageLimitReached:  "租户存储空间已达上限",
	CodeTenantTemplateLimitReached: "租户模板数量已达上限",
	CodeTenantAccountLimitReached:  "租户账号数量已达上限",
	CodeTenantSettingsInvalid:      "租户设置无效",
	CodeTenantTimezoneInvalid:      "租户时区无效",
	CodeTenantLanguageInvalid:      "租户语言无效",
	CodeTenantCurrencyInvalid:      "租户货币无效",

	// 订阅者相关错误
	CodeSubscriberNotFound:      "订阅者不存在",
	CodeSubscriberAlreadyExists: "订阅者已存在",
	CodeSubscriberEmailExists:   "订阅者邮箱已存在",
	CodeSubscriberInvalidEmail:  "订阅者邮箱无效",
	CodeSubscriberInvalidStatus: "订阅者状态无效",
	CodeSubscriberUnsubscribed:  "订阅者已退订",
	CodeSubscriberBounced:       "订阅者已退回",
	CodeSubscriberSpam:          "订阅者标记为垃圾邮件",
	CodeSubscriberBlacklisted:   "订阅者被拉黑",
	CodeSubscriberLimitReached:  "订阅者数量已达上限",
	CodeSubscriberImportFailed:  "订阅者导入失败",
	CodeSubscriberExportFailed:  "订阅者导出失败",
	CodeSubscriberGroupNotFound: "订阅者分组不存在",
	CodeSubscriberGroupExists:   "订阅者分组已存在",
	CodeSubscriberGroupInvalid:  "订阅者分组无效",

	// 邮件列表相关错误
	CodeListNotFound:           "邮件列表不存在",
	CodeListAlreadyExists:      "邮件列表已存在",
	CodeListNameExists:         "邮件列表名称已存在",
	CodeListInvalidName:        "邮件列表名称无效",
	CodeListInvalidDescription: "邮件列表描述无效",
	CodeListInvalidStatus:      "邮件列表状态无效",
	CodeListDisabled:           "邮件列表已禁用",
	CodeListDeleted:            "邮件列表已删除",
	CodeListLimitReached:       "邮件列表数量已达上限",
	CodeListSubscriberNotFound: "邮件列表订阅者不存在",
	CodeListSubscriberExists:   "邮件列表订阅者已存在",
	CodeListImportFailed:       "邮件列表导入失败",
	CodeListExportFailed:       "邮件列表导出失败",

	// 邮件活动相关错误
	CodeCampaignNotFound:         "邮件活动不存在",
	CodeCampaignAlreadyExists:    "邮件活动已存在",
	CodeCampaignNameExists:       "邮件活动名称已存在",
	CodeCampaignInvalidName:      "邮件活动名称无效",
	CodeCampaignInvalidStatus:    "邮件活动状态无效",
	CodeCampaignNotScheduled:     "邮件活动未调度",
	CodeCampaignScheduled:        "邮件活动已调度",
	CodeCampaignSending:          "邮件活动发送中",
	CodeCampaignSent:             "邮件活动已发送",
	CodeCampaignPaused:           "邮件活动已暂停",
	CodeCampaignCancelled:        "邮件活动已取消",
	CodeCampaignDraft:            "邮件活动草稿状态",
	CodeCampaignTemplateNotFound: "邮件活动模板不存在",
	CodeCampaignListNotFound:     "邮件活动列表不存在",
	CodeCampaignAccountNotFound:  "邮件活动账号不存在",
	CodeCampaignSendFailed:       "邮件活动发送失败",
	CodeCampaignScheduleFailed:   "邮件活动调度失败",
	CodeCampaignPauseFailed:      "邮件活动暂停失败",
	CodeCampaignCancelFailed:     "邮件活动取消失败",
	CodeCampaignDeleteFailed:     "邮件活动删除失败",

	// 统计分析相关错误
	CodeStatisticsNotFound:       "统计数据不存在",
	CodeStatisticsQueryInvalid:   "统计查询参数无效",
	CodeStatisticsDateInvalid:    "统计日期无效",
	CodeStatisticsRangeInvalid:   "统计范围无效",
	CodeStatisticsTypeInvalid:    "统计类型无效",
	CodeStatisticsExportFailed:   "统计导出失败",
	CodeStatisticsReportNotFound: "统计报告不存在",
	CodeStatisticsReportFailed:   "统计报告生成失败",

	// 系统错误
	CodeSystemError:           "系统错误",
	CodeDatabaseError:         "数据库错误",
	CodeCacheError:            "缓存错误",
	CodeNetworkError:          "网络错误",
	CodeServiceUnavailable:    "服务不可用",
	CodeServiceTimeout:        "服务超时",
	CodeServiceOverload:       "服务过载",
	CodeThirdPartyError:       "第三方服务错误",
	CodeThirdPartyTimeout:     "第三方服务超时",
	CodeThirdPartyUnavailable: "第三方服务不可用",
	CodeInternalError:         "内部错误",
	CodeUnexpectedError:       "意外错误",
}

// EmailError 邮件模块错误
type EmailError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// Error 实现error接口
func (e *EmailError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%d] %s: %s", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("[%d] %s", e.Code, e.Message)
}

// GetCode 获取错误码
func (e *EmailError) GetCode() int {
	return e.Code
}

// GetMessage 获取错误消息
func (e *EmailError) GetMessage() string {
	return e.Message
}

// GetDetails 获取错误详情
func (e *EmailError) GetDetails() string {
	return e.Details
}

// NewEmailError 创建邮件错误
func NewEmailError(code int, details ...string) *EmailError {
	var message string
	var detail string

	// 如果传入了自定义描述，优先使用自定义描述作为message
	if len(details) > 0 {
		message = details[0]
		// 如果还有更多参数，第二个参数作为detail
		if len(details) > 1 {
			detail = details[1]
		}
	} else {
		// 如果没有传入自定义描述，使用预定义的错误消息
		if msg, exists := errorMessages[code]; exists {
			message = msg
		} else {
			message = "未知错误"
		}
	}

	return &EmailError{
		Code:    code,
		Message: message,
		Details: detail,
	}
}

// 便捷的错误创建函数

// NewEmailNotFoundError 创建邮件不存在错误
func NewEmailNotFoundError(emailID interface{}) *EmailError {
	return NewEmailError(CodeEmailNotFound, fmt.Sprintf("email_id: %v", emailID))
}

// NewEmailAlreadyExistsError 创建邮件已存在错误
func NewEmailAlreadyExistsError(field, value string) *EmailError {
	return NewEmailError(CodeEmailAlreadyExists, fmt.Sprintf("field: %s, value: %s", field, value))
}

// NewEmailSendFailedError 创建邮件发送失败错误
func NewEmailSendFailedError(reason string) *EmailError {
	return NewEmailError(CodeEmailSendFailed, fmt.Sprintf("reason: %s", reason))
}

// NewEmailQuotaExceededError 创建邮件配额超限错误
func NewEmailQuotaExceededError(used, limit int) *EmailError {
	return NewEmailError(CodeEmailQuotaExceeded, fmt.Sprintf("used: %d, limit: %d", used, limit))
}

// NewEmailRateLimitExceededError 创建邮件频率限制超限错误
func NewEmailRateLimitExceededError(rate, limit int) *EmailError {
	return NewEmailError(CodeEmailRateLimitExceeded, fmt.Sprintf("rate: %d, limit: %d", rate, limit))
}

// NewTemplateNotFoundError 创建模板不存在错误
func NewTemplateNotFoundError(templateID interface{}) *EmailError {
	return NewEmailError(CodeTemplateNotFound, fmt.Sprintf("template_id: %v", templateID))
}

// NewTemplateNameExistsError 创建模板名称已存在错误
func NewTemplateNameExistsError(name string) *EmailError {
	return NewEmailError(CodeTemplateNameExists, fmt.Sprintf("name: %s", name))
}

// NewTemplateCodeExistsError 创建模板代码已存在错误
func NewTemplateCodeExistsError(code string) *EmailError {
	return NewEmailError(CodeTemplateCodeExists, fmt.Sprintf("code: %s", code))
}

// NewAccountNotFoundError 创建账号不存在错误
func NewAccountNotFoundError(accountID interface{}) *EmailError {
	return NewEmailError(CodeAccountNotFound, fmt.Sprintf("account_id: %v", accountID))
}

// NewAccountNameExistsError 创建账号名称已存在错误
func NewAccountNameExistsError(name string) *EmailError {
	return NewEmailError(CodeAccountNameExists, fmt.Sprintf("name: %s", name))
}

// NewAccountInactiveError 创建账号未激活错误
func NewAccountInactiveError(accountID interface{}) *EmailError {
	return NewEmailError(CodeAccountInactive, fmt.Sprintf("account_id: %v", accountID))
}

// NewAccountTestFailedError 创建账号测试失败错误
func NewAccountTestFailedError(accountID interface{}, reason string) *EmailError {
	return NewEmailError(CodeAccountTestFailed, fmt.Sprintf("account_id: %v, reason: %s", accountID, reason))
}

// NewTenantNotFoundError 创建租户不存在错误
func NewTenantNotFoundError(tenantID interface{}) *EmailError {
	return NewEmailError(CodeTenantNotFound, fmt.Sprintf("tenant_id: %v", tenantID))
}

// NewTenantConfigNotFoundError 创建租户配置不存在错误
func NewTenantConfigNotFoundError(tenantID interface{}) *EmailError {
	return NewEmailError(CodeTenantConfigNotFound, fmt.Sprintf("tenant_id: %v", tenantID))
}

// NewTenantQuotaExceededError 创建租户配额超限错误
func NewTenantQuotaExceededError(tenantID interface{}, used, limit int) *EmailError {
	return NewEmailError(CodeTenantQuotaExceeded, fmt.Sprintf("tenant_id: %v, used: %d, limit: %d", tenantID, used, limit))
}

// NewSubscriberNotFoundError 创建订阅者不存在错误
func NewSubscriberNotFoundError(subscriberID interface{}) *EmailError {
	return NewEmailError(CodeSubscriberNotFound, fmt.Sprintf("subscriber_id: %v", subscriberID))
}

// NewSubscriberEmailExistsError 创建订阅者邮箱已存在错误
func NewSubscriberEmailExistsError(email string) *EmailError {
	return NewEmailError(CodeSubscriberEmailExists, fmt.Sprintf("email: %s", email))
}

// NewCampaignNotFoundError 创建邮件活动不存在错误
func NewCampaignNotFoundError(campaignID interface{}) *EmailError {
	return NewEmailError(CodeCampaignNotFound, fmt.Sprintf("campaign_id: %v", campaignID))
}

// NewCampaignNameExistsError 创建邮件活动名称已存在错误
func NewCampaignNameExistsError(name string) *EmailError {
	return NewEmailError(CodeCampaignNameExists, fmt.Sprintf("name: %s", name))
}

// NewSystemError 创建系统错误
func NewSystemError(operation, reason string) *EmailError {
	return NewEmailError(CodeSystemError, fmt.Sprintf("operation: %s, reason: %s", operation, reason))
}

// NewDatabaseError 创建数据库错误
func NewDatabaseError(operation, reason string) *EmailError {
	return NewEmailError(CodeDatabaseError, fmt.Sprintf("operation: %s, reason: %s", operation, reason))
}

// NewThirdPartyError 创建第三方服务错误
func NewThirdPartyError(service, reason string) *EmailError {
	return NewEmailError(CodeThirdPartyError, fmt.Sprintf("service: %s, reason: %s", service, reason))
}

// NewValidationError 创建验证错误
func NewValidationError(field, message string) *EmailError {
	return NewEmailError(CodeAccountInvalidName, fmt.Sprintf("field: %s, message: %s", field, message))
}
