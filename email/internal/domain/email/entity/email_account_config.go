package entity

// EmailAccountConfig 定义邮件账户的可维护配置结构
// 目前包含发送频率相关配置，后续可按需扩展
type EmailAccountConfig struct {
	// 两封邮件之间的最小发送间隔（秒）。为0或nil表示不限制固定间隔
	SendIntervalSeconds *int `json:"send_interval_seconds,omitempty"`
	// 是否启用系统自动决策，根据历史表现与服务商限速自动调节发送频率
	AutoDecision bool `json:"auto_decision,omitempty"`
}

// UpdateFromMap 根据 map 输入更新配置字段（用于兼容现有 DTO）
func (c *EmailAccountConfig) UpdateFromMap(cfg map[string]interface{}) {
	if c == nil || cfg == nil {
		return
	}
	if v, ok := cfg["auto_decision"]; ok {
		switch t := v.(type) {
		case bool:
			c.AutoDecision = t
		case string:
			switch t {
			case "true", "1", "yes", "on", "TRUE", "Yes", "ON":
				c.AutoDecision = true
			default:
				c.AutoDecision = false
			}
		case float64:
			c.AutoDecision = t != 0
		case int:
			c.AutoDecision = t != 0
		}
	}
	if v, ok := cfg["send_interval_seconds"]; ok {
		switch t := v.(type) {
		case int:
			if t <= 0 {
				c.SendIntervalSeconds = nil
			} else {
				vv := t
				c.SendIntervalSeconds = &vv
			}
		case float64:
			ti := int(t)
			if ti <= 0 {
				c.SendIntervalSeconds = nil
			} else {
				vv := ti
				c.SendIntervalSeconds = &vv
			}
		case string:
			// 空字符串等视为未设置
			c.SendIntervalSeconds = nil
		default:
			// 非法值，移除
			c.SendIntervalSeconds = nil
		}
	}
}

// ToMap 导出为 map（用于 HTTP 响应保持兼容）
func (c *EmailAccountConfig) ToMap() map[string]interface{} {
	if c == nil {
		return nil
	}
	out := make(map[string]interface{}, 2)
	out["auto_decision"] = c.AutoDecision
	if c.SendIntervalSeconds != nil {
		out["send_interval_seconds"] = *c.SendIntervalSeconds
	}
	return out
}
