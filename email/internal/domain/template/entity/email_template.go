package entity

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"gitee.com/heiyee/platforms/pkg/logiface"
)

// TemplateStatus 模板状态类型
type TemplateStatus int

const (
	TemplateStatusDraft     TemplateStatus = iota + 1 // 草稿状态
	TemplateStatusPublished                           // 已发布状态
	TemplateStatusDisabled                            // 已停用状态
	TemplateStatusDeleted                             // 已删除状态
)

// String 返回状态的字符串表示
func (s TemplateStatus) String() string {
	switch s {
	case TemplateStatusDraft:
		return "draft"
	case TemplateStatusPublished:
		return "published"
	case TemplateStatusDisabled:
		return "disabled"
	case TemplateStatusDeleted:
		return "deleted"
	default:
		return "unknown"
	}
}

// TemplateType 模板类型
type TemplateType uint8

const (
	TemplateTypeHTML TemplateType = iota + 1 // HTML模板
	TemplateTypeText                         // 纯文本模板
)

// EmailTemplate 邮件模板实体
type EmailTemplate struct {
	ID                 int64                       `json:"id"`
	TenantID           int64                       `json:"tenant_id"`             // 租户ID
	InternalAppID      int64                       `json:"internal_app_id"`       // 应用ID，bigint类型提升性能
	TemplateCode       string                      `json:"template_code"`         // 模板代码
	AccountID          int64                       `json:"account_id"`            // 关联的发送账户ID
	Name               string                      `json:"name"`                  // 模板名称
	Type               TemplateType                `json:"type"`                  // 模板类型
	Status             TemplateStatus              `json:"status"`                // 模板状态
	Subject            string                      `json:"subject"`               // 邮件主题
	HTMLContent        string                      `json:"html_content"`          // HTML内容
	PlainTextContent   string                      `json:"plain_text_content"`    // 纯文本内容
	Variables          map[string]TemplateVariable `json:"variables"`             // 模板变量定义
	Lan                string                      `json:"lan"`                   // 语言
	RateLimitPerMinute uint                        `json:"rate_limit_per_minute"` // 每分钟发送限制
	RateLimitPerHour   uint                        `json:"rate_limit_per_hour"`   // 每小时发送限制
	RateLimitPerDay    uint                        `json:"rate_limit_per_day"`    // 每日发送限制
	ThumbnailURL       string                      `json:"thumbnail_url"`         // 缩略图URL
	IsResponsive       bool                        `json:"is_responsive"`         // 是否响应式
	Description        string                      `json:"description"`           // 模板描述

	// 版本管理字段
	Version       int64  `json:"version"`        // 版本号：0为当前版本，时间戳为历史版本
	ParentVersion *int64 `json:"parent_version"` // 父版本号（用于历史版本追溯）
	VersionRemark string `json:"version_remark"` // 版本备注

	// 发布相关字段
	PublishedAt *time.Time `json:"published_at"` // 发布时间
	PublishedBy *int64     `json:"published_by"` // 发布人

	// 时间字段
	CreatedAt time.Time  `json:"created_at"`           // 创建时间
	UpdatedAt time.Time  `json:"updated_at"`           // 更新时间
	DeletedAt *time.Time `json:"deleted_at,omitempty"` // 删除时间

	// 用户字段
	CreatedBy int64 `json:"created_by"` // 创建人
	UpdatedBy int64 `json:"updated_by"` // 更新人

	// 其他字段
	IsSystem        bool                     `json:"is_system"`        // 是否为系统内置模板
	TrackingEnabled bool                     `json:"tracking_enabled"` // 是否启用追踪
	TrackingOptions *TemplateTrackingOptions `json:"tracking_options"` // 追踪配置选项
}

// TemplateVariable 模板变量定义
type TemplateVariable struct {
	Label       string `json:"label"`       // 显示名称
	Type        string `json:"type"`        // 变量类型
	Required    bool   `json:"required"`    // 是否必填
	Description string `json:"description"` // 描述
}

// NewEmailTemplate 创建新的邮件模板
func NewEmailTemplate(tenantID, internalAppID int64, templateCode string, accountID int64, name string, templateType TemplateType, createdBy int64) (*EmailTemplate, error) {
	if tenantID == 0 {
		return nil, errors.New("tenant id is required")
	}
	if templateCode == "" {
		return nil, errors.New("template code is required")
	}
	if accountID == 0 {
		return nil, errors.New("account id is required")
	}
	if name == "" {
		return nil, errors.New("name is required")
	}
	if templateType == 0 {
		return nil, errors.New("template type is required")
	}
	if createdBy == 0 {
		return nil, errors.New("created by is required")
	}

	return &EmailTemplate{
		TenantID:           tenantID,
		InternalAppID:      internalAppID,
		TemplateCode:       templateCode,
		AccountID:          accountID,
		Name:               name,
		Type:               templateType,
		Status:             TemplateStatusDraft, // 默认为草稿状态
		RateLimitPerMinute: 60,                  // 默认值
		RateLimitPerHour:   1000,                // 默认值
		RateLimitPerDay:    10000,               // 默认值
		Version:            0,                   // 当前版本为0
		Variables:          make(map[string]TemplateVariable),
		TrackingEnabled:    true,                        // 默认启用追踪
		TrackingOptions:    NewDefaultTrackingOptions(), // 默认追踪选项
		CreatedBy:          createdBy,
		UpdatedBy:          createdBy,
		CreatedAt:          time.Now(),
		UpdatedAt:          time.Now(),
	}, nil
}

// Validate 验证模板
func (t *EmailTemplate) Validate() error {
	return t.ValidateWithOptions(false)
}

// ValidateWithOptions 验证模板（带选项）
func (t *EmailTemplate) ValidateWithOptions(allowEmptyContent bool) error {
	if t.TenantID == 0 {
		return errors.New("tenant id is required")
	}
	if t.TemplateCode == "" {
		return errors.New("template code is required")
	}
	if t.AccountID == 0 {
		return errors.New("account id is required")
	}
	if t.Name == "" {
		return errors.New("name is required")
	}
	if t.Subject == "" {
		return errors.New("subject is required")
	}
	if t.Type == 0 {
		return errors.New("type is required")
	}
	if t.Status == 0 {
		return errors.New("status is required")
	}

	// 检查内容（只有在不允许空内容时才检查）
	if !allowEmptyContent {
		if t.Type == TemplateTypeHTML && t.HTMLContent == "" {
			return errors.New("HTML content is required for HTML template")
		}
		if t.Type == TemplateTypeText && t.PlainTextContent == "" {
			return errors.New("plain text content is required for text template")
		}
	}

	// 验证模板变量
	if err := t.ValidateTemplateVariables(); err != nil {
		return err
	}

	return nil
}

// IsCurrentVersion 检查是否为当前版本
func (t *EmailTemplate) IsCurrentVersion() bool {
	return t.Version == 0
}

// CanEdit 检查是否可以编辑
func (t *EmailTemplate) CanEdit() bool {
	return t.Status == TemplateStatusDraft || t.Status == TemplateStatusPublished || t.Status == TemplateStatusDisabled
}

// CanPublish 检查是否可以发布
func (t *EmailTemplate) CanPublish() bool {
	return t.Status == TemplateStatusDraft && t.IsCurrentVersion()
}

// CanDisable 检查是否可以停用
func (t *EmailTemplate) CanDisable() bool {
	return t.Status == TemplateStatusPublished && t.IsCurrentVersion()
}

// CanDelete 检查是否可以删除
func (t *EmailTemplate) CanDelete() bool {
	return t.Status != TemplateStatusDeleted
}

// CanUse 检查是否可以使用（发送邮件）
func (t *EmailTemplate) CanUse() bool {
	return t.Status == TemplateStatusPublished && t.IsCurrentVersion()
}

// Publish 发布模板
func (t *EmailTemplate) Publish(publishedBy int64) error {
	if !t.CanPublish() {
		return errors.New("template cannot be published in current status")
	}

	now := time.Now()
	t.Status = TemplateStatusPublished
	t.PublishedAt = &now
	t.PublishedBy = &publishedBy
	t.UpdatedBy = publishedBy
	t.UpdatedAt = now

	return nil
}

// Disable 停用模板
func (t *EmailTemplate) Disable(updatedBy int64) error {
	if !t.CanDisable() {
		return errors.New("template cannot be disabled in current status")
	}

	t.Status = TemplateStatusDisabled
	t.UpdatedBy = updatedBy
	t.UpdatedAt = time.Now()

	return nil
}

// Enable 重新启用模板（从停用状态到草稿状态）
func (t *EmailTemplate) Enable(updatedBy int64) error {
	if t.Status != TemplateStatusDisabled {
		return errors.New("template cannot be enabled in current status")
	}

	t.Status = TemplateStatusDraft
	t.UpdatedBy = updatedBy
	t.UpdatedAt = time.Now()

	return nil
}

// Delete 删除模板（软删除）
func (t *EmailTemplate) Delete(deletedBy int64) error {
	if !t.CanDelete() {
		return errors.New("template cannot be deleted in current status")
	}

	now := time.Now()
	t.Status = TemplateStatusDeleted
	t.DeletedAt = &now
	t.UpdatedBy = deletedBy
	t.UpdatedAt = now

	return nil
}

// PrepareNewVersion 准备新版本（将当前版本备份为历史版本）
func (t *EmailTemplate) PrepareNewVersion() *EmailTemplate {
	// 创建历史版本（将当前版本号改为时间戳）
	historicalVersion := *t
	historicalVersion.Version = time.Now().UnixMilli() // 使用毫秒级时间戳
	historicalVersion.ParentVersion = &t.Version       // 设置父版本

	return &historicalVersion
}

// CreateNewVersionFrom 从历史版本创建新的当前版本
func (t *EmailTemplate) CreateNewVersionFrom(historicalTemplate *EmailTemplate, updatedBy int64, versionRemark string) {
	// 复制内容但保持当前版本为0
	t.Name = historicalTemplate.Name
	t.Subject = historicalTemplate.Subject
	t.HTMLContent = historicalTemplate.HTMLContent
	t.PlainTextContent = historicalTemplate.PlainTextContent
	t.Variables = historicalTemplate.Variables
	t.Type = historicalTemplate.Type
	t.Description = historicalTemplate.Description
	t.RateLimitPerMinute = historicalTemplate.RateLimitPerMinute
	t.RateLimitPerHour = historicalTemplate.RateLimitPerHour
	t.RateLimitPerDay = historicalTemplate.RateLimitPerDay
	t.ThumbnailURL = historicalTemplate.ThumbnailURL
	t.IsResponsive = historicalTemplate.IsResponsive

	// 重置版本和状态信息
	t.Version = 0                                 // 新版本为当前版本
	t.ParentVersion = &historicalTemplate.Version // 设置父版本
	t.Status = TemplateStatusDraft                // 重置为草稿状态
	t.VersionRemark = versionRemark
	t.PublishedAt = nil
	t.PublishedBy = nil
	t.UpdatedBy = updatedBy
	t.UpdatedAt = time.Now()
}

// UpdateContent 更新模板内容
func (t *EmailTemplate) UpdateContent(subject, htmlContent, plainTextContent string, variables map[string]TemplateVariable, updatedBy int64) error {
	if !t.CanEdit() {
		return errors.New("template cannot be edited in current status")
	}

	t.Subject = subject
	t.HTMLContent = htmlContent
	t.PlainTextContent = plainTextContent
	t.Variables = variables
	t.UpdatedBy = updatedBy
	t.UpdatedAt = time.Now()

	// 如果是已发布状态，编辑后回到草稿状态
	if t.Status == TemplateStatusPublished {
		t.Status = TemplateStatusDraft
		t.PublishedAt = nil
		t.PublishedBy = nil
	}

	return nil
}

// ExtractTemplateVariables 从模板内容中提取所有变量
func (t *EmailTemplate) ExtractTemplateVariables() []string {
	var variables []string
	seen := make(map[string]bool)

	// 正则表达式匹配 {{variable_name}} 格式
	re := regexp.MustCompile(`\{\{([^}]+)\}\}`)

	// 从主题中提取变量
	matches := re.FindAllStringSubmatch(t.Subject, -1)
	for _, match := range matches {
		if len(match) > 1 && !seen[match[1]] {
			variables = append(variables, strings.TrimSpace(match[1]))
			seen[match[1]] = true
		}
	}

	// 从HTML内容中提取变量
	matches = re.FindAllStringSubmatch(t.HTMLContent, -1)
	for _, match := range matches {
		if len(match) > 1 && !seen[match[1]] {
			variables = append(variables, strings.TrimSpace(match[1]))
			seen[match[1]] = true
		}
	}

	// 从纯文本内容中提取变量
	matches = re.FindAllStringSubmatch(t.PlainTextContent, -1)
	for _, match := range matches {
		if len(match) > 1 && !seen[match[1]] {
			variables = append(variables, strings.TrimSpace(match[1]))
			seen[match[1]] = true
		}
	}

	return variables
}

// ValidateTemplateVariables 验证模板变量是否在允许的参数列表中
func (t *EmailTemplate) ValidateTemplateVariables() error {
	invalidVariables := t.GetInvalidTemplateVariables()
	if len(invalidVariables) > 0 {
		logiface.GetLogger().Info(context.Background(), "模板中包含未定义的变量", logiface.Any("req", invalidVariables), logiface.String("content", t.HTMLContent))
		return fmt.Errorf("模板中包含未定义的变量: %s。请检查变量名称是否正确，或联系管理员添加这些变量", strings.Join(invalidVariables, ", "))
	}
	return nil
}

// GetInvalidTemplateVariables 获取模板中未定义的变量列表
func (t *EmailTemplate) GetInvalidTemplateVariables() []string {
	// 提取模板中的所有变量
	templateVariables := t.ExtractTemplateVariables()

	// 获取允许的系统变量列表
	allowedSystemVariables := t.GetAllowedSystemVariables()

	// 获取用户定义的变量列表
	userDefinedVariables := make([]string, 0, len(t.Variables))
	for varName := range t.Variables {
		userDefinedVariables = append(userDefinedVariables, varName)
	}

	// 合并所有允许的变量
	allowedVariables := make(map[string]bool)
	for _, varName := range allowedSystemVariables {
		allowedVariables[varName] = true
	}
	for _, varName := range userDefinedVariables {
		allowedVariables[varName] = true
	}

	// 检查模板中的变量是否都在允许列表中
	var invalidVariables []string
	for _, varName := range templateVariables {
		if !allowedVariables[varName] {
			invalidVariables = append(invalidVariables, varName)
		}
	}

	return invalidVariables
}

// GetAllowedSystemVariables 获取允许的系统变量列表
func (t *EmailTemplate) GetAllowedSystemVariables() []string {
	return []string{
		"current_date",
		"current_time",
		"company_name",
		"support_email",
	}
}

// ValidateSubjectInContent 验证内容中的主题格式
func (t *EmailTemplate) ValidateSubjectInContent(subject string) error {
	if len(subject) > 200 {
		return errors.New("subject length exceeds 200 characters")
	}
	return nil
}

// ValidateSubjectVariables 验证主题中的变量是否存在
func (t *EmailTemplate) ValidateSubjectVariables(subject string) error {
	invalidVariables := t.GetInvalidSubjectVariables(subject)
	if len(invalidVariables) > 0 {
		return fmt.Errorf("主题中包含未定义的变量: %s。请检查变量名称是否正确，或联系管理员添加这些变量", strings.Join(invalidVariables, ", "))
	}
	return nil
}

// GetInvalidSubjectVariables 获取主题中未定义的变量列表
func (t *EmailTemplate) GetInvalidSubjectVariables(subject string) []string {
	// 提取主题中的变量
	variables := t.ExtractVariablesFromText(subject)

	// 获取允许的系统变量列表
	allowedSystemVariables := t.GetAllowedSystemVariables()

	// 获取用户定义的变量列表
	userDefinedVariables := make([]string, 0, len(t.Variables))
	for varName := range t.Variables {
		userDefinedVariables = append(userDefinedVariables, varName)
	}

	// 合并所有允许的变量
	allowedVariables := make(map[string]bool)
	for _, varName := range allowedSystemVariables {
		allowedVariables[varName] = true
	}
	for _, varName := range userDefinedVariables {
		allowedVariables[varName] = true
	}

	// 检查主题中的变量是否都在允许列表中
	var invalidVariables []string
	for _, variable := range variables {
		if !allowedVariables[variable] {
			invalidVariables = append(invalidVariables, variable)
		}
	}

	return invalidVariables
}

// SetSubjectFromContent 根据内容设置主题
func (t *EmailTemplate) SetSubjectFromContent(subject string) error {
	if err := t.ValidateSubjectInContent(subject); err != nil {
		return err
	}

	// 验证主题中的变量
	if err := t.ValidateSubjectVariables(subject); err != nil {
		return err
	}

	t.Subject = subject
	return nil
}

// ExtractVariablesFromText 从文本中提取变量（与HTML内容共用）
func (t *EmailTemplate) ExtractVariablesFromText(text string) []string {
	// 使用正则表达式提取 {{variable}} 格式的变量
	re := regexp.MustCompile(`\{\{([^}]+)\}\}`)
	matches := re.FindAllStringSubmatch(text, -1)

	variables := make([]string, 0)
	for _, match := range matches {
		if len(match) > 1 {
			variables = append(variables, strings.TrimSpace(match[1]))
		}
	}

	return variables
}

// TemplateTrackingOptions 模板追踪选项配置
type TemplateTrackingOptions struct {
	Enabled     bool                `json:"enabled"`
	UTM         *UTMOptions         `json:"utm,omitempty"`
	Attribution *AttributionOptions `json:"attribution,omitempty"`
	Pixel       *PixelOptions       `json:"pixel,omitempty"`
	Redirect    *RedirectOptions    `json:"redirect,omitempty"`
	Domain      *DomainOptions      `json:"domain,omitempty"`
}

// UTMOptions UTM参数配置
type UTMOptions struct {
	AutoAppend bool              `json:"autoAppend"`
	Campaign   string            `json:"campaign"`
	Content    string            `json:"content"`
	Source     string            `json:"source"`
	Medium     string            `json:"medium"`
	Term       string            `json:"term"`
	Custom     map[string]string `json:"custom,omitempty"`
}

// AttributionOptions 归因配置
type AttributionOptions struct {
	WindowHours      int  `json:"windowHours"`
	EnableConversion bool `json:"enableConversion"`
}

// PixelOptions 像素追踪配置
type PixelOptions struct {
	EnableAmp    bool `json:"enableAmp"`
	EnableBeacon bool `json:"enableBeacon"`
}

// RedirectOptions 链接重定向配置
type RedirectOptions struct {
	Whitelist []string `json:"whitelist"`
	Blacklist []string `json:"blacklist"`
}

// DomainOptions 域名配置
type DomainOptions struct {
	TrackingDomain string `json:"trackingDomain"`
	Status         string `json:"status"` // verified, pending, failed
	DNSValidated   bool   `json:"dnsValidated"`
}

// NewDefaultTrackingOptions 创建默认追踪选项
func NewDefaultTrackingOptions() *TemplateTrackingOptions {
	return &TemplateTrackingOptions{
		Enabled: true,
		UTM: &UTMOptions{
			AutoAppend: true,
			Medium:     "email",
		},
		Attribution: &AttributionOptions{
			WindowHours:      24,
			EnableConversion: true,
		},
		Pixel: &PixelOptions{
			EnableAmp:    true,
			EnableBeacon: true,
		},
		Redirect: &RedirectOptions{
			Whitelist: []string{},
			Blacklist: []string{},
		},
		Domain: &DomainOptions{
			Status:       "pending",
			DNSValidated: false,
		},
	}
}

// SetTrackingOptions 设置追踪选项
func (t *EmailTemplate) SetTrackingOptions(options *TemplateTrackingOptions) {
	t.TrackingOptions = options
	if options != nil {
		t.TrackingEnabled = options.Enabled
	} else {
		t.TrackingEnabled = false
	}
}

// EnableTracking 启用追踪
func (t *EmailTemplate) EnableTracking() {
	t.TrackingEnabled = true
	if t.TrackingOptions == nil {
		t.TrackingOptions = NewDefaultTrackingOptions()
	} else {
		t.TrackingOptions.Enabled = true
	}
}

// DisableTracking 禁用追踪
func (t *EmailTemplate) DisableTracking() {
	t.TrackingEnabled = false
	if t.TrackingOptions != nil {
		t.TrackingOptions.Enabled = false
	}
}
