package service

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"text/template"
	"time"

	"gitee.com/heiyee/platforms/email/internal/domain/template/entity"
	trackingEntity "gitee.com/heiyee/platforms/email/internal/domain/tracking/entity"
	trackingService "gitee.com/heiyee/platforms/email/internal/domain/tracking/service"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"github.com/PuerkitoBio/goquery"
)

// TemplateRenderMode 模板渲染模式
type TemplateRenderMode int

const (
	// RenderModePreview 预览模式：用于模板维护时的预览，容错性强
	RenderModePreview TemplateRenderMode = iota
	// RenderModeValidation 验证模式：用于发送前的参数检查，严格验证
	RenderModeValidation
	// RenderModeSending 发送模式：用于实际发送时的渲染，包含版本控制
	RenderModeSending
)

// TemplateRenderRequest 模板渲染请求
type TemplateRenderRequest struct {
	Template       *entity.EmailTemplate  `json:"template"`                  // 模板实体
	Variables      map[string]interface{} `json:"variables"`                 // 变量值
	Mode           TemplateRenderMode     `json:"mode"`                      // 渲染模式
	Context        *TemplateRenderContext `json:"context,omitempty"`         // 渲染上下文
	TrackingConfig *TrackingConfig        `json:"tracking_config,omitempty"` // 追踪配置
}

// TrackingConfig 追踪配置
type TrackingConfig struct {
	EnableTracking bool   `json:"enable_tracking"`           // 是否启用追踪
	CampaignID     int64  `json:"campaign_id,omitempty"`     // 活动ID
	RecipientEmail string `json:"recipient_email"`           // 收件人邮箱
	TrackingDomain string `json:"tracking_domain,omitempty"` // 追踪域名
}

// TemplateRenderContext 模板渲染上下文
type TemplateRenderContext struct {
	TenantID     int64                  `json:"tenant_id"`     // 租户ID
	UserID       int64                  `json:"user_id"`       // 用户ID
	EmailMessage interface{}            `json:"email_message"` // 邮件消息（发送模式时需要）
	SystemVars   map[string]interface{} `json:"system_vars"`   // 系统变量
}

// TemplateRenderResult 模板渲染结果
type TemplateRenderResult struct {
	Subject         string                `json:"subject"`           // 渲染后的主题
	HTMLContent     string                `json:"html_content"`      // 渲染后的HTML内容
	TextContent     string                `json:"text_content"`      // 渲染后的文本内容
	TemplateVersion *int64                `json:"template_version"`  // 模板版本（发送模式）
	UsedVariables   []string              `json:"used_variables"`    // 实际使用的变量
	MissingVars     []string              `json:"missing_variables"` // 缺失的必需变量
	InvalidVars     []string              `json:"invalid_variables"` // 无效的变量
	Errors          []TemplateRenderError `json:"errors,omitempty"`  // 渲染错误
}

// TemplateRenderError 模板渲染错误
type TemplateRenderError struct {
	Type    string `json:"type"`    // 错误类型：subject/html/text/variable
	Message string `json:"message"` // 错误消息
	Field   string `json:"field"`   // 错误字段
}

// TemplateRenderer 统一的模板渲染器接口
type TemplateRenderer interface {
	// RenderTemplate 渲染模板
	RenderTemplate(ctx context.Context, req *TemplateRenderRequest) (*TemplateRenderResult, error)

	// ValidateTemplateVariables 验证模板变量
	ValidateTemplateVariables(ctx context.Context, template *entity.EmailTemplate, variables map[string]interface{}) (*TemplateVariableValidationResult, error)

	// ExtractTemplateVariables 提取模板中使用的所有变量
	ExtractTemplateVariables(ctx context.Context, template *entity.EmailTemplate) ([]string, error)
}

// TemplateVariableValidationResult 模板变量验证结果
type TemplateVariableValidationResult struct {
	Valid           bool     `json:"valid"`             // 是否通过验证
	MissingRequired []string `json:"missing_required"`  // 缺失的必需变量
	InvalidVars     []string `json:"invalid_variables"` // 无效变量（模板中使用但未定义）
	UnusedVars      []string `json:"unused_variables"`  // 未使用的变量（定义了但模板中未使用）
	UsedVars        []string `json:"used_variables"`    // 实际使用的变量
}

// TemplateRendererImpl 模板渲染器实现
type TemplateRendererImpl struct {
	logger           logiface.Logger
	trackingParamGen *trackingService.TrackingParameterGenerator
}

// NewTemplateRenderer 创建模板渲染器
func NewTemplateRenderer(
	logger logiface.Logger,
	trackingParamGen *trackingService.TrackingParameterGenerator,
) TemplateRenderer {
	return &TemplateRendererImpl{
		logger:           logger,
		trackingParamGen: trackingParamGen,
	}
}

// RenderTemplate 渲染模板
func (r *TemplateRendererImpl) RenderTemplate(ctx context.Context, req *TemplateRenderRequest) (*TemplateRenderResult, error) {
	result := &TemplateRenderResult{
		UsedVariables: []string{},
		MissingVars:   []string{},
		InvalidVars:   []string{},
		Errors:        []TemplateRenderError{},
	}

	// 验证请求参数
	if req.Template == nil {
		return nil, fmt.Errorf("template is required")
	}

	// 设置模板版本（发送模式）
	if req.Mode == RenderModeSending {
		result.TemplateVersion = &req.Template.Version
	}

	// 准备变量
	vars, err := r.prepareVariables(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to prepare variables: %w", err)
	}

	// 根据模式选择渲染策略
	switch req.Mode {
	case RenderModePreview:
		return r.renderPreviewMode(ctx, req.Template, vars, result)
	case RenderModeValidation:
		return r.renderValidationMode(ctx, req.Template, vars, result)
	case RenderModeSending:
		return r.renderSendingMode(ctx, req, vars, result)
	default:
		return nil, fmt.Errorf("unsupported render mode: %d", req.Mode)
	}
}

// prepareVariables 准备变量，合并用户变量和系统变量
func (r *TemplateRendererImpl) prepareVariables(ctx context.Context, req *TemplateRenderRequest) (map[string]interface{}, error) {
	vars := make(map[string]interface{})

	// 1. 添加用户变量
	for k, v := range req.Variables {
		vars[k] = v
	}

	// 2. 添加系统变量
	systemVars := r.getSystemVariables(ctx, req.Context)
	for k, v := range systemVars {
		// 系统变量优先级较低，不覆盖用户变量
		if _, exists := vars[k]; !exists {
			vars[k] = v
		}
	}

	return vars, nil
}

// getSystemVariables 获取系统变量
func (r *TemplateRendererImpl) getSystemVariables(ctx context.Context, renderCtx *TemplateRenderContext) map[string]interface{} {
	vars := make(map[string]interface{})

	// 时间相关变量
	now := time.Now()
	vars["current_date"] = now.Format("2006-01-02")
	vars["current_time"] = now.Format("2006-01-02 15:04:05")

	// 从上下文获取其他系统变量
	if renderCtx != nil && renderCtx.SystemVars != nil {
		for k, v := range renderCtx.SystemVars {
			vars[k] = v
		}
	}

	return vars
}

// renderPreviewMode 预览模式渲染：容错性强，失败时返回原始内容
func (r *TemplateRendererImpl) renderPreviewMode(ctx context.Context, tmpl *entity.EmailTemplate, vars map[string]interface{}, result *TemplateRenderResult) (*TemplateRenderResult, error) {
	// 转换变量为字符串
	stringVars := r.convertToStringVars(vars)

	// 渲染主题
	result.Subject = r.renderContentSafe(tmpl.Subject, stringVars)

	// 渲染HTML内容
	result.HTMLContent = r.renderContentSafe(tmpl.HTMLContent, stringVars)

	// 渲染文本内容
	result.TextContent = r.renderContentSafe(tmpl.PlainTextContent, stringVars)

	// 提取使用的变量
	result.UsedVariables = r.extractUsedVariables(tmpl)

	return result, nil
}

// renderValidationMode 验证模式渲染：严格验证，支持复杂模板语法
func (r *TemplateRendererImpl) renderValidationMode(ctx context.Context, tmpl *entity.EmailTemplate, vars map[string]interface{}, result *TemplateRenderResult) (*TemplateRenderResult, error) {
	// 先进行变量验证
	validation, err := r.ValidateTemplateVariables(ctx, tmpl, vars)
	if err != nil {
		return nil, fmt.Errorf("variable validation failed: %w", err)
	}

	result.UsedVariables = validation.UsedVars
	result.MissingVars = validation.MissingRequired
	result.InvalidVars = validation.InvalidVars

	// 如果有验证错误，记录但继续渲染
	if !validation.Valid {
		if len(validation.MissingRequired) > 0 {
			result.Errors = append(result.Errors, TemplateRenderError{
				Type:    "variable",
				Message: fmt.Sprintf("Missing required variables: %s", strings.Join(validation.MissingRequired, ", ")),
				Field:   "variables",
			})
		}
		if len(validation.InvalidVars) > 0 {
			result.Errors = append(result.Errors, TemplateRenderError{
				Type:    "variable",
				Message: fmt.Sprintf("Invalid variables: %s", strings.Join(validation.InvalidVars, ", ")),
				Field:   "variables",
			})
		}
	}

	// 转换变量为字符串
	stringVars := r.convertToStringVars(vars)

	// 渲染各部分内容
	var err1, err2, err3 error
	result.Subject, err1 = r.renderContentStrict(tmpl.Subject, stringVars)
	result.HTMLContent, err2 = r.renderContentStrict(tmpl.HTMLContent, stringVars)
	result.TextContent, err3 = r.renderContentStrict(tmpl.PlainTextContent, stringVars)

	// 收集渲染错误
	if err1 != nil {
		result.Errors = append(result.Errors, TemplateRenderError{
			Type:    "subject",
			Message: err1.Error(),
			Field:   "subject",
		})
	}
	if err2 != nil {
		result.Errors = append(result.Errors, TemplateRenderError{
			Type:    "html",
			Message: err2.Error(),
			Field:   "html_content",
		})
	}
	if err3 != nil {
		result.Errors = append(result.Errors, TemplateRenderError{
			Type:    "text",
			Message: err3.Error(),
			Field:   "text_content",
		})
	}

	return result, nil
}

// renderSendingMode 发送模式渲染：用于实际发送，包含版本控制
func (r *TemplateRendererImpl) renderSendingMode(ctx context.Context, req *TemplateRenderRequest, vars map[string]interface{}, result *TemplateRenderResult) (*TemplateRenderResult, error) {
	// 转换变量为字符串
	stringVars := r.convertToStringVars(vars)

	// 渲染各部分内容（使用严格模式）
	var err error
	result.Subject, err = r.renderContentStrict(req.Template.Subject, stringVars)
	if err != nil {
		return nil, fmt.Errorf("failed to render subject: %w", err)
	}

	result.HTMLContent, err = r.renderContentStrict(req.Template.HTMLContent, stringVars)
	if err != nil {
		return nil, fmt.Errorf("failed to render HTML content: %w", err)
	}

	// 文本内容可选
	if req.Template.PlainTextContent != "" {
		result.TextContent, err = r.renderContentStrict(req.Template.PlainTextContent, stringVars)
		if err != nil {
			return nil, fmt.Errorf("failed to render text content: %w", err)
		}
	}

	// 提取使用的变量
	result.UsedVariables = r.extractUsedVariables(req.Template)

	// 应用追踪功能（如果启用）
	if req.TrackingConfig != nil && req.TrackingConfig.EnableTracking && r.trackingParamGen != nil {
		result, err = r.applyTrackingToContent(ctx, result, req.TrackingConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to apply tracking: %w", err)
		}
	}

	return result, nil
}

// applyTrackingToContent 应用追踪功能到内容
func (r *TemplateRendererImpl) applyTrackingToContent(ctx context.Context, result *TemplateRenderResult, config *TrackingConfig) (*TemplateRenderResult, error) {
	// 只处理HTML内容
	if result.HTMLContent == "" {
		return result, nil
	}

	// 解析HTML内容
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(result.HTMLContent))
	if err != nil {
		return nil, fmt.Errorf("failed to parse HTML content: %w", err)
	}

	// 生成基础追踪参数
	campaignID := fmt.Sprintf("%d", config.CampaignID)
	subscriberID := config.RecipientEmail // 使用邮箱作为订阅者ID
	messageID := r.generateMessageID(config.CampaignID, config.RecipientEmail)
	trackingDomain := config.TrackingDomain
	if trackingDomain == "" {
		trackingDomain = "track.example.com" // 默认追踪域名
	}

	// 重写链接
	err = r.rewriteLinksForTracking(ctx, doc, campaignID, subscriberID, messageID, trackingDomain)
	if err != nil {
		return nil, fmt.Errorf("failed to rewrite links: %w", err)
	}

	// 注入追踪像素
	err = r.injectTrackingPixel(ctx, doc, campaignID, subscriberID, messageID, trackingDomain)
	if err != nil {
		return nil, fmt.Errorf("failed to inject tracking pixel: %w", err)
	}

	// 获取修改后的HTML
	modifiedHTML, err := doc.Html()
	if err != nil {
		return nil, fmt.Errorf("failed to get modified HTML: %w", err)
	}

	// 更新结果
	result.HTMLContent = modifiedHTML

	return result, nil
}

// rewriteLinksForTracking 重写链接进行追踪
func (r *TemplateRendererImpl) rewriteLinksForTracking(ctx context.Context, doc *goquery.Document, campaignID, subscriberID, messageID, trackingDomain string) error {
	var rewriteErr error
	linkCounter := 0

	doc.Find("a[href]").Each(func(i int, s *goquery.Selection) {
		if rewriteErr != nil {
			return
		}

		originalURL, exists := s.Attr("href")
		if !exists || originalURL == "" {
			return
		}

		// 跳过邮件链接和已经是追踪链接的URL
		if strings.HasPrefix(originalURL, "mailto:") || strings.Contains(originalURL, "/track/") {
			return
		}

		// 生成链接ID
		linkCounter++
		linkID := fmt.Sprintf("link_%d", linkCounter)

		// 创建点击参数
		clickParams := trackingEntity.ClickParams{
			CampaignID:     campaignID,
			SubscriberID:   subscriberID,
			MessageID:      messageID,
			LinkID:         linkID,
			TrackingDomain: trackingDomain,
			TenantID:       1, // TODO: 从上下文获取租户ID
		}

		// 生成追踪链接
		trackingURL, err := r.trackingParamGen.RewriteClickURL(originalURL, clickParams)
		if err != nil {
			rewriteErr = fmt.Errorf("failed to generate tracking URL for %s: %w", originalURL, err)
			return
		}

		// 设置新的href
		s.SetAttr("href", trackingURL)
	})

	return rewriteErr
}

// injectTrackingPixel 注入追踪像素
func (r *TemplateRendererImpl) injectTrackingPixel(ctx context.Context, doc *goquery.Document, campaignID, subscriberID, messageID, trackingDomain string) error {
	// 创建打开像素参数
	pixelParams := trackingEntity.OpenPixelParams{
		CampaignID:     campaignID,
		SubscriberID:   subscriberID,
		MessageID:      messageID,
		TrackingDomain: trackingDomain,
		TenantID:       1, // TODO: 从上下文获取租户ID
	}

	// 生成追踪像素URL
	pixelURL, err := r.trackingParamGen.GenerateOpenPixelURL(pixelParams)
	if err != nil {
		return fmt.Errorf("failed to generate tracking pixel URL: %w", err)
	}

	// 创建追踪像素IMG标签
	pixelHTML := fmt.Sprintf(`<img src="%s" width="1" height="1" alt="" style="display:none;">`, pixelURL)

	// 尝试在</body>标签前插入
	body := doc.Find("body")
	if body.Length() > 0 {
		body.AppendHtml(pixelHTML)
	} else {
		// 如果没有body标签，直接附加到文档末尾
		doc.Find("html").AppendHtml(pixelHTML)
	}

	return nil
}

// generateMessageID 生成消息ID
func (r *TemplateRendererImpl) generateMessageID(campaignID int64, recipientEmail string) string {
	timestamp := time.Now().Unix()
	return fmt.Sprintf("msg_%d_%d_%s", campaignID, timestamp, r.hashEmail(recipientEmail))
}

// hashEmail 对邮箱进行简单哈希
func (r *TemplateRendererImpl) hashEmail(email string) string {
	if len(email) > 8 {
		return email[:8]
	}
	return email
}

// renderContentSafe 安全渲染内容：失败时返回原始内容
func (r *TemplateRendererImpl) renderContentSafe(content string, vars map[string]string) string {
	if content == "" {
		return ""
	}

	// 简单变量替换 {{variable_name}} → {{.variable_name}}
	convertedContent := content
	for key := range vars {
		oldPlaceholder := fmt.Sprintf("{{%s}}", key)
		newPlaceholder := fmt.Sprintf("{{.%s}}", key)
		convertedContent = strings.ReplaceAll(convertedContent, oldPlaceholder, newPlaceholder)
	}

	// 尝试渲染
	tmpl, err := template.New("content").Parse(convertedContent)
	if err != nil {
		return content // 失败时返回原始内容
	}

	var buffer strings.Builder
	if err := tmpl.Execute(&buffer, vars); err != nil {
		return content // 失败时返回原始内容
	}

	return buffer.String()
}

// renderContentStrict 严格渲染内容：失败时返回错误
func (r *TemplateRendererImpl) renderContentStrict(content string, vars map[string]string) (string, error) {
	if content == "" {
		return "", nil
	}

	// 支持复杂模板语法的变量转换
	convertedContent := r.convertAdvancedTemplateVars(content, vars)

	// 创建模板并渲染
	tmpl, err := template.New("content").Option("missingkey=zero").Parse(convertedContent)
	if err != nil {
		return "", fmt.Errorf("failed to parse template: %w", err)
	}

	var buffer strings.Builder
	if err := tmpl.Execute(&buffer, vars); err != nil {
		return "", fmt.Errorf("failed to execute template: %w", err)
	}

	return buffer.String(), nil
}

// convertAdvancedTemplateVars 转换高级模板变量：支持保留关键字
func (r *TemplateRendererImpl) convertAdvancedTemplateVars(content string, vars map[string]string) string {
	// 正则表达式匹配 {{variable_name}} 但保留关键字
	mustacheVarPattern := regexp.MustCompile(`\{\{\s*([a-zA-Z0-9_]+)\s*\}\}`)
	reservedKeywords := map[string]struct{}{
		"if": {}, "else": {}, "end": {}, "range": {}, "with": {}, "block": {}, "define": {}, "template": {},
	}

	return mustacheVarPattern.ReplaceAllStringFunc(content, func(m string) string {
		submatches := mustacheVarPattern.FindStringSubmatch(m)
		if len(submatches) != 2 {
			return m
		}
		name := strings.TrimSpace(submatches[1])
		if _, isReserved := reservedKeywords[name]; isReserved {
			return m
		}
		return "{{." + name + "}}"
	})
}

// convertToStringVars 转换变量为字符串类型
func (r *TemplateRendererImpl) convertToStringVars(vars map[string]interface{}) map[string]string {
	stringVars := make(map[string]string)
	for k, v := range vars {
		if v != nil {
			stringVars[k] = fmt.Sprintf("%v", v)
		} else {
			stringVars[k] = ""
		}
	}
	return stringVars
}

// extractUsedVariables 提取模板中使用的变量
func (r *TemplateRendererImpl) extractUsedVariables(tmpl *entity.EmailTemplate) []string {
	var variables []string
	seen := make(map[string]bool)

	// 正则表达式匹配 {{variable_name}}
	re := regexp.MustCompile(`\{\{([^}]+)\}\}`)

	// 从各个字段中提取变量
	contents := []string{tmpl.Subject, tmpl.HTMLContent, tmpl.PlainTextContent}
	for _, content := range contents {
		matches := re.FindAllStringSubmatch(content, -1)
		for _, match := range matches {
			if len(match) > 1 {
				varName := strings.TrimSpace(match[1])
				// 去掉点号前缀（如果有）
				varName = strings.TrimPrefix(varName, ".")
				if !seen[varName] && varName != "" {
					variables = append(variables, varName)
					seen[varName] = true
				}
			}
		}
	}

	return variables
}

// ValidateTemplateVariables 验证模板变量
func (r *TemplateRendererImpl) ValidateTemplateVariables(ctx context.Context, tmpl *entity.EmailTemplate, variables map[string]interface{}) (*TemplateVariableValidationResult, error) {
	result := &TemplateVariableValidationResult{
		Valid:           true,
		MissingRequired: []string{},
		InvalidVars:     []string{},
		UnusedVars:      []string{},
		UsedVars:        []string{},
	}

	// 提取模板中实际使用的变量
	usedVars := r.extractUsedVariables(tmpl)
	result.UsedVars = usedVars

	// 获取允许的变量（系统变量 + 模板定义的变量）
	allowedVars := make(map[string]bool)

	// 添加系统变量
	systemVars := []string{"current_date", "current_time", "company_name", "support_email"}
	for _, v := range systemVars {
		allowedVars[v] = true
	}

	// 添加模板定义的变量
	for varName, varDef := range tmpl.Variables {
		allowedVars[varName] = true

		// 检查必需变量是否提供
		if varDef.Required {
			if _, provided := variables[varName]; !provided {
				result.MissingRequired = append(result.MissingRequired, varName)
				result.Valid = false
			}
		}
	}

	// 检查模板中使用的变量是否都被定义
	for _, usedVar := range usedVars {
		if !allowedVars[usedVar] {
			result.InvalidVars = append(result.InvalidVars, usedVar)
			result.Valid = false
		}
	}

	// 检查未使用的变量
	usedVarMap := make(map[string]bool)
	for _, v := range usedVars {
		usedVarMap[v] = true
	}

	for varName := range tmpl.Variables {
		if !usedVarMap[varName] {
			result.UnusedVars = append(result.UnusedVars, varName)
		}
	}

	return result, nil
}

// ExtractTemplateVariables 提取模板中使用的所有变量
func (r *TemplateRendererImpl) ExtractTemplateVariables(ctx context.Context, tmpl *entity.EmailTemplate) ([]string, error) {
	return r.extractUsedVariables(tmpl), nil
}
