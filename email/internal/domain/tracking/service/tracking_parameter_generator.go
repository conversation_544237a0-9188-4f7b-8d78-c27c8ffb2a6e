package service

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"io"
	"net/url"
	"strings"
	"time"

	"gitee.com/heiyee/platforms/email/internal/domain/tracking/entity"
)

// TrackingParameterGenerator 追踪参数生成器
type TrackingParameterGenerator struct {
	secretManager SecretManager
	timeProvider  TimeProvider
}

// SecretManager 密钥管理接口
type SecretManager interface {
	GetTrackingSecret(tenantID int64) (string, error)
}

// TimeProvider 时间提供接口
type TimeProvider interface {
	Now() time.Time
}

// NewTrackingParameterGenerator 创建追踪参数生成器
func NewTrackingParameterGenerator(secretManager SecretManager, timeProvider TimeProvider) *TrackingParameterGenerator {
	return &TrackingParameterGenerator{
		secretManager: secretManager,
		timeProvider:  timeProvider,
	}
}

// GenerateOpenPixelURL 生成打开像素URL
func (g *TrackingParameterGenerator) GenerateOpenPixelURL(params entity.OpenPixelParams) (string, error) {
	secret, err := g.secretManager.GetTrackingSecret(params.TenantID)
	if err != nil {
		return "", fmt.Errorf("failed to get tracking secret: %w", err)
	}

	timestamp := g.timeProvider.Now().Unix()
	nonce := g.generateNonce()

	baseString := fmt.Sprintf("cid=%s&sid=%s&mid=%s&ts=%d&nonce=%s",
		url.QueryEscape(params.CampaignID),
		url.QueryEscape(params.SubscriberID),
		url.QueryEscape(params.MessageID),
		timestamp,
		nonce,
	)

	signature := g.generateHMAC(baseString, secret)

	return fmt.Sprintf("https://%s/api/tracking/open?%s&sig=%s",
		params.TrackingDomain, baseString, signature), nil
}

// GenerateBeaconURL 生成Beacon追踪URL
func (g *TrackingParameterGenerator) GenerateBeaconURL(params entity.OpenPixelParams) (string, error) {
	pixelURL, err := g.GenerateOpenPixelURL(params)
	if err != nil {
		return "", err
	}
	return strings.Replace(pixelURL, "/api/tracking/open", "/api/tracking/beacon", 1), nil
}

// GenerateAMPPixelURL 生成AMP像素URL
func (g *TrackingParameterGenerator) GenerateAMPPixelURL(params entity.OpenPixelParams) (string, error) {
	pixelURL, err := g.GenerateOpenPixelURL(params)
	if err != nil {
		return "", err
	}
	return strings.Replace(pixelURL, "/api/tracking/open", "/api/tracking/amp/open", 1), nil
}

// RewriteClickURL 重写点击链接
func (g *TrackingParameterGenerator) RewriteClickURL(originalURL string, params entity.ClickParams) (string, error) {
	secret, err := g.secretManager.GetTrackingSecret(params.TenantID)
	if err != nil {
		return "", fmt.Errorf("failed to get tracking secret: %w", err)
	}

	timestamp := g.timeProvider.Now().Unix()
	nonce := g.generateNonce()

	// 加密目标URL
	encryptedDest, err := g.encryptDestination(originalURL, secret)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt destination: %w", err)
	}

	baseString := fmt.Sprintf("cid=%s&sid=%s&mid=%s&lid=%s&dest=%s&ts=%d&nonce=%s",
		url.QueryEscape(params.CampaignID),
		url.QueryEscape(params.SubscriberID),
		url.QueryEscape(params.MessageID),
		url.QueryEscape(params.LinkID),
		url.QueryEscape(encryptedDest),
		timestamp,
		nonce,
	)

	signature := g.generateHMAC(baseString, secret)

	return fmt.Sprintf("https://%s/api/tracking/redirect?%s&sig=%s",
		params.TrackingDomain, baseString, signature), nil
}

// generateNonce 生成随机数
func (g *TrackingParameterGenerator) generateNonce() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// 降级方案：使用时间戳
		return fmt.Sprintf("%d", g.timeProvider.Now().UnixNano())
	}
	return hex.EncodeToString(bytes)
}

// generateHMAC 生成HMAC签名
func (g *TrackingParameterGenerator) generateHMAC(message, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(message))
	return base64.URLEncoding.EncodeToString(h.Sum(nil))
}

// encryptDestination 加密目标地址
func (g *TrackingParameterGenerator) encryptDestination(destination, secret string) (string, error) {
	key := sha256.Sum256([]byte(secret))

	block, err := aes.NewCipher(key[:])
	if err != nil {
		return "", err
	}

	// 使用GCM模式进行加密
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(destination), nil)
	return base64.URLEncoding.EncodeToString(ciphertext), nil
}

// VerifyAndDecryptDestination 验证并解密目标地址
func (g *TrackingParameterGenerator) VerifyAndDecryptDestination(encryptedDest, secret string) (string, error) {
	key := sha256.Sum256([]byte(secret))

	block, err := aes.NewCipher(key[:])
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	data, err := base64.URLEncoding.DecodeString(encryptedDest)
	if err != nil {
		return "", err
	}

	if len(data) < gcm.NonceSize() {
		return "", fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext := data[:gcm.NonceSize()], data[gcm.NonceSize():]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// VerifySignature 验证签名
func (g *TrackingParameterGenerator) VerifySignature(baseString, signature, secret string) bool {
	expectedSig := g.generateHMAC(baseString, secret)
	return hmac.Equal([]byte(signature), []byte(expectedSig))
}
