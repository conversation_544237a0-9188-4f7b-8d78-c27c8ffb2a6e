package entity

import (
	"time"
)

// OpenPixelParams 打开像素参数
type OpenPixelParams struct {
	CampaignID     string
	SubscriberID   string
	MessageID      string
	TrackingDomain string
	TenantID       int64
}

// ClickParams 点击追踪参数
type ClickParams struct {
	CampaignID     string
	SubscriberID   string
	MessageID      string
	LinkID         string
	TrackingDomain string
	TenantID       int64
}

// TrackingConfig 追踪配置
type TrackingConfig struct {
	ID                    int64     `json:"id"`
	TenantID              int64     `json:"tenant_id"`
	TrackingDomain        string    `json:"tracking_domain"`
	TrackingSecret        string    `json:"tracking_secret"`
	UTMEnabled            bool      `json:"utm_enabled"`
	ConversionWindowHours int       `json:"conversion_window_hours"`
	LinkDomainWhitelist   []string  `json:"link_domain_whitelist"`
	CreatedAt             time.Time `json:"created_at"`
	UpdatedAt             time.Time `json:"updated_at"`
}

// TrackingOptions 模板追踪选项
type TrackingOptions struct {
	Enabled               bool       `json:"enabled"`
	UTM                   UTMOptions `json:"utm"`
	ConversionWindowHours int        `json:"conversion_window_hours"`
	CustomTrackingDomain  *string    `json:"custom_tracking_domain"`
	LinkDomainWhitelist   []string   `json:"link_domain_whitelist"`
}

// UTMOptions UTM参数选项
type UTMOptions struct {
	Enabled  bool              `json:"enabled"`
	Defaults map[string]string `json:"defaults"`
}

// ProcessTemplateRequest 模板处理请求
type ProcessTemplateRequest struct {
	TenantID        int64             `json:"tenant_id"`
	TemplateID      int64             `json:"template_id"`
	CampaignID      string            `json:"campaign_id"`
	SubscriberID    string            `json:"subscriber_id"`
	MessageID       string            `json:"message_id"`
	Variables       map[string]string `json:"variables"`
	TrackingEnabled bool              `json:"tracking_enabled"`
	TrackingOptions *TrackingOptions  `json:"tracking_options"`
}

// ProcessedTemplate 处理后的模板
type ProcessedTemplate struct {
	HTMLContent   string         `json:"html_content"`
	TextContent   string         `json:"text_content"`
	Subject       string         `json:"subject"`
	TrackingLinks []TrackingLink `json:"tracking_links"`
}

// TrackingLink 追踪链接信息
type TrackingLink struct {
	LinkID      string `json:"link_id"`
	OriginalURL string `json:"original_url"`
	TrackingURL string `json:"tracking_url"`
}

// TrackingParams 追踪参数结构
type TrackingParams struct {
	CID   string `json:"cid"`   // Campaign ID
	SID   string `json:"sid"`   // Subscriber ID
	MID   string `json:"mid"`   // Message ID
	LID   string `json:"lid"`   // Link ID (for clicks)
	TS    int64  `json:"ts"`    // Timestamp
	Nonce string `json:"nonce"` // Random nonce
	Sig   string `json:"sig"`   // HMAC signature
	Dest  string `json:"dest"`  // Encrypted destination (for clicks)
}
