package model

import "time"

// EmailProviderModel 邮箱服务商模型（用于预设配置与图标展示）
type EmailProviderModel struct {
	ID          int64  `gorm:"primaryKey;autoIncrement:false"`
	Name        string `gorm:"uniqueIndex;size:100;not null"` // 唯一标识（例如: gmail、outlook、qq、163、aliyun、smtp）
	DisplayName string `gorm:"size:100;not null"`             // 展示名称
	IconURL     string `gorm:"size:255"`                      // 图标 URL

	// SMTP 预设
	SMTPHost string `gorm:"size:150"`
	SMTPPort int    `gorm:"default:0"`

	// IMAP 预设
	IMAPHost string `gorm:"size:150"`
	IMAPPort int    `gorm:"default:0"`

	// POP3 预设
	POP3Host string `gorm:"size:150"`
	POP3Port int    `gorm:"default:0"`

	UseSSL  bool `gorm:"default:true"`
	Enabled bool `gorm:"default:true"`

	// 手动维护的排序字段，返回时按升序排列
	SortOrder int `gorm:"default:0"`

	CreatedAt time.Time
	UpdatedAt time.Time
}

// TableName overrides the default GORM table name
func (EmailProviderModel) TableName() string {
	return "email_providers"
}
