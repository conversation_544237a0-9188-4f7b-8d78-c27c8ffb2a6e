package model

import (
	"time"
)

// EmailMessageContentModel 邮件消息内容快照模型（按方式B独立表存储大字段）
type EmailMessageContentModel struct {
	EmailID         int64     `gorm:"primaryKey;column:email_id"`           // 对齐 email_messages.email_id
	SubjectTemplate string    `gorm:"type:text;column:subject_template"`    // 模板主题快照（原文）
	HTMLTemplate    string    `gorm:"type:mediumtext;column:html_template"` // 模板HTML快照（原文）
	TextTemplate    string    `gorm:"type:text;column:text_template"`       // 模板Text快照（原文）
	SubjectRendered string    `gorm:"type:text;column:subject_rendered"`    // 渲染后主题
	HTMLRendered    string    `gorm:"type:mediumtext;column:html_rendered"` // 渲染后HTML
	TextRendered    string    `gorm:"type:text;column:text_rendered"`       // 渲染后Text
	ContentChecksum *string   `gorm:"size:64;column:content_checksum"`      // 渲染结果校验和（SHA-256）
	SizeBytes       *int64    `gorm:"column:size_bytes"`                    // 内容大小字节数
	CreatedAt       time.Time `gorm:"type:datetime(3);not null;default:current_timestamp(3);column:created_at"`
}

// TableName 指定表名
func (EmailMessageContentModel) TableName() string { return "email_message_contents" }
