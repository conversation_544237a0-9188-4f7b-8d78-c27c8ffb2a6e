package repository

import (
	"context"
	"fmt"

	emailentity "gitee.com/heiyee/platforms/email/internal/domain/email/entity"
	emailrepo "gitee.com/heiyee/platforms/email/internal/domain/email/repository"
	"gitee.com/heiyee/platforms/email/internal/infrastructure/persistence/model"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// Ensure implementation satisfies interface
var _ emailrepo.ContentRepository = (*EmailMessageContentRepositoryImpl)(nil)

// EmailMessageContentRepositoryImpl 邮件内容快照仓储实现
type EmailMessageContentRepositoryImpl struct {
	db *gorm.DB
}

func NewEmailMessageContentRepositoryImpl(db *gorm.DB) *EmailMessageContentRepositoryImpl {
	return &EmailMessageContentRepositoryImpl{db: db}
}

// Save 保存或更新内容快照（按 email_id 主键幂等）
func (r *EmailMessageContentRepositoryImpl) Save(ctx context.Context, content *emailentity.EmailMessageContent) error {
	m := &model.EmailMessageContentModel{
		EmailID:         content.EmailID,
		SubjectTemplate: content.SubjectTemplate,
		HTMLTemplate:    content.HTMLTemplate,
		TextTemplate:    content.TextTemplate,
		SubjectRendered: content.SubjectRendered,
		HTMLRendered:    content.HTMLRendered,
		TextRendered:    content.TextRendered,
		ContentChecksum: content.ContentChecksum,
		SizeBytes:       content.SizeBytes,
		CreatedAt:       content.CreatedAt,
	}

	// Upsert by primary key email_id
	if err := r.db.WithContext(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "email_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"subject_template", "html_template", "text_template", "subject_rendered", "html_rendered", "text_rendered", "content_checksum", "size_bytes"}),
	}).Create(m).Error; err != nil {
		return fmt.Errorf("failed to save email message content: %w", err)
	}
	return nil
}

// FindByEmailID 根据邮件ID获取内容快照
func (r *EmailMessageContentRepositoryImpl) FindByEmailID(ctx context.Context, emailID int64) (*emailentity.EmailMessageContent, error) {
	var m model.EmailMessageContentModel
	if err := r.db.WithContext(ctx).Where("email_id = ?", emailID).First(&m).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to find email message content: %w", err)
	}

	return &emailentity.EmailMessageContent{
		EmailID:         m.EmailID,
		SubjectTemplate: m.SubjectTemplate,
		HTMLTemplate:    m.HTMLTemplate,
		TextTemplate:    m.TextTemplate,
		SubjectRendered: m.SubjectRendered,
		HTMLRendered:    m.HTMLRendered,
		TextRendered:    m.TextRendered,
		ContentChecksum: m.ContentChecksum,
		SizeBytes:       m.SizeBytes,
		CreatedAt:       m.CreatedAt,
	}, nil
}
