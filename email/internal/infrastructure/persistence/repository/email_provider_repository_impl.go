package repository

import (
	"context"

	"gorm.io/gorm"

	"gitee.com/heiyee/platforms/email/internal/infrastructure/persistence/model"
)

// EmailProviderRepositoryImpl 邮箱服务商仓储实现
type EmailProviderRepositoryImpl struct {
	db *gorm.DB
}

func NewEmailProviderRepositoryImpl(db *gorm.DB) *EmailProviderRepositoryImpl {
	return &EmailProviderRepositoryImpl{db: db}
}

func (r *EmailProviderRepositoryImpl) FindAll(ctx context.Context) ([]*model.EmailProviderModel, error) {
	var providers []*model.EmailProviderModel
	if err := r.db.WithContext(ctx).Where("enabled = ?", true).Order("sort_order asc, display_name asc").Find(&providers).Error; err != nil {
		return nil, err
	}
	return providers, nil
}

func (r *EmailProviderRepositoryImpl) FindByID(ctx context.Context, id int64) (*model.EmailProviderModel, error) {
	var provider model.EmailProviderModel
	if err := r.db.WithContext(ctx).First(&provider, id).Error; err != nil {
		return nil, err
	}
	return &provider, nil
}

func (r *EmailProviderRepositoryImpl) FindByName(ctx context.Context, name string) (*model.EmailProviderModel, error) {
	var provider model.EmailProviderModel
	if err := r.db.WithContext(ctx).Where("name = ?", name).First(&provider).Error; err != nil {
		return nil, err
	}
	return &provider, nil
}
