package external

import (
	"context"
	"fmt"
	"hash/crc32"
	"strings"
	"time"

	"gitee.com/heiyee/platforms/email/internal/application/template/dto"
	"gitee.com/heiyee/platforms/email/internal/application/template/service"
	"gitee.com/heiyee/platforms/email/internal/domain/email/entity"
	emailrepo "gitee.com/heiyee/platforms/email/internal/domain/email/repository"
	templateRepo "gitee.com/heiyee/platforms/email/internal/domain/template/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// EmailSendParams 邮件发送参数
type EmailSendParams struct {
	Account             *entity.EmailAccount
	Message             *entity.EmailMessage
	RenderedSubject     string
	RenderedHTMLContent string
	RenderedTextContent string
	TenantID            int64
	InternalAppID       int64

	// 工厂已处理好的收件人与头部（下游不再做解析处理）
	RcptList []string
	ToHeader string
	CcHeader string
}

// EmailSenderFactory 邮件发送工厂
type EmailSenderFactory struct {
	logger            logiface.Logger
	templateRepo      templateRepo.Repository
	contentRepo       emailrepo.ContentRepository
	emailRepo         emailrepo.Repository
	templateRenderSvc *service.TemplateRenderApplicationService
}

// NewEmailSenderFactory 创建邮件发送工厂
func NewEmailSenderFactory(logger logiface.Logger, templateRepo templateRepo.Repository) *EmailSenderFactory {
	return &EmailSenderFactory{
		logger:       logger,
		templateRepo: templateRepo,
	}
}

// WithContentRepository 可选注入内容快照仓储
func (f *EmailSenderFactory) WithContentRepository(repo emailrepo.ContentRepository) *EmailSenderFactory {
	f.contentRepo = repo
	return f
}

// WithEmailRepository 可选注入邮件仓储（用于写入模板版本等）
func (f *EmailSenderFactory) WithEmailRepository(repo emailrepo.Repository) *EmailSenderFactory {
	f.emailRepo = repo
	return f
}

// WithTemplateRenderService 注入模板渲染应用服务
func (f *EmailSenderFactory) WithTemplateRenderService(svc *service.TemplateRenderApplicationService) *EmailSenderFactory {
	f.templateRenderSvc = svc
	return f
}

// SendEmail 根据账户类型选择对应的发送协议，并完成模板格式化
func (f *EmailSenderFactory) SendEmail(ctx context.Context, account *entity.EmailAccount, message *entity.EmailMessage) error {
	f.logger.Info(ctx, "Sending email via protocol-based sender",
		logiface.String("account_id", fmt.Sprintf("%d", account.ID)),
		logiface.String("protocol", f.getProtocolName(account.Type)),
		logiface.String("to", message.ToAddresses),
		logiface.Int64("internal_app_id", message.InternalAppID))

	// 如果有模板ID，先渲染模板内容
	var renderedSubject, renderedHTMLContent, renderedTextContent string

	if message.TemplateID != nil {
		// 使用统一的模板渲染服务
		renderResult, err := f.renderTemplateForSending(ctx, message, account.TenantID)
		if err != nil {
			return fmt.Errorf("模板渲染失败: %w", err)
		}

		renderedSubject = renderResult.Subject
		renderedHTMLContent = renderResult.HTMLContent
		renderedTextContent = renderResult.TextContent

		f.logger.Info(ctx, "Template rendered successfully",
			logiface.Int64("template_id", *message.TemplateID),
			logiface.Int64("template_version", *renderResult.TemplateVersion),
			logiface.String("subject", renderedSubject))

		// 使用事务保存邮件消息和内容快照
		if f.emailRepo != nil && f.contentRepo != nil {
			checksum := computeCRC(renderedSubject + renderedHTMLContent + renderedTextContent)
			content := &entity.EmailMessageContent{
				EmailID:         message.EmailID,
				SubjectTemplate: renderResult.OriginalTemplate.Subject,
				HTMLTemplate:    renderResult.OriginalTemplate.HTMLContent,
				TextTemplate:    renderResult.OriginalTemplate.PlainTextContent,
				SubjectRendered: renderedSubject,
				HTMLRendered:    renderedHTMLContent,
				TextRendered:    renderedTextContent,
				ContentChecksum: &checksum,
				CreatedAt:       time.Now(),
			}

			// 事务保存邮件消息和内容快照
			if err := f.emailRepo.SaveWithContent(ctx, message, content); err != nil {
				f.logger.Error(ctx, "Failed to save email message and content", logiface.Error(err), logiface.Int64("email_id", message.EmailID))
				return fmt.Errorf("保存邮件消息及内容失败: %w", err)
			}
		} else if f.emailRepo != nil {
			// 只保存邮件消息
			if err := f.emailRepo.Save(ctx, message); err != nil {
				f.logger.Error(ctx, "Failed to save email message", logiface.Error(err), logiface.Int64("email_id", message.EmailID))
				return fmt.Errorf("保存邮件消息失败: %w", err)
			}
		}
	}

	// 收件人与头部由工厂统一处理
	toList := f.splitAddresses(message.ToAddresses)
	ccList := []string{}
	if message.CcAddresses != nil && *message.CcAddresses != "" {
		ccList = f.splitAddresses(*message.CcAddresses)
	}
	bccList := []string{}
	if message.BccAddresses != nil && *message.BccAddresses != "" {
		bccList = f.splitAddresses(*message.BccAddresses)
	}
	rcptList := append(append([]string{}, toList...), append(ccList, bccList...)...)
	toHeader := strings.Join(toList, ", ")
	ccHeader := ""
	if len(ccList) > 0 {
		ccHeader = strings.Join(ccList, ", ")
	}

	// 构建发送参数
	sendParams := &EmailSendParams{
		Account:             account,
		Message:             message,
		RenderedSubject:     renderedSubject,
		RenderedHTMLContent: renderedHTMLContent,
		RenderedTextContent: renderedTextContent,
		TenantID:            account.TenantID,
		InternalAppID:       message.InternalAppID,
		RcptList:            rcptList,
		ToHeader:            toHeader,
		CcHeader:            ccHeader,
	}

	// 根据账户类型选择对应的发送协议
	switch account.Type {
	case entity.AccountTypeSMTP:
		return f.sendViaSMTP(ctx, sendParams)
	case entity.AccountTypeAPI:
		return f.sendViaAPI(ctx, sendParams)
	case entity.AccountTypeIMAP:
		return f.sendViaIMAP(ctx, sendParams)
	case entity.AccountTypePOP3:
		return f.sendViaPOP3(ctx, sendParams)
	case entity.AccountTypeExchange:
		return f.sendViaExchange(ctx, sendParams)
	default:
		return fmt.Errorf("不支持的邮件协议类型: %d", account.Type)
	}
}

// computeCRC 计算简单校验和（如需更强校验可切换 SHA-256）
func computeCRC(s string) string {
	if s == "" {
		return ""
	}
	v := crc32.ChecksumIEEE([]byte(s))
	return fmt.Sprintf("%08x", v)
}

// getProtocolName 获取协议名称
func (f *EmailSenderFactory) getProtocolName(accountType entity.AccountType) string {
	switch accountType {
	case entity.AccountTypeSMTP:
		return "SMTP"
	case entity.AccountTypeAPI:
		return "API"
	case entity.AccountTypeIMAP:
		return "IMAP"
	case entity.AccountTypePOP3:
		return "POP3"
	case entity.AccountTypeExchange:
		return "Exchange"
	default:
		return "Unknown"
	}
}

// renderTemplateForSending 使用统一的模板渲染服务渲染模板
func (f *EmailSenderFactory) renderTemplateForSending(ctx context.Context, message *entity.EmailMessage, tenantID int64) (*dto.RenderTemplateResponse, error) {
	if message.TemplateID == nil {
		return nil, fmt.Errorf("模板ID为空")
	}

	// 使用统一的模板渲染服务
	req := &dto.RenderTemplateRequest{
		TemplateID:   *message.TemplateID,
		TenantID:     tenantID,
		Variables:    message.Variables,
		EmailMessage: message,
	}

	result, err := f.templateRenderSvc.RenderTemplateForSending(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("模板渲染失败: %w", err)
	}

	// 设置模板版本到消息中
	message.TemplateVersion = result.TemplateVersion

	return result, nil
}

// splitAddresses 工具：按逗号分割并去空格
func (f *EmailSenderFactory) splitAddresses(list string) []string {
	parts := strings.Split(list, ",")
	out := make([]string, 0, len(parts))
	for _, p := range parts {
		t := strings.TrimSpace(p)
		if t != "" {
			out = append(out, t)
		}
	}
	return out
}

// ValidateAccountCredentials 验证邮件账户凭据是否正确
func (f *EmailSenderFactory) ValidateAccountCredentials(ctx context.Context, account *entity.EmailAccount) error {
	f.logger.Info(ctx, "Validating account credentials",
		logiface.String("account_id", fmt.Sprintf("%d", account.ID)),
		logiface.String("protocol", f.getProtocolName(account.Type)),
		logiface.String("provider", account.Provider))

	// 根据账户类型选择对应的验证方法
	switch account.Type {
	case entity.AccountTypeSMTP:
		service := NewSMTPService(f.logger)
		return service.ValidateSMTPCredentials(ctx, account)
	case entity.AccountTypeAPI:
		// API类型暂时不做验证，因为需要不同的验证逻辑
		f.logger.Info(ctx, "API account validation skipped",
			logiface.String("provider", account.Provider))
		return nil
	case entity.AccountTypeIMAP:
		return fmt.Errorf("IMAP协议不支持发送邮件，无法验证")
	case entity.AccountTypePOP3:
		return fmt.Errorf("POP3协议不支持发送邮件，无法验证")
	case entity.AccountTypeExchange:
		return fmt.Errorf("Exchange协议暂不支持验证")
	default:
		return fmt.Errorf("不支持的邮件协议类型: %d", account.Type)
	}
}

// sendViaSMTP 通过SMTP协议发送邮件
func (f *EmailSenderFactory) sendViaSMTP(ctx context.Context, params *EmailSendParams) error {
	f.logger.Info(ctx, "Sending email via SMTP protocol",
		logiface.String("account_id", fmt.Sprintf("%d", params.Account.ID)),
		logiface.String("host", params.Account.Host),
		logiface.Int("port", params.Account.Port))

	service := NewSMTPService(f.logger)
	return service.SendEmail(ctx, params)
}

// sendViaAPI 通过API协议发送邮件
func (f *EmailSenderFactory) sendViaAPI(ctx context.Context, params *EmailSendParams) error {
	f.logger.Info(ctx, "API protocol currently unsupported",
		logiface.String("account_id", fmt.Sprintf("%d", params.Account.ID)),
		logiface.String("provider", params.Account.Provider))

	// API协议发送需要根据具体提供商定制实现
	// 这里只保留阿里云作为示例，其他提供商需要根据其API规范定制
	switch params.Account.Provider {
	case "Aliyun":
		return f.sendViaAliyunAPI(ctx, params)
	default:
		return fmt.Errorf("API协议发送需要定制实现，当前不支持提供商: %s", params.Account.Provider)
	}
}

// sendViaAliyunAPI 通过阿里云API发送邮件
func (f *EmailSenderFactory) sendViaAliyunAPI(ctx context.Context, params *EmailSendParams) error {
	f.logger.Info(ctx, "Sending email via Aliyun API",
		logiface.String("account_id", fmt.Sprintf("%d", params.Account.ID)),
		logiface.String("to", params.Message.ToAddresses))

	// 从account.Config中提取阿里云配置
	configMap := params.Account.Config.ToMap()
	if configMap == nil {
		return fmt.Errorf("阿里云账户配置为空")
	}

	// 创建阿里云DM配置
	aliyunConfig := &AliyunDMConfig{}
	if accessKeyID, ok := configMap["access_key_id"].(string); ok {
		aliyunConfig.AccessKeyID = accessKeyID
	} else {
		return fmt.Errorf("缺少access_key_id配置")
	}
	if accessKeySecret, ok := configMap["access_key_secret"].(string); ok {
		aliyunConfig.AccessKeySecret = accessKeySecret
	} else {
		return fmt.Errorf("缺少access_key_secret配置")
	}
	if region, ok := configMap["region"].(string); ok {
		aliyunConfig.Region = region
	} else {
		aliyunConfig.Region = "cn-hangzhou" // 默认区域
	}
	if domain, ok := configMap["domain"].(string); ok {
		aliyunConfig.Domain = domain
	}

	// 创建阿里云DM服务实例
	aliyunService, err := NewAliyunDMService(aliyunConfig, f.logger)
	if err != nil {
		return fmt.Errorf("创建阿里云DM服务失败: %w", err)
	}

	return aliyunService.SendEmail(ctx, params.Account, params.Message, params.RenderedSubject, params.RenderedHTMLContent, params.RenderedTextContent)
}

// sendViaIMAP 通过IMAP协议发送邮件
func (f *EmailSenderFactory) sendViaIMAP(ctx context.Context, params *EmailSendParams) error {
	f.logger.Info(ctx, "IMAP protocol not supported for sending",
		logiface.String("account_id", fmt.Sprintf("%d", params.Account.ID)))
	return fmt.Errorf("IMAP协议不支持发送邮件")
}

// sendViaPOP3 通过POP3协议发送邮件
func (f *EmailSenderFactory) sendViaPOP3(ctx context.Context, params *EmailSendParams) error {
	f.logger.Info(ctx, "POP3 protocol not supported for sending",
		logiface.String("account_id", fmt.Sprintf("%d", params.Account.ID)))
	return fmt.Errorf("POP3协议不支持发送邮件")
}

// sendViaExchange 通过Exchange协议发送邮件
func (f *EmailSenderFactory) sendViaExchange(ctx context.Context, params *EmailSendParams) error {
	f.logger.Info(ctx, "Exchange protocol currently unsupported",
		logiface.String("account_id", fmt.Sprintf("%d", params.Account.ID)),
		logiface.String("host", params.Account.Host))
	return fmt.Errorf("Exchange协议暂不支持")
}
