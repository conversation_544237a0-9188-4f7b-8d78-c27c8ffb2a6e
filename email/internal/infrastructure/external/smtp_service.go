package external

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/smtp"
	"strings"
	"time"

	"gitee.com/heiyee/platforms/email/internal/domain/email/entity"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// SMTPService SMTP发送服务
type SMTPService struct {
	logger logiface.Logger
}

// NewSMTPService 创建SMTP发送服务
func NewSMTPService(logger logiface.Logger) *SMTPService {
	return &SMTPService{
		logger: logger,
	}
}

// SendEmail 发送邮件（使用对象参数）
func (s *SMTPService) SendEmail(ctx context.Context, params *EmailSendParams) error {
	account := params.Account

	// 构建邮件内容
	emailContent := s.buildEmailContent(account, params)

	// 连接SMTP服务器
	client, err := s.connectSMTP(account)
	if err != nil {
		s.logger.Error(ctx, "Failed to connect to SMTP server",
			logiface.Error(err),
			logiface.String("host", account.Host),
			logiface.Int("port", account.Port))
		return fmt.Errorf("连接SMTP服务器失败: %w", err)
	}
	defer client.Close()

	// 发送邮件
	if err := client.Mail(account.FromAddress); err != nil {
		client.Close()
		return fmt.Errorf("设置发件人失败: %w", err)
	}

	// 逐个添加收件人（To, Cc, Bcc）已由工厂处理
	for _, addr := range params.RcptList {
		if err := client.Rcpt(addr); err != nil {
			client.Close()
			return fmt.Errorf("设置收件人失败(%s): %w", addr, err)
		}
	}

	w, err := client.Data()
	if err != nil {
		client.Close()
		return fmt.Errorf("开始发送邮件内容失败: %w", err)
	}
	defer w.Close()

	if _, err := w.Write(emailContent); err != nil {
		return fmt.Errorf("写入邮件内容失败: %w", err)
	}

	s.logger.Info(ctx, "Email sent successfully",
		logiface.String("to", params.ToHeader),
		logiface.String("account", account.Name))

	return nil
}

// connectSMTP 连接SMTP服务器
func (s *SMTPService) connectSMTP(account *entity.EmailAccount) (*smtp.Client, error) {
	addr := fmt.Sprintf("%s:%d", account.Host, account.Port)

	// 创建认证
	auth := smtp.PlainAuth("", account.Username, account.Password, account.Host)

	// 连接服务器
	client, err := smtp.Dial(addr)
	if err != nil {
		return nil, fmt.Errorf("连接SMTP服务器失败: %w", err)
	}

	// 启动TLS（如果需要）
	if account.IsSSL {
		if ok, _ := client.Extension("STARTTLS"); ok {
			tlsConfig := &tls.Config{
				ServerName:         account.Host,
				InsecureSkipVerify: false,
			}
			if err := client.StartTLS(tlsConfig); err != nil {
				client.Close()
				return nil, fmt.Errorf("启动TLS失败: %w", err)
			}
		}
	}

	// 身份验证
	if err := client.Auth(auth); err != nil {
		client.Close()
		return nil, fmt.Errorf("SMTP身份验证失败: %w", err)
	}

	return client, nil
}

// ValidateSMTPCredentials 验证SMTP凭据是否正确
func (s *SMTPService) ValidateSMTPCredentials(ctx context.Context, account *entity.EmailAccount) error {
	s.logger.Info(ctx, "Validating SMTP credentials",
		logiface.String("host", account.Host),
		logiface.Int("port", account.Port),
		logiface.String("username", account.Username))

	// 连接并验证SMTP服务器
	client, err := s.connectSMTP(account)
	if err != nil {
		s.logger.Error(ctx, "SMTP validation failed",
			logiface.Error(err),
			logiface.String("host", account.Host),
			logiface.Int("port", account.Port))
		return fmt.Errorf("SMTP凭据验证失败: %w", err)
	}
	defer client.Close()

	s.logger.Info(ctx, "SMTP credentials validated successfully",
		logiface.String("host", account.Host),
		logiface.Int("port", account.Port))

	return nil
}
func (s *SMTPService) buildEmailContent(account *entity.EmailAccount, params *EmailSendParams) []byte {
	var content strings.Builder

	// 添加邮件头（To/Cc 已由工厂格式化）
	content.WriteString(fmt.Sprintf("From: %s <%s>\r\n", account.FromName, account.FromAddress))
	content.WriteString(fmt.Sprintf("To: %s\r\n", params.ToHeader))
	if params.CcHeader != "" {
		content.WriteString(fmt.Sprintf("Cc: %s\r\n", params.CcHeader))
	}
	// 主题
	subject := params.RenderedSubject
	if subject == "" {
		subject = "(no subject)"
	}
	content.WriteString(fmt.Sprintf("Subject: %s\r\n", subject))
	content.WriteString(fmt.Sprintf("Date: %s\r\n", time.Now().Format("Mon, 02 Jan 2006 15:04:05 -0700")))
	content.WriteString("MIME-Version: 1.0\r\n")

	// 如果有回复地址
	if account.ReplyToAddress != "" {
		content.WriteString(fmt.Sprintf("Reply-To: %s\r\n", account.ReplyToAddress))
	}

	// 内容类型与主体（内容已在工厂渲染完成）
	if params.RenderedHTMLContent != "" {
		content.WriteString("Content-Type: text/html; charset=UTF-8\r\n")
		content.WriteString("\r\n")
		content.WriteString(params.RenderedHTMLContent)
	} else {
		content.WriteString("Content-Type: text/plain; charset=UTF-8\r\n")
		content.WriteString("\r\n")
		if params.RenderedTextContent != "" {
			content.WriteString(params.RenderedTextContent)
		} else {
			content.WriteString("(empty)")
		}
	}

	return []byte(content.String())
}
