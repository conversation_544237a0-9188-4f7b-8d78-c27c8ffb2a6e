package worker

import (
	"context"
	"time"

	emailRepo "gitee.com/heiyee/platforms/email/internal/domain/email/repository"
	templateRepo "gitee.com/heiyee/platforms/email/internal/domain/template/repository"
	"gitee.com/heiyee/platforms/email/internal/infrastructure/external"
	"gitee.com/heiyee/platforms/pkg/logiface"
)

// SenderWorker 基于数据库队列表的发送worker（无MQ依赖）
// 通过定时轮询 + 行级锁 领取任务并发送
// 注意：为简化实现，单实例串行处理；横向扩展可多实例部署，依赖DB锁避免重复
type SenderWorker struct {
	logger             logiface.Logger
	emailRepo          emailRepo.Repository
	emailAccountRepo   emailRepo.EmailAccountRepository
	templateRepo       templateRepo.Repository
	emailSenderFactory *external.EmailSenderFactory
	interval           time.Duration
	batchSize          int
	baseBackoff        time.Duration
	maxBackoff         time.Duration
	maxRetryWindow     time.Duration
	processingTimeout  time.Duration
	stopped            chan struct{}
}

func NewSenderWorker(
	logger logiface.Logger,
	emailRepo emailRepo.Repository,
	emailAccountRepo emailRepo.EmailAccountRepository,
	templateRepo templateRepo.Repository,
	emailSenderFactory *external.EmailSenderFactory,
	interval time.Duration,
	batchSize int,
	baseBackoff time.Duration,
	maxBackoff time.Duration,
	maxRetryWindow time.Duration,
	processingTimeout time.Duration,
) *SenderWorker {
	return &SenderWorker{
		logger:             logger,
		emailRepo:          emailRepo,
		emailAccountRepo:   emailAccountRepo,
		templateRepo:       templateRepo,
		emailSenderFactory: emailSenderFactory,
		interval:           interval,
		batchSize:          batchSize,
		baseBackoff:        baseBackoff,
		maxBackoff:         maxBackoff,
		maxRetryWindow:     maxRetryWindow,
		processingTimeout:  processingTimeout,
		stopped:            make(chan struct{}),
	}
}

func (w *SenderWorker) Start(ctx context.Context) {
	go w.loop(ctx)
}

func (w *SenderWorker) Stop() {
	close(w.stopped)
}

func (w *SenderWorker) loop(ctx context.Context) {
	ticker := time.NewTicker(w.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-w.stopped:
			return
		case <-ticker.C:
			w.processOnce(ctx)
		}
	}
}

func (w *SenderWorker) processOnce(ctx context.Context) {
	// 回收超时 processing 任务
	w.recoverStuckProcessing(ctx)

	msgs, err := w.emailRepo.ClaimPendingBatch(ctx, w.batchSize, w.baseBackoff, w.maxRetryWindow)
	if err != nil {
		w.logger.Error(ctx, "Claim pending emails failed", logiface.Error(err))
		return
	}
	if len(msgs) == 0 {
		return
	}

	for _, m := range msgs {
		// 优先使用消息中冗余的 AccountID；若无则回退模板
		accountID := m.AccountID
		if accountID == 0 && m.TemplateID != nil {
			if t, err := w.templateRepo.Get(ctx, m.TenantID, *m.TemplateID); err == nil {
				accountID = t.AccountID
			}
		}

		account, accErr := w.emailAccountRepo.FindByID(ctx, accountID)
		if accErr != nil {
			w.logger.Error(ctx, "Find account failed", logiface.Error(accErr))
			_ = w.emailRepo.UpdateSendResult(ctx, m.EmailID, false, accErr.Error())
			continue
		}

		if sendErr := w.emailSenderFactory.SendEmail(ctx, account, m); sendErr != nil {
			w.logger.Error(ctx, "Send email failed", logiface.Error(sendErr))
			_ = w.emailRepo.UpdateSendResult(ctx, m.EmailID, false, sendErr.Error())
			continue
		}

		_ = w.emailRepo.UpdateSendResult(ctx, m.EmailID, true, "")
	}
}

// recoverStuckProcessing 将长时间 processing 未更新的任务回滚为 pending
func (w *SenderWorker) recoverStuckProcessing(ctx context.Context) {
	_ = w.emailRepo.RecoverStuckProcessing(ctx, w.processingTimeout)
}
