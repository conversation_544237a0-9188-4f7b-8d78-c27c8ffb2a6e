package container

import (
	"context"

	"gitee.com/heiyee/platforms/email/internal/application/email/service"
	emailService "gitee.com/heiyee/platforms/email/internal/application/service"
	templateService "gitee.com/heiyee/platforms/email/internal/application/template/service"
	tenantService "gitee.com/heiyee/platforms/email/internal/application/tenant/service"
	"gitee.com/heiyee/platforms/email/internal/domain/email/entity"
	domainEvent "gitee.com/heiyee/platforms/email/internal/domain/email/event"
	domainTemplateService "gitee.com/heiyee/platforms/email/internal/domain/template/service"
	trackingService "gitee.com/heiyee/platforms/email/internal/domain/tracking/service"
	"gitee.com/heiyee/platforms/email/internal/infrastructure/config"
	"gitee.com/heiyee/platforms/email/internal/infrastructure/external"
	"gitee.com/heiyee/platforms/email/internal/infrastructure/id_generator"
	persistenceRepo "gitee.com/heiyee/platforms/email/internal/infrastructure/persistence/repository"
	workerpkg "gitee.com/heiyee/platforms/email/internal/infrastructure/worker"
	"gitee.com/heiyee/platforms/email/internal/interfaces/grpc"
	"gitee.com/heiyee/platforms/email/internal/interfaces/http/handlers"

	commonDB "gitee.com/heiyee/platforms/pkg/db"
	"gitee.com/heiyee/platforms/pkg/logiface"

	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Infrastructure 基础设施层
type Infrastructure struct {
	DB          *gorm.DB
	Logger      logiface.Logger
	AppConfig   *config.Config
	UserClient  *external.UserServiceClient
	IDGenClient *external.IDGeneratorClient
}

// Applications 应用层
type Applications struct {
	EmailService        *service.EmailApplicationService
	EmailAccountService *service.EmailAccountApplicationService
	TemplateService     *templateService.TemplateApplicationService
	TenantService       *tenantService.TenantApplicationService
	EmailGrpcService    *emailService.EmailService
}

// Interfaces 接口层
type Interfaces struct {
	EmailHandler        *handlers.EmailHandler
	EmailAccountHandler *handlers.EmailAccountHandler
	TemplateHandler     *handlers.TemplateHandler
	TenantHandler       *handlers.TenantHandler
	EmailGrpcServer     *grpc.EmailServiceServer
}

// Domain 领域层
type Domain struct {
	IDGenerator    *id_generator.EmailIDGenerator
	EntityFactory  *entity.EntityFactory
	EventPublisher domainEvent.Publisher
}

// DependencyContainer 依赖容器
type DependencyContainer struct {
	Infrastructure Infrastructure
	Applications   Applications
	Interfaces     Interfaces
	Domain         Domain
	Workers        struct {
		Sender *workerpkg.SenderWorker
	}
}

// NewDependencyContainer 创建依赖容器
func NewDependencyContainer(appConfig *config.Config, logger logiface.Logger) *DependencyContainer {
	return &DependencyContainer{
		Infrastructure: Infrastructure{
			DB:          nil, // 延迟初始化
			Logger:      logger,
			AppConfig:   appConfig,
			UserClient:  nil, // 延迟初始化
			IDGenClient: nil, // 延迟初始化
		},
		Applications: Applications{
			EmailService:        nil, // 延迟初始化
			EmailAccountService: nil, // 延迟初始化
			TemplateService:     nil, // 延迟初始化
			TenantService:       nil, // 延迟初始化
			EmailGrpcService:    nil, // 延迟初始化
		},
		Interfaces: Interfaces{
			EmailHandler:        nil, // 延迟初始化
			EmailAccountHandler: nil, // 延迟初始化
			TemplateHandler:     nil, // 延迟初始化
			TenantHandler:       nil, // 延迟初始化
			EmailGrpcServer:     nil, // 延迟初始化
		},
		Domain: Domain{
			IDGenerator:    nil, // 延迟初始化
			EntityFactory:  nil, // 延迟初始化
			EventPublisher: nil,
		},
	}
}

// Initialize 初始化所有依赖
func (c *DependencyContainer) Initialize(ctx context.Context) error {
	// 1. 初始化数据库
	if c.Infrastructure.DB == nil {
		gormLogger := commonDB.NewGormLoggerAdapter(c.Infrastructure.Logger, logger.Info) // info
		c.Infrastructure.DB = commonDB.NewDB(&c.Infrastructure.AppConfig.Database.MySQL, gormLogger)
	}
	// 2. 初始化gRPC客户端
	if c.Infrastructure.UserClient == nil {
		c.Infrastructure.UserClient = external.NewUserServiceClient(c.Infrastructure.Logger)
	}
	if c.Infrastructure.IDGenClient == nil {
		c.Infrastructure.IDGenClient = external.NewIDGeneratorClient(c.Infrastructure.Logger)
	}

	// 3. 初始化领域层
	if c.Domain.IDGenerator == nil {
		c.Domain.IDGenerator = id_generator.NewEmailIDGenerator(c.Infrastructure.IDGenClient, c.Infrastructure.Logger)
	}
	if c.Domain.EntityFactory == nil {
		c.Domain.EntityFactory = entity.NewEntityFactory(c.Domain.IDGenerator)
	}
	if c.Domain.EventPublisher == nil {
		// 默认注入 NoopPublisher，可在上层替换为带有消息队列/OTel 的实现
		c.Domain.EventPublisher = domainEvent.NoopPublisher{}
	}

	// 4. 初始化仓储
	db := c.Infrastructure.DB
	logger := c.Infrastructure.Logger
	emailRepo := persistenceRepo.NewEmailRepositoryImpl(db)
	emailAccountRepo := persistenceRepo.NewEmailAccountRepositoryImpl(db)
	emailProviderRepo := persistenceRepo.NewEmailProviderRepositoryImpl(db)
	templateRepo := persistenceRepo.NewTemplateRepository(db)
	tenantRepo := persistenceRepo.NewTenantRepository(db, logger)

	// 5. 初始化工厂
	emailContentRepo := persistenceRepo.NewEmailMessageContentRepositoryImpl(db)

	// 初始化追踪相关服务
	// TODO: 实现实际的SecretManager和TimeProvider
	trackingParamGen := trackingService.NewTrackingParameterGenerator(nil, nil)

	// 统一模板渲染器与应用服务
	tmplRenderer := domainTemplateService.NewTemplateRenderer(logger, trackingParamGen)
	tmplRenderAppSvc := templateService.NewTemplateRenderApplicationService(templateRepo, tmplRenderer, logger)

	// 注入统一模板渲染服务与仓储
	emailSenderFactory := external.NewEmailSenderFactory(logger, templateRepo).
		WithContentRepository(emailContentRepo).
		WithEmailRepository(emailRepo).
		WithTemplateRenderService(tmplRenderAppSvc)

	// 6. 初始化应用服务
	c.Applications.EmailService = service.NewEmailApplicationService(
		emailRepo,
		templateRepo,
		emailAccountRepo,
		emailSenderFactory,
		tmplRenderAppSvc,
		c.Infrastructure.UserClient,
		c.Domain.EntityFactory,
		logger,
	).WithEventPublisher(c.Domain.EventPublisher)
	c.Applications.EmailAccountService = service.NewEmailAccountApplicationService(
		emailAccountRepo,
		templateRepo,
		emailSenderFactory,
		tmplRenderAppSvc,
		c.Domain.EntityFactory,
		logger,
	).WithProviderRepository(emailProviderRepo)
	c.Applications.TemplateService = templateService.NewTemplateApplicationService(templateRepo, logger)
	c.Applications.TenantService = tenantService.NewTenantApplicationService(tenantRepo, logger)

	// 6.1 初始化 gRPC wrapper service
	c.Applications.EmailGrpcService = emailService.NewEmailService(c.Applications.EmailService)

	// 7. 初始化 handler
	c.Interfaces.EmailHandler = handlers.NewEmailHandler(c.Applications.EmailService)
	c.Interfaces.EmailAccountHandler = handlers.NewEmailAccountHandler(c.Applications.EmailAccountService, logger)
	c.Interfaces.TemplateHandler = handlers.NewTemplateHandler(c.Applications.TemplateService, logger)
	c.Interfaces.TenantHandler = handlers.NewTenantHandler(c.Applications.TenantService, logger)

	// 7.1 初始化 gRPC server
	zapLogger, _ := zap.NewProduction() // Convert logiface.Logger to zap.Logger for gRPC server
	c.Interfaces.EmailGrpcServer = grpc.NewEmailServiceServer(c.Applications.EmailGrpcService, zapLogger)

	// 8. 初始化 Workers（仅构造，不启动）
	c.initWorkers()
	return nil
}

// Close 关闭所有资源（如数据库连接、gRPC连接等）
func (c *DependencyContainer) Close() error {
	if c.Infrastructure.DB != nil {
		sqlDB, err := c.Infrastructure.DB.DB()
		if err == nil {
			sqlDB.Close()
		}
	}
	// 可扩展关闭gRPC客户端等
	return nil
}

// GetDB 获取数据库实例
func (c *DependencyContainer) GetDB() *gorm.DB {
	return c.Infrastructure.DB
}

// GetLogger 获取日志实例
func (c *DependencyContainer) GetLogger() logiface.Logger {
	return c.Infrastructure.Logger
}

// GetUserClient 获取用户服务gRPC客户端
func (c *DependencyContainer) GetUserClient() *external.UserServiceClient {
	return c.Infrastructure.UserClient
}

// GetIDGenClient 获取ID生成器gRPC客户端
func (c *DependencyContainer) GetIDGenClient() *external.IDGeneratorClient {
	return c.Infrastructure.IDGenClient
}

// GetEmailGrpcServer 获取Email gRPC服务器
func (c *DependencyContainer) GetEmailGrpcServer() *grpc.EmailServiceServer {
	return c.Interfaces.EmailGrpcServer
}

// initWorkers 构造所有基于容器依赖的后台worker（不启动）
func (c *DependencyContainer) initWorkers() {
	cfg := c.Infrastructure.AppConfig
	if cfg == nil {
		return
	}
	poll := parseDurationOr(cfg.Worker.PollInterval, 2*time.Second)
	batchSize := cfg.Worker.BatchSize
	if batchSize <= 0 {
		batchSize = 20
	}
	baseBackoff := parseDurationOr(cfg.Worker.BaseBackoff, 5*time.Second)
	maxBackoff := parseDurationOr(cfg.Worker.MaxBackoff, 2*time.Minute)
	maxRetryWindow := parseDurationOr(cfg.Worker.MaxRetryWindow, 10*time.Minute)
	processingTimeout := parseDurationOr(cfg.Worker.ProcessingTimeout, 2*time.Minute)

	c.Workers.Sender = workerpkg.NewSenderWorker(
		c.Infrastructure.Logger,
		c.Applications.EmailService.EmailRepository(),
		c.Applications.EmailService.EmailAccountRepository(),
		c.Applications.EmailService.TemplateRepository(),
		c.Applications.EmailService.EmailSenderFactory(),
		poll,
		batchSize,
		baseBackoff,
		maxBackoff,
		maxRetryWindow,
		processingTimeout,
	)
}

// StartWorkers 启动后台worker
func (c *DependencyContainer) StartWorkers(ctx context.Context) {
	if c.Workers.Sender != nil {
		c.Workers.Sender.Start(ctx)
		c.Infrastructure.Logger.Info(ctx, "DB-backed sender worker started")
	}
}

// StopWorkers 停止后台worker
func (c *DependencyContainer) StopWorkers() {
	if c.Workers.Sender != nil {
		c.Workers.Sender.Stop()
	}
}

func parseDurationOr(s string, d time.Duration) time.Duration {
	if s == "" {
		return d
	}
	if v, err := time.ParseDuration(s); err == nil {
		return v
	}
	return d
}
