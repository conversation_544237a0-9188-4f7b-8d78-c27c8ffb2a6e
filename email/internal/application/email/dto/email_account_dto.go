package dto

import (
	"time"
)

// CreateEmailAccountRequest 创建邮件账号请求
type CreateEmailAccountRequest struct {
	Name           string                 `json:"name" binding:"required,min=1,max=50"`
	Type           int                    `json:"type" binding:"required,min=1,max=5"`
	Provider       string                 `json:"provider" binding:"required,min=1,max=30"`
	Host           string                 `json:"host,omitempty" binding:"omitempty,hostname|ip"`
	Port           int                    `json:"port,omitempty" binding:"omitempty,min=1,max=65535"`
	Username       string                 `json:"username,omitempty" binding:"omitempty,min=1,max=100"`
	Password       string                 `json:"password,omitempty" binding:"omitempty,min=1,max=100"`
	FromAddress    string                 `json:"from_address" binding:"required,email,max=100"`
	FromName       string                 `json:"from_name,omitempty" binding:"omitempty,max=50"`
	ReplyToAddress string                 `json:"reply_to_address,omitempty" binding:"omitempty,email,max=100"`
	IsSSL          bool                   `json:"is_ssl"`
	IsActive       bool                   `json:"is_active"`
	DailyLimit     int                    `json:"daily_limit" binding:"omitempty,min=0,max=100000"`
	MonthlyLimit   int                    `json:"monthly_limit" binding:"omitempty,min=0,max=1000000"`
	Config         map[string]interface{} `json:"config,omitempty"`
	IsSystem       bool                   `json:"is_system"`
}

// GetEmailAccountRequest 获取邮件账号请求
type GetEmailAccountRequest struct {
	AccountID int64 `json:"account_id" binding:"required"`
}

// UpdateEmailAccountRequest 更新邮件账号请求
type UpdateEmailAccountRequest struct {
	AccountID      int64                  `json:"account_id" binding:"required"`
	Name           string                 `json:"name,omitempty"`
	Provider       string                 `json:"provider,omitempty"`
	Host           string                 `json:"host,omitempty"`
	Port           int                    `json:"port,omitempty"`
	Username       string                 `json:"username,omitempty"`
	Password       string                 `json:"password,omitempty"`
	FromAddress    string                 `json:"from_address,omitempty"`
	FromName       string                 `json:"from_name,omitempty"`
	ReplyToAddress string                 `json:"reply_to_address,omitempty"`
	IsSSL          *bool                  `json:"is_ssl,omitempty"`
	IsActive       *bool                  `json:"is_active,omitempty"`
	DailyLimit     *int                   `json:"daily_limit,omitempty"`
	MonthlyLimit   *int                   `json:"monthly_limit,omitempty"`
	Config         map[string]interface{} `json:"config,omitempty"`
	IsSystem       *bool                  `json:"is_system,omitempty"`
}

// Pagination 分页参数
// 声明式承载所有分页/游标/排序参数
// 可复用到所有分页接口
type Pagination struct {
	Page     int   `form:"page,default=1"`
	PageSize int   `form:"page_size,default=20,max=100"`
	Cursor   int64 `form:"cursor"`
}

func (p *Pagination) Limit() int {
	if p.PageSize > 100 {
		return 100
	}
	if p.PageSize <= 0 {
		return 20
	}
	return p.PageSize
}

func (p *Pagination) UseCursor() bool {
	return p.Cursor > 0
}

func (p *Pagination) Offset() int {
	if p.UseCursor() {
		return 0
	}
	page := p.Page
	if page <= 0 {
		page = 1
	}
	return (page - 1) * p.Limit()
}

// ListEmailAccountsRequest 获取邮件账号列表请求
// 组合Pagination参数
type ListEmailAccountsRequest struct {
	Pagination // 组合分页参数
}

// ListEmailAccountsResponse 获取邮件账号列表响应
type ListEmailAccountsResponse struct {
	Accounts   []*EmailAccountResponse `json:"accounts"`
	Total      int64                   `json:"total"`
	Page       int                     `json:"page"`
	PageSize   int                     `json:"page_size"`
	TotalPages int64                   `json:"total_pages"`
}

// EmailAccountResponse 邮件账号响应
type EmailAccountResponse struct {
	ID             int64                  `json:"id"`
	TenantID       int64                  `json:"tenant_id"` // 修改为int64类型
	Name           string                 `json:"name"`
	Type           int                    `json:"type"`
	Provider       string                 `json:"provider"`
	Host           string                 `json:"host,omitempty"`
	Port           int                    `json:"port,omitempty"`
	Username       string                 `json:"username,omitempty"`
	FromAddress    string                 `json:"from_address"`
	FromName       string                 `json:"from_name,omitempty"`
	ReplyToAddress string                 `json:"reply_to_address,omitempty"`
	IsSSL          bool                   `json:"is_ssl"`
	IsActive       bool                   `json:"is_active"`
	DailyLimit     int                    `json:"daily_limit"`
	MonthlyLimit   int                    `json:"monthly_limit"`
	SentToday      int                    `json:"sent_today"`
	SentThisMonth  int                    `json:"sent_this_month"`
	LastSentAt     *time.Time             `json:"last_sent_at,omitempty"`
	TestStatus     int                    `json:"test_status"`
	TestMessage    string                 `json:"test_message,omitempty"`
	Config         map[string]interface{} `json:"config,omitempty"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
	CreatedBy      string                 `json:"created_by,omitempty"`
	UpdatedBy      string                 `json:"updated_by,omitempty"`
	Version        int                    `json:"version"`
	IsSystem       bool                   `json:"is_system"`
}

// GetEmailAccountListRequest 获取邮件账号列表请求（已废弃，使用ListEmailAccountsRequest）
type GetEmailAccountListRequest struct {
	TenantID int64 `form:"tenant_id"` // 由服务端从JWT token设置，不允许客户端传递
	Offset   int   `form:"offset,default=0"`
	Limit    int   `form:"limit,default=20"`
}

// GetEmailAccountListResponse 获取邮件账号列表响应（已废弃，使用ListEmailAccountsResponse）
type GetEmailAccountListResponse struct {
	Accounts []*EmailAccountResponse `json:"accounts"`
	Total    int64                   `json:"total"`
	Offset   int                     `json:"offset"`
	Limit    int                     `json:"limit"`
}

// TestEmailAccountRequest 测试邮件账号请求
type TestEmailAccountRequest struct {
	AccountID int64  `json:"account_id"`
	ID        int64  `json:"id"`          // 支持id字段作为account_id的别名
	TestEmail string `json:"test_email" binding:"required,email"`
}

// TestEmailAccountResponse 测试邮件账号响应
type TestEmailAccountResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// AccountTypeItem 账户类型项（用于HTTP出参，snake_case）
type AccountTypeItem struct {
	Type        string `json:"type"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

// TestStatusItem 测试状态项（用于HTTP出参，snake_case）
type TestStatusItem struct {
	Status      string `json:"status"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

// DeleteEmailAccountRequest 删除邮件账号请求
type DeleteEmailAccountRequest struct {
	AccountID int64 `json:"account_id" binding:"required"`
}
