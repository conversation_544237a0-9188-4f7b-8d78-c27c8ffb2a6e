package service

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"gitee.com/heiyee/platforms/email/internal/application/email/dto"
	templateRenderService "gitee.com/heiyee/platforms/email/internal/application/template/service"
	"gitee.com/heiyee/platforms/email/internal/domain/email/entity"
	"gitee.com/heiyee/platforms/email/internal/domain/email/repository"
	emailErrors "gitee.com/heiyee/platforms/email/internal/domain/errors"
	templateRepo "gitee.com/heiyee/platforms/email/internal/domain/template/repository"
	"gitee.com/heiyee/platforms/email/internal/infrastructure/external"
	"gitee.com/heiyee/platforms/email/internal/infrastructure/persistence/model"
	repo "gitee.com/heiyee/platforms/email/internal/infrastructure/persistence/repository"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
)

// EmailAccountApplicationService 邮件账号应用服务
type EmailAccountApplicationService struct {
	emailAccountRepo   repository.EmailAccountRepository
	templateRepo       templateRepo.Repository
	emailSenderFactory *external.EmailSenderFactory
	templateRenderSvc  *templateRenderService.TemplateRenderApplicationService
	entityFactory      *entity.EntityFactory
	logger             logiface.Logger

	providerRepo *repo.EmailProviderRepositoryImpl
}

// NewEmailAccountApplicationService 创建邮件账号应用服务
func NewEmailAccountApplicationService(
	emailAccountRepo repository.EmailAccountRepository,
	templateRepo templateRepo.Repository,
	emailSenderFactory *external.EmailSenderFactory,
	templateRenderSvc *templateRenderService.TemplateRenderApplicationService,
	entityFactory *entity.EntityFactory,
	logger logiface.Logger,
) *EmailAccountApplicationService {
	return &EmailAccountApplicationService{
		emailAccountRepo:   emailAccountRepo,
		templateRepo:       templateRepo,
		emailSenderFactory: emailSenderFactory,
		templateRenderSvc:  templateRenderSvc,
		entityFactory:      entityFactory,
		logger:             logger,
	}
}

// WithProviderRepository 注入服务商仓储
func (s *EmailAccountApplicationService) WithProviderRepository(providerRepo *repo.EmailProviderRepositoryImpl) *EmailAccountApplicationService {
	s.providerRepo = providerRepo
	return s
}

// CreateEmailAccount 创建邮件账号
func (s *EmailAccountApplicationService) CreateEmailAccount(ctx context.Context, tenantId int64, req *dto.CreateEmailAccountRequest) (*dto.EmailAccountResponse, error) {
	// 从上下文中获取内部应用ID
	internalAppID, ok := usercontext.GetInternalAppID(ctx)
	if !ok || internalAppID <= 0 {
		s.logger.Error(ctx, "Internal app ID not found or invalid in context",
			logiface.Int64("tenant_id", tenantId))
		return nil, emailErrors.NewSystemError("internal_app_id", "内部应用ID不能为空")
	}

	// 检查账号名称是否已存在
	existingAccount, err := s.emailAccountRepo.FindByName(ctx, tenantId, req.Name)
	if err != nil && err != entity.ErrAccountNotFound {
		return nil, emailErrors.NewDatabaseError("检查账户名称", "数据查询失败")
	}
	if existingAccount != nil {
		return nil, entity.ErrAccountAlreadyExists
	}

	// 使用实体工厂创建邮件账号实体 - 自动生成分布式ID
	account, err := s.entityFactory.NewEmailAccount(ctx, tenantId, internalAppID, req.Name, entity.AccountType(req.Type), req.Provider, req.FromAddress)
	if err != nil {
		return nil, emailErrors.NewSystemError("创建邮件账户", "账户实体创建失败")
	}

	// 设置SMTP配置
	if req.Type == int(entity.AccountTypeSMTP) {
		account.SetSMTPConfig(req.Host, req.Port, req.Username, req.Password)
	}

	// 设置其他属性
	account.FromName = req.FromName
	account.ReplyToAddress = req.ReplyToAddress
	account.IsSSL = req.IsSSL
	// 创建时默认不启用，需测试通过后再启用
	account.IsActive = false

	// 设置限制
	account.SetLimits(req.DailyLimit, req.MonthlyLimit)
	// 固定间隔/自动决策由 Config 承载：send_interval_seconds / auto_decision

	// 设置配置（标准化速率控制：auto_decision / send_interval_seconds）
	if req.Config != nil {
		account.Config.UpdateFromMap(req.Config)
	}

	// 验证账号
	if err := account.Validate(); err != nil {
		return nil, emailErrors.NewSystemError("验证邮件账户", "账户验证失败")
	}

	// 验证SMTP凭据（如果是SMTP类型）
	if req.Type == int(entity.AccountTypeSMTP) {
		if err := s.emailSenderFactory.ValidateAccountCredentials(ctx, account); err != nil {
			s.logger.Error(ctx, "SMTP credentials validation failed",
				logiface.Error(err),
				logiface.String("host", req.Host),
				logiface.Int("port", req.Port),
				logiface.String("username", req.Username))
			return nil, emailErrors.NewValidationError("password", "SMTP密码验证失败，请检查用户名和密码")
		}
	}

	// 保存到数据库
	if err := s.emailAccountRepo.Save(ctx, account); err != nil {
		return nil, emailErrors.NewDatabaseError("保存邮件账户", "保存失败")
	}

	return s.toEmailAccountResponse(account), nil
}

// GetEmailAccount 获取邮件账号
func (s *EmailAccountApplicationService) GetEmailAccount(ctx context.Context, req *dto.GetEmailAccountRequest) (*dto.EmailAccountResponse, error) {
	account, err := s.emailAccountRepo.FindByAccountID(ctx, req.AccountID)
	if err != nil {
		return nil, emailErrors.NewAccountNotFoundError(req.AccountID)
	}

	return s.toEmailAccountResponse(account), nil
}

// UpdateEmailAccount 更新邮件账号
func (s *EmailAccountApplicationService) UpdateEmailAccount(ctx context.Context, tenantId int64, req *dto.UpdateEmailAccountRequest) (*dto.EmailAccountResponse, error) {
	// 获取现有账号
	account, err := s.emailAccountRepo.FindByAccountID(ctx, req.AccountID)
	if err != nil {
		return nil, emailErrors.NewAccountNotFoundError(req.AccountID)
	}

	// 检查名称是否已被其他账号使用
	if req.Name != "" && req.Name != account.Name {
		existingAccount, err := s.emailAccountRepo.FindByName(ctx, tenantId, req.Name)
		if err != nil && err != entity.ErrAccountNotFound {
			return nil, emailErrors.NewDatabaseError("检查账户名称", "数据查询失败")
		}
		if existingAccount != nil && existingAccount.ID != account.ID {
			return nil, entity.ErrAccountAlreadyExists
		}
		account.Name = req.Name
	}

	// 更新基本信息
	if req.Provider != "" {
		account.Provider = req.Provider
	}
	if req.FromAddress != "" {
		account.FromAddress = req.FromAddress
	}
	if req.FromName != "" {
		account.FromName = req.FromName
	}
	if req.ReplyToAddress != "" {
		account.ReplyToAddress = req.ReplyToAddress
	}

	// 更新SMTP配置
	if req.Host != "" {
		account.Host = req.Host
	}
	if req.Port > 0 {
		account.Port = req.Port
	}
	if req.Username != "" {
		account.Username = req.Username
	}
	if req.Password != "" {
		account.Password = req.Password
	}

	// 更新SSL和激活状态
	if req.IsSSL != nil {
		account.IsSSL = *req.IsSSL
	}
	if req.IsActive != nil {
		if *req.IsActive {
			account.Activate()
		} else {
			account.Deactivate()
		}
	}

	// 更新限制
	if req.DailyLimit != nil {
		account.DailyLimit = *req.DailyLimit
	}
	if req.MonthlyLimit != nil {
		account.MonthlyLimit = *req.MonthlyLimit
	}

	// 更新配置（标准化速率控制：auto_decision / send_interval_seconds）
	if req.Config != nil {
		account.Config.UpdateFromMap(req.Config)
	}

	// 验证账号
	if err := account.Validate(); err != nil {
		return nil, emailErrors.NewSystemError("验证邮件账户", "账户验证失败")
	}

	// 如果更新了SMTP相关配置，需要重新验证凭据
	needsValidation := false
	if req.Host != "" || req.Port > 0 || req.Username != "" || req.Password != "" {
		needsValidation = true
	}

	if account.Type == entity.AccountTypeSMTP && needsValidation {
		if err := s.emailSenderFactory.ValidateAccountCredentials(ctx, account); err != nil {
			s.logger.Error(ctx, "SMTP credentials validation failed during update",
				logiface.Error(err),
				logiface.String("host", account.Host),
				logiface.Int("port", account.Port),
				logiface.String("username", account.Username))
			return nil, emailErrors.NewValidationError("password", "SMTP密码验证失败，请检查用户名和密码")
		}
	}

	// 保存更新
	if err := s.emailAccountRepo.Update(ctx, account); err != nil {
		return nil, emailErrors.NewDatabaseError("更新邮件账户", "更新失败")
	}

	return s.toEmailAccountResponse(account), nil
}

// ListEmailAccounts 获取邮件账号列表
func (s *EmailAccountApplicationService) ListEmailAccounts(ctx context.Context, tenantId int64, req *dto.ListEmailAccountsRequest) (*dto.ListEmailAccountsResponse, error) {
	accounts, total, err := s.emailAccountRepo.FindByTenantID(ctx, tenantId, req.Pagination.Offset(), req.Pagination.Limit())
	if err != nil {
		return nil, emailErrors.NewDatabaseError("获取邮件账户列表", "数据查询失败")
	}

	// 转换为响应DTO
	accountResponses := make([]*dto.EmailAccountResponse, len(accounts))
	for i, account := range accounts {
		accountResponses[i] = s.toEmailAccountResponse(account)
	}

	// 计算总页数
	totalPages := int64(math.Ceil(float64(total) / float64(req.Pagination.Limit())))

	return &dto.ListEmailAccountsResponse{
		Accounts:   accountResponses,
		Total:      total,
		Page:       req.Pagination.Page,
		PageSize:   req.Pagination.Limit(),
		TotalPages: totalPages,
	}, nil
}

// TestEmailAccount 测试邮件账号
func (s *EmailAccountApplicationService) TestEmailAccount(ctx context.Context, tenantId int64, req *dto.TestEmailAccountRequest) (*dto.TestEmailAccountResponse, error) {
	// 获取邮件账号
	account, err := s.emailAccountRepo.FindByAccountID(ctx, req.AccountID)
	if err != nil {
		return nil, emailErrors.NewAccountNotFoundError(req.AccountID)
	}

	// 检查账号是否属于当前租户
	if account.TenantID != tenantId {
		return nil, emailErrors.NewAccountNotFoundError(req.AccountID)
	}

	// 从上下文中获取内部应用ID
	internalAppID, ok := usercontext.GetInternalAppID(ctx)
	if !ok || internalAppID <= 0 {
		s.logger.Error(ctx, "Internal app ID not found or invalid in context",
			logiface.Int64("tenant_id", tenantId))
		return nil, emailErrors.NewSystemError("internal_app_id", "内部应用ID不能为空")
	}

	// 创建简单的测试邮件
	message, err := s.entityFactory.NewEmailMessage(
		ctx,
		tenantId,
		internalAppID,
		req.TestEmail, // 收件人
	)
	if err != nil {
		return nil, emailErrors.NewSystemError("创建验证邮件", "邮件实体创建失败")
	}

	// 设置邮件内容（内置简单文本，不使用模板）
	message.FromAddress = account.FromAddress

	// 创建简单的邮件内容参数
	currentTime := time.Now().Format("2006年01月02日 15:04:05")
	subject := fmt.Sprintf("【测试邮件】%s 邮件服务配置验证", account.Name)
	htmlContent := fmt.Sprintf(`
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <h2 style="color: #1890ff;">邮件服务配置验证</h2>
    <p>这是一封测试邮件，用于验证您的邮件服务配置是否正常工作。</p>
    
    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0;">账户信息</h3>
        <p><strong>账户名称:</strong> %s</p>
        <p><strong>发件地址:</strong> %s</p>
        <p><strong>测试时间:</strong> %s</p>
    </div>
    
    <p style="color: #52c41a;"><strong>✓ 如果您收到这封邮件，说明您的邮件服务配置正常！</strong></p>
    
    <hr style="border: none; border-top: 1px solid #e8e8e8; margin: 20px 0;">
    <p style="font-size: 12px; color: #999;">此邮件为系统自动发送，请勿回复。</p>
</div>`, account.Name, account.FromAddress, currentTime)

	textContent := fmt.Sprintf(`邮件服务配置验证

这是一封测试邮件，用于验证您的邮件服务配置是否正常工作。

账户信息:
- 账户名称: %s
- 发件地址: %s  
- 测试时间: %s

✓ 如果您收到这封邮件，说明您的邮件服务配置正常！

此邮件为系统自动发送，请勿回复。`, account.Name, account.FromAddress, currentTime)

	// 使用EmailSenderFactory发送测试邮件，传入渲染好的内容
	var testSuccess bool
	var testMessage string

	err = s.sendTestEmail(ctx, account, message, subject, htmlContent, textContent)
	if err != nil {
		testSuccess = false
		testMessage = fmt.Sprintf("测试邮件发送失败: %s", err.Error())
		s.logger.Error(ctx, "Email account test failed",
			logiface.Error(err),
			logiface.String("account_id", fmt.Sprintf("%d", account.ID)),
			logiface.String("test_email", req.TestEmail),
			logiface.String("account_type", fmt.Sprintf("%d", account.Type)),
			logiface.String("provider", account.Provider))
		return nil, err
	} else {
		testSuccess = true
		testMessage = "测试邮件发送成功，请检查您的邮箱"
		s.logger.Info(ctx, "Email account test successful",
			logiface.String("account_id", fmt.Sprintf("%d", account.ID)),
			logiface.String("test_email", req.TestEmail),
			logiface.String("account_type", fmt.Sprintf("%d", account.Type)),
			logiface.String("provider", account.Provider))
	}

	// 更新测试状态
	var testStatus entity.TestStatus
	if testSuccess {
		testStatus = entity.TestStatusSuccess
		// 测试成功时激活账户
		account.Activate()
	} else {
		testStatus = entity.TestStatusFailed
	}

	account.SetTestResult(testStatus, testMessage)
	if err := s.emailAccountRepo.Update(ctx, account); err != nil {
		s.logger.Error(ctx, "Failed to update test result",
			logiface.Error(err),
			logiface.String("account_id", fmt.Sprintf("%d", account.ID)))
		// 不返回错误，因为测试本身可能成功，只是更新状态失败
	}

	return &dto.TestEmailAccountResponse{
		Success: testSuccess,
		Message: testMessage,
	}, nil
}

// sendTestEmail 发送测试邮件的内部方法
func (s *EmailAccountApplicationService) sendTestEmail(ctx context.Context, account *entity.EmailAccount, message *entity.EmailMessage, subject, htmlContent, textContent string) error {
	// 构建发送参数
	sendParams := &external.EmailSendParams{
		Account:             account,
		Message:             message,
		RenderedSubject:     subject,
		RenderedHTMLContent: htmlContent,
		RenderedTextContent: textContent,
		TenantID:            account.TenantID,
		InternalAppID:       message.InternalAppID,
		RcptList:            []string{message.ToAddresses},
		ToHeader:            message.ToAddresses,
		CcHeader:            "",
	}

	// 根据账户类型选择对应的发送协议
	switch account.Type {
	case entity.AccountTypeSMTP:
		return s.sendTestViaSMTP(ctx, sendParams)
	case entity.AccountTypeAPI:
		return s.sendTestViaAPI(ctx, sendParams)
	default:
		return fmt.Errorf("不支持的邮件协议类型: %d", account.Type)
	}
}

// sendTestViaSMTP 通过SMTP发送测试邮件
func (s *EmailAccountApplicationService) sendTestViaSMTP(ctx context.Context, params *external.EmailSendParams) error {
	service := external.NewSMTPService(s.logger)
	return service.SendEmail(ctx, params)
}

// sendTestViaAPI 通过API发送测试邮件
func (s *EmailAccountApplicationService) sendTestViaAPI(ctx context.Context, params *external.EmailSendParams) error {
	// API发送暂时不支持，返回错误
	return fmt.Errorf("API协议测试邮件发送暂不支持")
}

// ListProviders 获取启用的邮箱服务商列表（包含图标与预设配置）
func (s *EmailAccountApplicationService) ListProviders(ctx context.Context) ([]*model.EmailProviderModel, error) {
	if s.providerRepo == nil {
		return nil, fmt.Errorf("provider repository not configured")
	}
	return s.providerRepo.FindAll(ctx)
}

// GetProviderByName 按名称获取服务商
func (s *EmailAccountApplicationService) GetProviderByName(ctx context.Context, name string) (*model.EmailProviderModel, error) {
	if s.providerRepo == nil {
		return nil, fmt.Errorf("provider repository not configured")
	}
	return s.providerRepo.FindByName(ctx, name)
}

// GetAccountTypes 返回账户类型（避免HTTP层硬编码）
func (s *EmailAccountApplicationService) GetAccountTypes(ctx context.Context) ([]dto.AccountTypeItem, error) {
	if s.providerRepo == nil {
		return nil, fmt.Errorf("provider repository not configured")
	}
	providers, err := s.providerRepo.FindAll(ctx)
	if err != nil {
		return nil, err
	}
	items := make([]dto.AccountTypeItem, 0, len(providers))
	for _, p := range providers {
		items = append(items, dto.AccountTypeItem{
			Type:        p.Name,
			Name:        p.DisplayName,
			Description: p.DisplayName,
		})
	}
	return items, nil
}

// ApplyProviderToRequest 将服务商预设应用到创建请求（快捷填充）
func (s *EmailAccountApplicationService) ApplyProviderToRequest(ctx context.Context, providerName string, req *dto.CreateEmailAccountRequest) error {
	if s.providerRepo == nil {
		return fmt.Errorf("provider repository not configured")
	}
	p, err := s.providerRepo.FindByName(ctx, providerName)
	if err != nil {
		return err
	}
	if p == nil {
		return fmt.Errorf("provider not found")
	}
	// 覆盖请求的主机与端口、SSL
	if req.Host == "" {
		req.Host = p.SMTPHost
	}
	if req.Port == 0 && p.SMTPPort > 0 {
		req.Port = p.SMTPPort
	}
	req.IsSSL = p.UseSSL
	if req.Provider == "" {
		req.Provider = p.Name
	}
	return nil
}

// getAccountTypeName 获取账户类型名称
func getAccountTypeName(accountType entity.AccountType) string {
	switch accountType {
	case entity.AccountTypeSMTP:
		return "SMTP"
	case entity.AccountTypeIMAP:
		return "IMAP"
	case entity.AccountTypePOP3:
		return "POP3"
	case entity.AccountTypeExchange:
		return "Exchange"
	case entity.AccountTypeAPI:
		return "API"
	default:
		return "未知类型"
	}
}

// DeleteEmailAccount 删除邮件账号
func (s *EmailAccountApplicationService) DeleteEmailAccount(ctx context.Context, tenantId int64, req *dto.DeleteEmailAccountRequest) error {
	// 检查账号是否存在
	account, err := s.emailAccountRepo.FindByAccountID(ctx, req.AccountID)
	if err != nil {
		return emailErrors.NewAccountNotFoundError(req.AccountID)
	}

	// 检查账号是否属于当前租户
	if account.TenantID != tenantId {
		return emailErrors.NewAccountNotFoundError(req.AccountID)
	}

	// 删除账号
	if err := s.emailAccountRepo.Delete(ctx, account.ID); err != nil {
		return emailErrors.NewDatabaseError("删除邮件账户", "删除失败")
	}

	return nil
}

// toEmailAccountResponse 转换为响应DTO
func (s *EmailAccountApplicationService) toEmailAccountResponse(account *entity.EmailAccount) *dto.EmailAccountResponse {
	return &dto.EmailAccountResponse{
		ID:             account.ID,
		TenantID:       account.TenantID,
		Name:           account.Name,
		Type:           int(account.Type),
		Provider:       account.Provider,
		Host:           account.Host,
		Port:           account.Port,
		Username:       account.Username,
		FromAddress:    account.FromAddress,
		FromName:       account.FromName,
		ReplyToAddress: account.ReplyToAddress,
		IsSSL:          account.IsSSL,
		IsActive:       account.IsActive,
		DailyLimit:     account.DailyLimit,
		MonthlyLimit:   account.MonthlyLimit,
		SentToday:      account.SentToday,
		SentThisMonth:  account.SentThisMonth,
		LastSentAt:     account.LastSentAt,
		TestStatus:     int(account.TestStatus),
		TestMessage:    account.TestMessage,
		Config:         account.Config.ToMap(),
		CreatedAt:      account.CreatedAt,
		UpdatedAt:      account.UpdatedAt,
		CreatedBy:      account.CreatedBy,
		UpdatedBy:      account.UpdatedBy,
		Version:        account.Version,
		IsSystem:       account.IsSystem,
	}
}

// normalizeRateControlConfig 规范化速率控制配置：确保 auto_decision 为布尔，send_interval_seconds 为整数
func normalizeRateControlConfig(cfg map[string]interface{}) map[string]interface{} {
	out := make(map[string]interface{}, len(cfg))
	for k, v := range cfg {
		out[k] = v
	}

	// auto_decision -> bool
	if val, ok := out["auto_decision"]; ok {
		switch t := val.(type) {
		case bool:
			// ok
		case string:
			lower := strings.ToLower(t)
			out["auto_decision"] = lower == "true" || lower == "1" || lower == "yes" || lower == "on"
		case float64:
			out["auto_decision"] = t != 0
		case int:
			out["auto_decision"] = t != 0
		default:
			out["auto_decision"] = false
		}
	}

	// send_interval_seconds -> int
	if val, ok := out["send_interval_seconds"]; ok {
		switch t := val.(type) {
		case float64:
			out["send_interval_seconds"] = int(t)
		case string:
			if t == "" {
				delete(out, "send_interval_seconds")
			} else if n, err := strconv.Atoi(t); err == nil {
				out["send_interval_seconds"] = n
			} else {
				delete(out, "send_interval_seconds")
			}
		case int:
			out["send_interval_seconds"] = t
		default:
			// 非法则移除，避免脏数据
			delete(out, "send_interval_seconds")
		}
	}

	return out
}
