package service

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/email/internal/application/template/dto"
	emailErrors "gitee.com/heiyee/platforms/email/internal/domain/errors"
	"gitee.com/heiyee/platforms/email/internal/domain/template/repository"
	templateService "gitee.com/heiyee/platforms/email/internal/domain/template/service"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
)

// TemplateRenderApplicationService 模板渲染应用服务
type TemplateRenderApplicationService struct {
	templateRepo repository.Repository
	renderer     templateService.TemplateRenderer
	logger       logiface.Logger
}

// NewTemplateRenderApplicationService 创建模板渲染应用服务
func NewTemplateRenderApplicationService(
	templateRepo repository.Repository,
	renderer templateService.TemplateRenderer,
	logger logiface.Logger,
) *TemplateRenderApplicationService {
	return &TemplateRenderApplicationService{
		templateRepo: templateRepo,
		renderer:     renderer,
		logger:       logger,
	}
}

// PreviewTemplate 预览模板（场景1：模板维护时的预览）
func (s *TemplateRenderApplicationService) PreviewTemplate(ctx context.Context, req *dto.PreviewTemplateRequest) (*dto.PreviewTemplateResponse, error) {
	// 从上下文获取租户ID
	tenantID, ok := usercontext.GetTenantID(ctx)
	if !ok || tenantID == 0 {
		return nil, emailErrors.NewEmailError(emailErrors.CodeTenantNotFound, "租户信息未找到，请重新登录")
	}

	// 获取模板
	template, err := s.templateRepo.Get(ctx, tenantID, req.TemplateID)
	if err != nil {
		s.logger.Error(ctx, "Failed to get template for preview",
			logiface.Error(err),
			logiface.Int64("template_id", req.TemplateID),
			logiface.Int64("tenant_id", tenantID))
		return nil, emailErrors.NewTemplateNotFoundError(fmt.Sprintf("%d", req.TemplateID))
	}

	// 准备渲染请求
	renderReq := &templateService.TemplateRenderRequest{
		Template:  template,
		Variables: req.Variables,
		Mode:      templateService.RenderModePreview,
		Context: &templateService.TemplateRenderContext{
			TenantID: tenantID,
		},
	}

	// 渲染模板
	result, err := s.renderer.RenderTemplate(ctx, renderReq)
	if err != nil {
		s.logger.Error(ctx, "Failed to render template for preview",
			logiface.Error(err),
			logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewSystemError("template_render", "模板预览失败")
	}

	// 构建响应
	response := &dto.PreviewTemplateResponse{
		Subject:       result.Subject,
		HTMLContent:   result.HTMLContent,
		TextContent:   result.TextContent,
		UsedVariables: result.UsedVariables,
	}

	s.logger.Info(ctx, "Template preview successful",
		logiface.Int64("template_id", req.TemplateID),
		logiface.Int64("tenant_id", tenantID),
		logiface.Int("used_variables_count", len(result.UsedVariables)))

	return response, nil
}

// ValidateTemplateForSending 验证模板发送参数（场景3：发送前的参数检查）
func (s *TemplateRenderApplicationService) ValidateTemplateForSending(ctx context.Context, req *dto.ValidateTemplateRequest) (*dto.ValidateTemplateResponse, error) {
	// 从上下文获取租户ID
	tenantID, ok := usercontext.GetTenantID(ctx)
	if !ok || tenantID == 0 {
		return nil, emailErrors.NewEmailError(emailErrors.CodeTenantNotFound, "租户信息未找到，请重新登录")
	}

	// 获取模板
	template, err := s.templateRepo.Get(ctx, tenantID, req.TemplateID)
	if err != nil {
		s.logger.Error(ctx, "Failed to get template for validation",
			logiface.Error(err),
			logiface.Int64("template_id", req.TemplateID),
			logiface.Int64("tenant_id", tenantID))
		return nil, emailErrors.NewTemplateNotFoundError(fmt.Sprintf("%d", req.TemplateID))
	}

	// 准备系统变量
	systemVars := s.getSystemVariables(ctx, tenantID)

	// 准备渲染请求
	renderReq := &templateService.TemplateRenderRequest{
		Template:  template,
		Variables: req.Variables,
		Mode:      templateService.RenderModeValidation,
		Context: &templateService.TemplateRenderContext{
			TenantID:   tenantID,
			SystemVars: systemVars,
		},
	}

	// 渲染模板
	result, err := s.renderer.RenderTemplate(ctx, renderReq)
	if err != nil {
		s.logger.Error(ctx, "Failed to render template for validation",
			logiface.Error(err),
			logiface.Int64("template_id", req.TemplateID))
		return nil, emailErrors.NewSystemError("template_render", "模板验证失败")
	}

	// 构建响应
	response := &dto.ValidateTemplateResponse{
		Valid:            len(result.Errors) == 0,
		Subject:          result.Subject,
		HTMLContent:      result.HTMLContent,
		TextContent:      result.TextContent,
		UsedVariables:    result.UsedVariables,
		MissingVariables: result.MissingVars,
		InvalidVariables: result.InvalidVars,
		ValidationErrors: make([]dto.TemplateValidationError, 0, len(result.Errors)),
	}

	// 转换错误信息
	for _, err := range result.Errors {
		response.ValidationErrors = append(response.ValidationErrors, dto.TemplateValidationError{
			Type:    err.Type,
			Message: err.Message,
			Field:   err.Field,
		})
	}

	// 如果有验证错误，记录日志
	if !response.Valid {
		s.logger.Warn(ctx, "Template validation failed",
			logiface.Int64("template_id", req.TemplateID),
			logiface.Int64("tenant_id", tenantID),
			logiface.Any("missing_variables", result.MissingVars),
			logiface.Any("invalid_variables", result.InvalidVars),
			logiface.Int("error_count", len(result.Errors)))
	}

	return response, nil
}

// RenderTemplateForSending 渲染模板用于发送（场景2：发送时的模板格式化）
func (s *TemplateRenderApplicationService) RenderTemplateForSending(ctx context.Context, req *dto.RenderTemplateRequest) (*dto.RenderTemplateResponse, error) {
	// 获取模板
	template, err := s.templateRepo.Get(ctx, req.TenantID, req.TemplateID)
	if err != nil {
		s.logger.Error(ctx, "Failed to get template for sending",
			logiface.Error(err),
			logiface.Int64("template_id", req.TemplateID),
			logiface.Int64("tenant_id", req.TenantID))
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	// 准备系统变量
	systemVars := s.getSystemVariables(ctx, req.TenantID)

	// 准备渲染请求
	renderReq := &templateService.TemplateRenderRequest{
		Template:  template,
		Variables: req.Variables,
		Mode:      templateService.RenderModeSending,
		Context: &templateService.TemplateRenderContext{
			TenantID:     req.TenantID,
			UserID:       req.UserID,
			EmailMessage: req.EmailMessage,
			SystemVars:   systemVars,
		},
		TrackingConfig: s.convertToServiceTrackingConfig(req.TrackingConfig),
	}

	// 渲染模板
	result, err := s.renderer.RenderTemplate(ctx, renderReq)
	if err != nil {
		s.logger.Error(ctx, "Failed to render template for sending",
			logiface.Error(err),
			logiface.Int64("template_id", req.TemplateID),
			logiface.Int64("tenant_id", req.TenantID))
		return nil, fmt.Errorf("failed to render template: %w", err)
	}

	// 构建响应
	response := &dto.RenderTemplateResponse{
		Subject:         result.Subject,
		HTMLContent:     result.HTMLContent,
		TextContent:     result.TextContent,
		TemplateVersion: result.TemplateVersion,
		UsedVariables:   result.UsedVariables,
		OriginalTemplate: &dto.OriginalTemplateContent{
			Subject:          template.Subject,
			HTMLContent:      template.HTMLContent,
			PlainTextContent: template.PlainTextContent,
		},
	}

	s.logger.Info(ctx, "Template rendered for sending",
		logiface.Int64("template_id", req.TemplateID),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.Int64("template_version", *result.TemplateVersion),
		logiface.Int("used_variables_count", len(result.UsedVariables)))

	return response, nil
}

// getSystemVariables 获取系统变量
func (s *TemplateRenderApplicationService) getSystemVariables(ctx context.Context, tenantID int64) map[string]interface{} {
	vars := make(map[string]interface{})

	// 可以从用户上下文或其他服务获取系统变量
	if appInfo, ok := usercontext.GetAppInfo(ctx); ok {
		vars["tenant_id"] = appInfo.TenantID
		vars["app_id"] = appInfo.InternalAppId

		// 这里可以调用用户服务获取租户信息，设置 company_name 等
		// 暂时省略具体实现
	}

	return vars
}

// convertToServiceTrackingConfig 转换DTO追踪配置到服务层追踪配置
func (s *TemplateRenderApplicationService) convertToServiceTrackingConfig(dtoConfig *dto.TrackingConfig) *templateService.TrackingConfig {
	if dtoConfig == nil {
		return nil
	}

	return &templateService.TrackingConfig{
		EnableTracking: dtoConfig.EnableTracking,
		CampaignID:     dtoConfig.CampaignID,
		RecipientEmail: dtoConfig.RecipientEmail,
		TrackingDomain: dtoConfig.TrackingDomain,
	}
}
