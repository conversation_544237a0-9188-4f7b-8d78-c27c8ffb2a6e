package dto

import (
	"gitee.com/heiyee/platforms/email/internal/domain/template/entity"
)

// TemplateTrackingOptionsDTO 模板追踪选项传输对象
type TemplateTrackingOptionsDTO struct {
	Enabled     bool                   `json:"enabled"`
	UTM         *UTMOptionsDTO        `json:"utm,omitempty"`
	Attribution *AttributionOptionsDTO `json:"attribution,omitempty"`
	Pixel       *PixelOptionsDTO      `json:"pixel,omitempty"`
	Redirect    *RedirectOptionsDTO   `json:"redirect,omitempty"`
	Domain      *DomainOptionsDTO     `json:"domain,omitempty"`
}

// UTMOptionsDTO UTM参数传输对象
type UTMOptionsDTO struct {
	AutoAppend bool              `json:"autoAppend"`
	Campaign   string            `json:"campaign"`
	Content    string            `json:"content"`
	Source     string            `json:"source"`
	Medium     string            `json:"medium"`
	Term       string            `json:"term"`
	Custom     map[string]string `json:"custom,omitempty"`
}

// AttributionOptionsDTO 归因传输对象
type AttributionOptionsDTO struct {
	WindowHours      int  `json:"windowHours"`
	EnableConversion bool `json:"enableConversion"`
}

// PixelOptionsDTO 像素追踪传输对象
type PixelOptionsDTO struct {
	EnableAmp    bool `json:"enableAmp"`
	EnableBeacon bool `json:"enableBeacon"`
}

// RedirectOptionsDTO 链接重定向传输对象
type RedirectOptionsDTO struct {
	Whitelist []string `json:"whitelist"`
	Blacklist []string `json:"blacklist"`
}

// DomainOptionsDTO 域名传输对象
type DomainOptionsDTO struct {
	TrackingDomain string `json:"trackingDomain"`
	Status         string `json:"status"`
	DNSValidated   bool   `json:"dnsValidated"`
}

// GetTemplateTrackingRequest 获取模板追踪配置请求
type GetTemplateTrackingRequest struct {
	TemplateID int64 `json:"template_id" binding:"required"`
}

// GetTemplateTrackingResponse 获取模板追踪配置响应
type GetTemplateTrackingResponse struct {
	TemplateID      int64                       `json:"template_id"`
	TrackingEnabled bool                        `json:"tracking_enabled"`
	TrackingOptions *TemplateTrackingOptionsDTO `json:"tracking_options"`
}

// UpdateTemplateTrackingRequest 更新模板追踪配置请求
type UpdateTemplateTrackingRequest struct {
	TemplateID      int64                       `json:"template_id" binding:"required"`
	TrackingEnabled bool                        `json:"tracking_enabled"`
	TrackingOptions *TemplateTrackingOptionsDTO `json:"tracking_options"`
}

// UpdateTemplateTrackingResponse 更新模板追踪配置响应
type UpdateTemplateTrackingResponse struct {
	TemplateID      int64                       `json:"template_id"`
	TrackingEnabled bool                        `json:"tracking_enabled"`
	TrackingOptions *TemplateTrackingOptionsDTO `json:"tracking_options"`
}

// FromEntity 从领域实体转换追踪选项
func (dto *TemplateTrackingOptionsDTO) FromEntity(entity *entity.TemplateTrackingOptions) {
	if entity == nil {
		return
	}

	dto.Enabled = entity.Enabled

	if entity.UTM != nil {
		dto.UTM = &UTMOptionsDTO{
			AutoAppend: entity.UTM.AutoAppend,
			Campaign:   entity.UTM.Campaign,
			Content:    entity.UTM.Content,
			Source:     entity.UTM.Source,
			Medium:     entity.UTM.Medium,
			Term:       entity.UTM.Term,
			Custom:     entity.UTM.Custom,
		}
	}

	if entity.Attribution != nil {
		dto.Attribution = &AttributionOptionsDTO{
			WindowHours:      entity.Attribution.WindowHours,
			EnableConversion: entity.Attribution.EnableConversion,
		}
	}

	if entity.Pixel != nil {
		dto.Pixel = &PixelOptionsDTO{
			EnableAmp:    entity.Pixel.EnableAmp,
			EnableBeacon: entity.Pixel.EnableBeacon,
		}
	}

	if entity.Redirect != nil {
		dto.Redirect = &RedirectOptionsDTO{
			Whitelist: entity.Redirect.Whitelist,
			Blacklist: entity.Redirect.Blacklist,
		}
	}

	if entity.Domain != nil {
		dto.Domain = &DomainOptionsDTO{
			TrackingDomain: entity.Domain.TrackingDomain,
			Status:         entity.Domain.Status,
			DNSValidated:   entity.Domain.DNSValidated,
		}
	}
}

// ToEntity 转换为领域实体追踪选项
func (dto *TemplateTrackingOptionsDTO) ToEntity() *entity.TemplateTrackingOptions {
	if dto == nil {
		return nil
	}

	options := &entity.TemplateTrackingOptions{
		Enabled: dto.Enabled,
	}

	if dto.UTM != nil {
		options.UTM = &entity.UTMOptions{
			AutoAppend: dto.UTM.AutoAppend,
			Campaign:   dto.UTM.Campaign,
			Content:    dto.UTM.Content,
			Source:     dto.UTM.Source,
			Medium:     dto.UTM.Medium,
			Term:       dto.UTM.Term,
			Custom:     dto.UTM.Custom,
		}
	}

	if dto.Attribution != nil {
		options.Attribution = &entity.AttributionOptions{
			WindowHours:      dto.Attribution.WindowHours,
			EnableConversion: dto.Attribution.EnableConversion,
		}
	}

	if dto.Pixel != nil {
		options.Pixel = &entity.PixelOptions{
			EnableAmp:    dto.Pixel.EnableAmp,
			EnableBeacon: dto.Pixel.EnableBeacon,
		}
	}

	if dto.Redirect != nil {
		options.Redirect = &entity.RedirectOptions{
			Whitelist: dto.Redirect.Whitelist,
			Blacklist: dto.Redirect.Blacklist,
		}
	}

	if dto.Domain != nil {
		options.Domain = &entity.DomainOptions{
			TrackingDomain: dto.Domain.TrackingDomain,
			Status:         dto.Domain.Status,
			DNSValidated:   dto.Domain.DNSValidated,
		}
	}

	return options
}