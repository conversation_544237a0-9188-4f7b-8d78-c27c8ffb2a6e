package dto

// PreviewTemplateRequest 预览模板请求
type PreviewTemplateRequest struct {
	TemplateID int64                  `json:"template_id" binding:"required,min=1"`
	Variables  map[string]interface{} `json:"variables"`
}

// PreviewTemplateResponse 预览模板响应
type PreviewTemplateResponse struct {
	Subject       string   `json:"subject"`
	HTMLContent   string   `json:"html_content"`
	TextContent   string   `json:"text_content"`
	UsedVariables []string `json:"used_variables"`
}

// ValidateTemplateRequest 验证模板请求
type ValidateTemplateRequest struct {
	TemplateID int64                  `json:"template_id" binding:"required,min=1"`
	Variables  map[string]interface{} `json:"variables"`
}

// ValidateTemplateResponse 验证模板响应
type ValidateTemplateResponse struct {
	Valid            bool                      `json:"valid"`
	Subject          string                    `json:"subject"`
	HTMLContent      string                    `json:"html_content"`
	TextContent      string                    `json:"text_content"`
	UsedVariables    []string                  `json:"used_variables"`
	MissingVariables []string                  `json:"missing_variables"`
	InvalidVariables []string                  `json:"invalid_variables"`
	ValidationErrors []TemplateValidationError `json:"validation_errors"`
}

// TemplateValidationError 模板验证错误
type TemplateValidationError struct {
	Type    string `json:"type"` // subject/html/text/variable
	Message string `json:"message"`
	Field   string `json:"field"`
}

// RenderTemplateRequest 渲染模板请求（用于发送）
type RenderTemplateRequest struct {
	TemplateID     int64                  `json:"template_id"`
	TenantID       int64                  `json:"tenant_id"`
	UserID         int64                  `json:"user_id"`
	Variables      map[string]interface{} `json:"variables"`
	EmailMessage   interface{}            `json:"email_message,omitempty"`   // 邮件消息上下文
	TrackingConfig *TrackingConfig        `json:"tracking_config,omitempty"` // 追踪配置
}

// TrackingConfig 追踪配置
type TrackingConfig struct {
	EnableTracking bool   `json:"enable_tracking"`           // 是否启用追踪
	CampaignID     int64  `json:"campaign_id,omitempty"`     // 活动ID
	RecipientEmail string `json:"recipient_email"`           // 收件人邮箱
	TrackingDomain string `json:"tracking_domain,omitempty"` // 追踪域名
}

// RenderTemplateResponse 渲染模板响应（用于发送）
type RenderTemplateResponse struct {
	Subject          string                   `json:"subject"`
	HTMLContent      string                   `json:"html_content"`
	TextContent      string                   `json:"text_content"`
	TemplateVersion  *int64                   `json:"template_version"`
	UsedVariables    []string                 `json:"used_variables"`
	OriginalTemplate *OriginalTemplateContent `json:"original_template"`
}

// OriginalTemplateContent 原始模板内容（用于内容快照）
type OriginalTemplateContent struct {
	Subject          string `json:"subject"`
	HTMLContent      string `json:"html_content"`
	PlainTextContent string `json:"plain_text_content"`
}
