-- 添加追踪配置表
-- Migration: 0010_add_tracking_configs.sql
-- Created: 2025-01-14
-- Description: 添加追踪配置和域名管理相关表

-- 追踪配置表
CREATE TABLE IF NOT EXISTS email_tracking_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL,
    app_id BIGINT NOT NULL DEFAULT 1,
    tracking_domain VARCHAR(255) NOT NULL,
    tracking_secret VARCHAR(512) NOT NULL,
    utm_enabled BOOLEAN DEFAULT TRUE,
    utm_defaults JSON DEFAULT NULL COMMENT 'UTM参数默认值',
    conversion_window_hours INT DEFAULT 24,
    link_domain_whitelist TEXT COMMENT 'JSON格式的域名白名单',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL,
    
    INDEX idx_tenant_app (tenant_id, app_id),
    INDEX idx_tracking_domain (tracking_domain),
    INDEX idx_active (is_active),
    UNIQUE KEY uk_tenant_app_domain (tenant_id, app_id, tracking_domain)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='邮件追踪配置表';

-- 扩展邮件模板表，添加追踪选项
ALTER TABLE email_templates 
ADD COLUMN tracking_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用追踪' AFTER is_active,
ADD COLUMN tracking_options JSON DEFAULT NULL COMMENT '追踪配置选项' AFTER tracking_enabled;

-- 追踪域名表（支持自定义域名）
CREATE TABLE IF NOT EXISTS email_tracking_domains (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL,
    app_id BIGINT NOT NULL DEFAULT 1,
    domain VARCHAR(255) NOT NULL,
    tracking_secret VARCHAR(512) NOT NULL,
    ssl_cert_status ENUM('pending', 'valid', 'expired', 'failed') DEFAULT 'pending',
    ssl_cert_issued_at TIMESTAMP NULL,
    ssl_cert_expires_at TIMESTAMP NULL,
    dns_validated BOOLEAN DEFAULT FALSE,
    dns_validated_at TIMESTAMP NULL,
    dns_cname_record VARCHAR(500) COMMENT 'CNAME记录值',
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL,
    
    INDEX idx_tenant_app (tenant_id, app_id),
    INDEX idx_domain (domain),
    INDEX idx_active (is_active),
    INDEX idx_default (is_default),
    UNIQUE KEY uk_tenant_app_domain (tenant_id, app_id, domain)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='追踪域名配置表';

-- 链接重写映射表（用于调试和统计）
CREATE TABLE IF NOT EXISTS email_link_mappings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    tenant_id BIGINT NOT NULL,
    app_id BIGINT NOT NULL DEFAULT 1,
    message_id VARCHAR(255) NOT NULL,
    link_id VARCHAR(64) NOT NULL,
    original_url TEXT NOT NULL,
    tracking_url TEXT NOT NULL,
    url_hash VARCHAR(64) NOT NULL COMMENT 'URL的MD5哈希，用于去重',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_tenant_message (tenant_id, message_id),
    INDEX idx_link_id (link_id),
    INDEX idx_url_hash (url_hash),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='链接重写映射表';

-- 为现有租户创建默认追踪配置
INSERT INTO email_tracking_configs (tenant_id, app_id, tracking_domain, tracking_secret, utm_enabled, conversion_window_hours, is_active)
SELECT 
    t.id as tenant_id,
    1 as app_id,
    CONCAT('track.tenant', t.id, '.mailplatform.com') as tracking_domain,
    SHA2(CONCAT('tracking_secret_', t.id, '_', UNIX_TIMESTAMP()), 256) as tracking_secret,
    TRUE as utm_enabled,
    24 as conversion_window_hours,
    TRUE as is_active
FROM (
    SELECT DISTINCT tenant_id as id 
    FROM email_templates 
    WHERE tenant_id IS NOT NULL
    UNION
    SELECT DISTINCT tenant_id as id 
    FROM email_accounts 
    WHERE tenant_id IS NOT NULL
) t
WHERE NOT EXISTS (
    SELECT 1 FROM email_tracking_configs etc 
    WHERE etc.tenant_id = t.id AND etc.app_id = 1
);

-- 为现有租户创建默认追踪域名
INSERT INTO email_tracking_domains (tenant_id, app_id, domain, tracking_secret, is_active, is_default)
SELECT 
    tenant_id,
    app_id,
    tracking_domain,
    tracking_secret,
    TRUE,
    TRUE
FROM email_tracking_configs
WHERE NOT EXISTS (
    SELECT 1 FROM email_tracking_domains etd 
    WHERE etd.tenant_id = email_tracking_configs.tenant_id 
    AND etd.app_id = email_tracking_configs.app_id
);

-- 添加索引优化
CREATE INDEX idx_email_templates_tracking ON email_templates(tenant_id, tracking_enabled, is_active);

-- 创建视图：活跃的追踪配置
CREATE OR REPLACE VIEW v_active_tracking_configs AS
SELECT 
    etc.id,
    etc.tenant_id,
    etc.app_id,
    etc.tracking_domain,
    etc.tracking_secret,
    etc.utm_enabled,
    etc.utm_defaults,
    etc.conversion_window_hours,
    etc.link_domain_whitelist,
    etd.ssl_cert_status,
    etd.dns_validated,
    etc.created_at,
    etc.updated_at
FROM email_tracking_configs etc
LEFT JOIN email_tracking_domains etd ON (
    etc.tenant_id = etd.tenant_id 
    AND etc.app_id = etd.app_id 
    AND etc.tracking_domain = etd.domain
)
WHERE etc.is_active = TRUE;