-- 0009_add_template_version_and_contents.sql
-- B方案：在email_messages增加模板版本ID；新建email_message_contents存储渲染结果与模板原文快照

-- 1) email_messages 增加 template_version_id 列（可空）
ALTER TABLE `email_messages`
  ADD COLUMN `template_version_id` bigint NULL COMMENT '模板版本ID（历史表版本或时间戳）' AFTER `template_id`;
CREATE INDEX `idx_email_messages_template_version` ON `email_messages`(`template_version_id`);

-- 2) 新建 email_message_contents 表
CREATE TABLE IF NOT EXISTS `email_message_contents` (
  `email_id` bigint NOT NULL COMMENT '对齐 email_messages.email_id',
  `subject_template` text,
  `html_template` mediumtext,
  `text_template` text,
  `subject_rendered` text,
  `html_rendered` mediumtext,
  `text_rendered` text,
  `content_checksum` char(64) DEFAULT NULL,
  `size_bytes` bigint DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`email_id`),
  KEY `idx_email_message_contents_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='邮件消息内容快照（模板原文与渲染结果）';


