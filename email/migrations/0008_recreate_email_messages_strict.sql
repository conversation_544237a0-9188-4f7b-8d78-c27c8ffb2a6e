-- 0008_recreate_email_messages_strict.sql
-- 严格按给定设计：先删除表再创建；所有字段一次性在 CREATE TABLE 中定义（不使用 ALTER）
-- 说明：
--  - 地址列为逗号分隔字符串（varchar），发送内容与标题通过模板可以还原，不在表内持久化
--  - 取消使用 status 维护，无需单独取消标记列

SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS `email_messages`;
SET FOREIGN_KEY_CHECKS = 1;

CREATE TABLE `email_messages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `email_id` bigint NOT NULL COMMENT '业务侧请求ID/去重ID（唯一）',
  `tenant_id` bigint NOT NULL COMMENT '租户ID',
  `internal_app_id` bigint NOT NULL COMMENT '内部应用id',
  `template_id` bigint DEFAULT NULL COMMENT '模板ID（外部/邮件模块）',
  `account_id` bigint NOT NULL DEFAULT '0' COMMENT '冗余的发件账号ID',
  `from_address` text NOT NULL COMMENT '发件人地址',
  `to_addresses` text NOT NULL COMMENT '收件人地址，逗号分隔',
  `cc_addresses` text DEFAULT NULL COMMENT '抄送地址，逗号分隔',
  `bcc_addresses` text DEFAULT NULL COMMENT '密送地址，逗号分隔',
  `variables` json DEFAULT NULL COMMENT '模板变量快照（渲染所需）',
  `status` varchar(20) NOT NULL COMMENT '状态：queued|running|sent|delivered|bounced|failed|canceled|expired 等',
  `priority` bigint DEFAULT '2' COMMENT '优先级，数值越大越高',
  `retry_count` bigint DEFAULT '0' COMMENT '已重试次数',
  `last_retry_at` datetime(3) DEFAULT NULL COMMENT '上次重试时间',
  `max_retries` bigint DEFAULT '3' COMMENT '最大重试次数',
  `retry_policy` json DEFAULT NULL COMMENT '重试策略：{mode,max_attempts,initial_backoff_ms,max_backoff_ms,retry_on}',
  `scheduled_at` datetime(3) DEFAULT NULL COMMENT '计划发送时间',
  `sent_at` datetime(3) DEFAULT NULL COMMENT '实际发送时间',
  `no_later_than` datetime(3) DEFAULT NULL COMMENT '截止时间（超过不再发送）',
  `batch_id` bigint DEFAULT NULL COMMENT '发送批次ID',
  `idempotency_key` varchar(128) DEFAULT NULL COMMENT '幂等键（可选）',
  `canceled_at` datetime(3) DEFAULT NULL COMMENT '取消时间（取消由status维护）',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_email_messages_email_id` (`email_id`),
  KEY `idx_email_messages_tenant_id` (`tenant_id`),
  KEY `idx_email_messages_status` (`status`),
  KEY `idx_email_messages_deleted_at` (`deleted_at`),
  KEY `idx_tenant_status_created` (`tenant_id`,`status`,`created_at`),
  KEY `idx_tenant_sent_at` (`tenant_id`,`sent_at`)
) ENGINE=InnoDB AUTO_INCREMENT=5100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='发送内容与标题通过模板可还原，不落库';

-- 备注：发送内容与标题通过模板可以还原，不在表内持久化


