-- Add sort_order column to email_providers for manual ordering
ALTER TABLE `email_providers`
  ADD COLUMN `sort_order` INT NOT NULL DEFAULT 0 AFTER `enabled`;

-- Optional: initialize some sensible default orders (lower = higher priority)
UPDATE `email_providers`
SET `sort_order` = CASE LOWER(`name`)
  WHEN 'aliyun'  THEN 10
  WHEN 'gmail'   THEN 20
  WHEN 'outlook' THEN 30
  WHEN 'qq'      THEN 40
  WHEN '163'     THEN 50
  WHEN '126'     THEN 60
  WHEN 'smtp'    THEN 90
  ELSE 80
END;

