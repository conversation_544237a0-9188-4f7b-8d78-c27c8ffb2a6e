-- ==============================================
-- 插入账户激活邮件模板
-- 模板代码: account_activation
-- 类型: 系统级模板 (is_system = 1, tenant_id = 0)
-- 创建时间: 2025-01-27
-- ==============================================

-- 插入激活邮件模板
INSERT INTO `email_templates` (
    `tenant_id`, 
    `template_code`, 
    `account_id`, 
    `name`, 
    `type`, 
    `subject`, 
    `html_content`, 
    `plain_text_content`, 
    `variables`, 
    `lan`,
    `rate_limit_per_minute`,
    `rate_limit_per_hour`,
    `rate_limit_per_day`,
    `thumbnail_url`, 
    `is_responsive`, 
    `is_active`, 
    `created_at`, 
    `updated_at`, 
    `created_by`, 
    `updated_by`,
    `version`, 
    `is_system`
) VALUES (
    0, 
    'account_activation', 
    0, 
    '账户激活邮件模板', 
    1, 
    '【{{appName}}】激活您的账户',
    '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账户激活</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 300;
        }
        .content {
            padding: 40px 30px;
        }
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #2c3e50;
        }
        .main-text {
            font-size: 16px;
            margin-bottom: 25px;
            line-height: 1.8;
        }
        .activation-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0;
            text-align: center;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }
        .activation-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        .info-box {
            background-color: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 25px 0;
            border-radius: 4px;
        }
        .info-box h3 {
            margin: 0 0 15px 0;
            color: #667eea;
            font-size: 16px;
        }
        .info-item {
            margin: 8px 0;
            font-size: 14px;
        }
        .info-label {
            font-weight: bold;
            color: #555;
            display: inline-block;
            width: 100px;
        }
        .warning-message {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        .footer-text {
            font-size: 12px;
            color: #6c757d;
            line-height: 1.5;
        }
        .divider {
            border-top: 1px solid #e9ecef;
            margin: 20px 0;
        }
        @media only screen and (max-width: 600px) {
            .container {
                margin: 0;
                box-shadow: none;
            }
            .header, .content, .footer {
                padding: 20px;
            }
            .activation-button {
                display: block;
                margin: 20px auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 欢迎加入 {{appName}}</h1>
        </div>
        
        <div class="content">
            <div class="greeting">尊敬的用户，您好！</div>
            
            <div class="main-text">
                感谢您注册 {{appName}}！为了确保您的账户安全，请点击下面的按钮激活您的账户。
            </div>
            
            <div style="text-align: center;">
                <a href="{{link}}" class="activation-button">
                    🔐 立即激活账户
                </a>
            </div>
            
            <div class="warning-message">
                <strong>⚠️ 重要提醒</strong><br>
                此激活链接将在 <strong>{{expireTime}}</strong> 后失效，请及时激活您的账户。
            </div>
            
            <div class="info-box">
                <h3>📋 激活说明</h3>
                <div class="info-item">
                    <span class="info-label">应用名称:</span>
                    <span>{{appName}}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">激活链接:</span>
                    <span style="word-break: break-all; color: #667eea;">{{link}}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">过期时间:</span>
                    <span>{{expireTime}}</span>
                </div>
            </div>
            
            <div class="main-text">
                如果按钮无法点击，请复制上面的链接到浏览器地址栏中访问。<br>
                如果您没有注册 {{appName}}，请忽略此邮件。
            </div>
        </div>
        
        <div class="footer">
            <div class="footer-text">
                <p>本邮件由 {{appName}} 系统自动发送，请勿回复。</p>
                <p>感谢您对我们服务的信任与支持！</p>
                <div class="divider"></div>
                <p style="font-size: 11px; color: #999;">
                    发送时间: {{current_time}} | 应用: {{appName}}
                </p>
            </div>
        </div>
    </div>
</body>
</html>',
    '尊敬的用户，您好！

感谢您注册 {{appName}}！为了确保您的账户安全，请点击下面的链接激活您的账户。

激活链接: {{link}}

⚠️ 重要提醒
此激活链接将在 {{expireTime}} 后失效，请及时激活您的账户。

📋 激活说明
应用名称: {{appName}}
激活链接: {{link}}
过期时间: {{expireTime}}

如果链接无法点击，请复制上面的链接到浏览器地址栏中访问。
如果您没有注册 {{appName}}，请忽略此邮件。

---
本邮件由 {{appName}} 系统自动发送，请勿回复。
感谢您对我们服务的信任与支持！

发送时间: {{current_time}} | 应用: {{appName}}',
    '{
        "link": {
            "type": "string",
            "required": true,
            "description": "账户激活链接"
        },
        "appName": {
            "type": "string",
            "required": true,
            "description": "应用名称"
        },
        "expireTime": {
            "type": "string",
            "required": true,
            "description": "激活链接过期时间"
        },
        "current_time": {
            "type": "string",
            "required": true,
            "description": "当前发送时间"
        }
    }',
    'zh-CN',
    60,
    1000,
    10000,
    NULL,
    1,
    1,
    NOW(),
    NOW(),
    NULL,
    NULL,
    1,
    1
);

-- 验证插入结果
SELECT 
    id,
    template_code,
    name,
    is_system,
    is_active,
    created_at
FROM email_templates 
WHERE template_code = 'account_activation' 
AND is_system = 1; 