syntax = "proto3";

package email;

option go_package = "gitee.com/heiyee/platforms/email/api/emailpb";

// 邮件服务
service EmailService {
    // 发送模板邮件
    rpc SendTemplateEmail(SendTemplateEmailRequest) returns (SendTemplateEmailResponse);
    
    // 检查模板是否存在
    rpc CheckTemplateExists(CheckTemplateExistsRequest) returns (CheckTemplateExistsResponse);
    
    // 处理模板（包含追踪功能）
    rpc ProcessTemplate(ProcessTemplateRequest) returns (ProcessTemplateResponse);
    
    // 获取追踪配置
    rpc GetTrackingConfig(GetTrackingConfigRequest) returns (GetTrackingConfigResponse);
    
    // 验证并解密目标地址
    rpc VerifyAndDecryptDestination(VerifyDestinationRequest) returns (VerifyDestinationResponse);
}

// 发送模板邮件请求
message SendTemplateEmailRequest {
    // 租户ID
    int64 tenant_id = 1;
    // 内部应用ID
    int64 internal_app_id = 2;
    // 模板代码
    string template_code = 3;
    // 收件人邮箱列表
    repeated string to = 4;
    // 模板变量
    map<string, string> variables = 5;
    // 请求ID（可选，用于日志追踪）
    string request_id = 6;
}

// 发送模板邮件响应
message SendTemplateEmailResponse {
    // 状态码
    int32 code = 1;
    // 消息
    string message = 2;
    // 邮件发送结果数据
    EmailSendResult data = 3;
}

// 邮件发送结果
message EmailSendResult {
    // 消息ID
    string message_id = 1;
    // 发送状态
    string status = 2;
    // 发送时间
    int64 send_time = 3;
}

// 检查模板是否存在请求
message CheckTemplateExistsRequest {
    // 租户ID
    int64 tenant_id = 1;
    // 内部应用ID
    int64 internal_app_id = 2;
    // 模板代码
    string template_code = 3;
}

// 检查模板是否存在响应
message CheckTemplateExistsResponse {
    // 状态码
    int32 code = 1;
    // 消息
    string message = 2;
    // 模板存在结果数据
    TemplateExistsResult data = 3;
}

// 模板存在结果
message TemplateExistsResult {
    // 是否存在
    bool exists = 1;
    // 模板信息（如果存在）
    TemplateInfo template_info = 2;
}

// 模板信息
message TemplateInfo {
    // 模板ID
    int64 id = 1;
    // 模板代码
    string template_code = 2;
    // 模板名称
    string name = 3;
    // 模板类型
    string type = 4;
    // 是否启用
    bool is_enabled = 5;
    // 是否启用追踪
    bool tracking_enabled = 6;
    // 追踪选项
    TrackingOptions tracking_options = 7;
    // 创建时间
    int64 created_at = 8;
    // 更新时间
    int64 updated_at = 9;
}

// 处理模板请求
message ProcessTemplateRequest {
    // 租户ID
    int64 tenant_id = 1;
    // 内部应用ID
    int64 internal_app_id = 2;
    // 模板ID
    int64 template_id = 3;
    // 活动ID
    string campaign_id = 4;
    // 订阅者ID
    string subscriber_id = 5;
    // 消息ID
    string message_id = 6;
    // 模板变量
    map<string, string> variables = 7;
    // 是否启用追踪
    bool tracking_enabled = 8;
    // 追踪选项
    TrackingOptions tracking_options = 9;
}

// 处理模板响应
message ProcessTemplateResponse {
    // 状态码
    int32 code = 1;
    // 消息
    string message = 2;
    // 处理结果
    ProcessedTemplate data = 3;
}

// 处理后的模板
message ProcessedTemplate {
    // HTML内容
    string html_content = 1;
    // 文本内容
    string text_content = 2;
    // 主题
    string subject = 3;
    // 追踪链接列表
    repeated TrackingLink tracking_links = 4;
}

// 追踪链接
message TrackingLink {
    // 链接ID
    string link_id = 1;
    // 原始URL
    string original_url = 2;
    // 追踪URL
    string tracking_url = 3;
}

// 追踪选项
message TrackingOptions {
    // 是否启用
    bool enabled = 1;
    // UTM选项
    UTMOptions utm = 2;
    // 转化窗口（小时）
    int32 conversion_window_hours = 3;
    // 自定义追踪域名
    string custom_tracking_domain = 4;
    // 链接域名白名单
    repeated string link_domain_whitelist = 5;
}

// UTM选项
message UTMOptions {
    // 是否启用
    bool enabled = 1;
    // 默认值
    map<string, string> defaults = 2;
}

// 获取追踪配置请求
message GetTrackingConfigRequest {
    // 租户ID
    int64 tenant_id = 1;
    // 内部应用ID
    int64 internal_app_id = 2;
}

// 获取追踪配置响应
message GetTrackingConfigResponse {
    // 状态码
    int32 code = 1;
    // 消息
    string message = 2;
    // 追踪配置
    TrackingConfig data = 3;
}

// 追踪配置
message TrackingConfig {
    // 配置ID
    int64 id = 1;
    // 租户ID
    int64 tenant_id = 2;
    // 追踪域名
    string tracking_domain = 3;
    // 是否启用UTM
    bool utm_enabled = 4;
    // UTM默认值
    map<string, string> utm_defaults = 5;
    // 转化窗口（小时）
    int32 conversion_window_hours = 6;
    // 链接域名白名单
    repeated string link_domain_whitelist = 7;
    // 是否活跃
    bool is_active = 8;
}

// 验证目标地址请求
message VerifyDestinationRequest {
    // 租户ID
    int64 tenant_id = 1;
    // 加密的目标地址
    string encrypted_dest = 2;
    // 签名
    string signature = 3;
}

// 验证目标地址响应
message VerifyDestinationResponse {
    // 状态码
    int32 code = 1;
    // 消息
    string message = 2;
    // 解密后的URL
    string destination_url = 3;
    // 是否允许访问
    bool is_allowed = 4;
}