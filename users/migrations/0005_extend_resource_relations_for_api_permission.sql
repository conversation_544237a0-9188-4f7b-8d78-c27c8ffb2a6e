-- 扩展 resource_relations 表以支持 HTTP 接口权限拦截
-- Migration: 0005_extend_resource_relations_for_api_permission.sql

-- 新增权限差异化配置字段
ALTER TABLE `resource_relations` 
ADD COLUMN `permission_code` varchar(100) DEFAULT NULL COMMENT '权限编码，用于同一API在不同页面的差异化权限',
ADD COLUMN `is_required` tinyint(1) DEFAULT '1' COMMENT '是否必需权限',
ADD COLUMN `inherit_parent` tinyint(1) DEFAULT '1' COMMENT '是否继承父级权限',
ADD COLUMN `priority` int DEFAULT '0' COMMENT '权限优先级，数字越大优先级越高',
ADD COLUMN `status` varchar(20) DEFAULT 'active' COMMENT '状态：active-启用，inactive-禁用';

-- 新增索引以优化查询性能
CREATE INDEX `idx_resource_relations_permission` ON `resource_relations`(`permission_code`);
CREATE INDEX `idx_resource_relations_status` ON `resource_relations`(`status`);
CREATE INDEX `idx_resource_relations_api_lookup` ON `resource_relations`(`target_resource_id`, `tenant_id`, `internal_app_id`, `status`);
CREATE INDEX `idx_resource_relations_page_lookup` ON `resource_relations`(`source_resource_id`, `tenant_id`, `internal_app_id`, `status`);

-- 组合索引用于复杂查询
CREATE INDEX `idx_resource_relations_permission_lookup` ON `resource_relations`(`tenant_id`, `internal_app_id`, `source_resource_id`, `target_resource_id`, `status`);