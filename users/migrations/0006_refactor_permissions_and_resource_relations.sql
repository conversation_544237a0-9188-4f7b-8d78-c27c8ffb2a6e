-- Migration: 0006_refactor_permissions_and_resource_relations.sql
-- Description: 重构permissions和resource_relations表结构
-- 1. 在resource_relations表增加permission_id字段
-- 2. 从permissions表去掉resource_id和resource_type字段
-- Date: 2024-12-19

-- =======================================================================================
-- Step 1: 备份现有数据
-- =======================================================================================

-- 备份permissions表的resource_id和resource_type数据
CREATE TABLE IF NOT EXISTS permissions_backup_resource_fields AS
SELECT 
    id, 
    tenant_id, 
    internal_app_id,
    name,
    code,
    resource_type,
    resource_id
FROM permissions 
WHERE resource_type IS NOT NULL OR resource_id IS NOT NULL;

-- =======================================================================================
-- Step 2: 修改resource_relations表 - 增加permission_id字段
-- =======================================================================================

-- 添加permission_id字段到resource_relations表
ALTER TABLE resource_relations 
ADD COLUMN permission_id BIGINT DEFAULT NULL COMMENT '权限ID，关联permissions表';

-- 添加索引
ALTER TABLE resource_relations 
ADD INDEX idx_resource_relations_permission_id (permission_id);

-- 添加外键约束
ALTER TABLE resource_relations 
ADD CONSTRAINT fk_resource_relations_permission 
    FOREIGN KEY (permission_id) REFERENCES permissions (id) 
    ON DELETE SET NULL 
    ON UPDATE CASCADE;

-- =======================================================================================
-- Step 3: 数据迁移 - 将权限关联关系迁移到resource_relations表
-- =======================================================================================

-- 为现有的资源关联关系创建对应的权限，并关联到resource_relations
-- 这里假设每个resource_relation都需要一个对应的权限

-- 首先为没有permission_id的resource_relations记录创建权限
INSERT INTO permissions (
    tenant_id,
    internal_app_id,
    name,
    code,
    display_name,
    description,
    action,
    scope,
    status,
    is_system,
    created_at,
    updated_at
)
SELECT 
    rr.tenant_id,
    rr.internal_app_id,
    CONCAT('resource_relation_', rr.id) as name,
    CONCAT('resource_relation_', rr.id) as code,
    CONCAT('资源关联权限_', rr.id) as display_name,
    CONCAT('资源关联关系权限: ', rr.description) as description,
    'access' as action,
    'self' as scope,
    'active' as status,
    0 as is_system,
    NOW() as created_at,
    NOW() as updated_at
FROM resource_relations rr
WHERE rr.permission_id IS NULL;

-- 更新resource_relations表，关联刚创建的权限
UPDATE resource_relations rr
JOIN permissions p ON p.code = CONCAT('resource_relation_', rr.id)
SET rr.permission_id = p.id
WHERE rr.permission_id IS NULL;

-- =======================================================================================
-- Step 4: 修改permissions表 - 移除resource_id和resource_type字段
-- =======================================================================================

-- 移除resource_type相关的索引
DROP INDEX IF EXISTS idx_permission_resource ON permissions;

-- 移除resource_id和resource_type字段
ALTER TABLE permissions DROP COLUMN resource_type;
ALTER TABLE permissions DROP COLUMN resource_id;

-- =======================================================================================
-- Step 5: 更新索引和约束
-- =======================================================================================

-- 为permissions表的action字段添加索引（如果还没有的话）
ALTER TABLE permissions 
ADD INDEX idx_permission_action (action);

-- =======================================================================================
-- Step 6: 验证数据完整性
-- =======================================================================================

-- 检查是否有resource_relations记录没有关联权限
SELECT 
    COUNT(*) as unlinked_relations,
    'resource_relations without permission_id' as description
FROM resource_relations 
WHERE permission_id IS NULL;

-- 检查权限表中是否还有resource_id或resource_type字段的残留
-- (这个查询应该失败，因为字段已被删除)
-- SELECT COUNT(*) FROM permissions WHERE resource_id IS NOT NULL OR resource_type IS NOT NULL;

-- =======================================================================================
-- Step 7: 清理和优化
-- =======================================================================================

-- 分析表以优化查询性能
ANALYZE TABLE permissions;
ANALYZE TABLE resource_relations;

-- =======================================================================================
-- 回滚脚本（如需要）
-- =======================================================================================

/*
-- 回滚步骤（谨慎使用）：

-- 1. 重新添加resource_id和resource_type字段到permissions表
ALTER TABLE permissions 
ADD COLUMN resource_type VARCHAR(50) DEFAULT NULL COMMENT '资源类型：user, role, permission, tenant等',
ADD COLUMN resource_id BIGINT DEFAULT NULL COMMENT '资源ID';

-- 2. 从备份恢复数据
UPDATE permissions p
JOIN permissions_backup_resource_fields b ON p.id = b.id
SET 
    p.resource_type = b.resource_type,
    p.resource_id = b.resource_id;

-- 3. 移除resource_relations表的permission_id字段
ALTER TABLE resource_relations DROP FOREIGN KEY fk_resource_relations_permission;
ALTER TABLE resource_relations DROP INDEX idx_resource_relations_permission_id;
ALTER TABLE resource_relations DROP COLUMN permission_id;

-- 4. 重新添加resource_type索引
ALTER TABLE permissions ADD INDEX idx_permission_resource (resource_type);

-- 5. 删除迁移过程中创建的权限记录
DELETE FROM permissions WHERE code LIKE 'resource_relation_%';

-- 6. 删除备份表
DROP TABLE permissions_backup_resource_fields;
*/

-- =======================================================================================
-- 完成迁移
-- =======================================================================================

-- 记录迁移完成
INSERT INTO migration_history (version, description, executed_at) 
VALUES ('0006', 'Refactor permissions and resource_relations table structure', NOW())
ON DUPLICATE KEY UPDATE executed_at = NOW();
