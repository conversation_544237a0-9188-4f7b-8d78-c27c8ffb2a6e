package main

import (
	"context"
	"fmt"
	"log"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
)

// MockIDGenerator Mock ID生成器
type MockIDGenerator struct {
	userID       int64
	tenantID     int64
	roleID       int64
	permissionID int64
	departmentID int64
	positionID   int64
	resourceID   int64
}

// GenerateUserID 生成用户ID
func (m *MockIDGenerator) GenerateUserID(ctx context.Context) (int64, error) {
	m.userID++
	return m.userID, nil
}

// GenerateTenantID 生成租户ID
func (m *MockIDGenerator) GenerateTenantID(ctx context.Context) (int64, error) {
	m.tenantID++
	return m.tenantID, nil
}

// GenerateRoleID 生成角色ID
func (m *MockIDGenerator) GenerateRoleID(ctx context.Context) (int64, error) {
	m.roleID++
	return m.roleID, nil
}

// GeneratePermissionID 生成权限ID
func (m *MockIDGenerator) GeneratePermissionID(ctx context.Context) (int64, error) {
	m.permissionID++
	return m.permissionID, nil
}

// GenerateDepartmentID 生成部门ID
func (m *MockIDGenerator) GenerateDepartmentID(ctx context.Context) (int64, error) {
	m.departmentID++
	return m.departmentID, nil
}

// GeneratePositionID 生成职位ID
func (m *MockIDGenerator) GeneratePositionID(ctx context.Context) (int64, error) {
	m.positionID++
	return m.positionID, nil
}

// GenerateResourceID 生成资源ID
func (m *MockIDGenerator) GenerateResourceID(ctx context.Context) (int64, error) {
	m.resourceID++
	return m.resourceID, nil
}

// GenerateID 生成通用ID
func (m *MockIDGenerator) GenerateID(ctx context.Context, businessType string) (int64, error) {
	// 根据业务类型返回不同的ID
	switch businessType {
	case "user":
		return m.GenerateUserID(ctx)
	case "tenant":
		return m.GenerateTenantID(ctx)
	case "role":
		return m.GenerateRoleID(ctx)
	case "permission":
		return m.GeneratePermissionID(ctx)
	case "department":
		return m.GenerateDepartmentID(ctx)
	case "position":
		return m.GeneratePositionID(ctx)
	case "resource":
		return m.GenerateResourceID(ctx)
	default:
		return 0, fmt.Errorf("unsupported business type: %s", businessType)
	}
}

// GenerateSessionID 生成会话ID
func (m *MockIDGenerator) GenerateSessionID(ctx context.Context) (string, error) {
	return fmt.Sprintf("sess_%d", m.userID), nil
}

// GenerateRequestID 生成请求ID
func (m *MockIDGenerator) GenerateRequestID(ctx context.Context) (string, error) {
	return fmt.Sprintf("req_%d", m.userID), nil
}

// MockLogger Mock日志实现
type MockLogger struct{}

func (l *MockLogger) Debug(ctx context.Context, msg string, fields ...logiface.Field) {}
func (l *MockLogger) Info(ctx context.Context, msg string, fields ...logiface.Field)  {}
func (l *MockLogger) Warn(ctx context.Context, msg string, fields ...logiface.Field)  {}
func (l *MockLogger) Error(ctx context.Context, msg string, fields ...logiface.Field) {}

// UserService 用户服务示例
type UserService struct {
	entityFactory *entity.EntityFactory
	// userRepo      repository.UserRepository // 实际项目中会有仓储
}

// NewUserService 创建用户服务
func NewUserService(entityFactory *entity.EntityFactory) *UserService {
	return &UserService{
		entityFactory: entityFactory,
	}
}

// CreateUser 创建用户
func (s *UserService) CreateUser(ctx context.Context, req *CreateUserRequest) (*entity.User, error) {
	// 使用实体工厂创建用户
	user, err := s.entityFactory.NewUser(
		ctx,
		req.TenantID,
		req.Username,
		req.Email,
		req.RealName,
		req.Password,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// TODO: 保存到数据库
	// return user, s.userRepo.Create(ctx, user)
	return user, nil
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	TenantID int64
	Username string
	Email    string
	RealName string
	Password value_object.Password
}

// TenantService 租户服务示例
type TenantService struct {
	entityFactory *entity.EntityFactory
	// tenantRepo    repository.TenantRepository
}

// NewTenantService 创建租户服务
func NewTenantService(entityFactory *entity.EntityFactory) *TenantService {
	return &TenantService{
		entityFactory: entityFactory,
	}
}

// CreateTenant 创建租户
func (s *TenantService) CreateTenant(ctx context.Context, tenantCode, tenantName string) (*entity.Tenant, error) {
	// 使用实体工厂创建租户
	tenant, err := s.entityFactory.NewTenant(ctx, tenantCode, tenantName)
	if err != nil {
		return nil, fmt.Errorf("failed to create tenant: %w", err)
	}

	// TODO: 保存到数据库
	// return tenant, s.tenantRepo.Create(ctx, tenant)
	return tenant, nil
}

// RoleService 角色服务示例
type RoleService struct {
	entityFactory *entity.EntityFactory
	// roleRepo      repository.RoleRepository
}

// NewRoleService 创建角色服务
func NewRoleService(entityFactory *entity.EntityFactory) *RoleService {
	return &RoleService{
		entityFactory: entityFactory,
	}
}

// CreateRole 创建角色
func (s *RoleService) CreateRole(ctx context.Context, tenantID int64, name, code, displayName, description string) (*entity.Role, error) {
	// 使用实体工厂创建角色
	role, err := s.entityFactory.NewRole(ctx, tenantID, name, code, displayName, description)
	if err != nil {
		return nil, fmt.Errorf("failed to create role: %w", err)
	}

	// TODO: 保存到数据库
	// return role, s.roleRepo.Create(ctx, role)
	return role, nil
}

// runExample 运行示例
func runExample() {
	ctx := context.Background()

	// 1. 创建Mock ID生成器
	mockIDGenerator := &MockIDGenerator{}

	// 2. 创建实体工厂
	entityFactory := entity.NewEntityFactory(mockIDGenerator)

	// 3. 创建应用服务
	userService := NewUserService(entityFactory)
	tenantService := NewTenantService(entityFactory)
	roleService := NewRoleService(entityFactory)

	// 4. 使用服务创建实体
	fmt.Println("=== 最佳实践使用示例 ===")

	// 创建租户
	tenant, err := tenantService.CreateTenant(ctx, "demo", "演示租户")
	if err != nil {
		log.Fatalf("Failed to create tenant: %v", err)
	}
	fmt.Printf("创建租户成功: ID=%d, Code=%s, Name=%s\n", tenant.ID, tenant.TenantCode, tenant.TenantName)

	// 创建用户
	password, err := value_object.NewPassword("password123")
	if err != nil {
		log.Fatalf("Failed to create password: %v", err)
	}
	user, err := userService.CreateUser(ctx, &CreateUserRequest{
		TenantID: tenant.ID,
		Username: "admin",
		Email:    "<EMAIL>",
		RealName: "管理员",
		Password: *password,
	})
	if err != nil {
		log.Fatalf("Failed to create user: %v", err)
	}
	fmt.Printf("创建用户成功: ID=%d, Username=%s, Email=%s\n", user.ID, user.Username, user.Email)

	// 创建角色
	role, err := roleService.CreateRole(ctx, tenant.ID, "admin", "ADMIN", "管理员", "系统管理员角色")
	if err != nil {
		log.Fatalf("Failed to create role: %v", err)
	}
	fmt.Printf("创建角色成功: ID=%d, Name=%s, Code=%s\n", role.ID, role.Name, role.Code)

	// 创建部门
	department, err := entityFactory.NewDepartment(ctx, tenant.ID, "技术部", "TECH", "技术研发部门")
	if err != nil {
		log.Fatalf("Failed to create department: %v", err)
	}
	fmt.Printf("创建部门成功: ID=%d, Name=%s, Code=%s\n", department.ID, department.Name, department.Code)

	// 创建职位
	position, err := entityFactory.NewPosition(ctx, tenant.ID, "软件工程师", "SE", "负责软件开发")
	if err != nil {
		log.Fatalf("Failed to create position: %v", err)
	}
	fmt.Printf("创建职位成功: ID=%d, Name=%s, Code=%s\n", position.ID, position.Name, position.Code)

	// 创建权限
	permission, err := entityFactory.NewPermission(ctx, tenant.ID, "用户管理", "USER_MANAGE", "用户管理权限", "用户管理权限", "manage", "tenant")
	if err != nil {
		log.Fatalf("Failed to create permission: %v", err)
	}
	fmt.Printf("创建权限成功: ID=%d, Name=%s, Code=%s\n", permission.ID, permission.Name, permission.Code)

	fmt.Println("\n=== 所有实体创建成功 ===")
	fmt.Printf("租户ID: %d\n", tenant.ID)
	fmt.Printf("用户ID: %d\n", user.ID)
	fmt.Printf("角色ID: %d\n", role.ID)
	fmt.Printf("部门ID: %d\n", department.ID)
	fmt.Printf("职位ID: %d\n", position.ID)
	fmt.Printf("权限ID: %d\n", permission.ID)

	// 验证ID唯一性
	ids := map[int64]string{
		tenant.ID:     "租户",
		user.ID:       "用户",
		role.ID:       "角色",
		department.ID: "部门",
		position.ID:   "职位",
		permission.ID: "权限",
	}

	fmt.Println("\n=== ID唯一性验证 ===")
	uniqueIDs := make(map[int64]string)
	for id, entityType := range ids {
		if existing, exists := uniqueIDs[id]; exists {
			fmt.Printf("❌ ID冲突: %d 被 %s 和 %s 使用\n", id, existing, entityType)
		} else {
			uniqueIDs[id] = entityType
			fmt.Printf("✅ ID %d 分配给 %s\n", id, entityType)
		}
	}

	fmt.Println("\n=== 最佳实践优势总结 ===")
	fmt.Println("1. ✅ 统一构造函数: 所有实体都通过EntityFactory创建")
	fmt.Println("2. ✅ 依赖注入: 通过构造函数注入ID生成器")
	fmt.Println("3. ✅ 错误处理: 明确的错误返回，不隐藏错误")
	fmt.Println("4. ✅ 可测试性: 可以轻松Mock所有依赖")
	fmt.Println("5. ✅ 职责分离: 实体工厂只负责实体创建，ID生成器只负责ID生成")
	fmt.Println("6. ✅ 类型安全: 编译时检查，避免运行时错误")
	fmt.Println("7. ✅ 易于扩展: 支持新的实体类型和ID生成策略")
}
