#!/bin/bash

# 测试资源树API with internal_app_id 参数的完整功能
# 包括权限控制逻辑

echo "=== 测试资源树API with internal_app_id 参数和权限控制 ==="

# 设置基础变量
BASE_URL="http://localhost:8083"
CONTENT_TYPE="Content-Type: application/json"

# 测试用例1: 不带 internal_app_id 参数
echo "1. 测试不带 internal_app_id 参数（使用 usercontext 兜底）"
curl -s -X POST "${BASE_URL}/api/user/resource/tree" \
  -H "${CONTENT_TYPE}" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "include_universal": true,
    "max_depth": 3
  }' | jq '.'

echo -e "\n"

# 测试用例2: 带 internal_app_id 参数
echo "2. 测试带 internal_app_id 参数"
curl -s -X POST "${BASE_URL}/api/user/resource/tree" \
  -H "${CONTENT_TYPE}" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "include_universal": true,
    "max_depth": 3,
    "internal_app_id": 1
  }' | jq '.'

echo -e "\n"

# 测试用例3: 测试权限控制 - 普通用户访问无权限的应用
echo "3. 测试权限控制 - 普通用户访问无权限的应用"
curl -s -X POST "${BASE_URL}/api/user/resource/tree" \
  -H "${CONTENT_TYPE}" \
  -H "Authorization: Bearer REGULAR_USER_TOKEN_HERE" \
  -d '{
    "include_universal": true,
    "max_depth": 3,
    "internal_app_id": 999
  }' | jq '.'

echo -e "\n"

# 测试用例4: 测试超级管理员权限
echo "4. 测试超级管理员权限（应该忽略权限检查）"
curl -s -X POST "${BASE_URL}/api/user/resource/tree" \
  -H "${CONTENT_TYPE}" \
  -H "Authorization: Bearer SUPER_ADMIN_TOKEN_HERE" \
  -d '{
    "include_universal": true,
    "max_depth": 3,
    "internal_app_id": 999
  }' | jq '.'

echo -e "\n"

# 测试用例5: 测试资源类型过滤
echo "5. 测试资源类型过滤 + internal_app_id"
curl -s -X POST "${BASE_URL}/api/user/resource/tree" \
  -H "${CONTENT_TYPE}" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "include_universal": true,
    "max_depth": 3,
    "internal_app_id": 1,
    "resource_type": "page"
  }' | jq '.'

echo -e "\n=== 测试完成 ==="