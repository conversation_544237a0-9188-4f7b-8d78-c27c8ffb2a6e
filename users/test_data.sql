-- 插入测试资源数据
-- 这个脚本用于测试资源管理功能
-- 注意：已移除用户应用绑定功能，现在通过租户选择来控制应用访问

-- 插入一些测试资源数据（如果不存在）
INSERT INTO resources (tenant_id, internal_app_id, name, display_name, resource_type, parent_id, path, sort_order, is_system, is_universal, status, assignable, created_at, updated_at)
VALUES 
(1, 1, 'dashboard', '仪表板', 'page', 0, '/dashboard', 1, 0, 0, 'active', 1, NOW(), NOW()),
(1, 1, 'users', '用户管理', 'page', 0, '/users', 2, 0, 0, 'active', 1, NOW(), NOW()),
(1, 2, 'prompts', '提示词管理', 'page', 0, '/prompts', 1, 0, 0, 'active', 1, NOW(), NOW()),
(1, 2, 'templates', '模板管理', 'page', 0, '/templates', 2, 0, 0, 'active', 1, NOW(), NOW())
ON DUPLICATE KEY UPDATE updated_at = NOW();

-- 显示插入的数据
SELECT 'Resources by App:' as info;
SELECT internal_app_id, name, display_name, resource_type, path FROM resources ORDER BY internal_app_id, sort_order;