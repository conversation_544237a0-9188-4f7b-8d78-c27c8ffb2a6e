package config

import "time"

// API前缀配置
const (
	// 全局API前缀
	GlobalAPIPrefix = "/api/user"

	// 各模块API前缀
	AuthAPIPrefix         = GlobalAPIPrefix + "/auth"
	UserAPIPrefix         = GlobalAPIPrefix // 用户模块直接使用全局前缀
	RoleAPIPrefix         = GlobalAPIPrefix + "/role"
	PermissionAPIPrefix   = GlobalAPIPrefix + "/permission"
	DepartmentAPIPrefix   = GlobalAPIPrefix + "/department"
	PositionAPIPrefix     = GlobalAPIPrefix + "/position"
	CaptchaAPIPrefix      = GlobalAPIPrefix + "/captcha"
	VerificationAPIPrefix = GlobalAPIPrefix + "/verification"

	// 特殊路由前缀
	MeAPIPrefix     = GlobalAPIPrefix + "/me"
	PublicAPIPrefix = GlobalAPIPrefix + "/public"
	HealthAPIPrefix = GlobalAPIPrefix + "/health"
)

// 服务配置常量
const (
	DefaultServerPort = 8084
	DefaultDBHost     = "localhost"
	DefaultDBPort     = 3306
	DefaultDBName     = "platforms-user"
	DefaultDBUser     = "root"
	DefaultDBPassword = "password"
)

// JWT配置常量
const (
	DefaultJWTSecret       = "test-jwt-secret-key-2024"
	DefaultAccessTokenTTL  = "24h"
	DefaultRefreshTokenTTL = "168h"
	DefaultJWTIssuer       = "platforms-user"
	DefaultJWTAudience     = "platforms-user-client"
)

// Redis配置常量
const (
	DefaultRedisHost     = "localhost"
	DefaultRedisPort     = 6379
	DefaultRedisPassword = ""
	DefaultRedisDB       = 0
	DefaultRedisPoolSize = 10
)

// 日志配置常量
const (
	DefaultLogLevel      = "info"
	DefaultLogFormat     = "json"
	DefaultLogOutput     = "stdout"
	DefaultLogMaxSize    = 100
	DefaultLogMaxBackups = 3
	DefaultLogMaxAge     = 28
	DefaultLogCompress   = true
)

// 分页配置常量
const (
	DefaultPageSize = 20
	MaxPageSize     = 100
	MinPageSize     = 1
)

// 验证配置常量
const (
	MinPasswordLength = 6
	MaxPasswordLength = 50
	MinUsernameLength = 3
	MaxUsernameLength = 50
)

// 状态常量
const (
	StatusActive   = "active"
	StatusDisabled = "disabled"
	StatusDeleted  = "deleted"
)

// ID生成器配置常量
const (
	DefaultIncrementStep = 1    // 默认步长
	DefaultCacheSize     = 1000 // 默认缓存大小
	DefaultThreshold     = 20   // 默认预分配阈值
	DefaultMaxValue      = 100  // 默认最大值
	DefaultMinValue      = 1    // 默认最小值
	UnlimitedMaxValue    = 0    // 表示不限制最大值

	// ID生成器服务配置
	MaxRetries       = 3                      // 最大重试次数
	RetryInterval    = 100 * time.Millisecond // 重试间隔
	PreloadThreshold = 0.8                    // 预加载阈值
)

// 权限常量 - 已迁移到 internal/domain/user/constants/permission_constants.go
// 请使用 userconstants.PermissionRead, userconstants.PermissionWrite 等
