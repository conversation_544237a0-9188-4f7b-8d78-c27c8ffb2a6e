package validator

import (
	"fmt"
	commonResponse "gitee.com/heiyee/platforms/pkg/common/response"
	"regexp"
	"strings"
)

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

// ValidationErrors 验证错误集合
type ValidationErrors []ValidationError

// Error 实现error接口
func (v ValidationErrors) Error() string {
	if len(v) == 0 {
		return ""
	}

	var messages []string
	for _, err := range v {
		messages = append(messages, fmt.Sprintf("%s: %s", err.Field, err.Message))
	}
	return strings.Join(messages, "; ")
}

// Add 添加验证错误
func (v *ValidationErrors) Add(field, message string) {
	*v = append(*v, ValidationError{
		Field:   field,
		Message: message,
	})
}

// HasErrors 检查是否有错误
func (v ValidationErrors) HasErrors() bool {
	return len(v) > 0
}

// Validator 验证器
type Validator struct {
	errors ValidationErrors
}

// NewValidator 创建验证器
func NewValidator() *Validator {
	return &Validator{
		errors: make(ValidationErrors, 0),
	}
}

// Required 必填验证
func (v *Validator) Required(value, field string) *Validator {
	if strings.TrimSpace(value) == "" {
		v.errors.Add(field, "此字段为必填项")
	}
	return v
}

// RequiredInt 必填整数验证
func (v *Validator) RequiredInt(value int64, field string) *Validator {
	if value == 0 {
		v.errors.Add(field, "此字段为必填项")
	}
	return v
}

// RequiredArray 必填数组验证
func (v *Validator) RequiredArray(value []int64, field string) *Validator {
	if value == nil || len(value) == 0 {
		v.errors.Add(field, "此字段为必填项")
	}
	return v
}

// RequiredStringArray 必填字符串数组验证
func (v *Validator) RequiredStringArray(value []string, field string) *Validator {
	if value == nil || len(value) == 0 {
		v.errors.Add(field, "此字段为必填项")
	}
	return v
}

// Email 邮箱验证
func (v *Validator) Email(value, field string) *Validator {
	if value == "" {
		return v
	}

	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(value) {
		v.errors.Add(field, "请输入有效的邮箱地址")
	}
	return v
}

// Phone 手机号验证
func (v *Validator) Phone(value, field string) *Validator {
	if value == "" {
		return v
	}

	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	if !phoneRegex.MatchString(value) {
		v.errors.Add(field, "请输入有效的手机号码")
	}
	return v
}

// Username 用户名验证
func (v *Validator) Username(value, field string) *Validator {
	if value == "" {
		return v
	}

	// 用户名规则：3-20位字母、数字、下划线
	usernameRegex := regexp.MustCompile(`^[a-zA-Z0-9_]{3,20}$`)
	if !usernameRegex.MatchString(value) {
		v.errors.Add(field, "用户名必须是3-20位字母、数字或下划线")
	}
	return v
}

// PasswordWithPolicy 使用策略验证密码
func (v *Validator) PasswordWithPolicy(value, field string, validator interface {
	ValidatePassword(string, interface{}) error
}, policy interface{}) *Validator {
	if value == "" {
		return v
	}

	if err := validator.ValidatePassword(value, policy); err != nil {
		v.errors.Add(field, err.Error())
	}

	return v
}

// MinLength 最小长度验证
func (v *Validator) MinLength(value, field string, min int) *Validator {
	if value == "" {
		return v
	}

	if len(value) < min {
		v.errors.Add(field, fmt.Sprintf("字段长度至少%d个字符", min))
	}
	return v
}

// MaxLength 最大长度验证
func (v *Validator) MaxLength(value, field string, max int) *Validator {
	if value == "" {
		return v
	}

	if len(value) > max {
		v.errors.Add(field, fmt.Sprintf("字段长度不能超过%d个字符", max))
	}
	return v
}

// Length 长度范围验证
func (v *Validator) Length(value, field string, min, max int) *Validator {
	if value == "" {
		return v
	}

	if len(value) < min || len(value) > max {
		v.errors.Add(field, fmt.Sprintf("字段长度必须在%d-%d个字符之间", min, max))
	}
	return v
}

// Min 最小值验证
func (v *Validator) Min(value int64, field string, min int64) *Validator {
	if value < min {
		v.errors.Add(field, fmt.Sprintf("数值不能小于%d", min))
	}
	return v
}

// Max 最大值验证
func (v *Validator) Max(value int64, field string, max int64) *Validator {
	if value > max {
		v.errors.Add(field, fmt.Sprintf("数值不能大于%d", max))
	}
	return v
}

// Range 范围验证
func (v *Validator) Range(value int64, field string, min, max int64) *Validator {
	if value < min || value > max {
		v.errors.Add(field, fmt.Sprintf("数值必须在%d-%d之间", min, max))
	}
	return v
}

// Positive 正数验证
func (v *Validator) Positive(value int64, field string) *Validator {
	if value <= 0 {
		v.errors.Add(field, "数值必须为正数")
	}
	return v
}

// NonNegative 非负数验证
func (v *Validator) NonNegative(value int64, field string) *Validator {
	if value < 0 {
		v.errors.Add(field, "数值不能为负数")
	}
	return v
}

// In 枚举值验证
func (v *Validator) In(value, field string, allowedValues ...string) *Validator {
	if value == "" {
		return v
	}

	for _, allowed := range allowedValues {
		if value == allowed {
			return v
		}
	}

	v.errors.Add(field, fmt.Sprintf("字段值必须是以下之一: %s", strings.Join(allowedValues, ", ")))
	return v
}

// URL URL验证
func (v *Validator) URL(value, field string) *Validator {
	if value == "" {
		return v
	}

	urlRegex := regexp.MustCompile(`^https?://[^\s/$.?#].[^\s]*$`)
	if !urlRegex.MatchString(value) {
		v.errors.Add(field, "请输入有效的URL地址")
	}
	return v
}

// Alphanumeric 字母数字验证
func (v *Validator) Alphanumeric(value, field string) *Validator {
	if value == "" {
		return v
	}

	alphanumericRegex := regexp.MustCompile(`^[a-zA-Z0-9]+$`)
	if !alphanumericRegex.MatchString(value) {
		v.errors.Add(field, "字段只能包含字母和数字")
	}
	return v
}

// Chinese 中文字符验证
func (v *Validator) Chinese(value, field string) *Validator {
	if value == "" {
		return v
	}

	chineseRegex := regexp.MustCompile(`^[\p{Han}]+$`)
	if !chineseRegex.MatchString(value) {
		v.errors.Add(field, "字段只能包含中文字符")
	}
	return v
}

// ResourceType 资源类型验证
func (v *Validator) ResourceType(value, field string) *Validator {
	if value == "" {
		return v
	}

	allowedTypes := []string{"menu", "page", "api", "button", "component"}
	for _, allowed := range allowedTypes {
		if value == allowed {
			return v
		}
	}

	v.errors.Add(field, "资源类型必须是以下之一: menu, page, api, button, component")
	return v
}

// ResourceName 资源名称验证
func (v *Validator) ResourceName(value, field string) *Validator {
	if value == "" {
		return v
	}

	// 资源名称规则：3-50位字母、数字、下划线、中划线、中文
	nameRegex := regexp.MustCompile(`^[a-zA-Z0-9_\-\p{Han}]{3,50}$`)
	if !nameRegex.MatchString(value) {
		v.errors.Add(field, "资源名称必须是3-50位字母、数字、下划线、中划线或中文")
	}
	return v
}

// Path 路径验证 - 必须以/开头
func (v *Validator) Path(value, field string) *Validator {
	if value == "" {
		return v
	}

	if !strings.HasPrefix(value, "/") {
		v.errors.Add(field, "路径必须以 / 开头")
	}
	return v
}

// Custom 自定义验证
func (v *Validator) Custom(value, field string, validator func(string) bool, message string) *Validator {
	if value == "" {
		return v
	}

	if !validator(value) {
		v.errors.Add(field, message)
	}
	return v
}

// Errors 获取验证错误
func (v *Validator) Errors() ValidationErrors {
	return v.errors
}

// HasErrors 检查是否有错误
func (v *Validator) HasErrors() bool {
	return v.errors.HasErrors()
}

// AddError 添加自定义错误
func (v *Validator) AddError(field, message string) *Validator {
	v.errors.Add(field, message)
	return v
}

// Validate 执行验证并返回错误
func (v *Validator) Validate() error {
	if v.HasErrors() {
		return v.errors
	}
	return nil
}

// 便捷验证函数

// ValidateEmail 验证邮箱
func ValidateEmail(email string) error {
	v := NewValidator()
	v.Email(email, "email")
	return v.Validate()
}

// ValidatePhone 验证手机号
func ValidatePhone(phone string) error {
	v := NewValidator()
	v.Phone(phone, "phone")
	return v.Validate()
}

// ValidateUsername 验证用户名
func ValidateUsername(username string) error {
	v := NewValidator()
	v.Required(username, "username")
	v.Username(username, "username")
	return v.Validate()
}

// ValidateRequired 验证必填字段
func ValidateRequired(value, field string) error {
	v := NewValidator()
	v.Required(value, field)
	return v.Validate()
}

// ToResponseDetails 转换为 []commonResponse.ValidationError
func (v ValidationErrors) ToResponseDetails() []commonResponse.ValidationError {
	var details []commonResponse.ValidationError
	for _, err := range v {
		details = append(details, commonResponse.ValidationError{
			Field:   err.Field,
			Message: err.Message,
			Value:   "",
		})
	}
	return details
}
