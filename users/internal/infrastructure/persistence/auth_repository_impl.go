package persistence

import (
	"context"
	"time"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/domain/auth/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/auth/repository"

	"gorm.io/gorm"
)

// AuthRepositoryImpl 认证仓储实现
type AuthRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewAuthRepository 创建认证仓储
func NewAuthRepository(db *gorm.DB, logger logiface.Logger) repository.AuthRepository {
	return &AuthRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// CreateSession 创建会话
func (r *AuthRepositoryImpl) CreateSession(ctx context.Context, session *entity.AuthSession) error {
	err := r.db.WithContext(ctx).Create(session).Error
	if err != nil {
		r.logger.Error(ctx, "failed to create auth session",
			logiface.Error(err),
			logiface.Int64("user_id", session.UserID),
			logiface.String("session_id", session.ID),
			logiface.String("jti", session.JTI),
		)
	}
	return err
}

// GetSession 获取会话（向后兼容）
func (r *AuthRepositoryImpl) GetSession(ctx context.Context, sessionID string) (*entity.AuthSession, error) {
	var session entity.AuthSession
	err := r.db.WithContext(ctx).
		Where("id = ? AND status = 'active' AND expires_at > ?", sessionID, time.Now()).
		First(&session).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &session, nil
}

// UpdateSession 更新会话
func (r *AuthRepositoryImpl) UpdateSession(ctx context.Context, session *entity.AuthSession) error {
	err := r.db.WithContext(ctx).Save(session).Error
	if err != nil {
		r.logger.Error(ctx, "failed to update auth session",
			logiface.Error(err),
			logiface.String("session_id", session.ID),
			logiface.Int64("user_id", session.UserID),
		)
	}
	return err
}

// RevokeSession 撤销会话（向后兼容）
func (r *AuthRepositoryImpl) RevokeSession(ctx context.Context, sessionID string) error {
	err := r.db.WithContext(ctx).
		Model(&entity.AuthSession{}).
		Where("id = ?", sessionID).
		Update("status", "revoked").Error
	if err != nil {
		r.logger.Error(ctx, "failed to revoke session",
			logiface.Error(err),
			logiface.String("session_id", sessionID),
		)
	}
	return err
}

// RevokeUserSessions 撤销用户所有会话
func (r *AuthRepositoryImpl) RevokeUserSessions(ctx context.Context, userID int64) error {
	err := r.db.WithContext(ctx).
		Model(&entity.AuthSession{}).
		Where("user_id = ? AND status = 'active'", userID).
		Update("status", "revoked").Error
	if err != nil {
		r.logger.Error(ctx, "failed to revoke user sessions",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
		)
	}
	return err
}

// GetUserSessions 获取用户活跃会话
func (r *AuthRepositoryImpl) GetUserSessions(ctx context.Context, userID int64) ([]entity.AuthSession, error) {
	var sessions []entity.AuthSession
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND status = 'active' AND expires_at > ?", userID, time.Now()).
		Order("created_at DESC").
		Find(&sessions).Error
	return sessions, err
}

// CreateLoginAttempt 创建登录尝试记录
func (r *AuthRepositoryImpl) CreateLoginAttempt(ctx context.Context, attempt *entity.LoginAttempt) error {
	err := r.db.WithContext(ctx).Create(attempt).Error
	if err != nil {
		r.logger.Error(ctx, "failed to create login attempt",
			logiface.Error(err),
			logiface.String("username", attempt.Username),
			logiface.String("ip_address", attempt.IPAddress),
			logiface.String("status", string(attempt.Status)),
		)
	}
	return err
}

// GetLoginAttempts 获取登录尝试记录
func (r *AuthRepositoryImpl) GetLoginAttempts(ctx context.Context, identifier string, since time.Time) ([]entity.LoginAttempt, error) {
	var attempts []entity.LoginAttempt
	err := r.db.WithContext(ctx).
		Where("identifier = ? AND created_at >= ?", identifier, since).
		Order("created_at DESC").
		Find(&attempts).Error
	return attempts, err
}

// UpdateLoginAttempt 更新登录尝试状态
func (r *AuthRepositoryImpl) UpdateLoginAttempt(ctx context.Context, attempt *entity.LoginAttempt) error {
	err := r.db.WithContext(ctx).Save(attempt).Error
	if err != nil {
		r.logger.Error(ctx, "failed to update login attempt",
			logiface.Error(err),
			logiface.Int64("attempt_id", attempt.ID),
			logiface.String("username", attempt.Username),
			logiface.String("status", string(attempt.Status)),
		)
	}
	return err
}

// DeleteSession 删除会话
func (r *AuthRepositoryImpl) DeleteSession(ctx context.Context, sessionID string) error {
	err := r.db.WithContext(ctx).Where("id = ?", sessionID).Delete(&entity.AuthSession{}).Error
	if err != nil {
		r.logger.Error(ctx, "failed to delete session",
			logiface.Error(err),
			logiface.String("session_id", sessionID),
		)
	}
	return err
}

// FindSessionByID 根据会话ID查找会话
func (r *AuthRepositoryImpl) FindSessionByID(ctx context.Context, sessionID string) (*entity.AuthSession, error) {
	return r.GetSession(ctx, sessionID)
}

// FindSessionByJTI 根据JTI查找会话
func (r *AuthRepositoryImpl) FindSessionByJTI(ctx context.Context, jti string) (*entity.AuthSession, error) {
	var session entity.AuthSession
	err := r.db.WithContext(ctx).
		Where("jti = ? AND status = 'active' AND expires_at > ?", jti, time.Now()).
		First(&session).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &session, nil
}

// FindSessionByAccessTokenHash 根据访问令牌哈希查找会话
func (r *AuthRepositoryImpl) FindSessionByAccessTokenHash(ctx context.Context, accessTokenHash string) (*entity.AuthSession, error) {
	var session entity.AuthSession
	err := r.db.WithContext(ctx).
		Where("access_token_hash = ? AND status = 'active' AND expires_at > ?", accessTokenHash, time.Now()).
		First(&session).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &session, nil
}

// FindSessionByRefreshTokenHash 根据刷新令牌哈希查找会话
func (r *AuthRepositoryImpl) FindSessionByRefreshTokenHash(ctx context.Context, refreshTokenHash string) (*entity.AuthSession, error) {
	var session entity.AuthSession
	err := r.db.WithContext(ctx).
		Where("refresh_token_hash = ? AND status = 'active' AND expires_at > ?", refreshTokenHash, time.Now()).
		First(&session).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &session, nil
}

// FindSessionsByUserID 根据用户ID查找会话列表
func (r *AuthRepositoryImpl) FindSessionsByUserID(ctx context.Context, userID int64, offset, limit int) ([]*entity.AuthSession, int64, error) {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[entity.AuthSession](r.db, ctx, entity.AuthSession{}, r.logger).
		WithCondition("user_id", "=", userID).
		WithOrder("created_at", "DESC")

	// 添加分页
	if limit > 0 {
		queryBuilder = queryBuilder.WithPagination(offset/limit+1, limit)
	}

	// 执行查询
	models, err := queryBuilder.Find()
	if err != nil {
		return nil, 0, err
	}

	// 获取总数
	countBuilder := NewQueryBuilder[entity.AuthSession](r.db, ctx, entity.AuthSession{}, r.logger).
		WithCondition("user_id", "=", userID)
	total, err := countBuilder.Count()
	if err != nil {
		return nil, 0, err
	}

	// 转换为指针切片
	sessions := make([]*entity.AuthSession, len(models))
	for i := range models {
		sessions[i] = &models[i]
	}

	return sessions, total, nil
}

// FindActiveSessionsByUserID 根据用户ID查找活跃会话列表
func (r *AuthRepositoryImpl) FindActiveSessionsByUserID(ctx context.Context, userID int64) ([]*entity.AuthSession, error) {
	sessions, err := r.GetUserSessions(ctx, userID)
	if err != nil {
		return nil, err
	}

	sessionPtrs := make([]*entity.AuthSession, len(sessions))
	for i := range sessions {
		sessionPtrs[i] = &sessions[i]
	}

	return sessionPtrs, nil
}

// RevokeAllSessionsByUserID 撤销用户所有会话
func (r *AuthRepositoryImpl) RevokeAllSessionsByUserID(ctx context.Context, userID int64) error {
	return r.RevokeUserSessions(ctx, userID)
}

// RevokeSessionsByUserIDExcept 撤销用户除指定会话外的所有会话
func (r *AuthRepositoryImpl) RevokeSessionsByUserIDExcept(ctx context.Context, userID int64, exceptSessionID string) error {
	// 使用 UpdateBuilder 模式
	return NewUpdateBuilder[entity.AuthSession](r.db, ctx, entity.AuthSession{}).
		Set("status", "revoked").
		Where("user_id", "=", userID).
		Where("id", "!=", exceptSessionID).
		Where("status", "=", "active").
		Update()
}

// RevokeSessionByJTI 根据JTI撤销会话
func (r *AuthRepositoryImpl) RevokeSessionByJTI(ctx context.Context, jti string) error {
	// 使用 UpdateBuilder 模式
	return NewUpdateBuilder[entity.AuthSession](r.db, ctx, entity.AuthSession{}).
		Set("status", "revoked").
		Where("jti", "=", jti).
		Update()
}

// CleanExpiredSessions 清理过期会话
func (r *AuthRepositoryImpl) CleanExpiredSessions(ctx context.Context, before time.Time) error {
	// 使用 UpdateBuilder 模式
	return NewUpdateBuilder[entity.AuthSession](r.db, ctx, entity.AuthSession{}).
		Set("status", "expired").
		Where("expires_at", "<=", before).
		Where("status", "=", "active").
		Update()
}

// FindLoginAttemptsByUsername 根据用户名查找登录尝试
func (r *AuthRepositoryImpl) FindLoginAttemptsByUsername(ctx context.Context, tenantID int64, username string, since time.Time) ([]*entity.LoginAttempt, error) {
	// 使用 query builder 模式
	qb := NewQueryBuilder[entity.LoginAttempt](r.db, ctx, entity.LoginAttempt{}, r.logger).
		ValidateTenantID(tenantID).
		ValidateStringParam(username, "username").
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("username", "=", username).
		WithCondition("created_at", ">=", since).
		WithOrder("created_at", "DESC")

	attempts, err := qb.Find()
	if err != nil {
		return nil, err
	}

	// 转换为指针切片
	attemptPtrs := make([]*entity.LoginAttempt, len(attempts))
	for i := range attempts {
		attemptPtrs[i] = &attempts[i]
	}

	return attemptPtrs, nil
}

// FindLoginAttemptsByIP 根据IP地址查找登录尝试
func (r *AuthRepositoryImpl) FindLoginAttemptsByIP(ctx context.Context, tenantID int64, ipAddress string, since time.Time) ([]*entity.LoginAttempt, error) {
	// 使用 query builder 模式
	qb := NewQueryBuilder[entity.LoginAttempt](r.db, ctx, entity.LoginAttempt{}, r.logger).
		ValidateTenantID(tenantID).
		ValidateStringParam(ipAddress, "ip_address").
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("ip_address", "=", ipAddress).
		WithCondition("created_at", ">=", since).
		WithOrder("created_at", "DESC")

	attempts, err := qb.Find()
	if err != nil {
		return nil, err
	}

	// 转换为指针切片
	attemptPtrs := make([]*entity.LoginAttempt, len(attempts))
	for i := range attempts {
		attemptPtrs[i] = &attempts[i]
	}

	return attemptPtrs, nil
}

// CountFailedAttemptsByUsername 统计用户名失败次数
func (r *AuthRepositoryImpl) CountFailedAttemptsByUsername(ctx context.Context, tenantID int64, username string, since time.Time) (int64, error) {
	// 使用 query builder 模式
	qb := NewQueryBuilder[entity.LoginAttempt](r.db, ctx, entity.LoginAttempt{}, r.logger).
		ValidateTenantID(tenantID).
		ValidateStringParam(username, "username").
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("username", "=", username).
		WithCondition("status", "=", "failed").
		WithCondition("created_at", ">=", since)

	return qb.Count()
}

// CountFailedAttemptsByIP 统计IP地址失败次数
func (r *AuthRepositoryImpl) CountFailedAttemptsByIP(ctx context.Context, tenantID int64, ipAddress string, since time.Time) (int64, error) {
	// 使用 query builder 模式
	qb := NewQueryBuilder[entity.LoginAttempt](r.db, ctx, entity.LoginAttempt{}, r.logger).
		ValidateTenantID(tenantID).
		ValidateStringParam(ipAddress, "ip_address").
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("ip_address", "=", ipAddress).
		WithCondition("status", "=", "failed").
		WithCondition("created_at", ">=", since)

	return qb.Count()
}

// CleanOldLoginAttempts 清理旧的登录尝试记录
func (r *AuthRepositoryImpl) CleanOldLoginAttempts(ctx context.Context, before time.Time) error {
	// 使用 query builder 模式
	qb := NewQueryBuilder[entity.LoginAttempt](r.db, ctx, entity.LoginAttempt{}, r.logger).
		WithCondition("created_at", "<", before)

	// 构建删除查询
	query := qb.buildQuery()
	err := query.Delete(&entity.LoginAttempt{}).Error
	if err != nil {
		r.logger.Error(ctx, "failed to clean old login attempts",
			logiface.Error(err),
			logiface.Time("before", before),
		)
	}
	return err
}

// BlockIP 封禁IP地址
func (r *AuthRepositoryImpl) BlockIP(ctx context.Context, tenantID int64, ipAddress string, reason string, duration time.Duration) error {
	blockedUntil := time.Now().Add(duration)
	err := r.db.WithContext(ctx).
		Table("blocked_ips").
		Create(map[string]interface{}{
			"tenant_id":     tenantID,
			"ip_address":    ipAddress,
			"reason":        reason,
			"blocked_until": blockedUntil,
			"created_at":    time.Now(),
		}).Error
	if err != nil {
		r.logger.Error(ctx, "failed to block IP address",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip_address", ipAddress),
			logiface.String("reason", reason),
			logiface.Duration("duration", duration),
		)
	}
	return err
}

// IsIPBlocked 检查IP是否被封禁
func (r *AuthRepositoryImpl) IsIPBlocked(ctx context.Context, tenantID int64, ipAddress string) (bool, error) {
	// 使用 query builder 模式，但直接查询表
	qb := NewQueryBuilder[map[string]interface{}](r.db, ctx, map[string]interface{}{}, r.logger).
		ValidateTenantID(tenantID).
		ValidateStringParam(ipAddress, "ip_address").
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("ip_address", "=", ipAddress).
		WithCondition("blocked_until", ">", time.Now())

	// 构建查询并指定表名
	query := qb.buildQuery().Table("blocked_ips")

	var count int64
	err := query.Count(&count).Error
	return count > 0, err
}

// UnblockIP 解除IP封禁
func (r *AuthRepositoryImpl) UnblockIP(ctx context.Context, tenantID int64, ipAddress string) error {
	// 使用 UpdateBuilder 模式，但直接操作表
	return NewUpdateBuilder[map[string]interface{}](r.db, ctx, map[string]interface{}{}).
		Where("tenant_id", "=", tenantID).
		Where("ip_address", "=", ipAddress).
		Set("status", "unblocked").
		Set("unblocked_at", time.Now()).
		Update()
}

// GetSessionStats 获取会话统计
func (r *AuthRepositoryImpl) GetSessionStats(ctx context.Context, tenantID int64) (*repository.SessionStats, error) {
	stats := &repository.SessionStats{}

	// 总会话数
	qb1 := NewQueryBuilder[entity.AuthSession](r.db, ctx, entity.AuthSession{}, r.logger).
		ValidateTenantID(tenantID).
		WithCondition("tenant_id", "=", tenantID)
	stats.TotalSessions, _ = qb1.Count()

	// 活跃会话数
	qb2 := NewQueryBuilder[entity.AuthSession](r.db, ctx, entity.AuthSession{}, r.logger).
		ValidateTenantID(tenantID).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("status", "=", "active").
		WithCondition("expires_at", ">", time.Now())
	stats.ActiveSessions, _ = qb2.Count()

	// 过期会话数
	qb3 := NewQueryBuilder[entity.AuthSession](r.db, ctx, entity.AuthSession{}, r.logger).
		ValidateTenantID(tenantID).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("status", "=", "expired")
	stats.ExpiredSessions, _ = qb3.Count()

	// 撤销会话数
	qb4 := NewQueryBuilder[entity.AuthSession](r.db, ctx, entity.AuthSession{}, r.logger).
		ValidateTenantID(tenantID).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("status", "=", "revoked")
	stats.RevokedSessions, _ = qb4.Count()

	// 登出会话数
	qb5 := NewQueryBuilder[entity.AuthSession](r.db, ctx, entity.AuthSession{}, r.logger).
		ValidateTenantID(tenantID).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("status", "=", "logged_out")
	stats.LoggedOutSessions, _ = qb5.Count()

	return stats, nil
}

// GetLoginAttemptStats 获取登录尝试统计
func (r *AuthRepositoryImpl) GetLoginAttemptStats(ctx context.Context, tenantID int64) (*repository.LoginAttemptStats, error) {
	stats := &repository.LoginAttemptStats{}

	// 总尝试次数
	qb1 := NewQueryBuilder[entity.LoginAttempt](r.db, ctx, entity.LoginAttempt{}, r.logger).
		ValidateTenantID(tenantID).
		WithCondition("tenant_id", "=", tenantID)
	stats.TotalAttempts, _ = qb1.Count()

	// 成功尝试次数
	qb2 := NewQueryBuilder[entity.LoginAttempt](r.db, ctx, entity.LoginAttempt{}, r.logger).
		ValidateTenantID(tenantID).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("status", "=", "success")
	stats.SuccessfulAttempts, _ = qb2.Count()

	// 失败尝试次数
	qb3 := NewQueryBuilder[entity.LoginAttempt](r.db, ctx, entity.LoginAttempt{}, r.logger).
		ValidateTenantID(tenantID).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("status", "=", "failed")
	stats.FailedAttempts, _ = qb3.Count()

	// 被封禁的IP数量
	qb4 := NewQueryBuilder[map[string]interface{}](r.db, ctx, map[string]interface{}{}, r.logger).
		ValidateTenantID(tenantID).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("blocked_until", ">", time.Now())
	query := qb4.buildQuery().Table("blocked_ips")
	query.Count(&stats.BlockedIPs)

	return stats, nil
}
