package model

import (
	"context"
	"time"

	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// TenantAware 租户感知接口
type TenantAware interface {
	GetTenantID() int64
	SetTenantID(tenantID int64)
}

// AppAware 应用感知接口
type AppAware interface {
	GetInternalAppID() int64
	SetInternalAppID(appID int64)
}

// ParameterModeAware 参数模式感知接口，用于标记是否允许使用参数传递的租户和应用ID（特殊场景）
type ParameterModeAware interface {
	IsAllowParameterMode() bool
	SetAllowParameterMode(allow bool)
}

// BaseModel 基础模型结构体，包含通用字段
type BaseModel struct {
	TenantID           int64     `gorm:"not null;index" json:"tenant_id"`
	InternalAppID      int64     `gorm:"not null;index;comment:应用ID，bigint类型提升性能" json:"internal_app_id"`
	AllowParameterMode bool      `gorm:"-" json:"-"` // 不持久化，用于标记是否允许参数传递（默认false，严格使用context）
	CreatedAt          time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt          time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// SoftDeleteModel 软删除模型结构体，包含DeletedAt字段
type SoftDeleteModel struct {
	BaseModel
	DeletedAt *time.Time `gorm:"index" json:"deleted_at"`
}

// GetTenantID 获取租户ID
func (b *BaseModel) GetTenantID() int64 {
	return b.TenantID
}

// SetTenantID 设置租户ID
func (b *BaseModel) SetTenantID(tenantID int64) {
	b.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (b *BaseModel) GetInternalAppID() int64 {
	return b.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (b *BaseModel) SetInternalAppID(appID int64) {
	b.InternalAppID = appID
}

// IsAllowParameterMode 是否允许参数模式（默认false，严格使用context）
func (b *BaseModel) IsAllowParameterMode() bool {
	return b.AllowParameterMode
}

// SetAllowParameterMode 设置参数模式
func (b *BaseModel) SetAllowParameterMode(allow bool) {
	b.AllowParameterMode = allow
}

// TenantAwareModel 租户感知模型接口
type TenantAwareModel interface {
	TenantAware
	BeforeCreate(tx *gorm.DB) error
	BeforeUpdate(tx *gorm.DB) error
	BeforeQuery(tx *gorm.DB) error
}

// AppAwareModel 应用感知模型接口
type AppAwareModel interface {
	AppAware
	BeforeCreate(tx *gorm.DB) error
	BeforeUpdate(tx *gorm.DB) error
	BeforeQuery(tx *gorm.DB) error
}

// FullAwareModel 完整感知模型接口（同时支持租户和应用）
type FullAwareModel interface {
	TenantAware
	AppAware
	ParameterModeAware
	BeforeCreate(tx *gorm.DB) error
	BeforeUpdate(tx *gorm.DB) error
	BeforeQuery(tx *gorm.DB) error
}

// GetTenantIDFromContext 从上游context获取租户ID
func GetTenantIDFromContext(ctx context.Context) int64 {
	if ctx == nil {
		return 0
	}
	tenantID, _ := usercontext.GetTenantID(ctx)
	return tenantID
}

// GetInternalAppIDFromContext 从上游context获取内部应用ID
func GetInternalAppIDFromContext(ctx context.Context) int64 {
	if ctx == nil {
		return 0
	}
	internalAppID, _ := usercontext.GetInternalAppID(ctx)
	return internalAppID
}

// AutoFillTenantAndApp 自动填充租户和应用信息的钩子函数
// 默认采用严格模式：强制使用context中的值，只有明确标记AllowParameterMode的才允许参数传递
func AutoFillTenantAndApp(tx *gorm.DB) {
	ctx := tx.Statement.Context
	if ctx == nil {
		return
	}

	// 获取当前模型
	model := tx.Statement.Model
	if model == nil {
		return
	}

	// 从上游context获取租户ID和应用ID
	tenantID := GetTenantIDFromContext(ctx)
	internalAppID := GetInternalAppIDFromContext(ctx)

	// 处理租户ID
	if tenantAware, ok := model.(TenantAware); ok {
		currentTenantID := tenantAware.GetTenantID()

		if parameterAware, isParameterAware := model.(ParameterModeAware); isParameterAware && parameterAware.IsAllowParameterMode() {
			// 参数模式：允许使用参数传递的值，只在值为0时才从context填充
			if currentTenantID == 0 && tenantID > 0 {
				tenantAware.SetTenantID(tenantID)
			}
		} else {
			// 严格模式（默认）：强制使用context中的值，忽略参数传递的值
			tenantAware.SetTenantID(tenantID)
		}
	}

	// 处理应用ID
	if appAware, ok := model.(AppAware); ok {
		currentInternalAppID := appAware.GetInternalAppID()

		if parameterAware, isParameterAware := model.(ParameterModeAware); isParameterAware && parameterAware.IsAllowParameterMode() {
			// 参数模式：允许使用参数传递的值，只在值为0时才从context填充
			if currentInternalAppID == 0 && internalAppID > 0 {
				appAware.SetInternalAppID(internalAppID)
			}
		} else {
			// 严格模式（默认）：强制使用context中的值，忽略参数传递的值
			appAware.SetInternalAppID(internalAppID)
		}
	}
}

// AutoFilterTenantAndApp 自动过滤租户和应用信息的钩子函数
func AutoFilterTenantAndApp(tx *gorm.DB) {
	ctx := tx.Statement.Context
	if ctx == nil {
		return
	}

	// 从上游context获取租户ID和应用ID
	tenantID := GetTenantIDFromContext(ctx)
	internalAppID := GetInternalAppIDFromContext(ctx)

	// 添加租户过滤条件
	if tenantID > 0 {
		tx.Statement.AddClause(clause.Where{Exprs: []clause.Expression{
			clause.Eq{Column: "tenant_id", Value: tenantID},
		}})
	}

	// 添加应用过滤条件
	if internalAppID > 0 {
		tx.Statement.AddClause(clause.Where{Exprs: []clause.Expression{
			clause.Eq{Column: "internal_app_id", Value: internalAppID},
		}})
	}
}

// BeforeCreateHook 创建前的钩子函数
func BeforeCreateHook(tx *gorm.DB) {
	AutoFillTenantAndApp(tx)
}

// BeforeUpdateHook 更新前的钩子函数
func BeforeUpdateHook(tx *gorm.DB) {
	AutoFillTenantAndApp(tx)
}

// BeforeQueryHook 查询前的钩子函数
func BeforeQueryHook(tx *gorm.DB) {
	AutoFilterTenantAndApp(tx)
}

// RegisterHooks 注册GORM钩子
func RegisterHooks(db *gorm.DB) {
	// 注册全局钩子
	db.Callback().Create().Before("gorm:create").Register("auto_fill_tenant_app", BeforeCreateHook)
	db.Callback().Update().Before("gorm:update").Register("auto_fill_tenant_app", BeforeUpdateHook)
	db.Callback().Query().Before("gorm:query").Register("auto_filter_tenant_app", BeforeQueryHook)
}

// IsTenantAware 检查模型是否支持租户感知
func IsTenantAware(model interface{}) bool {
	_, ok := model.(TenantAware)
	return ok
}

// IsAppAware 检查模型是否支持应用感知
func IsAppAware(model interface{}) bool {
	_, ok := model.(AppAware)
	return ok
}

// IsFullAware 检查模型是否同时支持租户和应用感知
func IsFullAware(model interface{}) bool {
	_, ok := model.(FullAwareModel)
	return ok
}

// GetModelAwareness 获取模型的感知类型
func GetModelAwareness(model interface{}) string {
	if IsFullAware(model) {
		return "full"
	} else if IsTenantAware(model) && IsAppAware(model) {
		return "both"
	} else if IsTenantAware(model) {
		return "tenant"
	} else if IsAppAware(model) {
		return "app"
	}
	return "none"
}

// NewModelWithParameterMode 创建启用参数模式的模型实例（特殊场景使用）
func NewModelWithParameterMode[T interface {
	ParameterModeAware
	*BaseModel
}]() T {
	var model T = new(BaseModel)
	model.SetAllowParameterMode(true)
	return model
}

// AllowParameterMode 为已有模型启用参数模式（特殊场景使用）
func AllowParameterMode(model ParameterModeAware) {
	model.SetAllowParameterMode(true)
}

// DisallowParameterMode 为已有模型禁用参数模式（恢复严格模式）
func DisallowParameterMode(model ParameterModeAware) {
	model.SetAllowParameterMode(false)
}
