package model

import (
	"time"

	"gorm.io/gorm"
)

// ResourceAppAssignmentModel 资源应用关联数据模型
type ResourceAppAssignmentModel struct {
	ID            int64      `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID      int64      `gorm:"not null;index" json:"tenant_id"`
	ResourceID    int64      `gorm:"not null" json:"resource_id"`
	InternalAppID int64      `gorm:"not null" json:"internal_app_id"`
	IsActive      bool       `gorm:"not null;default:true" json:"is_active"`
	AssignedBy    *int64     `gorm:"default:null" json:"assigned_by"`
	AssignedAt    time.Time  `gorm:"not null;autoCreateTime" json:"assigned_at"`
	ExpiresAt     *time.Time `gorm:"default:null" json:"expires_at"`
	CreatedAt     time.Time  `gorm:"not null;autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time  `gorm:"not null;autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (ResourceAppAssignmentModel) TableName() string {
	return "resource_app_assignments"
}

// GetTenantID 获取租户ID
func (r *ResourceAppAssignmentModel) GetTenantID() int64 {
	return r.TenantID
}

// SetTenantID 设置租户ID
func (r *ResourceAppAssignmentModel) SetTenantID(tenantID int64) {
	r.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (r *ResourceAppAssignmentModel) GetInternalAppID() int64 {
	return r.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (r *ResourceAppAssignmentModel) SetInternalAppID(appID int64) {
	r.InternalAppID = appID
}

// IsAllowParameterMode 允许参数模式，使用参数传递的tenant_id和internal_app_id
func (r *ResourceAppAssignmentModel) IsAllowParameterMode() bool {
	return true
}

// BeforeCreate GORM创建前钩子
func (r *ResourceAppAssignmentModel) BeforeCreate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (r *ResourceAppAssignmentModel) BeforeUpdate(tx *gorm.DB) error {
	AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (r *ResourceAppAssignmentModel) BeforeQuery(tx *gorm.DB) error {
	AutoFilterTenantAndApp(tx)
	return nil
}
