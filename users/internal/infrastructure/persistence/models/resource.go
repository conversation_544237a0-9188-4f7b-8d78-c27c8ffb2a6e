package models

import (
	"time"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/model"
	"gorm.io/gorm"
)

// ResourceModel 资源数据模型 - 仅用于数据库操作
type ResourceModel struct {
	ID            int64      `gorm:"primaryKey"`
	TenantID      int64      `gorm:"not null;index"`
	InternalAppID int64      `gorm:"not null;index;comment:应用ID，bigint类型提升性能"`
	Name          string     `gorm:"not null;size:100"`
	DisplayName   string     `gorm:"size:100"`
	Description   string     `gorm:"size:255"`
	ResourceType  string     `gorm:"not null;size:50"`
	ServiceName   string     `gorm:"size:100"` // 应用服务名称
	RequestType   string     `gorm:"size:50"`  // 请求数据类型: json, form, file, text, stream, xml, binary
	ResponseType  string     `gorm:"size:50"`  // 响应数据类型: json, html, xml, stream, file, text, binary
	APIMethod     string     `gorm:"size:10"`  // HTTP方法: GET, POST, PUT, DELETE, PATCH
	ContentType   string     `gorm:"size:100"` // Content-Type: application/json, multipart/form-data等
	ParentID      *int64     `gorm:"index"`
	Path          string     `gorm:"size:255"`
	Icon          string     `gorm:"size:100"`
	SortOrder     int        `gorm:"default:0"`
	IsSystem      bool       `gorm:"default:false"`
	DeletedAt     *time.Time `gorm:"index"` // 新增：删除时间
	CreatedAt     time.Time  `gorm:"autoCreateTime"`
	UpdatedAt     time.Time  `gorm:"autoUpdateTime"`
	IsPublic      bool       `gorm:"default:false"`
	PublicLevel   string     `gorm:"type:varchar(20);default:'none'"`
	Assignable    bool       `gorm:"default:true"`

	// 参数模式控制（不持久化）
	allowParameterMode bool
}

// TableName 指定表名
func (ResourceModel) TableName() string {
	return "resource"
}

// GetTenantID 获取租户ID
func (r *ResourceModel) GetTenantID() int64 {
	return r.TenantID
}

// SetTenantID 设置租户ID
func (r *ResourceModel) SetTenantID(tenantID int64) {
	r.TenantID = tenantID
}

// GetInternalAppID 获取内部应用ID
func (r *ResourceModel) GetInternalAppID() int64 {
	return r.InternalAppID
}

// SetInternalAppID 设置内部应用ID
func (r *ResourceModel) SetInternalAppID(appID int64) {
	r.InternalAppID = appID
}

// IsAllowParameterMode 是否允许参数模式（默认false，严格使用context）
func (r *ResourceModel) IsAllowParameterMode() bool {
	return r.allowParameterMode
}

// SetAllowParameterMode 设置参数模式
func (r *ResourceModel) SetAllowParameterMode(allow bool) {
	r.allowParameterMode = allow
}

// EnableParameterMode 临时启用参数模式（用于特殊场景）
func (r *ResourceModel) EnableParameterMode() {
	r.allowParameterMode = true
}

// DisableParameterMode 禁用参数模式（恢复严格模式）
func (r *ResourceModel) DisableParameterMode() {
	r.allowParameterMode = false
}

// BeforeCreate GORM创建前钩子
func (r *ResourceModel) BeforeCreate(tx *gorm.DB) error {
	model.AutoFillTenantAndApp(tx)
	return nil
}

// BeforeUpdate GORM更新前钩子
func (r *ResourceModel) BeforeUpdate(tx *gorm.DB) error {
	model.AutoFillTenantAndApp(tx)
	return nil
}

// BeforeQuery GORM查询前钩子
func (r *ResourceModel) BeforeQuery(tx *gorm.DB) error {
	model.AutoFilterTenantAndApp(tx)
	return nil
}

// ToDomainEntity 转换为领域实体
func (r *ResourceModel) ToDomainEntity() *entity.Resource {
	resource := &entity.Resource{
		ID:            r.ID,
		TenantID:      r.TenantID,
		InternalAppID: r.InternalAppID,
		Name:          r.Name,
		DisplayName:   r.DisplayName,
		Description:   r.Description,
		ResourceType:  value_object.ResourceType(r.ResourceType),
		ServiceName:   r.ServiceName,
		RequestType:   r.RequestType,
		ResponseType:  r.ResponseType,
		APIMethod:     r.APIMethod,
		ContentType:   r.ContentType,
		ParentID:      r.ParentID,
		Path:          r.Path,
		Icon:          r.Icon,
		SortOrder:     r.SortOrder,
		IsSystem:      r.IsSystem,
		DeletedAt:     r.DeletedAt, // 新增字段
		CreatedAt:     r.CreatedAt,
		UpdatedAt:     r.UpdatedAt,
		IsPublic:      r.IsPublic,
		PublicLevel:   value_object.PublicLevel(r.PublicLevel),
		Assignable:    r.Assignable,
	}

	return resource
}

// FromDomainEntity 从领域实体创建数据模型
func (r *ResourceModel) FromDomainEntity(resource *entity.Resource) {
	r.ID = resource.ID
	r.TenantID = resource.TenantID
	r.InternalAppID = resource.InternalAppID
	r.Name = resource.Name
	r.DisplayName = resource.DisplayName
	r.Description = resource.Description
	r.ResourceType = string(resource.ResourceType)
	r.ServiceName = resource.ServiceName
	r.RequestType = resource.RequestType
	r.ResponseType = resource.ResponseType
	r.APIMethod = resource.APIMethod
	r.ContentType = resource.ContentType
	r.ParentID = resource.ParentID
	r.Path = resource.Path
	r.Icon = resource.Icon
	r.SortOrder = resource.SortOrder
	r.IsSystem = resource.IsSystem
	r.DeletedAt = resource.DeletedAt // 新增字段
	r.CreatedAt = resource.CreatedAt
	r.UpdatedAt = resource.UpdatedAt
	r.IsPublic = resource.IsPublic
	r.PublicLevel = string(resource.PublicLevel)
	r.Assignable = resource.Assignable
}

// NewResourceModelFromDomain 从领域实体创建数据模型
func NewResourceModelFromDomain(resource *entity.Resource) *ResourceModel {
	model := &ResourceModel{}
	model.FromDomainEntity(resource)
	return model
}

// NewResourceModelFromDomainWithParameterMode 从领域实体创建数据模型，可选择是否启用参数模式
func NewResourceModelFromDomainWithParameterMode(resource *entity.Resource, enableParameterMode bool) *ResourceModel {
	model := &ResourceModel{}
	model.FromDomainEntity(resource)
	if enableParameterMode {
		model.EnableParameterMode()
	}
	return model
}
