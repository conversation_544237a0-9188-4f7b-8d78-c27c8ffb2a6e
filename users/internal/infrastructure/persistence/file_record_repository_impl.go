package persistence

import (
	"context"
	"time"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/domain/fileupload/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/fileupload/repository"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/model"
	"gorm.io/gorm"
)

// FileRecordRepositoryImpl 文件记录仓储实现
type FileRecordRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewFileRecordRepositoryImpl 创建文件记录仓储实现
func NewFileRecordRepositoryImpl(db *gorm.DB, logger logiface.Logger) repository.FileRecordRepository {
	return &FileRecordRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建文件记录
func (r *FileRecordRepositoryImpl) Create(ctx context.Context, record *entity.FileRecord) error {
	model := model.NewFileRecordFromEntity(record)
	err := r.db.WithContext(ctx).Create(model).Error
	if err != nil {
		r.logger.Error(ctx, "failed to create file record",
			logiface.Error(err),
			logiface.String("filename", record.FileName),
			logiface.String("storage_path", record.StoragePath),
			logiface.Int64("tenant_id", record.TenantID),
		)
	}
	return err
}

// Update 更新文件记录
func (r *FileRecordRepositoryImpl) Update(ctx context.Context, record *entity.FileRecord) error {
	model := model.NewFileRecordFromEntity(record)
	err := r.db.WithContext(ctx).Save(model).Error
	if err != nil {
		r.logger.Error(ctx, "failed to update file record",
			logiface.Error(err),
			logiface.Int64("file_id", record.ID),
			logiface.String("filename", record.FileName),
			logiface.Int64("tenant_id", record.TenantID),
		)
	}
	return err
}

// Delete 删除文件记录
func (r *FileRecordRepositoryImpl) Delete(ctx context.Context, id int64) error {
	err := r.db.WithContext(ctx).Delete(&model.FileRecord{}, id).Error
	if err != nil {
		r.logger.Error(ctx, "failed to delete file record",
			logiface.Error(err),
			logiface.Int64("file_id", id),
		)
	}
	return err
}

// FindByID 根据ID查找文件记录
func (r *FileRecordRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.FileRecord, error) {
	var model model.FileRecord
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&model).Error; err != nil {
		return nil, err
	}

	return model.ToEntity(), nil
}

// FindByHash 根据文件哈希查找文件记录
func (r *FileRecordRepositoryImpl) FindByHash(ctx context.Context, tenantID int64, fileHash string) (*entity.FileRecord, error) {
	var model model.FileRecord
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND file_hash = ?", tenantID, fileHash).First(&model).Error; err != nil {
		return nil, err
	}

	return model.ToEntity(), nil
}

// FindByURL 根据访问URL查找文件记录
func (r *FileRecordRepositoryImpl) FindByURL(ctx context.Context, accessURL string) (*entity.FileRecord, error) {
	var model model.FileRecord
	if err := r.db.WithContext(ctx).Where("access_url = ?", accessURL).First(&model).Error; err != nil {
		return nil, err
	}

	return model.ToEntity(), nil
}

// FindByTenantID 根据租户ID查找文件记录列表
func (r *FileRecordRepositoryImpl) FindByTenantID(ctx context.Context, tenantID int64, page, size int) ([]*entity.FileRecord, int64, error) {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[model.FileRecord](r.db, ctx, model.FileRecord{}, r.logger).
		WithCondition("tenant_id", "=", tenantID)

	// 添加分页
	offset := (page - 1) * size
	queryBuilder = queryBuilder.WithPagination(offset/size+1, size)

	// 执行查询
	models, err := queryBuilder.Find()
	if err != nil {
		return nil, 0, err
	}

	// 获取总数
	countBuilder := NewQueryBuilder[model.FileRecord](r.db, ctx, model.FileRecord{}, r.logger).
		WithCondition("tenant_id", "=", tenantID)
	total, err := countBuilder.Count()
	if err != nil {
		return nil, 0, err
	}

	// 转换为实体
	entities := make([]*entity.FileRecord, len(models))
	for i, model := range models {
		entities[i] = model.ToEntity()
	}

	return entities, total, nil
}

// FindByUserID 根据用户ID查找文件记录列表
func (r *FileRecordRepositoryImpl) FindByUserID(ctx context.Context, tenantID, userID int64, page, size int) ([]*entity.FileRecord, int64, error) {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[model.FileRecord](r.db, ctx, model.FileRecord{}, r.logger).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("user_id", "=", userID)

	// 添加分页
	offset := (page - 1) * size
	queryBuilder = queryBuilder.WithPagination(offset/size+1, size)

	// 执行查询
	models, err := queryBuilder.Find()
	if err != nil {
		return nil, 0, err
	}

	// 获取总数
	countBuilder := NewQueryBuilder[model.FileRecord](r.db, ctx, model.FileRecord{}, r.logger).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("user_id", "=", userID)
	total, err := countBuilder.Count()
	if err != nil {
		return nil, 0, err
	}

	// 转换为实体
	entities := make([]*entity.FileRecord, len(models))
	for i, model := range models {
		entities[i] = model.ToEntity()
	}

	return entities, total, nil
}

// FindBySceneCode 根据场景代码查找文件记录列表
func (r *FileRecordRepositoryImpl) FindBySceneCode(ctx context.Context, tenantID int64, sceneCode string, page, size int) ([]*entity.FileRecord, int64, error) {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[model.FileRecord](r.db, ctx, model.FileRecord{}, r.logger).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("scene_code", "=", sceneCode)

	// 添加分页
	offset := (page - 1) * size
	queryBuilder = queryBuilder.WithPagination(offset/size+1, size)

	// 执行查询
	models, err := queryBuilder.Find()
	if err != nil {
		return nil, 0, err
	}

	// 获取总数
	countBuilder := NewQueryBuilder[model.FileRecord](r.db, ctx, model.FileRecord{}, r.logger).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("scene_code", "=", sceneCode)
	total, err := countBuilder.Count()
	if err != nil {
		return nil, 0, err
	}

	// 转换为实体
	entities := make([]*entity.FileRecord, len(models))
	for i, model := range models {
		entities[i] = model.ToEntity()
	}

	return entities, total, nil
}

// FindTemporaryFiles 查找临时文件
func (r *FileRecordRepositoryImpl) FindTemporaryFiles(ctx context.Context, tenantID int64, page, size int) ([]*entity.FileRecord, int64, error) {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[model.FileRecord](r.db, ctx, model.FileRecord{}, r.logger).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("is_temporary", "=", true)

	// 添加分页
	offset := (page - 1) * size
	queryBuilder = queryBuilder.WithPagination(offset/size+1, size)

	// 执行查询
	models, err := queryBuilder.Find()
	if err != nil {
		return nil, 0, err
	}

	// 获取总数
	countBuilder := NewQueryBuilder[model.FileRecord](r.db, ctx, model.FileRecord{}, r.logger).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("is_temporary", "=", true)
	total, err := countBuilder.Count()
	if err != nil {
		return nil, 0, err
	}

	// 转换为实体
	entities := make([]*entity.FileRecord, len(models))
	for i, model := range models {
		entities[i] = model.ToEntity()
	}

	return entities, total, nil
}

// FindExpiredFiles 查找过期文件
func (r *FileRecordRepositoryImpl) FindExpiredFiles(ctx context.Context, tenantID int64, before time.Time, page, size int) ([]*entity.FileRecord, int64, error) {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[model.FileRecord](r.db, ctx, model.FileRecord{}, r.logger).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("expire_at", "IS NOT", nil).
		WithCondition("expire_at", "<", before)

	// 添加分页
	offset := (page - 1) * size
	queryBuilder = queryBuilder.WithPagination(offset/size+1, size)

	// 执行查询
	models, err := queryBuilder.Find()
	if err != nil {
		return nil, 0, err
	}

	// 获取总数
	countBuilder := NewQueryBuilder[model.FileRecord](r.db, ctx, model.FileRecord{}, r.logger).
		WithCondition("tenant_id", "=", tenantID).
		WithCondition("expire_at", "IS NOT", nil).
		WithCondition("expire_at", "<", before)
	total, err := countBuilder.Count()
	if err != nil {
		return nil, 0, err
	}

	// 转换为实体
	entities := make([]*entity.FileRecord, len(models))
	for i, model := range models {
		entities[i] = model.ToEntity()
	}

	return entities, total, nil
}

// DeleteExpiredFiles 删除过期文件
func (r *FileRecordRepositoryImpl) DeleteExpiredFiles(ctx context.Context, tenantID int64, before time.Time) error {
	err := r.db.WithContext(ctx).Where("tenant_id = ? AND expire_at IS NOT NULL AND expire_at < ?", tenantID, before).Delete(&model.FileRecord{}).Error
	if err != nil {
		r.logger.Error(ctx, "failed to delete expired files",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.Time("before", before),
		)
	}
	return err
}
