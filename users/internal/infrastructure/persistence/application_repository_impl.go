package persistence

import (
	"context"
	"time"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"

	"gorm.io/gorm"
)

// ApplicationRepositoryImpl 应用仓储实现
type ApplicationRepositoryImpl struct {
	*BaseRepository[entity.Application]
}

// NewApplicationRepositoryImpl 创建应用仓储实现
func NewApplicationRepositoryImpl(db *gorm.DB, logger logiface.Logger) repository.ApplicationRepository {
	return &ApplicationRepositoryImpl{
		BaseRepository: NewBaseRepository[entity.Application](db, logger),
	}
}

// GetByID 根据ID查找应用
func (r *ApplicationRepositoryImpl) GetByID(ctx context.Context, id int64) (*entity.Application, error) {
	return r.FindByID(ctx, id)
}

// GetByInternalAppID 根据内部应用ID查找应用
func (r *ApplicationRepositoryImpl) GetByInternalAppID(ctx context.Context, internalAppID, tenantID int64) (*entity.Application, error) {

	// 添加internal_app_id条件
	query := r.GetDB().WithContext(ctx).Model(&entity.Application{}).
		Where("tenant_id = ? AND internal_app_id = ? AND deleted_at IS NULL", tenantID, internalAppID)

	var app entity.Application
	err := query.First(&app).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &app, nil
}

// GetByAppID 根据应用ID查找应用
func (r *ApplicationRepositoryImpl) GetByAppID(ctx context.Context, appID string) (*entity.Application, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)

	query := r.GetDB().WithContext(ctx).Model(&entity.Application{})

	// 如果tenantID为0，则只根据app_id查询
	if tenantID == 0 {
		query = query.Where("app_id = ? AND deleted_at IS NULL", appID)
	} else {
		query = query.Where("tenant_id = ? AND app_id = ? AND deleted_at IS NULL", tenantID, appID)
	}

	var app entity.Application
	err := query.First(&app).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &app, nil
}

// List 获取应用列表
func (r *ApplicationRepositoryImpl) List(ctx context.Context, params *repository.ApplicationQueryParams) ([]*entity.Application, int64, error) {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[entity.Application](r.GetDB(), ctx, entity.Application{}, r.logger).
		WithTenantFilter()

	// 添加查询条件
	queryBuilder = r.buildQueryConditions(queryBuilder, params)

	// 添加排序
	queryBuilder = queryBuilder.WithOrder(params.OrderBy, params.OrderDir)

	// 添加分页
	queryBuilder = queryBuilder.WithPagination(params.GetOffset()/params.Size+1, params.Size)

	// 执行查询
	applications, err := queryBuilder.Find()
	if err != nil {
		return nil, 0, err
	}

	// 获取总数
	countBuilder := NewQueryBuilder[entity.Application](r.GetDB(), ctx, entity.Application{}, r.logger).
		WithTenantFilter()
	countBuilder = r.buildQueryConditions(countBuilder, params)
	total, err := countBuilder.Count()
	if err != nil {
		return nil, 0, err
	}

	// 转换为指针切片
	applicationPtrs := make([]*entity.Application, len(applications))
	for i := range applications {
		applicationPtrs[i] = &applications[i]
	}

	return applicationPtrs, total, nil
}

// ListByTenantID 根据租户ID获取应用列表（用于超管查询指定租户）
func (r *ApplicationRepositoryImpl) ListByTenantID(ctx context.Context, tenantID int64, params *repository.ApplicationQueryParams) ([]*entity.Application, int64, error) {
	// 使用 QueryBuilder 构建查询，但不使用 WithTenantFilter
	queryBuilder := NewQueryBuilder[entity.Application](r.GetDB(), ctx, entity.Application{}, r.logger).
		WithCondition("tenant_id", "=", tenantID) // 手动指定tenant_id条件

	// 添加查询条件
	queryBuilder = r.buildQueryConditions(queryBuilder, params)

	// 添加排序
	queryBuilder = queryBuilder.WithOrder(params.OrderBy, params.OrderDir)

	// 添加分页
	queryBuilder = queryBuilder.WithPagination(params.GetOffset()/params.Size+1, params.Size)

	// 执行查询
	applications, err := queryBuilder.Find()
	if err != nil {
		return nil, 0, err
	}

	// 获取总数
	countBuilder := NewQueryBuilder[entity.Application](r.GetDB(), ctx, entity.Application{}, r.logger).
		WithCondition("tenant_id", "=", tenantID) // 手动指定tenant_id条件
	countBuilder = r.buildQueryConditions(countBuilder, params)
	total, err := countBuilder.Count()
	if err != nil {
		return nil, 0, err
	}

	// 转换为指针切片
	applicationPtrs := make([]*entity.Application, len(applications))
	for i := range applications {
		applicationPtrs[i] = &applications[i]
	}

	return applicationPtrs, total, nil
}

// Count 统计应用数量
func (r *ApplicationRepositoryImpl) Count(ctx context.Context, params *repository.ApplicationQueryParams) (int64, error) {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[entity.Application](r.GetDB(), ctx, entity.Application{}, r.logger).
		WithTenantFilter()

	// 添加查询条件
	queryBuilder = r.buildQueryConditions(queryBuilder, params)

	// 执行查询
	return queryBuilder.Count()
}

// Enable 启用应用
func (r *ApplicationRepositoryImpl) Enable(ctx context.Context, internalAppID int64, updatedBy int64) error {
	return r.GetDB().WithContext(ctx).Model(&entity.Application{}).
		Where("internal_app_id = ?", internalAppID).
		Updates(map[string]interface{}{
			"status":     "active",
			"updated_by": updatedBy,
			"updated_at": time.Now(),
		}).Error
}

// Disable 禁用应用
func (r *ApplicationRepositoryImpl) Disable(ctx context.Context, internalAppID int64, updatedBy int64) error {
	return r.GetDB().WithContext(ctx).Model(&entity.Application{}).
		Where("internal_app_id = ?", internalAppID).
		Updates(map[string]interface{}{
			"status":     "inactive",
			"updated_by": updatedBy,
			"updated_at": time.Now(),
		}).Error
}

// ExistsByAppID 检查应用ID是否存在
func (r *ApplicationRepositoryImpl) ExistsByAppID(ctx context.Context, appID string, excludeID ...int64) (bool, error) {
	query := r.GetDB().WithContext(ctx).Model(&entity.Application{}).
		Where("app_id = ? AND deleted_at IS NULL", appID)

	if len(excludeID) > 0 {
		query = query.Where("id != ?", excludeID[0])
	}

	var count int64
	err := query.Count(&count).Error
	return count > 0, err
}

// ExistsByInternalAppID 检查内部应用ID是否存在
func (r *ApplicationRepositoryImpl) ExistsByInternalAppID(ctx context.Context, internalAppID int64, excludeID ...int64) (bool, error) {
	query := r.GetDB().WithContext(ctx).Model(&entity.Application{}).
		Where("internal_app_id = ? AND deleted_at IS NULL", internalAppID)

	if len(excludeID) > 0 {
		query = query.Where("id != ?", excludeID[0])
	}

	var count int64
	err := query.Count(&count).Error
	return count > 0, err
}

// GetStats 获取应用统计信息
func (r *ApplicationRepositoryImpl) GetStats(ctx context.Context) (*repository.ApplicationStats, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)

	stats := &repository.ApplicationStats{
		TypeCounts:   make(map[entity.ApplicationType]int64),
		StatusCounts: make(map[entity.ApplicationStatus]int64),
	}

	db := r.GetDB().WithContext(ctx)

	// 统计总数
	if err := db.Model(&entity.Application{}).
		Where("tenant_id = ? AND deleted_at IS NULL", tenantID).
		Count(&stats.TotalCount).Error; err != nil {
		return nil, err
	}

	// 统计活跃应用数量
	if err := db.Model(&entity.Application{}).
		Where("tenant_id = ? AND status = ? AND deleted_at IS NULL", tenantID, "active").
		Count(&stats.ActiveCount).Error; err != nil {
		return nil, err
	}

	// 统计系统应用数量
	if err := db.Model(&entity.Application{}).
		Where("tenant_id = ? AND is_system = ? AND deleted_at IS NULL", tenantID, true).
		Count(&stats.SystemCount).Error; err != nil {
		return nil, err
	}

	// 统计各状态的应用数量
	var statusStats []struct {
		Status string `gorm:"column:status"`
		Count  int64  `gorm:"column:count"`
	}
	if err := db.Select("status, COUNT(*) as count").Group("status").Scan(&statusStats).Error; err != nil {
		return nil, err
	}
	for _, stat := range statusStats {
		stats.StatusCounts[entity.ApplicationStatus(stat.Status)] = stat.Count
	}

	return stats, nil
}

// SoftDelete 软删除应用
func (r *ApplicationRepositoryImpl) SoftDelete(ctx context.Context, id int64) error {
	return r.GetDB().WithContext(ctx).Model(&entity.Application{}).
		Where("id = ?", id).
		Update("deleted_at", time.Now()).Error
}

// buildQueryConditions 构建查询条件 - 使用 QueryBuilder 模式
func (r *ApplicationRepositoryImpl) buildQueryConditions(qb *QueryBuilder[entity.Application], params *repository.ApplicationQueryParams) *QueryBuilder[entity.Application] {
	// 软删除过滤
	if !params.WithDeleted {
		qb = qb.WithCondition("deleted_at", "IS", nil)
	}

	// IDs过滤
	if len(params.IDs) > 0 {
		qb = qb.WithInConditionInt64("id", params.IDs)
	}

	// InternalAppIDs过滤
	if len(params.InternalAppIDs) > 0 {
		qb = qb.WithInConditionInt64("internal_app_id", params.InternalAppIDs)
	}

	// AppIDs过滤
	if len(params.AppIDs) > 0 {
		appIDs := make([]interface{}, len(params.AppIDs))
		for i, appID := range params.AppIDs {
			appIDs[i] = appID
		}
		qb = qb.WithInCondition("app_id", appIDs)
	}

	// 应用名称过滤
	if params.AppName != nil {
		qb = qb.WithCondition("app_name", "=", *params.AppName)
	}

	// 应用类型过滤
	if params.AppType != nil {
		qb = qb.WithCondition("app_type", "=", *params.AppType)
	}

	// 状态过滤
	if params.Status != nil {
		qb = qb.WithCondition("status", "=", *params.Status)
	}

	// 是否系统应用过滤
	if params.IsSystem != nil {
		qb = qb.WithCondition("is_system", "=", *params.IsSystem)
	}

	// 关键词搜索
	if params.Keyword != nil && *params.Keyword != "" {
		searchFields := params.SearchFields
		if len(searchFields) == 0 {
			searchFields = []string{"app_name", "description", "app_id"}
		}

		for i, field := range searchFields {
			if i == 0 {
				qb = qb.WithLikeCondition(field, *params.Keyword)
			} else {
				qb = qb.WithOrCondition(field, "LIKE", "%"+*params.Keyword+"%")
			}
		}
	}

	// 时间范围过滤
	if params.CreatedAtStart != nil {
		if startTime, err := time.Parse("2006-01-02 15:04:05", *params.CreatedAtStart); err == nil {
			qb = qb.WithCondition("created_at", ">=", startTime)
		}
	}

	if params.CreatedAtEnd != nil {
		if endTime, err := time.Parse("2006-01-02 15:04:05", *params.CreatedAtEnd); err == nil {
			qb = qb.WithCondition("created_at", "<=", endTime)
		}
	}

	return qb
}
