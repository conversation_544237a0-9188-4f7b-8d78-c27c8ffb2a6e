package persistence

import (
	"context"
	"time"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/models"

	"gorm.io/gorm"
)

// RoleRepositoryImpl 角色仓储实现
type RoleRepositoryImpl struct {
	*BaseRepository[models.RoleModel]
	logger logiface.Logger
}

// NewRoleRepository 创建角色仓储
func NewRoleRepository(db *gorm.DB, logger logiface.Logger) repository.RoleRepository {
	return &RoleRepositoryImpl{
		BaseRepository: NewBaseRepository[models.RoleModel](db, logger),
		logger:         logger,
	}
}

// Create 创建角色
func (r *RoleRepositoryImpl) Create(ctx context.Context, role *entity.Role) error {
	roleModel := models.NewRoleModelFromDomain(role)
	err := r.db.WithContext(ctx).Create(roleModel).Error
	if err != nil {
		r.logger.Error(ctx, "failed to create role",
			logiface.Error(err),
			logiface.String("role_code", role.Code),
			logiface.String("role_name", role.Name),
			logiface.Int64("tenant_id", role.TenantID),
		)
		return err
	}
	// 将生成的ID设置回领域实体
	role.ID = roleModel.ID
	return nil
}

// Update 更新角色
func (r *RoleRepositoryImpl) Update(ctx context.Context, role *entity.Role) error {
	roleModel := models.NewRoleModelFromDomain(role)
	err := r.db.WithContext(ctx).Save(roleModel).Error
	if err != nil {
		r.logger.Error(ctx, "failed to update role",
			logiface.Error(err),
			logiface.Int64("role_id", role.ID),
			logiface.String("role_code", role.Code),
			logiface.Int64("tenant_id", role.TenantID),
		)
	}
	return err
}

// Delete 删除角色
func (r *RoleRepositoryImpl) Delete(ctx context.Context, id int64) error {
	err := r.db.WithContext(ctx).Delete(&models.RoleModel{}, id).Error
	if err != nil {
		r.logger.Error(ctx, "failed to delete role",
			logiface.Error(err),
			logiface.Int64("role_id", id),
		)
	}
	return err
}

// FindByID 根据ID查找角色
func (r *RoleRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.Role, error) {
	roleModel, err := NewQueryBuilder[models.RoleModel](r.db, ctx, models.RoleModel{}, r.logger).
		WithTenantFilter().
		ValidateUserID(id).
		WithCondition("id", "=", id).
		FindOne()
	if err != nil || roleModel == nil {
		return nil, err
	}
	return roleModel.ToDomainEntity(), nil
}

// GetByCode 根据代码查找角色
func (r *RoleRepositoryImpl) GetByCode(ctx context.Context, code string) (*entity.Role, error) {
	roleModel, err := NewQueryBuilder[models.RoleModel](r.db, ctx, models.RoleModel{}, r.logger).
		WithTenantFilter().
		ValidateStringParam(code, "code").
		WithCondition("code", "=", code).
		WithCondition("status", "=", "active").
		FindOne()
	if err != nil || roleModel == nil {
		return nil, err
	}
	return roleModel.ToDomainEntity(), nil
}

// FindByCode 根据代码查找角色（保持向后兼容）
func (r *RoleRepositoryImpl) FindByCode(ctx context.Context, tenantID int64, code string) (*entity.Role, error) {
	return r.GetByCode(ctx, code)
}

// ExistsByCode 检查代码是否存在
func (r *RoleRepositoryImpl) ExistsByCode(ctx context.Context, code string) (bool, error) {
	return NewQueryBuilder[models.RoleModel](r.db, ctx, models.RoleModel{}, r.logger).
		WithTenantFilter().
		ValidateStringParam(code, "code").
		WithCondition("code", "=", code).
		WithCondition("status", "=", "active").
		Exists()
}

// ExistsByName 检查名称是否存在
func (r *RoleRepositoryImpl) ExistsByName(ctx context.Context, name string) (bool, error) {
	return NewQueryBuilder[models.RoleModel](r.db, ctx, models.RoleModel{}, r.logger).
		WithTenantFilter().
		ValidateStringParam(name, "name").
		WithCondition("name", "=", name).
		WithCondition("status", "=", "active").
		Exists()
}

// QueryRoles 通用多维度组合查询
func (r *RoleRepositoryImpl) QueryRoles(ctx context.Context, filter repository.RoleQueryFilter) ([]entity.Role, int64, error) {
	// 基础查询构建器
	qb := NewQueryBuilder[models.RoleModel](r.db, ctx, models.RoleModel{}, r.logger).
		WithTenantFilter()

	// 应用过滤条件
	if filter.Code != nil && *filter.Code != "" {
		qb.WithCondition("code", "=", *filter.Code)
	}
	if filter.Name != nil && *filter.Name != "" {
		qb.WithCondition("name", "=", *filter.Name)
	}
	if filter.Status != nil && *filter.Status != "" {
		qb.WithCondition("status", "=", *filter.Status)
	}
	if filter.IsSystem != nil {
		qb.WithCondition("is_system", "=", *filter.IsSystem)
	}
	if filter.Keyword != nil && *filter.Keyword != "" {
		qb.WithCondition("name", "LIKE", "%"+*filter.Keyword+"%").
			WithOrCondition("description", "LIKE", "%"+*filter.Keyword+"%")
	}
	if len(filter.RoleIDs) > 0 {
		qb.WithInConditionInt64("id", filter.RoleIDs)
	}

	// 分页
	if filter.Offset > 0 || filter.Limit > 0 {
		qb.WithPagination(filter.Offset/filter.Limit+1, filter.Limit)
	}

	// 默认排序
	qb.WithOrder("id", "DESC")

	// 获取总数
	total, err := qb.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取数据
	roleModels, err := qb.Find()
	if err != nil {
		return nil, 0, err
	}

	// 转换为领域实体
	roles := make([]entity.Role, len(roleModels))
	for i, roleModel := range roleModels {
		roles[i] = *roleModel.ToDomainEntity()
	}

	return roles, total, nil
}

// GetUsersByRole 获取角色下的用户
func (r *RoleRepositoryImpl) GetUsersByRole(ctx context.Context, roleID int64) ([]entity.User, error) {
	// 使用 query builder 模式
	qb := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		ValidateUserID(roleID).
		WithTenantFilter()

	// 构建 JOIN 查询
	query := qb.buildQuery().
		Joins("JOIN user_roles ur ON users.id = ur.user_id").
		Where("ur.role_id = ? AND users.deleted_at IS NULL", roleID)

	var userModels []models.UserModel
	err := query.Find(&userModels).Error
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	users := make([]entity.User, len(userModels))
	for i, userModel := range userModels {
		users[i] = *userModel.ToDomainEntity()
	}
	return users, nil
}

// GetRolesByUser 获取用户的角色
func (r *RoleRepositoryImpl) GetRolesByUser(ctx context.Context, userID int64) ([]entity.Role, error) {
	// 使用 query builder 模式
	qb := NewQueryBuilder[models.RoleModel](r.db, ctx, models.RoleModel{}, r.logger).
		ValidateUserID(userID).
		WithTenantFilter()

	// 构建 JOIN 查询
	query := qb.buildQuery().
		Joins("JOIN user_roles ur ON roles.id = ur.role_id").
		Where("ur.user_id = ? AND roles.deleted_at IS NULL", userID)

	var roleModels []models.RoleModel
	err := query.Find(&roleModels).Error
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	roles := make([]entity.Role, len(roleModels))
	for i, roleModel := range roleModels {
		roles[i] = *roleModel.ToDomainEntity()
	}
	return roles, nil
}

// GetPermissionsByRole 获取角色的权限
func (r *RoleRepositoryImpl) GetPermissionsByRole(ctx context.Context, roleID int64) ([]entity.Permission, error) {
	// 使用 query builder 模式
	qb := NewQueryBuilder[models.PermissionModel](r.db, ctx, models.PermissionModel{}, r.logger).
		ValidateUserID(roleID).
		WithTenantFilter()

	// 构建 JOIN 查询
	query := qb.buildQuery().
		Joins("JOIN role_permissions rp ON permissions.id = rp.permission_id").
		Where("rp.role_id = ? AND permissions.deleted_at IS NULL", roleID)

	var permissionModels []models.PermissionModel
	err := query.Find(&permissionModels).Error
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	permissions := make([]entity.Permission, len(permissionModels))
	for i, permissionModel := range permissionModels {
		permissions[i] = *permissionModel.ToDomainEntity()
	}
	return permissions, nil
}

// GetPermissionsByRoleIDs 批量获取角色权限
func (r *RoleRepositoryImpl) GetPermissionsByRoleIDs(ctx context.Context, roleIDs []int64) (map[int64][]entity.Permission, error) {
	if len(roleIDs) == 0 {
		return make(map[int64][]entity.Permission), nil
	}

	// 查询角色权限关联和权限信息
	type RolePermissionResult struct {
		RoleID       int64 `gorm:"column:role_id"`
		PermissionID int64 `gorm:"column:permission_id"`
		models.PermissionModel
	}

	// 使用 query builder 模式
	qb := NewQueryBuilder[RolePermissionResult](r.db, ctx, RolePermissionResult{}, r.logger).
		ValidateInt64ArrayParam(roleIDs, "role_ids")

	// 构建复杂 JOIN 查询
	query := qb.buildQuery().
		Table("role_permissions rp").
		Joins("JOIN permissions p ON rp.permission_id = p.id").
		Where("rp.role_id IN ? AND p.deleted_at IS NULL", roleIDs).
		Select("rp.role_id, rp.permission_id, p.*")

	var results []RolePermissionResult
	err := query.Scan(&results).Error
	if err != nil {
		return nil, err
	}

	// 组织结果
	rolePermissions := make(map[int64][]entity.Permission)
	for _, result := range results {
		permission := result.PermissionModel.ToDomainEntity()
		rolePermissions[result.RoleID] = append(rolePermissions[result.RoleID], *permission)
	}

	// 确保所有角色都有键值，即使没有权限
	for _, roleID := range roleIDs {
		if _, exists := rolePermissions[roleID]; !exists {
			rolePermissions[roleID] = []entity.Permission{}
		}
	}

	return rolePermissions, nil
}

// GetRoleStats 获取角色统计信息
func (r *RoleRepositoryImpl) GetRoleStats(ctx context.Context) (total, active, system, disabled, userCount, permissionCount int, err error) {
	// 获取总角色数
	totalCount, err := NewQueryBuilder[models.RoleModel](r.db, ctx, models.RoleModel{}, r.logger).
		WithTenantFilter().
		Count()
	if err != nil {
		return 0, 0, 0, 0, 0, 0, err
	}
	total = int(totalCount)

	// 获取活跃角色数
	activeCount, err := NewQueryBuilder[models.RoleModel](r.db, ctx, models.RoleModel{}, r.logger).
		WithTenantFilter().
		WithCondition("status", "=", "active").
		Count()
	if err != nil {
		return 0, 0, 0, 0, 0, 0, err
	}
	active = int(activeCount)

	// 获取系统角色数
	systemCount, err := NewQueryBuilder[models.RoleModel](r.db, ctx, models.RoleModel{}, r.logger).
		WithTenantFilter().
		WithCondition("is_system", "=", true).
		Count()
	if err != nil {
		return 0, 0, 0, 0, 0, 0, err
	}
	system = int(systemCount)

	// 获取禁用角色数
	disabledCount, err := NewQueryBuilder[models.RoleModel](r.db, ctx, models.RoleModel{}, r.logger).
		WithTenantFilter().
		WithCondition("status", "=", "disabled").
		Count()
	if err != nil {
		return 0, 0, 0, 0, 0, 0, err
	}
	disabled = int(disabledCount)

	// 获取用户总数
	var userTotalCount int64
	err = r.db.WithContext(ctx).
		Table("user_roles ur").
		Joins("JOIN users u ON ur.user_id = u.id").
		Where("u.deleted_at IS NULL").
		Count(&userTotalCount).Error
	if err != nil {
		return 0, 0, 0, 0, 0, 0, err
	}
	userCount = int(userTotalCount)

	// 获取权限总数
	var permissionTotalCount int64
	err = r.db.WithContext(ctx).
		Table("role_permissions rp").
		Joins("JOIN permissions p ON rp.permission_id = p.id").
		Where("p.deleted_at IS NULL").
		Count(&permissionTotalCount).Error
	if err != nil {
		return 0, 0, 0, 0, 0, 0, err
	}
	permissionCount = int(permissionTotalCount)

	return total, active, system, disabled, userCount, permissionCount, nil
}

// AssignPermissions 分配权限
func (r *RoleRepositoryImpl) AssignPermissions(ctx context.Context, roleID int64, permissionIDs []int64) error {
	if len(permissionIDs) == 0 {
		return nil
	}

	// 批量插入角色权限关联
	var rolePermissions []interface{}
	for _, permissionID := range permissionIDs {
		rolePermissions = append(rolePermissions, map[string]interface{}{
			"role_id":       roleID,
			"permission_id": permissionID,
		})
	}

	err := r.db.WithContext(ctx).Table("role_permissions").Create(rolePermissions).Error
	if err != nil {
		r.logger.Error(ctx, "failed to assign permissions to role",
			logiface.Error(err),
			logiface.Int64("role_id", roleID),
			logiface.Any("permission_ids", permissionIDs),
			logiface.Int("permission_count", len(permissionIDs)),
		)
	}
	return err
}

// RemovePermissions 移除权限
func (r *RoleRepositoryImpl) RemovePermissions(ctx context.Context, roleID int64, permissionIDs []int64) error {
	if len(permissionIDs) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).
		Table("role_permissions").
		Where("role_id = ? AND permission_id IN ?", roleID, permissionIDs).
		Delete(nil).Error
	if err != nil {
		r.logger.Error(ctx, "failed to remove permissions from role",
			logiface.Error(err),
			logiface.Int64("role_id", roleID),
			logiface.Any("permission_ids", permissionIDs),
			logiface.Int("permission_count", len(permissionIDs)),
		)
	}
	return err
}

// BatchCreate 批量创建
func (r *RoleRepositoryImpl) BatchCreate(ctx context.Context, roles []entity.Role) error {
	if len(roles) == 0 {
		return nil
	}

	roleModels := make([]*models.RoleModel, len(roles))
	for i, role := range roles {
		roleModels[i] = models.NewRoleModelFromDomain(&role)
	}

	err := r.db.WithContext(ctx).Create(roleModels).Error
	if err != nil {
		r.logger.Error(ctx, "failed to batch create roles",
			logiface.Error(err),
			logiface.Int("role_count", len(roles)),
		)
	}
	return err
}

// BatchDelete 批量删除
func (r *RoleRepositoryImpl) BatchDelete(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).Delete(&models.RoleModel{}, ids).Error
	if err != nil {
		r.logger.Error(ctx, "failed to batch delete roles",
			logiface.Error(err),
			logiface.Any("role_ids", ids),
			logiface.Int("role_count", len(ids)),
		)
	}
	return err
}

// SoftDelete 软删除
func (r *RoleRepositoryImpl) SoftDelete(ctx context.Context, id int64) error {
	return NewUpdateBuilder[models.RoleModel](r.db, ctx, models.RoleModel{}).
		Set("deleted_at", time.Now()).
		WithTenantFilter().
		Where("id", "=", id).
		Update()
}

// Restore 恢复软删除
func (r *RoleRepositoryImpl) Restore(ctx context.Context, id int64) error {
	return NewUpdateBuilder[models.RoleModel](r.db, ctx, models.RoleModel{}).
		Set("deleted_at", nil).
		WithTenantFilter().
		Where("id", "=", id).
		Update()
}
