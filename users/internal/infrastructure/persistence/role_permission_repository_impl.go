package persistence

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/internal/domain/user/constants"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"

	"gorm.io/gorm"
)

// RolePermissionRepositoryImpl 角色权限仓储实现
type RolePermissionRepositoryImpl struct {
	db *gorm.DB
}

// NewRolePermissionRepository 创建角色权限仓储
func NewRolePermissionRepository(db *gorm.DB) repository.RolePermissionRepository {
	return &RolePermissionRepositoryImpl{db: db}
}

// AssignPermission 分配权限给角色
func (r *RolePermissionRepositoryImpl) AssignPermission(ctx context.Context, roleID, permissionID, grantedBy int64) error {
	rolePermission := struct {
		RoleID       int64 `gorm:"column:role_id"`
		PermissionID int64 `gorm:"column:permission_id"`
		GrantedBy    int64 `gorm:"column:granted_by"`
	}{
		RoleID:       roleID,
		PermissionID: permissionID,
		GrantedBy:    grantedBy,
	}

	return r.db.WithContext(ctx).Table("role_permissions").Create(&rolePermission).Error
}

// RemovePermission 移除角色权限
func (r *RolePermissionRepositoryImpl) RemovePermission(ctx context.Context, roleID, permissionID int64) error {
	return r.db.WithContext(ctx).
		Table("role_permissions").
		Where("role_id = ? AND permission_id = ?", roleID, permissionID).
		Delete(nil).Error
}

// GetRolePermissions 获取角色的所有权限
func (r *RolePermissionRepositoryImpl) GetRolePermissions(ctx context.Context, roleID int64) ([]entity.Permission, error) {
	var permissions []entity.Permission
	err := r.db.WithContext(ctx).
		Table("permissions").
		Joins("JOIN role_permissions rp ON permissions.id = rp.permission_id").
		Where("rp.role_id = ? AND permissions.deleted_at IS NULL", roleID).
		Order("permissions.sort ASC, permissions.id ASC").
		Find(&permissions).Error
	return permissions, err
}

// GetPermissionRoles 获取权限分配给哪些角色
func (r *RolePermissionRepositoryImpl) GetPermissionRoles(ctx context.Context, permissionID int64) ([]entity.Role, error) {
	var roles []entity.Role
	err := r.db.WithContext(ctx).
		Table("roles").
		Joins("JOIN role_permissions rp ON roles.id = rp.role_id").
		Where("rp.permission_id = ? AND roles.deleted_at IS NULL", permissionID).
		Order("roles.sort ASC, roles.id ASC").
		Find(&roles).Error
	return roles, err
}

// HasPermission 检查角色是否有指定权限
func (r *RolePermissionRepositoryImpl) HasPermission(ctx context.Context, roleID, permissionID int64) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Table("role_permissions").
		Where("role_id = ? AND permission_id = ?", roleID, permissionID).
		Count(&count).Error
	return count > 0, err
}

// BatchAssignPermissions 批量分配权限
func (r *RolePermissionRepositoryImpl) BatchAssignPermissions(ctx context.Context, roleID int64, permissionIDs []int64, grantedBy int64) error {
	if len(permissionIDs) == 0 {
		return nil
	}

	// 准备批量插入的数据
	rolePermissions := make([]map[string]interface{}, len(permissionIDs))
	for i, permissionID := range permissionIDs {
		rolePermissions[i] = map[string]interface{}{
			"role_id":       roleID,
			"permission_id": permissionID,
			"granted_by":    grantedBy,
		}
	}

	return r.db.WithContext(ctx).Table("role_permissions").Create(&rolePermissions).Error
}

// BatchRemovePermissions 批量移除权限
func (r *RolePermissionRepositoryImpl) BatchRemovePermissions(ctx context.Context, roleID int64, permissionIDs []int64) error {
	if len(permissionIDs) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).
		Table("role_permissions").
		Where("role_id = ? AND permission_id IN ?", roleID, permissionIDs).
		Delete(nil).Error
}

// ReplacePermissions 替换角色的所有权限
func (r *RolePermissionRepositoryImpl) ReplacePermissions(ctx context.Context, roleID int64, permissionIDs []int64, grantedBy int64) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先删除所有现有权限
		err := tx.Table("role_permissions").
			Where("role_id = ?", roleID).
			Delete(nil).Error
		if err != nil {
			return err
		}

		// 如果有新权限，则批量插入
		if len(permissionIDs) > 0 {
			rolePermissions := make([]map[string]interface{}, len(permissionIDs))
			for i, permissionID := range permissionIDs {
				rolePermissions[i] = map[string]interface{}{
					"role_id":       roleID,
					"permission_id": permissionID,
					"granted_by":    grantedBy,
				}
			}
			err = tx.Table("role_permissions").Create(&rolePermissions).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
}

// GetUserPermissions 获取用户的所有权限（通过角色）
func (r *RolePermissionRepositoryImpl) GetUserPermissions(ctx context.Context, userID int64) ([]entity.Permission, error) {
	var permissions []entity.Permission
	err := r.db.WithContext(ctx).
		Table("permissions").
		Joins("JOIN role_permissions rp ON permissions.id = rp.permission_id").
		Joins("JOIN user_roles ur ON rp.role_id = ur.role_id").
		Where("ur.user_id = ? AND permissions.deleted_at IS NULL", userID).
		Distinct("permissions.*").
		Order("permissions.sort ASC, permissions.id ASC").
		Find(&permissions).Error
	return permissions, err
}

// CheckUserPermission 检查用户是否有指定权限
func (r *RolePermissionRepositoryImpl) CheckUserPermission(ctx context.Context, userID int64, permissionCode string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Table("permissions").
		Joins("JOIN role_permissions rp ON permissions.id = rp.permission_id").
		Joins("JOIN user_roles ur ON rp.role_id = ur.role_id").
		Where("ur.user_id = ? AND permissions.code = ? AND permissions.deleted_at IS NULL", userID, permissionCode).
		Count(&count).Error
	return count > 0, err
}

// GetUserPermissionCodes 获取用户的所有权限代码
func (r *RolePermissionRepositoryImpl) GetUserPermissionCodes(ctx context.Context, userID int64) ([]string, error) {
	var codes []string
	err := r.db.WithContext(ctx).
		Table("permissions").
		Joins("JOIN role_permissions rp ON permissions.id = rp.permission_id").
		Joins("JOIN user_roles ur ON rp.role_id = ur.role_id").
		Where("ur.user_id = ? AND permissions.deleted_at IS NULL", userID).
		Distinct("permissions.code").
		Pluck("permissions.code", &codes).Error
	return codes, err
}

// CleanRolePermissions 清理角色的所有权限
func (r *RolePermissionRepositoryImpl) CleanRolePermissions(ctx context.Context, roleID int64) error {
	return r.db.WithContext(ctx).
		Table("role_permissions").
		Where("role_id = ?", roleID).
		Delete(nil).Error
}

// CleanPermissionRoles 清理权限的所有角色关联
func (r *RolePermissionRepositoryImpl) CleanPermissionRoles(ctx context.Context, permissionID int64) error {
	return r.db.WithContext(ctx).
		Table("role_permissions").
		Where("permission_id = ?", permissionID).
		Delete(nil).Error
}

// AssignRole 分配角色给用户
func (r *RolePermissionRepositoryImpl) AssignRole(ctx context.Context, userID, roleID, grantedBy int64) error {
	userRole := &repository.UserRole{
		UserID:    userID,
		RoleID:    roleID,
		GrantedBy: grantedBy,
	}
	return r.db.WithContext(ctx).Create(userRole).Error
}

// BatchAssignRole 批量分配角色给用户
func (r *RolePermissionRepositoryImpl) BatchAssignRole(ctx context.Context, userIDs []int64, roleID, grantedBy int64) error {
	var userRoles []repository.UserRole
	for _, userID := range userIDs {
		userRoles = append(userRoles, repository.UserRole{
			UserID:    userID,
			RoleID:    roleID,
			GrantedBy: grantedBy,
		})
	}
	return r.db.WithContext(ctx).Create(&userRoles).Error
}

// BatchRemoveRole 批量移除用户角色
func (r *RolePermissionRepositoryImpl) BatchRemoveRole(ctx context.Context, userIDs []int64, roleID int64) error {
	return r.db.WithContext(ctx).
		Where("user_id IN ? AND role_id = ?", userIDs, roleID).
		Delete(&repository.UserRole{}).Error
}

// GetExpiredRoleAssignments 获取过期的角色分配
func (r *RolePermissionRepositoryImpl) GetExpiredRoleAssignments(ctx context.Context) ([]repository.UserRole, error) {
	var userRoles []repository.UserRole
	err := r.db.WithContext(ctx).
		Table("user_roles").
		Where("expires_at IS NOT NULL AND expires_at < NOW()").
		Find(&userRoles).Error
	return userRoles, err
}

// RemoveExpiredRoleAssignments 移除过期的角色分配
func (r *RolePermissionRepositoryImpl) RemoveExpiredRoleAssignments(ctx context.Context) error {
	return r.db.WithContext(ctx).
		Table("user_roles").
		Where("expires_at IS NOT NULL AND expires_at < NOW()").
		Delete(nil).Error
}

// GetPermissionAssignmentHistory 获取权限分配历史
func (r *RolePermissionRepositoryImpl) GetPermissionAssignmentHistory(ctx context.Context, roleID int64) ([]repository.RolePermission, error) {
	var rolePermissions []repository.RolePermission
	err := r.db.WithContext(ctx).
		Table("role_permissions").
		Where("role_id = ?", roleID).
		Order("granted_at DESC").
		Find(&rolePermissions).Error
	return rolePermissions, err
}

// GetRoleAssignmentHistory 获取角色分配历史
func (r *RolePermissionRepositoryImpl) GetRoleAssignmentHistory(ctx context.Context, userID int64) ([]repository.UserRole, error) {
	var userRoles []repository.UserRole
	err := r.db.WithContext(ctx).
		Table("user_roles").
		Where("user_id = ?", userID).
		Order("granted_at DESC").
		Find(&userRoles).Error
	return userRoles, err
}

// GetRoleUsers 获取角色的所有用户
func (r *RolePermissionRepositoryImpl) GetRoleUsers(ctx context.Context, roleID int64) ([]entity.User, error) {
	var users []entity.User
	err := r.db.WithContext(ctx).
		Table("users").
		Joins("JOIN user_roles ur ON users.id = ur.user_id").
		Where("ur.role_id = ? AND users.deleted_at IS NULL", roleID).
		Order("users.id ASC").
		Find(&users).Error
	return users, err
}

// GetUserPermissionsByResourceID 获取用户指定资源的权限
func (r *RolePermissionRepositoryImpl) GetUserPermissionsByResourceID(ctx context.Context, userID int64, resourceID int64) ([]entity.Permission, error) {
	var permissions []entity.Permission
	err := r.db.WithContext(ctx).
		Table("permissions").
		Joins("JOIN role_permissions rp ON permissions.id = rp.permission_id").
		Joins("JOIN user_roles ur ON rp.role_id = ur.role_id").
		Joins("JOIN resource_relations rr ON permissions.id = rr.permission_id").
		Where("ur.user_id = ? AND rr.target_resource_id = ? AND permissions.deleted_at IS NULL", userID, resourceID).
		Distinct("permissions.*").
		Order("permissions.id ASC").
		Find(&permissions).Error
	return permissions, err
}

// GetUserPermissionsByResourceIDs 获取用户指定资源列表的权限
func (r *RolePermissionRepositoryImpl) GetUserPermissionsByResourceIDs(ctx context.Context, userID int64, resourceIDs []int64) ([]entity.Permission, error) {
	if len(resourceIDs) == 0 {
		return []entity.Permission{}, nil
	}

	var permissions []entity.Permission
	err := r.db.WithContext(ctx).
		Table("permissions").
		Joins("JOIN role_permissions rp ON permissions.id = rp.permission_id").
		Joins("JOIN user_roles ur ON rp.role_id = ur.role_id").
		Joins("JOIN resource_relations rr ON permissions.id = rr.permission_id").
		Where("ur.user_id = ? AND rr.target_resource_id IN ? AND permissions.deleted_at IS NULL", userID, resourceIDs).
		Distinct("permissions.*").
		Order("rr.target_resource_id ASC, permissions.id ASC").
		Find(&permissions).Error
	return permissions, err
}

// GetUserPermissionsByTenant 获取指定租户下用户的所有权限
func (r *RolePermissionRepositoryImpl) GetUserPermissionsByTenant(ctx context.Context, userID int64) ([]entity.Permission, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	var permissions []entity.Permission
	err := r.db.WithContext(ctx).
		Table("permissions").
		Joins("JOIN role_permissions rp ON permissions.id = rp.permission_id").
		Joins("JOIN user_roles ur ON rp.role_id = ur.role_id").
		Where("ur.user_id = ? AND ur.tenant_id = ? AND permissions.deleted_at IS NULL", userID, tenantID).
		Distinct("permissions.*").
		Order("permissions.sort ASC, permissions.id ASC").
		Find(&permissions).Error
	return permissions, err
}

// GetUserRoles 获取用户的所有角色
func (r *RolePermissionRepositoryImpl) GetUserRoles(ctx context.Context, userID int64) ([]entity.Role, error) {
	var roles []entity.Role
	err := r.db.WithContext(ctx).
		Table("roles").
		Joins("JOIN user_roles ur ON roles.id = ur.role_id").
		Where("ur.user_id = ? AND roles.deleted_at IS NULL", userID).
		Order("roles.sort ASC, roles.id ASC").
		Find(&roles).Error
	return roles, err
}

// GetUserRolesByTenant 获取指定租户下用户的所有角色
func (r *RolePermissionRepositoryImpl) GetUserRolesByTenant(ctx context.Context, userID int64) ([]entity.Role, error) {
	// 从 context 获取租户ID
	tenantID, _ := usercontext.GetTenantID(ctx)
	var roles []entity.Role
	err := r.db.WithContext(ctx).
		Table("roles").
		Joins("JOIN user_roles ur ON roles.id = ur.role_id").
		Where("ur.user_id = ? AND roles.tenant_id = ? AND roles.deleted_at IS NULL", userID, tenantID).
		Order("roles.sort ASC, roles.id ASC").
		Find(&roles).Error
	return roles, err
}

// HasRole 检查用户是否有指定角色
func (r *RolePermissionRepositoryImpl) HasRole(ctx context.Context, userID, roleID int64) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Table("user_roles").
		Where("user_id = ? AND role_id = ?", userID, roleID).
		Count(&count).Error
	return count > 0, err
}

// HasUserPermission 检查用户是否有指定权限
func (r *RolePermissionRepositoryImpl) HasUserPermission(ctx context.Context, userID int64, permissionName string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Table("permissions").
		Joins("JOIN role_permissions rp ON permissions.id = rp.permission_id").
		Joins("JOIN user_roles ur ON rp.role_id = ur.role_id").
		Where("ur.user_id = ? AND permissions.name = ? AND permissions.deleted_at IS NULL", userID, permissionName).
		Count(&count).Error
	return count > 0, err
}

// RemoveRole 移除用户的角色
func (r *RolePermissionRepositoryImpl) RemoveRole(ctx context.Context, userID, roleID int64) error {
	return r.db.WithContext(ctx).
		Table("user_roles").
		Where("user_id = ? AND role_id = ?", userID, roleID).
		Delete(nil).Error
}

// CheckUserPermissionByApp 基于应用ID检查用户权限
func (r *RolePermissionRepositoryImpl) CheckUserPermissionByApp(ctx context.Context, userID int64, internalAppID int64, permissionCode string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Table("permissions").
		Joins("JOIN role_permissions rp ON permissions.id = rp.permission_id").
		Joins("JOIN user_roles ur ON rp.role_id = ur.role_id").
		Joins("JOIN roles r ON ur.role_id = r.id").
		Where("ur.user_id = ? AND r.internal_app_id = ? AND permissions.code = ? AND permissions.deleted_at IS NULL",
			userID, internalAppID, permissionCode).
		Count(&count).Error
	return count > 0, err
}

// GetUserPermissionsByResourceIDAndApp 基于应用ID获取用户对指定资源的权限
func (r *RolePermissionRepositoryImpl) GetUserPermissionsByResourceIDAndApp(ctx context.Context, userID int64, internalAppID int64, resourceID int64) ([]entity.Permission, error) {
	var permissions []entity.Permission
	err := r.db.WithContext(ctx).
		Table("permissions").
		Joins("JOIN role_permissions rp ON permissions.id = rp.permission_id").
		Joins("JOIN user_roles ur ON rp.role_id = ur.role_id").
		Joins("JOIN roles r ON ur.role_id = r.id").
		Joins("JOIN resource_relations rr ON permissions.id = rr.permission_id").
		Where("ur.user_id = ? AND r.internal_app_id = ? AND rr.target_resource_id = ? AND permissions.deleted_at IS NULL",
			userID, internalAppID, resourceID).
		Distinct("permissions.*").
		Order("permissions.id ASC").
		Find(&permissions).Error
	return permissions, err
}

// GetUserPermissionsByResourceIDsAndApp 基于应用ID批量获取用户对多个资源的权限
func (r *RolePermissionRepositoryImpl) GetUserPermissionsByResourceIDsAndApp(ctx context.Context, userID int64, internalAppID int64, resourceIDs []int64) ([]entity.Permission, error) {
	if len(resourceIDs) == 0 {
		return []entity.Permission{}, nil
	}

	var permissions []entity.Permission
	err := r.db.WithContext(ctx).
		Table("permissions").
		Joins("JOIN role_permissions rp ON permissions.id = rp.permission_id").
		Joins("JOIN user_roles ur ON rp.role_id = ur.role_id").
		Joins("JOIN roles r ON ur.role_id = r.id").
		Joins("JOIN resource_relations rr ON permissions.id = rr.permission_id").
		Where("ur.user_id = ? AND r.internal_app_id = ? AND rr.target_resource_id IN ? AND permissions.deleted_at IS NULL",
			userID, internalAppID, resourceIDs).
		Distinct("permissions.*").
		Order("rr.target_resource_id ASC, permissions.id ASC").
		Find(&permissions).Error
	return permissions, err
}

// GetUserRolesByApp 基于应用ID获取用户角色
func (r *RolePermissionRepositoryImpl) GetUserRolesByApp(ctx context.Context, userID int64, internalAppID int64) ([]entity.Role, error) {
	var roles []entity.Role
	err := r.db.WithContext(ctx).
		Table("roles").
		Joins("JOIN user_roles ur ON roles.id = ur.role_id").
		Where("ur.user_id = ? AND roles.internal_app_id = ? AND roles.deleted_at IS NULL",
			userID, internalAppID).
		Order("roles.sort ASC, roles.id ASC").
		Find(&roles).Error
	return roles, err
}

// ==================== 超级管理员权限查询链路（仅用于权限检查，不允许其他业务使用）====================

// CheckUserSuperAdminRoleOnly 检查用户是否具备超级管理员角色（不考虑appId和tenantId）
// 注意：此方法仅用于权限检查，不允许给其他正常业务使用
func (r *RolePermissionRepositoryImpl) CheckUserSuperAdminRoleOnly(ctx context.Context, userID int64) (bool, error) {
	if userID <= 0 {
		return false, fmt.Errorf("invalid user ID: %d", userID)
	}

	// 直接查询用户角色，不考虑应用ID和租户ID
	// 只检查超级管理员角色，用于权限检查场景
	var count int64
	err := r.db.WithContext(ctx).
		Table("user_roles ur").
		Joins("JOIN roles r ON ur.role_id = r.id").
		Where("ur.user_id = ? AND r.code = ? AND r.deleted_at IS NULL", userID, constants.RoleSuperAdmin).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check super admin role: %w", err)
	}

	return count > 0, nil
}
