package persistence

import (
	"context"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/models"

	"gorm.io/gorm"
)

// PermissionRepositoryImpl 权限仓储实现
type PermissionRepositoryImpl struct {
	*BaseRepository[models.PermissionModel]
	logger logiface.Logger
}

// NewPermissionRepository 创建权限仓储
func NewPermissionRepository(db *gorm.DB, logger logiface.Logger) repository.PermissionRepository {
	return &PermissionRepositoryImpl{
		BaseRepository: NewBaseRepository[models.PermissionModel](db, logger),
		logger:         logger,
	}
}

// Create 创建权限
func (r *PermissionRepositoryImpl) Create(ctx context.Context, permission *entity.Permission) error {
	permissionModel := models.NewPermissionModelFromDomain(permission)
	err := r.db.WithContext(ctx).Create(permissionModel).Error
	if err != nil {
		r.logger.Error(ctx, "failed to create permission",
			logiface.Error(err),
			logiface.String("permission_code", permission.Code),
			logiface.String("permission_name", permission.Name),
			logiface.Int64("tenant_id", permission.TenantID),
		)
		return err
	}
	// 将生成的ID设置回领域实体
	permission.ID = permissionModel.ID
	return nil
}

// Update 更新权限
func (r *PermissionRepositoryImpl) Update(ctx context.Context, permission *entity.Permission) error {
	permissionModel := models.NewPermissionModelFromDomain(permission)
	err := r.db.WithContext(ctx).Save(permissionModel).Error
	if err != nil {
		r.logger.Error(ctx, "failed to update permission",
			logiface.Error(err),
			logiface.Int64("permission_id", permission.ID),
			logiface.String("permission_code", permission.Code),
			logiface.Int64("tenant_id", permission.TenantID),
		)
	}
	return err
}

// Delete 删除权限
func (r *PermissionRepositoryImpl) Delete(ctx context.Context, id int64) error {
	err := r.db.WithContext(ctx).Delete(&models.PermissionModel{}, id).Error
	if err != nil {
		r.logger.Error(ctx, "failed to delete permission",
			logiface.Error(err),
			logiface.Int64("permission_id", id),
		)
	}
	return err
}

// FindByID 根据ID查找权限
func (r *PermissionRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.Permission, error) {
	permissionModel, err := NewQueryBuilder[models.PermissionModel](r.db, ctx, models.PermissionModel{}, r.logger).
		WithTenantFilter().
		ValidateUserID(id).
		WithCondition("id", "=", id).
		FindOne()
	if err != nil || permissionModel == nil {
		return nil, err
	}
	return permissionModel.ToDomainEntity(), nil
}

// GetByCode 根据代码查找权限
func (r *PermissionRepositoryImpl) GetByCode(ctx context.Context, code string) (*entity.Permission, error) {
	permissionModel, err := NewQueryBuilder[models.PermissionModel](r.db, ctx, models.PermissionModel{}, r.logger).
		WithTenantFilter().
		ValidateStringParam(code, "code").
		WithCondition("code", "=", code).
		FindOne()
	if err != nil || permissionModel == nil {
		return nil, err
	}
	return permissionModel.ToDomainEntity(), nil
}

// ExistsByCode 检查代码是否存在
func (r *PermissionRepositoryImpl) ExistsByCode(ctx context.Context, code string) (bool, error) {
	return NewQueryBuilder[models.PermissionModel](r.db, ctx, models.PermissionModel{}, r.logger).
		WithTenantFilter().
		ValidateStringParam(code, "code").
		WithCondition("code", "=", code).
		Exists()
}

// ExistsByName 检查名称是否存在
func (r *PermissionRepositoryImpl) ExistsByName(ctx context.Context, name string) (bool, error) {
	return NewQueryBuilder[models.PermissionModel](r.db, ctx, models.PermissionModel{}, r.logger).
		WithTenantFilter().
		ValidateStringParam(name, "name").
		WithCondition("name", "=", name).
		Exists()
}

// List 列出权限
func (r *PermissionRepositoryImpl) List(ctx context.Context, offset, limit int) ([]entity.Permission, int64, error) {
	qb := NewQueryBuilder[models.PermissionModel](r.db, ctx, models.PermissionModel{}, r.logger).
		WithTenantFilter().
		WithOrder("created_at", "ASC")

	if offset > 0 || limit > 0 {
		qb.WithPagination(offset/limit+1, limit)
	}

	// 获取总数
	total, err := qb.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取数据
	permissionModels, err := qb.Find()
	if err != nil {
		return nil, 0, err
	}

	// 转换为领域实体
	permissions := make([]entity.Permission, len(permissionModels))
	for i, permissionModel := range permissionModels {
		permissions[i] = *permissionModel.ToDomainEntity()
	}

	return permissions, total, nil
}

// BatchCreate 批量创建权限
func (r *PermissionRepositoryImpl) BatchCreate(ctx context.Context, permissions []entity.Permission) error {
	if len(permissions) == 0 {
		return nil
	}

	permissionModels := make([]*models.PermissionModel, len(permissions))
	for i, perm := range permissions {
		permissionModels[i] = models.NewPermissionModelFromDomain(&perm)
	}

	err := r.db.WithContext(ctx).Create(permissionModels).Error
	if err != nil {
		r.logger.Error(ctx, "failed to batch create permissions",
			logiface.Error(err),
			logiface.Int("permission_count", len(permissions)),
		)
	}
	return err
}

// BatchDelete 批量删除权限
func (r *PermissionRepositoryImpl) BatchDelete(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).Delete(&models.PermissionModel{}, ids).Error
	if err != nil {
		r.logger.Error(ctx, "failed to batch delete permissions",
			logiface.Error(err),
			logiface.Any("permission_ids", ids),
			logiface.Int("permission_count", len(ids)),
		)
	}
	return err
}

// GetByResource 根据资源获取权限
func (r *PermissionRepositoryImpl) GetByResource(ctx context.Context, resourceID int64) ([]entity.Permission, error) {
	permissionModels, err := NewQueryBuilder[models.PermissionModel](r.db, ctx, models.PermissionModel{}, r.logger).
		WithTenantFilter().
		ValidateUserID(resourceID).
		WithCondition("resource_id", "=", resourceID).
		WithOrder("created_at", "ASC").
		Find()
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	permissions := make([]entity.Permission, len(permissionModels))
	for i, permissionModel := range permissionModels {
		permissions[i] = *permissionModel.ToDomainEntity()
	}

	return permissions, nil
}

// Find 通用查询方法
func (r *PermissionRepositoryImpl) Find(ctx context.Context, params *repository.QueryParams) (*repository.QueryResult[*entity.Permission], error) {
	// 使用 BaseRepository 的 Find 方法
	result, err := r.BaseRepository.Find(ctx, params)
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	permissions := make([]*entity.Permission, len(result.Data))
	for i, model := range result.Data {
		permissions[i] = model.ToDomainEntity()
	}

	return repository.NewQueryResult(permissions, result.Total, result.Error), nil
}

// Count 统计数量
func (r *PermissionRepositoryImpl) Count(ctx context.Context, params *repository.QueryParams) (int64, error) {
	return r.BaseRepository.Count(ctx, params)
}

// GetByName 根据名称获取权限
func (r *PermissionRepositoryImpl) GetByName(ctx context.Context, name string) (*entity.Permission, error) {
	permissionModel, err := NewQueryBuilder[models.PermissionModel](r.db, ctx, models.PermissionModel{}, r.logger).
		WithTenantFilter().
		ValidateStringParam(name, "name").
		WithCondition("name", "=", name).
		FindOne()
	if err != nil || permissionModel == nil {
		return nil, err
	}
	return permissionModel.ToDomainEntity(), nil
}

// GetByTenant 根据租户获取权限
func (r *PermissionRepositoryImpl) GetByTenant(ctx context.Context) ([]entity.Permission, error) {
	permissionModels, err := NewQueryBuilder[models.PermissionModel](r.db, ctx, models.PermissionModel{}, r.logger).
		WithTenantFilter().
		WithOrder("created_at", "ASC").
		Find()
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	permissions := make([]entity.Permission, len(permissionModels))
	for i, permissionModel := range permissionModels {
		permissions[i] = *permissionModel.ToDomainEntity()
	}

	return permissions, nil
}

// GetByResourceID 根据资源ID获取权限
func (r *PermissionRepositoryImpl) GetByResourceID(ctx context.Context, resourceID int64) ([]entity.Permission, error) {
	return r.GetByResource(ctx, resourceID)
}

// GetByResourceIDs 根据资源ID列表获取权限
func (r *PermissionRepositoryImpl) GetByResourceIDs(ctx context.Context, resourceIDs []int64) ([]entity.Permission, error) {
	permissionModels, err := NewQueryBuilder[models.PermissionModel](r.db, ctx, models.PermissionModel{}, r.logger).
		WithTenantFilter().
		ValidateInt64ArrayParam(resourceIDs, "resource_ids").
		WithInConditionInt64("resource_id", resourceIDs).
		WithOrder("created_at", "ASC").
		Find()
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	permissions := make([]entity.Permission, len(permissionModels))
	for i, permissionModel := range permissionModels {
		permissions[i] = *permissionModel.ToDomainEntity()
	}

	return permissions, nil
}

// GetByAction 根据动作获取权限
func (r *PermissionRepositoryImpl) GetByAction(ctx context.Context, action string) ([]entity.Permission, error) {
	permissionModels, err := NewQueryBuilder[models.PermissionModel](r.db, ctx, models.PermissionModel{}, r.logger).
		WithTenantFilter().
		ValidateStringParam(action, "action").
		WithCondition("action", "=", action).
		WithOrder("created_at", "ASC").
		Find()
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	permissions := make([]entity.Permission, len(permissionModels))
	for i, permissionModel := range permissionModels {
		permissions[i] = *permissionModel.ToDomainEntity()
	}

	return permissions, nil
}

// SoftDelete 软删除权限
func (r *PermissionRepositoryImpl) SoftDelete(ctx context.Context, id int64) error {
	return r.BaseRepository.SoftDelete(ctx, id)
}

// Restore 恢复权限
func (r *PermissionRepositoryImpl) Restore(ctx context.Context, id int64) error {
	return r.BaseRepository.Restore(ctx, id)
}
