package persistence

import (
	"context"
	"sort"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/model"

	"gorm.io/gorm"
)

// ResourceAppAssignmentRepositoryImpl 资源应用关联仓储实现
type ResourceAppAssignmentRepositoryImpl struct {
	db     *gorm.DB
	logger logiface.Logger
}

// NewResourceAppAssignmentRepository 创建资源应用关联仓储
func NewResourceAppAssignmentRepository(db *gorm.DB, logger logiface.Logger) repository.ResourceAppAssignmentRepository {
	return &ResourceAppAssignmentRepositoryImpl{
		db:     db,
		logger: logger,
	}
}

// Create 创建资源应用关联
func (r *ResourceAppAssignmentRepositoryImpl) Create(ctx context.Context, assignment *entity.ResourceAppAssignment) error {
	return r.db.WithContext(ctx).Create(assignment).Error
}

// Update 更新资源应用关联
func (r *ResourceAppAssignmentRepositoryImpl) Update(ctx context.Context, assignment *entity.ResourceAppAssignment) error {
	return r.db.WithContext(ctx).Save(assignment).Error
}

// Delete 删除资源应用关联
func (r *ResourceAppAssignmentRepositoryImpl) Delete(ctx context.Context, id int64) error {
	return r.db.WithContext(ctx).Delete(&entity.ResourceAppAssignment{}, id).Error
}

// FindByID 根据ID查找资源应用关联
func (r *ResourceAppAssignmentRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.ResourceAppAssignment, error) {
	var assignment entity.ResourceAppAssignment
	err := r.db.WithContext(ctx).First(&assignment, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &assignment, nil
}

// FindByResource 根据资源查找关联
func (r *ResourceAppAssignmentRepositoryImpl) FindByResource(ctx context.Context, resourceID int64) ([]entity.ResourceAppAssignment, error) {
	tenantID, _ := usercontext.GetTenantID(ctx)

	var assignments []entity.ResourceAppAssignment
	err := r.db.WithContext(ctx).
		Where("resource_id = ? AND tenant_id = ?", resourceID, tenantID).
		Find(&assignments).Error
	return assignments, err
}

// FindByApp 根据应用查找关联
func (r *ResourceAppAssignmentRepositoryImpl) FindByApp(ctx context.Context, appID int64) ([]entity.ResourceAppAssignment, error) {
	tenantID, _ := usercontext.GetTenantID(ctx)

	var assignments []entity.ResourceAppAssignment
	err := r.db.WithContext(ctx).
		Where("internal_app_id = ? AND tenant_id = ?", appID, tenantID).
		Find(&assignments).Error
	return assignments, err
}

// FindByResourceAndApp 根据资源和应用查找关联
func (r *ResourceAppAssignmentRepositoryImpl) FindByResourceAndApp(ctx context.Context, resourceID, appID int64) (*entity.ResourceAppAssignment, error) {
	tenantID, _ := usercontext.GetTenantID(ctx)

	var assignment entity.ResourceAppAssignment
	err := r.db.WithContext(ctx).
		Where("resource_id = ? AND internal_app_id = ? AND tenant_id = ?", resourceID, appID, tenantID).
		First(&assignment).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &assignment, nil
}

// FindActiveByApp 查找应用的所有激活关联
func (r *ResourceAppAssignmentRepositoryImpl) FindActiveByApp(ctx context.Context, appID int64) ([]entity.ResourceAppAssignment, error) {
	tenantID, _ := usercontext.GetTenantID(ctx)

	var assignments []entity.ResourceAppAssignment
	err := r.db.WithContext(ctx).
		Where("internal_app_id = ? AND tenant_id = ? AND is_active = ?", appID, tenantID, true).
		Find(&assignments).Error
	return assignments, err
}

// FindActiveByResource 查找资源的所有激活关联
func (r *ResourceAppAssignmentRepositoryImpl) FindActiveByResource(ctx context.Context, resourceID int64) ([]entity.ResourceAppAssignment, error) {
	tenantID, _ := usercontext.GetTenantID(ctx)

	var assignments []entity.ResourceAppAssignment
	err := r.db.WithContext(ctx).
		Where("resource_id = ? AND tenant_id = ? AND is_active = ?", resourceID, tenantID, true).
		Find(&assignments).Error
	return assignments, err
}

// BatchCreate 批量创建资源应用关联
func (r *ResourceAppAssignmentRepositoryImpl) BatchCreate(ctx context.Context, assignments []entity.ResourceAppAssignment) error {
	if len(assignments) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).Create(&assignments).Error
}

// BatchDelete 批量删除资源应用关联
func (r *ResourceAppAssignmentRepositoryImpl) BatchDelete(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).Delete(&entity.ResourceAppAssignment{}, ids).Error
}

// DeleteByResource 根据资源删除关联
func (r *ResourceAppAssignmentRepositoryImpl) DeleteByResource(ctx context.Context, resourceID int64) error {
	tenantID, _ := usercontext.GetTenantID(ctx)
	return r.db.WithContext(ctx).
		Where("resource_id = ? AND tenant_id = ?", resourceID, tenantID).
		Delete(&entity.ResourceAppAssignment{}).Error
}

// DeleteByApp 根据应用删除关联
func (r *ResourceAppAssignmentRepositoryImpl) DeleteByApp(ctx context.Context, appID int64) error {
	tenantID, _ := usercontext.GetTenantID(ctx)
	return r.db.WithContext(ctx).
		Where("internal_app_id = ? AND tenant_id = ?", appID, tenantID).
		Delete(&entity.ResourceAppAssignment{}).Error
}

// Exists 检查关联是否存在
func (r *ResourceAppAssignmentRepositoryImpl) Exists(ctx context.Context, resourceID, appID int64) (bool, error) {
	tenantID, _ := usercontext.GetTenantID(ctx)
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.ResourceAppAssignment{}).
		Where("resource_id = ? AND internal_app_id = ? AND tenant_id = ?", resourceID, appID, tenantID).
		Count(&count).Error
	return count > 0, err
}

// IsActive 检查关联是否激活
func (r *ResourceAppAssignmentRepositoryImpl) IsActive(ctx context.Context, resourceID, appID int64) (bool, error) {
	tenantID, _ := usercontext.GetTenantID(ctx)
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.ResourceAppAssignment{}).
		Where("resource_id = ? AND internal_app_id = ? AND tenant_id = ? AND is_active = ?", resourceID, appID, tenantID, true).
		Count(&count).Error
	return count > 0, err
}

// CountByResource 统计资源关联数量
func (r *ResourceAppAssignmentRepositoryImpl) CountByResource(ctx context.Context, resourceID int64) (int64, error) {
	tenantID, _ := usercontext.GetTenantID(ctx)
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.ResourceAppAssignment{}).
		Where("resource_id = ? AND tenant_id = ?", resourceID, tenantID).
		Count(&count).Error
	return count, err
}

// CountByApp 统计应用关联数量
func (r *ResourceAppAssignmentRepositoryImpl) CountByApp(ctx context.Context, appID int64) (int64, error) {
	tenantID, _ := usercontext.GetTenantID(ctx)
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.ResourceAppAssignment{}).
		Where("internal_app_id = ? AND tenant_id = ?", appID, tenantID).
		Count(&count).Error
	return count, err
}

// GetResourcesByApp 获取应用相关的资源（通过resource_app_assignments表）
func (r *ResourceAppAssignmentRepositoryImpl) GetResourcesByApp(ctx context.Context, appID int64, tenantID int64) ([]entity.Resource, error) {
	var allResources []entity.Resource

	// 第一步：查询resource_app_assignments表中分配给应用的资源ID
	var assignmentModels []model.ResourceAppAssignmentModel
	err := r.db.WithContext(ctx).
		Where("internal_app_id = ? AND tenant_id = ? AND is_active = ?", appID, tenantID, true).
		Find(&assignmentModels).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to get resource assignments from resource_app_assignments",
			logiface.Error(err),
			logiface.Int64("app_id", appID),
			logiface.Int64("tenant_id", tenantID),
		)
		return nil, err
	}

	// 第二步：查询数据源1 - resource_app_assignments.resource_id + resource.internal_app_id 匹配的数据
	var assignedResources []entity.Resource
	if len(assignmentModels) > 0 {
		var resourceIDs []int64
		for _, assignment := range assignmentModels {
			resourceIDs = append(resourceIDs, assignment.ResourceID)
		}

		// 查询resource表中存在的资源（通过resource_id匹配）
		err = r.db.WithContext(ctx).
			Where("id IN ?", resourceIDs).
			Find(&assignedResources).Error

		if err != nil {
			r.logger.Error(ctx, "Failed to get resources by assignment IDs from resource table",
				logiface.Error(err),
				logiface.Int64("app_id", appID),
				logiface.Int64("tenant_id", tenantID),
				logiface.Int("resource_count", len(resourceIDs)),
			)
			return nil, err
		}

		// 记录查询结果统计
		validResourceCount := len(assignedResources)
		invalidResourceCount := len(assignmentModels) - validResourceCount

		if invalidResourceCount > 0 {
			r.logger.Warn(ctx, "Some resource assignments reference non-existent resources",
				logiface.Int("total_assignments", len(assignmentModels)),
				logiface.Int("valid_resources", validResourceCount),
				logiface.Int("invalid_resources", invalidResourceCount),
				logiface.Int64("app_id", appID),
				logiface.Int64("tenant_id", tenantID),
			)
		}

		r.logger.Info(ctx, "Retrieved assigned resources from resource_app_assignments",
			logiface.Int("assignment_count", len(assignmentModels)),
			logiface.Int("valid_resource_count", validResourceCount),
			logiface.Int("invalid_resource_count", invalidResourceCount),
			logiface.Int64("app_id", appID),
			logiface.Int64("tenant_id", tenantID),
		)
	}

	// 第三步：查询数据源2 - resource.internal_app_id 匹配的数据
	var ownedResources []entity.Resource
	err = r.db.WithContext(ctx).
		Where("internal_app_id = ? AND tenant_id = ?", appID, tenantID).
		Find(&ownedResources).Error

	if err != nil {
		r.logger.Error(ctx, "Failed to get resources by internal_app_id from resource table",
			logiface.Error(err),
			logiface.Int64("app_id", appID),
			logiface.Int64("tenant_id", tenantID),
		)
		return nil, err
	}

	r.logger.Info(ctx, "Retrieved owned resources by internal_app_id",
		logiface.Int("owned_resource_count", len(ownedResources)),
		logiface.Int64("app_id", appID),
		logiface.Int64("tenant_id", tenantID),
	)

	// 第四步：合并两个数据源并去重
	allResources = r.mergeAndDeduplicateResources(assignedResources, ownedResources)

	r.logger.Info(ctx, "Merged resources from both data sources",
		logiface.Int("assigned_resource_count", len(assignedResources)),
		logiface.Int("owned_resource_count", len(ownedResources)),
		logiface.Int("merged_resource_count", len(allResources)),
		logiface.Int64("app_id", appID),
		logiface.Int64("tenant_id", tenantID),
	)

	return allResources, nil
}

// mergeAndDeduplicateResources 合并两个资源数组并去重
func (r *ResourceAppAssignmentRepositoryImpl) mergeAndDeduplicateResources(assignedResources []entity.Resource, ownedResources []entity.Resource) []entity.Resource {
	// 使用map去重，以resource ID为key
	resourceMap := make(map[int64]entity.Resource)

	// 先添加分配的资源
	for _, resource := range assignedResources {
		resourceMap[resource.ID] = resource
	}

	// 再添加拥有的资源，如果ID已存在则跳过
	for _, resource := range ownedResources {
		if _, exists := resourceMap[resource.ID]; !exists {
			resourceMap[resource.ID] = resource
		}
	}

	// 转换回数组并按sort_order排序
	mergedResources := make([]entity.Resource, 0, len(resourceMap))
	for _, resource := range resourceMap {
		mergedResources = append(mergedResources, resource)
	}

	// 按sort_order排序，然后按ID排序确保顺序一致
	sort.Slice(mergedResources, func(i, j int) bool {
		if mergedResources[i].SortOrder != mergedResources[j].SortOrder {
			return mergedResources[i].SortOrder < mergedResources[j].SortOrder
		}
		return mergedResources[i].ID < mergedResources[j].ID
	})

	return mergedResources
}
