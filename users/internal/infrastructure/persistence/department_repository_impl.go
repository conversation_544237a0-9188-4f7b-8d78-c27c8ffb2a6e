package persistence

import (
	"context"
	"fmt"
	"strings"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/models"

	"gorm.io/gorm"
)

// DepartmentRepositoryImpl 部门仓储实现
type DepartmentRepositoryImpl struct {
	*BaseRepository[models.DepartmentModel]
	logger logiface.Logger
}

// NewDepartmentRepository 创建部门仓储
func NewDepartmentRepository(db *gorm.DB, logger logiface.Logger) repository.DepartmentRepository {
	return &DepartmentRepositoryImpl{
		BaseRepository: NewBaseRepository[models.DepartmentModel](db, logger),
		logger:         logger,
	}
}

// Create 创建部门
func (r *DepartmentRepositoryImpl) Create(ctx context.Context, department *entity.Department) error {
	departmentModel := models.NewDepartmentModelFromDomain(department)
	err := r.db.WithContext(ctx).Create(departmentModel).Error
	if err != nil {
		r.logger.Error(ctx, "failed to create department",
			logiface.Error(err),
			logiface.String("department_name", department.Name),
			logiface.String("department_code", department.Code),
			logiface.Int64("tenant_id", department.TenantID),
		)
		return err
	}
	// 将生成的ID设置回领域实体
	department.ID = departmentModel.ID
	return nil
}

// Update 更新部门
func (r *DepartmentRepositoryImpl) Update(ctx context.Context, department *entity.Department) error {
	departmentModel := models.NewDepartmentModelFromDomain(department)
	err := r.db.WithContext(ctx).Save(departmentModel).Error
	if err != nil {
		r.logger.Error(ctx, "failed to update department",
			logiface.Error(err),
			logiface.Int64("department_id", department.ID),
			logiface.String("department_name", department.Name),
			logiface.Int64("tenant_id", department.TenantID),
		)
	}
	return err
}

// Delete 删除部门
func (r *DepartmentRepositoryImpl) Delete(ctx context.Context, id int64) error {
	err := r.db.WithContext(ctx).Delete(&models.DepartmentModel{}, id).Error
	if err != nil {
		r.logger.Error(ctx, "failed to delete department",
			logiface.Error(err),
			logiface.Int64("department_id", id),
		)
	}
	return err
}

// FindByID 根据ID查找部门
func (r *DepartmentRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.Department, error) {
	departmentModel, err := NewQueryBuilder[models.DepartmentModel](r.db, ctx, models.DepartmentModel{}, r.logger).
		WithTenantFilter().
		ValidateUserID(id).
		WithCondition("id", "=", id).
		FindOne()
	if err != nil || departmentModel == nil {
		return nil, err
	}
	return departmentModel.ToDomainEntity(), nil
}

// GetByCode 根据代码查找部门
func (r *DepartmentRepositoryImpl) GetByCode(ctx context.Context, code string) (*entity.Department, error) {
	departmentModel, err := NewQueryBuilder[models.DepartmentModel](r.db, ctx, models.DepartmentModel{}, r.logger).
		WithTenantFilter().
		ValidateStringParam(code, "code").
		WithCondition("code", "=", code).
		FindOne()
	if err != nil || departmentModel == nil {
		return nil, err
	}
	return departmentModel.ToDomainEntity(), nil
}

// List 列出部门
func (r *DepartmentRepositoryImpl) List(ctx context.Context, offset, limit int) ([]entity.Department, int64, error) {
	qb := NewQueryBuilder[models.DepartmentModel](r.db, ctx, models.DepartmentModel{}, r.logger).
		WithTenantFilter().
		WithOrder("sort", "ASC").
		WithOrder("created_at", "ASC")

	if offset > 0 || limit > 0 {
		qb.WithPagination(offset/limit+1, limit)
	}

	// 获取总数
	total, err := qb.Count()
	if err != nil {
		return nil, 0, err
	}

	// 获取数据
	departmentModels, err := qb.Find()
	if err != nil {
		return nil, 0, err
	}

	// 转换为领域实体
	departments := make([]entity.Department, len(departmentModels))
	for i, departmentModel := range departmentModels {
		departments[i] = *departmentModel.ToDomainEntity()
	}

	return departments, total, nil
}

// GetRootDepartments 获取根部门
func (r *DepartmentRepositoryImpl) GetRootDepartments(ctx context.Context) ([]entity.Department, error) {
	departmentModels, err := NewQueryBuilder[models.DepartmentModel](r.db, ctx, models.DepartmentModel{}, r.logger).
		WithTenantFilter().
		WithCondition("parent_id", "IS", nil).
		WithOrder("sort", "ASC").
		WithOrder("created_at", "ASC").
		Find()
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	departments := make([]entity.Department, len(departmentModels))
	for i, departmentModel := range departmentModels {
		departments[i] = *departmentModel.ToDomainEntity()
	}

	return departments, nil
}

// GetChildren 获取子部门
func (r *DepartmentRepositoryImpl) GetChildren(ctx context.Context, parentID int64) ([]entity.Department, error) {
	departmentModels, err := NewQueryBuilder[models.DepartmentModel](r.db, ctx, models.DepartmentModel{}, r.logger).
		WithTenantFilter().
		ValidateUserID(parentID).
		WithCondition("parent_id", "=", parentID).
		WithOrder("sort", "ASC").
		WithOrder("created_at", "ASC").
		Find()
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	departments := make([]entity.Department, len(departmentModels))
	for i, departmentModel := range departmentModels {
		departments[i] = *departmentModel.ToDomainEntity()
	}

	return departments, nil
}

// GetAncestors 获取祖先部门
func (r *DepartmentRepositoryImpl) GetAncestors(ctx context.Context, departmentID int64) ([]entity.Department, error) {
	var ancestors []entity.Department
	currentID := departmentID

	for currentID != 0 {
		dept, err := r.FindByID(ctx, currentID)
		if err != nil {
			return nil, err
		}
		if dept == nil {
			break
		}

		ancestors = append(ancestors, *dept)
		if dept.ParentID == nil {
			break
		}
		currentID = *dept.ParentID
	}

	// 反转数组，使根部门在前
	for i := 0; i < len(ancestors)/2; i++ {
		ancestors[i], ancestors[len(ancestors)-1-i] = ancestors[len(ancestors)-1-i], ancestors[i]
	}

	return ancestors, nil
}

// GetDescendants 获取后代部门
func (r *DepartmentRepositoryImpl) GetDescendants(ctx context.Context, departmentID int64) ([]entity.Department, error) {
	var descendants []entity.Department
	var queue []int64 = []int64{departmentID}

	for len(queue) > 0 {
		currentID := queue[0]
		queue = queue[1:]

		children, err := r.GetChildren(ctx, currentID)
		if err != nil {
			return nil, err
		}

		for _, child := range children {
			descendants = append(descendants, child)
			queue = append(queue, child.ID)
		}
	}

	return descendants, nil
}

// GetFullPath 获取完整路径
func (r *DepartmentRepositoryImpl) GetFullPath(ctx context.Context, departmentID int64) (string, error) {
	ancestors, err := r.GetAncestors(ctx, departmentID)
	if err != nil {
		return "", err
	}

	if len(ancestors) == 0 {
		return "", nil
	}

	var pathParts []string
	for _, ancestor := range ancestors {
		pathParts = append(pathParts, ancestor.Name)
	}

	return strings.Join(pathParts, "/"), nil
}

// GetByTenant 获取租户的所有部门
func (r *DepartmentRepositoryImpl) GetByTenant(ctx context.Context) ([]entity.Department, error) {
	departmentModels, err := NewQueryBuilder[models.DepartmentModel](r.db, ctx, models.DepartmentModel{}, r.logger).
		WithTenantFilter().
		WithOrder("sort", "ASC").
		WithOrder("created_at", "ASC").
		Find()
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	departments := make([]entity.Department, len(departmentModels))
	for i, departmentModel := range departmentModels {
		departments[i] = *departmentModel.ToDomainEntity()
	}

	return departments, nil
}

// ExistsByCode 检查代码是否存在
func (r *DepartmentRepositoryImpl) ExistsByCode(ctx context.Context, code string) (bool, error) {
	return NewQueryBuilder[models.DepartmentModel](r.db, ctx, models.DepartmentModel{}, r.logger).
		WithTenantFilter().
		ValidateStringParam(code, "code").
		WithCondition("code", "=", code).
		Exists()
}

// ExistsByName 检查名称是否存在
func (r *DepartmentRepositoryImpl) ExistsByName(ctx context.Context, name string) (bool, error) {
	return NewQueryBuilder[models.DepartmentModel](r.db, ctx, models.DepartmentModel{}, r.logger).
		WithTenantFilter().
		ValidateStringParam(name, "name").
		WithCondition("name", "=", name).
		Exists()
}

// GetUsersByDepartment 获取部门用户
func (r *DepartmentRepositoryImpl) GetUsersByDepartment(ctx context.Context, departmentID int64) ([]entity.User, error) {
	// 使用 query builder 模式
	qb := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		ValidateUserID(departmentID).
		WithTenantFilter().
		WithCondition("department_id", "=", departmentID)

	userModels, err := qb.Find()
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	users := make([]entity.User, len(userModels))
	for i, userModel := range userModels {
		users[i] = *userModel.ToDomainEntity()
	}

	return users, nil
}

// GetUserCount 获取部门用户数量
func (r *DepartmentRepositoryImpl) GetUserCount(ctx context.Context, departmentID int64) (int64, error) {
	return NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		WithTenantFilter().
		ValidateUserID(departmentID).
		WithCondition("department_id", "=", departmentID).
		Count()
}

// BatchCreate 批量创建部门
func (r *DepartmentRepositoryImpl) BatchCreate(ctx context.Context, departments []entity.Department) error {
	if len(departments) == 0 {
		return nil
	}

	departmentModels := make([]*models.DepartmentModel, len(departments))
	for i, dept := range departments {
		departmentModels[i] = models.NewDepartmentModelFromDomain(&dept)
	}

	err := r.db.WithContext(ctx).Create(departmentModels).Error
	if err != nil {
		r.logger.Error(ctx, "failed to batch create departments",
			logiface.Error(err),
			logiface.Int("department_count", len(departments)),
		)
	}
	return err
}

// BatchDelete 批量删除部门
func (r *DepartmentRepositoryImpl) BatchDelete(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).Delete(&models.DepartmentModel{}, ids).Error
	if err != nil {
		r.logger.Error(ctx, "failed to batch delete departments",
			logiface.Error(err),
			logiface.Any("department_ids", ids),
			logiface.Int("department_count", len(ids)),
		)
	}
	return err
}

// UpdateSort 更新排序
func (r *DepartmentRepositoryImpl) UpdateSort(ctx context.Context, id int64, sort int) error {
	return NewUpdateBuilder[models.DepartmentModel](r.db, ctx, models.DepartmentModel{}).
		Set("sort", sort).
		WithTenantFilter().
		Where("id", "=", id).
		Update()
}

// Move 移动部门到新的父部门
func (r *DepartmentRepositoryImpl) Move(ctx context.Context, departmentID int64, newParentID *int64) error {
	updates := map[string]interface{}{
		"parent_id": newParentID,
	}

	// 如果有新的父部门，需要更新路径
	if newParentID != nil {
		parentPath, err := r.GetFullPath(ctx, *newParentID)
		if err != nil {
			return err
		}

		dept, err := r.FindByID(ctx, departmentID)
		if err != nil {
			return err
		}
		if dept != nil {
			updates["path"] = fmt.Sprintf("%s/%s", parentPath, dept.Name)
		}
	}

	return NewUpdateBuilder[models.DepartmentModel](r.db, ctx, models.DepartmentModel{}).
		SetMap(updates).
		WithTenantFilter().
		Where("id", "=", departmentID).
		Update()
}

// GetDepartmentByUser 根据用户获取部门
func (r *DepartmentRepositoryImpl) GetDepartmentByUser(ctx context.Context, userID int64) (*entity.Department, error) {
	var departmentModel models.DepartmentModel
	err := r.db.WithContext(ctx).
		Joins("JOIN users u ON departments.id = u.department_id").
		Where("u.id = ? AND u.deleted_at IS NULL AND departments.deleted_at IS NULL", userID).
		First(&departmentModel).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return departmentModel.ToDomainEntity(), nil
}

// GetUserCountsByDepartmentIDs 批量获取部门用户数量
func (r *DepartmentRepositoryImpl) GetUserCountsByDepartmentIDs(ctx context.Context, departmentIDs []int64) (map[int64]int64, error) {
	if len(departmentIDs) == 0 {
		return make(map[int64]int64), nil
	}

	type CountResult struct {
		DepartmentID int64 `gorm:"column:department_id"`
		UserCount    int64 `gorm:"column:user_count"`
	}

	// 使用 query builder 模式
	qb := NewQueryBuilder[CountResult](r.db, ctx, CountResult{}, r.logger).
		ValidateInt64ArrayParam(departmentIDs, "department_ids")

	// 构建聚合查询
	query := qb.buildQuery().
		Table("users").
		Select("department_id, COUNT(*) as user_count").
		Where("department_id IN ? AND deleted_at IS NULL", departmentIDs).
		Group("department_id")

	var results []CountResult
	err := query.Scan(&results).Error
	if err != nil {
		return nil, err
	}

	// 组织结果
	userCounts := make(map[int64]int64)
	for _, result := range results {
		userCounts[result.DepartmentID] = result.UserCount
	}

	// 确保所有部门都有键值，即使用户数为0
	for _, deptID := range departmentIDs {
		if _, exists := userCounts[deptID]; !exists {
			userCounts[deptID] = 0
		}
	}

	return userCounts, nil
}

// MoveDepartment 移动部门（别名方法）
func (r *DepartmentRepositoryImpl) MoveDepartment(ctx context.Context, departmentID int64, newParentID int64) error {
	var parentPtr *int64
	if newParentID > 0 {
		parentPtr = &newParentID
	}
	return r.Move(ctx, departmentID, parentPtr)
}
