package persistence

import (
	"context"
	"time"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence/models"

	"gorm.io/gorm"
)

// UserRepositoryImpl 用户仓储实现
type UserRepositoryImpl struct {
	*BaseRepository[models.UserModel]
	logger logiface.Logger
}

// NewUserRepository 创建用户仓储
func NewUserRepository(db *gorm.DB, logger logiface.Logger) repository.UserRepository {
	return &UserRepositoryImpl{
		BaseRepository: NewBaseRepository[models.UserModel](db, logger),
		logger:         logger,
	}
}

// Create 创建用户
func (r *UserRepositoryImpl) Create(ctx context.Context, user *entity.User) error {
	userModel := models.NewUserModelFromDomain(user)
	err := r.db.WithContext(ctx).Create(userModel).Error
	if err != nil {
		r.logger.Error(ctx, "failed to create user",
			logiface.Error(err),
			logiface.String("username", user.Username),
			logiface.String("email", user.Email),
			logiface.Int64("tenant_id", user.TenantID),
		)
		return err
	}
	// 将生成的ID设置回领域实体
	user.ID = userModel.ID
	return nil
}

// Update 更新用户
func (r *UserRepositoryImpl) Update(ctx context.Context, user *entity.User) error {
	userModel := models.NewUserModelFromDomain(user)
	err := r.db.WithContext(ctx).Save(userModel).Error
	if err != nil {
		r.logger.Error(ctx, "failed to update user",
			logiface.Error(err),
			logiface.Int64("user_id", user.ID),
			logiface.String("username", user.Username),
			logiface.Int64("tenant_id", user.TenantID),
		)
	}
	return err
}

// Delete 删除用户（软删除，设置 deleted_at）
func (r *UserRepositoryImpl) Delete(ctx context.Context, id int64) error {
	err := r.db.WithContext(ctx).
		Model(&models.UserModel{}).
		Where("id = ?", id).
		Update("deleted_at", time.Now()).Error
	if err != nil {
		r.logger.Error(ctx, "failed to delete user",
			logiface.Error(err),
			logiface.Int64("user_id", id),
		)
	}
	return err
}

// FindByID 根据ID查找用户
func (r *UserRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.User, error) {
	userModel, err := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		WithCondition("id", "=", id).
		FindOne()
	if err != nil || userModel == nil {
		return nil, err
	}
	return userModel.ToDomainEntity(), nil
}

// Find 通用查询方法
func (r *UserRepositoryImpl) Find(ctx context.Context, params *repository.QueryParams) (*repository.QueryResult[*entity.User], error) {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		WithTenantFilter()

	// 添加查询条件
	queryBuilder = r.buildQueryConditions(queryBuilder, params)

	// 添加排序
	if params.OrderBy != "" {
		queryBuilder = queryBuilder.WithOrder(params.OrderBy, params.OrderDir)
	}

	// 添加分页
	if params.Limit > 0 {
		queryBuilder = queryBuilder.WithPagination(params.Offset/params.Limit+1, params.Limit)
	}

	// 执行查询
	userModels, err := queryBuilder.Find()
	if err != nil {
		return nil, err
	}

	// 获取总数
	countBuilder := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		WithTenantFilter()
	countBuilder = r.buildQueryConditions(countBuilder, params)
	total, err := countBuilder.Count()
	if err != nil {
		return nil, err
	}

	// 转换为领域实体指针切片
	userPtrs := make([]*entity.User, len(userModels))
	for i := range userModels {
		userPtrs[i] = userModels[i].ToDomainEntity()
	}

	return repository.NewQueryResult(userPtrs, total, nil), nil
}

// FindOne 查询单个记录
func (r *UserRepositoryImpl) FindOne(ctx context.Context, params *repository.QueryParams) (*entity.User, error) {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		WithTenantFilter()

	// 添加查询条件
	queryBuilder = r.buildQueryConditions(queryBuilder, params)

	// 添加排序
	if params.OrderBy != "" {
		queryBuilder = queryBuilder.WithOrder(params.OrderBy, params.OrderDir)
	}

	// 执行查询
	userModel, err := queryBuilder.FindOne()
	if err != nil || userModel == nil {
		return nil, err
	}

	return userModel.ToDomainEntity(), nil
}

// Count 统计数量
func (r *UserRepositoryImpl) Count(ctx context.Context, params *repository.QueryParams) (int64, error) {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		WithTenantFilter()

	// 添加查询条件
	queryBuilder = r.buildQueryConditions(queryBuilder, params)

	// 执行查询
	return queryBuilder.Count()
}

// Exists 检查是否存在
func (r *UserRepositoryImpl) Exists(ctx context.Context, params *repository.QueryParams) (bool, error) {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		WithTenantFilter()

	// 添加查询条件
	queryBuilder = r.buildQueryConditions(queryBuilder, params)

	// 执行查询
	return queryBuilder.Exists()
}

// UpdateByParams 根据条件更新
func (r *UserRepositoryImpl) UpdateByParams(ctx context.Context, params *repository.UpdateParams) error {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		WithTenantFilter()

	// 添加查询条件
	queryBuilder = r.buildQueryConditions(queryBuilder, params.QueryParams)

	// 构建更新查询
	query := queryBuilder.buildQuery()
	err := query.Updates(params.Updates).Error
	return err
}

// DeleteByParams 根据条件删除
func (r *UserRepositoryImpl) DeleteByParams(ctx context.Context, params *repository.QueryParams) error {
	// 使用 QueryBuilder 构建查询
	queryBuilder := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		WithTenantFilter()

	// 添加查询条件
	queryBuilder = r.buildQueryConditions(queryBuilder, params)

	// 构建删除查询
	query := queryBuilder.buildQuery()
	err := query.Delete(&models.UserModel{}).Error
	return err
}

// BatchCreate 批量创建
func (r *UserRepositoryImpl) BatchCreate(ctx context.Context, users []*entity.User) error {
	userModels := make([]*models.UserModel, len(users))
	for i, user := range users {
		userModels[i] = models.NewUserModelFromDomain(user)
	}
	err := r.db.WithContext(ctx).Create(userModels).Error
	if err != nil {
		r.logger.Error(ctx, "failed to batch create users",
			logiface.Error(err),
			logiface.Int("user_count", len(users)),
		)
	}
	return err
}

// BatchUpdate 批量更新
func (r *UserRepositoryImpl) BatchUpdate(ctx context.Context, users []*entity.User) error {
	userModels := make([]*models.UserModel, len(users))
	for i, user := range users {
		userModels[i] = models.NewUserModelFromDomain(user)
	}
	err := r.db.WithContext(ctx).Save(userModels).Error
	if err != nil {
		r.logger.Error(ctx, "failed to batch update users",
			logiface.Error(err),
			logiface.Int("user_count", len(users)),
		)
	}
	return err
}

// BatchDelete 批量软删除用户
func (r *UserRepositoryImpl) BatchDelete(ctx context.Context, ids []int64) error {
	err := r.db.WithContext(ctx).
		Model(&models.UserModel{}).
		Where("id IN ?", ids).
		Update("deleted_at", time.Now()).Error
	if err != nil {
		r.logger.Error(ctx, "failed to batch delete users",
			logiface.Error(err),
			logiface.Any("user_ids", ids),
			logiface.Int("user_count", len(ids)),
		)
	}
	return err
}

// buildQuery 构建查询（默认过滤 deleted_at）

// FindByUsername 根据用户名查找用户
func (r *UserRepositoryImpl) FindByUsername(ctx context.Context, username string) (*entity.User, error) {
	userModel, err := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		ValidateStringParam(username, "username").
		WithTenantFilter().
		WithCondition("username", "=", username).
		FindOne()
	if err != nil || userModel == nil {
		return nil, err
	}
	return userModel.ToDomainEntity(), nil
}

// FindByEmail 根据邮箱查找用户
func (r *UserRepositoryImpl) FindByEmail(ctx context.Context, email string) (*entity.User, error) {
	userModel, err := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		ValidateStringParam(email, "email").
		WithTenantFilter().
		WithCondition("email", "=", email).
		FindOne()
	if err != nil || userModel == nil {
		return nil, err
	}
	return userModel.ToDomainEntity(), nil
}

// FindByPhone 根据手机号查找用户
func (r *UserRepositoryImpl) FindByPhone(ctx context.Context, phone string) (*entity.User, error) {
	userModel, err := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		ValidateStringParam(phone, "phone").
		WithCondition("phone", "=", phone).
		FindOne()
	if err != nil || userModel == nil {
		return nil, err
	}
	return userModel.ToDomainEntity(), nil
}

// UpdatePassword 更新密码
func (r *UserRepositoryImpl) UpdatePassword(ctx context.Context, id int64, passwordHash string) error {
	updates := map[string]interface{}{
		"password_hash":       passwordHash,
		"password_changed_at": time.Now(),
	}
	err := NewUpdateBuilder[models.UserModel](r.db, ctx, models.UserModel{}).
		SetMap(updates).
		WithTenantFilter().
		Where("id", "=", id).
		Update()
	if err != nil {
		r.logger.Error(ctx, "failed to update user password",
			logiface.Error(err),
			logiface.Int64("user_id", id),
		)
	}
	return err
}

// UpdateStatus 更新状态
func (r *UserRepositoryImpl) UpdateStatus(ctx context.Context, id int64, status value_object.UserStatus) error {
	err := NewUpdateBuilder[models.UserModel](r.db, ctx, models.UserModel{}).
		Set("status", status.String()).
		WithTenantFilter().
		Where("id", "=", id).
		Update()
	if err != nil {
		r.logger.Error(ctx, "failed to update user status",
			logiface.Error(err),
			logiface.Int64("user_id", id),
			logiface.String("status", status.String()),
		)
	}
	return err
}

// UpdateLastLogin 更新最后登录时间和IP
func (r *UserRepositoryImpl) UpdateLastLogin(ctx context.Context, id int64, ip string) error {
	updates := map[string]interface{}{
		"last_login_at": time.Now(),
		"last_login_ip": ip,
	}
	err := NewUpdateBuilder[models.UserModel](r.db, ctx, models.UserModel{}).
		SetMap(updates).
		WithTenantFilter().
		Where("id", "=", id).
		Update()
	if err != nil {
		r.logger.Error(ctx, "failed to update user last login",
			logiface.Error(err),
			logiface.Int64("user_id", id),
			logiface.String("ip", ip),
		)
	}
	return err
}

// IncrementFailedCount 增加失败次数
func (r *UserRepositoryImpl) IncrementFailedCount(ctx context.Context, id int64) error {
	err := NewUpdateBuilder[models.UserModel](r.db, ctx, models.UserModel{}).
		Increment("failed_login_count", 1).
		WithTenantFilter().
		Where("id", "=", id).
		Update()
	if err != nil {
		r.logger.Error(ctx, "failed to increment user failed login count",
			logiface.Error(err),
			logiface.Int64("user_id", id),
		)
	}
	return err
}

// ResetFailedCount 重置失败次数
func (r *UserRepositoryImpl) ResetFailedCount(ctx context.Context, id int64) error {
	err := NewUpdateBuilder[models.UserModel](r.db, ctx, models.UserModel{}).
		Set("failed_login_count", 0).
		WithTenantFilter().
		Where("id", "=", id).
		Update()
	if err != nil {
		r.logger.Error(ctx, "failed to reset user failed login count",
			logiface.Error(err),
			logiface.Int64("user_id", id),
		)
	}
	return err
}

// LockUser 锁定用户
func (r *UserRepositoryImpl) LockUser(ctx context.Context, id int64, duration int, reason string) error {
	lockUntil := time.Now().Add(time.Duration(duration) * time.Minute)
	updates := map[string]interface{}{
		"status":      value_object.UserStatusLocked.String(),
		"locked_at":   time.Now(),
		"lock_until":  lockUntil,
		"lock_reason": reason,
	}
	err := NewUpdateBuilder[models.UserModel](r.db, ctx, models.UserModel{}).
		SetMap(updates).
		WithTenantFilter().
		Where("id", "=", id).
		Update()
	if err != nil {
		r.logger.Error(ctx, "failed to lock user",
			logiface.Error(err),
			logiface.Int64("user_id", id),
			logiface.Int("duration_minutes", duration),
			logiface.String("reason", reason),
		)
	}
	return err
}

// UnlockUser 解锁用户
func (r *UserRepositoryImpl) UnlockUser(ctx context.Context, id int64) error {
	updates := map[string]interface{}{
		"status":      value_object.UserStatusActive.String(),
		"locked_at":   nil,
		"lock_until":  nil,
		"lock_reason": "",
	}
	err := NewUpdateBuilder[models.UserModel](r.db, ctx, models.UserModel{}).
		SetMap(updates).
		WithTenantFilter().
		Where("id", "=", id).
		Update()
	if err != nil {
		r.logger.Error(ctx, "failed to unlock user",
			logiface.Error(err),
			logiface.Int64("user_id", id),
		)
	}
	return err
}

// FindUsersByIDs 根据用户ID列表查找用户
func (r *UserRepositoryImpl) FindUsersByIDs(ctx context.Context, userIDs []int64) ([]entity.User, error) {
	userModels, err := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		WithTenantFilter().
		ValidateInt64ArrayParam(userIDs, "user_ids").
		WithInConditionInt64("id", userIDs).
		Find()
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	users := make([]entity.User, len(userModels))
	for i, userModel := range userModels {
		users[i] = *userModel.ToDomainEntity()
	}
	return users, nil
}

// FindUsersByStatuses 根据状态列表查找用户
func (r *UserRepositoryImpl) FindUsersByStatuses(ctx context.Context, statuses []value_object.UserStatus) ([]entity.User, error) {
	interfaceStatuses := make([]interface{}, len(statuses))
	for i, status := range statuses {
		interfaceStatuses[i] = status.String()
	}

	userModels, err := NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		WithTenantFilter().
		ValidateArrayParam(interfaceStatuses, "statuses").
		WithInCondition("status", interfaceStatuses).
		Find()
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	users := make([]entity.User, len(userModels))
	for i, userModel := range userModels {
		users[i] = *userModel.ToDomainEntity()
	}
	return users, nil
}

// BatchUpdateStatus 批量更新用户状态
func (r *UserRepositoryImpl) BatchUpdateStatus(ctx context.Context, ids []int64, status value_object.UserStatus) error {
	updates := map[string]interface{}{
		"status":     status.String(),
		"updated_at": time.Now(),
	}
	err := NewUpdateBuilder[models.UserModel](r.db, ctx, models.UserModel{}).
		SetMap(updates).
		WithTenantFilter().
		WhereIn("id", r.convertInt64ToInterface(ids)).
		Update()
	if err != nil {
		r.logger.Error(ctx, "failed to batch update user status",
			logiface.Error(err),
			logiface.Any("user_ids", ids),
			logiface.String("status", status.String()),
		)
	}
	return err
}

// BatchAssignDepartment 批量分配部门
func (r *UserRepositoryImpl) BatchAssignDepartment(ctx context.Context, ids []int64, departmentID int64) error {
	updates := map[string]interface{}{
		"department_id": departmentID,
		"updated_at":    time.Now(),
	}
	err := NewUpdateBuilder[models.UserModel](r.db, ctx, models.UserModel{}).
		SetMap(updates).
		WithTenantFilter().
		WhereIn("id", r.convertInt64ToInterface(ids)).
		Update()
	if err != nil {
		r.logger.Error(ctx, "failed to batch assign department",
			logiface.Error(err),
			logiface.Any("user_ids", ids),
			logiface.Int64("department_id", departmentID),
		)
	}
	return err
}

// convertInt64ToInterface 转换int64切片为interface{}切片的辅助方法
func (r *UserRepositoryImpl) convertInt64ToInterface(ids []int64) []interface{} {
	result := make([]interface{}, len(ids))
	for i, id := range ids {
		result[i] = id
	}
	return result
}

// GetUserRoles 获取用户角色
func (r *UserRepositoryImpl) GetUserRoles(ctx context.Context, userID int64) ([]entity.Role, error) {
	var roleModels []models.RoleModel
	err := r.db.WithContext(ctx).
		Table("roles").
		Joins("JOIN user_roles ur ON roles.id = ur.role_id").
		Where("ur.user_id = ? AND roles.deleted_at IS NULL", userID).
		Find(&roleModels).Error
	if err != nil {
		return nil, err
	}

	// 转换为领域实体
	roles := make([]entity.Role, len(roleModels))
	for i, roleModel := range roleModels {
		roles[i] = *roleModel.ToDomainEntity()
	}
	return roles, nil
}

// ExistsByUsername 检查用户名是否存在
func (r *UserRepositoryImpl) ExistsByUsername(ctx context.Context, username string) (bool, error) {
	return NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		WithTenantFilter().
		ValidateStringParam(username, "username").
		WithCondition("username", "=", username).
		Exists()
}

// ExistsByEmail 检查邮箱是否存在
func (r *UserRepositoryImpl) ExistsByEmail(ctx context.Context, email string) (bool, error) {
	// 使用 internal_app_id + email 进行唯一性判断，不再按租户隔离
	internalAppID, _ := usercontext.GetInternalAppID(ctx)
	return NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		ValidateStringParam(email, "email").
		ValidateAppID(internalAppID).
		WithTenantFilter().
		WithCondition("internal_app_id", "=", internalAppID).
		WithCondition("email", "=", email).
		Exists()
}

// ExistsByPhone 检查手机号是否存在
func (r *UserRepositoryImpl) ExistsByPhone(ctx context.Context, phone string) (bool, error) {
	return NewQueryBuilder[models.UserModel](r.db, ctx, models.UserModel{}, r.logger).
		ValidateStringParam(phone, "phone").
		WithTenantFilter().
		WithCondition("phone", "=", phone).
		Exists()
}

// buildQueryConditions 构建查询条件 - 使用 QueryBuilder 模式
func (r *UserRepositoryImpl) buildQueryConditions(qb *QueryBuilder[models.UserModel], params *repository.QueryParams) *QueryBuilder[models.UserModel] {
	// 基础条件 - 只处理通用字段
	if len(params.IDs) > 0 {
		qb = qb.WithInConditionInt64("id", params.IDs)
	}

	if params.TenantID != nil {
		qb = qb.WithCondition("tenant_id", "=", *params.TenantID)
	}

	if params.InternalAppID != nil {
		qb = qb.WithCondition("internal_app_id", "=", *params.InternalAppID)
	}

	if params.Status != nil {
		qb = qb.WithCondition("status", "=", params.Status.String())
	}

	if params.IsSystem != nil {
		qb = qb.WithCondition("is_system", "=", *params.IsSystem)
	}

	// 时间范围条件 - 通用字段
	if params.CreatedAtStart != nil {
		qb = qb.WithCondition("created_at", ">=", *params.CreatedAtStart)
	}

	if params.CreatedAtEnd != nil {
		qb = qb.WithCondition("created_at", "<=", *params.CreatedAtEnd)
	}

	if params.UpdatedAtStart != nil {
		qb = qb.WithCondition("updated_at", ">=", *params.UpdatedAtStart)
	}

	if params.UpdatedAtEnd != nil {
		qb = qb.WithCondition("updated_at", "<=", *params.UpdatedAtEnd)
	}

	// 软删除处理
	if !params.WithDeleted {
		qb = qb.WithCondition("deleted_at", "IS", nil)
	}

	// 用户特定的查询条件
	if params.Username != nil {
		qb = qb.WithCondition("username", "=", *params.Username)
	}

	if params.Email != nil {
		qb = qb.WithCondition("email", "=", *params.Email)
	}

	if params.Phone != nil {
		qb = qb.WithCondition("phone", "=", *params.Phone)
	}

	if params.Code != nil {
		qb = qb.WithCondition("code", "=", *params.Code)
	}

	if params.Name != nil {
		qb = qb.WithCondition("name", "=", *params.Name)
	}

	// 关联查询条件
	if params.DepartmentID != nil {
		qb = qb.WithCondition("department_id", "=", *params.DepartmentID)
	}

	if params.PositionID != nil {
		qb = qb.WithCondition("position_id", "=", *params.PositionID)
	}

	// 角色关联查询 - 需要手动处理，不使用 GORM 自动关联
	if params.RoleID != nil {
		// 这里需要手动查询，不使用 GORM 的 JOIN
		// 在应用层处理角色关联查询
	}

	// 关键词搜索
	if params.Keyword != nil && *params.Keyword != "" {
		searchFields := params.SearchFields
		if len(searchFields) == 0 {
			// 默认搜索字段
			searchFields = []string{"username", "real_name", "email", "phone"}
		}

		// 构建 OR 条件
		for i, field := range searchFields {
			if i == 0 {
				qb = qb.WithLikeCondition(field, *params.Keyword)
			} else {
				qb = qb.WithOrCondition(field, "LIKE", "%"+*params.Keyword+"%")
			}
		}
	}

	return qb
}
