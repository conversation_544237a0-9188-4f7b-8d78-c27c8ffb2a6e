package thirdparty

import (
	"context"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"net/http"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/pkg/jwt"
	"strings"
	"time"

	jwtlib "github.com/golang-jwt/jwt/v5"
	// "golang.org/x/oauth2"
	// "golang.org/x/oauth2/google"
)

// AppleValidator Apple登录验证器
type AppleValidator struct {
	logger     logiface.Logger
	clientID   string
	teamID     string
	keyID      string
	privateKey *rsa.PrivateKey
	jwtService *jwt.JWTService
}

// NewAppleValidator 创建Apple验证器
func NewAppleValidator(logger logiface.Logger, clientID, teamID, keyID, privateKeyPEM string, jwtService *jwt.JWTService) (*AppleValidator, error) {
	// 解析私钥
	block, _ := pem.Decode([]byte(privateKeyPEM))
	if block == nil {
		return nil, fmt.Errorf("failed to decode private key")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %w", err)
	}

	rsaPrivateKey, ok := privateKey.(*rsa.PrivateKey)
	if !ok {
		return nil, fmt.Errorf("private key is not RSA key")
	}

	return &AppleValidator{
		logger:     logger,
		clientID:   clientID,
		teamID:     teamID,
		keyID:      keyID,
		privateKey: rsaPrivateKey,
		jwtService: jwtService,
	}, nil
}

// GetProvider 获取提供商名称
func (v *AppleValidator) GetProvider() string {
	return "apple"
}

// ValidateToken 验证Apple Token
func (v *AppleValidator) ValidateToken(ctx context.Context, authorizationCode string) (map[string]interface{}, error) {
	v.logger.Info(ctx, "validating Apple token",
		logiface.String("client_id", v.clientID),
	)

	// 1. 生成客户端密钥
	clientSecret, err := v.generateClientSecret(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate client secret: %w", err)
	}

	// 2. 交换访问令牌
	accessToken, err := v.exchangeAccessToken(ctx, authorizationCode, clientSecret)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange access token: %w", err)
	}

	// 3. 验证ID Token
	userInfo, err := v.validateIDToken(ctx, accessToken)
	if err != nil {
		return nil, fmt.Errorf("failed to validate ID token: %w", err)
	}
	return userInfo, nil
}

// generateClientSecret 生成客户端密钥
func (v *AppleValidator) generateClientSecret(ctx context.Context) (string, error) {
	now := time.Now()

	// 创建JWT声明
	claims := map[string]interface{}{
		"iss": v.teamID,
		"iat": now.Unix(),
		"exp": now.Add(5 * time.Minute).Unix(),
		"aud": "https://appleid.apple.com",
		"sub": v.clientID,
	}

	// 创建JWT头部
	headers := map[string]interface{}{
		"kid": v.keyID,
	}

	// 使用JWTService生成自定义JWT
	return v.jwtService.GenerateCustomJWT(claims, headers, v.privateKey, jwtlib.SigningMethodRS256)
}

// exchangeAccessToken 交换访问令牌
func (v *AppleValidator) exchangeAccessToken(ctx context.Context, authorizationCode, clientSecret string) (string, error) {
	data := map[string]string{
		"client_id":     v.clientID,
		"client_secret": clientSecret,
		"code":          authorizationCode,
		"grant_type":    "authorization_code",
	}

	jsonData, _ := json.Marshal(data)
	req, err := http.NewRequestWithContext(ctx, "POST", "https://appleid.apple.com/auth/token", strings.NewReader(string(jsonData)))
	if err != nil {
		return "", err
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("Apple API error: %s", string(body))
	}

	var tokenResponse struct {
		AccessToken string `json:"access_token"`
		IDToken     string `json:"id_token"`
	}

	if err := json.Unmarshal(body, &tokenResponse); err != nil {
		return "", err
	}

	return tokenResponse.IDToken, nil
}

// validateIDToken 验证ID Token
func (v *AppleValidator) validateIDToken(ctx context.Context, idToken string) (map[string]interface{}, error) {
	// 解析JWT Token（不验证签名，因为Apple的密钥需要从Apple获取）
	parts := strings.Split(idToken, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("invalid JWT token format")
	}

	payload, err := base64.RawURLEncoding.DecodeString(parts[1])
	if err != nil {
		return nil, fmt.Errorf("failed to decode JWT payload: %w", err)
	}

	var claims map[string]interface{}
	if err := json.Unmarshal(payload, &claims); err != nil {
		return nil, fmt.Errorf("failed to parse JWT claims: %w", err)
	}

	// 验证基本字段
	if claims["iss"] != "https://appleid.apple.com" {
		return nil, fmt.Errorf("invalid issuer")
	}

	if claims["aud"] != v.clientID {
		return nil, fmt.Errorf("invalid audience")
	}

	// 验证过期时间
	if exp, ok := claims["exp"].(float64); ok {
		if time.Unix(int64(exp), 0).Before(time.Now()) {
			return nil, fmt.Errorf("token expired")
		}
	}

	return claims, nil
}

// WechatValidator 微信登录验证器
type WechatValidator struct {
	logger    logiface.Logger
	appID     string
	appSecret string
}

// NewWechatValidator 创建微信验证器
func NewWechatValidator(logger logiface.Logger, appID, appSecret string) *WechatValidator {
	return &WechatValidator{
		logger:    logger,
		appID:     appID,
		appSecret: appSecret,
	}
}

// GetProvider 获取提供商名称
func (v *WechatValidator) GetProvider() string {
	return "wechat"
}

// ValidateToken 验证微信Token
func (v *WechatValidator) ValidateToken(ctx context.Context, authorizationCode string) (map[string]interface{}, error) {
	v.logger.Info(ctx, "validating WeChat token",
		logiface.String("app_id", v.appID),
	)

	// 1. 使用授权码获取访问令牌
	accessToken, openID, err := v.getAccessToken(ctx, authorizationCode)
	if err != nil {
		return nil, fmt.Errorf("failed to get access token: %w", err)
	}

	// 2. 获取用户信息
	userInfo, err := v.getUserInfo(ctx, accessToken, openID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}

	return userInfo, nil
}

// getAccessToken 获取访问令牌
func (v *WechatValidator) getAccessToken(ctx context.Context, authorizationCode string) (string, string, error) {
	url := fmt.Sprintf("https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code",
		v.appID, v.appSecret, authorizationCode)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return "", "", err
	}

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", "", err
	}

	var response struct {
		AccessToken string `json:"access_token"`
		OpenID      string `json:"openid"`
		UnionID     string `json:"unionid"`
		ErrCode     int    `json:"errcode"`
		ErrMsg      string `json:"errmsg"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return "", "", err
	}

	if response.ErrCode != 0 {
		return "", "", fmt.Errorf("WeChat API error: %d - %s", response.ErrCode, response.ErrMsg)
	}

	return response.AccessToken, response.OpenID, nil
}

// getUserInfo 获取用户信息
func (v *WechatValidator) getUserInfo(ctx context.Context, accessToken, openID string) (map[string]interface{}, error) {
	url := fmt.Sprintf("https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN",
		accessToken, openID)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var userInfo map[string]interface{}
	if err := json.Unmarshal(body, &userInfo); err != nil {
		return nil, err
	}

	if errCode, ok := userInfo["errcode"].(float64); ok && errCode != 0 {
		errMsg, _ := userInfo["errmsg"].(string)
		return nil, fmt.Errorf("WeChat API error: %.0f - %s", errCode, errMsg)
	}

	return userInfo, nil
}

// GoogleValidator Google登录验证器 - 暂时禁用以解决依赖冲突
/*
type GoogleValidator struct {
	logger       logiface.Logger
	clientID     string
	clientSecret string
	config       *oauth2.Config
}

// NewGoogleValidator 创建Google验证器
func NewGoogleValidator(logger logiface.Logger, clientID, clientSecret, redirectURI string) *GoogleValidator {
	config := &oauth2.Config{
		ClientID:     clientID,
		ClientSecret: clientSecret,
		RedirectURL:  redirectURI,
		Scopes: []string{
			"https://www.googleapis.com/auth/userinfo.profile",
			"https://www.googleapis.com/auth/userinfo.email",
		},
		Endpoint: google.Endpoint,
	}

	return &GoogleValidator{
		logger:       logger,
		clientID:     clientID,
		clientSecret: clientSecret,
		config:       config,
	}
}

// GetProvider 获取提供商名称
func (v *GoogleValidator) GetProvider() string {
	return "google"
}

// ValidateToken 验证Google Token
func (v *GoogleValidator) ValidateToken(ctx context.Context, authorizationCode string) (map[string]interface{}, error) {
	v.logger.Info(ctx, "validating Google token",
		logiface.String("client_id", v.clientID),
	)

	// 1. 交换访问令牌
	token, err := v.config.Exchange(ctx, authorizationCode)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange access token: %w", err)
	}

	// 2. 获取用户信息
	userInfo, err := v.getUserInfo(ctx, token.AccessToken)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}

	return userInfo, nil
}

// getUserInfo 获取用户信息
func (v *GoogleValidator) getUserInfo(ctx context.Context, accessToken string) (map[string]interface{}, error) {
	url := "https://www.googleapis.com/oauth2/v2/userinfo"

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set(common.HeaderAuthorization, common.BearerTokenPrefix+accessToken)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("Google API error: %s", string(body))
	}

	var userInfo map[string]interface{}
	if err := json.Unmarshal(body, &userInfo); err != nil {
		return nil, err
	}

	return userInfo, nil
}
*/
