package thirdparty

import (
	"context"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/application/auth/dto"
	"gitee.com/heiyee/platforms/users/internal/domain/errors"
)

// ConfigService 第三方配置服务
type ConfigService struct {
	logger logiface.Logger
	config map[string]*ProviderConfig
}

// ProviderConfig 第三方提供商配置
type ProviderConfig struct {
	Enabled        bool   `json:"enabled"`
	ClientID       string `json:"client_id"`
	ClientSecret   string `json:"client_secret"`
	RedirectURI    string `json:"redirect_uri"`
	AutoCreateUser bool   `json:"auto_create_user"`
	RequireEmail   bool   `json:"require_email"`

	// Apple特有配置
	TeamID     string `json:"team_id,omitempty"`
	KeyID      string `json:"key_id,omitempty"`
	PrivateKey string `json:"private_key,omitempty"`

	// 微信特有配置
	AppID     string `json:"app_id,omitempty"`
	AppSecret string `json:"app_secret,omitempty"`
}

// NewConfigService 创建配置服务
func NewConfigService(logger logiface.Logger) *ConfigService {
	return &ConfigService{
		logger: logger,
		config: make(map[string]*ProviderConfig),
	}
}

// LoadConfig 加载配置
func (s *ConfigService) LoadConfig(config map[string]interface{}) error {
	s.logger.Info(context.Background(), "loading third party config")

	// Apple配置
	if appleConfig, ok := config["apple"].(map[string]interface{}); ok {
		s.config["apple"] = &ProviderConfig{
			Enabled:        s.getBool(appleConfig, "enabled", false),
			ClientID:       s.getString(appleConfig, "client_id", ""),
			RedirectURI:    s.getString(appleConfig, "redirect_uri", ""),
			AutoCreateUser: s.getBool(appleConfig, "auto_create_user", true),
			RequireEmail:   s.getBool(appleConfig, "require_email", false),
			TeamID:         s.getString(appleConfig, "team_id", ""),
			KeyID:          s.getString(appleConfig, "key_id", ""),
			PrivateKey:     s.getString(appleConfig, "private_key", ""),
		}
	}

	// 微信配置
	if wechatConfig, ok := config["wechat"].(map[string]interface{}); ok {
		s.config["wechat"] = &ProviderConfig{
			Enabled:        s.getBool(wechatConfig, "enabled", false),
			AppID:          s.getString(wechatConfig, "app_id", ""),
			AppSecret:      s.getString(wechatConfig, "app_secret", ""),
			RedirectURI:    s.getString(wechatConfig, "redirect_uri", ""),
			AutoCreateUser: s.getBool(wechatConfig, "auto_create_user", true),
			RequireEmail:   s.getBool(wechatConfig, "require_email", false),
		}
	}

	// Google配置
	if googleConfig, ok := config["google"].(map[string]interface{}); ok {
		s.config["google"] = &ProviderConfig{
			Enabled:        s.getBool(googleConfig, "enabled", false),
			ClientID:       s.getString(googleConfig, "client_id", ""),
			ClientSecret:   s.getString(googleConfig, "client_secret", ""),
			RedirectURI:    s.getString(googleConfig, "redirect_uri", ""),
			AutoCreateUser: s.getBool(googleConfig, "auto_create_user", true),
			RequireEmail:   s.getBool(googleConfig, "require_email", true),
		}
	}

	s.logger.Info(context.Background(), "third party config loaded",
		logiface.Int("provider_count", len(s.config)),
	)

	return nil
}

// GetConfig 获取第三方配置
func (s *ConfigService) GetConfig(ctx context.Context, provider string) (*dto.ThirdPartyConfigResponseDTO, error) {
	s.logger.Info(ctx, "getting third party config",
		logiface.String("provider", provider),
	)

	config, exists := s.config[provider]
	if !exists {
		return nil, errors.NewBusinessError(errors.CodeThirdPartyPlatformNotSupported, "不支持的第三方平台")
	}

	return &dto.ThirdPartyConfigResponseDTO{
		Provider:       provider,
		Enabled:        config.Enabled,
		ClientID:       s.getClientID(config),
		RedirectURI:    config.RedirectURI,
		AutoCreateUser: config.AutoCreateUser,
		RequireEmail:   config.RequireEmail,
	}, nil
}

// IsEnabled 检查第三方平台是否启用
func (s *ConfigService) IsEnabled(ctx context.Context, provider string) (bool, error) {
	config, exists := s.config[provider]
	if !exists {
		return false, errors.NewBusinessError(errors.CodeThirdPartyPlatformNotSupported, "不支持的第三方平台")
	}

	return config.Enabled, nil
}

// GetProviderConfig 获取提供商配置
func (s *ConfigService) GetProviderConfig(provider string) (*ProviderConfig, error) {
	config, exists := s.config[provider]
	if !exists {
		return nil, errors.NewBusinessError(errors.CodeThirdPartyPlatformNotSupported, "不支持的第三方平台")
	}

	return config, nil
}

// getClientID 获取客户端ID
func (s *ConfigService) getClientID(config *ProviderConfig) string {
	if config.ClientID != "" {
		return config.ClientID
	}
	return config.AppID
}

// getString 安全获取字符串值
func (s *ConfigService) getString(config map[string]interface{}, key string, defaultValue string) string {
	if value, ok := config[key].(string); ok {
		return value
	}
	return defaultValue
}

// getBool 安全获取布尔值
func (s *ConfigService) getBool(config map[string]interface{}, key string, defaultValue bool) bool {
	if value, ok := config[key].(bool); ok {
		return value
	}
	return defaultValue
}

// ValidateConfig 验证配置
func (s *ConfigService) ValidateConfig() error {
	s.logger.Info(context.Background(), "validating third party config")

	for provider, config := range s.config {
		if !config.Enabled {
			continue
		}

		switch provider {
		case "apple":
			if err := s.validateAppleConfig(config); err != nil {
				return errors.NewThirdPartyError("Apple配置验证", "Apple配置验证失败")
			}
		case "wechat":
			if err := s.validateWechatConfig(config); err != nil {
				return errors.NewThirdPartyError("微信配置验证", "微信配置验证失败")
			}
		case "google":
			if err := s.validateGoogleConfig(config); err != nil {
				return errors.NewThirdPartyError("Google配置验证", "Google配置验证失败")
			}
		}
	}

	s.logger.Info(context.Background(), "third party config validation passed")
	return nil
}

// validateAppleConfig 验证Apple配置
func (s *ConfigService) validateAppleConfig(config *ProviderConfig) error {
	if config.ClientID == "" {
		return errors.NewBusinessError(errors.CodeThirdPartyConfigError, "client_id不能为空")
	}
	if config.TeamID == "" {
		return errors.NewBusinessError(errors.CodeThirdPartyConfigError, "team_id不能为空")
	}
	if config.KeyID == "" {
		return errors.NewBusinessError(errors.CodeThirdPartyConfigError, "key_id不能为空")
	}
	if config.PrivateKey == "" {
		return errors.NewBusinessError(errors.CodeThirdPartyConfigError, "private_key不能为空")
	}
	if config.RedirectURI == "" {
		return errors.NewBusinessError(errors.CodeThirdPartyConfigError, "redirect_uri不能为空")
	}
	return nil
}

// validateWechatConfig 验证微信配置
func (s *ConfigService) validateWechatConfig(config *ProviderConfig) error {
	if config.AppID == "" {
		return errors.NewBusinessError(errors.CodeThirdPartyConfigError, "app_id不能为空")
	}
	if config.AppSecret == "" {
		return errors.NewBusinessError(errors.CodeThirdPartyConfigError, "app_secret不能为空")
	}
	if config.RedirectURI == "" {
		return errors.NewBusinessError(errors.CodeThirdPartyConfigError, "redirect_uri不能为空")
	}
	return nil
}

// validateGoogleConfig 验证Google配置
func (s *ConfigService) validateGoogleConfig(config *ProviderConfig) error {
	if config.ClientID == "" {
		return errors.NewBusinessError(errors.CodeThirdPartyConfigError, "client_id不能为空")
	}
	if config.ClientSecret == "" {
		return errors.NewBusinessError(errors.CodeThirdPartyConfigError, "client_secret不能为空")
	}
	if config.RedirectURI == "" {
		return errors.NewBusinessError(errors.CodeThirdPartyConfigError, "redirect_uri不能为空")
	}
	return nil
}
