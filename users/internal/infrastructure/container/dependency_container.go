package container

import (
	"context"
	"fmt"

	commonDB "gitee.com/heiyee/platforms/pkg/db"
	"gitee.com/heiyee/platforms/pkg/logiface"
	appService "gitee.com/heiyee/platforms/users/internal/application/application/service"
	authService "gitee.com/heiyee/platforms/users/internal/application/auth/service"
	fileUploadService "gitee.com/heiyee/platforms/users/internal/application/fileupload/service"
	idGeneratorService "gitee.com/heiyee/platforms/users/internal/application/idgenerator/service"
	oauthService "gitee.com/heiyee/platforms/users/internal/application/oauth/service"
	userAppService "gitee.com/heiyee/platforms/users/internal/application/user/service"
	verificationService "gitee.com/heiyee/platforms/users/internal/application/verification/service"
	appDomainService "gitee.com/heiyee/platforms/users/internal/domain/application/service"
	authRepo "gitee.com/heiyee/platforms/users/internal/domain/auth/repository"
	fileUploadRepo "gitee.com/heiyee/platforms/users/internal/domain/fileupload/repository"
	storageService "gitee.com/heiyee/platforms/users/internal/domain/fileupload/service/storage"
	idGeneratorRepo "gitee.com/heiyee/platforms/users/internal/domain/idgenerator/repository"
	oauthRepo "gitee.com/heiyee/platforms/users/internal/domain/oauth/repository"
	userEntity "gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	verificationRepo "gitee.com/heiyee/platforms/users/internal/domain/verification/repository"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/adapter"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/captcha"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/external"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/mfa"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence"

	"gitee.com/heiyee/platforms/users/internal/infrastructure/sms"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/storage"
	"gitee.com/heiyee/platforms/users/internal/interfaces/http/handlers"
	idgenerator "gitee.com/heiyee/platforms/users/internal/interfaces/http/idgenerator"
	"gitee.com/heiyee/platforms/users/pkg/config"
	idGeneratorPkg "gitee.com/heiyee/platforms/users/pkg/id_generator"
	"gitee.com/heiyee/platforms/users/pkg/jwt"

	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
)

// DependencyContainer 依赖注入容器 - 使用最佳实践设计
type DependencyContainer struct {
	// 基础设施层
	infrastructure struct {
		logger             logiface.Logger
		db                 *gorm.DB
		jwtService         *jwt.JWTService
		captchaService     *captcha.CaptchaService
		smsService         *sms.SMSService
		emailServiceClient *external.EmailServiceClient
		mfaService         *mfa.MFAService
		storageService     storageService.StorageService
	}

	// 仓储层
	repositories struct {
		userRepo                  repository.UserRepository
		tenantRepo                repository.TenantRepository
		roleRepo                  repository.RoleRepository
		permissionRepo            repository.PermissionRepository
		rolePermissionRepo        repository.RolePermissionRepository
		departmentRepo            repository.DepartmentRepository
		positionRepo              repository.PositionRepository
		resourceRepo              repository.ResourceRepository
		userExtRepo               repository.UserExtRepository
		sequenceRepo              idGeneratorRepo.SequenceRepository
		appConfigRepo             repository.AppConfigRepository
		thirdPartyAccountRepo     repository.ThirdPartyAccountRepository
		authRepo                  authRepo.AuthRepository
		passwordRepo              repository.PasswordRepository
		fileUploadConfigRepo      fileUploadRepo.FileUploadConfigRepository
		fileRecordRepo            fileUploadRepo.FileRecordRepository
		uploadTokenRepo           fileUploadRepo.UploadTokenRepository
		verificationTokenRepo     verificationRepo.VerificationTokenRepository
		verificationConfigRepo    verificationRepo.VerificationConfigRepository
		smsTemplateRepo           verificationRepo.SMSTemplateRepository
		applicationRepo           repository.ApplicationRepository
		oauthChannelConfigRepo    oauthRepo.OAuthChannelConfigRepository
		tenantOAuthChannelRepo    oauthRepo.TenantOAuthChannelRepository
		resourceRelationRepo      repository.ResourceRelationRepository
		resourceAppAssignmentRepo repository.ResourceAppAssignmentRepository
	}

	// 应用层
	applications struct {
		simpleIDGeneratorService *idGeneratorService.SimpleIDGenerator
		appIDFactory             *appDomainService.AppIDFactory
		userService              *userAppService.UserApplicationService
		tenantService            *userAppService.TenantApplicationService
		appService               *appService.ApplicationApplicationService
		roleService              *userAppService.RoleApplicationService
		permissionService        *userAppService.PermissionApplicationService
		departmentService        *userAppService.DepartmentApplicationService
		positionService          *userAppService.PositionApplicationService
		resourceService          *userAppService.ResourceApplicationService
		resourceRelationService  *userAppService.ResourceRelationApplicationService
		userProfileService       *userAppService.UserProfileApplicationService
		appConfigService         *userAppService.AppConfigService
		menuService              *userAppService.MenuApplicationService
		permissionCheckService   userAppService.PermissionCheckService
		authService              *authService.AuthApplicationService
		registerService          *authService.RegisterApplicationService
		thirdPartyService        *authService.ThirdPartyApplicationService
		thirdPartyConfigService  *authService.ThirdPartyConfigService
		tenantLookupService      *authService.TenantLookupService
		passwordService          *userAppService.PasswordService
		fileUploadService        *fileUploadService.FileUploadApplicationService
		oauthChannelService      *oauthService.OAuthChannelApplicationService
		verificationService      *verificationService.VerificationApplicationService
	}

	// 接口层 - HTTP处理器
	handlers struct {
		userHandler             *handlers.UserHandler
		userProfileHandler      *handlers.UserProfileHandler
		roleHandler             *handlers.RoleHandler
		permissionHandler       *handlers.PermissionHandler
		resourceHandler         *handlers.ResourceHandler
		resourceRelationHandler *handlers.ResourceRelationHandler
		departmentHandler       *handlers.DepartmentHandler
		positionHandler     *handlers.PositionHandler
		tenantHandler       *handlers.TenantHandler
		appConfigHandler    *handlers.AppConfigHandler
		menuHandler         *handlers.MenuHandler
		authHandler         *handlers.AuthHandler
		registerHandler     *handlers.RegisterHandler
		thirdPartyHandler   *handlers.ThirdPartyHandler
		passwordHandler     *handlers.PasswordHandler
		fileUploadHandler   *handlers.FileUploadHandler
		oauthChannelHandler *handlers.OAuthChannelHandler
		verificationHandler *handlers.VerificationHandler
		applicationHandler  *handlers.ApplicationHandler
		idGeneratorHandler  *idgenerator.Handler
	}

	// 领域层
	domain struct {
		entityFactory *userEntity.EntityFactory
		idGenerator   *idGeneratorPkg.IDGenerator
	}

	// 应用配置
	appConfig *config.AppConfig
}

// NewDependencyContainer 创建依赖注入容器（只做结构体初始化，不做资源连接）
func NewDependencyContainer(appConfig *config.AppConfig, logger logiface.Logger) *DependencyContainer {
	container := &DependencyContainer{}
	// 保存配置和日志器，供后续初始化使用
	container.infrastructure.logger = logger
	// 将 appConfig 保存到容器中，供 Initialize 方法使用
	container.appConfig = appConfig
	return container
}

// Initialize 初始化容器
func (c *DependencyContainer) Initialize(ctx context.Context) error {
	// 1. 初始化基础设施
	if err := c.initInfrastructure(ctx, c.appConfig); err != nil {
		return fmt.Errorf("failed to initialize infrastructure: %w", err)
	}

	// 2. 初始化仓储层
	if err := c.initRepositories(ctx); err != nil {
		return fmt.Errorf("failed to initialize repositories: %w", err)
	}

	// 3. 初始化领域层（需要在应用层之前初始化）
	if err := c.initDomain(ctx); err != nil {
		return fmt.Errorf("failed to initialize domain: %w", err)
	}

	// 4. 初始化应用层
	if err := c.initApplications(ctx); err != nil {
		return fmt.Errorf("failed to initialize applications: %w", err)
	}

	// 5. 初始化接口层 - HTTP处理器
	if err := c.initHandlers(ctx); err != nil {
		return fmt.Errorf("failed to initialize handlers: %w", err)
	}

	c.infrastructure.logger.Info(ctx, "Dependency container initialized successfully")
	return nil
}

// initInfrastructure 初始化基础设施层
func (c *DependencyContainer) initInfrastructure(ctx context.Context, appConfig *config.AppConfig) error {
	// 初始化数据库连接
	gormLogger := commonDB.NewGormLoggerAdapter(c.infrastructure.logger, gormlogger.Info)
	c.infrastructure.db = commonDB.NewDB(&appConfig.Database.MySQL, gormLogger)

	// 初始化JWT服务
	c.infrastructure.jwtService = jwt.NewJWTServiceWithLogger(
		c.infrastructure.logger,
		appConfig.JWT.Secret,
		appConfig.GetJWTAccessTokenTTL(),
		appConfig.GetJWTRefreshTokenTTL(),
	)

	// 初始化验证码服务
	c.infrastructure.captchaService = captcha.NewCaptchaService()

	// 初始化短信服务
	c.infrastructure.smsService = sms.NewSMSService(
		[]sms.SMSProvider{}, // 暂时为空，后续添加短信提供商
		c.infrastructure.logger,
		&sms.SMSConfig{
			DefaultProvider: appConfig.SMS.DefaultProvider,
			RetryCount:      appConfig.SMS.RetryCount,
			RetryInterval:   appConfig.SMS.RetryInterval,
			Timeout:         appConfig.SMS.Timeout,
			EnableFailover:  appConfig.SMS.EnableFailover,
			RateLimitPerMin: appConfig.SMS.RateLimitPerMin,
			MaxConcurrent:   appConfig.SMS.MaxConcurrent,
		},
	)

	// 初始化邮件服务客户端 (gRPC)
	c.infrastructure.emailServiceClient = external.NewEmailServiceClient(
		c.infrastructure.logger,
	)

	// 初始化MFA服务
	c.infrastructure.mfaService = mfa.NewMFAService(
		[]mfa.MFAProvider{}, // 暂时为空，后续添加MFA提供商
		c.infrastructure.logger,
		&mfa.MFAConfig{
			DefaultProvider:   appConfig.MFA.DefaultProvider,
			DefaultCodeLength: appConfig.MFA.CodeLength,
			DefaultExpiryTime: appConfig.MFA.CodeExpiry,
			EnableFailover:    appConfig.MFA.EnableFailover,
		},
	)

	// 初始化存储服务
	if appConfig.Upload.OSS.Endpoint != "" {
		// 使用OSS存储
		ossStorageService, err := storage.NewOSSStorageService(
			appConfig.Upload.OSS.Endpoint,
			appConfig.Upload.OSS.Region,
			appConfig.Upload.OSS.Bucket,
			appConfig.Upload.OSS.AccessKeyID,
			appConfig.Upload.OSS.AccessKeySecret,
			appConfig.Upload.OSS.PathPrefix,
			c.infrastructure.logger,
		)
		if err != nil {
			c.infrastructure.logger.Error(ctx, "failed to create OSS storage service", logiface.Error(err))
			return fmt.Errorf("创建OSS存储服务失败: %w", err)
		}
		c.infrastructure.storageService = ossStorageService
		c.infrastructure.logger.Info(ctx, "OSS storage service initialized")
	} else {
		// 使用本地存储
		c.infrastructure.storageService = storage.NewLocalStorageService("./uploads", c.infrastructure.logger)
		c.infrastructure.logger.Info(ctx, "Local storage service initialized")
	}

	c.infrastructure.logger.Info(ctx, "Infrastructure initialized")
	return nil
}

// initRepositories 初始化仓储层
func (c *DependencyContainer) initRepositories(ctx context.Context) error {
	db := c.infrastructure.db
	logger := c.infrastructure.logger

	// 初始化所有仓储
	c.repositories.userRepo = persistence.NewUserRepository(db, logger)
	c.repositories.tenantRepo = persistence.NewTenantRepository(db, logger)
	c.repositories.roleRepo = persistence.NewRoleRepository(db, logger)
	c.repositories.permissionRepo = persistence.NewPermissionRepository(db, logger)
	c.repositories.rolePermissionRepo = persistence.NewRolePermissionRepository(db)
	c.repositories.departmentRepo = persistence.NewDepartmentRepository(db, logger)
	c.repositories.positionRepo = persistence.NewPositionRepository(db, logger)
	c.repositories.resourceRepo = persistence.NewResourceRepository(db, logger)
	c.repositories.userExtRepo = persistence.NewUserExtRepository(db, logger)
	c.repositories.sequenceRepo = persistence.NewSequenceRepositoryImpl(db)
	c.repositories.appConfigRepo = persistence.NewAppConfigRepository(db, logger)
	c.repositories.thirdPartyAccountRepo = persistence.NewThirdPartyAccountRepositoryImpl(db, logger)
	c.repositories.authRepo = persistence.NewAuthRepository(db, logger)
	c.repositories.passwordRepo = persistence.NewPasswordRepository(db)
	c.repositories.fileUploadConfigRepo = persistence.NewFileUploadConfigRepositoryImpl(db, logger)
	c.repositories.fileRecordRepo = persistence.NewFileRecordRepositoryImpl(db, logger)
	c.repositories.uploadTokenRepo = persistence.NewUploadTokenRepositoryImpl(db)

	// 初始化验证相关仓储
	c.repositories.verificationTokenRepo = persistence.NewVerificationTokenRepository(db, logger)
	c.repositories.verificationConfigRepo = persistence.NewVerificationConfigRepository(db, logger)
	c.repositories.smsTemplateRepo = persistence.NewSMSTemplateRepository(db, logger)

	// 初始化OAuth相关仓储
	c.repositories.oauthChannelConfigRepo = persistence.NewOAuthChannelConfigRepository(db, logger)
	c.repositories.tenantOAuthChannelRepo = persistence.NewTenantOAuthChannelRepository(db, logger)

	// 初始化资源相关仓储
	c.repositories.resourceRelationRepo = persistence.NewResourceRelationRepository(db, logger)
	c.repositories.resourceAppAssignmentRepo = persistence.NewResourceAppAssignmentRepository(db, logger)

	// 初始化应用相关仓储
	c.repositories.applicationRepo = persistence.NewApplicationRepositoryImpl(db, logger)

	c.infrastructure.logger.Info(ctx, "Repositories initialized")
	return nil
}

// initApplications 初始化应用层
func (c *DependencyContainer) initApplications(ctx context.Context) error {
	// 初始化应用服务
	if err := c.initApplicationServices(ctx); err != nil {
		return fmt.Errorf("failed to initialize application services: %w", err)
	}

	c.infrastructure.logger.Info(ctx, "Applications initialized")
	return nil
}

// initIDGeneratorService 初始化ID生成器服务
func (c *DependencyContainer) initIDGeneratorService(ctx context.Context) error {
	// 创建ID生成器服务
	c.applications.simpleIDGeneratorService = idGeneratorService.NewSimpleIDGenerator(
		c.repositories.sequenceRepo,
		c.infrastructure.logger,
	)
	c.infrastructure.logger.Info(ctx, "ID generator service initialized")
	return nil
}

// initApplicationServices 初始化应用服务
func (c *DependencyContainer) initApplicationServices(ctx context.Context) error {
	// 初始化租户配置服务（需要在其他服务之前初始化）
	c.applications.appConfigService = userAppService.NewAppConfigService(
		c.repositories.appConfigRepo,
		c.repositories.applicationRepo,
		c.infrastructure.logger,
	)

	// 初始化用户相关应用服务
	c.applications.userService = userAppService.NewUserApplicationService(
		c.repositories.userRepo,
		c.applications.appConfigService,
		c.infrastructure.jwtService,
		c.domain.entityFactory,
		c.repositories.applicationRepo,
		c.infrastructure.logger,
	)

	c.applications.tenantService = userAppService.NewTenantApplicationService(
		c.repositories.tenantRepo,
		c.repositories.userRepo,
		c.domain.entityFactory,
		c.infrastructure.logger,
	)

	// 初始化应用ID生成器
	c.applications.appIDFactory = appDomainService.NewAppIDFactory(
		appDomainService.NewAppIDGenerator(),
		c.domain.idGenerator,
		c.infrastructure.logger,
	)

	// 初始化权限检查服务（需要在应用服务之前初始化）
	c.applications.permissionCheckService = userAppService.NewPermissionCheckService(
		c.repositories.rolePermissionRepo,
		c.infrastructure.logger,
	)

	// 初始化应用服务
	c.applications.appService = appService.NewApplicationApplicationService(
		c.repositories.applicationRepo,
		c.applications.appIDFactory,
		c.applications.permissionCheckService,
		c.infrastructure.logger,
	)

	c.applications.roleService = userAppService.NewRoleApplicationService(
		c.repositories.roleRepo,
		c.repositories.permissionRepo,
		c.repositories.userRepo,
		c.domain.entityFactory,
		c.infrastructure.logger,
	)

	c.applications.permissionService = userAppService.NewPermissionApplicationService(
		c.repositories.permissionRepo,
		c.domain.entityFactory,
		c.infrastructure.logger,
	)

	c.applications.departmentService = userAppService.NewDepartmentApplicationService(
		c.repositories.departmentRepo,
		c.repositories.userRepo,
		c.domain.entityFactory,
		c.infrastructure.logger,
	)

	c.applications.positionService = userAppService.NewPositionApplicationService(
		c.repositories.positionRepo,
		c.repositories.departmentRepo,
		c.repositories.userRepo,
		c.domain.entityFactory,
		c.infrastructure.logger,
	)

	c.applications.resourceService = userAppService.NewResourceApplicationService(
		c.repositories.resourceRepo,
		c.repositories.resourceRelationRepo,
		c.repositories.resourceAppAssignmentRepo,
		c.repositories.permissionRepo,
		c.repositories.userRepo,
		c.domain.entityFactory,
		c.infrastructure.logger,
	)

	c.applications.resourceRelationService = userAppService.NewResourceRelationApplicationService(
		c.infrastructure.logger,
		c.repositories.resourceRelationRepo,
		c.repositories.resourceRepo,
	)

	c.applications.userProfileService = userAppService.NewUserProfileApplicationService(
		c.repositories.userExtRepo,
		c.repositories.userRepo,
		c.infrastructure.logger,
	)

	// 初始化菜单服务
	c.applications.menuService = userAppService.NewMenuApplicationService(
		c.infrastructure.logger,
		c.repositories.resourceRepo,
		c.repositories.userRepo,
		c.applications.permissionCheckService,
		c.domain.entityFactory,
	)

	// 初始化验证服务（需要在注册服务之前初始化）
	c.applications.verificationService = verificationService.NewVerificationApplicationService(
		c.repositories.verificationTokenRepo,
		c.repositories.verificationConfigRepo,
		c.repositories.smsTemplateRepo,
		c.repositories.userRepo,
		c.repositories.tenantRepo,
		c.infrastructure.smsService,
		c.infrastructure.emailServiceClient,
		c.infrastructure.mfaService,
		c.infrastructure.logger,
		c.appConfig,
	)

	// 初始化认证相关服务
	c.applications.tenantLookupService = authService.NewTenantLookupService(
		c.infrastructure.logger,
		c.repositories.tenantRepo,
	)

	c.applications.thirdPartyConfigService = authService.NewThirdPartyConfigService(
		c.infrastructure.logger,
	)

	c.applications.authService = authService.NewAuthApplicationService(
		c.infrastructure.logger,
		c.repositories.authRepo,
		c.repositories.userRepo,
		c.infrastructure.jwtService,
		c.applications.tenantLookupService,
		adapter.NewCaptchaServiceAdapter(c.infrastructure.captchaService),
		c.applications.verificationService,
		c.applications.appConfigService,
		c.applications.userService,
		authService.NewDefaultAuthServiceConfig(), // 使用默认配置
	)

	c.applications.registerService = authService.NewRegisterApplicationService(
		c.infrastructure.logger,
		c.applications.userService,
		c.applications.tenantService,
		c.applications.appConfigService,
		c.applications.verificationService,
		adapter.NewCaptchaServiceAdapter(c.infrastructure.captchaService),
		c.appConfig, // 传递appConfig
	)

	c.applications.thirdPartyService = authService.NewThirdPartyApplicationService(
		c.infrastructure.logger,
		c.applications.userService,
		c.infrastructure.jwtService,
		c.applications.thirdPartyConfigService,
		c.repositories.thirdPartyAccountRepo,
	)

	// 初始化密码服务
	c.applications.passwordService = userAppService.NewPasswordService(
		c.repositories.userRepo,
		c.repositories.passwordRepo,
		c.infrastructure.logger,
	)

	// 初始化文件上传服务
	c.applications.fileUploadService = fileUploadService.NewFileUploadApplicationService(
		c.repositories.fileUploadConfigRepo,
		c.repositories.fileRecordRepo,
		c.repositories.uploadTokenRepo,
		c.infrastructure.storageService,
		c.domain.idGenerator, // 注入ID生成器
		c.infrastructure.logger,
	)

	// 初始化OAuth渠道服务
	c.applications.oauthChannelService = oauthService.NewOAuthChannelApplicationService(
		c.infrastructure.logger,
		c.repositories.oauthChannelConfigRepo,
		c.repositories.tenantOAuthChannelRepo,
		c.repositories.tenantRepo,
		c.applications.userService,
		c.repositories.thirdPartyAccountRepo,
		c.infrastructure.jwtService,
	)

	return nil
}

// initDomain 初始化领域层
func (c *DependencyContainer) initDomain(ctx context.Context) error {
	// 1. 初始化ID生成器服务（先初始化服务）
	if err := c.initIDGeneratorService(ctx); err != nil {
		return fmt.Errorf("failed to initialize ID generator service: %w", err)
	}

	// 2. 初始化ID生成器（包装器）
	if err := c.initIDGenerator(ctx); err != nil {
		return fmt.Errorf("failed to initialize ID generator: %w", err)
	}

	// 3. 初始化实体工厂
	if err := c.initEntityFactory(ctx); err != nil {
		return fmt.Errorf("failed to initialize entity factory: %w", err)
	}

	c.infrastructure.logger.Info(ctx, "Domain layer initialized")
	return nil
}

// initHandlers 初始化HTTP处理器
func (c *DependencyContainer) initHandlers(ctx context.Context) error {
	logger := c.infrastructure.logger

	// 初始化所有HTTP处理器
	c.handlers.userHandler = handlers.NewUserHandler(logger, c.applications.userService)
	c.handlers.userProfileHandler = handlers.NewUserProfileHandler(c.applications.userProfileService)
	c.handlers.roleHandler = handlers.NewRoleHandler(logger, c.applications.roleService)
	c.handlers.permissionHandler = handlers.NewPermissionHandler(logger, c.applications.permissionService)
	c.handlers.resourceHandler = handlers.NewResourceHandler(logger, c.applications.resourceService)
	c.handlers.resourceRelationHandler = handlers.NewResourceRelationHandler(logger, c.applications.resourceRelationService)
	c.handlers.departmentHandler = handlers.NewDepartmentHandler(logger, c.applications.departmentService)
	c.handlers.positionHandler = handlers.NewPositionHandler(logger, c.applications.positionService)
	c.handlers.tenantHandler = handlers.NewTenantHandler(logger, c.applications.tenantService, c.applications.appConfigService)
	c.handlers.appConfigHandler = handlers.NewAppConfigHandler(logger, c.applications.appConfigService, c.applications.fileUploadService, c.applications.appService)
	c.handlers.menuHandler = handlers.NewMenuHandler(logger, c.applications.menuService)
	c.handlers.authHandler = handlers.NewAuthHandler(logger, c.applications.authService)
	c.handlers.registerHandler = handlers.NewRegisterHandler(logger, c.applications.registerService)
	c.handlers.thirdPartyHandler = handlers.NewThirdPartyHandler(logger, c.applications.thirdPartyService)
	c.handlers.passwordHandler = handlers.NewPasswordHandler(logger, c.applications.passwordService, c.applications.appConfigService)

	c.handlers.fileUploadHandler = handlers.NewFileUploadHandler(c.applications.fileUploadService, logger)

	// 初始化OAuth渠道处理器
	c.handlers.oauthChannelHandler = handlers.NewOAuthChannelHandler(logger, c.applications.oauthChannelService, c.applications.tenantLookupService)

	// 初始化验证处理器
	c.handlers.verificationHandler = handlers.NewVerificationHandler(c.applications.verificationService, logger)

	// 初始化应用处理器
	c.handlers.applicationHandler = handlers.NewApplicationHandler(c.applications.appService)

	// 初始化ID生成器处理器
	c.handlers.idGeneratorHandler = idgenerator.NewHandler(c.applications.simpleIDGeneratorService, c.repositories.sequenceRepo)

	c.infrastructure.logger.Info(ctx, "HTTP handlers initialized")
	return nil
}

// initIDGenerator 初始化ID生成器
func (c *DependencyContainer) initIDGenerator(ctx context.Context) error {
	// 创建ID生成器
	c.domain.idGenerator = idGeneratorPkg.NewIDGenerator(c.applications.simpleIDGeneratorService)

	c.infrastructure.logger.Info(ctx, "ID generator initialized")
	return nil
}

// initEntityFactory 初始化实体工厂
func (c *DependencyContainer) initEntityFactory(ctx context.Context) error {
	// 创建实体工厂，注入ID生成器
	c.domain.entityFactory = userEntity.NewEntityFactory(c.domain.idGenerator)

	c.infrastructure.logger.Info(ctx, "Entity factory initialized")
	return nil
}

// GetEntityFactory 获取实体工厂
func (c *DependencyContainer) GetEntityFactory() *userEntity.EntityFactory {
	return c.domain.entityFactory
}

// GetIDGenerator 获取ID生成器
func (c *DependencyContainer) GetIDGenerator() *idGeneratorPkg.IDGenerator {
	return c.domain.idGenerator
}

// GetUserService 获取用户服务
func (c *DependencyContainer) GetUserService() *userAppService.UserApplicationService {
	return c.applications.userService
}

// GetUserAppService 获取用户应用服务（别名方法）
func (c *DependencyContainer) GetUserAppService() *userAppService.UserApplicationService {
	return c.applications.userService
}

// GetTenantService 获取租户服务
func (c *DependencyContainer) GetTenantService() *userAppService.TenantApplicationService {
	return c.applications.tenantService
}

func (c *DependencyContainer) GetAppService() *appService.ApplicationApplicationService {
	return c.applications.appService
}

// GetRoleService 获取角色服务
func (c *DependencyContainer) GetRoleService() *userAppService.RoleApplicationService {
	return c.applications.roleService
}

// GetPermissionService 获取权限服务
func (c *DependencyContainer) GetPermissionService() *userAppService.PermissionApplicationService {
	return c.applications.permissionService
}

// GetDepartmentService 获取部门服务
func (c *DependencyContainer) GetDepartmentService() *userAppService.DepartmentApplicationService {
	return c.applications.departmentService
}

// GetPositionService 获取职位服务
func (c *DependencyContainer) GetPositionService() *userAppService.PositionApplicationService {
	return c.applications.positionService
}

// GetResourceService 获取资源服务
func (c *DependencyContainer) GetResourceService() *userAppService.ResourceApplicationService {
	return c.applications.resourceService
}

// GetUserProfileService 获取用户档案服务
func (c *DependencyContainer) GetUserProfileService() *userAppService.UserProfileApplicationService {
	return c.applications.userProfileService
}

// GetAppConfigService 获取应用配置服务（原租户配置服务）
func (c *DependencyContainer) GetAppConfigService() *userAppService.AppConfigService {
	return c.applications.appConfigService
}

// GetAuthService 获取认证服务
func (c *DependencyContainer) GetAuthService() *authService.AuthApplicationService {
	return c.applications.authService
}

// GetRegisterService 获取注册服务
func (c *DependencyContainer) GetRegisterService() *authService.RegisterApplicationService {
	return c.applications.registerService
}

// GetThirdPartyService 获取第三方服务
func (c *DependencyContainer) GetThirdPartyService() *authService.ThirdPartyApplicationService {
	return c.applications.thirdPartyService
}

// GetTenantLookupService 获取租户查找服务
func (c *DependencyContainer) GetTenantLookupService() *authService.TenantLookupService {
	return c.applications.tenantLookupService
}

// GetPasswordService 获取密码服务
func (c *DependencyContainer) GetPasswordService() *userAppService.PasswordService {
	return c.applications.passwordService
}

// GetFileUploadService 获取文件上传服务
func (c *DependencyContainer) GetFileUploadService() *fileUploadService.FileUploadApplicationService {
	return c.applications.fileUploadService
}

// GetVerificationService 获取验证服务
func (c *DependencyContainer) GetVerificationService() *verificationService.VerificationApplicationService {
	return c.applications.verificationService
}

// GetUserRepo 获取用户仓储
func (c *DependencyContainer) GetUserRepo() repository.UserRepository {
	return c.repositories.userRepo
}

// GetTenantRepo 获取租户仓储
func (c *DependencyContainer) GetTenantRepo() repository.TenantRepository {
	return c.repositories.tenantRepo
}

// GetVerificationTokenRepo 获取验证令牌仓储
func (c *DependencyContainer) GetVerificationTokenRepo() verificationRepo.VerificationTokenRepository {
	return c.repositories.verificationTokenRepo
}

// GetJWTService 获取JWT服务
func (c *DependencyContainer) GetJWTService() *jwt.JWTService {
	return c.infrastructure.jwtService
}

// GetDB 获取数据库连接
func (c *DependencyContainer) GetDB() *gorm.DB {
	return c.infrastructure.db
}

// GetLogger 获取日志器
func (c *DependencyContainer) GetLogger() logiface.Logger {
	return c.infrastructure.logger
}

// GetIDGeneratorService 获取ID生成器服务
func (c *DependencyContainer) GetIDGeneratorService() *idGeneratorService.SimpleIDGenerator {
	return c.applications.simpleIDGeneratorService
}

// GetUserHandler 获取用户处理器
func (c *DependencyContainer) GetUserHandler() *handlers.UserHandler {
	return c.handlers.userHandler
}

// GetUserProfileHandler 获取用户档案处理器
func (c *DependencyContainer) GetUserProfileHandler() *handlers.UserProfileHandler {
	return c.handlers.userProfileHandler
}

// GetRoleHandler 获取角色处理器
func (c *DependencyContainer) GetRoleHandler() *handlers.RoleHandler {
	return c.handlers.roleHandler
}

// GetPermissionHandler 获取权限处理器
func (c *DependencyContainer) GetPermissionHandler() *handlers.PermissionHandler {
	return c.handlers.permissionHandler
}

// GetResourceHandler 获取资源处理器
func (c *DependencyContainer) GetResourceHandler() *handlers.ResourceHandler {
	return c.handlers.resourceHandler
}

// GetResourceRelationHandler 获取资源关系处理器
func (c *DependencyContainer) GetResourceRelationHandler() *handlers.ResourceRelationHandler {
	return c.handlers.resourceRelationHandler
}

// GetDepartmentHandler 获取部门处理器
func (c *DependencyContainer) GetDepartmentHandler() *handlers.DepartmentHandler {
	return c.handlers.departmentHandler
}

// GetPositionHandler 获取职位处理器
func (c *DependencyContainer) GetPositionHandler() *handlers.PositionHandler {
	return c.handlers.positionHandler
}

// GetTenantHandler 获取租户处理器
func (c *DependencyContainer) GetTenantHandler() *handlers.TenantHandler {
	return c.handlers.tenantHandler
}

// GetAppConfigHandler 获取应用配置处理器
func (c *DependencyContainer) GetAppConfigHandler() *handlers.AppConfigHandler {
	return c.handlers.appConfigHandler
}

// GetMenuHandler 获取菜单处理器
func (c *DependencyContainer) GetMenuHandler() *handlers.MenuHandler {
	return c.handlers.menuHandler
}

// GetAuthHandler 获取认证处理器
func (c *DependencyContainer) GetAuthHandler() *handlers.AuthHandler {
	return c.handlers.authHandler
}

// GetRegisterHandler 获取注册处理器
func (c *DependencyContainer) GetRegisterHandler() *handlers.RegisterHandler {
	return c.handlers.registerHandler
}

// GetThirdPartyHandler 获取第三方处理器
func (c *DependencyContainer) GetThirdPartyHandler() *handlers.ThirdPartyHandler {
	return c.handlers.thirdPartyHandler
}

// GetPasswordHandler 获取密码处理器
func (c *DependencyContainer) GetPasswordHandler() *handlers.PasswordHandler {
	return c.handlers.passwordHandler
}

// GetFileUploadHandler 获取文件上传处理器
func (c *DependencyContainer) GetFileUploadHandler() *handlers.FileUploadHandler {
	return c.handlers.fileUploadHandler
}

// GetOAuthChannelHandler 获取OAuth渠道处理器
func (c *DependencyContainer) GetOAuthChannelHandler() *handlers.OAuthChannelHandler {
	return c.handlers.oauthChannelHandler
}

// GetVerificationHandler 获取验证处理器
func (c *DependencyContainer) GetVerificationHandler() *handlers.VerificationHandler {
	return c.handlers.verificationHandler
}

func (c *DependencyContainer) GetApplicationHandler() *handlers.ApplicationHandler {
	return c.handlers.applicationHandler
}

// GetIDGeneratorHandler 获取ID生成器处理器
func (c *DependencyContainer) GetIDGeneratorHandler() *idgenerator.Handler {
	return c.handlers.idGeneratorHandler
}

// Close 关闭容器
func (c *DependencyContainer) Close(ctx context.Context) error {

	// 关闭数据库连接
	if c.infrastructure.db != nil {
		if sqlDB, err := c.infrastructure.db.DB(); err == nil {
			_ = sqlDB.Close()
		}
	}

	c.infrastructure.logger.Info(ctx, "Dependency container closed")
	return nil
}
