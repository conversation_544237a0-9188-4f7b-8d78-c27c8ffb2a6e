package dto

import (
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
)

// CreateApplicationRequest 创建应用请求
type CreateApplicationRequest struct {
	TenantID          int64                    `json:"tenant_id" validate:"required,min=1"`
	AppName           string                   `json:"app_name" validate:"required,max=128"`
	Description       string                   `json:"description,omitempty" validate:"max=256"`
	AppType           entity.ApplicationType   `json:"app_type" validate:"required"`
	IsSystem          bool                     `json:"is_system"`
	IsActive          bool                     `json:"is_active"`
	Status            entity.ApplicationStatus `json:"status" validate:"required"`
	CallbackURLs      []string                 `json:"callback_urls,omitempty"`
	AllowedOrigins    []string                 `json:"allowed_origins,omitempty"`
	Scopes            []string                 `json:"scopes,omitempty"`
	RateLimit         int                      `json:"rate_limit" validate:"min=0"`
	LogoURL           string                   `json:"logo_url,omitempty"`
	HomepageURL       string                   `json:"homepage_url,omitempty"`
	PrivacyPolicyURL  string                   `json:"privacy_policy_url,omitempty"`
	TermsOfServiceURL string                   `json:"terms_of_service_url,omitempty"`
	ContactEmail      string                   `json:"contact_email,omitempty" validate:"omitempty,email"`
	Config            map[string]interface{}   `json:"config,omitempty"`
}

// UpdateApplicationRequest 更新应用请求
type UpdateApplicationRequest struct {
	InternalAppID     int64                     `json:"internal_app_id" validate:"required,min=1"`
	AppName           *string                   `json:"app_name,omitempty" validate:"omitempty,max=128"`
	AppType           *entity.ApplicationType   `json:"app_type,omitempty"`
	Description       *string                   `json:"description,omitempty" validate:"omitempty,max=256"`
	IsActive          *bool                     `json:"is_active,omitempty"`
	Status            *entity.ApplicationStatus `json:"status,omitempty"`
	CallbackURLs      []string                  `json:"callback_urls,omitempty"`
	AllowedOrigins    []string                  `json:"allowed_origins,omitempty"`
	Scopes            []string                  `json:"scopes,omitempty"`
	RateLimit         *int                      `json:"rate_limit,omitempty" validate:"omitempty,min=0"`
	LogoURL           *string                   `json:"logo_url,omitempty"`
	HomepageURL       *string                   `json:"homepage_url,omitempty"`
	PrivacyPolicyURL  *string                   `json:"privacy_policy_url,omitempty"`
	TermsOfServiceURL *string                   `json:"terms_of_service_url,omitempty"`
	ContactEmail      *string                   `json:"contact_email,omitempty" validate:"omitempty,email"`
	Config            map[string]interface{}    `json:"config,omitempty"`
}

// ListApplicationsRequest 应用列表请求
type ListApplicationsRequest struct {
	TenantID       int64                     `json:"tenant_id" validate:"min=0"`
	Page           int                       `json:"page" validate:"min=1"`
	Size           int                       `json:"size" validate:"min=1,max=100"`
	Keyword        *string                   `json:"keyword,omitempty"`
	AppType        *entity.ApplicationType   `json:"app_type,omitempty"`
	Status         *entity.ApplicationStatus `json:"status,omitempty"`
	IsSystem       *bool                     `json:"is_system,omitempty"`
	CreatedAtStart *string                   `json:"created_at_start,omitempty"`
	CreatedAtEnd   *string                   `json:"created_at_end,omitempty"`
}

// ApplicationResponse 应用响应
type ApplicationResponse struct {
	ID                int64                    `json:"id"`
	InternalAppID     int64                    `json:"internal_app_id"`
	AppID             string                   `json:"app_id"`
	TenantID          int64                    `json:"tenant_id"`
	AppName           string                   `json:"app_name"`
	AppType           entity.ApplicationType   `json:"app_type"`
	Description       string                   `json:"description"`
	Status            entity.ApplicationStatus `json:"status"`
	IsSystem          bool                     `json:"is_system"`
	IsActive          bool                     `json:"is_active"`
	CallbackURLs      []string                 `json:"callback_urls"`
	AllowedOrigins    []string                 `json:"allowed_origins"`
	Scopes            []string                 `json:"scopes"`
	RateLimit         int                      `json:"rate_limit"`
	LogoURL           string                   `json:"logo_url"`
	HomepageURL       string                   `json:"homepage_url"`
	PrivacyPolicyURL  string                   `json:"privacy_policy_url"`
	TermsOfServiceURL string                   `json:"terms_of_service_url"`
	ContactEmail      string                   `json:"contact_email"`
	Config            map[string]interface{}   `json:"config"`
	CreatedBy         *int64                   `json:"created_by"`
	UpdatedBy         *int64                   `json:"updated_by"`
	CreatedAt         string                   `json:"created_at"`
	UpdatedAt         string                   `json:"updated_at"`
}

// ApplicationListResponse 应用列表响应
type ApplicationListResponse struct {
	List  []ApplicationResponse `json:"list"`
	Total int64                 `json:"total"`
	Page  int                   `json:"page"`
	Size  int                   `json:"size"`
}

// EnableApplicationRequest 启用应用请求
type EnableApplicationRequest struct {
	InternalAppID int64 `json:"internal_app_id" validate:"required,min=1"`
}

// DisableApplicationRequest 禁用应用请求
type DisableApplicationRequest struct {
	InternalAppID int64 `json:"internal_app_id" validate:"required,min=1"`
}

// DeleteApplicationRequest 删除应用请求
type DeleteApplicationRequest struct {
	InternalAppID int64 `json:"internal_app_id" validate:"required,min=1"`
}

// GetApplicationRequest 获取应用请求
type GetApplicationRequest struct {
	InternalAppID int64 `json:"internal_app_id" validate:"required,min=1"`
}

// GetApplicationByAppIDRequest 根据应用ID获取应用请求
type GetApplicationByAppIDRequest struct {
	AppID string `json:"app_id" validate:"required"`
}

// GetApplicationByInternalAppIDRequest 根据内部应用ID获取应用请求
type GetApplicationByInternalAppIDRequest struct {
	InternalAppID int64 `json:"internal_app_id" validate:"required,min=1"`
	TenantID      int64
}

// ApplicationStatsResponse 应用统计响应
type ApplicationStatsResponse struct {
	TotalCount   int64                              `json:"total_count"`
	ActiveCount  int64                              `json:"active_count"`
	SystemCount  int64                              `json:"system_count"`
	TypeCounts   map[entity.ApplicationType]int64   `json:"type_counts"`
	StatusCounts map[entity.ApplicationStatus]int64 `json:"status_counts"`
}

// ToApplicationResponse 转换为应用响应
func ToApplicationResponse(app *entity.Application) *ApplicationResponse {
	if app == nil {
		return nil
	}

	// 从PublicConfig解析配置信息
	var callbackURLs []string
	var allowedOrigins []string
	var scopes []string
	var logoURL, homepageURL, privacyPolicyURL, termsOfServiceURL, contactEmail string
	var config map[string]interface{}

	if app.PublicConfig != nil {
		config = app.PublicConfig

		// 解析callback_urls
		if urls, ok := app.PublicConfig["callback_urls"].([]interface{}); ok {
			callbackURLs = make([]string, len(urls))
			for i, url := range urls {
				if s, ok := url.(string); ok {
					callbackURLs[i] = s
				}
			}
		}

		// 解析allowed_origins
		if origins, ok := app.PublicConfig["allowed_origins"].([]interface{}); ok {
			allowedOrigins = make([]string, len(origins))
			for i, origin := range origins {
				if s, ok := origin.(string); ok {
					allowedOrigins[i] = s
				}
			}
		}

		// 解析scopes
		if scopeList, ok := app.PublicConfig["scopes"].([]interface{}); ok {
			scopes = make([]string, len(scopeList))
			for i, scope := range scopeList {
				if s, ok := scope.(string); ok {
					scopes[i] = s
				}
			}
		}

		// 解析字符串字段
		if url, ok := app.PublicConfig["logo_url"].(string); ok {
			logoURL = url
		}
		if url, ok := app.PublicConfig["homepage_url"].(string); ok {
			homepageURL = url
		}
		if url, ok := app.PublicConfig["privacy_policy_url"].(string); ok {
			privacyPolicyURL = url
		}
		if url, ok := app.PublicConfig["terms_of_service_url"].(string); ok {
			termsOfServiceURL = url
		}
		if email, ok := app.PublicConfig["contact_email"].(string); ok {
			contactEmail = email
		}
	}

	return &ApplicationResponse{
		ID:                app.ID,
		InternalAppID:     app.InternalAppID,
		AppID:             app.AppID,
		TenantID:          app.TenantID,
		AppName:           app.AppName,
		AppType:           app.AppType,
		Description:       app.Description,
		Status:            app.Status,
		IsSystem:          app.IsSystem,
		IsActive:          app.IsActive(),
		CallbackURLs:      callbackURLs,
		AllowedOrigins:    allowedOrigins,
		Scopes:            scopes,
		RateLimit:         app.MaxRequestsPerMinute,
		LogoURL:           logoURL,
		HomepageURL:       homepageURL,
		PrivacyPolicyURL:  privacyPolicyURL,
		TermsOfServiceURL: termsOfServiceURL,
		ContactEmail:      contactEmail,
		Config:            config,
		CreatedBy:         app.CreatedBy,
		UpdatedBy:         app.UpdatedBy,
		CreatedAt:         app.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:         app.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}

// ToApplicationListResponse 转换为应用列表响应
func ToApplicationListResponse(apps []*entity.Application, total int64, page, size int) *ApplicationListResponse {
	list := make([]ApplicationResponse, len(apps))
	for i, app := range apps {
		if resp := ToApplicationResponse(app); resp != nil {
			list[i] = *resp
		}
	}

	return &ApplicationListResponse{
		List:  list,
		Total: total,
		Page:  page,
		Size:  size,
	}
}
