package service

import (
	"context"
	"fmt"
	"time"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/internal/application/application/dto"
	userservice "gitee.com/heiyee/platforms/users/internal/application/user/service"
	domainservice "gitee.com/heiyee/platforms/users/internal/domain/application/service"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
)

// ApplicationApplicationService 应用管理服务
type ApplicationApplicationService struct {
	applicationRepo   repository.ApplicationRepository
	appIDFactory      *domainservice.AppIDFactory
	permissionService userservice.PermissionCheckService
	logger            logiface.Logger
}

// NewApplicationApplicationService 创建应用管理服务
func NewApplicationApplicationService(
	applicationRepo repository.ApplicationRepository,
	appIDFactory *domainservice.AppIDFactory,
	permissionService userservice.PermissionCheckService,
	logger logiface.Logger,
) *ApplicationApplicationService {
	return &ApplicationApplicationService{
		applicationRepo:   applicationRepo,
		appIDFactory:      appIDFactory,
		permissionService: permissionService,
		logger:            logger,
	}
}

// CreateApplication 创建应用
func (s *ApplicationApplicationService) CreateApplication(ctx context.Context, req *dto.CreateApplicationRequest) (*dto.ApplicationResponse, error) {
	s.logger.Info(ctx, "开始创建应用",
		logiface.String("app_name", req.AppName),
		logiface.Int64("tenant_id", req.TenantID),
	)

	// 生成应用ID
	appIDs, err := s.appIDFactory.GenerateAppIDs(ctx, req.TenantID)
	if err != nil {
		s.logger.Error(ctx, "生成应用ID失败", logiface.Error(err))
		return nil, fmt.Errorf("生成应用ID失败: %w", err)
	}

	// 创建应用实体
	now := time.Now()
	application := &entity.Application{
		ID:            appIDs.InternalAppID, // 使用生成的内部应用ID作为主键
		TenantID:      req.TenantID,
		AppID:         appIDs.AppID,
		InternalAppID: appIDs.InternalAppID,
		AppName:       req.AppName,
		AppType:       req.AppType,
		Description:   req.Description,
		Status:        req.Status,
		IsSystem:      req.IsSystem,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	// 设置公开配置
	if application.PublicConfig == nil {
		application.PublicConfig = make(entity.JSONMap)
	}

	// 设置应用状态
	if req.IsActive {
		application.Status = entity.ApplicationStatusActive
	} else {
		application.Status = entity.ApplicationStatusDisabled
	}

	// 设置配置数据到PublicConfig
	if len(req.CallbackURLs) > 0 {
		application.PublicConfig["callback_urls"] = req.CallbackURLs
	}
	if len(req.AllowedOrigins) > 0 {
		application.PublicConfig["allowed_origins"] = req.AllowedOrigins
	}
	if len(req.Scopes) > 0 {
		application.PublicConfig["scopes"] = req.Scopes
	}
	application.MaxRequestsPerMinute = req.RateLimit
	if req.LogoURL != "" {
		application.PublicConfig["logo_url"] = req.LogoURL
	}
	if req.HomepageURL != "" {
		application.PublicConfig["homepage_url"] = req.HomepageURL
	}
	if req.PrivacyPolicyURL != "" {
		application.PublicConfig["privacy_policy_url"] = req.PrivacyPolicyURL
	}
	if req.TermsOfServiceURL != "" {
		application.PublicConfig["terms_of_service_url"] = req.TermsOfServiceURL
	}
	if req.ContactEmail != "" {
		application.PublicConfig["contact_email"] = req.ContactEmail
	}

	// 其他额外配置
	if req.Config != nil {
		for k, v := range req.Config {
			application.PublicConfig[k] = v
		}
	}

	// 验证应用数据
	if !application.IsValidStatus() {
		return nil, fmt.Errorf("无效的应用状态: %s", application.Status)
	}
	if !application.IsValidType() {
		return nil, fmt.Errorf("无效的应用类型: %s", application.AppType)
	}

	// 保存到数据库
	if err := s.applicationRepo.Create(ctx, application); err != nil {
		s.logger.Error(ctx, "创建应用失败", logiface.Error(err))
		return nil, fmt.Errorf("创建应用失败: %w", err)
	}

	s.logger.Info(ctx, "创建应用成功",
		logiface.Int64("internal_app_id", application.InternalAppID),
		logiface.String("app_id", application.AppID),
	)

	return dto.ToApplicationResponse(application), nil
}

// UpdateApplication 更新应用
func (s *ApplicationApplicationService) UpdateApplication(ctx context.Context, req *dto.UpdateApplicationRequest) (*dto.ApplicationResponse, error) {
	s.logger.Info(ctx, "开始更新应用",
		logiface.Int64("internal_app_id", req.InternalAppID),
	)
	tenantId, _ := usercontext.GetTenantID(ctx)
	// 查找现有应用
	application, err := s.applicationRepo.GetByInternalAppID(ctx, req.InternalAppID, tenantId)
	if err != nil {
		s.logger.Error(ctx, "查找应用失败", logiface.Error(err))
		return nil, fmt.Errorf("查找应用失败: %w", err)
	}
	if application == nil {
		return nil, fmt.Errorf("应用不存在")
	}

	// 更新字段
	if req.AppName != nil {
		application.AppName = *req.AppName
	}
	if req.AppType != nil {
		application.AppType = *req.AppType
	}
	if req.Description != nil {
		application.Description = *req.Description
	}
	if req.IsActive != nil {
		if *req.IsActive {
			application.Status = entity.ApplicationStatusActive
		} else {
			application.Status = entity.ApplicationStatusDisabled
		}
	}
	if req.Status != nil {
		application.Status = *req.Status
	}

	// 初始化PublicConfig
	if application.PublicConfig == nil {
		application.PublicConfig = make(entity.JSONMap)
	}

	// 更新配置字段
	if req.CallbackURLs != nil {
		if len(req.CallbackURLs) > 0 {
			application.PublicConfig["callback_urls"] = req.CallbackURLs
		} else {
			delete(application.PublicConfig, "callback_urls")
		}
	}
	if req.AllowedOrigins != nil {
		if len(req.AllowedOrigins) > 0 {
			application.PublicConfig["allowed_origins"] = req.AllowedOrigins
		} else {
			delete(application.PublicConfig, "allowed_origins")
		}
	}
	if req.Scopes != nil {
		if len(req.Scopes) > 0 {
			application.PublicConfig["scopes"] = req.Scopes
		} else {
			delete(application.PublicConfig, "scopes")
		}
	}
	if req.RateLimit != nil {
		application.MaxRequestsPerMinute = *req.RateLimit
	}
	if req.LogoURL != nil {
		if *req.LogoURL != "" {
			application.PublicConfig["logo_url"] = *req.LogoURL
		} else {
			delete(application.PublicConfig, "logo_url")
		}
	}
	if req.HomepageURL != nil {
		if *req.HomepageURL != "" {
			application.PublicConfig["homepage_url"] = *req.HomepageURL
		} else {
			delete(application.PublicConfig, "homepage_url")
		}
	}
	if req.PrivacyPolicyURL != nil {
		if *req.PrivacyPolicyURL != "" {
			application.PublicConfig["privacy_policy_url"] = *req.PrivacyPolicyURL
		} else {
			delete(application.PublicConfig, "privacy_policy_url")
		}
	}
	if req.TermsOfServiceURL != nil {
		if *req.TermsOfServiceURL != "" {
			application.PublicConfig["terms_of_service_url"] = *req.TermsOfServiceURL
		} else {
			delete(application.PublicConfig, "terms_of_service_url")
		}
	}
	if req.ContactEmail != nil {
		if *req.ContactEmail != "" {
			application.PublicConfig["contact_email"] = *req.ContactEmail
		} else {
			delete(application.PublicConfig, "contact_email")
		}
	}
	if req.Config != nil {
		// 合并额外配置
		for k, v := range req.Config {
			application.PublicConfig[k] = v
		}
	}

	application.UpdatedAt = time.Now()

	// 验证应用数据
	if !application.IsValidStatus() {
		return nil, fmt.Errorf("无效的应用状态: %s", application.Status)
	}
	if !application.IsValidType() {
		return nil, fmt.Errorf("无效的应用类型: %s", application.AppType)
	}

	// 保存到数据库
	if err := s.applicationRepo.Update(ctx, application); err != nil {
		s.logger.Error(ctx, "更新应用失败", logiface.Error(err))
		return nil, fmt.Errorf("更新应用失败: %w", err)
	}

	s.logger.Info(ctx, "更新应用成功",
		logiface.Int64("internal_app_id", application.InternalAppID),
	)

	return dto.ToApplicationResponse(application), nil
}

// GetApplication 获取应用详情
func (s *ApplicationApplicationService) GetApplication(ctx context.Context, req *dto.GetApplicationRequest) (*dto.ApplicationResponse, error) {
	tenantId, _ := usercontext.GetTenantID(ctx)
	application, err := s.applicationRepo.GetByInternalAppID(ctx, req.InternalAppID, tenantId)
	if err != nil {
		s.logger.Error(ctx, "获取应用失败", logiface.Error(err))
		return nil, fmt.Errorf("获取应用失败: %w", err)
	}
	if application == nil {
		return nil, fmt.Errorf("应用不存在")
	}

	return dto.ToApplicationResponse(application), nil
}

// GetApplicationByAppID 根据应用ID获取应用详情
func (s *ApplicationApplicationService) GetApplicationByAppID(ctx context.Context, req *dto.GetApplicationByAppIDRequest) (*dto.ApplicationResponse, error) {
	// 直接根据app_id查询应用，不需要用户授权
	application, err := s.applicationRepo.GetByAppID(ctx, req.AppID)
	if err != nil {
		s.logger.Error(ctx, "获取应用失败", logiface.Error(err))
		return nil, fmt.Errorf("获取应用失败: %w", err)
	}
	if application == nil {
		return nil, fmt.Errorf("应用不存在")
	}

	return dto.ToApplicationResponse(application), nil
}

// GetApplicationByInternalAppID 根据内部应用ID获取应用详情
func (s *ApplicationApplicationService) GetApplicationByInternalAppID(ctx context.Context, req *dto.GetApplicationByInternalAppIDRequest) (*dto.ApplicationResponse, error) {
	// 直接根据internal_app_id查询应用，不需要用户授权
	application, err := s.applicationRepo.GetByInternalAppID(ctx, req.InternalAppID, req.TenantID)
	if err != nil {
		s.logger.Error(ctx, "获取应用失败", logiface.Error(err))
		return nil, fmt.Errorf("获取应用失败: %w", err)
	}
	if application == nil {
		return nil, fmt.Errorf("应用不存在")
	}

	return dto.ToApplicationResponse(application), nil
}

// ListApplications 获取应用列表
func (s *ApplicationApplicationService) ListApplications(ctx context.Context, req *dto.ListApplicationsRequest) (*dto.ApplicationListResponse, error) {
	s.logger.Info(ctx, "开始获取应用列表",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.Int("page", req.Page),
		logiface.Int("size", req.Size),
	)

	// 获取当前用户信息
	userInfo, err := usercontext.RequireAuth(ctx)
	if err != nil {
		return nil, fmt.Errorf("用户未认证: %w", err)
	}

	// 检查是否为超管
	isSuperAdmin, err := s.permissionService.IsUserSuperAdmin(ctx, userInfo.UserID, userInfo.InternalAppID)
	if err != nil {
		s.logger.Error(ctx, "检查超管权限失败", logiface.Error(err))
		return nil, fmt.Errorf("检查超管权限失败: %w", err)
	}

	// 根据超管权限决定查询逻辑
	var effectiveTenantID int64
	if isSuperAdmin {
		// 超管逻辑：如果请求中携带了tenant_id则查询指定租户，否则返回空数据
		if req.TenantID > 0 {
			effectiveTenantID = req.TenantID
		} else {
			// 超管未指定tenant_id，返回空数据
			s.logger.Info(ctx, "超管未指定tenant_id，返回空数据")
			return dto.ToApplicationListResponse([]*entity.Application{}, 0, req.Page, req.Size), nil
		}
	} else {
		// 非超管逻辑：使用usercontext中的tenant_id
		effectiveTenantID = userInfo.TenantID
	}

	s.logger.Info(ctx, "确定查询租户",
		logiface.Bool("is_super_admin", isSuperAdmin),
		logiface.Int64("effective_tenant_id", effectiveTenantID),
		logiface.Int64("user_tenant_id", userInfo.TenantID),
	)

	// 构建查询参数
	params := repository.NewApplicationQueryParams().
		WithPagination(req.Page, req.Size)

	if req.Keyword != nil && *req.Keyword != "" {
		params.WithKeyword(*req.Keyword)
	}
	if req.AppType != nil {
		params.WithAppType(*req.AppType)
	}
	if req.Status != nil {
		params.WithStatus(*req.Status)
	}
	if req.IsSystem != nil {
		params.WithIsSystem(*req.IsSystem)
	}
	if req.CreatedAtStart != nil && req.CreatedAtEnd != nil {
		params.WithTimeRange(req.CreatedAtStart, req.CreatedAtEnd)
	}

	// 根据是否为超管选择不同的查询方法
	var applications []*entity.Application
	var total int64
	if isSuperAdmin {
		// 超管查询：不使用WithTenantFilter，直接指定tenant_id查询
		applications, total, err = s.applicationRepo.ListByTenantID(ctx, effectiveTenantID, params)
	} else {
		// 普通用户查询：使用正常的List方法（会自动应用tenant过滤）
		applications, total, err = s.applicationRepo.List(ctx, params)
	}

	if err != nil {
		s.logger.Error(ctx, "获取应用列表失败", logiface.Error(err))
		return nil, fmt.Errorf("获取应用列表失败: %w", err)
	}

	s.logger.Info(ctx, "获取应用列表成功",
		logiface.Int64("total", total),
		logiface.Int("count", len(applications)),
	)

	return dto.ToApplicationListResponse(applications, total, req.Page, req.Size), nil
}

// EnableApplication 启用应用
func (s *ApplicationApplicationService) EnableApplication(ctx context.Context, req *dto.EnableApplicationRequest) error {
	s.logger.Info(ctx, "开始启用应用",
		logiface.Int64("internal_app_id", req.InternalAppID),
	)

	// 获取当前用户信息
	userInfo, err := usercontext.RequireAuth(ctx)
	if err != nil {
		return fmt.Errorf("用户未认证: %w", err)
	}
	userID := userInfo.UserID

	if err := s.applicationRepo.Enable(ctx, req.InternalAppID, userID); err != nil {
		s.logger.Error(ctx, "启用应用失败", logiface.Error(err))
		return fmt.Errorf("启用应用失败: %w", err)
	}

	s.logger.Info(ctx, "启用应用成功",
		logiface.Int64("internal_app_id", req.InternalAppID),
	)

	return nil
}

// DisableApplication 禁用应用
func (s *ApplicationApplicationService) DisableApplication(ctx context.Context, req *dto.DisableApplicationRequest) error {
	s.logger.Info(ctx, "开始禁用应用",
		logiface.Int64("internal_app_id", req.InternalAppID),
	)

	// 获取当前用户信息
	userInfo, err := usercontext.RequireAuth(ctx)
	if err != nil {
		return fmt.Errorf("用户未认证: %w", err)
	}
	userID := userInfo.UserID

	if err := s.applicationRepo.Disable(ctx, req.InternalAppID, userID); err != nil {
		s.logger.Error(ctx, "禁用应用失败", logiface.Error(err))
		return fmt.Errorf("禁用应用失败: %w", err)
	}

	s.logger.Info(ctx, "禁用应用成功",
		logiface.Int64("internal_app_id", req.InternalAppID),
	)

	return nil
}

// DeleteApplication 删除应用
func (s *ApplicationApplicationService) DeleteApplication(ctx context.Context, req *dto.DeleteApplicationRequest) error {
	s.logger.Info(ctx, "开始删除应用",
		logiface.Int64("internal_app_id", req.InternalAppID),
	)
	tenantId, _ := usercontext.GetTenantID(ctx)
	// 查找应用
	application, err := s.applicationRepo.GetByInternalAppID(ctx, req.InternalAppID, tenantId)
	if err != nil {
		s.logger.Error(ctx, "查找应用失败", logiface.Error(err))
		return fmt.Errorf("查找应用失败: %w", err)
	}
	if application == nil {
		return fmt.Errorf("应用不存在")
	}

	// 系统应用不能删除
	if application.IsSystem {
		return fmt.Errorf("系统应用不能删除")
	}

	// 软删除
	if err := s.applicationRepo.SoftDelete(ctx, application.ID); err != nil {
		s.logger.Error(ctx, "删除应用失败", logiface.Error(err))
		return fmt.Errorf("删除应用失败: %w", err)
	}

	s.logger.Info(ctx, "删除应用成功",
		logiface.Int64("internal_app_id", req.InternalAppID),
	)

	return nil
}

// GetStats 获取应用统计信息
func (s *ApplicationApplicationService) GetStats(ctx context.Context, tenantID int64) (*dto.ApplicationStatsResponse, error) {
	stats, err := s.applicationRepo.GetStats(ctx)
	if err != nil {
		s.logger.Error(ctx, "获取应用统计失败", logiface.Error(err))
		return nil, fmt.Errorf("获取应用统计失败: %w", err)
	}

	return &dto.ApplicationStatsResponse{
		TotalCount:   stats.TotalCount,
		ActiveCount:  stats.ActiveCount,
		SystemCount:  stats.SystemCount,
		TypeCounts:   stats.TypeCounts,
		StatusCounts: stats.StatusCounts,
	}, nil
}
