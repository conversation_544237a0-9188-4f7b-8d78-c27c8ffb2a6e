package service

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/domain/user/constants"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
)

// PermissionCheckService 权限检查服务接口
type PermissionCheckService interface {
	// 检查用户是否有指定权限
	CheckUserPermission(ctx context.Context, userID int64, internalAppID int64, permissionCode string) (bool, error)

	// 批量检查用户权限
	BatchCheckUserPermissions(ctx context.Context, userID int64, internalAppID int64, permissionCodes []string) (map[string]bool, error)

	// 获取用户对指定资源的权限
	GetUserResourcePermissions(ctx context.Context, userID int64, internalAppID int64, resourceID int64) ([]string, error)

	// 批量获取用户对多个资源的权限
	BatchGetUserResourcePermissions(ctx context.Context, userID int64, internalAppID int64, resourceIDs []int64) (map[int64][]string, error)

	// 检查用户角色
	HasUserRole(ctx context.Context, userID int64, internalAppID int64, roleCode string) (bool, error)

	// 获取用户角色列表
	GetUserRoles(ctx context.Context, userID int64, internalAppID int64) ([]*entity.Role, error)

	// 检查用户是否为管理员
	IsUserAdmin(ctx context.Context, userID int64, internalAppID int64) (bool, error)

	// 检查用户是否为超级管理员
	IsUserSuperAdmin(ctx context.Context, userID int64, internalAppID int64) (bool, error)

	// 检查用户是否有任一指定角色
	HasAnyUserRole(ctx context.Context, userID int64, internalAppID int64, roleCodes []string) (bool, error)

	// 检查用户是否为管理员或超级管理员
	IsAdminOrSuperAdmin(ctx context.Context, userID int64, internalAppID int64) (bool, error)

	// ==================== 超级管理员权限查询链路（仅用于权限检查，不允许其他业务使用）====================

	// CheckUserSuperAdminRoleOnly 检查用户是否具备超级管理员角色（不考虑appId和tenantId）
	// 注意：此方法仅用于权限检查，不允许给其他正常业务使用
	CheckUserSuperAdminRoleOnly(ctx context.Context, userID int64) (bool, error)
}

// PermissionCheckServiceImpl 权限检查服务实现
type PermissionCheckServiceImpl struct {
	rolePermissionRepo repository.RolePermissionRepository
	logger             logiface.Logger
}

// NewPermissionCheckService 创建权限检查服务
func NewPermissionCheckService(
	rolePermissionRepo repository.RolePermissionRepository,
	logger logiface.Logger,
) PermissionCheckService {
	return &PermissionCheckServiceImpl{
		rolePermissionRepo: rolePermissionRepo,
		logger:             logger,
	}
}

func (s *PermissionCheckServiceImpl) CheckUserPermission(ctx context.Context, userID int64, internalAppID int64, permissionCode string) (bool, error) {
	// 使用带应用ID的权限检查
	return s.rolePermissionRepo.CheckUserPermissionByApp(ctx, userID, internalAppID, permissionCode)
}

func (s *PermissionCheckServiceImpl) BatchCheckUserPermissions(ctx context.Context, userID int64, internalAppID int64, permissionCodes []string) (map[string]bool, error) {
	// 实现批量权限检查逻辑
	results := make(map[string]bool)
	for _, code := range permissionCodes {
		hasPermission, err := s.rolePermissionRepo.CheckUserPermissionByApp(ctx, userID, internalAppID, code)
		if err != nil {
			return nil, fmt.Errorf("failed to check permission %s: %w", code, err)
		}
		results[code] = hasPermission
	}
	return results, nil
}

func (s *PermissionCheckServiceImpl) GetUserResourcePermissions(ctx context.Context, userID int64, internalAppID int64, resourceID int64) ([]string, error) {
	permissions, err := s.rolePermissionRepo.GetUserPermissionsByResourceIDAndApp(ctx, userID, internalAppID, resourceID)
	if err != nil {
		return nil, err
	}

	codes := make([]string, len(permissions))
	for i, permission := range permissions {
		codes[i] = permission.Code
	}
	return codes, nil
}

func (s *PermissionCheckServiceImpl) BatchGetUserResourcePermissions(ctx context.Context, userID int64, internalAppID int64, resourceIDs []int64) (map[int64][]string, error) {
	// 获取用户的所有权限
	// 注意：这个方法签名可能需要调整，因为不再通过resourceIDs直接查询
	_, err := s.rolePermissionRepo.GetUserPermissions(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 注意：此方法需要重构，因为权限不再直接关联资源
	// 现在需要通过resource_relations表来建立权限和资源的关联
	// 临时返回空map，需要在后续版本中重构此逻辑
	permissionMap := make(map[int64][]string)

	// TODO: 重构此方法，通过resource_relations表查询权限和资源的关联关系
	// 1. 通过userID获取用户的权限列表
	// 2. 通过resource_relations表查询这些权限关联的资源
	// 3. 构建resourceID -> []permissionCode 的映射

	return permissionMap, nil
}

func (s *PermissionCheckServiceImpl) HasUserRole(ctx context.Context, userID int64, internalAppID int64, roleCode string) (bool, error) {
	return s.HasAnyUserRole(ctx, userID, internalAppID, []string{roleCode})
}

func (s *PermissionCheckServiceImpl) GetUserRoles(ctx context.Context, userID int64, internalAppID int64) ([]*entity.Role, error) {
	// 使用统一查询入口
	roles, err := s.getUserRolesWithCache(ctx, userID, internalAppID)
	if err != nil {
		return nil, err
	}

	// 转换为指针切片
	rolePtrs := make([]*entity.Role, len(roles))
	for i := range roles {
		rolePtrs[i] = &roles[i]
	}

	return rolePtrs, nil
}

func (s *PermissionCheckServiceImpl) IsUserAdmin(ctx context.Context, userID int64, internalAppID int64) (bool, error) {
	return s.HasUserRole(ctx, userID, internalAppID, constants.RoleAdmin)
}

func (s *PermissionCheckServiceImpl) IsUserSuperAdmin(ctx context.Context, userID int64, internalAppID int64) (bool, error) {
	return s.HasUserRole(ctx, userID, internalAppID, constants.RoleSuperAdmin)
}

// HasAnyUserRole 检查用户是否有任一指定角色（统一查询入口，为缓存预留）
func (s *PermissionCheckServiceImpl) HasAnyUserRole(ctx context.Context, userID int64, internalAppID int64, roleCodes []string) (bool, error) {
	if len(roleCodes) == 0 {
		return false, nil
	}

	// 统一查询入口：获取用户所有角色（此处可以后续添加缓存）
	userRoles, err := s.getUserRolesWithCache(ctx, userID, internalAppID)
	if err != nil {
		return false, err
	}

	// 构建用户角色集合，提高查找效率
	userRoleSet := make(map[string]bool)
	for _, role := range userRoles {
		userRoleSet[role.Code] = true
	}

	// 检查是否有任一指定角色
	for _, roleCode := range roleCodes {
		if userRoleSet[roleCode] {
			return true, nil
		}
	}

	return false, nil
}

// IsAdminOrSuperAdmin 检查用户是否为管理员或超级管理员
func (s *PermissionCheckServiceImpl) IsAdminOrSuperAdmin(ctx context.Context, userID int64, internalAppID int64) (bool, error) {
	return s.HasAnyUserRole(ctx, userID, internalAppID, constants.HighLevelAdminRoles)
}

// getUserRolesWithCache 获取用户角色（统一查询入口，为缓存预留）
// 这个方法作为统一的角色查询入口，后续可以在这里添加缓存逻辑
func (s *PermissionCheckServiceImpl) getUserRolesWithCache(ctx context.Context, userID int64, internalAppID int64) ([]entity.Role, error) {
	// TODO: 后续可以在这里添加缓存逻辑
	// 1. 先检查缓存
	// 2. 缓存未命中时查询数据库
	// 3. 将结果写入缓存

	s.logger.Debug(ctx, "Getting user roles from database",
		logiface.Int64("userID", userID),
		logiface.Int64("internalAppID", internalAppID))

	return s.rolePermissionRepo.GetUserRolesByApp(ctx, userID, internalAppID)
}

// ==================== 超级管理员权限查询链路（仅用于权限检查，不允许其他业务使用）====================

// CheckUserSuperAdminRoleOnly 检查用户是否具备超级管理员角色（不考虑appId和tenantId）
// 注意：此方法仅用于权限检查，不允许给其他正常业务使用
func (s *PermissionCheckServiceImpl) CheckUserSuperAdminRoleOnly(ctx context.Context, userID int64) (bool, error) {
	if userID <= 0 {
		return false, fmt.Errorf("invalid user ID: %d", userID)
	}

	s.logger.Debug(ctx, "Checking user super admin role only (permission check only)",
		logiface.Int64("userID", userID),
		logiface.String("note", "This method ignores appId and tenantId, only for permission checking"))

	// 直接查询用户角色，不考虑应用ID和租户ID
	// 使用专门的仓储方法，只检查超级管理员角色
	hasRole, err := s.rolePermissionRepo.CheckUserSuperAdminRoleOnly(ctx, userID)
	if err != nil {
		s.logger.Error(ctx, "Failed to check user super admin role only",
			logiface.Error(err),
			logiface.Int64("userID", userID))
		return false, fmt.Errorf("failed to check super admin role: %w", err)
	}

	s.logger.Debug(ctx, "User super admin role check completed",
		logiface.Int64("userID", userID),
		logiface.Bool("hasSuperAdminRole", hasRole))

	return hasRole, nil
}
