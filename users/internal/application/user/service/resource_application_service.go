package service

import (
	"context"
	"fmt"
	"sort"
	"time"

	commonResponse "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	userErrors "gitee.com/heiyee/platforms/users/internal/domain/errors"
	"gitee.com/heiyee/platforms/users/internal/domain/user/constants"
	userEntity "gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	userRepo "gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/persistence"
)

// ResourceApplicationService 资源应用服务
type ResourceApplicationService struct {
	*BaseService
	resourceRepo              userRepo.ResourceRepository
	resourceRelationRepo      userRepo.ResourceRelationRepository
	resourceAppAssignmentRepo userRepo.ResourceAppAssignmentRepository
	permissionRepo            userRepo.PermissionRepository
	userRepo                  userRepo.UserRepository
	entityFactory             *userEntity.EntityFactory
	resourceFactory           *userEntity.ResourceFactory
	logger                    logiface.Logger
}

// NewResourceApplicationService 创建资源应用服务
func NewResourceApplicationService(
	resourceRepo userRepo.ResourceRepository,
	resourceRelationRepo userRepo.ResourceRelationRepository,
	resourceAppAssignmentRepo userRepo.ResourceAppAssignmentRepository,
	permissionRepo userRepo.PermissionRepository,
	userRepo userRepo.UserRepository,
	entityFactory *userEntity.EntityFactory,
	logger logiface.Logger,
) *ResourceApplicationService {
	resourceFactory := userEntity.NewResourceFactory(entityFactory)

	return &ResourceApplicationService{
		BaseService:               NewBaseService(),
		resourceRepo:              resourceRepo,
		resourceRelationRepo:      resourceRelationRepo,
		resourceAppAssignmentRepo: resourceAppAssignmentRepo,
		permissionRepo:            permissionRepo,
		userRepo:                  userRepo,
		entityFactory:             entityFactory,
		resourceFactory:           resourceFactory,
		logger:                    logger,
	}
}

// existsByNameAdapter 适配器方法，用于ValidateUniqueField
func (s *ResourceApplicationService) existsByNameAdapter(ctx context.Context, tenantID int64, value string) (bool, error) {
	return s.resourceRepo.ExistsByName(ctx, value)
}

// CreateResource 创建资源
func (s *ResourceApplicationService) CreateResource(ctx context.Context, req *dto.CreateResourceRequest) (*userEntity.Resource, error) {
	s.logger.Info(ctx, "Creating resource", logiface.Any("request", req))

	// 确定使用的 tenantID：优先使用请求中的，如果没有则从 context 获取
	var tenantID int64
	if req.TenantID > 0 {
		tenantID = req.TenantID
	} else {
		// 从 context 获取租户ID
		if contextTenantID, exists := usercontext.GetTenantID(ctx); exists && contextTenantID > 0 {
			tenantID = contextTenantID
		} else {
			return nil, fmt.Errorf("tenant ID not found in request or context")
		}
	}

	// 如果是通用资源，设置为租户ID为0
	if req.IsUniversal {
		tenantID = 0 // 0表示通用资源，适用于所有租户
	}

	// 验证名称唯一性
	if err := s.ValidateUniqueField(ctx, s.existsByNameAdapter, req.Name, tenantID, "resource name"); err != nil {
		return nil, err
	}

	// 验证资源类型
	resourceType := value_object.ResourceType(req.ResourceType)
	if !resourceType.IsValid() {
		return nil, userErrors.NewBusinessError(userErrors.CodeUserResourceNotFound, fmt.Sprintf("invalid resource type: %s", req.ResourceType))
	}

	// 如果有父资源，验证父资源存在且类型兼容
	if req.ParentID != nil {
		// 使用非基础组件注入tenantId的方式查询数据
		parent, err := s.findParentResourceWithoutTenantInjection(ctx, *req.ParentID, tenantID)
		if err != nil {
			return nil, userErrors.NewDatabaseError("获取父资源", fmt.Sprintf("database query failed: %v", err))
		}
		if parent == nil {
			// 返回字段验证错误，而不是业务逻辑错误
			return nil, &commonResponse.FieldValidationError{
				Field:   "parent_id",
				Message: "父资源不存在",
			}
		}

		// 检查父子资源类型的兼容性
		if err := s.validateParentChildCompatibility(parent.ResourceType, resourceType); err != nil {
			return nil, err
		}
	}

	// 确定使用的 internalAppID：优先使用请求中的，如果没有则从 context 获取
	var internalAppID int64
	if req.InternalAppID != nil && *req.InternalAppID > 0 {
		internalAppID = *req.InternalAppID
	} else {
		// 从 context 获取内部应用ID
		if contextInternalAppID, exists := usercontext.GetInternalAppID(ctx); exists && contextInternalAppID > 0 {
			internalAppID = contextInternalAppID
		} else {
			return nil, fmt.Errorf("internal app ID not found in request or context")
		}
	}
	// 创建资源实体
	var options []userEntity.ResourceOption
	if req.IsSystem {
		options = append(options, userEntity.WithSystem())
	}

	// 处理 parentId：如果为 nil 则设置为 0
	var parentID *int64
	if req.ParentID != nil {
		parentID = req.ParentID
	} else {
		// 如果 parentId 为 nil，则设置为 0 表示根节点
		// 注意：数据库中根节点的 parent_id 通常为 NULL，但为了前端兼容性设置为 0
		parentID = new(int64)
		*parentID = 0
	}

	resource, err := s.resourceFactory.NewResourceWithOptions(
		ctx,
		tenantID,
		internalAppID,
		req.Name,
		req.DisplayName,
		req.Description,
		resourceType,
		parentID,
		req.Path,
		req.Icon,
		options...,
	)

	if err != nil {
		return nil, userErrors.NewSystemError("创建资源实体", fmt.Sprintf("entity creation failed: %v", err))
	}

	// 设置 API 相关字段
	if req.ServiceName != "" {
		resource.ServiceName = req.ServiceName
	}
	if req.RequestType != "" {
		resource.RequestType = req.RequestType
	}
	if req.ResponseType != "" {
		resource.ResponseType = req.ResponseType
	}
	if req.APIMethod != "" {
		resource.APIMethod = req.APIMethod
	}
	if req.ContentType != "" {
		resource.ContentType = req.ContentType
	}

	// 设置排序
	if req.SortOrder > 0 {
		resource.SortOrder = req.SortOrder
	} else {
		// 自动设置排序
		maxSort, err := s.resourceRepo.GetMaxSortOrder(ctx, parentID)
		if err != nil {
			return nil, userErrors.NewDatabaseError("获取最大排序", fmt.Sprintf("database query failed: %v", err))
		}
		resource.SortOrder = maxSort + 1
	}

	// 保存资源
	if err := s.resourceRepo.Create(ctx, resource); err != nil {
		return nil, fmt.Errorf("failed to create resource: %w", err)
	}

	s.logger.Info(ctx, "Resource created", logiface.Any("resource", resource))
	return resource, nil
}

// GetResource 获取资源
func (s *ResourceApplicationService) GetResource(ctx context.Context, id int64) (*dto.ResourceResponse, error) {
	s.logger.Info(ctx, "Getting resource", logiface.Int64("id", id))

	resource, err := s.resourceRepo.FindByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource: %w", err)
	}
	if resource == nil {
		return nil, fmt.Errorf("resource not found")
	}

	response, err := s.toResourceResponse(ctx, resource, true) // 包含权限和API资源信息
	if err != nil {
		return nil, err
	}

	s.logger.Info(ctx, "Resource retrieved", logiface.Any("resource", response))
	return response, nil
}

// ListResources 获取资源列表
func (s *ResourceApplicationService) ListResources(ctx context.Context, req *dto.ListResourcesRequest) (*dto.ListResourcesResponse, error) {
	s.logger.Info(ctx, "Listing resources", logiface.Any("request", req))

	// 确定使用的 tenantID：优先使用请求中的，如果没有则从 context 获取
	var tenantID int64
	if req.TenantID > 0 {
		tenantID = req.TenantID
	} else {
		// 从 context 获取租户ID
		if contextTenantID, exists := usercontext.GetTenantID(ctx); exists && contextTenantID > 0 {
			tenantID = contextTenantID
		} else {
			return nil, fmt.Errorf("tenant ID not found in request or context")
		}
	}

	// 构建查询参数
	params := userRepo.NewQueryParams().
		WithTenantID(tenantID).
		WithPagination(req.Offset, req.Limit).
		WithOrder(req.OrderBy, req.OrderDir)

	// 添加搜索条件
	if req.Keyword != "" {
		params.WithKeyword(req.Keyword, "name", "display_name", "description")
	}

	if req.ResourceType != "" {
		params.WithResourceType(req.ResourceType)
	}

	if req.ParentID != nil {
		params.WithParentID(*req.ParentID)
	}

	if req.IsSystem != nil {
		params.WithIsSystem(*req.IsSystem)
	}

	if req.Path != "" {
		params.WithPath(req.Path)
	}

	// 执行查询
	result, err := s.resourceRepo.Find(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to list resources: %w", err)
	}

	// 转换为响应
	resources := make([]*dto.ResourceResponse, 0, len(result.Data))
	for _, resource := range result.Data {
		response, err := s.toResourceResponse(ctx, resource, false) // 列表不包含详细信息
		if err != nil {
			s.logger.Warn(ctx, "Failed to convert resource", logiface.Error(err))
			continue
		}
		resources = append(resources, response)
	}

	s.logger.Info(ctx, "Resources listed", logiface.Int("count", len(resources)))
	return &dto.ListResourcesResponse{
		Resources: resources,
		Total:     result.Total,
		Page:      req.Page,
		Size:      req.Size,
	}, nil
}

// GetResourceTree 获取资源树（优化版本：批量查询 + 分层加载 + 权限控制）
func (s *ResourceApplicationService) GetResourceTree(ctx context.Context, req *dto.ListResourceTreeRequest) ([]*dto.ResourceTreeNode, error) {
	startTime := time.Now()
	s.logger.Info(ctx, "Getting resource tree", logiface.Any("request", req))

	// 应用默认值
	req.ApplyDefaults()

	// 如果是懒加载请求，直接处理子节点加载
	if req.LazyLoad && req.ParentID != nil {
		return s.getChildrenForLazyLoad(ctx, req)
	}

	// 获取用户信息和权限检查
	userInfo, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		s.logger.Warn(ctx, "No user info found in context")
		return []*dto.ResourceTreeNode{}, nil
	}

	// 确定最终使用的 internal_app_id
	var finalInternalAppID *int64
	if req.InternalAppID != nil && *req.InternalAppID > 0 {
		// 优先使用请求参数中的 internal_app_id
		finalInternalAppID = req.InternalAppID
	} else {
		// 从 usercontext 获取作为兜底
		if contextAppID, exists := usercontext.GetInternalAppID(ctx); exists && contextAppID > 0 {
			finalInternalAppID = &contextAppID
		}
	}

	// 检查用户是否有超级管理员权限
	isSuperAdmin, err := s.checkUserSuperAdminRole(ctx, userInfo.UserID)
	if err != nil {
		s.logger.Error(ctx, "Failed to check user super admin role", logiface.Error(err))
		return nil, fmt.Errorf("failed to check user permissions: %w", err)
	}

	// 注意：已移除用户应用绑定检查，现在所有用户都可以通过租户选择来访问应用资源

	var resourceType *value_object.ResourceType
	if req.ResourceType != "" {
		rt := value_object.ResourceType(req.ResourceType)
		if !rt.IsValid() {
			return nil, fmt.Errorf("invalid resource type: %s", req.ResourceType)
		}
		resourceType = &rt
	}

	// 确定使用的 tenantID：优先使用请求中的，如果没有则从 context 获取
	var tenantID int64
	if req.TenantID > 0 {
		tenantID = req.TenantID
	} else {
		// 从 context 获取租户ID
		if contextTenantID, exists := usercontext.GetTenantID(ctx); exists && contextTenantID > 0 {
			tenantID = contextTenantID
		} else {
			return nil, fmt.Errorf("tenant ID not found in request or context")
		}
	}

	// 使用批量查询获取限制深度的资源树
	resources, err := s.resourceRepo.GetResourceTreeByLevels(ctx, req.MaxDepth, resourceType, finalInternalAppID, &tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get resources: %w", err)
	}

	// 如果指定了内部应用ID，还需要查询通过resource_app_assignments分配给应用的资源
	var relatedResources []userEntity.Resource
	if finalInternalAppID != nil {
		relatedResources, err = s.resourceAppAssignmentRepo.GetResourcesByApp(ctx, *finalInternalAppID, tenantID)
		if err != nil {
			s.logger.Warn(ctx, "Failed to get resources from resource_app_assignments",
				logiface.Error(err),
				logiface.Int64("internal_app_id", *finalInternalAppID),
				logiface.Int64("tenant_id", tenantID))
			// 不返回错误，只记录警告，继续使用原有资源
		} else {
			s.logger.Info(ctx, "Retrieved additional resources from resource_app_assignments",
				logiface.Int("related_resource_count", len(relatedResources)),
				logiface.Int64("internal_app_id", *finalInternalAppID))
		}
	}

	// 如果指定了内部应用ID，还需要查询resource表中internal_app_id匹配的资源
	var appOwnedResources []userEntity.Resource
	if finalInternalAppID != nil {
		appOwnedResources, err = s.getResourcesByInternalAppID(ctx, *finalInternalAppID, tenantID)
		if err != nil {
			s.logger.Warn(ctx, "Failed to get resources by internal_app_id",
				logiface.Error(err),
				logiface.Int64("internal_app_id", *finalInternalAppID),
				logiface.Int64("tenant_id", tenantID))
			// 不返回错误，只记录警告，继续使用原有资源
		} else {
			s.logger.Info(ctx, "Retrieved app-owned resources by internal_app_id",
				logiface.Int("app_owned_resource_count", len(appOwnedResources)),
				logiface.Int64("internal_app_id", *finalInternalAppID))
		}
	}

	// 合并三个数据源并去重
	allResources := s.mergeAndDeduplicateResources(resources, relatedResources, appOwnedResources)

	s.logger.Info(ctx, "Merged resource data sources",
		logiface.Int("original_resource_count", len(resources)),
		logiface.Int("related_resource_count", len(relatedResources)),
		logiface.Int("app_owned_resource_count", len(appOwnedResources)),
		logiface.Int("merged_resource_count", len(allResources)),
		logiface.Any("internal_app_id", finalInternalAppID))

	// 在内存中构建优化的树形结构
	tree, _, stats := s.buildOptimizedResourceTree(ctx, allResources, req.MaxDepth)

	queryTime := time.Since(startTime).Milliseconds()
	stats.QueryTimeMs = queryTime

	s.logger.Info(ctx, "Resource tree retrieved",
		logiface.Int("count", len(tree)),
		logiface.Int("max_depth", req.MaxDepth),
		logiface.Any("internal_app_id", finalInternalAppID),
		logiface.Bool("is_super_admin", isSuperAdmin),
		logiface.Int64("query_time_ms", queryTime))

	return tree, nil
}

// checkUserSuperAdminRole 检查用户是否有超级管理员权限
func (s *ResourceApplicationService) checkUserSuperAdminRole(ctx context.Context, userID int64) (bool, error) {
	// 获取用户角色
	roles, err := s.userRepo.GetUserRoles(ctx, userID)
	if err != nil {
		return false, err
	}

	// 检查用户是否有超级管理员角色
	for _, role := range roles {
		if role.Code == constants.RoleSuperAdmin {
			return true, nil
		}
	}

	return false, nil
}

// GetResourcePermissions 获取资源权限
func (s *ResourceApplicationService) GetResourcePermissions(ctx context.Context, req *dto.ResourcePermissionsRequest) (*dto.ResourcePermissionsResponse, error) {
	s.logger.Info(ctx, "Getting resource permissions", logiface.Any("request", req))

	// 获取资源信息
	resource, err := s.resourceRepo.FindByID(ctx, req.ResourceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource: %w", err)
	}
	if resource == nil {
		return nil, fmt.Errorf("resource not found")
	}

	// 获取资源权限
	permissions, err := s.resourceRepo.GetResourcePermissions(ctx, req.ResourceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource permissions: %w", err)
	}

	// 转换为响应格式
	permissionSummaries := make([]*dto.PermissionSummary, 0, len(permissions))
	for _, permission := range permissions {
		permissionSummaries = append(permissionSummaries, &dto.PermissionSummary{
			ID:               permission.ID,
			Name:             permission.Name,
			Code:             permission.Code,
			DisplayName:      permission.DisplayName,
			Scope:            permission.Scope,
			ScopeDisplayName: permission.GetScopeDisplayName(),
			Status:           permission.Status,
		})
	}

	resourceSummary := &dto.ResourceSummary{
		ID:           resource.ID,
		Name:         resource.Name,
		DisplayName:  resource.DisplayName,
		ResourceType: string(resource.ResourceType),
		Path:         resource.Path,
		Icon:         resource.Icon,
		IsSystem:     resource.IsSystem,
		IsUniversal:  resource.TenantID == 0,
	}

	s.logger.Info(ctx, "Resource permissions retrieved", logiface.Int("count", len(permissionSummaries)))
	return &dto.ResourcePermissionsResponse{
		ResourceID:  req.ResourceID,
		Resource:    resourceSummary,
		Permissions: permissionSummaries,
		Total:       int64(len(permissionSummaries)),
	}, nil
}

// ConfigureResourcePermissions 配置资源权限
func (s *ResourceApplicationService) ConfigureResourcePermissions(ctx context.Context, req *dto.ConfigureResourcePermissionsRequest) (*dto.ConfigureResourcePermissionsResponse, error) {
	s.logger.Info(ctx, "Configuring resource permissions", logiface.Any("request", req))

	// 验证资源是否存在
	resource, err := s.resourceRepo.FindByID(ctx, req.ResourceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource: %w", err)
	}
	if resource == nil {
		return nil, fmt.Errorf("resource not found")
	}

	var createdPermissions []*dto.PermissionSummary
	var skippedPermissions []dto.SkippedPermission

	for _, item := range req.Permissions {
		// 检查权限名称是否已存在
		existingPermissions, err := s.resourceRepo.GetResourcePermissions(ctx, req.ResourceID)
		if err != nil {
			s.logger.Warn(ctx, "Failed to check existing permissions",
				logiface.String("name", item.Name),
				logiface.Error(err))
			skippedPermissions = append(skippedPermissions, dto.SkippedPermission{
				Name:        item.Name,
				Code:        item.Code,
				DisplayName: item.DisplayName,
				Scope:       item.Scope,
				Reason:      "检查现有权限失败",
			})
			continue
		}

		// 检查是否已存在相同名称的权限
		exists := false
		for _, existing := range existingPermissions {
			if existing.Name == item.Name {
				exists = true
				break
			}
		}

		if exists {
			s.logger.Info(ctx, "Permission already exists for resource, skipping",
				logiface.String("name", item.Name),
				logiface.Int64("resource_id", req.ResourceID))
			skippedPermissions = append(skippedPermissions, dto.SkippedPermission{
				Name:        item.Name,
				Code:        item.Code,
				DisplayName: item.DisplayName,
				Scope:       item.Scope,
				Reason:      "权限已存在",
			})
			continue
		}

		// 确定使用的 tenantID：优先使用请求中的，如果没有则从 context 获取
		var tenantID int64
		if req.TenantID > 0 {
			tenantID = req.TenantID
		} else {
			// 从 context 获取租户ID
			if contextTenantID, exists := usercontext.GetTenantID(ctx); exists && contextTenantID > 0 {
				tenantID = contextTenantID
			} else {
				return nil, fmt.Errorf("tenant ID not found in request or context")
			}
		}

		// 创建权限实体
		permission, err := s.entityFactory.NewPermission(
			ctx,
			tenantID,
			item.Name,
			item.Code,
			item.DisplayName,
			item.Description,
			"access", // 默认action类型
			item.Scope,
		)
		if err != nil {
			s.logger.Warn(ctx, "Failed to create permission entity",
				logiface.String("name", item.Name),
				logiface.Error(err))
			skippedPermissions = append(skippedPermissions, dto.SkippedPermission{
				Name:        item.Name,
				Code:        item.Code,
				DisplayName: item.DisplayName,
				Scope:       item.Scope,
				Reason:      "创建权限实体失败",
			})
			continue
		}

		// 保存权限
		if err := s.permissionRepo.Create(ctx, permission); err != nil {
			s.logger.Warn(ctx, "Failed to save permission",
				logiface.String("name", item.Name),
				logiface.Error(err))
			skippedPermissions = append(skippedPermissions, dto.SkippedPermission{
				Name:        item.Name,
				Code:        item.Code,
				DisplayName: item.DisplayName,
				Scope:       item.Scope,
				Reason:      "保存权限失败",
			})
			continue
		}

		// 转换为响应格式
		createdPermissions = append(createdPermissions, &dto.PermissionSummary{
			ID:               permission.ID,
			Name:             permission.Name,
			Code:             permission.Code,
			DisplayName:      permission.DisplayName,
			Scope:            permission.Scope,
			ScopeDisplayName: permission.GetScopeDisplayName(),
			Status:           permission.Status,
		})
	}

	response := &dto.ConfigureResourcePermissionsResponse{
		ResourceID:         req.ResourceID,
		CreatedPermissions: createdPermissions,
		SkippedPermissions: skippedPermissions,
		SuccessCount:       len(createdPermissions),
		SkippedCount:       len(skippedPermissions),
		TotalCount:         len(req.Permissions),
	}

	s.logger.Info(ctx, "Resource permissions configuration completed",
		logiface.Int64("resource_id", req.ResourceID),
		logiface.Int("success_count", response.SuccessCount),
		logiface.Int("skipped_count", response.SkippedCount),
		logiface.Int("total_count", response.TotalCount))

	return response, nil
}

// GetAvailableAPIResources 获取可用的API资源（为页面资源分配API时使用）
func (s *ResourceApplicationService) GetAvailableAPIResources(ctx context.Context, pageResourceID int64, tenantID int64) (*dto.AvailableAPIResourcesResponse, error) {
	s.logger.Info(ctx, "Getting available API resources", logiface.Int64("pageResourceID", pageResourceID))

	// 验证页面资源存在且为page类型
	pageResource, err := s.resourceRepo.FindByID(ctx, pageResourceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get page resource: %w", err)
	}
	if pageResource == nil {
		return nil, fmt.Errorf("page resource not found")
	}
	if !pageResource.IsPage() {
		return nil, fmt.Errorf("resource is not a page type")
	}

	// 获取所有API类型的资源
	apiResources, err := s.resourceRepo.GetByResourceType(ctx, value_object.ResourceTypeAPI)
	if err != nil {
		return nil, fmt.Errorf("failed to get API resources: %w", err)
	}

	// 包含通用API资源
	if tenantID != 0 {
		universalAPIResources, err := s.resourceRepo.GetByResourceType(ctx, value_object.ResourceTypeAPI)
		if err == nil {
			apiResources = append(apiResources, universalAPIResources...)
		}
	}

	// 获取已分配的API资源ID
	assignedRelations, err := s.resourceRelationRepo.FindBySourceResource(ctx, pageResourceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get assigned API resources: %w", err)
	}

	assignedAPIIDs := make([]int64, 0, len(assignedRelations))
	for _, relation := range assignedRelations {
		assignedAPIIDs = append(assignedAPIIDs, relation.TargetResourceID)
	}

	// 转换为响应格式
	apiResourceSummaries := make([]*dto.ResourceSummary, 0, len(apiResources))
	for _, resource := range apiResources {
		apiResourceSummaries = append(apiResourceSummaries, &dto.ResourceSummary{
			ID:           resource.ID,
			Name:         resource.Name,
			DisplayName:  resource.DisplayName,
			ResourceType: string(resource.ResourceType),
			Path:         resource.Path,
			Icon:         resource.Icon,
			IsSystem:     resource.IsSystem,
			IsUniversal:  resource.TenantID == 0,
		})
	}

	s.logger.Info(ctx, "Available API resources retrieved", logiface.Int("count", len(apiResourceSummaries)))
	return &dto.AvailableAPIResourcesResponse{
		PageResourceID: pageResourceID,
		APIResources:   apiResourceSummaries,
		AssignedAPIIDs: assignedAPIIDs,
		Total:          int64(len(apiResourceSummaries)),
	}, nil
}

// BatchAssignAPIResources 批量分配API资源给页面资源
func (s *ResourceApplicationService) BatchAssignAPIResources(ctx context.Context, req *dto.BatchAssignAPIResourcesRequest) error {
	s.logger.Info(ctx, "Batch assigning API resources", logiface.Any("request", req))

	// 验证页面资源存在且为page类型
	pageResource, err := s.resourceRepo.FindByID(ctx, req.PageResourceID)
	if err != nil {
		return fmt.Errorf("failed to get page resource: %w", err)
	}
	if pageResource == nil {
		return fmt.Errorf("page resource not found")
	}
	if !pageResource.IsPage() {
		return fmt.Errorf("resource is not a page type")
	}

	// 验证所有API资源存在且为api类型
	for _, apiResourceID := range req.APIResourceIDs {
		apiResource, err := s.resourceRepo.FindByID(ctx, apiResourceID)
		if err != nil {
			return fmt.Errorf("failed to get API resource %d: %w", apiResourceID, err)
		}
		if apiResource == nil {
			return fmt.Errorf("API resource %d not found", apiResourceID)
		}
		if !apiResource.IsAPI() {
			return fmt.Errorf("resource %d is not an API type", apiResourceID)
		}
	}

	// 开始事务
	tx := s.resourceRepo.(*persistence.ResourceRepositoryImpl).GetDB().Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to start transaction: %w", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建资源关系仓储的事务版本
	relationRepo := persistence.NewResourceRelationRepository(tx, s.logger)

	// 创建资源关系
	relations := make([]userEntity.ResourceRelation, 0, len(req.APIResourceIDs))
	for _, apiResourceID := range req.APIResourceIDs {
		// 检查关系是否已存在
		exists, err := relationRepo.Exists(ctx, req.PageResourceID, apiResourceID)
		if err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to check relation existence: %w", err)
		}
		if exists {
			// 关系已存在，跳过
			s.logger.Info(ctx, "Relation already exists, skipping",
				logiface.Int64("pageResourceID", req.PageResourceID),
				logiface.Int64("apiResourceID", apiResourceID))
			continue
		}

		relation := &userEntity.ResourceRelation{
			TenantID:         pageResource.TenantID,
			SourceResourceID: req.PageResourceID,
			TargetResourceID: apiResourceID,
			Description:      fmt.Sprintf("页面资源 %s 依赖API资源", pageResource.DisplayName),
		}
		relations = append(relations, *relation)
	}

	// 批量创建关系
	if len(relations) > 0 {
		if err := relationRepo.BatchCreate(ctx, relations); err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create resource relations: %w", err)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	s.logger.Info(ctx, "API resources assigned successfully",
		logiface.Int64("pageResourceID", req.PageResourceID),
		logiface.Int("assignedCount", len(relations)),
		logiface.Int("totalRequested", len(req.APIResourceIDs)))
	return nil
}

// UpdateResource 更新资源
func (s *ResourceApplicationService) UpdateResource(ctx context.Context, req *dto.UpdateResourceRequest) (*userEntity.Resource, error) {
	s.logger.Info(ctx, "Updating resource", logiface.Any("request", req))

	resource, err := s.resourceRepo.FindByID(ctx, req.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get resource: %w", err)
	}
	if resource == nil {
		return nil, fmt.Errorf("resource not found")
	}

	// 验证名称唯一性（如果名称发生变化）
	if req.Name != "" && req.Name != resource.Name {
		if err := s.ValidateUniqueField(ctx, s.existsByNameAdapter, req.Name, resource.TenantID, "resource name"); err != nil {
			return nil, err
		}
	}

	// 更新资源信息
	if req.Name != "" {
		resource.Name = req.Name
	}
	if req.DisplayName != "" {
		resource.DisplayName = req.DisplayName
	}
	if req.Description != "" {
		resource.Description = req.Description
	}
	if req.ResourceType != "" {
		resourceType := value_object.ResourceType(req.ResourceType)
		if !resourceType.IsValid() {
			return nil, fmt.Errorf("invalid resource type: %s", req.ResourceType)
		}
		resource.ResourceType = resourceType
	}
	if req.ServiceName != "" {
		resource.ServiceName = req.ServiceName
	}
	if req.RequestType != "" {
		resource.RequestType = req.RequestType
	}
	if req.ResponseType != "" {
		resource.ResponseType = req.ResponseType
	}
	if req.APIMethod != "" {
		resource.APIMethod = req.APIMethod
	}
	if req.ContentType != "" {
		resource.ContentType = req.ContentType
	}
	if req.ParentID != nil {
		resource.ParentID = req.ParentID
	} else {
		// 如果 parentId 为 nil，则设置为 0 表示根节点
		// 注意：数据库中根节点的 parent_id 通常为 NULL，但为了前端兼容性设置为 0
		resource.ParentID = new(int64)
		*resource.ParentID = 0
	}
	if req.Path != "" {
		resource.Path = req.Path
	}
	if req.Icon != "" {
		resource.Icon = req.Icon
	}
	if req.SortOrder > 0 {
		resource.SortOrder = req.SortOrder
	}

	// 保存更新
	if err := s.resourceRepo.Update(ctx, resource); err != nil {
		return nil, fmt.Errorf("failed to update resource: %w", err)
	}

	s.logger.Info(ctx, "Resource updated", logiface.Any("resource", resource))
	return resource, nil
}

// DeleteResource 删除资源
func (s *ResourceApplicationService) DeleteResource(ctx context.Context, id int64) error {
	s.logger.Info(ctx, "Deleting resource", logiface.Int64("id", id))

	resource, err := s.resourceRepo.FindByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get resource: %w", err)
	}
	if resource == nil {
		return fmt.Errorf("resource not found")
	}

	// 系统资源不允许删除
	if resource.IsSystem {
		return fmt.Errorf("system resource cannot be deleted")
	}

	// 检查是否有子资源
	children, err := s.resourceRepo.GetChildren(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to check child resources: %w", err)
	}
	if len(children) > 0 {
		return fmt.Errorf("resource has child resources, cannot delete")
	}

	// 检查是否有关联的权限
	permissions, err := s.resourceRepo.GetResourcePermissions(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to check resource permissions: %w", err)
	}
	if len(permissions) > 0 {
		return fmt.Errorf("resource has associated permissions, cannot delete")
	}

	if err := s.resourceRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete resource: %w", err)
	}

	s.logger.Info(ctx, "Resource deleted", logiface.Int64("id", id))
	return nil
}

// GetResourceStats 获取资源统计信息
func (s *ResourceApplicationService) GetResourceStats(ctx context.Context, tenantID int64) (*dto.ResourceStatsResponse, error) {
	s.logger.Info(ctx, "Getting resource stats", logiface.Int64("tenantID", tenantID))

	// 获取总资源数
	totalResources, err := s.resourceRepo.Count(ctx, userRepo.NewQueryParams().WithTenantID(tenantID))
	if err != nil {
		return nil, fmt.Errorf("failed to get total resources count: %w", err)
	}

	// 获取系统资源数
	systemResources, err := s.resourceRepo.Count(ctx, userRepo.NewQueryParams().
		WithTenantID(tenantID).
		WithIsSystem(true))
	if err != nil {
		return nil, fmt.Errorf("failed to get system resources count: %w", err)
	}

	// 获取通用资源数
	universalResources, err := s.resourceRepo.Count(ctx, userRepo.NewQueryParams().WithTenantID(0))
	if err != nil {
		return nil, fmt.Errorf("failed to get universal resources count: %w", err)
	}

	// 获取根资源数
	rootResources, err := s.resourceRepo.GetRootResources(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get root resources: %w", err)
	}

	// 获取资源类型统计
	resourceTypeCount := make(map[string]int64)
	for _, resourceType := range value_object.GetAllResourceTypes() {
		count, err := s.resourceRepo.Count(ctx, userRepo.NewQueryParams().
			WithTenantID(tenantID).
			WithResourceType(string(resourceType)))
		if err != nil {
			s.logger.Warn(ctx, "Failed to get resource type count", logiface.Error(err))
			continue
		}
		resourceTypeCount[string(resourceType)] = count
	}

	// TODO: 计算最大深度和各租户统计
	maxDepth := s.calculateMaxDepth(ctx, tenantID)
	tenantStats := make([]*dto.TenantResourceSummary, 0)

	s.logger.Info(ctx, "Resource stats retrieved")
	return &dto.ResourceStatsResponse{
		TotalResources:      totalResources,
		SystemResources:     systemResources,
		UniversalResources:  universalResources,
		ResourceTypeCount:   resourceTypeCount,
		MaxDepth:            maxDepth,
		RootResourcesCount:  int64(len(rootResources)),
		TenantResourceStats: tenantStats,
	}, nil
}

// validateParentChildCompatibility 验证父子资源类型兼容性
// 修复问题: 允许 page 类型资源有 page 类型的子资源，支持更灵活的层级结构
func (s *ResourceApplicationService) validateParentChildCompatibility(parentType, childType value_object.ResourceType) error {
	// 定义允许的父子关系
	// 规则说明:
	// - menu: 可包含子菜单和页面
	// - page: 可包含子页面、按钮、API和功能 (修复: 新增支持子页面)
	// - system: 作为顶级容器，可包含所有主要类型
	// - function: 可包含子功能、API和按钮
	// - data: 可包含子数据结构和相关API
	allowedRelations := map[value_object.ResourceType][]value_object.ResourceType{
		value_object.ResourceTypeMenu:     {value_object.ResourceTypeMenu, value_object.ResourceTypePage},
		value_object.ResourceTypePage:     {value_object.ResourceTypePage, value_object.ResourceTypeButton, value_object.ResourceTypeAPI, value_object.ResourceTypeFunction},
		value_object.ResourceTypeSystem:   {value_object.ResourceTypeMenu, value_object.ResourceTypePage, value_object.ResourceTypeAPI, value_object.ResourceTypeFunction, value_object.ResourceTypeData},
		value_object.ResourceTypeFunction: {value_object.ResourceTypeFunction, value_object.ResourceTypeAPI, value_object.ResourceTypeButton},
		value_object.ResourceTypeData:     {value_object.ResourceTypeData, value_object.ResourceTypeAPI},
	}

	if allowedChildren, exists := allowedRelations[parentType]; exists {
		for _, allowedChild := range allowedChildren {
			if childType == allowedChild {
				return nil
			}
		}
	}

	return fmt.Errorf("resource type %s cannot be child of %s", childType, parentType)
}

// buildResourceTree 构建资源树
func (s *ResourceApplicationService) buildResourceTree(ctx context.Context, resources []userEntity.Resource) ([]*dto.ResourceTreeNode, int) {
	// 创建资源映射
	resourceMap := make(map[int64]*dto.ResourceTreeNode)
	var rootNodes []*dto.ResourceTreeNode
	maxDepth := 0

	// 第一遍：创建所有节点
	for _, resource := range resources {
		response, err := s.toResourceResponse(ctx, &resource, false)
		if err != nil {
			s.logger.Warn(ctx, "Failed to convert resource", logiface.Error(err))
			continue
		}

		node := &dto.ResourceTreeNode{
			ResourceResponse: response,
			Level:            0,
			IsLeaf:           true,
			Expanded:         false,
			Children:         make([]*dto.ResourceTreeNode, 0),
		}
		resourceMap[resource.ID] = node
	}

	// 第二遍：建立父子关系
	for _, resource := range resources {
		node := resourceMap[resource.ID]
		if node == nil {
			continue
		}

		// 根节点：parentId为0或nil的节点
		if resource.ParentID == nil || *resource.ParentID == 0 {
			// 根节点
			rootNodes = append(rootNodes, node)
			node.Level = 1
		} else {
			// 子节点
			parentNode := resourceMap[*resource.ParentID]
			if parentNode != nil {
				parentNode.Children = append(parentNode.Children, node)
				parentNode.IsLeaf = false
				node.Level = parentNode.Level + 1
				if node.Level > maxDepth {
					maxDepth = node.Level
				}
			}
		}
	}

	return rootNodes, maxDepth
}

// calculateMaxDepth 计算最大深度
func (s *ResourceApplicationService) calculateMaxDepth(ctx context.Context, tenantID int64) int {
	// TODO: 实现深度计算逻辑
	return 0
}

// toResourceResponse 转换为资源响应
func (s *ResourceApplicationService) toResourceResponse(ctx context.Context, resource *userEntity.Resource, includeDetails bool) (*dto.ResourceResponse, error) {
	response := &dto.ResourceResponse{
		ID:           resource.ID,
		TenantID:     resource.TenantID,
		Name:         resource.Name,
		DisplayName:  resource.DisplayName,
		Description:  resource.Description,
		ResourceType: string(resource.ResourceType),
		ServiceName:  resource.ServiceName,
		RequestType:  resource.RequestType,
		ResponseType: resource.ResponseType,
		APIMethod:    resource.APIMethod,
		ContentType:  resource.ContentType,
		ParentID:     resource.ParentID,
		Path:         resource.Path,
		Icon:         resource.Icon,
		SortOrder:    resource.SortOrder,
		IsSystem:     resource.IsSystem,
		IsUniversal:  resource.TenantID == 0,
		HasChildren:  false,
		Depth:        0,
		FullPath:     resource.GetFullPath(),
		CreatedAt:    resource.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:    resource.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 如果需要包含详细信息
	if includeDetails {
		// 获取父资源信息
		if resource.ParentID != nil {
			parent, err := s.resourceRepo.FindByID(ctx, *resource.ParentID)
			if err == nil && parent != nil {
				response.Parent = &dto.ResourceSummary{
					ID:           parent.ID,
					Name:         parent.Name,
					DisplayName:  parent.DisplayName,
					ResourceType: string(parent.ResourceType),
					Path:         parent.Path,
					Icon:         parent.Icon,
					IsSystem:     parent.IsSystem,
					IsUniversal:  parent.TenantID == 0,
				}
			}
		}

		// 获取权限信息
		permissions, err := s.resourceRepo.GetResourcePermissions(ctx, resource.ID)
		if err == nil {
			response.Permissions = make([]*dto.PermissionSummary, 0, len(permissions))
			for _, permission := range permissions {
				response.Permissions = append(response.Permissions, &dto.PermissionSummary{
					ID:               permission.ID,
					Name:             permission.Name,
					Code:             permission.Code,
					DisplayName:      permission.DisplayName,
					Scope:            permission.Scope,
					ScopeDisplayName: permission.GetScopeDisplayName(),
					Status:           permission.Status,
				})
			}
		}

		// 如果是页面资源，获取关联的API资源
		if resource.IsPage() {
			// TODO: 获取关联的API资源
			response.APIResources = make([]*dto.ResourceSummary, 0)
		}
	}

	return response, nil
}

// toResourceResponseWithChildren 转换为资源响应（包含子节点信息）
func (s *ResourceApplicationService) toResourceResponseWithChildren(ctx context.Context, resource *userEntity.Resource, childrenCount int, includeDetails bool) (*dto.ResourceResponse, error) {
	response, err := s.toResourceResponse(ctx, resource, includeDetails)
	if err != nil {
		return nil, err
	}

	// 设置子节点信息
	response.HasChildren = childrenCount > 0

	return response, nil
}

// getChildrenForLazyLoad 获取懒加载的子节点
func (s *ResourceApplicationService) getChildrenForLazyLoad(ctx context.Context, req *dto.ListResourceTreeRequest) ([]*dto.ResourceTreeNode, error) {
	startTime := time.Now()
	s.logger.Info(ctx, "Getting children for lazy load",
		logiface.Int64("parent_id", *req.ParentID),
		logiface.Int("max_depth", req.MaxDepth))

	// 获取指定父节点的子节点
	children, err := s.resourceRepo.GetResourcesByParentIDs(ctx, []int64{*req.ParentID})
	if err != nil {
		return nil, fmt.Errorf("failed to get children: %w", err)
	}

	// 如果需要更深层级，递归获取
	if req.MaxDepth > 1 {
		var allResources []userEntity.Resource
		allResources = append(allResources, children...)

		// 收集所有子节点ID
		currentLevelIDs := make([]int64, 0, len(children))
		for _, child := range children {
			currentLevelIDs = append(currentLevelIDs, child.ID)
		}

		// 递归获取更深层级
		for level := 2; level <= req.MaxDepth && len(currentLevelIDs) > 0; level++ {
			nextLevelResources, err := s.resourceRepo.GetResourcesByParentIDs(ctx, currentLevelIDs)
			if err != nil {
				s.logger.Warn(ctx, "Failed to get next level resources", logiface.Error(err))
				break
			}

			if len(nextLevelResources) == 0 {
				break
			}

			allResources = append(allResources, nextLevelResources...)

			// 更新下一层的父ID列表
			currentLevelIDs = currentLevelIDs[:0]
			for _, resource := range nextLevelResources {
				currentLevelIDs = append(currentLevelIDs, resource.ID)
			}
		}

		children = allResources
	}

	// 构建树形结构
	tree, _, stats := s.buildOptimizedResourceTree(ctx, children, req.MaxDepth)

	queryTime := time.Since(startTime).Milliseconds()
	stats.QueryTimeMs = queryTime

	s.logger.Info(ctx, "Children loaded for lazy load",
		logiface.Int("count", len(tree)),
		logiface.Int64("query_time_ms", queryTime))

	return tree, nil
}

// buildOptimizedResourceTree 构建优化的资源树（批量查询版本）
func (s *ResourceApplicationService) buildOptimizedResourceTree(ctx context.Context, resources []userEntity.Resource, maxDepth int) ([]*dto.ResourceTreeNode, int, *dto.TreeQueryStats) {
	stats := &dto.TreeQueryStats{
		TotalQueries:     1, // 主查询
		BatchQueriesUsed: 1,
		NodesProcessed:   len(resources),
	}

	// 创建资源映射，使用ID作为key去重
	resourceMap := make(map[int64]*dto.ResourceTreeNode)
	var rootNodes []*dto.ResourceTreeNode
	actualMaxDepth := 0

	// 如果没有资源，直接返回
	if len(resources) == 0 {
		return rootNodes, actualMaxDepth, stats
	}

	// 收集所有父ID，用于批量查询子节点数量
	parentIDs := make([]int64, 0, len(resources))
	for _, resource := range resources {
		parentIDs = append(parentIDs, resource.ID)
	}

	// 批量获取子节点数量
	childrenCounts, err := s.resourceRepo.GetChildrenCounts(ctx, parentIDs)
	if err != nil {
		s.logger.Warn(ctx, "Failed to get children counts", logiface.Error(err))
		childrenCounts = make(map[int64]int)
	} else {
		stats.TotalQueries++
		stats.BatchQueriesUsed++
	}

	// 检查是否有更深层级的子节点
	hasDeepChildren, err := s.resourceRepo.CheckHasDeepChildren(ctx, parentIDs, maxDepth)
	if err != nil {
		s.logger.Warn(ctx, "Failed to check deep children", logiface.Error(err))
		hasDeepChildren = make(map[int64]bool)
	} else {
		stats.TotalQueries++
		stats.BatchQueriesUsed++
	}

	// 第一遍：创建所有节点（去重）
	for _, resource := range resources {
		// 如果节点已存在，跳过
		if _, exists := resourceMap[resource.ID]; exists {
			continue
		}

		childrenCount := childrenCounts[resource.ID]
		lazyLoad := hasDeepChildren[resource.ID]

		response, err := s.toResourceResponseWithChildren(ctx, &resource, childrenCount, false)
		if err != nil {
			s.logger.Warn(ctx, "Failed to convert resource", logiface.Error(err))
			continue
		}

		node := &dto.ResourceTreeNode{
			ResourceResponse: response,
			Level:            0, // 将在第二遍中设置
			IsLeaf:           childrenCount == 0,
			Expanded:         false,
			LazyLoad:         lazyLoad,
			ChildrenCount:    childrenCount,
			Children:         make([]*dto.ResourceTreeNode, 0),
		}
		resourceMap[resource.ID] = node
	}

	// 第二遍：建立父子关系并设置层级
	for _, resource := range resources {
		node := resourceMap[resource.ID]
		if node == nil {
			continue
		}

		// 根节点：parentId为0或nil的节点
		if resource.ParentID == nil || *resource.ParentID == 0 {
			// 根节点
			rootNodes = append(rootNodes, node)
			node.Level = 1
			if node.Level > actualMaxDepth {
				actualMaxDepth = node.Level
			}
		} else {
			// 子节点
			parentNode := resourceMap[*resource.ParentID]
			if parentNode != nil {
				// 检查是否已经添加过这个子节点
				alreadyAdded := false
				for _, child := range parentNode.Children {
					if child.ID == node.ID {
						alreadyAdded = true
						break
					}
				}
				if !alreadyAdded {
					parentNode.Children = append(parentNode.Children, node)
					parentNode.IsLeaf = false
					node.Level = parentNode.Level + 1
					if node.Level > actualMaxDepth {
						actualMaxDepth = node.Level
					}
				}
			} else {
				// 父节点不在当前结果集中，可能是跨层级查询
				// 将其作为根节点处理
				rootNodes = append(rootNodes, node)
				node.Level = 1
			}
		}
	}

	return rootNodes, actualMaxDepth, stats
}

// checkHasMoreLevels 检查是否还有更深层级
func (s *ResourceApplicationService) checkHasMoreLevels(ctx context.Context, tree []*dto.ResourceTreeNode, tenantID int64, maxDepth int) bool {
	// 收集最深层级的节点ID
	deepestLevelIDs := s.collectDeepestLevelIDs(tree, maxDepth)
	if len(deepestLevelIDs) == 0 {
		return false
	}

	// 检查这些节点是否有子节点
	childrenCounts, err := s.resourceRepo.GetChildrenCounts(ctx, deepestLevelIDs)
	if err != nil {
		s.logger.Warn(ctx, "Failed to check for more levels", logiface.Error(err))
		return false
	}

	// 如果任何节点有子节点，说明还有更深层级
	for _, count := range childrenCounts {
		if count > 0 {
			return true
		}
	}

	return false
}

// collectDeepestLevelIDs 收集最深层级的节点ID
func (s *ResourceApplicationService) collectDeepestLevelIDs(tree []*dto.ResourceTreeNode, maxDepth int) []int64 {
	var deepestIDs []int64

	var traverse func([]*dto.ResourceTreeNode)
	traverse = func(nodes []*dto.ResourceTreeNode) {
		for _, node := range nodes {
			if node.Level == maxDepth {
				deepestIDs = append(deepestIDs, node.ID)
			} else if len(node.Children) > 0 {
				traverse(node.Children)
			}
		}
	}

	traverse(tree)
	return deepestIDs
}

// LoadChildren 加载子节点（新的API方法）
func (s *ResourceApplicationService) LoadChildren(ctx context.Context, req *dto.LoadChildrenRequest) (*dto.LoadChildrenResponse, error) {
	s.logger.Info(ctx, "Loading children", logiface.Any("request", req))

	// 应用默认值
	req.ApplyDefaults()

	// 确定使用的 tenantID：优先使用请求中的，如果没有则从 context 获取
	var tenantID int64
	if req.TenantID > 0 {
		tenantID = req.TenantID
	} else {
		// 从 context 获取租户ID
		if contextTenantID, exists := usercontext.GetTenantID(ctx); exists && contextTenantID > 0 {
			tenantID = contextTenantID
		} else {
			return nil, fmt.Errorf("tenant ID not found in request or context")
		}
	}

	// 构建树请求
	treeReq := &dto.ListResourceTreeRequest{
		TenantID:     tenantID,
		ResourceType: req.ResourceType,
		MaxDepth:     req.MaxDepth,
		ParentID:     &req.ParentID,
		LazyLoad:     true,
	}

	// 获取子树
	treeResponse, err := s.GetResourceTree(ctx, treeReq)
	if err != nil {
		return nil, err
	}

	s.logger.Info(ctx, "Children loaded",
		logiface.Int64("parent_id", req.ParentID),
		logiface.Int("count", len(treeResponse)))

	return &dto.LoadChildrenResponse{
		ParentID:      req.ParentID,
		Children:      treeResponse,
		Total:         int64(len(treeResponse)),
		HasMoreLevels: false, // LazyLoad 时没有更多层级概念
		LoadedDepth:   req.MaxDepth,
	}, nil
}

// AssignResourcesToAppResult 资源分配结果
type AssignResourcesToAppResult struct {
	SuccessCount int
	FailureCount int
	TotalCount   int
}

// AssignResourcesToApp 将资源分配给应用
func (s *ResourceApplicationService) AssignResourcesToApp(ctx context.Context, resourceIDs []int64, appName string, tenantID int64, targetInternalAppID int64) (*AssignResourcesToAppResult, error) {
	s.logger.Info(ctx, "Starting resource assignment to app",
		logiface.String("app_name", appName),
		logiface.Int("resource_count", len(resourceIDs)),
		logiface.Int64("tenant_id", tenantID),
		logiface.Int64("target_internal_app_id", targetInternalAppID),
	)

	result := &AssignResourcesToAppResult{
		TotalCount: len(resourceIDs),
	}

	// 验证应用名称和租户ID
	if appName == "" {
		return nil, fmt.Errorf("app name cannot be empty")
	}

	if tenantID <= 0 {
		return nil, fmt.Errorf("invalid tenant ID")
	}

	// 验证目标应用ID
	if targetInternalAppID <= 0 {
		return nil, fmt.Errorf("invalid target internal app ID")
	}

	// 获取当前用户ID
	assignedBy := s.getUserIDFromContext(ctx)

	// 批量验证资源
	validResources := make([]*userEntity.Resource, 0, len(resourceIDs))
	for _, resourceID := range resourceIDs {
		resource, err := s.resourceRepo.FindByID(ctx, resourceID)
		if err != nil {
			s.logger.Error(ctx, "Failed to find resource",
				logiface.Int64("resource_id", resourceID),
				logiface.Error(err),
			)
			result.FailureCount++
			continue
		}

		if resource == nil {
			s.logger.Warn(ctx, "Resource not found",
				logiface.Int64("resource_id", resourceID),
			)
			result.FailureCount++
			continue
		}

		// 检查资源是否属于指定租户（或者是通用资源）
		isUniversal := resource.TenantID == 0
		if !isUniversal && resource.TenantID != tenantID {
			s.logger.Warn(ctx, "Resource does not belong to tenant",
				logiface.Int64("resource_id", resourceID),
				logiface.Int64("resource_tenant_id", resource.TenantID),
				logiface.Int64("request_tenant_id", tenantID),
			)
			result.FailureCount++
			continue
		}

		// 检查资源是否可分配
		if !resource.IsAssignable() {
			s.logger.Warn(ctx, "Resource is not assignable",
				logiface.Int64("resource_id", resourceID),
			)
			result.FailureCount++
			continue
		}

		// 检查资源是否已经分配给该应用
		exists, err := s.resourceAppAssignmentRepo.Exists(ctx, resourceID, targetInternalAppID)
		if err != nil {
			s.logger.Error(ctx, "Failed to check resource assignment existence",
				logiface.Int64("resource_id", resourceID),
				logiface.Int64("app_id", targetInternalAppID),
				logiface.Error(err),
			)
			result.FailureCount++
			continue
		}

		if exists {
			s.logger.Warn(ctx, "Resource already assigned to app",
				logiface.Int64("resource_id", resourceID),
				logiface.Int64("app_id", targetInternalAppID),
			)
			result.FailureCount++
			continue
		}

		validResources = append(validResources, resource)
	}

	// 批量创建资源应用关联
	if len(validResources) > 0 {
		assignments := make([]userEntity.ResourceAppAssignment, 0, len(validResources))
		for _, resource := range validResources {
			assignment := &userEntity.ResourceAppAssignment{
				TenantID:      tenantID,
				ResourceID:    resource.ID,
				InternalAppID: targetInternalAppID,
				IsActive:      true,
				AssignedBy:    &assignedBy,
				AssignedAt:    time.Now(),
			}
			assignments = append(assignments, *assignment)
		}

		// 批量保存到数据库
		if err := s.resourceAppAssignmentRepo.BatchCreate(ctx, assignments); err != nil {
			s.logger.Error(ctx, "Failed to batch create resource app assignments",
				logiface.Int("assignment_count", len(assignments)),
				logiface.Int64("app_id", targetInternalAppID),
				logiface.Int64("tenant_id", tenantID),
				logiface.Error(err),
			)
			return nil, fmt.Errorf("failed to assign resources to app: %w", err)
		}

		s.logger.Info(ctx, "Resources assigned to app successfully",
			logiface.Int("success_count", len(validResources)),
			logiface.String("app_name", appName),
			logiface.Int64("app_id", targetInternalAppID),
			logiface.Int64("tenant_id", tenantID),
		)

		result.SuccessCount = len(validResources)
	}

	s.logger.Info(ctx, "Resource assignment to app completed",
		logiface.String("app_name", appName),
		logiface.Int("success_count", result.SuccessCount),
		logiface.Int("failure_count", result.FailureCount),
		logiface.Int("total_count", result.TotalCount),
		logiface.Int64("tenant_id", tenantID),
	)

	return result, nil
}

// getUserIDFromContext 从上下文获取用户ID
func (s *ResourceApplicationService) getUserIDFromContext(ctx context.Context) int64 {
	userInfo, ok := usercontext.GetUserInfo(ctx)
	if !ok {
		return 0 // 如果无法获取用户信息，返回0
	}
	return userInfo.UserID
}

// getResourcesByInternalAppID 获取通过internal_app_id匹配的资源
func (s *ResourceApplicationService) getResourcesByInternalAppID(ctx context.Context, internalAppID, tenantID int64) ([]userEntity.Resource, error) {
	// 构建查询参数，查询resource表中internal_app_id匹配的资源
	queryParams := &userRepo.QueryParams{
		InternalAppID: &internalAppID,
		TenantID:      &tenantID,
		// 可以添加其他过滤条件，如资源类型、状态等
	}

	// 使用资源仓储的Find方法查询
	result, err := s.resourceRepo.Find(ctx, queryParams)
	if err != nil {
		return nil, fmt.Errorf("failed to get resources by internal_app_id: %w", err)
	}

	// 转换类型：从 []*Resource 到 []Resource
	resources := make([]userEntity.Resource, len(result.Data))
	for i, resource := range result.Data {
		resources[i] = *resource
	}

	return resources, nil
}

// findParentResourceWithoutTenantInjection 不使用基础组件注入tenantId的方式查询父资源
func (s *ResourceApplicationService) findParentResourceWithoutTenantInjection(ctx context.Context, parentID, tenantID int64) (*userEntity.Resource, error) {
	// 使用新的仓储方法，不依赖基础组件的tenantId注入
	parent, err := s.resourceRepo.FindByIDWithoutTenantInjection(ctx, parentID)
	if err != nil {
		return nil, err
	}
	return parent, nil
}

// mergeAndDeduplicateResources 合并三个资源数组并去重
func (s *ResourceApplicationService) mergeAndDeduplicateResources(resources []userEntity.Resource, relatedResources []userEntity.Resource, appOwnedResources []userEntity.Resource) []userEntity.Resource {
	// 使用map去重，以resource ID为key
	resourceMap := make(map[int64]userEntity.Resource)

	// 先添加原始资源
	for _, resource := range resources {
		resourceMap[resource.ID] = resource
	}

	// 再添加关联资源，如果ID已存在则跳过
	for _, resource := range relatedResources {
		if _, exists := resourceMap[resource.ID]; !exists {
			resourceMap[resource.ID] = resource
		}
	}

	// 最后添加应用拥有的资源，如果ID已存在则跳过
	for _, resource := range appOwnedResources {
		if _, exists := resourceMap[resource.ID]; !exists {
			resourceMap[resource.ID] = resource
		}
	}

	// 转换回数组并按sort_order排序
	mergedResources := make([]userEntity.Resource, 0, len(resourceMap))
	for _, resource := range resourceMap {
		mergedResources = append(mergedResources, resource)
	}

	// 按sort_order排序，然后按ID排序确保顺序一致
	sort.Slice(mergedResources, func(i, j int) bool {
		if mergedResources[i].SortOrder != mergedResources[j].SortOrder {
			return mergedResources[i].SortOrder < mergedResources[j].SortOrder
		}
		return mergedResources[i].ID < mergedResources[j].ID
	})

	return mergedResources
}
