package service

import (
	"context"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	userErrors "gitee.com/heiyee/platforms/users/internal/domain/errors"
	userEntity "gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	userRepo "gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
)

// PermissionApplicationService 权限应用服务
type PermissionApplicationService struct {
	*BaseService
	permissionRepo userRepo.PermissionRepository
	entityFactory  *userEntity.EntityFactory
	logger         logiface.Logger
}

// NewPermissionApplicationService 创建权限应用服务
func NewPermissionApplicationService(permissionRepo userRepo.PermissionRepository, entityFactory *userEntity.EntityFactory, logger logiface.Logger) *PermissionApplicationService {
	return &PermissionApplicationService{
		BaseService:    NewBaseService(),
		permissionRepo: permissionRepo,
		entityFactory:  entityFactory,
		logger:         logger,
	}
}

// existsByNameAdapter 适配器方法，用于ValidateUniqueField
func (s *PermissionApplicationService) existsByNameAdapter(ctx context.Context, tenantID int64, value string) (bool, error) {
	return s.permissionRepo.ExistsByName(ctx, value)
}

// CreatePermission 创建权限
func (s *PermissionApplicationService) CreatePermission(ctx context.Context, req *dto.CreatePermissionRequest) (*userEntity.Permission, error) {
	s.logger.Info(ctx, "Creating permission", logiface.Any("request", req))
	if err := s.ValidateUniqueField(ctx, s.existsByNameAdapter, req.Name, req.TenantID, "permission name"); err != nil {
		return nil, err
	}

	// 创建权限实体
	permission, err := s.entityFactory.NewPermission(
		ctx,
		req.TenantID,
		req.Name,
		req.Code,
		req.DisplayName,
		req.Description,
		req.Action,
		req.Scope,
	)
	if err != nil {
		return nil, userErrors.NewSystemError("failed to create permission entity", err.Error())
	}

	// 如果是系统权限，设置系统标识
	if req.IsSystem {
		permission.IsSystem = true
	}

	// 保存权限
	if err := s.permissionRepo.Create(ctx, permission); err != nil {
		return nil, userErrors.NewDatabaseError("failed to create permission", err.Error())
	}

	s.logger.Info(ctx, "Permission created", logiface.Any("permission", permission))
	return permission, nil
}

// BatchCreatePermissions 批量创建权限
func (s *PermissionApplicationService) BatchCreatePermissions(ctx context.Context, req *dto.BatchCreatePermissionsRequest) (*dto.BatchCreatePermissionsResponse, error) {
	s.logger.Info(ctx, "Batch creating permissions", logiface.Any("request", req))

	var createdPermissions []*dto.PermissionResponse
	var skippedPermissions []dto.SkippedPermission

	for _, item := range req.Permissions {
		// 检查权限名称是否已存在
		exists, err := s.permissionRepo.ExistsByName(ctx, item.Name)
		if err != nil {
			s.logger.Warn(ctx, "Failed to check permission existence",
				logiface.String("name", item.Name),
				logiface.Error(err))
			skippedPermissions = append(skippedPermissions, dto.SkippedPermission{
				Name:        item.Name,
				Code:        item.Code,
				DisplayName: item.DisplayName,
				Scope:       item.Scope,
				Reason:      "检查权限存在性失败",
			})
			continue
		}

		if exists {
			s.logger.Info(ctx, "Permission already exists, skipping",
				logiface.String("name", item.Name))
			skippedPermissions = append(skippedPermissions, dto.SkippedPermission{
				Name:        item.Name,
				Code:        item.Code,
				DisplayName: item.DisplayName,
				Scope:       item.Scope,
				Reason:      "权限已存在",
			})
			continue
		}

		// 创建权限实体
		permission, createErr := s.entityFactory.NewPermission(
			ctx,
			req.TenantID,
			item.Name,
			item.Code,
			item.DisplayName,
			item.Description,
			item.Action,
			item.Scope,
		)

		if createErr != nil {
			s.logger.Warn(ctx, "Failed to create permission entity",
				logiface.String("name", item.Name),
				logiface.Error(createErr))
			skippedPermissions = append(skippedPermissions, dto.SkippedPermission{
				Name:        item.Name,
				Code:        item.Code,
				DisplayName: item.DisplayName,
				Scope:       item.Scope,
				Reason:      "创建权限实体失败",
			})
			continue
		}

		// 如果是系统权限，设置系统标识
		if req.IsSystem {
			permission.IsSystem = true
		}

		// 保存权限
		if err := s.permissionRepo.Create(ctx, permission); err != nil {
			s.logger.Warn(ctx, "Failed to save permission",
				logiface.String("name", item.Name),
				logiface.Error(err))
			skippedPermissions = append(skippedPermissions, dto.SkippedPermission{
				Name:        item.Name,
				Code:        item.Code,
				DisplayName: item.DisplayName,
				Scope:       item.Scope,
				Reason:      "保存权限失败",
			})
			continue
		}

		// 转换为响应格式
		createdPermissions = append(createdPermissions, s.toPermissionResponse(permission))
	}

	response := &dto.BatchCreatePermissionsResponse{
		CreatedPermissions: createdPermissions,
		SkippedPermissions: skippedPermissions,
		SuccessCount:       len(createdPermissions),
		SkippedCount:       len(skippedPermissions),
		TotalCount:         len(req.Permissions),
	}

	s.logger.Info(ctx, "Batch permission creation completed",
		logiface.Int("success_count", response.SuccessCount),
		logiface.Int("skipped_count", response.SkippedCount),
		logiface.Int("total_count", response.TotalCount))

	return response, nil
}

// GetPermission 获取权限
func (s *PermissionApplicationService) GetPermission(ctx context.Context, id int64) (*userEntity.Permission, error) {
	s.logger.Info(ctx, "Getting permission", logiface.Int64("id", id))
	permission, err := s.permissionRepo.FindByID(ctx, id)
	if err != nil {
		return nil, userErrors.NewDatabaseError("failed to get permission", err.Error())
	}
	if permission == nil {
		return nil, userErrors.NewUserError(userErrors.CodePermissionNotFound, "permission not found")
	}
	s.logger.Info(ctx, "Permission retrieved", logiface.Any("permission", permission))
	return permission, nil
}

// ListPermissions 获取权限列表
func (s *PermissionApplicationService) ListPermissions(ctx context.Context, req *dto.ListPermissionsRequest) (*dto.ListPermissionsResponse, error) {
	s.logger.Info(ctx, "Listing permissions", logiface.Any("request", req))
	// 构建查询参数
	params := userRepo.NewQueryParams().
		WithTenantID(req.TenantID).
		WithPagination(req.Offset, req.Limit).
		WithOrder(req.OrderBy, req.OrderDir)

	// 添加搜索条件
	if req.Keyword != "" {
		params.WithKeyword(req.Keyword, "name", "display_name", "description")
	}

	if req.Status != "" {
		status := value_object.UserStatus(req.Status)
		params.WithStatus(status)
	}

	// 移除ResourceID相关的过滤，因为权限不再直接关联资源

	if req.IsSystem != nil {
		params.WithIsSystem(*req.IsSystem)
	}

	// 执行查询
	result, err := s.permissionRepo.Find(ctx, params)
	if err != nil {
		return nil, userErrors.NewDatabaseError("failed to list permissions", err.Error())
	}

	// 转换为响应 - 确保即使没有数据也返回空数组而不是null
	permissions := make([]*dto.PermissionResponse, 0, len(result.Data))
	for _, permission := range result.Data {
		permissions = append(permissions, s.toPermissionResponse(permission))
	}

	s.logger.Info(ctx, "Permissions listed", logiface.Any("response", permissions))
	return &dto.ListPermissionsResponse{
		Permissions: permissions,
		Total:       result.Total,
		Page:        req.Offset/req.Limit + 1,
		Size:        req.Limit,
	}, nil
}

// UpdatePermission 更新权限
func (s *PermissionApplicationService) UpdatePermission(ctx context.Context, id int64, req *dto.UpdatePermissionRequest) (*userEntity.Permission, error) {
	s.logger.Info(ctx, "Updating permission", logiface.Int64("id", id), logiface.Any("request", req))
	permission, err := s.permissionRepo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if permission == nil {
		return nil, userErrors.NewUserError(userErrors.CodePermissionNotFound, "permission not found")
	}
	if req.Name != "" && req.Name != permission.Name {
		if err := s.ValidateUniqueField(ctx, s.existsByNameAdapter, req.Name, permission.TenantID, "permission name"); err != nil {
			return nil, err
		}
	}

	// 更新权限信息
	if req.Name != "" {
		permission.Name = req.Name
	}
	if req.Code != "" {
		permission.Code = req.Code
	}
	if req.DisplayName != "" {
		permission.DisplayName = req.DisplayName
	}
	if req.Description != "" {
		permission.Description = req.Description
	}
	if req.Action != "" {
		permission.Action = req.Action
	}
	if req.Scope != "" {
		permission.Scope = req.Scope
	}
	if req.Status != "" {
		permission.Status = req.Status
	}

	// 保存更新
	if err := s.permissionRepo.Update(ctx, permission); err != nil {
		return nil, userErrors.NewDatabaseError("failed to update permission", err.Error())
	}

	s.logger.Info(ctx, "Permission updated", logiface.Any("permission", permission))
	return permission, nil
}

// DeletePermission 删除权限
func (s *PermissionApplicationService) DeletePermission(ctx context.Context, id int64) error {
	s.logger.Info(ctx, "Deleting permission", logiface.Int64("id", id))
	permission, err := s.permissionRepo.FindByID(ctx, id)
	if err != nil {
		return userErrors.NewDatabaseError("failed to get permission", err.Error())
	}
	if permission == nil {
		return userErrors.NewUserError(userErrors.CodePermissionNotFound, "permission not found")
	}

	// 系统权限不允许删除
	if permission.IsSystem {
		return userErrors.NewUserError(userErrors.CodeSystemError, "system permission cannot be deleted")
	}

	if err := s.permissionRepo.Delete(ctx, id); err != nil {
		return userErrors.NewDatabaseError("failed to delete permission", err.Error())
	}

	s.logger.Info(ctx, "Permission deleted", logiface.Int64("id", id))
	return nil
}

// GetPermissionStats 获取权限统计信息
func (s *PermissionApplicationService) GetPermissionStats(ctx context.Context, tenantID int64) (*dto.PermissionStatsResponse, error) {
	s.logger.Info(ctx, "Getting permission stats", logiface.Int64("tenantID", tenantID))
	// 获取总权限数
	totalPermissions, err := s.permissionRepo.Count(ctx, userRepo.NewQueryParams().WithTenantID(tenantID))
	if err != nil {
		return nil, userErrors.NewDatabaseError("failed to get total permissions count", err.Error())
	}

	// 获取活跃权限数
	activePermissions, err := s.permissionRepo.Count(ctx, userRepo.NewQueryParams().
		WithTenantID(tenantID).
		WithStatus("active"))
	if err != nil {
		return nil, userErrors.NewDatabaseError("failed to get active permissions count", err.Error())
	}

	// 获取系统权限数
	systemPermissions, err := s.permissionRepo.Count(ctx, userRepo.NewQueryParams().
		WithTenantID(tenantID).
		WithIsSystem(true))
	if err != nil {
		return nil, userErrors.NewDatabaseError("failed to get system permissions count", err.Error())
	}

	// 获取关联的资源数量（简化处理）
	resourceCount := int64(0)
	// 移除资源计数逻辑，因为权限不再直接关联资源
	// 可以通过resource_relations表来统计关联的资源

	s.logger.Info(ctx, "Permission stats retrieved", logiface.Any("response", &dto.PermissionStatsResponse{
		TotalPermissions:  totalPermissions,
		ActivePermissions: activePermissions,
		SystemPermissions: systemPermissions,
		ResourceTypeCount: resourceCount,
	}))
	return &dto.PermissionStatsResponse{
		TotalPermissions:  totalPermissions,
		ActivePermissions: activePermissions,
		SystemPermissions: systemPermissions,
		ResourceTypeCount: resourceCount,
	}, nil
}

// toPermissionResponse 转换为权限响应
func (s *PermissionApplicationService) toPermissionResponse(permission *userEntity.Permission) *dto.PermissionResponse {
	response := &dto.PermissionResponse{
		ID:               permission.ID,
		TenantID:         permission.TenantID,
		Name:             permission.Name,
		Code:             permission.Code,
		DisplayName:      permission.DisplayName,
		Description:      permission.Description,
		Action:           permission.Action,
		Scope:            permission.Scope,
		ScopeDisplayName: permission.GetScopeDisplayName(),
		Status:           permission.Status,
		IsSystem:         permission.IsSystem,
		CreatedAt:        permission.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:        permission.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 注意：Resource 关联字段已被移除，如果需要资源信息，需要单独查询
	// 这里暂时不设置 Resource 字段，避免编译错误

	return response
}
