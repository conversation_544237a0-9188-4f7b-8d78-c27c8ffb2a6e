# 权限检查服务优化说明

## 概述

本文档描述了权限检查服务的完整优化方案，包括算法选择、缓存策略、淘汰机制和事件驱动架构，旨在实现极致的内存使用优化和高效的查询性能。

## 核心算法：Bloom Filter + Bitmap

### 1. Bloom Filter（布隆过滤器）
- **存储空间**：极低，每个权限只需要几个bit
- **查询效率**：O(k) 其中k是哈希函数数量（通常3-5个）
- **内存使用**：每个权限约占用 0.1-0.5 bytes
- **用途**：快速判断权限是否存在，避免无效查询

### 2. Bitmap（位图）
- **存储空间**：每个用户每个权限1 bit
- **查询效率**：O(1) 位运算
- **内存使用**：每个用户约占用 (权限总数/8) bytes
- **用途**：精确存储用户权限，支持快速权限检查

## 分层缓存淘汰策略

### 1. L1缓存：LRU + TTL（热数据）
```go
type L1Cache struct {
    // 使用 LRU 算法，固定大小
    lru *lru.Cache
    // TTL 控制，避免过期数据
    ttl time.Duration
}

// LRU 淘汰：最近最少使用的数据被淘汰
func (l1 *L1Cache) Get(key string) (interface{}, bool) {
    if value, exists := l1.lru.Get(key); exists {
        // 检查 TTL
        if item, ok := value.(*CacheItem); ok && !item.IsExpired() {
            return item.Value, true
        }
        // 过期则删除
        l1.lru.Remove(key)
    }
    return nil, false
}
```

### 2. L2缓存：LFU + 内存限制（温数据）
```go
type L2Cache struct {
    // LFU 算法：使用频率最低的被淘汰
    lfu *lfu.Cache
    // 内存使用限制
    maxMemory int64
    currentMemory int64
    mutex sync.RWMutex
}

// LFU 淘汰：使用频率最低的数据被淘汰
func (l2 *L2Cache) Get(key string) (interface{}, bool) {
    if value, exists := l2.lfu.Get(key); exists {
        return value, true
    }
    return nil, false
}

// 内存超限时淘汰
func (l2 *L2Cache) checkMemoryLimit() {
    l2.mutex.Lock()
    defer l2.mutex.Unlock()
    
    if l2.currentMemory > l2.maxMemory {
        // 淘汰最不常用的数据直到内存使用正常
        for l2.currentMemory > l2.maxMemory*8/10 {
            key, value := l2.lfu.RemoveLeastFrequent()
            if key != nil {
                l2.currentMemory -= l2.calculateSize(value)
            }
        }
    }
}
```

### 3. L3缓存：访问时间驱动（冷数据）
```go
type L3Cache struct {
    // 用户权限缓存
    userPermissions sync.Map // userID -> *UserPermissionCache
    
    // 用户最后访问时间（KV存储）
    lastAccessTimes sync.Map // userID -> time.Time
    
    // 清理配置
    config *CacheConfig
    
    // 清理定时器
    cleanupTicker *time.Ticker
    stopChan      chan struct{}
}

type CacheConfig struct {
    InactiveTimeout time.Duration // 无活动超时时间（默认30分钟）
    CleanupInterval time.Duration // 清理检查间隔（默认5分钟）
    MaxCacheSize    int           // 最大缓存条目数
}
```

## 访问时间驱动缓存淘汰

### 核心思想
- 使用KV存储记录用户最后访问时间
- 超过30分钟无活动的用户缓存自动清理
- 实现内存自动回收，无需手动干预

### 实现方案
```go
type AccessTimeDrivenCache struct {
    // 用户权限缓存
    userPermissions sync.Map // userID -> *UserPermissionCache
    
    // 用户最后访问时间（KV存储）
    lastAccessTimes sync.Map // userID -> time.Time
    
    // 清理配置
    config *CacheConfig
    
    // 清理定时器
    cleanupTicker *time.Ticker
    stopChan      chan struct{}
}

// 启动清理协程
func (c *AccessTimeDrivenCache) StartCleanup(ctx context.Context) {
    c.cleanupTicker = time.NewTicker(c.config.CleanupInterval)
    
    go func() {
        for {
            select {
            case <-c.cleanupTicker.C:
                c.cleanupInactiveUsers(ctx)
            case <-c.stopChan:
                c.cleanupTicker.Stop()
                return
            case <-ctx.Done():
                c.cleanupTicker.Stop()
                return
            }
        }
    }()
}

// 清理非活跃用户
func (c *AccessTimeDrivenCache) cleanupInactiveUsers(ctx context.Context) {
    now := time.Now()
    inactiveUsers := make([]int64, 0)
    
    // 扫描非活跃用户
    c.lastAccessTimes.Range(func(key, value interface{}) bool {
        userID := key.(int64)
        lastAccess := value.(time.Time)
        
        if now.Sub(lastAccess) > c.config.InactiveTimeout {
            inactiveUsers = append(inactiveUsers, userID)
        }
        return true
    })
    
    // 批量清理
    for _, userID := range inactiveUsers {
        c.removeUserCache(userID)
    }
    
    if len(inactiveUsers) > 0 {
        c.logger.Info(ctx, "Cleaned up inactive user caches",
            logiface.Int("count", len(inactiveUsers)))
    }
}

// 移除用户缓存
func (c *AccessTimeDrivenCache) removeUserCache(userID int64) {
    c.userPermissions.Delete(userID)
    c.lastAccessTimes.Delete(userID)
}
```

## 事件驱动缓存失效

### 1. 权限变更事件定义
```go
// 权限变更事件
type PermissionChangeEvent struct {
    EventType   PermissionEventType `json:"event_type"`
    UserID      int64               `json:"user_id"`
    TenantID    int64               `json:"tenant_id"`
    RoleIDs     []int64             `json:"role_ids,omitempty"`
    PermissionIDs []int64           `json:"permission_ids,omitempty"`
    Timestamp   time.Time           `json:"timestamp"`
    Source      string              `json:"source"` // 事件来源
}

type PermissionEventType string

const (
    EventUserRoleChanged     PermissionEventType = "user_role_changed"
    EventRolePermissionChanged PermissionEventType = "role_permission_changed"
    EventUserPermissionChanged PermissionEventType = "user_permission_changed"
    EventRoleDeleted         PermissionEventType = "role_deleted"
    EventUserDeleted         PermissionEventType = "user_deleted"
)
```

### 2. 事件发布者（权限变更服务）
```go
type PermissionChangeService struct {
    eventPublisher EventPublisher
    logger         logiface.Logger
}

// 用户角色变更时发布事件
func (s *PermissionChangeService) UpdateUserRoles(ctx context.Context, userID, tenantID int64, roleIDs []int64) error {
    // 1. 执行角色更新
    if err := s.roleRepo.UpdateUserRoles(ctx, userID, tenantID, roleIDs); err != nil {
        return err
    }
    
    // 2. 发布权限变更事件
    event := &PermissionChangeEvent{
        EventType: EventUserRoleChanged,
        UserID:    userID,
        TenantID:  tenantID,
        RoleIDs:   roleIDs,
        Timestamp: time.Now(),
        Source:    "permission_service",
    }
    
    if err := s.eventPublisher.Publish(ctx, "permission.changed", event); err != nil {
        s.logger.Error(ctx, "Failed to publish permission change event", 
            logiface.Error(err),
            logiface.Int64("user_id", userID))
        // 不返回错误，避免影响主流程
    }
    
    return nil
}
```

### 3. 事件订阅者（缓存失效服务）
```go
type CacheInvalidationService struct {
    permissionCache *AccessTimeDrivenCache
    logger          logiface.Logger
}

// 订阅权限变更事件
func (s *CacheInvalidationService) SubscribeToEvents(ctx context.Context) {
    // 订阅用户角色变更事件
    s.eventSubscriber.Subscribe("permission.changed", func(event *PermissionChangeEvent) {
        s.handlePermissionChange(ctx, event)
    })
}

// 处理权限变更事件
func (s *CacheInvalidationService) handlePermissionChange(ctx context.Context, event *PermissionChangeEvent) {
    switch event.EventType {
    case EventUserRoleChanged:
        s.invalidateUserCache(ctx, event.UserID, event.TenantID)
        
    case EventRolePermissionChanged:
        s.invalidateRoleRelatedCaches(ctx, event.RoleIDs, event.TenantID)
        
    case EventUserPermissionChanged:
        s.invalidateUserCache(ctx, event.UserID, event.TenantID)
        
    case EventRoleDeleted:
        s.invalidateRoleRelatedCaches(ctx, event.RoleIDs, event.TenantID)
        
    case EventUserDeleted:
        s.removeUserCache(ctx, event.UserID, event.TenantID)
    }
    
    s.logger.Info(ctx, "Cache invalidated for permission change event",
        logiface.String("event_type", string(event.EventType)),
        logiface.Int64("user_id", event.UserID),
        logiface.Int64("tenant_id", event.TenantID))
}

// 失效用户缓存
func (s *CacheInvalidationService) invalidateUserCache(ctx context.Context, userID, tenantID int64) {
    // 1. 清除权限缓存
    s.permissionCache.userPermissions.Delete(userID)
    
    // 2. 清除访问时间记录
    s.permissionCache.lastAccessTimes.Delete(userID)
    
    // 3. 清除Bloom Filter中的用户权限位
    s.permissionCache.invalidateUserInBloomFilter(userID)
    
    s.logger.Debug(ctx, "User permission cache invalidated",
        logiface.Int64("user_id", userID),
        logiface.Int64("tenant_id", tenantID))
}

// 失效角色相关缓存
func (s *CacheInvalidationService) invalidateRoleRelatedCaches(ctx context.Context, roleIDs []int64, tenantID int64) {
    // 获取所有使用该角色的用户
    userIDs, err := s.getUsersByRoleIDs(ctx, roleIDs, tenantID)
    if err != nil {
        s.logger.Error(ctx, "Failed to get users by role IDs", 
            logiface.Error(err),
            logiface.Int64s("role_ids", roleIDs))
        return
    }
    
    // 批量失效用户缓存
    for _, userID := range userIDs {
        s.invalidateUserCache(ctx, userID, tenantID)
    }
    
    s.logger.Info(ctx, "Role-related caches invalidated",
        logiface.Int64s("role_ids", roleIDs),
        logiface.Int("affected_users", len(userIDs)))
}
```

## 完整架构设计

### 1. 系统组件
```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   Permission API    │    │  Permission Cache   │    │  Event Publisher    │
│                     │    │                     │    │                     │
│ - Check Permission  │◄──►│ - Bloom Filter      │◄──►│ - Publish Events    │
│ - Update Roles      │    │ - Bitmap Storage    │    │ - Event Bus         │
│ - Assign Perms      │    │ - L1/L2/L3 Cache    │    │                     │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
                                    │                           │
                                    ▼                           ▼
                           ┌─────────────────────┐    ┌─────────────────────┐
                           │ Cache Invalidation │    │  Event Subscriber   │
                           │                     │    │                     │
                           │ - Time-based Cleanup│    │ - Listen Events     │
                           │ - Event-driven      │    │ - Cache Invalidation│
                           │ - Batch Processing  │    │                     │
                           └─────────────────────┘    └─────────────────────┘
```

### 2. 数据流
1. **权限检查请求** → Bloom Filter快速过滤 → Bitmap精确检查 → 返回结果
2. **权限变更操作** → 发布事件 → 订阅者接收 → 缓存失效 → 下次请求重新加载
3. **定时清理任务** → 扫描非活跃用户 → 批量清理缓存 → 释放内存

### 3. 性能指标
- **查询延迟**：< 1ms（缓存命中）
- **内存使用**：每个用户 < 1KB
- **缓存命中率**：> 95%
- **失效延迟**：< 100ms（事件驱动）

## 配置参数

### 缓存配置
```yaml
permission_cache:
  # L1缓存配置
  l1:
    max_size: 10000        # 最大条目数
    ttl: 5m               # 生存时间
  
  # L2缓存配置
  l2:
    max_size: 50000       # 最大条目数
    max_memory: 100MB     # 最大内存使用
  
  # L3缓存配置
  l3:
    inactive_timeout: 30m # 非活跃超时
    cleanup_interval: 5m  # 清理间隔
    max_size: 100000      # 最大条目数
```

### 事件配置
```yaml
permission_events:
  # 事件总线配置
  event_bus:
    buffer_size: 1000     # 事件缓冲区大小
    worker_count: 4       # 工作协程数
  
  # 失效配置
  invalidation:
    batch_size: 100       # 批量失效大小
    worker_count: 2       # 失效工作协程数
```

## 部署建议

### 1. 内存配置
- **最小内存**：2GB（支持10万用户）
- **推荐内存**：8GB（支持100万用户）
- **最大内存**：32GB（支持1000万用户）

### 2. 监控指标
- 缓存命中率
- 内存使用量
- 查询延迟
- 失效频率
- 事件处理延迟

### 3. 告警设置
- 内存使用 > 80%
- 缓存命中率 < 90%
- 查询延迟 > 5ms
- 事件积压 > 1000

## 总结

本优化方案通过以下技术手段实现了极致的内存优化和高性能：

1. **Bloom Filter + Bitmap**：最小化存储空间，最大化查询效率
2. **分层缓存策略**：L1/L2/L3缓存，不同淘汰算法，最优性能
3. **访问时间驱动**：自动清理非活跃用户，内存自愈
4. **事件驱动失效**：实时响应权限变更，保证数据一致性
5. **批量处理**：减少系统开销，提高整体吞吐量

该方案特别适合高并发、多租户的权限系统，能够在保证性能的同时，将内存使用控制在最低水平。
