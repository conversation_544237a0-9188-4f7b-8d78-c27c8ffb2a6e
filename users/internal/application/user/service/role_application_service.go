package service

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	userErrors "gitee.com/heiyee/platforms/users/internal/domain/errors"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
)

// RoleApplicationService 角色应用服务
type RoleApplicationService struct {
	*BaseService
	roleRepo       repository.RoleRepository
	permissionRepo repository.PermissionRepository
	userRepo       repository.UserRepository
	entityFactory  *entity.EntityFactory
	logger         logiface.Logger
}

// NewRoleApplicationService 创建角色应用服务
func NewRoleApplicationService(
	roleRepo repository.RoleRepository,
	permissionRepo repository.PermissionRepository,
	userRepo repository.UserRepository,
	entityFactory *entity.EntityFactory,
	logger logiface.Logger,
) *RoleApplicationService {
	return &RoleApplicationService{
		BaseService:    NewBaseService(),
		roleRepo:       roleRepo,
		permissionRepo: permissionRepo,
		userRepo:       userRepo,
		entityFactory:  entityFactory,
		logger:         logger,
	}
}

// CreateRole 创建角色
func (s *RoleApplicationService) CreateRole(ctx context.Context, tenantID int64, req *dto.CreateRoleRequest) (*dto.RoleResponse, error) {
	s.logger.Info(ctx, "Creating role", logiface.Int64("tenantID", tenantID), logiface.Any("req", req))

	// 验证角色名称唯一性（单个租户维度）
	if err := s.ValidateUniqueField(ctx, s.existsByNameAdapter, req.Name, tenantID, "角色名称"); err != nil {
		return nil, err
	}

	// 验证角色编码唯一性（单个租户维度）
	if err := s.ValidateUniqueField(ctx, s.existsByCodeAdapter, req.Code, tenantID, "角色编码"); err != nil {
		return nil, err
	}

	// 创建角色实体
	var role *entity.Role
	var err error

	if req.IsSystem {
		role, err = s.entityFactory.NewRole(ctx, tenantID, req.Name, req.Code, req.DisplayName, req.Description)
	} else {
		role, err = s.entityFactory.NewRole(ctx, tenantID, req.Name, req.Code, req.DisplayName, req.Description)
	}

	if err != nil {
		return nil, userErrors.NewSystemError("create_role_entity", fmt.Sprintf("failed to create role entity: %v", err))
	}

	// 保存角色
	if err := s.roleRepo.Create(ctx, role); err != nil {
		return nil, userErrors.NewDatabaseError("创建角色", fmt.Sprintf("创建角色失败: %v", err))
	}

	// 转换为响应DTO
	return s.toRoleResponse(ctx, role)
}

// GetRole 获取角色详情
func (s *RoleApplicationService) GetRole(ctx context.Context, id int64) (*dto.RoleResponse, error) {
	s.logger.Info(ctx, "Getting role", logiface.Int64("id", id))

	role, err := s.roleRepo.FindByID(ctx, id)
	if err != nil {
		return nil, userErrors.NewDatabaseError("获取角色", fmt.Sprintf("获取角色失败: %v", err))
	}
	if role == nil {
		return nil, userErrors.NewUserError(userErrors.CodeRoleNotFound, fmt.Sprintf("role_id: %d", id))
	}

	return s.toRoleResponse(ctx, role)
}

// UpdateRole 更新角色
func (s *RoleApplicationService) UpdateRole(ctx context.Context, id int64, req *dto.UpdateRoleRequest) (*dto.RoleResponse, error) {
	s.logger.Info(ctx, "Updating role", logiface.Int64("id", id), logiface.Any("req", req))

	role, err := s.roleRepo.FindByID(ctx, id)
	if err != nil {
		return nil, userErrors.NewDatabaseError("获取角色", fmt.Sprintf("获取角色失败: %v", err))
	}
	if role == nil {
		return nil, userErrors.NewUserError(userErrors.CodeRoleNotFound, fmt.Sprintf("role_id: %d", id))
	}

	// 验证角色名称唯一性（排除自身，单个租户维度）
	if err := s.ValidateUniqueFieldExcludeSelf(ctx, s.existsByNameAdapter, req.Name, role.TenantID, "角色名称", role.Name); err != nil {
		return nil, err
	}

	// 验证角色编码唯一性（排除自身，单个租户维度）
	if err := s.ValidateUniqueFieldExcludeSelf(ctx, s.existsByCodeAdapter, req.Code, role.TenantID, "角色编码", role.Code); err != nil {
		return nil, err
	}

	// 更新角色信息
	role.Name = req.Name
	role.Code = req.Code
	role.DisplayName = req.DisplayName
	role.Description = req.Description
	role.Status = req.Status

	// 保存更新
	if err := s.roleRepo.Update(ctx, role); err != nil {
		return nil, userErrors.NewDatabaseError("更新角色", fmt.Sprintf("更新角色失败: %v", err))
	}

	return s.toRoleResponse(ctx, role)
}

// DeleteRole 删除角色
func (s *RoleApplicationService) DeleteRole(ctx context.Context, id int64) error {
	s.logger.Info(ctx, "Deleting role", logiface.Int64("id", id))

	role, err := s.roleRepo.FindByID(ctx, id)
	if err != nil {
		return userErrors.NewDatabaseError("获取角色", fmt.Sprintf("获取角色失败: %v", err))
	}
	if role == nil {
		return userErrors.NewUserError(userErrors.CodeRoleNotFound, fmt.Sprintf("role_id: %d", id))
	}

	// 使用基础服务验证是否为系统角色
	if err := s.ValidateSystemEntity(role, "角色"); err != nil {
		return err
	}

	// 使用基础服务验证没有关联用户
	if err := s.ValidateNoAssociatedUsers(ctx, s.getUsersByRole, id, "角色"); err != nil {
		return err
	}

	return s.roleRepo.Delete(ctx, id)
}

// ListRoles 获取角色列表
func (s *RoleApplicationService) ListRoles(ctx context.Context, tenantID int64, req *dto.ListRoleRequest) ([]dto.RoleResponse, int64, error) {
	offset, limit := s.BuildPaginationParams(req.Page, req.Size)
	filter := repository.RoleQueryFilter{
		Status: nil, // 可根据 req.Status 设置
		Offset: offset,
		Limit:  limit,
	}
	roles, total, err := s.roleRepo.QueryRoles(ctx, filter)
	if err != nil {
		return nil, 0, userErrors.NewDatabaseError("获取角色列表", fmt.Sprintf("获取角色列表失败: %v", err))
	}

	// 批量查所有角色权限
	roleIDs := make([]int64, 0, len(roles))
	for _, r := range roles {
		roleIDs = append(roleIDs, r.ID)
	}
	permMap, err := s.roleRepo.GetPermissionsByRoleIDs(ctx, roleIDs)
	if err != nil {
		return nil, 0, userErrors.NewDatabaseError("批量获取角色权限", fmt.Sprintf("批量获取角色权限失败: %v", err))
	}

	responses := make([]dto.RoleResponse, 0, len(roles))
	for _, role := range roles {
		response, err := s.toRoleResponseWithPerms(ctx, &role, permMap[role.ID])
		if err != nil {
			return nil, 0, userErrors.NewSystemError("转换角色数据", fmt.Sprintf("转换角色数据失败: %v", err))
		}
		responses = append(responses, *response)
	}

	return responses, total, nil
}

// AssignRole 分配角色给用户
func (s *RoleApplicationService) AssignRole(ctx context.Context, roleID int64, req *dto.AssignRoleRequest) error {
	s.logger.Info(ctx, "Assigning role", logiface.Int64("roleID", roleID), logiface.Any("req", req))

	role, err := s.roleRepo.FindByID(ctx, roleID)
	if err != nil {
		return userErrors.NewDatabaseError("获取角色", fmt.Sprintf("获取角色失败: %v", err))
	}
	if role == nil {
		return userErrors.NewUserError(userErrors.CodeRoleNotFound, fmt.Sprintf("role_id: %d", roleID))
	}

	// 分配角色给用户
	for _, userID := range req.UserIDs {
		user, err := s.userRepo.FindByID(ctx, userID)
		if err != nil {
			return userErrors.NewDatabaseError("获取用户", fmt.Sprintf("获取用户失败 %d: %v", userID, err))
		}
		if user == nil {
			return userErrors.NewUserError(userErrors.CodeUserNotFound, fmt.Sprintf("user_id: %d", userID))
		}

		// 注意：由于移除了关联字段，这里需要通过用户角色仓储来处理角色分配
		// 暂时跳过，因为需要实现用户角色关联表
		// TODO: 实现用户角色分配逻辑
	}

	return nil
}

// RemoveRole 移除用户角色
func (s *RoleApplicationService) RemoveRole(ctx context.Context, roleID int64, req *dto.RemoveRoleRequest) error {
	s.logger.Info(ctx, "Removing role", logiface.Int64("roleID", roleID), logiface.Any("req", req))

	role, err := s.roleRepo.FindByID(ctx, roleID)
	if err != nil {
		return userErrors.NewDatabaseError("获取角色", fmt.Sprintf("获取角色失败: %v", err))
	}
	if role == nil {
		return userErrors.NewUserError(userErrors.CodeRoleNotFound, fmt.Sprintf("role_id: %d", roleID))
	}

	// 移除用户的角色
	for _, userID := range req.UserIDs {
		user, err := s.userRepo.FindByID(ctx, userID)
		if err != nil {
			return userErrors.NewDatabaseError("获取用户", fmt.Sprintf("获取用户失败 %d: %v", userID, err))
		}
		if user == nil {
			return userErrors.NewUserError(userErrors.CodeUserNotFound, fmt.Sprintf("user_id: %d", userID))
		}

		// 移除角色
		// TODO: 实现角色移除逻辑
		// 这里需要通过角色权限仓储来移除用户角色关联
		// 暂时跳过，因为需要实现用户角色关联表
	}

	return nil
}

// AssignPermissions 分配权限给角色
func (s *RoleApplicationService) AssignPermissions(ctx context.Context, roleID int64, req *dto.AssignPermissionRequest) error {
	s.logger.Info(ctx, "Assigning permissions", logiface.Int64("roleID", roleID), logiface.Any("req", req))

	role, err := s.roleRepo.FindByID(ctx, roleID)
	if err != nil {
		return userErrors.NewDatabaseError("获取角色", fmt.Sprintf("获取角色失败: %v", err))
	}
	if role == nil {
		return userErrors.NewUserError(userErrors.CodeRoleNotFound, fmt.Sprintf("role_id: %d", roleID))
	}

	// 检查权限是否存在
	for _, permissionID := range req.PermissionIDs {
		permission, err := s.permissionRepo.FindByID(ctx, permissionID)
		if err != nil {
			return userErrors.NewDatabaseError("获取权限", fmt.Sprintf("获取权限失败 %d: %v", permissionID, err))
		}
		if permission == nil {
			return userErrors.NewUserError(userErrors.CodePermissionNotFound, fmt.Sprintf("permission_id: %d", permissionID))
		}
	}

	// 分配权限
	return s.roleRepo.AssignPermissions(ctx, roleID, req.PermissionIDs)
}

// RemovePermissions 移除角色权限
func (s *RoleApplicationService) RemovePermissions(ctx context.Context, roleID int64, req *dto.AssignPermissionRequest) error {
	s.logger.Info(ctx, "Removing permissions", logiface.Int64("roleID", roleID), logiface.Any("req", req))

	role, err := s.roleRepo.FindByID(ctx, roleID)
	if err != nil {
		return userErrors.NewDatabaseError("获取角色", fmt.Sprintf("获取角色失败: %v", err))
	}
	if role == nil {
		return userErrors.NewUserError(userErrors.CodeRoleNotFound, fmt.Sprintf("role_id: %d", roleID))
	}

	// 移除权限
	return s.roleRepo.RemovePermissions(ctx, roleID, req.PermissionIDs)
}

// GetRoleStats 获取角色统计信息
func (s *RoleApplicationService) GetRoleStats(ctx context.Context, tenantID int64) (*dto.RoleStatsResponse, error) {
	total, active, system, _, userCount, permissionCount, err := s.roleRepo.GetRoleStats(ctx)
	if err != nil {
		return nil, userErrors.NewDatabaseError("获取角色统计", fmt.Sprintf("获取角色统计失败: %v", err))
	}
	return &dto.RoleStatsResponse{
		TotalRoles:       total,
		ActiveRoles:      active,
		SystemRoles:      system,
		TotalUsers:       userCount,
		TotalPermissions: permissionCount,
	}, nil
}

// toRoleResponse 转换为角色响应DTO
func (s *RoleApplicationService) toRoleResponse(ctx context.Context, role *entity.Role) (*dto.RoleResponse, error) {
	permissions, err := s.roleRepo.GetPermissionsByRole(ctx, role.ID)
	if err != nil {
		return nil, userErrors.NewDatabaseError("获取权限", fmt.Sprintf("获取权限失败: %v", err))
	}
	return s.toRoleResponseWithPerms(ctx, role, permissions)
}

// toRoleResponseWithPerms 支持传入权限列表，避免单独查
func (s *RoleApplicationService) toRoleResponseWithPerms(ctx context.Context, role *entity.Role, permissions []entity.Permission) (*dto.RoleResponse, error) {
	response := &dto.RoleResponse{
		ID:          role.ID,
		Name:        role.Name,
		DisplayName: role.DisplayName,
		Description: role.Description,
		Status:      role.Status,
		IsSystem:    role.IsSystem,
		CreatedAt:   role.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   role.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	permissionResponses := make([]dto.PermissionResponse, 0, len(permissions))
	for _, perm := range permissions {
		permissionResponses = append(permissionResponses, dto.PermissionResponse{
			ID:               perm.ID,
			TenantID:         perm.TenantID,
			Name:             perm.Name,
			DisplayName:      perm.DisplayName,
			Description:      perm.Description,
			Action:           perm.Action,
			Scope:            perm.Scope,
			ScopeDisplayName: perm.GetScopeDisplayName(),
			Status:           perm.Status,
			IsSystem:         perm.IsSystem,
			CreatedAt:        perm.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:        perm.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}
	response.Permissions = permissionResponses

	return response, nil
}

// getUsersByRole 获取角色下的用户（适配基础服务接口）
func (s *RoleApplicationService) getUsersByRole(ctx context.Context, id int64) ([]interface{}, error) {
	users, err := s.roleRepo.GetUsersByRole(ctx, id)
	if err != nil {
		return nil, err
	}

	// 转换为interface{}切片 - 确保即使没有数据也返回空数组而不是null
	result := make([]interface{}, 0, len(users))
	for _, user := range users {
		result = append(result, user)
	}
	return result, nil
}

// existsByNameAdapter 适配器函数，调整参数顺序以匹配BaseService期望的签名
func (s *RoleApplicationService) existsByNameAdapter(ctx context.Context, tenantID int64, name string) (bool, error) {
	return s.roleRepo.ExistsByName(ctx, name)
}

// existsByCodeAdapter 适配器方法，用于ValidateUniqueField
func (s *RoleApplicationService) existsByCodeAdapter(ctx context.Context, tenantID int64, value string) (bool, error) {
	return s.roleRepo.ExistsByCode(ctx, value)
}

// GetRolePermissions 获取角色权限
func (s *RoleApplicationService) GetRolePermissions(ctx context.Context, roleID int64) ([]dto.PermissionResponse, error) {
	role, err := s.roleRepo.FindByID(ctx, roleID)
	if err != nil {
		return nil, userErrors.NewDatabaseError("获取角色", fmt.Sprintf("获取角色失败: %v", err))
	}
	if role == nil {
		return nil, userErrors.NewUserError(userErrors.CodeRoleNotFound, fmt.Sprintf("role_id: %d", roleID))
	}

	permissions, err := s.roleRepo.GetPermissionsByRole(ctx, roleID)
	if err != nil {
		return nil, userErrors.NewDatabaseError("获取角色权限", fmt.Sprintf("获取角色权限失败: %v", err))
	}

	responses := make([]dto.PermissionResponse, 0, len(permissions))
	for _, permission := range permissions {
		responses = append(responses, dto.PermissionResponse{
			ID:               permission.ID,
			TenantID:         permission.TenantID,
			Name:             permission.Name,
			DisplayName:      permission.DisplayName,
			Description:      permission.Description,
			Action:           permission.Action,
			Scope:            permission.Scope,
			ScopeDisplayName: permission.GetScopeDisplayName(),
			Status:           permission.Status,
			IsSystem:         permission.IsSystem,
			CreatedAt:        permission.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:        permission.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return responses, nil
}

// GetRoleUsers 获取角色用户
func (s *RoleApplicationService) GetRoleUsers(ctx context.Context, roleID int64) ([]dto.UserResponse, error) {
	role, err := s.roleRepo.FindByID(ctx, roleID)
	if err != nil {
		return nil, userErrors.NewDatabaseError("获取角色", fmt.Sprintf("获取角色失败: %v", err))
	}
	if role == nil {
		return nil, userErrors.NewUserError(userErrors.CodeRoleNotFound, fmt.Sprintf("role_id: %d", roleID))
	}

	users, err := s.roleRepo.GetUsersByRole(ctx, roleID)
	if err != nil {
		return nil, userErrors.NewDatabaseError("获取角色用户", fmt.Sprintf("获取角色用户失败: %v", err))
	}

	responses := make([]dto.UserResponse, 0, len(users))
	for _, user := range users {
		responses = append(responses, dto.UserResponse{
			ID:        user.ID,
			TenantID:  user.TenantID,
			Username:  user.Username,
			Email:     user.Email,
			RealName:  user.RealName,
			Phone:     user.Phone,
			Status:    string(user.Status),
			CreatedAt: user.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt: user.UpdatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return responses, nil
}

// RemoveUsers 移除角色用户
func (s *RoleApplicationService) RemoveUsers(ctx context.Context, roleID int64, req *dto.AssignRoleRequest) error {
	role, err := s.roleRepo.FindByID(ctx, roleID)
	if err != nil {
		return userErrors.NewDatabaseError("获取角色", fmt.Sprintf("获取角色失败: %v", err))
	}
	if role == nil {
		return userErrors.NewUserError(userErrors.CodeRoleNotFound, fmt.Sprintf("role_id: %d", roleID))
	}

	// 移除用户的角色
	for _, userID := range req.UserIDs {
		user, err := s.userRepo.FindByID(ctx, userID)
		if err != nil {
			return userErrors.NewDatabaseError("获取用户", fmt.Sprintf("获取用户失败 %d: %v", userID, err))
		}
		if user == nil {
			return userErrors.NewUserError(userErrors.CodeUserNotFound, fmt.Sprintf("user_id: %d", userID))
		}

		// TODO: 实现角色移除逻辑
		// 这里需要通过角色权限仓储来移除用户角色关联
		// 暂时返回成功
	}

	return nil
}
