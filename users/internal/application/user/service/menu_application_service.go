package service

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	userErrors "gitee.com/heiyee/platforms/users/internal/domain/errors"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/repository"
	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
)

// MenuApplicationService 菜单应用服务
type MenuApplicationService struct {
	logger                 logiface.Logger
	resourceRepo           repository.ResourceRepository
	userRepo               repository.UserRepository
	permissionCheckService PermissionCheckService
	entityFactory          *entity.EntityFactory
}

// NewMenuApplicationService 创建菜单应用服务
func NewMenuApplicationService(
	logger logiface.Logger,
	resourceRepo repository.ResourceRepository,
	userRepo repository.UserRepository,
	permissionCheckService PermissionCheckService,
	entityFactory *entity.EntityFactory,
) *MenuApplicationService {
	return &MenuApplicationService{
		logger:                 logger,
		resourceRepo:           resourceRepo,
		userRepo:               userRepo,
		permissionCheckService: permissionCheckService,
		entityFactory:          entityFactory,
	}
}

// GetUserMenuTree 获取用户菜单树
func (s *MenuApplicationService) GetUserMenuTree(ctx context.Context, req *dto.UserMenuTreeRequest) ([]*dto.MenuTreeResponse, error) {
	s.logger.Info(ctx, "Getting user menu tree", logiface.Int64("userID", req.UserID), logiface.Int64("tenantID", req.TenantID))
	// 从 context 获取应用ID
	internalAppID, _ := usercontext.GetInternalAppID(ctx)
	if internalAppID == 0 {
		return nil, fmt.Errorf("internal app ID not found in context")
	}

	// 使用权限检查服务判断用户角色（优化：一次查询判断管理员或超级管理员）
	isAdmin, err := s.permissionCheckService.IsAdminOrSuperAdmin(ctx, req.UserID, internalAppID)
	if err != nil {
		s.logger.Warn(ctx, "Failed to check admin role, treating as regular user",
			logiface.Error(err), logiface.Int64("userID", req.UserID))
		isAdmin = false
	}

	var resources []*entity.Resource
	// 管理员：获取所有页面资源
	pageType := value_object.ResourceTypePage
	var appIDPtr *int64
	if internalAppID > 0 {
		appIDPtr = &internalAppID
	}

	// 确定使用的 tenantID：优先使用请求中的，如果没有则从 context 获取
	var tenantID int64
	if req.TenantID > 0 {
		tenantID = req.TenantID
	} else {
		// 从 context 获取租户ID
		if contextTenantID, exists := usercontext.GetTenantID(ctx); exists && contextTenantID > 0 {
			tenantID = contextTenantID
		} else {
			return nil, fmt.Errorf("tenant ID not found in request or context")
		}
	}

	resourceModels, err := s.resourceRepo.GetResourceTreeByLevels(ctx, 4, &pageType, appIDPtr, &tenantID)
	if err != nil {
		return nil, userErrors.NewDatabaseError("failed to get resource tree", err.Error())
	}

	// 转换为指针切片
	resources = make([]*entity.Resource, len(resourceModels))
	for i := range resourceModels {
		resources[i] = &resourceModels[i]
	}

	// 如果用户没有任何权限，返回空菜单
	if len(resources) == 0 {
		return []*dto.MenuTreeResponse{}, nil
	}

	// 构建菜单树
	menuTree := s.buildMenuTreeWithStrategy(ctx, resources, req.UserID, internalAppID, isAdmin)

	return menuTree, nil
}

// buildMenuTree 构建菜单树
func (s *MenuApplicationService) buildMenuTree(resources []*entity.Resource, userResourceIDs map[int64]bool, permissionMap map[int64][]string) []*dto.MenuTreeResponse {
	// 创建资源映射
	resourceMap := make(map[int64]*entity.Resource)
	for _, resource := range resources {
		resourceMap[resource.ID] = resource
	}

	// 构建树形结构
	var rootMenus []*dto.MenuTreeResponse
	childrenMap := make(map[int64][]*dto.MenuTreeResponse)

	for _, resource := range resources {
		// 只包含用户有权限的资源
		if !userResourceIDs[resource.ID] {
			continue
		}

		menu := &dto.MenuTreeResponse{
			ID:           resource.ID,
			Name:         resource.Name,
			DisplayName:  resource.DisplayName,
			Path:         resource.Path,
			Icon:         resource.Icon,
			SortOrder:    resource.SortOrder,
			ResourceType: string(resource.ResourceType),
			Permissions:  permissionMap[resource.ID],
		}

		// 根节点：parentId为0或nil的节点
		if resource.ParentID == nil || *resource.ParentID == 0 {
			rootMenus = append(rootMenus, menu)
		} else {
			parentID := *resource.ParentID
			if children, exists := childrenMap[parentID]; exists {
				childrenMap[parentID] = append(children, menu)
			} else {
				childrenMap[parentID] = []*dto.MenuTreeResponse{menu}
			}
		}
	}

	// 递归设置子菜单
	s.setChildren(rootMenus, childrenMap)

	return rootMenus
}

// setChildren 递归设置子菜单
func (s *MenuApplicationService) setChildren(menus []*dto.MenuTreeResponse, childrenMap map[int64][]*dto.MenuTreeResponse) {
	for _, menu := range menus {
		if children, exists := childrenMap[menu.ID]; exists {
			menu.Children = children
			s.setChildren(children, childrenMap)
		}
	}
}

// getUserAccessibleResources 获取用户可访问的资源
func (s *MenuApplicationService) getUserAccessibleResources(ctx context.Context, userID int64, internalAppID int64, tenantID int64) ([]*entity.Resource, error) {
	// 获取页面类型的资源
	pageType := value_object.ResourceTypePage
	var appIDPtr *int64
	if internalAppID > 0 {
		appIDPtr = &internalAppID
	}
	resourceModels, err := s.resourceRepo.GetResourceTreeByLevels(ctx, 4, &pageType, appIDPtr, &tenantID)
	if err != nil {
		return nil, userErrors.NewDatabaseError("failed to get resource tree", err.Error())
	}

	// 提取资源ID列表
	resourceIDs := make([]int64, len(resourceModels))
	for i, resource := range resourceModels {
		resourceIDs[i] = resource.ID
	}

	// 使用权限检查服务批量获取权限
	permissionMap, err := s.permissionCheckService.BatchGetUserResourcePermissions(ctx, userID, internalAppID, resourceIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %w", err)
	}

	// 过滤有权限的资源
	var accessibleResources []*entity.Resource
	for _, resource := range resourceModels {
		if permissions, exists := permissionMap[resource.ID]; exists && len(permissions) > 0 {
			accessibleResources = append(accessibleResources, &resource)
		}
	}

	return accessibleResources, nil
}

// buildMenuTreeWithStrategy 根据策略构建菜单树
func (s *MenuApplicationService) buildMenuTreeWithStrategy(ctx context.Context, resources []*entity.Resource, userID int64, internalAppID int64, isAdmin bool) []*dto.MenuTreeResponse {
	// 创建资源映射
	resourceMap := make(map[int64]*entity.Resource)
	for _, resource := range resources {
		resourceMap[resource.ID] = resource
	}

	// 根据策略类型决定权限处理方式
	var permissionMap map[int64][]string
	var err error

	if isAdmin {
		// 管理员：无需查询权限，直接设置所有权限
		permissionMap = nil
	} else {
		// 普通用户：批量查询权限（避免 N+1 查询）
		resourceIDs := make([]int64, len(resources))
		for i, resource := range resources {
			resourceIDs[i] = resource.ID
		}

		// 批量获取权限信息
		permissionMap, err = s.permissionCheckService.BatchGetUserResourcePermissions(ctx, userID, internalAppID, resourceIDs)
		if err != nil {
			s.logger.Error(ctx, "Failed to get user permissions", logiface.Error(err))
			// 权限查询失败时，返回空权限（安全降级）
			permissionMap = make(map[int64][]string)
		}
	}

	// 构建树形结构
	var rootMenus []*dto.MenuTreeResponse
	childrenMap := make(map[int64][]*dto.MenuTreeResponse)

	for _, resource := range resources {
		menu := &dto.MenuTreeResponse{
			ID:           resource.ID,
			Name:         resource.Name,
			DisplayName:  resource.DisplayName,
			Path:         resource.Path,
			Icon:         resource.Icon,
			SortOrder:    resource.SortOrder,
			ResourceType: string(resource.ResourceType),
		}

		// 根据策略设置权限
		if isAdmin {
			// 管理员拥有所有权限
			menu.Permissions = []string{"*"}
		} else {
			// 普通用户根据实际权限设置
			if permissions, exists := permissionMap[resource.ID]; exists {
				menu.Permissions = permissions
			} else {
				menu.Permissions = []string{}
			}
		}

		// 根节点：parentId为0或nil的节点
		if resource.ParentID == nil || *resource.ParentID == 0 {
			rootMenus = append(rootMenus, menu)
		} else {
			parentID := *resource.ParentID
			if children, exists := childrenMap[parentID]; exists {
				childrenMap[parentID] = append(children, menu)
			} else {
				childrenMap[parentID] = []*dto.MenuTreeResponse{menu}
			}
		}
	}

	// 递归设置子菜单
	s.setChildren(rootMenus, childrenMap)

	return rootMenus
}

// GetUserButtonPermissions 获取用户按钮权限
func (s *MenuApplicationService) GetUserButtonPermissions(ctx context.Context, req *dto.UserButtonPermissionsRequest) (*dto.UserButtonPermissionsResponse, error) {
	s.logger.Info(ctx, "Getting user button permissions",
		logiface.Int64("userID", req.UserID),
		logiface.Int64("tenantID", req.TenantID),
		logiface.String("menuPath", req.MenuPath))

	// 根据路径查找资源
	resource, err := s.resourceRepo.GetByPath(ctx, req.MenuPath)
	if err != nil {
		return nil, userErrors.NewDatabaseError("failed to get resource by path", err.Error())
	}
	if resource == nil {
		return &dto.UserButtonPermissionsResponse{
			MenuPath:    req.MenuPath,
			Permissions: []string{},
		}, nil
	}

	// 从 context 获取应用ID
	internalAppID, _ := usercontext.GetInternalAppID(ctx)
	if internalAppID == 0 {
		return nil, fmt.Errorf("internal app ID not found in context")
	}

	// 使用权限检查服务获取用户在该资源下的权限
	permissions, err := s.permissionCheckService.GetUserResourcePermissions(ctx, req.UserID, internalAppID, resource.ID)
	if err != nil {
		return nil, userErrors.NewDatabaseError("failed to get user permissions by resource", err.Error())
	}

	return &dto.UserButtonPermissionsResponse{
		MenuPath:    req.MenuPath,
		Permissions: permissions,
	}, nil
}

// CheckPermission 检查权限
func (s *MenuApplicationService) CheckPermission(ctx context.Context, req *dto.CheckPermissionRequest) (*dto.CheckPermissionResponse, error) {
	s.logger.Info(ctx, "Checking permission",
		logiface.Int64("userID", req.UserID),
		logiface.String("permissionCode", req.PermissionCode))

	// 从 context 获取应用ID
	internalAppID, _ := usercontext.GetInternalAppID(ctx)
	if internalAppID == 0 {
		return nil, fmt.Errorf("internal app ID not found in context")
	}

	// 使用权限检查服务检查权限
	hasPermission, err := s.permissionCheckService.CheckUserPermission(ctx, req.UserID, internalAppID, req.PermissionCode)
	if err != nil {
		return nil, userErrors.NewDatabaseError("failed to check user permission", err.Error())
	}

	return &dto.CheckPermissionResponse{
		HasPermission:  hasPermission,
		PermissionCode: req.PermissionCode,
	}, nil
}

// BatchCheckPermission 批量检查权限
func (s *MenuApplicationService) BatchCheckPermission(ctx context.Context, req *dto.BatchCheckPermissionRequest) (*dto.BatchCheckPermissionResponse, error) {
	s.logger.Info(ctx, "Batch checking permissions",
		logiface.Int64("userID", req.UserID),
		logiface.Int("permissionCount", len(req.Permissions)))

	// 从 context 获取应用ID
	internalAppID, _ := usercontext.GetInternalAppID(ctx)
	if internalAppID == 0 {
		return nil, fmt.Errorf("internal app ID not found in context")
	}

	// 提取权限代码列表
	permissionCodes := make([]string, len(req.Permissions))
	for i, item := range req.Permissions {
		permissionCodes[i] = item.Code
	}

	// 使用权限检查服务批量检查权限
	permissionResults, err := s.permissionCheckService.BatchCheckUserPermissions(ctx, req.UserID, internalAppID, permissionCodes)
	if err != nil {
		return nil, userErrors.NewDatabaseError("failed to batch check user permissions", err.Error())
	}

	// 构建结果映射
	results := make(map[string]bool)
	for _, item := range req.Permissions {
		results[item.Key] = permissionResults[item.Code]
	}

	return &dto.BatchCheckPermissionResponse{
		Results: results,
	}, nil
}
