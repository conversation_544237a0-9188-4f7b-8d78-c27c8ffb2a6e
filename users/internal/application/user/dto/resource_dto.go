package dto

// CreateResourceRequest 创建资源请求
type CreateResourceRequest struct {
	Name          string `json:"name" binding:"required"`
	DisplayName   string `json:"display_name" binding:"required"`
	Description   string `json:"description"`
	ResourceType  string `json:"resource_type" binding:"required"`
	ServiceName   string `json:"service_name" binding:"omitempty,max=100"` // 应用服务名称
	RequestType   string `json:"request_type" binding:"omitempty,max=50"`  // 请求数据类型: json, form, file, text, stream, xml, binary
	ResponseType  string `json:"response_type" binding:"omitempty,max=50"` // 响应数据类型: json, html, xml, stream, file, text, binary
	APIMethod     string `json:"api_method" binding:"omitempty,max=10"`    // HTTP方法: GET, POST, PUT, DELETE, PATCH
	ContentType   string `json:"content_type" binding:"omitempty,max=100"` // Content-Type: application/json, multipart/form-data等
	ParentID      *int64 `json:"parent_id"`
	Path          string `json:"path"`
	Icon          string `json:"icon"`
	SortOrder     int    `json:"sort_order"`
	TenantID      int64  `json:"tenant_id" binding:"required"`        // 租户ID，由客户端传递
	InternalAppID *int64 `json:"internal_app_id" binding:"omitempty"` // 应用内部ID，由客户端传递
	IsSystem      bool   `json:"is_system" binding:"omitempty"`
	IsUniversal   bool   `json:"is_universal"` // 是否为通用资源（所有租户可用）
	IsPublic      bool   `json:"is_public"`    // 是否公开访问
	PublicLevel   string `json:"public_level"` // 公开级别：none, anonymous, authenticated, conditional
	Assignable    bool   `json:"assignable"`   // 是否可分配给用户
}

// UpdateResourceRequest 更新资源请求
type UpdateResourceRequest struct {
	ID           int64  `json:"id" binding:"required"`
	Name         string `json:"name" binding:"omitempty,min=3,max=100"`
	DisplayName  string `json:"display_name" binding:"omitempty,min=3,max=100"`
	Description  string `json:"description" binding:"omitempty,max=255"`
	ResourceType string `json:"resource_type" binding:"omitempty"`
	ServiceName  string `json:"service_name" binding:"omitempty,max=100"` // 应用服务名称
	RequestType  string `json:"request_type" binding:"omitempty,max=50"`  // 请求数据类型: json, form, file, text, stream, xml, binary
	ResponseType string `json:"response_type" binding:"omitempty,max=50"` // 响应数据类型: json, html, xml, stream, file, text, binary
	APIMethod    string `json:"api_method" binding:"omitempty,max=10"`    // HTTP方法: GET, POST, PUT, DELETE, PATCH
	ContentType  string `json:"content_type" binding:"omitempty,max=100"` // Content-Type: application/json, multipart/form-data等
	ParentID     *int64 `json:"parent_id"`
	Path         string `json:"path" binding:"omitempty,max=255"`
	Icon         string `json:"icon" binding:"omitempty,max=100"`
	SortOrder    int    `json:"sort_order"`
	IsPublic     *bool  `json:"is_public"`    // 是否公开访问（指针类型，支持更新为false）
	PublicLevel  string `json:"public_level"` // 公开级别：none, anonymous, authenticated, conditional
	Assignable   *bool  `json:"assignable"`   // 是否可分配给用户（指针类型，支持更新为false）
}

// ListResourcesRequest 资源列表请求
type ListResourcesRequest struct {
	PaginationRequest        // 嵌入通用分页请求
	TenantID          int64  `json:"tenant_id"` // 由服务端从JWT token设置，不允许客户端传递
	Keyword           string `json:"keyword" binding:"omitempty"`
	ResourceType      string `json:"resource_type" binding:"omitempty"`
	ParentID          *int64 `json:"parent_id"`
	IsSystem          *bool  `json:"is_system" binding:"omitempty"`
	IsPublic          *bool  `json:"is_public" binding:"omitempty"`    // 是否公开访问过滤
	PublicLevel       string `json:"public_level" binding:"omitempty"` // 公开级别过滤
	Assignable        *bool  `json:"assignable" binding:"omitempty"`   // 是否可分配过滤
	Path              string `json:"path" binding:"omitempty"`
	Offset            int    `json:"offset" binding:"omitempty"`
	Limit             int    `json:"limit" binding:"omitempty"`
}

// ListResourceTreeRequest 资源树列表请求
type ListResourceTreeRequest struct {
	TenantID         int64  `json:"tenant_id"`         // 由服务端从JWT token设置，不允许客户端传递
	ResourceType     string `json:"resource_type"`     // 资源类型过滤，空表示全部
	IncludeUniversal bool   `json:"include_universal"` // 是否包含通用资源
	MaxDepth         int    `json:"max_depth"`         // 最大查询深度，默认3层
	ParentID         *int64 `json:"parent_id"`         // 父节点ID，用于异步加载子节点
	LazyLoad         bool   `json:"lazy_load"`         // 是否为懒加载请求
	InternalAppID    *int64 `json:"internal_app_id"`   // 应用内部ID，用于过滤应用相关的资源
}

// ApplyDefaults 应用默认值
func (req *ListResourceTreeRequest) ApplyDefaults() {
	if req.MaxDepth <= 0 {
		req.MaxDepth = 3 // 默认最大3层
	}
	if req.MaxDepth > 5 {
		req.MaxDepth = 5 // 限制最大深度不超过5层
	}
}

// BatchAssignAPIResourcesRequest 批量分配API资源给页面资源请求
type BatchAssignAPIResourcesRequest struct {
	PageResourceID int64   `json:"page_resource_id" binding:"required"` // 页面资源ID
	APIResourceIDs []int64 `json:"api_resource_ids" binding:"required"` // API资源ID列表
}

// ResourcePermissionsRequest 资源权限查询请求
type ResourcePermissionsRequest struct {
	ResourceID int64 `json:"resource_id" binding:"required"`
}

// LoadChildrenRequest 加载子节点请求
type LoadChildrenRequest struct {
	ParentID     int64  `json:"parent_id" binding:"required"`
	TenantID     int64  `json:"tenant_id"`     // 由服务端从JWT token设置，不允许客户端传递
	ResourceType string `json:"resource_type"` // 资源类型过滤
	MaxDepth     int    `json:"max_depth"`     // 从当前节点开始的最大深度
}

// ApplyDefaults 应用默认值
func (req *LoadChildrenRequest) ApplyDefaults() {
	if req.MaxDepth <= 0 {
		req.MaxDepth = 2 // 懒加载默认最多2层
	}
	if req.MaxDepth > 3 {
		req.MaxDepth = 3 // 懒加载限制最大深度不超过3层
	}
}

// NewListResourcesRequest 创建新的资源列表请求，设置默认值
func NewListResourcesRequest() *ListResourcesRequest {
	return &ListResourcesRequest{
		PaginationRequest: *NewPaginationRequest(),
	}
}

// ApplyDefaults 应用默认值
func (req *ListResourcesRequest) ApplyDefaults() {
	req.PaginationRequest.ApplyDefaults()
	// 将分页参数同步到Offset和Limit字段以保持兼容性
	req.Offset, req.Limit = req.ToOffsetLimit()
}

// ResourceResponse 资源响应
type ResourceResponse struct {
	ID                  int64                `json:"id"`
	TenantID            int64                `json:"tenant_id"`
	Name                string               `json:"name"`
	DisplayName         string               `json:"display_name"`
	Description         string               `json:"description"`
	ResourceType        string               `json:"resource_type"`
	ServiceName         string               `json:"service_name"`  // 应用服务名称
	RequestType         string               `json:"request_type"`  // 请求数据类型
	ResponseType        string               `json:"response_type"` // 响应数据类型
	APIMethod           string               `json:"api_method"`    // HTTP方法
	ContentType         string               `json:"content_type"`  // Content-Type
	ParentID            *int64               `json:"parent_id"`
	Parent              *ResourceSummary     `json:"parent,omitempty"`
	Children            []*ResourceResponse  `json:"children,omitempty"`
	Path                string               `json:"path"`
	Icon                string               `json:"icon"`
	SortOrder           int                  `json:"sort_order"`
	IsSystem            bool                 `json:"is_system"`
	IsUniversal         bool                 `json:"is_universal"`            // 是否为通用资源
	IsPublic            bool                 `json:"is_public"`               // 是否公开访问
	PublicLevel         string               `json:"public_level"`            // 公开级别
	PublicLevelDisplay  string               `json:"public_level_display"`    // 公开级别显示名称
	Assignable          bool                 `json:"assignable"`              // 是否可分配给用户
	AssignabilityStatus string               `json:"assignability_status"`    // 可分配状态描述
	HasChildren         bool                 `json:"has_children"`            // 是否有子资源
	Depth               int                  `json:"depth"`                   // 树深度
	FullPath            string               `json:"full_path"`               // 完整路径
	Permissions         []*PermissionSummary `json:"permissions,omitempty"`   // 关联权限
	APIResources        []*ResourceSummary   `json:"api_resources,omitempty"` // 关联的API资源（仅page类型）
	CreatedAt           string               `json:"created_at"`
	UpdatedAt           string               `json:"updated_at"`
}

// PermissionSummary 权限摘要信息
type PermissionSummary struct {
	ID               int64  `json:"id"`
	Name             string `json:"name"`
	Code             string `json:"code"`
	DisplayName      string `json:"display_name"`
	Scope            string `json:"scope"`
	ScopeDisplayName string `json:"scope_display_name"`
	Status           string `json:"status"`
}

// ResourceTreeNode 资源树节点
type ResourceTreeNode struct {
	*ResourceResponse
	Level         int                 `json:"level"`          // 层级
	IsLeaf        bool                `json:"is_leaf"`        // 是否叶子节点
	Expanded      bool                `json:"expanded"`       // 是否展开
	LazyLoad      bool                `json:"lazy_load"`      // 是否支持懒加载
	ChildrenCount int                 `json:"children_count"` // 子节点数量
	Children      []*ResourceTreeNode `json:"children,omitempty"`
}

// ListResourcesResponse 资源列表响应
type ListResourcesResponse struct {
	Resources []*ResourceResponse `json:"resources"`
	Total     int64               `json:"total"`
	Page      int                 `json:"page"`
	Size      int                 `json:"size"`
}

// TreeQueryStats 树查询统计信息
type TreeQueryStats struct {
	TotalQueries     int   `json:"total_queries"`      // 总查询次数
	BatchQueriesUsed int   `json:"batch_queries_used"` // 使用的批量查询次数
	NodesProcessed   int   `json:"nodes_processed"`    // 处理的节点数
	QueryTimeMs      int64 `json:"query_time_ms"`      // 查询耗时（毫秒）
}

// LoadChildrenResponse 加载子节点响应
type LoadChildrenResponse struct {
	ParentID      int64               `json:"parent_id"`
	Children      []*ResourceTreeNode `json:"children"`
	Total         int64               `json:"total"`
	HasMoreLevels bool                `json:"has_more_levels"`
	LoadedDepth   int                 `json:"loaded_depth"`
}

// TenantResourceSummary 租户资源摘要
type TenantResourceSummary struct {
	TenantID      int64            `json:"tenant_id"`
	TenantName    string           `json:"tenant_name"`
	ResourceCount int64            `json:"resource_count"`
	ResourceTypes map[string]int64 `json:"resource_types"`
}

// ResourceStatsResponse 资源统计响应
type ResourceStatsResponse struct {
	TotalResources      int64                    `json:"total_resources"`
	SystemResources     int64                    `json:"system_resources"`
	UniversalResources  int64                    `json:"universal_resources"` // 通用资源数量
	ResourceTypeCount   map[string]int64         `json:"resource_type_count"`
	MaxDepth            int                      `json:"max_depth"`
	RootResourcesCount  int64                    `json:"root_resources_count"`
	TenantResourceStats []*TenantResourceSummary `json:"tenant_resource_stats"` // 各租户资源统计
}

// ResourcePermissionsResponse 资源权限响应
type ResourcePermissionsResponse struct {
	ResourceID  int64                `json:"resource_id"`
	Resource    *ResourceSummary     `json:"resource"`
	Permissions []*PermissionSummary `json:"permissions"`
	Total       int64                `json:"total"`
}

// ConfigureResourcePermissionsRequest 配置资源权限请求
type ConfigureResourcePermissionsRequest struct {
	ResourceID  int64                    `json:"resource_id" binding:"required"`
	Permissions []ResourcePermissionItem `json:"permissions" binding:"required,min=1"`
	TenantID    int64                    `json:"tenant_id"` // 由服务端从JWT token设置，不允许客户端传递
}

// ResourcePermissionItem 资源权限项
type ResourcePermissionItem struct {
	Name        string `json:"name" binding:"required"`
	Code        string `json:"code" binding:"required"`
	DisplayName string `json:"display_name" binding:"required"`
	Scope       string `json:"scope" binding:"required,oneof=all self"`
	Description string `json:"description"`
}

// ConfigureResourcePermissionsResponse 配置资源权限响应
type ConfigureResourcePermissionsResponse struct {
	ResourceID         int64                `json:"resource_id"`
	CreatedPermissions []*PermissionSummary `json:"created_permissions"`
	SkippedPermissions []SkippedPermission  `json:"skipped_permissions"`
	SuccessCount       int                  `json:"success_count"`
	SkippedCount       int                  `json:"skipped_count"`
	TotalCount         int                  `json:"total_count"`
}

// AvailableAPIResourcesResponse 可用API资源响应（为页面资源分配API时使用）
type AvailableAPIResourcesResponse struct {
	PageResourceID int64              `json:"page_resource_id"`
	APIResources   []*ResourceSummary `json:"api_resources"`
	AssignedAPIIDs []int64            `json:"assigned_api_ids"` // 已分配的API资源ID
	Total          int64              `json:"total"`
}

// AssignResourcesToAppRequest 将资源分配给应用请求
type AssignResourcesToAppRequest struct {
	ResourceIDs         []int64 `json:"resource_ids" binding:"required,min=1"`
	AppName             string  `json:"app_name" binding:"required"`
	TenantID            int64   `json:"tenant_id" binding:"required,min=1"`              // 租户ID，必填
	TargetInternalAppID int64   `json:"target_internal_app_id" binding:"required,min=1"` // 目标应用的内部ID，必填
}

// AssignResourcesToAppResponse 将资源分配给应用响应
type AssignResourcesToAppResponse struct {
	SuccessCount int `json:"success_count"`
	FailureCount int `json:"failure_count"`
	TotalCount   int `json:"total_count"`
}
