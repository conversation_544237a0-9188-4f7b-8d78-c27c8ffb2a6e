package dto

// CreatePermissionRequest 创建权限请求
type CreatePermissionRequest struct {
	Name        string `json:"name" binding:"required"`
	Code        string `json:"code" binding:"required"`
	DisplayName string `json:"display_name" binding:"required"`
	Action      string `json:"action" binding:"omitempty"` // 操作类型：create, read, update, delete等
	Scope       string `json:"scope" binding:"required,oneof=all self"`
	Description string `json:"description"`
	TenantID    int64  `json:"tenant_id"` // 由服务端从JWT token设置，不允许客户端传递
	IsSystem    bool   `json:"is_system" binding:"omitempty"`
}

// BatchCreatePermissionsRequest 批量创建权限请求
type BatchCreatePermissionsRequest struct {
	Permissions []CreatePermissionItem `json:"permissions" binding:"required,min=1"`
	TenantID    int64                  `json:"tenant_id"` // 由服务端从JWT token设置，不允许客户端传递
	IsSystem    bool                   `json:"is_system" binding:"omitempty"`
}

// CreatePermissionItem 批量创建权限项
type CreatePermissionItem struct {
	Name        string `json:"name" binding:"required"`
	Code        string `json:"code" binding:"required"`
	DisplayName string `json:"display_name" binding:"required"`
	Action      string `json:"action" binding:"omitempty"` // 操作类型：create, read, update, delete等
	Scope       string `json:"scope" binding:"required,oneof=all self"`
	Description string `json:"description"`
}

// BatchCreatePermissionsResponse 批量创建权限响应
type BatchCreatePermissionsResponse struct {
	CreatedPermissions []*PermissionResponse `json:"created_permissions"`
	SkippedPermissions []SkippedPermission   `json:"skipped_permissions"`
	SuccessCount       int                   `json:"success_count"`
	SkippedCount       int                   `json:"skipped_count"`
	TotalCount         int                   `json:"total_count"`
}

// SkippedPermission 跳过的权限信息
type SkippedPermission struct {
	Name        string `json:"name"`
	Code        string `json:"code"`
	DisplayName string `json:"display_name"`
	Scope       string `json:"scope"`
	Reason      string `json:"reason"`
}

// UpdatePermissionRequest 更新权限请求
type UpdatePermissionRequest struct {
	ID          int64  `json:"id" binding:"required"`
	Name        string `json:"name" binding:"omitempty,min=3,max=100"`
	Code        string `json:"code" binding:"omitempty,min=3,max=100"`
	DisplayName string `json:"display_name" binding:"omitempty,min=3,max=100"`
	Description string `json:"description" binding:"omitempty,max=500"`
	Action      string `json:"action" binding:"omitempty"` // 操作类型：create, read, update, delete等
	Scope       string `json:"scope" binding:"omitempty,oneof=all self"`
	Status      string `json:"status" binding:"omitempty,oneof=active inactive"`
}

// ListPermissionsRequest 权限列表请求
type ListPermissionsRequest struct {
	PaginationRequest        // 嵌入通用分页请求
	TenantID          int64  `json:"tenant_id"` // 由服务端从JWT token设置，不允许客户端传递
	Keyword           string `json:"keyword" binding:"omitempty"`
	Status            string `json:"status" binding:"omitempty"`
	Action            string `json:"action" binding:"omitempty"` // 操作类型过滤
	IsSystem          *bool  `json:"is_system" binding:"omitempty"`
	Scope             string `json:"scope" binding:"omitempty,oneof=all self"`
	Offset            int    `json:"offset" binding:"omitempty"`
	Limit             int    `json:"limit" binding:"omitempty"`
}

// NewListPermissionsRequest 创建新的权限列表请求，设置默认值
func NewListPermissionsRequest() *ListPermissionsRequest {
	return &ListPermissionsRequest{
		PaginationRequest: *NewPaginationRequest(),
	}
}

// ApplyDefaults 应用默认值
func (req *ListPermissionsRequest) ApplyDefaults() {
	req.PaginationRequest.ApplyDefaults()
	// 将分页参数同步到Offset和Limit字段以保持兼容性
	req.Offset, req.Limit = req.ToOffsetLimit()
}

// PermissionResponse 权限响应
type PermissionResponse struct {
	ID               int64  `json:"id"`
	TenantID         int64  `json:"tenant_id"`
	Name             string `json:"name"`
	Code             string `json:"code"`
	DisplayName      string `json:"display_name"`
	Description      string `json:"description"`
	Action           string `json:"action"` // 操作类型：create, read, update, delete等
	Scope            string `json:"scope"`
	ScopeDisplayName string `json:"scope_display_name"`
	Status           string `json:"status"`
	IsSystem         bool   `json:"is_system"`
	CreatedAt        string `json:"created_at"`
	UpdatedAt        string `json:"updated_at"`
}

// ResourceSummary 资源摘要信息
type ResourceSummary struct {
	ID           int64  `json:"id"`
	Name         string `json:"name"`
	DisplayName  string `json:"display_name"`
	ResourceType string `json:"resource_type"`
	Path         string `json:"path"`
	Icon         string `json:"icon"`
	IsSystem     bool   `json:"is_system"`
	IsUniversal  bool   `json:"is_universal"`
	IsPublic     bool   `json:"is_public"`    // 是否公开访问
	PublicLevel  string `json:"public_level"` // 公开级别
	Assignable   bool   `json:"assignable"`   // 是否可分配给用户
}

// ListPermissionsResponse 权限列表响应
type ListPermissionsResponse struct {
	Permissions []*PermissionResponse `json:"permissions"`
	Total       int64                 `json:"total"`
	Page        int                   `json:"page"`
	Size        int                   `json:"size"`
}

// PermissionStatsResponse 权限统计响应
type PermissionStatsResponse struct {
	TotalPermissions  int64 `json:"total_permissions"`
	ActivePermissions int64 `json:"active_permissions"`
	SystemPermissions int64 `json:"system_permissions"`
	ResourceTypeCount int64 `json:"resource_type_count"`
}
