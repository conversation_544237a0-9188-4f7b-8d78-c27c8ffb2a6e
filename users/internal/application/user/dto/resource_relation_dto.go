package dto

import "time"

// CreateResourceRelationRequest 创建资源关系请求
type CreateResourceRelationRequest struct {
	TenantID         int64  `json:"tenant_id" binding:"required"`          // 租户ID
	InternalAppID    int64  `json:"internal_app_id" binding:"required"`    // 应用ID
	SourceResourceID int64  `json:"source_resource_id" binding:"required"` // 源资源ID（通常是页面）
	TargetResourceID int64  `json:"target_resource_id" binding:"required"` // 目标资源ID（通常是API）
	PermissionID     *int64 `json:"permission_id"`                         // 权限ID，关联permissions表
	Description      string `json:"description"`                           // 关系描述
	PermissionCode   string `json:"permission_code"`                       // 权限编码
	IsRequired       bool   `json:"is_required"`                           // 是否必需权限
	InheritParent    bool   `json:"inherit_parent"`                        // 是否继承父级权限
	Priority         int    `json:"priority"`                              // 权限优先级
	Status           string `json:"status"`                                // 状态
}

// UpdateResourceRelationRequest 更新资源关系请求
type UpdateResourceRelationRequest struct {
	ID             int64   `json:"id" binding:"required"`     // 关系ID
	PermissionID   *int64  `json:"permission_id,omitempty"`   // 权限ID，关联permissions表
	Description    *string `json:"description,omitempty"`     // 关系描述
	PermissionCode *string `json:"permission_code,omitempty"` // 权限编码
	IsRequired     *bool   `json:"is_required,omitempty"`     // 是否必需权限
	InheritParent  *bool   `json:"inherit_parent,omitempty"`  // 是否继承父级权限
	Priority       *int    `json:"priority,omitempty"`        // 权限优先级
	Status         *string `json:"status,omitempty"`          // 状态
}

// DeleteResourceRelationRequest 删除资源关系请求
type DeleteResourceRelationRequest struct {
	ID int64 `json:"id" binding:"required"` // 关系ID
}

// ToggleResourceRelationStatusRequest 切换资源关系状态请求
type ToggleResourceRelationStatusRequest struct {
	ID     int64  `json:"id" binding:"required"`     // 关系ID
	Status string `json:"status" binding:"required"` // 新状态
}

// GetResourceRelationsRequest 获取资源关系列表请求
type GetResourceRelationsRequest struct {
	TenantID         *int64  `json:"tenant_id,omitempty"`          // 租户ID
	InternalAppID    *int64  `json:"internal_app_id,omitempty"`    // 应用ID
	SourceResourceID *int64  `json:"source_resource_id,omitempty"` // 源资源ID
	TargetResourceID *int64  `json:"target_resource_id,omitempty"` // 目标资源ID
	Status           *string `json:"status,omitempty"`             // 状态
	Page             int     `json:"page"`                         // 页码
	Size             int     `json:"size"`                         // 每页大小
}

// ResourceRelationResponse 资源关系响应
type ResourceRelationResponse struct {
	ID               int64     `json:"id"`                 // 关系ID
	TenantID         int64     `json:"tenant_id"`          // 租户ID
	InternalAppID    int64     `json:"internal_app_id"`    // 应用ID
	SourceResourceID int64     `json:"source_resource_id"` // 源资源ID
	TargetResourceID int64     `json:"target_resource_id"` // 目标资源ID
	PermissionID     *int64    `json:"permission_id"`      // 权限ID，关联permissions表
	Description      string    `json:"description"`        // 关系描述
	PermissionCode   string    `json:"permission_code"`    // 权限编码
	IsRequired       bool      `json:"is_required"`        // 是否必需权限
	InheritParent    bool      `json:"inherit_parent"`     // 是否继承父级权限
	Priority         int       `json:"priority"`           // 权限优先级
	Status           string    `json:"status"`             // 状态
	StatusDisplay    string    `json:"status_display"`     // 状态显示名称
	CreatedAt        time.Time `json:"created_at"`         // 创建时间
	UpdatedAt        time.Time `json:"updated_at"`         // 更新时间

	// 关联资源信息
	SourceResource *ResourceResponse  `json:"source_resource,omitempty"` // 源资源信息
	TargetResource *ResourceResponse  `json:"target_resource,omitempty"` // 目标资源信息
	Permission     *PermissionSummary `json:"permission,omitempty"`      // 权限信息
}

// PagedResourceRelationResponse 分页资源关系响应
type PagedResourceRelationResponse struct {
	List       []*ResourceRelationResponse `json:"list"`        // 关系列表
	Total      int64                       `json:"total"`       // 总数
	Page       int                         `json:"page"`        // 当前页
	Size       int                         `json:"size"`        // 每页大小
	TotalPages int                         `json:"total_pages"` // 总页数
}

// BatchCreateResourceRelationsRequest 批量创建资源关系请求
type BatchCreateResourceRelationsRequest struct {
	TenantID      int64                            `json:"tenant_id" binding:"required"`       // 租户ID
	InternalAppID int64                            `json:"internal_app_id" binding:"required"` // 应用ID
	Relations     []*CreateResourceRelationRequest `json:"relations" binding:"required"`       // 关系列表
}

// BatchCreateResourceRelationsResponse 批量创建资源关系响应
type BatchCreateResourceRelationsResponse struct {
	SuccessCount int                         `json:"success_count"` // 成功数量
	FailureCount int                         `json:"failure_count"` // 失败数量
	Relations    []*ResourceRelationResponse `json:"relations"`     // 创建的关系列表
	Errors       []string                    `json:"errors"`        // 错误信息列表
}

// ResourceRelationStatsResponse 资源关系统计响应
type ResourceRelationStatsResponse struct {
	TotalRelations      int64            `json:"total_relations"`       // 总关系数
	ActiveRelations     int64            `json:"active_relations"`      // 激活关系数
	InactiveRelations   int64            `json:"inactive_relations"`    // 非激活关系数
	RelationsByType     map[string]int64 `json:"relations_by_type"`     // 按类型统计
	RelationsByPriority map[string]int64 `json:"relations_by_priority"` // 按优先级统计
}

// APIPermissionCheckRequest API权限检查请求
type APIPermissionCheckRequest struct {
	UserID         int64  `json:"user_id" binding:"required"`         // 用户ID
	TenantID       int64  `json:"tenant_id" binding:"required"`       // 租户ID
	InternalAppID  int64  `json:"internal_app_id" binding:"required"` // 应用ID
	APIPath        string `json:"api_path" binding:"required"`        // API路径
	APIMethod      string `json:"api_method" binding:"required"`      // API方法
	PageResourceID *int64 `json:"page_resource_id,omitempty"`         // 页面资源ID（可选）
}

// APIPermissionCheckResponse API权限检查响应
type APIPermissionCheckResponse struct {
	HasAccess       bool     `json:"has_access"`                  // 是否有访问权限
	Reason          string   `json:"reason"`                      // 决策原因
	RequiredPerms   []string `json:"required_perms"`              // 需要的权限
	GrantedPerms    []string `json:"granted_perms"`               // 已授予的权限
	MissingPerms    []string `json:"missing_perms"`               // 缺失的权限
	APIResourceID   *int64   `json:"api_resource_id,omitempty"`   // API资源ID
	APIResourceName *string  `json:"api_resource_name,omitempty"` // API资源名称
}

// RefreshPermissionCacheRequest 刷新权限缓存请求
type RefreshPermissionCacheRequest struct {
	TenantID      *int64 `json:"tenant_id,omitempty"`       // 租户ID（可选，不指定则刷新所有）
	InternalAppID *int64 `json:"internal_app_id,omitempty"` // 应用ID（可选）
	UserID        *int64 `json:"user_id,omitempty"`         // 用户ID（可选）
	CacheType     string `json:"cache_type"`                // 缓存类型：all, resources, permissions, relations
}

// PermissionCacheStatsResponse 权限缓存统计响应
type PermissionCacheStatsResponse struct {
	TotalEntries      int     `json:"total_entries"`      // 总缓存条目数
	ResourceEntries   int     `json:"resource_entries"`   // 资源缓存条目数
	PermissionEntries int     `json:"permission_entries"` // 权限缓存条目数
	RelationEntries   int     `json:"relation_entries"`   // 关系缓存条目数
	HitCount          int64   `json:"hit_count"`          // 命中次数
	MissCount         int64   `json:"miss_count"`         // 未命中次数
	HitRate           float64 `json:"hit_rate"`           // 命中率
}
