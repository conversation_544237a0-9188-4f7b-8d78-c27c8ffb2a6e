package grpc

import (
	"context"

	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/api/userpb"
	"gitee.com/heiyee/platforms/users/internal/application/application/dto"
	appService "gitee.com/heiyee/platforms/users/internal/application/application/service"
	userService "gitee.com/heiyee/platforms/users/internal/application/user/service"
	"gitee.com/heiyee/platforms/users/internal/domain/errors"
	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
	"gitee.com/heiyee/platforms/users/pkg/jwt"
)

// 辅助函数：注入用户信息到上下文
func withUserContext(ctx context.Context, userInfo *usercontext.UserInfo) context.Context {
	return usercontext.SetUserInfo(ctx, userInfo)
}

// 辅助函数：注入应用信息到上下文
func withAppContext(ctx context.Context, appInfo *usercontext.AppInfo) context.Context {
	return usercontext.SetAppInfo(ctx, appInfo)
}

// UserServiceServer gRPC用户服务实现
type UserServiceServer struct {
	userAppService   *userService.UserApplicationService
	tenantAppService *userService.TenantApplicationService
	appAppService    *appService.ApplicationApplicationService
	jwtService       *jwt.JWTService
	logger           logiface.Logger
	userpb.UnimplementedUserServiceServer
}

// NewUserServiceServer 创建用户服务服务器
func NewUserServiceServer(userAppService *userService.UserApplicationService, tenantAppService *userService.TenantApplicationService, appAppService *appService.ApplicationApplicationService, jwtService *jwt.JWTService, logger logiface.Logger) *UserServiceServer {
	return &UserServiceServer{
		userAppService:   userAppService,
		tenantAppService: tenantAppService,
		appAppService:    appAppService,
		jwtService:       jwtService,
		logger:           logger,
	}
}

// GetUserInfoByToken 实现
func (s *UserServiceServer) GetUserInfoByToken(ctx context.Context, req *userpb.GetUserInfoByTokenRequest) (*userpb.GetUserInfoByTokenResponse, error) {
	token := req.GetToken()
	appId := req.GetAppId()

	if token == "" {
		s.logger.Warn(ctx, "GetUserInfoByToken: token required")
		return &userpb.GetUserInfoByTokenResponse{Code: 400, Message: "token required"}, nil
	}

	if appId == "" {
		s.logger.Warn(ctx, "GetUserInfoByToken: app_id required")
		return &userpb.GetUserInfoByTokenResponse{Code: 400, Message: "app_id required"}, nil
	}

	// 验证token并获取用户信息
	user, err := s.userAppService.GetUserByToken(ctx, token)
	if err != nil {
		s.logger.Error(ctx, "GetUserInfoByToken: failed to get user by token", logiface.Error(err))
		return &userpb.GetUserInfoByTokenResponse{Code: 500, Message: "internal server error"}, nil
	}
	if user == nil {
		s.logger.Warn(ctx, "GetUserInfoByToken: user not found")
		return &userpb.GetUserInfoByTokenResponse{Code: 404, Message: "user not found"}, nil
	}

	// 检查用户状态
	if user.Status != value_object.UserStatusActive {
		s.logger.Warn(ctx, "GetUserInfoByToken: user not active", logiface.Int64("user_id", user.ID))
		return &userpb.GetUserInfoByTokenResponse{Code: 403, Message: "user not active"}, nil
	}

	// 通过appId查询应用信息
	appReq := &dto.GetApplicationByAppIDRequest{
		AppID: appId,
	}
	app, err := s.appAppService.GetApplicationByAppID(ctx, appReq)
	if err != nil {
		s.logger.Error(ctx, "GetUserInfoByToken: failed to get app info", logiface.Error(err), logiface.String("app_id", appId))
		return &userpb.GetUserInfoByTokenResponse{Code: 500, Message: "failed to get app info"}, nil
	}
	if app == nil {
		s.logger.Warn(ctx, "GetUserInfoByToken: app not found", logiface.String("app_id", appId))
		return &userpb.GetUserInfoByTokenResponse{Code: 404, Message: "app not found"}, nil
	}

	// 验证用户是否属于该应用
	if user.InternalAppID != app.InternalAppID {
		s.logger.Warn(ctx, "GetUserInfoByToken: user does not belong to app", logiface.Int64("user_id", user.ID), logiface.Int64("user_internal_app_id", user.InternalAppID), logiface.Int64("app_internal_app_id", app.InternalAppID))
		return &userpb.GetUserInfoByTokenResponse{Code: 403, Message: "user does not belong to this app"}, nil
	}

	// 构建usercontext.UserInfo
	userContextInfo := &usercontext.UserInfo{
		UserID:        user.ID,
		Username:      user.Username,
		RealName:      user.RealName,
		Email:         user.Email,
		TenantID:      user.TenantID,
		AppId:         appId, // 使用传入的appId
		InternalAppID: user.InternalAppID,
	}

	// 使用辅助函数注入用户信息到上下文
	ctx = withUserContext(ctx, userContextInfo)

	resp := &userpb.GetUserInfoByTokenResponse{
		Code:    0,
		Message: "success",
		Data: &userpb.UserInfo{
			UserId:        user.ID,
			Username:      user.Username,
			RealName:      user.RealName,
			Email:         user.Email,
			TenantId:      user.TenantID,
			InternalAppId: user.InternalAppID, // 返回内部应用ID，bigint类型提升性能
			AppId:         appId,              // 返回外部应用ID
		},
	}
	s.logger.Info(ctx, "GetUserInfoByToken: success", logiface.Int64("user_id", user.ID), logiface.String("app_id", appId), logiface.Int64("InternalAppId", user.InternalAppID))
	return resp, nil
}

// CheckUserPermissions 检查用户权限
func (s *UserServiceServer) CheckUserPermissions(ctx context.Context, req *userpb.CheckUserPermissionsRequest) (*userpb.CheckUserPermissionsResponse, error) {
	// 从上下文获取用户信息
	userInfo, exists := usercontext.GetUserInfo(ctx)
	if !exists || userInfo == nil {
		userErr := errors.NewUserError(errors.CodeUserNotFound, "用户未认证")
		s.logger.Warn(ctx, "CheckUserPermissions: user not authenticated")
		return &userpb.CheckUserPermissionsResponse{
			Code:    int32(userErr.Code),
			Message: userErr.Message,
		}, nil
	}

	codes := req.GetPermissionCodes()
	if len(codes) == 0 {
		userErr := errors.NewParameterValidationFailedError("permission_codes", "权限代码列表不能为空")
		s.logger.Warn(ctx, "CheckUserPermissions: empty permission codes", logiface.Int64("user_id", userInfo.UserID))
		return &userpb.CheckUserPermissionsResponse{
			Code:    int32(userErr.Code),
			Message: userErr.Message,
		}, nil
	}

	// 注意：由于移除了关联字段，这里需要通过其他方式检查用户权限
	// 暂时返回所有权限为 false，避免编译错误
	results := make([]*userpb.PermissionCheckResult, 0, len(codes))
	for _, code := range codes {
		// TODO: 实现权限检查逻辑，可能需要通过用户角色仓储来查询
		results = append(results, &userpb.PermissionCheckResult{PermissionCode: code, HasPermission: false})
	}
	s.logger.Info(ctx, "CheckUserPermissions: returning mock results", logiface.Int("count", len(results)), logiface.Int64("user_id", userInfo.UserID))
	return &userpb.CheckUserPermissionsResponse{
		Code:    0,
		Message: "success",
		Results: results,
	}, nil
}

// GetAppInfo 根据内部应用ID或外部应用ID查询应用基本信息
func (s *UserServiceServer) GetAppInfo(ctx context.Context, req *userpb.GetAppInfoRequest) (*userpb.GetAppInfoResponse, error) {
	internalAppID := req.GetInternalAppId()
	appID := req.GetAppId()

	// 验证参数：必须提供其中一个ID
	if internalAppID == 0 && appID == "" {
		userErr := errors.NewParameterValidationFailedError("internal_app_id or app_id", "必须提供内部应用ID或外部应用ID")
		s.logger.Warn(ctx, "GetAppInfo: missing identifiers")
		return &userpb.GetAppInfoResponse{
			Code:    int32(userErr.Code),
			Message: userErr.Message,
		}, nil
	}

	var app *dto.ApplicationResponse
	var err error

	if internalAppID > 0 {
		// 通过内部应用ID查询
		appReq := &dto.GetApplicationRequest{
			InternalAppID: internalAppID,
		}
		app, err = s.appAppService.GetApplication(ctx, appReq)
	} else {
		// 通过外部应用ID查询
		appReq := &dto.GetApplicationByAppIDRequest{
			AppID: appID,
		}
		app, err = s.appAppService.GetApplicationByAppID(ctx, appReq)
	}

	if err != nil {
		userErr := errors.NewSystemError("get application", "查询应用信息失败")
		s.logger.Error(ctx, "GetAppInfo: failed to get application", logiface.Error(err), logiface.Int64("internal_app_id", internalAppID), logiface.String("app_id", appID))
		return &userpb.GetAppInfoResponse{
			Code:    int32(userErr.Code),
			Message: userErr.Message,
		}, nil
	}

	if app == nil {
		userErr := errors.NewUserError(errors.CodeUserResourceNotFound, "应用不存在")
		s.logger.Warn(ctx, "GetAppInfo: app not found", logiface.Int64("internal_app_id", internalAppID), logiface.String("app_id", appID))
		return &userpb.GetAppInfoResponse{
			Code:    int32(userErr.Code),
			Message: userErr.Message,
		}, nil
	}

	// 构建响应
	s.logger.Info(ctx, "GetAppInfo: success", logiface.Int64("internal_app_id", app.InternalAppID), logiface.String("app_id", app.AppID))
	return &userpb.GetAppInfoResponse{
		Code:    0,
		Message: "success",
		Data: &userpb.AppInfo{
			InternalAppId: app.InternalAppID,
			AppId:         app.AppID,
			AppName:       app.AppName,
			AppType:       string(app.AppType),
			IsSystem:      app.IsSystem,
			Status:        string(app.Status),
			TenantId:      app.TenantID,
		},
	}, nil
}

// GetTenantInfo 根据租户ID查询租户信息
func (s *UserServiceServer) GetTenantInfo(ctx context.Context, req *userpb.GetTenantInfoRequest) (*userpb.GetTenantInfoResponse, error) {
	tenantID := req.GetTenantId()

	// 验证参数
	if tenantID <= 0 {
		userErr := errors.NewParameterValidationFailedError("tenant_id", "租户ID必须大于0")
		s.logger.Warn(ctx, "GetTenantInfo: invalid tenant id", logiface.Int64("tenant_id", tenantID))
		return &userpb.GetTenantInfoResponse{
			Code:    int32(userErr.Code),
			Message: userErr.Message,
		}, nil
	}

	// 查询租户信息
	tenant, err := s.tenantAppService.GetTenant(ctx, tenantID)
	if err != nil {
		userErr := errors.NewSystemError("get tenant", "查询租户信息失败")
		s.logger.Error(ctx, "GetTenantInfo: failed to get tenant", logiface.Error(err), logiface.Int64("tenant_id", tenantID))
		return &userpb.GetTenantInfoResponse{
			Code:    int32(userErr.Code),
			Message: userErr.Message,
		}, nil
	}

	if tenant == nil {
		userErr := errors.NewUserError(errors.CodeUserResourceNotFound, "租户不存在")
		s.logger.Warn(ctx, "GetTenantInfo: tenant not found", logiface.Int64("tenant_id", tenantID))
		return &userpb.GetTenantInfoResponse{
			Code:    int32(userErr.Code),
			Message: userErr.Message,
		}, nil
	}

	// 构建响应
	s.logger.Info(ctx, "GetTenantInfo: success", logiface.Int64("tenant_id", tenant.ID))
	return &userpb.GetTenantInfoResponse{
		Code:    0,
		Message: "success",
		Data: &userpb.TenantInfo{
			TenantId:         tenant.ID,
			TenantCode:       tenant.TenantCode,
			TenantName:       tenant.TenantName,
			SubscriptionPlan: tenant.SubscriptionPlan,
		},
	}, nil
}
