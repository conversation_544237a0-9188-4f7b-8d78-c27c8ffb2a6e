package routes

import (
	"gitee.com/heiyee/platforms/pkg/httpmiddleware"
	"gitee.com/heiyee/platforms/users/internal/interfaces/http/handlers"
	"gitee.com/heiyee/platforms/users/pkg/config"
	"gitee.com/heiyee/platforms/users/pkg/jwt"

	"github.com/gin-gonic/gin"
)

// SetupMenuRoutes 设置菜单权限相关路由
func SetupMenuRoutes(engine *gin.RouterGroup, menuHandler *handlers.MenuHandler, jwtService *jwt.JWTService) {
	// 菜单权限路由组
	menu := engine.Group(config.GlobalAPIPrefix + "/menu")
	menu.Use(httpmiddleware.RequireAuthedMiddleware())
	{
		// 用户菜单相关
		menu.POST("/menu-tree", menuHandler.GetUserMenuTree)        // 获取用户菜单树
		menu.POST("/buttons", menuHandler.GetUserButtonPermissions) // 获取用户按钮权限

		// 权限检查相关
		menu.POST("/check-permission", menuHandler.CheckPermission)            // 检查单个权限
		menu.POST("/batch-check-permission", menuHandler.BatchCheckPermission) // 批量检查权限
	}
}
