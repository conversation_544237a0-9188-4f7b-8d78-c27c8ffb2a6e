package routes

import (
	"gitee.com/heiyee/platforms/pkg/httpmiddleware"
	"gitee.com/heiyee/platforms/users/internal/interfaces/http/handlers"
	"gitee.com/heiyee/platforms/users/pkg/config"
	"gitee.com/heiyee/platforms/users/pkg/jwt"

	"github.com/gin-gonic/gin"
)

// SetupResourceRelationRoutes 设置资源关系相关路由
func SetupResourceRelationRoutes(engine *gin.RouterGroup, resourceRelationHandler *handlers.ResourceRelationHandler, jwtService *jwt.JWTService) {
	relations := engine.Group(config.GlobalAPIPrefix + "/resource-relation")
	relations.Use(httpmiddleware.RequireAuthedMiddleware())
	{
		// 基础CRUD操作
		relations.POST("/create", resourceRelationHandler.CreateResourceRelation)
		relations.POST("/update", resourceRelationHandler.UpdateResourceRelation)
		relations.POST("/delete", resourceRelationHandler.DeleteResourceRelation)
		relations.POST("/list", resourceRelationHandler.GetResourceRelations)
		relations.POST("/get", resourceRelationHandler.GetResourceRelation)

		// 状态管理
		relations.POST("/toggle-status", resourceRelationHandler.ToggleResourceRelationStatus)

		// 批量操作
		relations.POST("/batch-create", resourceRelationHandler.BatchCreateResourceRelations)
		relations.POST("/batch-delete", resourceRelationHandler.BatchDeleteResourceRelations)

		// 查询操作
		relations.POST("/find-by-api", resourceRelationHandler.FindRelationsByAPI)
		relations.POST("/find-by-page", resourceRelationHandler.FindRelationsByPage)
		relations.POST("/find-by-permission", resourceRelationHandler.FindRelationsByPermission)

		// 统计信息
		relations.POST("/stats", resourceRelationHandler.GetResourceRelationStats)

		// 权限检查相关
		relations.POST("/check-api-permission", resourceRelationHandler.CheckAPIPermission)
		relations.POST("/get-user-api-permissions", resourceRelationHandler.GetUserAPIPermissions)

		// 缓存管理
		relations.POST("/cache/refresh", resourceRelationHandler.RefreshPermissionCache)
		relations.POST("/cache/stats", resourceRelationHandler.GetPermissionCacheStats)
	}
}