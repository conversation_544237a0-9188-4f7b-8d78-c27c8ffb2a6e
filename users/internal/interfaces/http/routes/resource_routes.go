package routes

import (
	"gitee.com/heiyee/platforms/pkg/httpmiddleware"
	"gitee.com/heiyee/platforms/users/internal/interfaces/http/handlers"
	"gitee.com/heiyee/platforms/users/pkg/config"
	"gitee.com/heiyee/platforms/users/pkg/jwt"

	"github.com/gin-gonic/gin"
)

// SetupResourceRoutes 设置资源相关路由
func SetupResourceRoutes(engine *gin.RouterGroup, resourceHandler *handlers.ResourceHandler, jwtService *jwt.JWTService) {
	resources := engine.Group(config.GlobalAPIPrefix + "/resource")
	resources.Use(httpmiddleware.RequireAuthedMiddleware())
	{
		// 基础CRUD操作
		resources.POST("/list", resourceHandler.ListResources)
		resources.POST("/create", resourceHandler.CreateResource)
		resources.POST("/get", resourceHandler.GetResource)
		resources.POST("/update", resourceHandler.UpdateResource)
		resources.POST("/delete", resourceHandler.DeleteResource)

		// 树形结构操作
		resources.POST("/tree", resourceHandler.GetResourceTree)
		resources.POST("/tree/children", resourceHandler.LoadChildren)

		// 权限相关操作
		resources.POST("/permissions", resourceHandler.GetResourcePermissions)
		resources.POST("/permissions/configure", resourceHandler.ConfigureResourcePermissions)

		// API资源分配操作（仅适用于page类型资源）
		resources.POST("/api-resources/available", resourceHandler.GetAvailableAPIResources)
		resources.POST("/api-resources/assign", resourceHandler.BatchAssignAPIResources)

		// 资源分配给应用
		resources.POST("/assign-to-app", resourceHandler.AssignResourcesToApp)

		// 统计信息
		resources.POST("/stats", resourceHandler.GetResourceStats)
	}
}
