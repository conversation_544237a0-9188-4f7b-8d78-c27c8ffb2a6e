package routes

import (
	"context"
	"gitee.com/heiyee/platforms/pkg/common"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"time"

	common_response "gitee.com/heiyee/platforms/pkg/common/response"

	"gitee.com/heiyee/platforms/pkg/httpmiddleware"
	"gitee.com/heiyee/platforms/users/internal/application/application/dto"
	"gitee.com/heiyee/platforms/users/internal/application/auth/service"
	userDomainService "gitee.com/heiyee/platforms/users/internal/domain/user/service"
	"gitee.com/heiyee/platforms/users/internal/infrastructure/container"
	"gitee.com/heiyee/platforms/users/internal/interfaces/http/handlers"
	"gitee.com/heiyee/platforms/users/internal/interfaces/web"
	"gitee.com/heiyee/platforms/users/pkg/config"
	"gitee.com/heiyee/platforms/users/pkg/jwt"

	"github.com/gin-gonic/gin"
)

// SetupRoutes 设置所有路由
func SetupRoutes(engine *gin.Engine, container *container.DependencyContainer, jwtService *jwt.JWTService, tenantLookupService *service.TenantLookupService, serviceName string) {
	public := engine.Group("")
	// 设置Web路由
	webHandlers := web.NewHandlers(
		container.GetVerificationService(),
		container.GetUserAppService(),
		container.GetAppConfigService(),
		userDomainService.NewPasswordPolicyValidator(),
		container.GetUserRepo(),
		container.GetTenantRepo(),
		container.GetVerificationTokenRepo(),
		container.GetLogger(),
	)
	// 为密码重置相关路由设置宽松的CSP策略
	passwordResetGroup := public.Group("/password-reset")
	{
		passwordResetGroup.GET("/", webHandlers.PasswordReset.ShowResetFormGin)
		// 新增：重新申请页面
		passwordResetGroup.GET("/request", webHandlers.PasswordReset.ShowRequestPageGin)
		// 接收提交
		passwordResetGroup.POST("/", webHandlers.PasswordReset.ProcessResetGin)
	}

	// 设置静态文件服务
	public.Static("/static", "./static")

	// 基础路由
	public.GET("/health", func(c *gin.Context) {
		common_response.Success(c, gin.H{"status": "ok"})
	})
	auth := engine.Group("")
	// 创建适配器
	userInfoProvider := &UserInfoProviderAdapter{container: container}
	AppInfoProvider := &AppInfoProviderAdapter{container: container}
	httpmiddleware.SetupCommonMiddleware(auth, &httpmiddleware.MiddlewareConfig{
		ServiceName:           serviceName,
		EnableRequestID:       true,
		EnableSecurityHeaders: true,
		EnableRecovery:        true,
		EnableMetrics:         true,
		EnableRequestSize:     true,
		MaxRequestSize:        10 << 20, // 10MB
		EnableAPIVersion:      false,
		EnableTimeout:         true,
		RequestTimeout:        10 * time.Second,
		EnableTraceID:         true,
		Logger:                container.GetLogger(),
		EnableUserInfo:        true,
		UserInfoProvider:      userInfoProvider,
		AppInfoProvider:       AppInfoProvider,
		EnableAccessLog:       true,
	})
	// 设置各模块路由
	SetupAuthRoutes(auth, container.GetAuthHandler(), container.GetRegisterHandler(), container.GetThirdPartyHandler(), jwtService)
	SetupUserRoutes(auth, container.GetUserHandler(), container.GetUserProfileHandler(), jwtService)
	SetupRoleRoutes(auth, container.GetRoleHandler(), jwtService)
	SetupPermissionRoutes(auth, container.GetPermissionHandler(), jwtService)
	SetupResourceRoutes(auth, container.GetResourceHandler(), jwtService)
	SetupDepartmentRoutes(auth, container.GetDepartmentHandler(), jwtService)
	SetupPositionRoutes(auth, container.GetPositionHandler(), jwtService)
	SetupTenantRoutes(auth, container.GetTenantHandler(), jwtService)
	SetupAppConfigRoutes(auth, container.GetAppConfigHandler(), jwtService)
	SetupMenuRoutes(auth, container.GetMenuHandler(), jwtService)
	SetupCaptchaRoutes(auth, container.GetRegisterHandler())
	SetupPasswordRoutes(auth, container.GetPasswordHandler(), jwtService)
	SetupIDGeneratorRoutes(auth, container.GetIDGeneratorHandler())
	SetupFileUploadRoutes(auth, container.GetFileUploadHandler(), jwtService)
	SetupOAuthChannelRoutes(auth, container.GetOAuthChannelHandler())
	SetupVerificationRoutes(auth, container.GetVerificationHandler())
	SetupApplicationRoutes(auth, container.GetApplicationHandler(), jwtService)
	SetupResourceRelationRoutes(auth, container.GetResourceRelationHandler(), jwtService)
}

// SetupPasswordRoutes 设置密码相关路由
func SetupPasswordRoutes(engine *gin.RouterGroup, passwordHandler *handlers.PasswordHandler, jwtService *jwt.JWTService) {
	passwordGroup := engine.Group(config.GlobalAPIPrefix + "/password")
	passwordGroup.Use(httpmiddleware.RequireAuthedMiddleware())
	{
		passwordGroup.POST("/change", passwordHandler.ChangePassword)
		passwordGroup.POST("/reset", passwordHandler.ResetPassword)
		passwordGroup.POST("/history", passwordHandler.GetPasswordHistory)
	}
}

// SetupFileUploadRoutes 设置文件上传相关路由
func SetupFileUploadRoutes(engine *gin.RouterGroup, fileUploadHandler *handlers.FileUploadHandler, jwtService *jwt.JWTService) {
	fileGroup := engine.Group(config.GlobalAPIPrefix + "/file")
	fileGroup.Use(httpmiddleware.RequireAuthedMiddleware())
	{
		RegisterFileUploadRoutes(fileGroup, fileUploadHandler)
	}
}

// UserInfoProviderAdapter 用户信息提供者适配器
type UserInfoProviderAdapter struct {
	container *container.DependencyContainer
}

// GetUserInfo 实现 UserInfoProvider 接口
func (a *UserInfoProviderAdapter) GetUserInfo(ctx context.Context, token string, headers map[string]string) *httpmiddleware.AuthedUser {
	if headers[common.HeaderAppId] == "" {
		a.container.GetLogger().Error(ctx, "app id is empty")
		return nil
	}
	var logger = a.container.GetLogger()
	// 解析 JWT token 获取用户ID和AppID
	claims, err := a.container.GetJWTService().ParseToken(token)
	if err != nil || claims == nil {
		return nil
	}

	// 通过用户ID查询用户信息
	user, err := a.container.GetUserAppService().GetUser(ctx, claims.UserID)
	if err != nil || user == nil {
		logger.Info(ctx, "user is not found", logiface.Error(err), logiface.Int64("user_id", claims.UserID))
		return nil
	}

	// 验证用户状态
	if !user.IsActive() || claims.AppID <= 0 {
		logger.Info(ctx, "user is not active or app id is invalid", logiface.Error(err), logiface.String("username", user.Username),
			logiface.Int64("app_id", claims.AppID),
		)
		return nil
	}

	// 通过 AppID 查询应用信息
	internalAppID := claims.AppID
	tenantId := user.TenantID
	// 通过内部应用ID查询应用信息
	appReq := &dto.GetApplicationByInternalAppIDRequest{
		InternalAppID: internalAppID,
		TenantID:      tenantId,
	}
	app, err := a.container.GetAppService().GetApplicationByInternalAppID(ctx, appReq)
	if err != nil || app == nil {
		logger.Error(ctx, "app is not found", logiface.Error(err), logiface.Int64("app_id", internalAppID), logiface.Int64("tenant_id", tenantId))
		return nil
	}
	if app.AppID != headers[common.HeaderAppId] {
		logger.Error(ctx, "app id is invalid", logiface.String("app_id", app.AppID), logiface.String("header_app_id", headers[common.HeaderAppId]))
		return nil
	}
	// 填充 AuthedUser 的所有字段
	return &httpmiddleware.AuthedUser{
		UserId:        user.ID,
		Username:      user.Username,
		RealName:      user.RealName,
		Email:         user.Email,
		TenantId:      user.TenantID,
		InternalAppID: internalAppID,
		AppId:         app.AppID,
	}
}

// AppInfoProviderAdapter 租户信息提供者适配器
type AppInfoProviderAdapter struct {
	container *container.DependencyContainer
}

// GetAppInfo 实现 AppInfoProvider 接口
func (a *AppInfoProviderAdapter) GetAppInfo(ctx context.Context, appId string) *httpmiddleware.AppInfo {
	// 使用应用服务查找应用信息
	appReq := &dto.GetApplicationByAppIDRequest{
		AppID: appId,
	}
	app, err := a.container.GetAppService().GetApplicationByAppID(ctx, appReq)
	if err != nil || app == nil {
		return nil
	}

	// 使用应用的tenantId查询租户信息
	tenant, err := a.container.GetTenantLookupService().FindTenantByID(ctx, app.TenantID)
	if err != nil || tenant == nil {
		return nil
	}

	return &httpmiddleware.AppInfo{
		TenantId:      app.TenantID,
		AppId:         app.AppID,
		InternalAppId: app.InternalAppID,
	}
}
