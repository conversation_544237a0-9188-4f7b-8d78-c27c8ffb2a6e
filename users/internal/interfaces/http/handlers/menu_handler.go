package handlers

import (
	commonResponse "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	"gitee.com/heiyee/platforms/users/internal/application/user/service"
	"gitee.com/heiyee/platforms/users/pkg/validator"

	"github.com/gin-gonic/gin"
)

// MenuHandler 菜单处理器
type MenuHandler struct {
	logger      logiface.Logger
	menuService *service.MenuApplicationService
}

// NewMenuHandler 创建菜单处理器
func NewMenuHandler(logger logiface.Logger, menuService *service.MenuApplicationService) *MenuHandler {
	return &MenuHandler{
		logger:      logger,
		menuService: menuService,
	}
}

// GetUserMenuTree 获取用户菜单树
func (h *MenuHandler) GetUserMenuTree(c *gin.Context) {
	var req dto.UserMenuTreeRequest

	// 从请求体获取参数
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 从上下文获取租户ID和用户ID
	req.TenantID = getTenantIDFromContext(c)
	req.UserID = getUserIDFromContext(c)

	h.logger.Info(c.Request.Context(), "get user menu tree attempt",
		logiface.Int64("user_id", req.UserID),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("ip", c.ClientIP()),
	)

	menuTree, err := h.menuService.GetUserMenuTree(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "get user menu tree failed",
			logiface.Error(err),
			logiface.Int64("user_id", req.UserID),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "user menu tree retrieved successfully",
		logiface.Int64("user_id", req.UserID),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.Int("menu_count", len(menuTree)),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Success(c, menuTree)
}

// GetUserButtonPermissions 获取用户按钮权限
func (h *MenuHandler) GetUserButtonPermissions(c *gin.Context) {
	var req dto.UserButtonPermissionsRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 从上下文获取租户ID和用户ID
	req.TenantID = getTenantIDFromContext(c)
	req.UserID = getUserIDFromContext(c)

	// 验证必填字段
	v := validator.NewValidator()
	v.Required(req.MenuPath, "menu_path")

	if err := v.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "get user button permissions validation failed",
			logiface.String("menu_path", req.MenuPath),
			logiface.Int64("user_id", req.UserID),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
			logiface.Any("errors", v.Errors().ToResponseDetails()),
		)
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	h.logger.Info(c.Request.Context(), "get user button permissions attempt",
		logiface.String("menu_path", req.MenuPath),
		logiface.Int64("user_id", req.UserID),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("ip", c.ClientIP()),
	)

	permissions, err := h.menuService.GetUserButtonPermissions(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "get user button permissions failed",
			logiface.Error(err),
			logiface.String("menu_path", req.MenuPath),
			logiface.Int64("user_id", req.UserID),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "user button permissions retrieved successfully",
		logiface.String("menu_path", req.MenuPath),
		logiface.Int64("user_id", req.UserID),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.Int("permission_count", len(permissions.Permissions)),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Success(c, permissions)
}

// CheckPermission 检查权限
func (h *MenuHandler) CheckPermission(c *gin.Context) {
	var req dto.CheckPermissionRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 从上下文获取租户ID和用户ID
	req.TenantID = getTenantIDFromContext(c)
	req.UserID = getUserIDFromContext(c)

	// 验证必填字段
	v := validator.NewValidator()
	v.Required(req.PermissionCode, "permission_code")

	if err := v.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "check permission validation failed",
			logiface.String("permission_code", req.PermissionCode),
			logiface.Int64("user_id", req.UserID),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
			logiface.Any("errors", v.Errors().ToResponseDetails()),
		)
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	result, err := h.menuService.CheckPermission(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "check permission failed",
			logiface.Error(err),
			logiface.String("permission_code", req.PermissionCode),
			logiface.Int64("user_id", req.UserID),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "permission checked successfully",
		logiface.String("permission_code", req.PermissionCode),
		logiface.Bool("has_permission", result.HasPermission),
		logiface.Int64("user_id", req.UserID),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Success(c, result)
}

// BatchCheckPermission 批量检查权限
func (h *MenuHandler) BatchCheckPermission(c *gin.Context) {
	var req dto.BatchCheckPermissionRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 从上下文获取租户ID和用户ID
	req.TenantID = getTenantIDFromContext(c)
	req.UserID = getUserIDFromContext(c)

	// 验证必填字段
	v := validator.NewValidator()
	if len(req.Permissions) == 0 {
		v.AddError("permissions", "权限列表不能为空")
	}

	if err := v.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "batch check permission validation failed",
			logiface.Int("permission_count", len(req.Permissions)),
			logiface.Int64("user_id", req.UserID),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
			logiface.Any("errors", v.Errors().ToResponseDetails()),
		)
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	result, err := h.menuService.BatchCheckPermission(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "batch check permission failed",
			logiface.Error(err),
			logiface.Int("permission_count", len(req.Permissions)),
			logiface.Int64("user_id", req.UserID),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "permissions batch checked successfully",
		logiface.Int("permission_count", len(req.Permissions)),
		logiface.Int("result_count", len(result.Results)),
		logiface.Int64("user_id", req.UserID),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Success(c, result)
}
