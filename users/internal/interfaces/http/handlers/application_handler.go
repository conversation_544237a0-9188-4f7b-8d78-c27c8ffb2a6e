package handlers

import (
	common_response "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/usercontext"
	"gitee.com/heiyee/platforms/users/internal/application/application/dto"
	"gitee.com/heiyee/platforms/users/internal/application/application/service"
	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"

	"github.com/gin-gonic/gin"
)

// ApplicationHandler 应用处理器
type ApplicationHandler struct {
	appService *service.ApplicationApplicationService
}

// NewApplicationHandler 创建应用处理器
func NewApplicationHandler(appService *service.ApplicationApplicationService) *ApplicationHandler {
	return &ApplicationHandler{
		appService: appService,
	}
}

// ListApplications 获取应用列表
func (h *ApplicationHandler) ListApplications(c *gin.Context) {
	// 从请求体解析参数
	var req dto.ListApplicationsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, "请求参数格式错误")
		return
	}

	// 获取当前用户租户ID
	userInfo, ok := usercontext.GetUserInfoFromGinContext(c)
	if !ok || userInfo == nil {
		common_response.Error(c, common_response.CodeUnauthenticated, "用户未认证")
		return
	}

	// 如果请求中没有提供租户ID，使用用户上下文的租户ID
	if req.TenantID == 0 {
		req.TenantID = userInfo.TenantID
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	// 调用服务层
	result, err := h.appService.ListApplications(c.Request.Context(), &req)
	if err != nil {
		common_response.InternalError(c, err)
		return
	}

	// 构建分页响应
	common_response.Paginated(c, result.List, result.Page, result.Size, result.Total)
}

// GetApplication 获取应用详情
func (h *ApplicationHandler) GetApplication(c *gin.Context) {
	// 从请求体解析参数
	var req dto.GetApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, "请求参数格式错误")
		return
	}

	// 调用服务层
	result, err := h.appService.GetApplication(c.Request.Context(), &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	common_response.Success(c, result)
}

// GetApplicationByAppID 根据应用ID获取应用详情
func (h *ApplicationHandler) GetApplicationByAppID(c *gin.Context) {
	// 从请求体解析参数
	var req dto.GetApplicationByAppIDRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, "请求参数格式错误")
		return
	}

	// 调用服务层
	result, err := h.appService.GetApplicationByAppID(c.Request.Context(), &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	common_response.Success(c, result)
}

// CreateApplication 创建应用
func (h *ApplicationHandler) CreateApplication(c *gin.Context) {
	// 从请求体解析参数
	var req dto.CreateApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, "请求参数格式错误")
		return
	}

	// 设置租户ID
	userInfo, ok := usercontext.GetUserInfoFromGinContext(c)
	if !ok || userInfo == nil {
		common_response.Error(c, common_response.CodeUnauthenticated, "用户未认证")
		return
	}
	req.TenantID = userInfo.TenantID

	// 设置默认值
	if req.Status == "" {
		req.Status = entity.ApplicationStatusActive
	}
	if req.AppType == "" {
		req.AppType = entity.ApplicationTypeWeb
	}
	if req.RateLimit == 0 {
		req.RateLimit = 1000
	}
	req.IsActive = true

	// 调用服务层
	result, err := h.appService.CreateApplication(c.Request.Context(), &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	common_response.Success(c, result)
}

// UpdateApplication 更新应用
func (h *ApplicationHandler) UpdateApplication(c *gin.Context) {
	// 从请求体解析参数
	var req dto.UpdateApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, "请求参数格式错误")
		return
	}

	// 调用服务层
	result, err := h.appService.UpdateApplication(c.Request.Context(), &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	common_response.Success(c, result)
}

// DeleteApplication 删除应用
func (h *ApplicationHandler) DeleteApplication(c *gin.Context) {
	// 从请求体解析参数
	var req dto.DeleteApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, "请求参数格式错误")
		return
	}

	// 调用服务层
	err := h.appService.DeleteApplication(c.Request.Context(), &req)
	if err != nil {
		HandleUserError(c, err)
		return
	}

	common_response.Success(c, gin.H{"message": "应用删除成功"})
}

// EnableApplication 启用应用
func (h *ApplicationHandler) EnableApplication(c *gin.Context) {
	// 从请求体解析参数
	var req dto.EnableApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, err.Error())
		return
	}

	// 调用服务层
	err := h.appService.EnableApplication(c.Request.Context(), &req)
	if err != nil {
		common_response.InternalError(c, err)
		return
	}

	common_response.Success(c, gin.H{"message": "应用启用成功"})
}

// DisableApplication 禁用应用
func (h *ApplicationHandler) DisableApplication(c *gin.Context) {
	// 从请求体解析参数
	var req dto.DisableApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common_response.BadRequest(c, err.Error())
		return
	}

	// 调用服务层
	err := h.appService.DisableApplication(c.Request.Context(), &req)
	if err != nil {
		common_response.InternalError(c, err)
		return
	}

	common_response.Success(c, gin.H{"message": "应用禁用成功"})
}

// GetApplicationStats 获取应用统计信息
func (h *ApplicationHandler) GetApplicationStats(c *gin.Context) {
	// 获取租户ID
	userInfo, ok := usercontext.GetUserInfoFromGinContext(c)
	if !ok || userInfo == nil {
		common_response.Error(c, common_response.CodeUnauthenticated, "用户未认证")
		return
	}
	tenantID := userInfo.TenantID

	// 调用服务层
	stats, err := h.appService.GetStats(c.Request.Context(), tenantID)
	if err != nil {
		common_response.InternalError(c, err)
		return
	}

	common_response.Success(c, stats)
}
