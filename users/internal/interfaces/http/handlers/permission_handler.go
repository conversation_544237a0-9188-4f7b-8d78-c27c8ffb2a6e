package handlers

import (
	"strconv"

	commonResponse "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	"gitee.com/heiyee/platforms/users/internal/application/user/service"
	"gitee.com/heiyee/platforms/users/pkg/validator"

	"github.com/gin-gonic/gin"
)

// PermissionHandler 权限处理器
type PermissionHandler struct {
	logger            logiface.Logger
	permissionService *service.PermissionApplicationService
}

// NewPermissionHandler 创建权限处理器
func NewPermissionHandler(logger logiface.Logger, permissionService *service.PermissionApplicationService) *PermissionHandler {
	return &PermissionHandler{
		logger:            logger,
		permissionService: permissionService,
	}
}

// CreatePermission 创建权限
func (h *PermissionHandler) CreatePermission(c *gin.Context) {
	var req dto.CreatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 从上下文获取租户ID
	req.TenantID = getTenantIDFromContext(c)

	// 验证必填字段
	v := validator.NewValidator()
	v.Required(req.Name, "name")
	v.Required(req.Code, "code")
	v.Required(req.DisplayName, "display_name")
	// 移除ResourceID验证，因为权限不再直接关联资源
	v.Required(req.Scope, "scope")

	if err := v.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "create permission validation failed",
			logiface.String("name", req.Name),
			logiface.String("code", req.Code),
			logiface.String("action", req.Action),
			logiface.String("scope", req.Scope),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
			logiface.Any("errors", v.Errors().ToResponseDetails()),
		)
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	h.logger.Info(c.Request.Context(), "create permission attempt",
		logiface.String("name", req.Name),
		logiface.String("code", req.Code),
		logiface.String("action", req.Action),
		logiface.String("scope", req.Scope),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("ip", c.ClientIP()),
	)

	permission, err := h.permissionService.CreatePermission(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "create permission failed",
			logiface.Error(err),
			logiface.String("name", req.Name),
			logiface.String("code", req.Code),
			logiface.String("action", req.Action),
			logiface.String("scope", req.Scope),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
		)

		// 检查是否为唯一性约束错误
		if isUniqueConstraintError(err) {
			handleUniqueConstraintError(c, err)
			return
		}

		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "permission created successfully",
		logiface.String("name", req.Name),
		logiface.String("code", req.Code),
		logiface.String("action", req.Action),
		logiface.String("scope", req.Scope),
		logiface.Int64("permission_id", permission.ID),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Created(c, permission)
}

// BatchCreatePermissions 批量创建权限
func (h *PermissionHandler) BatchCreatePermissions(c *gin.Context) {
	var req dto.BatchCreatePermissionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 从上下文获取租户ID
	req.TenantID = getTenantIDFromContext(c)

	// 验证必填字段
	v := validator.NewValidator()
	// 移除ResourceID验证，因为权限不再直接关联资源

	// 验证权限列表
	if len(req.Permissions) == 0 {
		v.Custom("", "permissions", func(string) bool { return false }, "权限列表不能为空")
	}

	if err := v.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "batch create permissions validation failed",
			logiface.Int("permissions_count", len(req.Permissions)),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
			logiface.Any("errors", v.Errors().ToResponseDetails()),
		)
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	h.logger.Info(c.Request.Context(), "batch create permissions attempt",
		logiface.Int("permissions_count", len(req.Permissions)),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("ip", c.ClientIP()),
	)

	result, err := h.permissionService.BatchCreatePermissions(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "batch create permissions failed",
			logiface.Error(err),
			logiface.Int("permissions_count", len(req.Permissions)),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "permissions batch created successfully",
		logiface.Int("success_count", result.SuccessCount),
		logiface.Int("skipped_count", result.SkippedCount),
		logiface.Int("total_count", result.TotalCount),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Created(c, result)
}

// UpdatePermission 更新权限
func (h *PermissionHandler) UpdatePermission(c *gin.Context) {
	var req dto.UpdatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 兼容字符串/数字类型ID
	var id int64
	switch v := any(req.ID).(type) {
	case float64:
		id = int64(v)
	case string:
		parsed, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			h.logger.Warn(c.Request.Context(), "update permission validation failed: invalid ID",
				logiface.Any("permission_id", req.ID),
				logiface.String("ip", c.ClientIP()),
			)
			details := []commonResponse.ValidationError{{Field: "id", Message: "权限ID格式错误"}}
			commonResponse.ValidationErrorResponse(c, details)
			return
		}
		id = parsed
	default:
		h.logger.Warn(c.Request.Context(), "update permission validation failed: invalid ID type",
			logiface.Any("permission_id", req.ID),
			logiface.String("ip", c.ClientIP()),
		)
		details := []commonResponse.ValidationError{{Field: "id", Message: "权限ID格式错误"}}
		commonResponse.ValidationErrorResponse(c, details)
		return
	}

	h.logger.Info(c.Request.Context(), "update permission attempt",
		logiface.Int64("permission_id", id),
		logiface.String("ip", c.ClientIP()),
	)

	permission, err := h.permissionService.UpdatePermission(c.Request.Context(), id, &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "update permission failed",
			logiface.Error(err),
			logiface.Int64("permission_id", id),
			logiface.String("ip", c.ClientIP()),
		)
		if vErr, ok := err.(*commonResponse.FieldValidationError); ok {
			var details []commonResponse.ValidationError
			details = append(details, commonResponse.ValidationError{Field: vErr.Field, Message: vErr.Message})
			commonResponse.ValidationErrorResponse(c, details)
			return
		}
		if isUniqueConstraintError(err) {
			handleUniqueConstraintError(c, err)
			return
		}
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "permission updated successfully",
		logiface.Int64("permission_id", id),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Updated(c, permission)
}

// DeletePermission 删除权限
func (h *PermissionHandler) DeletePermission(c *gin.Context) {
	var req struct {
		ID interface{} `json:"id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	id, err := parseIDFromAny(req.ID)
	if err != nil {
		h.logger.Warn(c.Request.Context(), "delete permission validation failed: invalid ID",
			logiface.Any("permission_id", req.ID),
			logiface.String("ip", c.ClientIP()),
		)
		details := []commonResponse.ValidationError{{Field: "id", Message: "权限ID格式错误"}}
		commonResponse.ValidationErrorResponse(c, details)
		return
	}

	h.logger.Info(c.Request.Context(), "delete permission attempt",
		logiface.Int64("permission_id", id),
		logiface.String("ip", c.ClientIP()),
	)

	err = h.permissionService.DeletePermission(c.Request.Context(), id)
	if err != nil {
		h.logger.Error(c.Request.Context(), "delete permission failed",
			logiface.Error(err),
			logiface.Int64("permission_id", id),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "permission deleted successfully",
		logiface.Int64("permission_id", id),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Deleted(c)
}

// GetPermission 获取权限详情
func (h *PermissionHandler) GetPermission(c *gin.Context) {
	var req struct {
		ID interface{} `json:"id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	id, err := parseIDFromAny(req.ID)
	if err != nil {
		h.logger.Warn(c.Request.Context(), "get permission validation failed: invalid ID",
			logiface.Any("permission_id", req.ID),
			logiface.String("ip", c.ClientIP()),
		)
		details := []commonResponse.ValidationError{{Field: "id", Message: "权限ID格式错误"}}
		commonResponse.ValidationErrorResponse(c, details)
		return
	}

	permission, err := h.permissionService.GetPermission(c.Request.Context(), id)
	if err != nil {
		h.logger.Error(c.Request.Context(), "get permission failed",
			logiface.Error(err),
			logiface.Int64("permission_id", id),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, permission)
}

// ListPermissions 获取权限列表
func (h *PermissionHandler) ListPermissions(c *gin.Context) {
	var req dto.ListPermissionsRequest

	// 从请求体获取参数，符合interface-design原则
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 从上下文获取租户ID（强制覆盖，防止前端传递）
	req.TenantID = getTenantIDFromContext(c)

	// 应用默认值（在struct层面处理）
	req.ApplyDefaults()

	permissions, err := h.permissionService.ListPermissions(c.Request.Context(), &req)
	if err != nil {
		commonResponse.InternalError(c, err)
		return
	}

	// 确保权限数组不为nil，防止JSON序列化为null
	permissionList := permissions.Permissions
	if permissionList == nil {
		permissionList = make([]*dto.PermissionResponse, 0)
	}

	commonResponse.Paginated(c, permissionList, req.Page, req.Size, permissions.Total)
}

// GetPermissionStats 获取权限统计信息
func (h *PermissionHandler) GetPermissionStats(c *gin.Context) {
	// 空的请求体，符合interface-design原则
	var req struct{}
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	tenantID := getTenantIDFromContext(c)

	stats, err := h.permissionService.GetPermissionStats(c.Request.Context(), tenantID)
	if err != nil {
		h.logger.Error(c.Request.Context(), "get permission stats failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "permission stats retrieved successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Success(c, stats)
}
