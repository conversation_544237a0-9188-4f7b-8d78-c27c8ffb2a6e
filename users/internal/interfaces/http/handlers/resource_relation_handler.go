package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	commonResponse "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	"gitee.com/heiyee/platforms/users/internal/application/user/service"
	"gitee.com/heiyee/platforms/users/internal/domain/errors"
	"gitee.com/heiyee/platforms/users/pkg/validator"
)

// ResourceRelationHandler 资源关系处理器
type ResourceRelationHandler struct {
	logger                       logiface.Logger
	resourceRelationService     *service.ResourceRelationApplicationService
}

// NewResourceRelationHandler 创建资源关系处理器
func NewResourceRelationHandler(
	logger logiface.Logger,
	resourceRelationService *service.ResourceRelationApplicationService,
) *ResourceRelationHandler {
	return &ResourceRelationHandler{
		logger:                       logger,
		resourceRelationService:     resourceRelationService,
	}
}

// CreateResourceRelation 创建资源关系
// @Summary 创建资源关系
// @Description 创建页面与API的绑定关系
// @Tags 资源关系管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.CreateResourceRelationRequest true "创建资源关系请求"
// @Success 200 {object} commonResponse.Response{data=dto.ResourceRelationResponse} "创建成功"
// @Failure 400 {object} commonResponse.Response "参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/user/resource-relation/create [post]
func (h *ResourceRelationHandler) CreateResourceRelation(c *gin.Context) {
	var req dto.CreateResourceRelationRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 参数验证
	v := validator.NewValidator()
	v.RequiredInt(req.SourceResourceID, "source_resource_id")
	v.RequiredInt(req.TargetResourceID, "target_resource_id")
	v.RequiredInt(req.TenantID, "tenant_id")
	v.RequiredInt(req.InternalAppID, "internal_app_id")
	
	if req.PermissionCode != "" {
		v.Alphanumeric(req.PermissionCode, "permission_code")
		v.Length(req.PermissionCode, "permission_code", 1, 100)
	}
	
	v.Range(int64(req.Priority), "priority", 0, 100)

	if err := v.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "create resource relation validation failed",
			logiface.Int64("source_resource_id", req.SourceResourceID),
			logiface.Int64("target_resource_id", req.TargetResourceID),
			logiface.String("permission_code", req.PermissionCode),
			logiface.String("ip", c.ClientIP()),
			logiface.Any("errors", v.Errors().ToResponseDetails()))
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	h.logger.Info(c.Request.Context(), "create resource relation attempt",
		logiface.Int64("source_resource_id", req.SourceResourceID),
		logiface.Int64("target_resource_id", req.TargetResourceID),
		logiface.String("permission_code", req.PermissionCode),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("ip", c.ClientIP()))

	relation, err := h.resourceRelationService.CreateResourceRelation(c.Request.Context(), &req)
	if err != nil {
		if userErr, ok := err.(*errors.UserError); ok {
			h.logger.Warn(c.Request.Context(), "create resource relation business error",
				logiface.Int("error_code", userErr.GetCode()),
				logiface.String("error_message", userErr.GetMessage()),
				logiface.Int64("source_resource_id", req.SourceResourceID),
				logiface.Int64("target_resource_id", req.TargetResourceID),
				logiface.String("ip", c.ClientIP()))
			commonResponse.BusinessError(c, userErr.GetCode(), userErr.GetMessage())
			return
		}

		h.logger.Error(c.Request.Context(), "create resource relation failed",
			logiface.Error(err),
			logiface.Int64("source_resource_id", req.SourceResourceID),
			logiface.Int64("target_resource_id", req.TargetResourceID),
			logiface.String("ip", c.ClientIP()))
		commonResponse.InternalError(c, nil)
		return
	}

	h.logger.Info(c.Request.Context(), "create resource relation success",
		logiface.Int64("relation_id", relation.ID),
		logiface.Int64("source_resource_id", req.SourceResourceID),
		logiface.Int64("target_resource_id", req.TargetResourceID),
		logiface.String("ip", c.ClientIP()))

	commonResponse.Success(c, relation)
}

// UpdateResourceRelation 更新资源关系
// @Summary 更新资源关系
// @Description 更新资源关系的权限配置
// @Tags 资源关系管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.UpdateResourceRelationRequest true "更新资源关系请求"
// @Success 200 {object} commonResponse.Response{data=dto.ResourceRelationResponse} "更新成功"
// @Failure 400 {object} commonResponse.Response "参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/user/resource-relation/update [post]
func (h *ResourceRelationHandler) UpdateResourceRelation(c *gin.Context) {
	var req dto.UpdateResourceRelationRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 参数验证
	v := validator.NewValidator()
	v.RequiredInt(req.ID, "id")
	
	if req.PermissionCode != nil && *req.PermissionCode != "" {
		v.Alphanumeric(*req.PermissionCode, "permission_code")
		v.Length(*req.PermissionCode, "permission_code", 1, 100)
	}
	
	if req.Priority != nil {
		v.Range(int64(*req.Priority), "priority", 0, 100)
	}

	if err := v.Validate(); err != nil {
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	relation, err := h.resourceRelationService.UpdateResourceRelation(c.Request.Context(), &req)
	if err != nil {
		if userErr, ok := err.(*errors.UserError); ok {
			commonResponse.BusinessError(c, userErr.GetCode(), userErr.GetMessage())
			return
		}
		commonResponse.InternalError(c, nil)
		return
	}

	commonResponse.Success(c, relation)
}

// GetResourceRelations 获取资源关系列表
// @Summary 获取资源关系列表
// @Description 获取指定条件的资源关系列表
// @Tags 资源关系管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param tenant_id query int64 false "租户ID"
// @Param internal_app_id query int64 false "应用ID"
// @Param source_resource_id query int64 false "源资源ID"
// @Param target_resource_id query int64 false "目标资源ID"
// @Param status query string false "状态"
// @Param page query int false "页码"
// @Param size query int false "每页大小"
// @Success 200 {object} commonResponse.Response{data=dto.PagedResourceRelationResponse} "查询成功"
// @Failure 400 {object} commonResponse.Response "参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/user/resource-relation/list [get]
func (h *ResourceRelationHandler) GetResourceRelations(c *gin.Context) {
	// 解析查询参数
	req := &dto.GetResourceRelationsRequest{
		Page: 1,
		Size: 20,
	}

	if tenantIDStr := c.Query("tenant_id"); tenantIDStr != "" {
		if tenantID, err := strconv.ParseInt(tenantIDStr, 10, 64); err == nil {
			req.TenantID = &tenantID
		}
	}

	if appIDStr := c.Query("internal_app_id"); appIDStr != "" {
		if appID, err := strconv.ParseInt(appIDStr, 10, 64); err == nil {
			req.InternalAppID = &appID
		}
	}

	if sourceIDStr := c.Query("source_resource_id"); sourceIDStr != "" {
		if sourceID, err := strconv.ParseInt(sourceIDStr, 10, 64); err == nil {
			req.SourceResourceID = &sourceID
		}
	}

	if targetIDStr := c.Query("target_resource_id"); targetIDStr != "" {
		if targetID, err := strconv.ParseInt(targetIDStr, 10, 64); err == nil {
			req.TargetResourceID = &targetID
		}
	}

	if status := c.Query("status"); status != "" {
		req.Status = &status
	}

	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		}
	}

	if sizeStr := c.Query("size"); sizeStr != "" {
		if size, err := strconv.Atoi(sizeStr); err == nil && size > 0 && size <= 100 {
			req.Size = size
		}
	}

	result, err := h.resourceRelationService.GetResourceRelations(c.Request.Context(), req)
	if err != nil {
		if userErr, ok := err.(*errors.UserError); ok {
			commonResponse.BusinessError(c, userErr.GetCode(), userErr.GetMessage())
			return
		}
		commonResponse.InternalError(c, nil)
		return
	}

	commonResponse.Success(c, result)
}

// DeleteResourceRelation 删除资源关系
// @Summary 删除资源关系
// @Description 删除指定的资源关系
// @Tags 资源关系管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.DeleteResourceRelationRequest true "删除资源关系请求"
// @Success 200 {object} commonResponse.Response "删除成功"
// @Failure 400 {object} commonResponse.Response "参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/user/resource-relation/delete [post]
func (h *ResourceRelationHandler) DeleteResourceRelation(c *gin.Context) {
	var req dto.DeleteResourceRelationRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 参数验证
	v := validator.NewValidator()
	v.RequiredInt(req.ID, "id")

	if err := v.Validate(); err != nil {
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	err := h.resourceRelationService.DeleteResourceRelation(c.Request.Context(), &req)
	if err != nil {
		if userErr, ok := err.(*errors.UserError); ok {
			commonResponse.BusinessError(c, userErr.GetCode(), userErr.GetMessage())
			return
		}
		commonResponse.InternalError(c, nil)
		return
	}

	commonResponse.Success(c, nil)
}

// ToggleResourceRelationStatus 切换资源关系状态
// @Summary 切换资源关系状态
// @Description 启用或禁用资源关系
// @Tags 资源关系管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.ToggleResourceRelationStatusRequest true "切换状态请求"
// @Success 200 {object} commonResponse.Response{data=dto.ResourceRelationResponse} "操作成功"
// @Failure 400 {object} commonResponse.Response "参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/user/resource-relation/toggle-status [post]
func (h *ResourceRelationHandler) ToggleResourceRelationStatus(c *gin.Context) {
	var req dto.ToggleResourceRelationStatusRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 参数验证
	v := validator.NewValidator()
	v.RequiredInt(req.ID, "id")
	v.Required(req.Status, "status")
	v.In(req.Status, "status", "active", "inactive")

	if err := v.Validate(); err != nil {
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	relation, err := h.resourceRelationService.ToggleResourceRelationStatus(c.Request.Context(), &req)
	if err != nil {
		if userErr, ok := err.(*errors.UserError); ok {
			commonResponse.BusinessError(c, userErr.GetCode(), userErr.GetMessage())
			return
		}
		commonResponse.InternalError(c, nil)
		return
	}

	commonResponse.Success(c, relation)
}
// GetResourceRelation 获取单个资源关系
func (h *ResourceRelationHandler) GetResourceRelation(c *gin.Context) {
	commonResponse.InternalError(c, nil)
}

// BatchCreateResourceRelations 批量创建资源关系
func (h *ResourceRelationHandler) BatchCreateResourceRelations(c *gin.Context) {
	commonResponse.InternalError(c, nil)
}

// BatchDeleteResourceRelations 批量删除资源关系
func (h *ResourceRelationHandler) BatchDeleteResourceRelations(c *gin.Context) {
	commonResponse.InternalError(c, nil)
}

// FindRelationsByAPI 根据API查找关系
func (h *ResourceRelationHandler) FindRelationsByAPI(c *gin.Context) {
	commonResponse.InternalError(c, nil)
}

// FindRelationsByPage 根据页面查找关系
func (h *ResourceRelationHandler) FindRelationsByPage(c *gin.Context) {
	commonResponse.InternalError(c, nil)
}

// FindRelationsByPermission 根据权限查找关系
func (h *ResourceRelationHandler) FindRelationsByPermission(c *gin.Context) {
	commonResponse.InternalError(c, nil)
}

// GetResourceRelationStats 获取资源关系统计
func (h *ResourceRelationHandler) GetResourceRelationStats(c *gin.Context) {
	commonResponse.InternalError(c, nil)
}

// CheckAPIPermission 检查API权限
func (h *ResourceRelationHandler) CheckAPIPermission(c *gin.Context) {
	commonResponse.InternalError(c, nil)
}

// GetUserAPIPermissions 获取用户API权限
func (h *ResourceRelationHandler) GetUserAPIPermissions(c *gin.Context) {
	commonResponse.InternalError(c, nil)
}

// RefreshPermissionCache 刷新权限缓存
func (h *ResourceRelationHandler) RefreshPermissionCache(c *gin.Context) {
	commonResponse.InternalError(c, nil)
}

// GetPermissionCacheStats 获取权限缓存统计
func (h *ResourceRelationHandler) GetPermissionCacheStats(c *gin.Context) {
	commonResponse.InternalError(c, nil)
}
