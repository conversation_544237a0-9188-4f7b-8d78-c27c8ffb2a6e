package handlers

import (
	commonResponse "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/users/internal/application/user/dto"
	"gitee.com/heiyee/platforms/users/internal/application/user/service"
	"gitee.com/heiyee/platforms/users/internal/domain/errors"
	"gitee.com/heiyee/platforms/users/pkg/validator"

	"github.com/gin-gonic/gin"
)

// ResourceHandler 资源处理器
type ResourceHandler struct {
	logger          logiface.Logger
	resourceService *service.ResourceApplicationService
}

// NewResourceHandler 创建资源处理器
func NewResourceHandler(logger logiface.Logger, resourceService *service.ResourceApplicationService) *ResourceHandler {
	return &ResourceHandler{
		logger:          logger,
		resourceService: resourceService,
	}
}

// CreateResource 创建资源
// @Summary 创建资源
// @Description 创建新的系统资源
// @Tags 资源管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.CreateResourceRequest true "创建资源请求"
// @Success 200 {object} commonResponse.Response{data=dto.ResourceResponse} "创建成功"
// @Failure 400 {object} commonResponse.Response "参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/user/resource/create [post]
func (h *ResourceHandler) CreateResource(c *gin.Context) {
	var req dto.CreateResourceRequest

	// 从请求体获取参数，符合interface-design原则
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 自定义参数验证
	v := validator.NewValidator()
	v.Required(req.Name, "name").ResourceName(req.Name, "name")
	v.Required(req.DisplayName, "display_name").MinLength(req.DisplayName, "display_name", 2).MaxLength(req.DisplayName, "display_name", 100)
	v.Required(req.ResourceType, "resource_type").ResourceType(req.ResourceType, "resource_type")
	if req.Path != "" {
		v.Path(req.Path, "path")
	}
	if req.Description != "" {
		v.MaxLength(req.Description, "description", 255)
	}
	if err := v.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "create resource validation failed",
			logiface.String("name", req.Name),
			logiface.String("display_name", req.DisplayName),
			logiface.String("resource_type", req.ResourceType),
			logiface.String("path", req.Path),
			logiface.String("ip", c.ClientIP()),
			logiface.Any("errors", v.Errors().ToResponseDetails()),
		)
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	h.logger.Info(c.Request.Context(), "create resource attempt",
		logiface.String("name", req.Name),
		logiface.String("display_name", req.DisplayName),
		logiface.String("resource_type", req.ResourceType),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("ip", c.ClientIP()),
	)

	resource, err := h.resourceService.CreateResource(c.Request.Context(), &req)
	if err != nil {
		// 检查是否为业务错误
		if userErr, ok := err.(*errors.UserError); ok {
			h.logger.Warn(c.Request.Context(), "create resource business error",
				logiface.Int("error_code", userErr.GetCode()),
				logiface.String("error_message", userErr.GetMessage()),
				logiface.String("name", req.Name),
				logiface.String("resource_type", req.ResourceType),
				logiface.Int64("tenant_id", req.TenantID),
				logiface.String("ip", c.ClientIP()),
			)
			commonResponse.BusinessError(c, userErr.GetCode(), userErr.GetMessage())
			return
		}

		// 系统错误使用Error级别
		h.logger.Error(c.Request.Context(), "create resource failed",
			logiface.Error(err),
			logiface.String("name", req.Name),
			logiface.String("resource_type", req.ResourceType),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "create resource successful",
		logiface.Int64("resource_id", resource.ID),
		logiface.String("name", req.Name),
		logiface.String("resource_type", req.ResourceType),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Success(c, resource)
}

// GetResource 获取资源详情
// @Summary 获取资源详情
// @Description 根据资源ID获取资源详细信息
// @Tags 资源管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.GetResourceRequest true "获取资源请求"
// @Success 200 {object} commonResponse.Response{data=dto.ResourceResponse} "获取成功"
// @Failure 400 {object} commonResponse.Response "参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 404 {object} commonResponse.Response "资源不存在"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/user/resource/get [post]
func (h *ResourceHandler) GetResource(c *gin.Context) {
	var req struct {
		ID interface{} `json:"id" binding:"required"`
	}

	// 从请求体获取参数，符合interface-design原则
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 参数验证
	id, err := parseIDFromAny(req.ID)
	if err != nil {
		commonResponse.BadRequest(c, err.Error())
		return
	}

	h.logger.Info(c.Request.Context(), "get resource attempt",
		logiface.Int64("resource_id", id),
		logiface.String("ip", c.ClientIP()),
	)

	resource, err := h.resourceService.GetResource(c.Request.Context(), id)
	if err != nil {
		// 检查是否为资源不存在错误
		if userErr, ok := err.(*errors.UserError); ok && userErr.GetCode() == errors.CodeUserResourceNotFound {
			h.logger.Warn(c.Request.Context(), "resource not found",
				logiface.Int64("resource_id", id),
				logiface.String("ip", c.ClientIP()),
			)
			HandleUserError(c, err)
			return
		}

		// 系统错误使用Error级别
		h.logger.Error(c.Request.Context(), "get resource failed",
			logiface.Error(err),
			logiface.Int64("resource_id", id),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, resource)
}

// ListResources 获取资源列表
func (h *ResourceHandler) ListResources(c *gin.Context) {
	var req dto.ListResourcesRequest

	// 从请求体获取参数，符合interface-design原则
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 从上下文获取租户ID（强制覆盖，防止前端传递）
	req.TenantID = getTenantIDFromContext(c)

	// 应用默认值
	req.ApplyDefaults()

	resources, err := h.resourceService.ListResources(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "list resources failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	// 确保资源数组不为nil，防止JSON序列化为null
	resourceList := resources.Resources
	if resourceList == nil {
		resourceList = make([]*dto.ResourceResponse, 0)
	}

	commonResponse.Paginated(c, resourceList, req.Page, req.Size, resources.Total)
}

// GetResourceTree 获取资源树
func (h *ResourceHandler) GetResourceTree(c *gin.Context) {
	var req dto.ListResourceTreeRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 从上下文获取租户ID
	if req.TenantID == 0 {
		req.TenantID = getTenantIDFromContext(c)
	}

	h.logger.Info(c.Request.Context(), "get resource tree attempt",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("resource_type", req.ResourceType),
		logiface.Bool("include_universal", req.IncludeUniversal),
		logiface.Any("internal_app_id", req.InternalAppID),
		logiface.String("ip", c.ClientIP()),
	)

	tree, err := h.resourceService.GetResourceTree(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "get resource tree failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("resource_type", req.ResourceType),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "resource tree retrieved successfully",
		logiface.Int64("tenant_id", req.TenantID),
		logiface.Int("total", len(tree)),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Success(c, tree)
}

// LoadChildren 加载子节点（懒加载）
func (h *ResourceHandler) LoadChildren(c *gin.Context) {
	var req dto.LoadChildrenRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 从上下文获取租户ID
	if req.TenantID == 0 {
		req.TenantID = getTenantIDFromContext(c)
	}

	h.logger.Info(c.Request.Context(), "load children attempt",
		logiface.Int64("parent_id", req.ParentID),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("resource_type", req.ResourceType),
		logiface.Int("max_depth", req.MaxDepth),
		logiface.String("ip", c.ClientIP()),
	)

	children, err := h.resourceService.LoadChildren(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "load children failed",
			logiface.Error(err),
			logiface.Int64("parent_id", req.ParentID),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "children loaded successfully",
		logiface.Int64("parent_id", req.ParentID),
		logiface.Int64("total", children.Total),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Success(c, children)
}

// GetResourcePermissions 获取资源权限
func (h *ResourceHandler) GetResourcePermissions(c *gin.Context) {
	var req dto.ResourcePermissionsRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 验证必填字段
	v := validator.NewValidator()
	v.RequiredInt(req.ResourceID, "resource_id")

	if err := v.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "get resource permissions validation failed",
			logiface.Int64("resource_id", req.ResourceID),
			logiface.String("ip", c.ClientIP()),
			logiface.Any("errors", v.Errors().ToResponseDetails()),
		)
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	permissions, err := h.resourceService.GetResourcePermissions(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "get resource permissions failed",
			logiface.Error(err),
			logiface.Int64("resource_id", req.ResourceID),
			logiface.String("ip", c.ClientIP()),
		)

		if err.Error() == "resource not found" {
			HandleUserError(c, err)
			return
		}

		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, permissions)
}

// GetAvailableAPIResources 获取可用的API资源（为页面资源分配API时使用）
func (h *ResourceHandler) GetAvailableAPIResources(c *gin.Context) {
	var req struct {
		PageResourceID interface{} `json:"page_resource_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	pageResourceID, err := parseIDFromAny(req.PageResourceID)
	if err != nil {
		h.logger.Warn(c.Request.Context(), "get available API resources validation failed: invalid page resource ID",
			logiface.Any("page_resource_id", req.PageResourceID),
			logiface.String("ip", c.ClientIP()),
		)
		details := []commonResponse.ValidationError{{Field: "page_resource_id", Message: "页面资源ID格式错误", Value: ""}}
		commonResponse.ValidationErrorResponse(c, details)
		return
	}

	tenantID := getTenantIDFromContext(c)

	apiResources, err := h.resourceService.GetAvailableAPIResources(c.Request.Context(), pageResourceID, tenantID)
	if err != nil {
		h.logger.Error(c.Request.Context(), "get available API resources failed",
			logiface.Error(err),
			logiface.Int64("page_resource_id", pageResourceID),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()),
		)

		if err.Error() == "resource not found" {
			HandleUserError(c, err)
			return
		}

		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, apiResources)
}

// BatchAssignAPIResources 批量分配API资源给页面资源
func (h *ResourceHandler) BatchAssignAPIResources(c *gin.Context) {
	var req dto.BatchAssignAPIResourcesRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 验证必填字段
	v := validator.NewValidator()
	v.RequiredInt(req.PageResourceID, "page_resource_id")
	v.RequiredArray(req.APIResourceIDs, "api_resource_ids")

	if err := v.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "batch assign API resources validation failed",
			logiface.Int64("page_resource_id", req.PageResourceID),
			logiface.Any("api_resource_ids", req.APIResourceIDs),
			logiface.String("ip", c.ClientIP()),
			logiface.Any("errors", v.Errors().ToResponseDetails()),
		)
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	h.logger.Info(c.Request.Context(), "batch assign API resources attempt",
		logiface.Int64("page_resource_id", req.PageResourceID),
		logiface.Any("api_resource_ids", req.APIResourceIDs),
		logiface.String("ip", c.ClientIP()),
	)

	err := h.resourceService.BatchAssignAPIResources(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "batch assign API resources failed",
			logiface.Error(err),
			logiface.Int64("page_resource_id", req.PageResourceID),
			logiface.Any("api_resource_ids", req.APIResourceIDs),
			logiface.String("ip", c.ClientIP()),
		)

		if err.Error() == "resource not found" {
			HandleUserError(c, err)
			return
		}

		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "API resources assigned successfully",
		logiface.Int64("page_resource_id", req.PageResourceID),
		logiface.Any("api_resource_ids", req.APIResourceIDs),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Success(c, gin.H{"message": "API resources assigned successfully"})
}

// UpdateResource 更新资源
func (h *ResourceHandler) UpdateResource(c *gin.Context) {
	var req dto.UpdateResourceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 自定义参数验证
	v := validator.NewValidator()
	if req.Name != "" {
		v.ResourceName(req.Name, "name")
	}
	if req.DisplayName != "" {
		v.MinLength(req.DisplayName, "display_name", 2).MaxLength(req.DisplayName, "display_name", 100)
	}
	if req.ResourceType != "" {
		v.ResourceType(req.ResourceType, "resource_type")
	}
	if req.Path != "" {
		v.Path(req.Path, "path")
	}
	if req.Description != "" {
		v.MaxLength(req.Description, "description", 255)
	}
	if err := v.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "update resource validation failed",
			logiface.String("name", req.Name),
			logiface.String("display_name", req.DisplayName),
			logiface.String("resource_type", req.ResourceType),
			logiface.String("path", req.Path),
			logiface.Int64("resource_id", req.ID),
			logiface.String("ip", c.ClientIP()),
			logiface.Any("errors", v.Errors().ToResponseDetails()),
		)
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	h.logger.Info(c.Request.Context(), "update resource attempt",
		logiface.Int64("resource_id", req.ID),
		logiface.String("ip", c.ClientIP()),
	)

	resource, err := h.resourceService.UpdateResource(c.Request.Context(), &req)
	if err != nil {
		if isUniqueConstraintError(err) {
			handleUniqueConstraintError(c, err)
			return
		}
		// 系统错误使用Error级别
		h.logger.Error(c.Request.Context(), "update resource failed",
			logiface.Error(err),
			logiface.Int64("resource_id", req.ID),
			logiface.String("ip", c.ClientIP()),
		)
		HandleUserError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "resource updated successfully",
		logiface.Int64("resource_id", req.ID),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Updated(c, resource)
}

// DeleteResource 删除资源
func (h *ResourceHandler) DeleteResource(c *gin.Context) {
	var req struct {
		ID interface{} `json:"id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	id, err := parseIDFromAny(req.ID)
	if err != nil {
		h.logger.Warn(c.Request.Context(), "delete resource validation failed: invalid ID",
			logiface.Any("resource_id", req.ID),
			logiface.String("ip", c.ClientIP()),
		)
		details := []commonResponse.ValidationError{{Field: "id", Message: "资源ID格式错误", Value: ""}}
		commonResponse.ValidationErrorResponse(c, details)
		return
	}

	h.logger.Info(c.Request.Context(), "delete resource attempt",
		logiface.Int64("resource_id", id),
		logiface.String("ip", c.ClientIP()),
	)

	err = h.resourceService.DeleteResource(c.Request.Context(), id)
	if err != nil {
		if err.Error() == "resource not found" {
			h.logger.Warn(c.Request.Context(), "resource not found",
				logiface.Int64("resource_id", id),
				logiface.String("ip", c.ClientIP()),
			)
			HandleUserError(c, err)
			return
		}

		// 系统错误使用Error级别
		h.logger.Error(c.Request.Context(), "delete resource failed",
			logiface.Error(err),
			logiface.Int64("resource_id", id),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "resource deleted successfully",
		logiface.Int64("resource_id", id),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Deleted(c)
}

// GetResourceStats 获取资源统计信息
func (h *ResourceHandler) GetResourceStats(c *gin.Context) {
	// 空的请求体，符合interface-design原则
	var req struct{}
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	tenantID := getTenantIDFromContext(c)

	stats, err := h.resourceService.GetResourceStats(c.Request.Context(), tenantID)
	if err != nil {
		h.logger.Error(c.Request.Context(), "get resource stats failed",
			logiface.Error(err),
			logiface.Int64("tenant_id", tenantID),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "resource stats retrieved successfully",
		logiface.Int64("tenant_id", tenantID),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Success(c, stats)
}

// ConfigureResourcePermissions 配置资源权限
func (h *ResourceHandler) ConfigureResourcePermissions(c *gin.Context) {
	var req dto.ConfigureResourcePermissionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 从上下文获取租户ID
	req.TenantID = getTenantIDFromContext(c)

	h.logger.Info(c.Request.Context(), "configure resource permissions attempt",
		logiface.Int64("resource_id", req.ResourceID),
		logiface.Int("permissions_count", len(req.Permissions)),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("ip", c.ClientIP()),
	)

	result, err := h.resourceService.ConfigureResourcePermissions(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error(c.Request.Context(), "configure resource permissions failed",
			logiface.Error(err),
			logiface.Int64("resource_id", req.ResourceID),
			logiface.Int("permissions_count", len(req.Permissions)),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.String("ip", c.ClientIP()),
		)

		if err.Error() == "resource not found" {
			HandleUserError(c, err)
			return
		}

		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "resource permissions configured successfully",
		logiface.Int64("resource_id", req.ResourceID),
		logiface.Int("success_count", result.SuccessCount),
		logiface.Int("skipped_count", result.SkippedCount),
		logiface.Int("total_count", result.TotalCount),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Success(c, result)
}

// AssignResourcesToApp 将资源分配给应用
// @Summary 将资源分配给应用
// @Description 将指定的资源分配给应用
// @Tags 资源管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.AssignResourcesToAppRequest true "资源分配请求"
// @Success 200 {object} commonResponse.Response{data=dto.AssignResourcesToAppResponse} "分配成功"
// @Failure 400 {object} commonResponse.Response "参数错误"
// @Failure 401 {object} commonResponse.Response "未授权"
// @Failure 500 {object} commonResponse.Response "服务器错误"
// @Router /api/user/resource/assign-to-app [post]
func (h *ResourceHandler) AssignResourcesToApp(c *gin.Context) {
	var req dto.AssignResourcesToAppRequest

	// 从请求体获取参数
	if err := c.ShouldBindJSON(&req); err != nil {
		handleGinValidationError(c, err)
		return
	}

	// 验证必填字段
	v := validator.NewValidator()
	v.RequiredArray(req.ResourceIDs, "resource_ids")
	v.Required(req.AppName, "app_name")
	v.RequiredInt(req.TenantID, "tenant_id")
	v.RequiredInt(req.TargetInternalAppID, "target_internal_app_id")

	if err := v.Validate(); err != nil {
		h.logger.Warn(c.Request.Context(), "assign resources to app validation failed",
			logiface.String("app_name", req.AppName),
			logiface.Int("resource_count", len(req.ResourceIDs)),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.Int64("target_internal_app_id", req.TargetInternalAppID),
			logiface.String("ip", c.ClientIP()),
			logiface.Any("errors", v.Errors().ToResponseDetails()),
		)
		commonResponse.ValidationErrorResponse(c, v.Errors().ToResponseDetails())
		return
	}

	h.logger.Info(c.Request.Context(), "assigning resources to app",
		logiface.String("app_name", req.AppName),
		logiface.Int("resource_count", len(req.ResourceIDs)),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.Int64("target_internal_app_id", req.TargetInternalAppID),
		logiface.String("ip", c.ClientIP()),
	)

	// 调用应用服务进行资源分配
	result, err := h.resourceService.AssignResourcesToApp(c.Request.Context(), req.ResourceIDs, req.AppName, req.TenantID, req.TargetInternalAppID)
	if err != nil {
		h.logger.Error(c.Request.Context(), "failed to assign resources to app",
			logiface.Error(err),
			logiface.String("app_name", req.AppName),
			logiface.Int("resource_count", len(req.ResourceIDs)),
			logiface.Int64("tenant_id", req.TenantID),
			logiface.Int64("target_internal_app_id", req.TargetInternalAppID),
		)

		if err.Error() == "resource not found" || err.Error() == "app not found" {
			HandleUserError(c, err)
			return
		}

		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "resources assigned to app successfully",
		logiface.String("app_name", req.AppName),
		logiface.Int("success_count", result.SuccessCount),
		logiface.Int("failure_count", result.FailureCount),
		logiface.Int("total_count", result.TotalCount),
		logiface.Int64("tenant_id", req.TenantID),
		logiface.Int64("target_internal_app_id", req.TargetInternalAppID),
		logiface.String("ip", c.ClientIP()),
	)

	response := dto.AssignResourcesToAppResponse{
		SuccessCount: result.SuccessCount,
		FailureCount: result.FailureCount,
		TotalCount:   result.TotalCount,
	}

	commonResponse.Success(c, response)
}
