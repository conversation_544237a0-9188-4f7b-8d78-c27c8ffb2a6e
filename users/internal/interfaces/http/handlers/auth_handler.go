package handlers

import (
	"fmt"
	"gitee.com/heiyee/platforms/users/internal/application/auth/dto"
	"gitee.com/heiyee/platforms/users/internal/application/auth/service"
	"strconv"
	"strings"
	"time"

	"gitee.com/heiyee/platforms/pkg/logiface"

	commonResponse "gitee.com/heiyee/platforms/pkg/common/response"

	"errors"
	userErrors "gitee.com/heiyee/platforms/users/internal/domain/errors"

	"github.com/gin-gonic/gin"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	logger      logiface.Logger
	authService *service.AuthApplicationService
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(logger logiface.Logger, authService *service.AuthApplicationService) *AuthHandler {
	return &AuthHandler{
		logger:      logger,
		authService: authService,
	}
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req dto.LoginDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn(c.Request.Context(), "login request binding failed",
			logiface.Error(err),
			logiface.String("raw_error", err.Error()),
			logiface.String("error_type", fmt.Sprintf("%T", err)),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.GinValidationError(c, err)
		return
	}
	// 获取客户端IP和User-Agent
	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	h.logger.Info(c.Request.Context(), "user login attempt", logiface.Any("request", req))

	// 调用认证服务
	result, err := h.authService.Login(c.Request.Context(), &req, ipAddress, userAgent)
	if err != nil {
		h.logger.Info(c.Request.Context(), "user login failed",
			logiface.Error(err),
			logiface.String("username", req.Username),
			logiface.String("ip", ipAddress),
		)
		// 使用新的错误处理函数
		HandleUserError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "user login successful",
		logiface.String("username", req.Username),
		logiface.Int64("user_id", result.User.ID),
		logiface.String("ip", ipAddress),
	)

	commonResponse.Success(c, result)
}

// MFALogin MFA登录
func (h *AuthHandler) MFALogin(c *gin.Context) {
	var req dto.MFALoginDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 验证必填字段
	if strings.TrimSpace(req.SessionID) == "" {
		commonResponse.FieldError(c, "session_id", "会话ID不能为空")
		return
	}
	if strings.TrimSpace(req.Code) == "" {
		commonResponse.FieldError(c, "code", "验证码不能为空")
		return
	}

	// 获取客户端IP和User-Agent
	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	h.logger.Info(c.Request.Context(), "mfa login attempt",
		logiface.String("session_id", req.SessionID),
		logiface.String("ip", ipAddress),
	)

	result, err := h.authService.MFALogin(c.Request.Context(), &req, ipAddress, userAgent)
	if err != nil {
		// 检查是否为用户错误类型
		var userErr *userErrors.UserError
		if errors.As(err, &userErr) {
			// 对于用户错误，只记录警告日志，不记录错误日志
			h.logger.Warn(c.Request.Context(), "mfa login failed with user error",
				logiface.String("session_id", req.SessionID),
				logiface.String("ip", ipAddress),
				logiface.Int("error_code", userErr.GetCode()),
				logiface.String("error_message", userErr.GetMessage()),
			)
			HandleUserError(c, err)
			return
		}

		// 对于系统错误，记录错误日志
		h.logger.Error(c.Request.Context(), "mfa login failed with system error",
			logiface.Error(err),
			logiface.String("session_id", req.SessionID),
			logiface.String("ip", ipAddress),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "mfa login successful",
		logiface.String("session_id", req.SessionID),
		logiface.String("ip", ipAddress),
		logiface.Int64("user_id", result.User.ID),
	)

	commonResponse.Success(c, result)
}

// RefreshToken 刷新令牌
// 说明：该接口不是表单提交，不涉及与用户交互，ShouldBindJSON 失败时无需用 response.ValidationError 结构化返回。
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req dto.RefreshTokenDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 验证必填字段
	if strings.TrimSpace(req.RefreshToken) == "" {
		commonResponse.FieldError(c, "refresh_token", "刷新令牌不能为空")
		return
	}

	h.logger.Info(c.Request.Context(), "token refresh attempt",
		logiface.String("ip", c.ClientIP()),
	)

	result, err := h.authService.RefreshToken(c.Request.Context(), &req)
	if err != nil {
		// 检查是否为用户错误类型
		var userErr *userErrors.UserError
		if errors.As(err, &userErr) {
			// 对于用户错误，只记录警告日志，不记录错误日志
			h.logger.Warn(c.Request.Context(), "token refresh failed with user error",
				logiface.String("ip", c.ClientIP()),
				logiface.Int("error_code", userErr.GetCode()),
				logiface.String("error_message", userErr.GetMessage()),
			)
			HandleUserError(c, err)
			return
		}

		// 对于系统错误，记录错误日志
		h.logger.Error(c.Request.Context(), "token refresh failed with system error",
			logiface.Error(err),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "token refresh successful",
		logiface.String("ip", c.ClientIP()),
		logiface.Int64("user_id", result.User.ID),
	)

	commonResponse.Success(c, result)
}

// Logout 用户登出
func (h *AuthHandler) Logout(c *gin.Context) {
	var req dto.LogoutDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 从上下文获取用户ID
	userID := getUserIDFromContext(c)
	if userID == 0 {
		h.logger.Warn(c.Request.Context(), "logout attempt without authentication",
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.Unauthorized(c, "user not authenticated")
		return
	}

	h.logger.Info(c.Request.Context(), "user logout attempt",
		logiface.Int64("user_id", userID),
		logiface.String("session_id", req.SessionID),
		logiface.String("ip", c.ClientIP()),
	)

	err := h.authService.Logout(c.Request.Context(), &req, userID)
	if err != nil {
		// 检查是否为用户错误类型
		var userErr *userErrors.UserError
		if errors.As(err, &userErr) {
			// 对于用户错误，只记录警告日志，不记录错误日志
			h.logger.Warn(c.Request.Context(), "logout failed with user error",
				logiface.Int64("user_id", userID),
				logiface.String("session_id", req.SessionID),
				logiface.String("ip", c.ClientIP()),
				logiface.Int("error_code", userErr.GetCode()),
				logiface.String("error_message", userErr.GetMessage()),
			)
			HandleUserError(c, err)
			return
		}

		// 对于系统错误，记录错误日志
		h.logger.Error(c.Request.Context(), "logout failed with system error",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
			logiface.String("session_id", req.SessionID),
			logiface.String("ip", c.ClientIP()),
		)
		commonResponse.InternalError(c, err)
		return
	}

	h.logger.Info(c.Request.Context(), "user logout successful",
		logiface.Int64("user_id", userID),
		logiface.String("session_id", req.SessionID),
		logiface.String("ip", c.ClientIP()),
	)

	commonResponse.Success(c, gin.H{"message": "logout successful"})
}

// GetSessions 获取用户会话列表
func (h *AuthHandler) GetSessions(c *gin.Context) {
	// 从上下文获取用户ID
	userID := getUserIDFromContext(c)
	if userID == 0 {
		commonResponse.Unauthorized(c, "user not authenticated")
		return
	}

	// 解析分页参数
	page, pageSize := parsePaginationParams(c)

	sessions, err := h.authService.GetSessions(c.Request.Context(), userID, page, pageSize)
	if err != nil {
		// 检查是否为用户错误类型
		var userErr *userErrors.UserError
		if errors.As(err, &userErr) {
			// 对于用户错误，只记录警告日志，不记录错误日志
			h.logger.Warn(c.Request.Context(), "get sessions failed with user error",
				logiface.Int64("user_id", userID),
				logiface.Int("error_code", userErr.GetCode()),
				logiface.String("error_message", userErr.GetMessage()),
			)
			HandleUserError(c, err)
			return
		}

		// 对于系统错误，记录错误日志
		h.logger.Error(c.Request.Context(), "get sessions failed with system error",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
		)
		commonResponse.InternalError(c, err)
		return
	}

	// 确保会话数组不为nil，防止JSON序列化为null
	sessionList := sessions.Sessions
	if sessionList == nil {
		sessionList = make([]dto.SessionInfoDTO, 0)
	}

	commonResponse.Paginated(c, sessionList, sessions.Page, sessions.PageSize, sessions.Total)
}

// RevokeSession 撤销会话
func (h *AuthHandler) RevokeSession(c *gin.Context) {
	var req dto.RevokeSessionDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 验证必填字段
	if strings.TrimSpace(req.SessionID) == "" {
		commonResponse.FieldError(c, "session_id", "会话ID不能为空")
		return
	}

	// 从上下文获取用户ID
	userID := getUserIDFromContext(c)
	if userID == 0 {
		commonResponse.Unauthorized(c, "user not authenticated")
		return
	}

	err := h.authService.RevokeSession(c.Request.Context(), req.SessionID, userID)
	if err != nil {
		// 检查是否为用户错误类型
		var userErr *userErrors.UserError
		if errors.As(err, &userErr) {
			// 对于用户错误，只记录警告日志，不记录错误日志
			h.logger.Warn(c.Request.Context(), "revoke session failed with user error",
				logiface.Int64("user_id", userID),
				logiface.String("session_id", req.SessionID),
				logiface.Int("error_code", userErr.GetCode()),
				logiface.String("error_message", userErr.GetMessage()),
			)
			HandleUserError(c, err)
			return
		}

		// 对于系统错误，记录错误日志
		h.logger.Error(c.Request.Context(), "revoke session failed with system error",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
			logiface.String("session_id", req.SessionID),
		)
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, gin.H{"message": "session revoked successfully"})
}

// ForgotPassword 忘记密码
func (h *AuthHandler) ForgotPassword(c *gin.Context) {
	var req dto.ForgotPasswordDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	if strings.TrimSpace(req.Email) == "" {
		commonResponse.FieldError(c, "email", "邮箱不能为空")
		return
	}

	// 获取客户端信息
	ipAddress := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// 调用带上下文的忘记密码方法
	err := h.authService.ForgotPasswordWithContext(c.Request.Context(), &req, ipAddress, userAgent)
	if err != nil {
		// 检查是否为用户错误类型
		var userErr *userErrors.UserError
		if errors.As(err, &userErr) {
			// 对于用户错误，只记录警告日志，不记录错误日志
			h.logger.Warn(c.Request.Context(), "forgot password failed with user error",
				logiface.String("email", req.Email),
				logiface.Int("error_code", userErr.GetCode()),
				logiface.String("error_message", userErr.GetMessage()),
			)
			HandleUserError(c, err)
			return
		}

		// 对于系统错误，记录错误日志
		h.logger.Error(c.Request.Context(), "forgot password failed with system error",
			logiface.Error(err),
			logiface.String("email", req.Email),
		)
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, gin.H{"message": "password reset email sent"})
}

// ResetPassword 重置密码
func (h *AuthHandler) ResetPassword(c *gin.Context) {
	var req dto.ResetPasswordDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 验证必填字段
	if strings.TrimSpace(req.Token) == "" {
		commonResponse.FieldError(c, "token", "重置令牌不能为空")
		return
	}
	if strings.TrimSpace(req.NewPassword) == "" {
		commonResponse.FieldError(c, "new_password", "新密码不能为空")
		return
	}

	err := h.authService.ResetPassword(c.Request.Context(), &req)
	if err != nil {
		// 检查是否为用户错误类型
		var userErr *userErrors.UserError
		if errors.As(err, &userErr) {
			// 对于用户错误，只记录警告日志，不记录错误日志
			h.logger.Warn(c.Request.Context(), "reset password failed with user error",
				logiface.String("token", req.Token),
				logiface.Int("error_code", userErr.GetCode()),
				logiface.String("error_message", userErr.GetMessage()),
			)
			HandleUserError(c, err)
			return
		}

		// 对于系统错误，记录错误日志
		h.logger.Error(c.Request.Context(), "reset password failed with system error",
			logiface.Error(err),
			logiface.String("token", req.Token),
		)
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, gin.H{"message": "password reset successfully"})
}

// ResetPasswordPage 密码重置页面验证 (GET endpoint for email links)
func (h *AuthHandler) ResetPasswordPage(c *gin.Context) {
	// 从查询参数获取重置令牌
	token := c.Query("token")
	if token == "" {
		h.logger.Warn(c.Request.Context(), "password reset page missing token",
			logiface.String("ip", c.ClientIP()))
		commonResponse.BadRequest(c, "重置令牌不能为空")
		return
	}

	h.logger.Info(c.Request.Context(), "password reset page access",
		logiface.String("token", token),
		logiface.String("ip", c.ClientIP()))

	// 验证令牌有效性（不实际使用，只检查状态）
	if err := h.authService.ValidateResetToken(c.Request.Context(), token); err != nil {
		// 检查是否为用户错误类型
		var userErr *userErrors.UserError
		if errors.As(err, &userErr) {
			h.logger.Info(c.Request.Context(), "password reset page token validation failed",
				logiface.String("token", token),
				logiface.String("ip", c.ClientIP()),
				logiface.Int("error_code", userErr.GetCode()),
				logiface.String("error_message", userErr.GetMessage()))
			HandleUserError(c, err)
			return
		}

		h.logger.Error(c.Request.Context(), "password reset page token validation error",
			logiface.Error(err),
			logiface.String("token", token),
			logiface.String("ip", c.ClientIP()))
		commonResponse.InternalError(c, err)
		return
	}

	// 返回成功响应，前端可以展示密码重置表单
	commonResponse.Success(c, gin.H{
		"valid":   true,
		"token":   token,
		"message": "令牌有效，可以重置密码",
	})
}

// SendMFACode 发送MFA验证码
func (h *AuthHandler) SendMFACode(c *gin.Context) {
	var req dto.SendMFACodeDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 从上下文获取用户ID
	userID := getUserIDFromContext(c)
	if userID == 0 {
		commonResponse.Unauthorized(c, "user not authenticated")
		return
	}

	// 设置用户ID
	req.UserID = userID

	err := h.authService.SendMFACode(c.Request.Context(), &req)
	if err != nil {
		// 检查是否为用户错误类型
		var userErr *userErrors.UserError
		if errors.As(err, &userErr) {
			// 对于用户错误，只记录警告日志，不记录错误日志
			h.logger.Warn(c.Request.Context(), "send MFA code failed with user error",
				logiface.Int64("user_id", userID),
				logiface.Int("error_code", userErr.GetCode()),
				logiface.String("error_message", userErr.GetMessage()),
			)
			HandleUserError(c, err)
			return
		}

		// 对于系统错误，记录错误日志
		h.logger.Error(c.Request.Context(), "send MFA code failed with system error",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
		)
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, gin.H{"message": "MFA code sent successfully"})
}

// VerifyMFACode 验证MFA验证码
func (h *AuthHandler) VerifyMFACode(c *gin.Context) {
	var req dto.VerifyMFACodeDTO
	if err := c.ShouldBindJSON(&req); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 从上下文获取用户ID
	userID := getUserIDFromContext(c)
	if userID == 0 {
		commonResponse.Unauthorized(c, "user not authenticated")
		return
	}

	// 验证必填字段
	if strings.TrimSpace(req.Code) == "" {
		commonResponse.FieldError(c, "code", "验证码不能为空")
		return
	}

	// 设置用户ID
	req.UserID = userID

	result, err := h.authService.VerifyMFACode(c.Request.Context(), &req)
	if err != nil {
		// 检查是否为用户错误类型
		var userErr *userErrors.UserError
		if errors.As(err, &userErr) {
			// 对于用户错误，只记录警告日志，不记录错误日志
			h.logger.Warn(c.Request.Context(), "verify MFA code failed with user error",
				logiface.Int64("user_id", userID),
				logiface.String("code", req.Code),
				logiface.Int("error_code", userErr.GetCode()),
				logiface.String("error_message", userErr.GetMessage()),
			)
			HandleUserError(c, err)
			return
		}

		// 对于系统错误，记录错误日志
		h.logger.Error(c.Request.Context(), "verify MFA code failed with system error",
			logiface.Error(err),
			logiface.Int64("user_id", userID),
			logiface.String("code", req.Code),
		)
		commonResponse.InternalError(c, err)
		return
	}

	commonResponse.Success(c, result)
}

// HealthCheck 健康检查
func (h *AuthHandler) HealthCheck(c *gin.Context) {
	commonResponse.Success(c, gin.H{
		"status":        "ok",
		"timestamp":     strconv.FormatInt(time.Now().Unix(), 10),
		"timestamp_str": time.Now().Format("2006-01-02 15:04:05"),
	})
}
