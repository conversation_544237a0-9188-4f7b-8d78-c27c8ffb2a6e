package errors

import (
	"fmt"
)

// 用户模块错误码范围: 110000-119999
const (
	// 用户认证相关错误 (110000-110099)
	CodeUserNotFound           = 110000 // 用户不存在 - 当根据用户ID、用户名、邮箱或手机号查找用户时，数据库中不存在该用户记录
	CodeUserAlreadyExists      = 110001 // 用户已存在 - 在用户注册、创建用户或更新用户信息时，发现用户名、邮箱或手机号已被其他用户使用
	CodeUserLocked             = 110002 // 用户被锁定 - 用户账户因安全原因被管理员或系统锁定，需要解锁后才能正常使用
	CodeUserDisabled           = 110003 // 用户被禁用 - 用户账户被管理员禁用，无法登录和访问系统
	CodeUserInactive           = 110004 // 用户未激活 - 用户注册后未完成邮箱验证、手机验证或其他激活流程
	CodeUserExpired            = 110005 // 用户已过期 - 用户账户因超过有效期而失效，需要续期或重新激活
	CodeUserSuspended          = 110006 // 用户已暂停 - 用户账户被临时暂停，暂停期间无法使用系统功能
	CodeUserStatusInvalid      = 110007 // 用户状态无效 - 用户状态值不在有效范围内，可能是数据异常或状态枚举值错误
	CodeInvalidCredentials     = 110008 // 用户名或密码错误 - 用户登录时提供的用户名或密码不正确
	CodePasswordIncorrect      = 110009 // 密码错误 - 用户输入的密码与数据库中存储的密码不匹配
	CodePasswordExpired        = 110010 // 密码已过期 - 用户密码超过设定的有效期，需要修改密码
	CodePasswordTooWeak        = 110011 // 密码强度不足 - 用户设置的密码不符合系统安全策略要求
	CodePasswordHistoryLimit   = 110012 // 密码历史限制 - 用户设置的新密码与历史密码重复，违反密码历史策略
	CodePasswordPolicyViolated = 110013 // 密码策略违规 - 用户设置的密码不符合密码复杂度、长度等安全策略
	CodeUserAccountLocked      = 110014 // 用户账户被锁定 - 用户账户因多次登录失败或其他安全原因被系统自动锁定
	CodeUserAccountDisabled    = 110015 // 用户账户被禁用 - 用户账户被管理员手动禁用，无法登录系统
	CodeLoginAttemptsExceeded  = 110016 // 登录尝试次数超限 - 用户在短时间内多次登录失败，触发账户保护机制
	CodeIPBlocked              = 110017 // IP被阻止 - 用户使用的IP地址被系统或管理员加入黑名单
	CodeAccountLocked          = 110014 // 账户被锁定 (别名) - 与CodeUserAccountLocked相同，提供更简洁的命名
	CodeAccountDisabled        = 110015 // 账户被禁用 (别名) - 与CodeUserAccountDisabled相同，提供更简洁的命名
	CodeDeviceNotTrusted       = 110018 // 设备不受信任 - 用户使用的设备未通过安全验证，需要额外的身份验证
	CodeLocationRestricted     = 110019 // 地理位置受限 - 用户当前的地理位置不在允许访问的范围内
	CodeTimeRestricted         = 110020 // 时间受限 - 用户当前的时间不在允许访问的时间范围内
	CodeConcurrentLoginLimit   = 110021 // 并发登录限制 - 用户同时在多个设备或会话中登录，超过系统限制
	CodeSessionExpired         = 110022 // 会话已过期 - 用户会话超过设定的有效期，需要重新登录
	CodeSessionInvalid         = 110023 // 会话无效 - 用户会话数据损坏或格式错误，无法验证会话有效性
	CodeSessionNotFound        = 110024 // 会话不存在 - 用户会话在数据库中不存在，可能是会话已被清理或从未创建
	CodeSessionRevoked         = 110025 // 会话已撤销 - 用户会话被管理员或系统主动撤销，需要重新登录
	CodeTokenExpired           = 110026 // Token已过期 - 用户访问令牌超过有效期，需要重新获取令牌
	CodeTokenInvalid           = 110027 // Token无效 - 用户访问令牌格式错误、签名无效或已被篡改
	CodeTokenRevoked           = 110028 // Token已撤销 - 用户访问令牌被主动撤销，无法继续使用
	CodeTokenNotFound          = 110029 // Token不存在 - 用户访问令牌在系统中不存在，可能是令牌已被清理
	CodeAttemptLimitExceeded   = 110030 // 尝试次数超限（通用频控错误码） - 用户在短时间内进行某项操作的次数超过限制
	CodeMFARequired            = 110031 // 需要多因素认证 - 系统要求用户进行多因素认证才能完成操作
	CodeMFAInvalid             = 110032 // 多因素认证失败 - 用户提供的多因素认证码不正确或已过期
	CodeMFAExpired             = 110033 // 多因素认证已过期 - 多因素认证码超过有效期，需要重新获取
	CodeCaptchaRequired        = 110034 // 需要验证码 - 系统要求用户输入验证码才能继续操作
	CodeCaptchaInvalid         = 110035 // 验证码错误 - 用户输入的验证码不正确
	CodeCaptchaExpired         = 110036 // 验证码已过期 - 验证码超过有效期，需要重新获取

	// 用户注册相关错误 (110100-110199)
	CodeRegistrationDisabled      = 110100 // 注册功能已禁用 - 系统管理员禁用了用户注册功能，新用户无法注册
	CodeInvitationRequired        = 110101 // 需要邀请码 - 系统要求用户必须使用有效的邀请码才能注册
	CodeInvitationInvalid         = 110102 // 邀请码无效 - 用户提供的邀请码格式错误或不存在
	CodeInvitationExpired         = 110103 // 邀请码已过期 - 邀请码超过有效期，无法继续使用
	CodeInvitationUsed            = 110104 // 邀请码已使用 - 邀请码已被其他用户使用，无法重复使用
	CodeInvitationLimitReached    = 110105 // 邀请码使用次数已达上限 - 邀请码的使用次数已达到最大限制
	CodeEmailVerificationRequired = 110106 // 需要邮箱验证 - 用户注册后需要验证邮箱地址才能激活账户
	CodePhoneVerificationRequired = 110107 // 需要手机验证 - 用户注册后需要验证手机号才能激活账户
	CodeUsernameExists            = 110108 // 用户名已存在 - 用户注册时选择的用户名已被其他用户使用
	CodeEmailExists               = 110109 // 邮箱已存在 - 用户注册时提供的邮箱地址已被其他用户使用
	CodePhoneExists               = 110110 // 手机号已存在 - 用户注册时提供的手机号已被其他用户使用
	CodeUsernameInvalid           = 110111 // 用户名格式无效 - 用户提供的用户名不符合系统命名规则
	CodeEmailInvalid              = 110112 // 邮箱格式无效 - 用户提供的邮箱地址格式不正确
	CodePhoneInvalid              = 110113 // 手机号格式无效 - 用户提供的手机号格式不正确
	CodeRealNameRequired          = 110114 // 真实姓名必填 - 用户注册时必须提供真实姓名
	CodeRealNameInvalid           = 110115 // 真实姓名格式无效 - 用户提供的真实姓名格式不符合要求
	CodeAgeRestriction            = 110116 // 年龄限制 - 用户年龄不符合系统设定的年龄要求
	CodeRegionRestriction         = 110117 // 地区限制 - 用户所在地区不在系统允许注册的范围内
	CodeDomainRestriction         = 110118 // 域名限制 - 用户使用的邮箱域名不在系统允许的范围内
	CodeRateLimit                 = 110119 // 频率限制 - 用户在短时间内多次尝试注册/发送验证码，超过频率限制
	CodeVerificationTokenInvalid  = 110120 // 验证令牌无效 - 用户提供的验证令牌格式错误或已失效

	// 用户信息相关错误 (110200-110299)
	CodeProfileUpdateFailed  = 110200 // 个人信息更新失败 - 用户更新个人信息时发生系统错误
	CodeAvatarUploadFailed   = 110201 // 头像上传失败 - 用户上传头像文件时发生错误
	CodeAvatarFormatInvalid  = 110202 // 头像格式无效 - 用户上传的头像文件格式不被系统支持
	CodeAvatarSizeExceeded   = 110203 // 头像大小超限 - 用户上传的头像文件大小超过系统限制
	CodeEmailUpdateFailed    = 110204 // 邮箱更新失败 - 用户更新邮箱地址时发生错误
	CodePhoneUpdateFailed    = 110205 // 手机号更新失败 - 用户更新手机号时发生错误
	CodeRealNameUpdateFailed = 110206 // 真实姓名更新失败 - 用户更新真实姓名时发生错误
	CodeDepartmentNotFound   = 110207 // 部门不存在 - 用户指定的部门在系统中不存在
	CodePositionNotFound     = 110208 // 职位不存在 - 用户指定的职位在系统中不存在
	CodeEmployeeIDExists     = 110209 // 员工编号已存在 - 用户提供的员工编号已被其他员工使用
	CodeEmployeeIDInvalid    = 110210 // 员工编号格式无效 - 用户提供的员工编号格式不符合要求
	CodeHireDateInvalid      = 110211 // 入职日期无效 - 用户提供的入职日期格式错误或不在合理范围内
	CodeProfileIncomplete    = 110212 // 个人信息不完整 - 用户必须填写的个人信息字段未完整填写

	// 用户权限相关错误 (110300-110399)
	CodePermissionDenied         = 110300 // 权限不足 - 用户尝试执行某项操作时，权限级别不够
	CodeRoleNotFound             = 110301 // 角色不存在 - 用户指定的角色在系统中不存在
	CodeRoleAlreadyAssigned      = 110302 // 角色已分配 - 用户已经被分配了指定的角色
	CodeRoleNotAssigned          = 110303 // 角色未分配 - 用户未被分配指定的角色
	CodeRoleLimitExceeded        = 110304 // 角色数量超限 - 用户被分配的角色数量超过系统限制
	CodeRoleAlreadyExists        = 110305 // 角色已存在 - 尝试创建的角色名称在系统中已存在
	CodePermissionNotFound       = 110306 // 权限不存在 - 用户指定的权限在系统中不存在
	CodePermissionAlreadyGranted = 110307 // 权限已授予 - 用户已经被授予指定的权限
	CodePermissionNotGranted     = 110308 // 权限未授予 - 用户未被授予指定的权限
	CodeUserResourceNotFound     = 110309 // 用户资源不存在 - 用户尝试访问的资源在系统中不存在
	CodeResourceAccessDenied     = 110310 // 资源访问被拒绝 - 用户尝试访问资源时被系统拒绝
	CodeUserOperationNotAllowed  = 110311 // 用户操作不被允许 - 用户尝试执行的操作被系统策略禁止
	CodeInvalidAppID             = 110312 // 应用ID无效 - 内部应用ID格式错误或不存在
	CodeInvalidTenantID          = 110313 // 租户ID无效 - 租户ID格式错误或不存在
	CodeSelfOperationNotAllowed  = 110312 // 不能对自己执行此操作 - 用户尝试对自己执行不允许的操作
	CodeAdminOperationRequired   = 110313 // 需要管理员权限 - 执行该操作需要管理员级别的权限
	CodeSuperAdminRequired       = 110314 // 需要超级管理员权限 - 执行该操作需要超级管理员级别的权限

	// 租户相关错误 (110400-110499)
	CodeTenantNotFound            = 110400 // 租户不存在 - 指定的租户在系统中不存在
	CodeTenantDisabled            = 110401 // 租户已禁用 - 租户被管理员禁用，无法正常使用系统
	CodeTenantExpired             = 110402 // 租户已过期 - 租户的服务期限已到期，需要续费
	CodeTenantSuspended           = 110403 // 租户已暂停 - 租户被临时暂停服务
	CodeTenantCodeInvalid         = 110404 // 租户代码无效 - 租户代码格式错误或不符合命名规则
	CodeTenantNameExists          = 110405 // 租户名称已存在 - 尝试创建的租户名称在系统中已存在
	CodeTenantCodeExists          = 110406 // 租户代码已存在 - 尝试创建的租户代码在系统中已存在
	CodeTenantLimitReached        = 110407 // 租户限制已达上限 - 租户数量已达到系统设定的上限
	CodeTenantUserLimitReached    = 110408 // 租户用户数量已达上限 - 租户下的用户数量已达到限制
	CodeTenantStorageLimitReached = 110409 // 租户存储空间已达上限 - 租户的存储空间使用量已达到限制
	CodeTenantQuotaExceeded       = 110410 // 租户配额超限 - 租户的资源使用量超过配额限制
	CodeTenantConfigInvalid       = 110411 // 租户配置无效 - 租户的配置参数格式错误或值不在有效范围内
	CodeTenantConfigNotFound      = 110412 // 租户配置不存在 - 租户的配置信息在系统中不存在

	// 第三方认证相关错误 (110500-110599)
	CodeOAuthProviderNotFound          = 110500 // OAuth提供商不存在 - 指定的OAuth提供商在系统中未配置
	CodeOAuthProviderDisabled          = 110501 // OAuth提供商已禁用 - OAuth提供商被管理员禁用
	CodeOAuthProviderError             = 110502 // OAuth提供商错误 - OAuth提供商返回错误响应
	CodeOAuthTokenInvalid              = 110503 // OAuth Token无效 - OAuth访问令牌格式错误或已失效
	CodeOAuthTokenExpired              = 110504 // OAuth Token已过期 - OAuth访问令牌超过有效期
	CodeOAuthUserNotFound              = 110505 // OAuth用户不存在 - OAuth提供商返回的用户信息在系统中不存在
	CodeOAuthAccountLinked             = 110506 // OAuth账户已关联 - 该OAuth账户已经关联到其他用户
	CodeOAuthAccountUnlinked           = 110507 // OAuth账户未关联 - 该OAuth账户未关联到任何用户
	CodeOAuthScopeInvalid              = 110508 // OAuth权限范围无效 - OAuth请求的权限范围不被支持
	CodeOAuthCallbackInvalid           = 110509 // OAuth回调无效 - OAuth回调地址格式错误或不被允许
	CodeOAuthStateInvalid              = 110510 // OAuth状态无效 - OAuth状态参数不匹配或已过期
	CodeOAuthCodeInvalid               = 110511 // OAuth授权码无效 - OAuth授权码格式错误或已失效
	CodeOAuthCodeExpired               = 110512 // OAuth授权码已过期 - OAuth授权码超过有效期
	CodeOAuthCodeUsed                  = 110513 // OAuth授权码已使用 - OAuth授权码已被使用，无法重复使用
	CodeOAuthRefreshTokenInvalid       = 110514 // OAuth刷新Token无效 - OAuth刷新令牌格式错误或已失效
	CodeOAuthRefreshTokenExpired       = 110515 // OAuth刷新Token已过期 - OAuth刷新令牌超过有效期
	CodeOAuthUserInfoFailed            = 110516 // OAuth用户信息获取失败 - 从OAuth提供商获取用户信息时失败
	CodeOAuthEmailMismatch             = 110517 // OAuth邮箱不匹配 - OAuth账户的邮箱与系统用户邮箱不匹配
	CodeOAuthAccountConflict           = 110518 // OAuth账户冲突 - OAuth账户与现有用户账户产生冲突
	CodeThirdPartyLoginDisabled        = 110519 // 第三方登录已禁用 - 系统管理员禁用了第三方登录功能
	CodeThirdPartyPlatformNotSupported = 110520 // 第三方平台不支持 - 指定的第三方平台不被系统支持
	CodeThirdPartyConfigError          = 110521 // 第三方平台配置错误 - 第三方平台的配置参数错误
	CodeThirdPartyAuthCodeInvalid      = 110522 // 第三方授权码无效 - 第三方平台返回的授权码无效
	CodeThirdPartyAccountBound         = 110523 // 第三方账户已绑定其他用户 - 该第三方账户已绑定到其他用户
	CodeThirdPartyBindFailed           = 110524 // 第三方账户绑定失败 - 绑定第三方账户时发生错误
	CodeTokenGenerationFailed          = 110525 // 令牌生成失败 - 系统生成访问令牌时发生错误

	// 文件上传相关错误 (110600-110699)
	CodeFileUploadFailed         = 110600 // 文件上传失败 - 文件上传过程中发生系统错误
	CodeFileFormatInvalid        = 110601 // 文件格式无效 - 上传的文件格式不被系统支持
	CodeFileSizeExceeded         = 110602 // 文件大小超限 - 上传的文件大小超过系统限制
	CodeFileTypeNotAllowed       = 110603 // 文件类型不允许 - 上传的文件类型不在允许的范围内
	CodeFileVirusDetected        = 110604 // 检测到病毒 - 上传的文件被检测到包含恶意代码
	CodeFileCorrupted            = 110605 // 文件损坏 - 上传的文件数据损坏，无法正常读取
	CodeFileNotFound             = 110606 // 文件不存在 - 请求的文件在系统中不存在
	CodeFileAccessDenied         = 110607 // 文件访问被拒绝 - 用户没有权限访问指定的文件
	CodeFileDeleteFailed         = 110608 // 文件删除失败 - 删除文件时发生系统错误
	CodeFileDownloadFailed       = 110609 // 文件下载失败 - 下载文件时发生系统错误
	CodeFileStorageError         = 110610 // 文件存储错误 - 文件存储服务发生错误
	CodeFileQuotaExceeded        = 110611 // 文件配额超限 - 用户的文件存储空间已达到配额限制
	CodeFileRecordNotFound       = 110612 // 文件记录不存在 - 文件记录在数据库中不存在
	CodeFileUploadConfigNotFound = 110613 // 文件上传配置不存在 - 文件上传的配置信息不存在
	CodeFileSceneCodeExists      = 110614 // 场景编码已存在 - 文件上传场景编码在系统中已存在

	// 职位相关错误 (110700-110799)
	CodePositionNameExists = 110700 // 职位名称已存在 - 尝试创建的职位名称在系统中已存在
	CodePositionCodeExists = 110701 // 职位编码已存在 - 尝试创建的职位编码在系统中已存在

	// 验证码相关错误 (110800-110899)
	CodeCaptchaNotFound = 110800 // 验证码不存在 - 请求的验证码在系统中不存在或已过期

	// 参数验证相关错误 (110850-110899)
	CodeParameterValidationFailed = 110850 // 参数验证失败 - 请求参数不符合验证规则，如格式错误、长度超限、必填字段缺失等

	// 资源关系相关错误 (110850-110899)
	CodeResourceRelationExists   = 110851 // 资源关系已存在 - 尝试创建已存在的资源关系
	CodeResourceRelationNotFound = 110852 // 资源关系不存在 - 指定的资源关系在系统中不存在
	CodeResourceNotFound         = 110853 // 资源不存在 - 指定的资源在系统中不存在
	CodeValidationFailed         = 110854 // 验证失败 - 数据验证不通过
	CodeResourceRelationConflict = 110855 // 资源关系冲突 - 资源关系配置冲突

	// 系统错误 (110900-110999)
	CodeSystemError           = 110900 // 系统错误，系统内部出现问题，如代码bug，未初始化数据，接口调用，依赖异常等
	CodeDatabaseError         = 110901 // 数据库错误 - 数据库操作失败，如连接失败、查询错误、事务回滚等
	CodeCacheError            = 110902 // 缓存错误 - 缓存服务操作失败，如Redis连接失败、缓存读写错误等
	CodeNetworkError          = 110903 // 网络错误 - 网络连接失败，如DNS解析失败、连接超时、网络中断等
	CodeServiceUnavailable    = 110904 // 服务不可用 - 系统服务暂时不可用，如服务维护、负载过高、资源不足等
	CodeServiceTimeout        = 110905 // 服务超时 - 系统服务响应超时，如数据库查询超时、外部API调用超时等
	CodeServiceOverload       = 110906 // 服务过载 - 系统负载过高，无法处理更多请求
	CodeThirdPartyError       = 110907 // 第三方服务错误 - 调用第三方服务时发生错误
	CodeThirdPartyTimeout     = 110908 // 第三方服务超时 - 调用第三方服务时发生超时
	CodeThirdPartyUnavailable = 110909 // 第三方服务不可用 - 第三方服务暂时不可用
	CodeInternalError         = 110910 // 内部错误 - 系统内部发生未预期的错误
	CodeUnexpectedError       = 110911 // 意外错误 - 系统发生意外的错误，通常需要人工介入处理
	CodeIntegrationFailed     = 110912 // 集成失败 - 系统集成过程中发生错误，如数据同步失败、接口对接失败等
	CodeEmailSendFailed       = 110913 // 邮件发送失败 - 发送邮件时发生错误，如SMTP连接失败、邮件格式错误等
)

// 错误消息映射
var errorMessages = map[int]string{
	// 用户认证相关错误
	CodeUserNotFound:           "用户不存在",
	CodeUserAlreadyExists:      "用户已存在",
	CodeUserLocked:             "用户被锁定",
	CodeUserDisabled:           "用户被禁用",
	CodeUserInactive:           "用户未激活",
	CodeUserExpired:            "用户已过期",
	CodeUserSuspended:          "用户已暂停",
	CodeInvalidCredentials:     "用户名或密码错误",
	CodePasswordIncorrect:      "密码错误",
	CodePasswordExpired:        "密码已过期",
	CodePasswordTooWeak:        "密码强度不足",
	CodePasswordHistoryLimit:   "密码不能与历史密码相同",
	CodePasswordPolicyViolated: "密码不符合安全策略要求",
	CodeUserAccountLocked:      "用户账户被锁定",
	CodeUserAccountDisabled:    "用户账户被禁用",
	CodeLoginAttemptsExceeded:  "登录尝试次数超限",
	CodeIPBlocked:              "IP被阻止",
	CodeDeviceNotTrusted:       "设备不受信任",
	CodeLocationRestricted:     "地理位置受限",
	CodeTimeRestricted:         "时间受限",
	CodeConcurrentLoginLimit:   "并发登录限制",
	CodeSessionExpired:         "会话已过期",
	CodeSessionInvalid:         "会话无效",
	CodeSessionNotFound:        "会话不存在",
	CodeSessionRevoked:         "会话已撤销",
	CodeTokenExpired:           "Token已过期",
	CodeTokenInvalid:           "Token无效",
	CodeTokenRevoked:           "Token已撤销",
	CodeTokenNotFound:          "Token不存在",
	CodeAttemptLimitExceeded:   "尝试次数超限",
	CodeMFARequired:            "需要多因素认证",
	CodeMFAInvalid:             "多因素认证失败",
	CodeMFAExpired:             "多因素认证已过期",
	CodeCaptchaRequired:        "需要验证码",
	CodeCaptchaInvalid:         "验证码错误",
	CodeCaptchaExpired:         "验证码已过期",

	// 用户注册相关错误
	CodeRegistrationDisabled:      "注册功能已禁用",
	CodeInvitationRequired:        "需要邀请码",
	CodeInvitationInvalid:         "邀请码无效",
	CodeInvitationExpired:         "邀请码已过期",
	CodeInvitationUsed:            "邀请码已使用",
	CodeInvitationLimitReached:    "邀请码使用次数已达上限",
	CodeEmailVerificationRequired: "需要邮箱验证",
	CodePhoneVerificationRequired: "需要手机验证",
	CodeUsernameExists:            "用户名已存在",
	CodeEmailExists:               "邮箱已存在",
	CodePhoneExists:               "手机号已存在",
	CodeUsernameInvalid:           "用户名格式无效",
	CodeEmailInvalid:              "邮箱格式无效",
	CodePhoneInvalid:              "手机号格式无效",
	CodeRealNameRequired:          "真实姓名必填",
	CodeRealNameInvalid:           "真实姓名格式无效",
	CodeAgeRestriction:            "年龄限制",
	CodeRegionRestriction:         "地区限制",
	CodeDomainRestriction:         "域名限制",
	CodeRateLimit:                 "注册频率限制",
	CodeVerificationTokenInvalid:  "验证令牌无效",

	// 用户信息相关错误
	CodeProfileUpdateFailed:  "个人信息更新失败",
	CodeAvatarUploadFailed:   "头像上传失败",
	CodeAvatarFormatInvalid:  "头像格式无效",
	CodeAvatarSizeExceeded:   "头像大小超限",
	CodeEmailUpdateFailed:    "邮箱更新失败",
	CodePhoneUpdateFailed:    "手机号更新失败",
	CodeRealNameUpdateFailed: "真实姓名更新失败",
	CodeDepartmentNotFound:   "部门不存在",
	CodePositionNotFound:     "职位不存在",
	CodeEmployeeIDExists:     "员工编号已存在",
	CodeEmployeeIDInvalid:    "员工编号格式无效",
	CodeHireDateInvalid:      "入职日期无效",
	CodeProfileIncomplete:    "个人信息不完整",

	// 用户权限相关错误
	CodePermissionDenied:         "权限不足",
	CodeRoleNotFound:             "角色不存在",
	CodeRoleAlreadyAssigned:      "角色已分配",
	CodeRoleNotAssigned:          "角色未分配",
	CodeRoleLimitExceeded:        "角色数量超限",
	CodeRoleAlreadyExists:        "角色已存在",
	CodePermissionNotFound:       "权限不存在",
	CodePermissionAlreadyGranted: "权限已授予",
	CodePermissionNotGranted:     "权限未授予",
	CodeUserResourceNotFound:     "用户资源不存在",
	CodeResourceAccessDenied:     "资源访问被拒绝",
	CodeUserOperationNotAllowed:  "用户操作不被允许",
	CodeSelfOperationNotAllowed:  "不能对自己执行此操作",
	CodeAdminOperationRequired:   "需要管理员权限",
	CodeSuperAdminRequired:       "需要超级管理员权限",

	// 租户相关错误
	CodeTenantNotFound:            "租户不存在",
	CodeTenantDisabled:            "租户已禁用",
	CodeTenantExpired:             "租户已过期",
	CodeTenantSuspended:           "租户已暂停",
	CodeTenantCodeInvalid:         "租户代码无效",
	CodeTenantNameExists:          "租户名称已存在",
	CodeTenantCodeExists:          "租户代码已存在",
	CodeTenantLimitReached:        "租户限制已达上限",
	CodeTenantUserLimitReached:    "租户用户数量已达上限",
	CodeTenantStorageLimitReached: "租户存储空间已达上限",
	CodeTenantQuotaExceeded:       "租户配额超限",
	CodeTenantConfigInvalid:       "租户配置无效",
	CodeTenantConfigNotFound:      "租户配置不存在",

	// 第三方认证相关错误
	CodeOAuthProviderNotFound:          "OAuth提供商不存在",
	CodeOAuthProviderDisabled:          "OAuth提供商已禁用",
	CodeOAuthProviderError:             "OAuth提供商错误",
	CodeOAuthTokenInvalid:              "OAuth Token无效",
	CodeOAuthTokenExpired:              "OAuth Token已过期",
	CodeOAuthUserNotFound:              "OAuth用户不存在",
	CodeOAuthAccountLinked:             "OAuth账户已关联",
	CodeOAuthAccountUnlinked:           "OAuth账户未关联",
	CodeOAuthScopeInvalid:              "OAuth权限范围无效",
	CodeOAuthCallbackInvalid:           "OAuth回调无效",
	CodeOAuthStateInvalid:              "OAuth状态无效",
	CodeOAuthCodeInvalid:               "OAuth授权码无效",
	CodeOAuthCodeExpired:               "OAuth授权码已过期",
	CodeOAuthCodeUsed:                  "OAuth授权码已使用",
	CodeOAuthRefreshTokenInvalid:       "OAuth刷新Token无效",
	CodeOAuthRefreshTokenExpired:       "OAuth刷新Token已过期",
	CodeOAuthUserInfoFailed:            "OAuth用户信息获取失败",
	CodeOAuthEmailMismatch:             "OAuth邮箱不匹配",
	CodeOAuthAccountConflict:           "OAuth账户冲突",
	CodeThirdPartyLoginDisabled:        "第三方登录已禁用",
	CodeThirdPartyPlatformNotSupported: "第三方平台不支持",
	CodeThirdPartyConfigError:          "第三方平台配置错误",
	CodeThirdPartyAuthCodeInvalid:      "第三方授权码无效",
	CodeThirdPartyAccountBound:         "第三方账户已绑定其他用户",
	CodeThirdPartyBindFailed:           "第三方账户绑定失败",
	CodeTokenGenerationFailed:          "令牌生成失败",

	// 文件上传相关错误
	CodeFileUploadFailed:         "文件上传失败",
	CodeFileFormatInvalid:        "文件格式无效",
	CodeFileSizeExceeded:         "文件大小超限",
	CodeFileTypeNotAllowed:       "文件类型不允许",
	CodeFileVirusDetected:        "检测到病毒",
	CodeFileCorrupted:            "文件损坏",
	CodeFileNotFound:             "文件不存在",
	CodeFileAccessDenied:         "文件访问被拒绝",
	CodeFileDeleteFailed:         "文件删除失败",
	CodeFileDownloadFailed:       "文件下载失败",
	CodeFileStorageError:         "文件存储错误",
	CodeFileQuotaExceeded:        "文件配额超限",
	CodeFileRecordNotFound:       "文件记录不存在",
	CodeFileUploadConfigNotFound: "文件上传配置不存在",
	CodeFileSceneCodeExists:      "场景编码已存在",

	// 职位相关错误
	CodePositionNameExists: "职位名称已存在",
	CodePositionCodeExists: "职位编码已存在",

	// 验证码相关错误
	CodeCaptchaNotFound: "验证码不存在",

	// 参数验证相关错误
	CodeParameterValidationFailed: "参数验证失败",

	// 资源关系相关错误
	CodeResourceRelationExists:   "资源关系已存在",
	CodeResourceRelationNotFound: "资源关系不存在",
	CodeResourceNotFound:         "资源不存在",
	CodeValidationFailed:         "验证失败",
	CodeResourceRelationConflict: "资源关系冲突",

	// 系统错误
	CodeSystemError:           "系统错误",
	CodeDatabaseError:         "数据库错误",
	CodeCacheError:            "缓存错误",
	CodeNetworkError:          "网络错误",
	CodeServiceUnavailable:    "服务不可用",
	CodeServiceTimeout:        "服务超时",
	CodeServiceOverload:       "服务过载",
	CodeThirdPartyError:       "第三方服务错误",
	CodeThirdPartyTimeout:     "第三方服务超时",
	CodeThirdPartyUnavailable: "第三方服务不可用",
	CodeInternalError:         "内部错误",
	CodeUnexpectedError:       "意外错误",
	CodeIntegrationFailed:     "集成失败",
	CodeEmailSendFailed:       "邮件发送失败",
}

// UserError 用户模块错误
type UserError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Field   string `json:"field,omitempty"`
	Details string `json:"details,omitempty"`
}

// Error 实现error接口
// 使用场景：当需要将UserError转换为字符串时自动调用
// 返回格式：[错误码] 错误消息: 错误详情
// 注意：此方法主要用于日志记录和调试，不建议直接返回给用户
func (e *UserError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%d] %s: %s", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("[%d] %s", e.Code, e.Message)
}

// GetCode 获取错误码
// 使用场景：需要获取错误码进行错误分类或日志记录时
// 返回：整数类型的错误码
// 注意：错误码范围在110000-119999之间
func (e *UserError) GetCode() int {
	return e.Code
}

// GetMessage 获取错误消息
// 使用场景：需要获取用户友好的错误消息时
// 返回：字符串类型的错误消息
// 注意：此消息可以直接返回给用户界面
func (e *UserError) GetMessage() string {
	return e.Message
}

// GetDetails 获取错误详情
// 使用场景：需要获取详细的错误信息用于调试或日志记录时
// 返回：字符串类型的错误详情
// 注意：此详情可能包含敏感信息，不应直接返回给用户
func (e *UserError) GetDetails() string {
	return e.Details
}

// NewUserError 创建用户错误
// 使用场景：需要创建自定义错误时使用此基础方法
// 参数：
//   - code: 错误码，必须是预定义的错误码常量
//   - details: 可选的错误详情，用于提供额外的上下文信息
//
// 注意：
//   - 如果传入的code不存在于errorMessages中，会使用"未知错误"作为消息
//   - 建议优先使用预定义的便捷函数，而不是直接调用此方法
//
// 示例：
//
//	return NewUserError(CodeUsernameExists, fmt.Sprintf("username: %s", username))
func NewUserError(code int, message string, details ...string) *UserError {
	if message == "" {
		msg, exists := errorMessages[code]
		if !exists {
			message = "未知错误"
		} else {
			message = msg
		}
	}
	var detail string
	if len(details) > 0 {
		detail = details[0]
	}

	return &UserError{
		Code:    code,
		Message: message,
		Details: detail,
	}
}

// 核心便捷函数 - 只保留最常用的
// 这些函数提供了类型安全和可读性，同时避免了过度设计

// NewUserNotFoundError 创建用户不存在错误
// 使用场景：当根据用户ID查询用户但用户不存在时
// 参数：
//   - userID: 用户ID，可以是任何类型（int、string等）
//
// 注意：
//   - 仅在确认用户确实不存在时使用，不要用于权限验证
//   - 通常用于用户详情查询、用户信息更新等操作
//
// 示例：
//
//	user, err := userRepo.GetByID(123)
//	if err != nil {
//	    return NewUserNotFoundError(123)
//	}
func NewUserNotFoundError(userID interface{}) *UserError {
	return NewUserError(CodeUserNotFound, "", fmt.Sprintf("user_id: %v", userID))
}

// NewInvalidCredentialsError 创建凭证无效错误
// 使用场景：用户登录时用户名或密码错误
// 参数：
//   - username: 用户名，用于记录哪个用户尝试登录失败
//
// 注意：
//   - 不要在此错误中暴露密码信息
//   - 通常与登录尝试次数限制配合使用
//   - 建议记录IP地址用于安全审计
//
// 示例：
//
//	if !validatePassword(username, password) {
//	    return NewInvalidCredentialsError(username)
//	}
func NewInvalidCredentialsError(username string) *UserError {
	return NewUserError(CodeInvalidCredentials, "", fmt.Sprintf("username: %s", username))
}

// NewAccountLockedError 创建账户锁定错误
// 使用场景：用户账户因安全原因被锁定时
// 参数：
//   - reason: 锁定原因，如"登录失败次数过多"、"管理员锁定"等
//
// 注意：
//   - 锁定原因应该清晰明确，便于用户理解
//   - 通常与账户安全策略配合使用
//   - 建议提供解锁方式或联系管理员的信息
//
// 示例：
//
//	if loginAttempts > maxAttempts {
//	    return NewAccountLockedError("登录失败次数过多，账户已锁定")
//	}
func NewAccountLockedError(reason string) *UserError {
	return NewUserError(CodeUserAccountLocked, "", fmt.Sprintf("reason: %s", reason))
}

// NewPermissionDeniedError 创建权限不足错误
// 使用场景：用户尝试执行没有权限的操作时
// 参数：
//   - operation: 尝试执行的操作，如"删除用户"、"查看敏感数据"
//   - resource: 操作的目标资源，如"用户123"、"财务数据"
//
// 注意：
//   - 不要在此错误中暴露敏感的系统信息
//   - 通常与权限检查逻辑配合使用
//   - 建议记录操作日志用于安全审计
//
// 示例：
//
//	if !hasPermission(user, "delete", "user") {
//	    return NewPermissionDeniedError("删除用户", fmt.Sprintf("用户ID: %d", targetUserID))
//	}
func NewPermissionDeniedError(operation, resource string) *UserError {
	return NewUserError(CodePermissionDenied, "", fmt.Sprintf("operation: %s, resource: %s", operation, resource))
}

// NewTenantNotFoundError 创建租户不存在错误
// 使用场景：根据租户代码查询租户但租户不存在时
// 参数：
//   - tenantCode: 租户代码
//
// 注意：
//   - 仅在确认租户确实不存在时使用
//   - 通常用于租户信息查询、租户配置获取等操作
//   - 在多租户系统中，此错误可能影响整个租户的用户
//
// 示例：
//
//	tenant, err := tenantRepo.GetByCode("TENANT001")
//	if err != nil {
//	    return NewTenantNotFoundError("TENANT001")
//	}
func NewTenantNotFoundError(tenantCode string) *UserError {
	return NewUserError(CodeTenantNotFound, "", fmt.Sprintf("tenant_code: %s", tenantCode))
}

// NewSystemError 创建系统错误
// 使用场景：系统内部发生不可预期的错误时
// 参数：
//   - operation: 正在执行的操作，如"用户注册"、"数据同步"
//   - reason: 错误原因，如"内存不足"、"配置错误"
//
// 注意：
//   - 此错误表示系统级别的故障，需要管理员介入
//   - 不要在此错误中暴露敏感的系统信息
//   - 通常与系统监控和告警配合使用
//
// 示例：
//
//	if err := criticalOperation(); err != nil {
//	    return NewSystemError("用户注册", "数据库连接失败")
//	}
func NewSystemError(operation, reason string) *UserError {
	return NewUserError(CodeSystemError, "", fmt.Sprintf("operation: %s, reason: %s", operation, reason))
}

// NewDatabaseError 创建数据库错误
// 使用场景：数据库操作失败时
// 参数：
//   - operation: 数据库操作，如"查询用户"、"插入记录"
//   - reason: 数据库错误原因，如"连接超时"、"约束违反"
//
// 注意：
//   - 仅数据库发生异常使用此方法，如果数据库执行正常不应该使用
//   - 不要在此错误中暴露数据库连接信息或SQL语句
//   - 通常与数据库连接池和重试机制配合使用
//   - 此错误可能表示数据库配置问题或性能问题
//
// 示例：
//
//	if err := db.Query("SELECT * FROM users WHERE id = ?", userID); err != nil {
//	    return NewDatabaseError("查询用户", "连接超时")
//	}
func NewDatabaseError(operation, reason string) *UserError {
	return NewUserError(CodeDatabaseError, "", fmt.Sprintf("operation: %s, reason: %s", operation, reason))
}

// NewThirdPartyError 创建第三方服务错误
// 使用场景：调用第三方服务失败时
// 参数：
//   - service: 第三方服务名称，如"邮件服务"、"短信服务"
//   - reason: 错误原因，如"服务不可用"、"认证失败"
//
// 注意：
//   - 不要在此错误中暴露第三方服务的敏感信息
//   - 通常与重试机制和降级策略配合使用
//   - 此错误可能影响用户体验，需要合理的错误处理
//
// 示例：
//
//	if err := emailService.Send(email); err != nil {
//	    return NewThirdPartyError("邮件服务", "发送失败")
//	}
func NewThirdPartyError(service, reason string) *UserError {
	return NewUserError(CodeThirdPartyError, "", fmt.Sprintf("service: %s, reason: %s", service, reason))
}

// NewIntegrationFailedError 创建集成失败错误
// 使用场景：系统集成操作失败时
// 参数：
//   - reason: 集成失败的原因，如"数据格式不匹配"、"接口版本不兼容"
//
// 注意：
//   - 此错误通常涉及多个系统之间的协作
//   - 需要详细的错误信息来帮助排查问题
//   - 通常与系统监控和告警配合使用
//
// 示例：
//
//	if err := syncUserData(); err != nil {
//	    return NewIntegrationFailedError("用户数据同步失败：数据格式不匹配")
//	}
func NewIntegrationFailedError(reason string) *UserError {
	return NewUserError(CodeIntegrationFailed, reason)
}

// NewBusinessError 创建业务错误
// 使用场景：需要创建自定义业务逻辑错误时
// 参数：
//   - code: 业务错误码，必须是预定义的错误码常量
//   - reason: 业务错误原因
//
// 注意：
//   - 此方法提供了创建自定义业务错误的灵活性
//   - 建议优先使用预定义的便捷函数
//   - 确保传入的code是有效的错误码常量
//
// 示例：
//
//	if user.Age < 18 {
//	    return NewBusinessError(CodeAgeRestriction, "用户年龄不满足要求")
//	}
func NewBusinessError(code int, reason string) *UserError {
	return NewUserError(code, reason)
}

// NewAccountDisabledError 创建账户禁用错误
// 使用场景：用户账户被禁用时
// 参数：
//   - reason: 禁用原因
//
// 示例：
//
//	if user.Status == UserStatusDisabled {
//	    return NewAccountDisabledError("账户已被管理员禁用")
//	}
func NewAccountDisabledError(reason string) *UserError {
	return NewUserError(CodeUserAccountDisabled, "", fmt.Sprintf("reason: %s", reason))
}

// NewLoginAttemptsExceededError 创建登录尝试次数超限错误
// 使用场景：用户登录尝试次数超过限制时
// 参数：
//   - attempts: 当前尝试次数
//   - maxAttempts: 最大允许尝试次数
//
// 示例：
//
//	if loginAttempts >= maxAttempts {
//	    return NewLoginAttemptsExceededError(loginAttempts, maxAttempts)
//	}
func NewLoginAttemptsExceededError(attempts, maxAttempts int) *UserError {
	return NewUserError(CodeLoginAttemptsExceeded, "", fmt.Sprintf("attempts: %d, max: %d", attempts, maxAttempts))
}

// NewIPBlockedError 创建IP被阻止错误
// 使用场景：用户IP被阻止访问时
// 参数：
//   - ipAddress: 被阻止的IP地址
//   - reason: 阻止原因
//
// 示例：
//
//	if isIPBlocked(clientIP) {
//	    return NewIPBlockedError(clientIP, "too many failed attempts")
//	}
func NewIPBlockedError(ipAddress, reason string) *UserError {
	return NewUserError(CodeIPBlocked, "", fmt.Sprintf("ip: %s, reason: %s", ipAddress, reason))
}

// NewParameterValidationFailedError 创建参数验证失败错误
// 使用场景：任何参数校验失败时都可以使用此错误码
// 参数：
//   - field: 验证失败的字段名，如"username"、"email"、"phone"
//   - reason: 验证失败的具体原因，如"不能为空"、"格式不正确"、"长度超限"
//
// 注意：
//   - 此错误码适用于所有类型的参数验证失败
//   - 建议提供具体的字段名和失败原因，便于用户理解和修复
//   - 可以用于表单验证、API参数验证、业务规则验证等场景
//
// 示例：
//
//	if username == "" {
//	    return NewParameterValidationFailedError("username", "不能为空")
//	}
//	if !isValidEmail(email) {
//	    return NewParameterValidationFailedError("email", "格式不正确")
//	}
//	if len(password) < 8 {
//	    return NewParameterValidationFailedError("password", "长度不能少于8位")
//	}
func NewParameterValidationFailedError(field, reason string) *UserError {
	return &UserError{
		Code:    CodeParameterValidationFailed,
		Message: reason,
		Details: field,
	}
}
