package entity

import (
	"context"
	"fmt"

	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
)

// ResourceFactory 资源工厂 - 专门负责创建资源实体
type ResourceFactory struct {
	entityFactory *EntityFactory
}

// NewResourceFactory 创建资源工厂
func NewResourceFactory(entityFactory *EntityFactory) *ResourceFactory {
	return &ResourceFactory{
		entityFactory: entityFactory,
	}
}

// NewResource 创建新资源 - 使用依赖注入的ID生成器
func (f *ResourceFactory) NewResource(ctx context.Context, tenantID int64, internalAppID int64, name, displayName, description string,
	resourceType value_object.ResourceType, parentID *int64, path, icon string) (*Resource, error) {
	resourceID, err := f.entityFactory.idGenerator.GenerateResourceID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to generate resource ID: %w", err)
	}

	return &Resource{
		ID:            resourceID,
		TenantID:      tenantID,
		InternalAppID: internalAppID,
		Name:          name,
		DisplayName:   displayName,
		Description:   description,
		ResourceType:  resourceType,
		ParentID:      parentID,
		Path:          path,
		Icon:          icon,
		SortOrder:     0,
		IsSystem:      false,
		IsPublic:      false,
		PublicLevel:   value_object.PublicLevelNone,
		Assignable:    true,
	}, nil
}

// NewSystemResource 创建系统资源 - 使用依赖注入的ID生成器
func (f *ResourceFactory) NewSystemResource(ctx context.Context, tenantID int64, internalAppID int64, name, displayName, description string,
	resourceType value_object.ResourceType, parentID *int64, path, icon string) (*Resource, error) {
	return f.NewResourceWithOptions(ctx, tenantID, internalAppID, name, displayName, description, resourceType, parentID, path, icon, WithSystem())
}

// NewPublicResource 创建公开资源 - 使用依赖注入的ID生成器
func (f *ResourceFactory) NewPublicResource(ctx context.Context, tenantID int64, internalAppID int64, name, displayName, description string,
	resourceType value_object.ResourceType, parentID *int64, path, icon string,
	publicLevel value_object.PublicLevel) (*Resource, error) {
	return f.NewResourceWithOptions(ctx, tenantID, internalAppID, name, displayName, description, resourceType, parentID, path, icon, WithPublicAccess(publicLevel))
}

// NewNonAssignableResource 创建不可分配的资源 - 使用依赖注入的ID生成器
func (f *ResourceFactory) NewNonAssignableResource(ctx context.Context, tenantID int64, internalAppID int64, name, displayName, description string,
	resourceType value_object.ResourceType, parentID *int64, path, icon string) (*Resource, error) {
	return f.NewResourceWithOptions(ctx, tenantID, internalAppID, name, displayName, description, resourceType, parentID, path, icon, WithNonAssignable())
}

// NewSystemInternalResource 创建系统内部不可分配的资源 - 使用依赖注入的ID生成器
func (f *ResourceFactory) NewSystemInternalResource(ctx context.Context, tenantID int64, internalAppID int64, name, displayName, description string,
	resourceType value_object.ResourceType, parentID *int64, path, icon string) (*Resource, error) {
	return f.NewResourceWithOptions(ctx, tenantID, internalAppID, name, displayName, description, resourceType, parentID, path, icon, WithSystem(), WithNonAssignable())
}

// NewResourceWithOptions 创建资源 - 使用依赖注入的ID生成器，支持选项配置
func (f *ResourceFactory) NewResourceWithOptions(ctx context.Context, tenantID int64, internalAppID int64, name, displayName, description string,
	resourceType value_object.ResourceType, parentID *int64, path, icon string, options ...ResourceOption) (*Resource, error) {
	resource, err := f.NewResource(ctx, tenantID, internalAppID, name, displayName, description, resourceType, parentID, path, icon)
	if err != nil {
		return nil, err
	}

	// 应用选项
	for _, option := range options {
		option(resource)
	}

	return resource, nil
}
