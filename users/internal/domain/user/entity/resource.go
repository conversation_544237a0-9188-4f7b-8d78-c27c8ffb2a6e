package entity

import (
	"context"
	"net/http"
	"time"

	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
	commonResource "gitee.com/heiyee/platforms/pkg/common/resource"
)

// 确保Resource实现了commonResource.Resource接口
var _ commonResource.Resource = (*Resource)(nil)

// Resource 资源实体
type Resource struct {
	ID            int64
	TenantID      int64
	InternalAppID int64
	Name          string
	DisplayName   string
	Description   string
	ResourceType  value_object.ResourceType
	ServiceName   string
	RequestType   string
	ResponseType  string
	APIMethod     string
	ContentType   string
	ParentID      *int64
	Path          string
	Icon          string
	SortOrder     int
	IsSystem      bool
	DeletedAt     *time.Time // 新增：删除时间
	CreatedAt     time.Time
	UpdatedAt     time.Time
	IsPublic      bool
	PublicLevel   value_object.PublicLevel
	Assignable    bool
}

// ResourceOption 资源选项函数类型
type ResourceOption func(*Resource)

// WithInternalAppID 设置内部应用ID
func WithInternalAppID(internalAppID int64) ResourceOption {
	return func(r *Resource) {
		r.InternalAppID = internalAppID
	}
}

// WithSystem 设置为系统资源
func WithSystem() ResourceOption {
	return func(r *Resource) {
		r.IsSystem = true
	}
}

// WithPublicAccess 设置为公开资源
func WithPublicAccess(level value_object.PublicLevel) ResourceOption {
	return func(r *Resource) {
		r.SetPublicAccess(true, level)
	}
}

// WithNonAssignable 设置为不可分配资源
func WithNonAssignable() ResourceOption {
	return func(r *Resource) {
		r.SetAssignable(false)
	}
}

// WithSortOrder 设置排序
func WithSortOrder(sortOrder int) ResourceOption {
	return func(r *Resource) {
		r.SortOrder = sortOrder
	}
}

// IsPage 检查是否为页面资源
func (r *Resource) IsPage() bool {
	return r.ResourceType == value_object.ResourceTypePage
}

// IsAPI 检查是否为API资源
func (r *Resource) IsAPI() bool {
	return r.ResourceType == value_object.ResourceTypeAPI
}

// IsButton 检查是否为按钮资源
func (r *Resource) IsButton() bool {
	return r.ResourceType == value_object.ResourceTypeButton
}

// IsMenu 检查是否为菜单资源
func (r *Resource) IsMenu() bool {
	return r.ResourceType == value_object.ResourceTypeMenu
}

// HasParent 检查是否有父资源
func (r *Resource) HasParent() bool {
	return r.ParentID != nil
}

// TableName 指定表名
func (r *Resource) TableName() string {
	return "resource"
}

// GetFullPath 获取完整路径
func (r *Resource) GetFullPath() string {
	if r.Path == "" {
		return r.Name
	}
	return r.Path
}

// GetTypeDisplayName 获取资源类型显示名称
func (r *Resource) GetTypeDisplayName() string {
	return r.ResourceType.GetDisplayName()
}

// IsPublicResource 检查是否为公开资源
func (r *Resource) IsPublicResource() bool {
	return r.IsPublic && r.PublicLevel.IsPublic()
}

// CheckPublicAccess 检查公开访问条件
func (r *Resource) CheckPublicAccess(ctx context.Context, req *http.Request) bool {
	if !r.IsPublic {
		return false
	}

	switch r.PublicLevel {
	case value_object.PublicLevelAnonymous:
		return true
	case value_object.PublicLevelAuthenticated:
		// 检查是否有用户ID，表示已登录
		if userID := ctx.Value("user_id"); userID != nil {
			return true
		}
		return false
	case value_object.PublicLevelConditional:
		// 条件式访问需要额外的条件检查
		// 这里可以扩展为检查IP白名单、时间限制等
		return r.checkConditionalAccess(ctx, req)
	default:
		return false
	}
}

// checkConditionalAccess 检查条件式访问
func (r *Resource) checkConditionalAccess(ctx context.Context, req *http.Request) bool {
	// 这里可以扩展为更复杂的条件检查
	// 例如：IP白名单、时间窗口、User-Agent检查等
	return true // 默认允许，实际应根据具体条件判断
}

// SetPublicAccess 设置公开访问
func (r *Resource) SetPublicAccess(isPublic bool, level value_object.PublicLevel) {
	r.IsPublic = isPublic
	if isPublic && level.IsValid() {
		r.PublicLevel = level
	} else {
		r.PublicLevel = value_object.PublicLevelNone
	}
}

// GetPublicDisplayName 获取公开级别显示名称
func (r *Resource) GetPublicDisplayName() string {
	if !r.IsPublic {
		return "非公开"
	}
	return r.PublicLevel.GetDisplayName()
}

// IsAssignable 检查资源是否可以分配
func (r *Resource) IsAssignable() bool {
	return r.Assignable
}

// SetAssignable 设置资源是否可分配
func (r *Resource) SetAssignable(assignable bool) {
	r.Assignable = assignable
}

// CanAssignToUser 检查是否可以分配给用户
func (r *Resource) CanAssignToUser() bool {
	// 可分配的资源必须同时满足：
	// 1. 标记为可分配
	// 2. 不是系统内部资源（或者是用户可见的系统资源）
	return r.Assignable && (!r.IsSystem || r.IsUserVisibleSystemResource())
}

// IsUserVisibleSystemResource 检查是否为用户可见的系统资源
func (r *Resource) IsUserVisibleSystemResource() bool {
	// 用户管理、角色管理等页面虽然是系统资源，但用户可见
	userVisibleResources := []string{"用户管理", "角色管理", "权限管理", "部门管理", "职位管理"}
	for _, name := range userVisibleResources {
		if r.Name == name {
			return true
		}
	}
	return false
}

// GetAssignabilityStatus 获取可分配状态描述
func (r *Resource) GetAssignabilityStatus() string {
	if !r.Assignable {
		return "不可分配"
	}
	if r.IsSystem && !r.IsUserVisibleSystemResource() {
		return "系统内部"
	}
	return "可分配"
}

// 实现 commonResource.Resource 接口

// GetID 获取资源ID
func (r *Resource) GetID() int64 {
	return r.ID
}

// GetName 获取资源名称
func (r *Resource) GetName() string {
	return r.Name
}

// GetDisplayName 获取显示名称
func (r *Resource) GetDisplayName() string {
	return r.DisplayName
}

// GetResourceType 获取资源类型
func (r *Resource) GetResourceType() string {
	return string(r.ResourceType)
}

// GetPath 获取路径
func (r *Resource) GetPath() string {
	return r.Path
}

// GetAPIMethod 获取API方法
func (r *Resource) GetAPIMethod() string {
	return r.APIMethod
}

// GetTenantID 获取租户ID
func (r *Resource) GetTenantID() int64 {
	return r.TenantID
}

// GetInternalAppID 获取内部应用ID
func (r *Resource) GetInternalAppID() int64 {
	return r.InternalAppID
}
