package entity

import (
	"time"

	"gitee.com/heiyee/platforms/users/pkg/validator"
)

// ResourceRelation 资源关联关系实体（扩展版本，支持权限配置）
type ResourceRelation struct {
	ID               int64  `json:"id" gorm:"primaryKey"`
	TenantID         int64  `json:"tenant_id" gorm:"not null;index"`
	InternalAppID    int64  `json:"internal_app_id" gorm:"not null;default:1;index;comment:应用ID，bigint类型提升性能"`
	SourceResourceID int64  `json:"source_resource_id" gorm:"not null;index"`
	TargetResourceID int64  `json:"target_resource_id" gorm:"not null;index"`
	PermissionID     *int64 `json:"permission_id" gorm:"index;comment:权限ID，关联permissions表"`
	Description      string `json:"description" gorm:"size:500"`

	// 新增权限配置字段
	PermissionCode string `json:"permission_code" gorm:"size:100;index;comment:权限编码，用于同一API在不同页面的差异化权限"`
	IsRequired     bool   `json:"is_required" gorm:"default:1;comment:是否必需权限"`
	InheritParent  bool   `json:"inherit_parent" gorm:"default:1;comment:是否继承父级权限"`
	Priority       int    `json:"priority" gorm:"default:0;comment:权限优先级，数字越大优先级越高"`
	Status         string `json:"status" gorm:"size:20;default:active;index;comment:状态：active-启用，inactive-禁用"`

	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (ResourceRelation) TableName() string {
	return "resource_relations"
}

// IsValid 检查资源关系是否有效
func (rr *ResourceRelation) IsValid() bool {
	return rr.SourceResourceID > 0 && rr.TargetResourceID > 0 && rr.SourceResourceID != rr.TargetResourceID
}

// GetDescription 获取关联描述
func (rr *ResourceRelation) GetDescription() string {
	if rr.Description != "" {
		return rr.Description
	}
	return "资源关联关系"
}

// IsActive 检查关系是否激活
func (rr *ResourceRelation) IsActive() bool {
	return rr.Status == "active"
}

// GetDisplayStatus 获取状态显示名称
func (rr *ResourceRelation) GetDisplayStatus() string {
	switch rr.Status {
	case "active":
		return "启用"
	case "inactive":
		return "禁用"
	default:
		return "未知"
	}
}

// ValidatePermissionConfig 验证权限配置
func (rr *ResourceRelation) ValidatePermissionConfig() error {
	v := validator.NewValidator()

	// 验证状态
	v.In(rr.Status, "status", "active", "inactive")

	// 验证优先级
	v.Range(int64(rr.Priority), "priority", 0, 100)

	// 验证权限编码（如果提供）
	if rr.PermissionCode != "" {
		v.Length(rr.PermissionCode, "permission_code", 1, 100)
		v.Alphanumeric(rr.PermissionCode, "permission_code")
	}

	return v.Validate()
}

// SetPermissionConfig 设置权限配置
func (rr *ResourceRelation) SetPermissionConfig(
	permissionCode string,
	isRequired bool,
	inheritParent bool,
	priority int,
) error {
	rr.PermissionCode = permissionCode
	rr.IsRequired = isRequired
	rr.InheritParent = inheritParent
	rr.Priority = priority

	return rr.ValidatePermissionConfig()
}

// Activate 激活关系
func (rr *ResourceRelation) Activate() {
	rr.Status = "active"
	rr.UpdatedAt = time.Now()
}

// Deactivate 停用关系
func (rr *ResourceRelation) Deactivate() {
	rr.Status = "inactive"
	rr.UpdatedAt = time.Now()
}

// ResourceRelationOption 资源关系选项函数类型
type ResourceRelationOption func(*ResourceRelation)

// WithPermissionConfig 设置权限配置选项
func WithPermissionConfig(
	code string,
	required bool,
	inherit bool,
	priority int,
) ResourceRelationOption {
	return func(rr *ResourceRelation) {
		rr.PermissionCode = code
		rr.IsRequired = required
		rr.InheritParent = inherit
		rr.Priority = priority
	}
}

// WithDescriptionOption 设置描述选项
func WithDescriptionOption(description string) ResourceRelationOption {
	return func(rr *ResourceRelation) {
		rr.Description = description
	}
}

// WithStatusOption 设置状态选项
func WithStatusOption(status string) ResourceRelationOption {
	return func(rr *ResourceRelation) {
		rr.Status = status
	}
}

// NewResourceRelation 创建新的资源关系
func NewResourceRelation(
	tenantID, internalAppID, sourceResourceID, targetResourceID int64,
	options ...ResourceRelationOption,
) *ResourceRelation {
	rr := &ResourceRelation{
		TenantID:         tenantID,
		InternalAppID:    internalAppID,
		SourceResourceID: sourceResourceID,
		TargetResourceID: targetResourceID,
		IsRequired:       true,     // 默认必需权限
		InheritParent:    true,     // 默认继承父级权限
		Priority:         0,        // 默认优先级
		Status:           "active", // 默认激活状态
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	// 应用选项
	for _, option := range options {
		option(rr)
	}

	return rr
}
