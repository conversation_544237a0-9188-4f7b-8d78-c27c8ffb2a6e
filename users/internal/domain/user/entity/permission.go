package entity

import (
	"time"
)

// Permission 权限实体
type Permission struct {
	ID            int64
	TenantID      int64
	InternalAppID int64
	Name          string
	Code          string
	DisplayName   string
	Description   string
	Action        string // 操作类型：create, read, update, delete等
	Scope         string
	Status        string
	IsSystem      bool
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     *time.Time
}

// IsActive 检查权限是否激活
func (p *Permission) IsActive() bool {
	return p.Status == "active"
}

// Disable 禁用权限
func (p *Permission) Disable() {
	p.Status = "disabled"
}

// Enable 启用权限
func (p *Permission) Enable() {
	p.Status = "active"
}

// GetFullName 获取完整权限名称
func (p *Permission) GetFullName() string {
	return p.Name + ":" + p.Scope
}

// GetScopeDisplayName 获取权限范围显示名称
func (p *Permission) GetScopeDisplayName() string {
	switch p.Scope {
	case "all":
		return "全部权限"
	case "self":
		return "普通权限"
	default:
		return p.Scope
	}
}

// IsAllScope 检查是否为全部权限
func (p *Permission) IsAllScope() bool {
	return p.Scope == "all"
}

// IsSelfScope 检查是否为普通权限
func (p *Permission) IsSelfScope() bool {
	return p.Scope == "self"
}
