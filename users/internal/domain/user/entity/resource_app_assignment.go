package entity

import (
	"time"
)

// ResourceAppAssignment 资源应用关联实体
type ResourceAppAssignment struct {
	ID            int64      `json:"id"`
	TenantID      int64      `json:"tenant_id"`
	ResourceID    int64      `json:"resource_id"`
	InternalAppID int64      `json:"internal_app_id"`
	IsActive      bool       `json:"is_active"`
	AssignedBy    *int64     `json:"assigned_by"`
	AssignedAt    time.Time  `json:"assigned_at"`
	ExpiresAt     *time.Time `json:"expires_at"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
}

// TableName 指定表名
func (ResourceAppAssignment) TableName() string {
	return "resource_app_assignments"
}

// IsValid 检查资源应用关联是否有效
func (raa *ResourceAppAssignment) IsValid() bool {
	return raa.ResourceID > 0 && raa.InternalAppID > 0 && raa.TenantID > 0
}

// IsExpired 检查是否已过期
func (raa *ResourceAppAssignment) IsExpired() bool {
	if raa.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*raa.ExpiresAt)
}

// IsActiveAndValid 检查是否激活且有效
func (raa *ResourceAppAssignment) IsActiveAndValid() bool {
	return raa.IsActive && raa.IsValid() && !raa.IsExpired()
}

// GetDescription 获取关联描述
func (raa *ResourceAppAssignment) GetDescription() string {
	return "资源应用关联"
}
