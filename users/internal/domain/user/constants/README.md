# 用户领域常量包

本包包含了用户领域相关的所有常量定义，包括角色代码、权限代码等。

## 文件结构

- `role_constants.go` - 角色相关常量
- `permission_constants.go` - 权限相关常量
- `role_constants_example.go` - 角色常量使用示例
- `permission_constants_example.go` - 权限常量使用示例

## 角色常量

### 系统角色
- `RoleSuperAdmin` - 超级管理员
- `RoleAdmin` - 管理员
- `RoleUser` - 普通用户

### 业务角色
- `RoleManager` - 经理
- `RoleOperator` - 操作员
- `RoleViewer` - 查看者
- `RoleGuest` - 访客

### 辅助函数
- `IsSystemRole(roleCode string) bool` - 检查是否为系统角色
- `IsAdminRole(roleCode string) bool` - 检查是否为管理员角色
- `IsValidRole(roleCode string) bool` - 检查角色代码是否有效

## 权限常量

### 基础操作权限
- `PermissionRead` - 读取权限
- `PermissionWrite` - 写入权限
- `PermissionDelete` - 删除权限
- `PermissionAdmin` - 管理权限

### 系统权限
- `PermissionSystemSuperAdmin` - 系统超级管理员
- `PermissionSystemAdmin` - 系统管理员
- `PermissionSystemUser` - 系统用户

### 用户管理权限
- `PermissionUserCreate` - 创建用户
- `PermissionUserRead` - 读取用户
- `PermissionUserUpdate` - 更新用户
- `PermissionUserDelete` - 删除用户

### 角色管理权限
- `PermissionRoleCreate` - 创建角色
- `PermissionRoleRead` - 读取角色
- `PermissionRoleUpdate` - 更新角色
- `PermissionRoleDelete` - 删除角色

### 权限管理权限
- `PermissionPermissionCreate` - 创建权限
- `PermissionPermissionRead` - 读取权限
- `PermissionPermissionUpdate` - 更新权限
- `PermissionPermissionDelete` - 删除权限

### 资源管理权限
- `PermissionResourceCreate` - 创建资源
- `PermissionResourceRead` - 读取资源
- `PermissionResourceUpdate` - 更新资源
- `PermissionResourceDelete` - 删除资源

### 辅助函数
- `IsBasicPermission(permissionCode string) bool` - 检查是否为基础权限
- `IsSystemPermission(permissionCode string) bool` - 检查是否为系统权限
- `IsUserPermission(permissionCode string) bool` - 检查是否为用户管理权限
- `IsRolePermission(permissionCode string) bool` - 检查是否为角色管理权限
- `IsResourcePermission(permissionCode string) bool` - 检查是否为资源管理权限
- `IsValidPermission(permissionCode string) bool` - 检查权限代码是否有效
- `GetPermissionCategory(permissionCode string) string` - 获取权限分类

## 使用方式

### 导入包
```go
import "gitee.com/heiyee/platforms/users/internal/domain/user/constants"
```

### 角色检查
```go
// 检查用户是否为管理员
isAdmin, err := permissionService.IsUserAdmin(ctx, userID, appID)

// 检查用户是否有特定角色
hasRole, err := permissionService.HasUserRole(ctx, userID, appID, constants.RoleManager)

// 角色验证
if constants.IsValidRole(roleCode) {
    // 处理有效角色
}
```

### 权限检查
```go
// 检查用户是否有特定权限
hasPermission, err := permissionService.CheckUserPermission(ctx, userID, appID, constants.PermissionUserRead)

// 权限验证
if constants.IsValidPermission(permissionCode) {
    // 处理有效权限
}

// 获取权限分类
category := constants.GetPermissionCategory(permissionCode)
```

### 角色权限映射
```go
// 管理员角色应该具有的权限
adminPermissions := []string{
    constants.PermissionUserCreate,
    constants.PermissionUserRead,
    constants.PermissionUserUpdate,
    constants.PermissionUserDelete,
    constants.PermissionRoleCreate,
    constants.PermissionRoleRead,
    constants.PermissionRoleUpdate,
    constants.PermissionRoleDelete,
}
```

## 最佳实践

1. **避免魔法值**：始终使用常量而不是硬编码字符串
2. **统一管理**：所有角色和权限常量都在这个包中定义
3. **类型安全**：使用常量可以避免拼写错误
4. **易于维护**：修改常量只需要在一个地方修改
5. **IDE支持**：自动补全和重构支持

## 迁移指南

如果之前使用了硬编码的角色或权限字符串，请按以下步骤迁移：

1. 导入常量包
2. 将硬编码字符串替换为对应的常量
3. 使用辅助函数进行验证和检查
4. 测试确保功能正常

## 注意事项

- 常量值一旦定义，不应随意修改，以免影响现有数据
- 新增常量时，请同时更新相关的辅助函数
- 示例代码仅供参考，实际使用时请根据具体需求调整
