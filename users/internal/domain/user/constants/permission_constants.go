package constants

// 权限代码常量
const (
	// 基础操作权限
	PermissionRead   = "read"   // 读取权限
	PermissionWrite  = "write"  // 写入权限
	PermissionDelete = "delete" // 删除权限
	PermissionAdmin  = "admin"  // 管理权限

	// 系统权限
	PermissionSystemSuperAdmin = "system:superadmin" // 系统超级管理员
	PermissionSystemAdmin      = "system:admin"      // 系统管理员
	PermissionSystemUser       = "system:user"       // 系统用户

	// 用户管理权限
	PermissionUserCreate = "user:create" // 创建用户
	PermissionUserRead   = "user:read"   // 读取用户
	PermissionUserUpdate = "user:update" // 更新用户
	PermissionUserDelete = "user:delete" // 删除用户

	// 角色管理权限
	PermissionRoleCreate = "role:create" // 创建角色
	PermissionRoleRead   = "role:read"   // 读取角色
	PermissionRoleUpdate = "role:update" // 更新角色
	PermissionRoleDelete = "role:delete" // 删除角色

	// 权限管理权限
	PermissionPermissionCreate = "permission:create" // 创建权限
	PermissionPermissionRead   = "permission:read"   // 读取权限
	PermissionPermissionUpdate = "permission:update" // 更新权限
	PermissionPermissionDelete = "permission:delete" // 删除权限

	// 资源管理权限
	PermissionResourceCreate = "resource:create" // 创建资源
	PermissionResourceRead   = "resource:read"   // 读取资源
	PermissionResourceUpdate = "resource:update" // 更新资源
	PermissionResourceDelete = "resource:delete" // 删除资源
)

// 权限代码列表 - 用于验证和批量操作
var (
	// 基础权限列表
	BasicPermissions = []string{
		PermissionRead,
		PermissionWrite,
		PermissionDelete,
		PermissionAdmin,
	}

	// 系统权限列表
	SystemPermissions = []string{
		PermissionSystemSuperAdmin,
		PermissionSystemAdmin,
		PermissionSystemUser,
	}

	// 用户管理权限列表
	UserPermissions = []string{
		PermissionUserCreate,
		PermissionUserRead,
		PermissionUserUpdate,
		PermissionUserDelete,
	}

	// 角色管理权限列表
	RolePermissions = []string{
		PermissionRoleCreate,
		PermissionRoleRead,
		PermissionRoleUpdate,
		PermissionRoleDelete,
	}

	// 权限管理权限列表
	PermissionManagementPermissions = []string{
		PermissionPermissionCreate,
		PermissionPermissionRead,
		PermissionPermissionUpdate,
		PermissionPermissionDelete,
	}

	// 资源管理权限列表
	ResourcePermissions = []string{
		PermissionResourceCreate,
		PermissionResourceRead,
		PermissionResourceUpdate,
		PermissionResourceDelete,
	}

	// 所有权限列表
	AllPermissions = append(
		append(
			append(
				append(
					append(BasicPermissions, SystemPermissions...),
					UserPermissions...,
				),
				RolePermissions...,
			),
			PermissionManagementPermissions...,
		),
		ResourcePermissions...,
	)
)

// IsBasicPermission 检查是否为基础权限
func IsBasicPermission(permissionCode string) bool {
	for _, permission := range BasicPermissions {
		if permission == permissionCode {
			return true
		}
	}
	return false
}

// IsSystemPermission 检查是否为系统权限
func IsSystemPermission(permissionCode string) bool {
	for _, permission := range SystemPermissions {
		if permission == permissionCode {
			return true
		}
	}
	return false
}

// IsUserPermission 检查是否为用户管理权限
func IsUserPermission(permissionCode string) bool {
	for _, permission := range UserPermissions {
		if permission == permissionCode {
			return true
		}
	}
	return false
}

// IsRolePermission 检查是否为角色管理权限
func IsRolePermission(permissionCode string) bool {
	for _, permission := range RolePermissions {
		if permission == permissionCode {
			return true
		}
	}
	return false
}

// IsResourcePermission 检查是否为资源管理权限
func IsResourcePermission(permissionCode string) bool {
	for _, permission := range ResourcePermissions {
		if permission == permissionCode {
			return true
		}
	}
	return false
}

// IsValidPermission 检查权限代码是否有效
func IsValidPermission(permissionCode string) bool {
	for _, permission := range AllPermissions {
		if permission == permissionCode {
			return true
		}
	}
	return false
}

// GetPermissionCategory 获取权限分类
func GetPermissionCategory(permissionCode string) string {
	if IsBasicPermission(permissionCode) {
		return "basic"
	}
	if IsSystemPermission(permissionCode) {
		return "system"
	}
	if IsUserPermission(permissionCode) {
		return "user"
	}
	if IsRolePermission(permissionCode) {
		return "role"
	}
	if IsResourcePermission(permissionCode) {
		return "resource"
	}
	return "unknown"
}
