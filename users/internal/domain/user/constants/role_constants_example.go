package constants

import "fmt"

// ExampleUsage 展示如何使用角色常量的示例
func ExampleUsage() {
	// 1. 基本角色检查
	userRole := "admin"
	if IsAdminRole(userRole) {
		fmt.Printf("用户具有管理员角色: %s\n", userRole)
	}

	// 2. 系统角色检查
	if IsSystemRole(userRole) {
		fmt.Printf("用户具有系统角色: %s\n", userRole)
	}

	// 3. 角色验证
	if IsValidRole(userRole) {
		fmt.Printf("角色代码有效: %s\n", userRole)
	}

	// 4. 使用常量进行比较
	switch userRole {
	case RoleSuperAdmin:
		fmt.Println("超级管理员")
	case RoleAdmin:
		fmt.Println("管理员")
	case RoleUser:
		fmt.Println("普通用户")
	case RoleManager:
		fmt.Println("经理")
	default:
		fmt.Println("未知角色")
	}

	// 5. 批量角色检查
	userRoles := []string{RoleAdmin, RoleManager}
	for _, role := range userRoles {
		if IsAdminRole(role) {
			fmt.Printf("角色 %s 具有管理权限\n", role)
		}
	}
}

// ExamplePermissionCheck 展示在权限检查中使用常量的示例
func ExamplePermissionCheck() {
	// 检查用户是否为管理员（使用常量）
	requiredRole := RoleAdmin

	// 在权限检查服务中使用
	// isAdmin, err := permissionService.IsUserAdmin(ctx, userID, appID)
	// 或者
	// hasRole, err := permissionService.HasUserRole(ctx, userID, appID, constants.RoleAdmin)

	fmt.Printf("需要角色: %s\n", requiredRole)
}

// ExampleRoleAssignment 展示在角色分配中使用常量的示例
func ExampleRoleAssignment() {
	// 分配管理员角色
	roleToAssign := RoleAdmin

	// 在角色分配服务中使用
	// err := roleService.AssignRole(ctx, userID, roleToAssign, grantedBy)

	fmt.Printf("分配角色: %s\n", roleToAssign)
}
