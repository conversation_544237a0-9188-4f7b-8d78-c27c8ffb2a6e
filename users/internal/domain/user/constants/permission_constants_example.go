package constants

import "fmt"

// ExamplePermissionUsage 展示如何使用权限常量的示例
func ExamplePermissionUsage() {
	// 1. 基本权限检查
	userPermission := "user:read"
	if IsUserPermission(userPermission) {
		fmt.Printf("用户具有用户管理权限: %s\n", userPermission)
	}

	// 2. 系统权限检查
	if IsSystemPermission(userPermission) {
		fmt.Printf("用户具有系统权限: %s\n", userPermission)
	}

	// 3. 权限验证
	if IsValidPermission(userPermission) {
		fmt.Printf("权限代码有效: %s\n", userPermission)
	}

	// 4. 使用常量进行比较
	switch userPermission {
	case PermissionRead:
		fmt.Println("读取权限")
	case PermissionWrite:
		fmt.Println("写入权限")
	case PermissionDelete:
		fmt.Println("删除权限")
	case PermissionAdmin:
		fmt.Println("管理权限")
	case PermissionUserCreate:
		fmt.Println("创建用户权限")
	case PermissionUserRead:
		fmt.Println("读取用户权限")
	default:
		fmt.Println("未知权限")
	}

	// 5. 批量权限检查
	userPermissions := []string{PermissionUserRead, PermissionUserUpdate}
	for _, permission := range userPermissions {
		if IsUserPermission(permission) {
			fmt.Printf("权限 %s 属于用户管理类别\n", permission)
		}
	}

	// 6. 获取权限分类
	category := GetPermissionCategory(userPermission)
	fmt.Printf("权限 %s 属于 %s 类别\n", userPermission, category)
}

// ExamplePermissionCheckUsage 展示在权限检查中使用常量的示例
func ExamplePermissionCheckUsage() {
	// 检查用户是否有读取权限（使用常量）
	requiredPermission := PermissionUserRead

	// 在权限检查服务中使用
	// hasPermission, err := permissionService.CheckUserPermission(ctx, userID, appID, constants.PermissionUserRead)

	fmt.Printf("需要权限: %s\n", requiredPermission)
}

// ExamplePermissionAssignment 展示在权限分配中使用常量的示例
func ExamplePermissionAssignment() {
	// 分配用户读取权限
	permissionToAssign := PermissionUserRead

	// 在权限分配服务中使用
	// err := permissionService.AssignPermission(ctx, roleID, permissionToAssign, grantedBy)

	fmt.Printf("分配权限: %s\n", permissionToAssign)
}

// ExampleRolePermissionMapping 展示角色权限映射的示例
func ExampleRolePermissionMapping() {
	// 管理员角色应该具有的权限
	adminPermissions := []string{
		PermissionUserCreate,
		PermissionUserRead,
		PermissionUserUpdate,
		PermissionUserDelete,
		PermissionRoleCreate,
		PermissionRoleRead,
		PermissionRoleUpdate,
		PermissionRoleDelete,
	}

	fmt.Println("管理员角色权限:")
	for _, permission := range adminPermissions {
		fmt.Printf("  - %s\n", permission)
	}

	// 普通用户角色应该具有的权限
	userPermissions := []string{
		PermissionUserRead,
		PermissionResourceRead,
	}

	fmt.Println("普通用户角色权限:")
	for _, permission := range userPermissions {
		fmt.Printf("  - %s\n", permission)
	}
}
