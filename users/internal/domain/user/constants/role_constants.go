package constants

// 角色代码常量
const (
	// 系统角色
	RoleSuperAdmin = "super_admin" // 超级管理员
	RoleAdmin      = "admin"       // 管理员
	RoleUser       = "user"        // 普通用户

	// 业务角色
	RoleManager  = "manager"  // 经理
	RoleOperator = "operator" // 操作员
	RoleViewer   = "viewer"   // 查看者
	RoleGuest    = "guest"    // 访客
)

// 角色代码列表 - 用于验证和批量操作
var (
	// 系统角色列表
	SystemRoles = []string{
		RoleSuperAdmin,
		RoleAdmin,
		RoleUser,
	}

	// 业务角色列表
	BusinessRoles = []string{
		RoleManager,
		RoleOperator,
		RoleViewer,
		RoleGuest,
	}

	// 所有角色列表
	AllRoles = append(SystemRoles, BusinessRoles...)

	// 管理员角色列表（具有管理权限的角色）
	AdminRoles = []string{
		RoleSuperAdmin,
		RoleAdmin,
		RoleManager,
	}

	// 高级管理员角色列表（超级管理员和管理员）
	HighLevelAdminRoles = []string{
		RoleSuperAdmin,
		RoleAdmin,
	}
)

// IsSystemRole 检查是否为系统角色
func IsSystemRole(roleCode string) bool {
	for _, role := range SystemRoles {
		if role == roleCode {
			return true
		}
	}
	return false
}

// IsAdminRole 检查是否为管理员角色
func IsAdminRole(roleCode string) bool {
	for _, role := range AdminRoles {
		if role == roleCode {
			return true
		}
	}
	return false
}

// IsValidRole 检查角色代码是否有效
func IsValidRole(roleCode string) bool {
	for _, role := range AllRoles {
		if role == roleCode {
			return true
		}
	}
	return false
}
