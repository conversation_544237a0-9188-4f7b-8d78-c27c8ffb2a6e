package repository

import (
	"context"
	"time"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
)

// RolePermissionRepository 角色权限仓储接口
type RolePermissionRepository interface {
	// 角色分配相关
	AssignRole(ctx context.Context, userID, roleID, grantedBy int64) error
	RemoveRole(ctx context.Context, userID, roleID int64) error
	BatchAssignRole(ctx context.Context, userIDs []int64, roleID, grantedBy int64) error
	BatchRemoveRole(ctx context.Context, userIDs []int64, roleID int64) error
	GetUserRoles(ctx context.Context, userID int64) ([]entity.Role, error)
	GetRoleUsers(ctx context.Context, roleID int64) ([]entity.User, error)
	HasRole(ctx context.Context, userID, roleID int64) (bool, error)

	// 权限分配相关
	AssignPermission(ctx context.Context, roleID, permissionID, grantedBy int64) error
	RemovePermission(ctx context.Context, roleID, permissionID int64) error
	GetRolePermissions(ctx context.Context, roleID int64) ([]entity.Permission, error)
	GetPermissionRoles(ctx context.Context, permissionID int64) ([]entity.Role, error)
	HasPermission(ctx context.Context, roleID, permissionID int64) (bool, error)

	// 用户权限查询
	GetUserPermissions(ctx context.Context, userID int64) ([]entity.Permission, error)
	GetUserPermissionsByResourceID(ctx context.Context, userID int64, resourceID int64) ([]entity.Permission, error)
	GetUserPermissionsByResourceIDs(ctx context.Context, userID int64, resourceIDs []int64) ([]entity.Permission, error)
	HasUserPermission(ctx context.Context, userID int64, permissionName string) (bool, error)

	// 角色权限历史
	GetRoleAssignmentHistory(ctx context.Context, userID int64) ([]UserRole, error)
	GetPermissionAssignmentHistory(ctx context.Context, roleID int64) ([]RolePermission, error)

	// 租户隔离查询
	GetUserRolesByTenant(ctx context.Context, userID int64) ([]entity.Role, error)
	GetUserPermissionsByTenant(ctx context.Context, userID int64) ([]entity.Permission, error)

	// 基于应用ID的权限查询
	CheckUserPermissionByApp(ctx context.Context, userID int64, internalAppID int64, permissionCode string) (bool, error)
	GetUserPermissionsByResourceIDAndApp(ctx context.Context, userID int64, internalAppID int64, resourceID int64) ([]entity.Permission, error)
	GetUserPermissionsByResourceIDsAndApp(ctx context.Context, userID int64, internalAppID int64, resourceIDs []int64) ([]entity.Permission, error)
	GetUserRolesByApp(ctx context.Context, userID int64, internalAppID int64) ([]entity.Role, error)

	// ==================== 超级管理员权限查询链路（仅用于权限检查，不允许其他业务使用）====================

	// CheckUserSuperAdminRoleOnly 检查用户是否具备超级管理员角色（不考虑appId和tenantId）
	// 注意：此方法仅用于权限检查，不允许给其他正常业务使用
	CheckUserSuperAdminRoleOnly(ctx context.Context, userID int64) (bool, error)

	// 过期角色处理
	GetExpiredRoleAssignments(ctx context.Context) ([]UserRole, error)
	RemoveExpiredRoleAssignments(ctx context.Context) error
}

// UserRole 用户角色关联实体
type UserRole struct {
	ID        int64      `json:"id" gorm:"primaryKey"`
	UserID    int64      `json:"user_id" gorm:"not null;index"`
	RoleID    int64      `json:"role_id" gorm:"not null;index"`
	GrantedBy int64      `json:"granted_by" gorm:"not null;index"`
	GrantedAt time.Time  `json:"granted_at" gorm:"autoCreateTime"`
	ExpiresAt *time.Time `json:"expires_at"`
	CreatedAt time.Time  `json:"created_at" gorm:"autoCreateTime"`
}

// RolePermission 角色权限关联实体
type RolePermission struct {
	ID           int64     `json:"id" gorm:"primaryKey"`
	RoleID       int64     `json:"role_id" gorm:"not null;index"`
	PermissionID int64     `json:"permission_id" gorm:"not null;index"`
	GrantedBy    int64     `json:"granted_by" gorm:"not null;index"`
	GrantedAt    time.Time `json:"granted_at" gorm:"autoCreateTime"`
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`
}
