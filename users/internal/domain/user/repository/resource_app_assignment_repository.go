package repository

import (
	"context"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
)

// ResourceAppAssignmentRepository 资源应用关联仓储接口
type ResourceAppAssignmentRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, assignment *entity.ResourceAppAssignment) error
	FindByID(ctx context.Context, id int64) (*entity.ResourceAppAssignment, error)
	Update(ctx context.Context, assignment *entity.ResourceAppAssignment) error
	Delete(ctx context.Context, id int64) error

	// 查询操作
	FindByResource(ctx context.Context, resourceID int64) ([]entity.ResourceAppAssignment, error)
	FindByApp(ctx context.Context, appID int64) ([]entity.ResourceAppAssignment, error)
	FindByResourceAndApp(ctx context.Context, resourceID, appID int64) (*entity.ResourceAppAssignment, error)
	FindActiveByApp(ctx context.Context, appID int64) ([]entity.ResourceAppAssignment, error)
	FindActiveByResource(ctx context.Context, resourceID int64) ([]entity.ResourceAppAssignment, error)

	// 批量操作
	BatchCreate(ctx context.Context, assignments []entity.ResourceAppAssignment) error
	BatchDelete(ctx context.Context, ids []int64) error
	DeleteByResource(ctx context.Context, resourceID int64) error
	DeleteByApp(ctx context.Context, appID int64) error

	// 检查操作
	Exists(ctx context.Context, resourceID, appID int64) (bool, error)
	IsActive(ctx context.Context, resourceID, appID int64) (bool, error)
	CountByResource(ctx context.Context, resourceID int64) (int64, error)
	CountByApp(ctx context.Context, appID int64) (int64, error)

	// 应用相关的资源查询
	GetResourcesByApp(ctx context.Context, appID int64, tenantID int64) ([]entity.Resource, error)
}
