package repository

import (
	"context"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
)

// ResourceRelationRepository 资源关系仓储接口
type ResourceRelationRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, relation *entity.ResourceRelation) error
	FindByID(ctx context.Context, id int64) (*entity.ResourceRelation, error)
	Update(ctx context.Context, relation *entity.ResourceRelation) error
	Delete(ctx context.Context, id int64) error

	// 查询操作
	FindBySourceResource(ctx context.Context, sourceResourceID int64) ([]entity.ResourceRelation, error)
	FindByTargetResource(ctx context.Context, targetResourceID int64) ([]entity.ResourceRelation, error)
	FindByResources(ctx context.Context, sourceResourceID, targetResourceID int64) (*entity.ResourceRelation, error)
	
	// 新增权限相关查询
	FindBySourceAndTarget(ctx context.Context, sourceResourceID, targetResourceID, tenantID, internalAppID int64) (*entity.ResourceRelation, error)
	FindRelationsByAPI(ctx context.Context, apiResourceID, tenantID, internalAppID int64) ([]*entity.ResourceRelation, error)
	FindRelationsByPageAndAPI(ctx context.Context, pageResourceID, apiResourceID, tenantID, internalAppID int64) ([]*entity.ResourceRelation, error)
	FindActiveRelations(ctx context.Context, tenantID, internalAppID int64) ([]*entity.ResourceRelation, error)
	FindWithConditions(ctx context.Context, conditions map[string]interface{}, offset, limit int) ([]*entity.ResourceRelation, error)
	Count(ctx context.Context, conditions map[string]interface{}) (int64, error)

	// 批量操作
	BatchCreate(ctx context.Context, relations []entity.ResourceRelation) error
	BatchDelete(ctx context.Context, ids []int64) error
	DeleteBySourceResource(ctx context.Context, sourceResourceID int64) error
	DeleteByTargetResource(ctx context.Context, targetResourceID int64) error

	// 关联查询
	GetRelatedResources(ctx context.Context, resourceID int64) ([]entity.Resource, error)
	GetSourceResources(ctx context.Context, targetResourceID int64) ([]entity.Resource, error)
	GetTargetResources(ctx context.Context, sourceResourceID int64) ([]entity.Resource, error)

	// 检查操作
	Exists(ctx context.Context, sourceResourceID, targetResourceID int64) (bool, error)
	CountBySourceResource(ctx context.Context, sourceResourceID int64) (int64, error)
	CountByTargetResource(ctx context.Context, targetResourceID int64) (int64, error)

	// 应用相关的资源查询
	GetResourcesByApp(ctx context.Context, internalAppID int64, tenantID int64) ([]entity.Resource, error)
	
	// 权限管理相关
	FindByPermissionCode(ctx context.Context, permissionCode string, tenantID, internalAppID int64) ([]*entity.ResourceRelation, error)
	GetResourceRelationStats(ctx context.Context, tenantID, internalAppID int64) (map[string]int64, error)
}
