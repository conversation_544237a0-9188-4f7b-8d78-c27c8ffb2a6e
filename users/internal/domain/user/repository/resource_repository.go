package repository

import (
	"context"

	"gitee.com/heiyee/platforms/users/internal/domain/user/entity"
	"gitee.com/heiyee/platforms/users/internal/domain/user/value_object"
)

// ResourceRepository 资源仓储接口
type ResourceRepository interface {
	// 基础CRUD操作
	Create(ctx context.Context, resource *entity.Resource) error
	FindByID(ctx context.Context, id int64) (*entity.Resource, error)
	FindByIDWithoutTenantInjection(ctx context.Context, id int64) (*entity.Resource, error)
	Update(ctx context.Context, resource *entity.Resource) error
	Delete(ctx context.Context, id int64) error
	List(ctx context.Context, offset, limit int) ([]entity.Resource, int64, error)

	// 通用查询方法
	Find(ctx context.Context, params *QueryParams) (*QueryResult[*entity.Resource], error)
	Count(ctx context.Context, params *QueryParams) (int64, error)

	// 租户相关查询
	GetByTenant(ctx context.Context) ([]entity.Resource, error)
	ExistsByName(ctx context.Context, name string) (bool, error)

	// 资源类型相关查询
	GetByResourceType(ctx context.Context, resourceType value_object.ResourceType) ([]entity.Resource, error)
	GetByResourceTypes(ctx context.Context, resourceTypes []value_object.ResourceType) ([]entity.Resource, error)

	// 层级查询
	GetChildren(ctx context.Context, parentID int64) ([]entity.Resource, error)
	GetRootResources(ctx context.Context) ([]entity.Resource, error)
	GetResourceTree(ctx context.Context) ([]entity.Resource, error)
	GetByParent(ctx context.Context, parentID *int64) ([]entity.Resource, error)

	// 路径查询
	GetByPath(ctx context.Context, path string) (*entity.Resource, error)
	ExistsByPath(ctx context.Context, path string) (bool, error)

	// 批量操作
	BatchCreate(ctx context.Context, resources []entity.Resource) error
	BatchDelete(ctx context.Context, ids []int64) error

	// 软删除 (如果需要)
	SoftDelete(ctx context.Context, id int64) error
	Restore(ctx context.Context, id int64) error

	// 排序操作
	UpdateSortOrder(ctx context.Context, id int64, sortOrder int) error
	GetMaxSortOrder(ctx context.Context, parentID *int64) (int, error)

	// 权限关联查询
	GetResourcesWithPermissions(ctx context.Context) ([]entity.Resource, error)
	GetResourcePermissions(ctx context.Context, resourceID int64) ([]entity.Permission, error)

	// 公开访问相关查询
	GetPublicResources(ctx context.Context, publicLevel *value_object.PublicLevel, resourceType *value_object.ResourceType) ([]entity.Resource, error)
	GetPublicResourceByPath(ctx context.Context, path string) (*entity.Resource, error)
	IsResourcePublic(ctx context.Context, resourceID int64) (bool, error)
	UpdatePublicAccess(ctx context.Context, resourceID int64, isPublic bool, publicLevel value_object.PublicLevel) error
	BatchUpdatePublicAccess(ctx context.Context, updates []struct {
		ResourceID  int64
		IsPublic    bool
		PublicLevel value_object.PublicLevel
	}) error
	GetPublicResourcesCount(ctx context.Context) (map[string]int64, error)
	GetAnonymousResources(ctx context.Context) ([]entity.Resource, error)
	GetAuthenticatedResources(ctx context.Context) ([]entity.Resource, error)

	// 可分配性相关查询
	GetAssignableResources(ctx context.Context, resourceType *value_object.ResourceType, includeSystem bool) ([]entity.Resource, error)
	GetNonAssignableResources(ctx context.Context) ([]entity.Resource, error)
	UpdateAssignability(ctx context.Context, resourceID int64, assignable bool) error
	BatchUpdateAssignability(ctx context.Context, updates []struct {
		ResourceID int64
		Assignable bool
	}) error
	GetAssignabilityStats(ctx context.Context) (map[string]int64, error)
	IsResourceAssignable(ctx context.Context, resourceID int64) (bool, error)
	GetUserAssignableResources(ctx context.Context, resourceType *value_object.ResourceType) ([]entity.Resource, error)

	// 批量和分层查询优化方法
	GetResourcesByParentIDs(ctx context.Context, parentIDs []int64) ([]entity.Resource, error)
	GetResourceTreeByLevels(ctx context.Context, maxDepth int, resourceType *value_object.ResourceType, internalAppID, tenantId *int64) ([]entity.Resource, error)
	GetChildrenCounts(ctx context.Context, parentIDs []int64) (map[int64]int, error)
	GetResourcesByLevel(ctx context.Context, level int, parentIDs []int64) ([]entity.Resource, error)
	CheckHasDeepChildren(ctx context.Context, parentIDs []int64, maxDepth int) (map[int64]bool, error)
	
	// API资源相关查询（新增）
	FindAPIResources(ctx context.Context, tenantID, internalAppID int64) ([]*entity.Resource, error)
	FindAPIResourceByPathAndMethod(ctx context.Context, path, method string, tenantID, internalAppID int64) (*entity.Resource, error)
	GetAPIResourcesByApp(ctx context.Context, tenantID, internalAppID int64) ([]*entity.Resource, error)
}
