# 菜单权限系统架构设计

## 总体架构

菜单权限系统基于DDD（领域驱动设计）架构，采用Clean Architecture模式，确保业务逻辑与技术实现的分离。

## 架构分层

### 1. 接口层 (Interfaces Layer)

负责处理外部请求，包括HTTP API和gRPC接口。

```
internal/interfaces/
├── http/
│   ├── handlers/
│   │   └── menu_handler.go          # 菜单HTTP处理器
│   ├── middleware/
│   │   └── menu_permission.go       # 菜单权限中间件
│   └── routes/
│       └── menu_routes.go           # 菜单路由配置
└── grpc/
    └── menu_service.go              # 菜单gRPC服务
```

**职责：**
- HTTP请求路由和参数验证
- 响应格式统一处理
- 权限中间件集成
- 错误处理和日志记录

### 2. 应用层 (Application Layer)

包含应用服务和业务用例编排。

```
internal/application/
└── user/
    ├── dto/
    │   └── menu_dto.go              # 菜单数据传输对象
    └── service/
        ├── menu_application_service.go    # 菜单应用服务
        └── menu_permission_service.go     # 菜单权限服务
```

**职责：**
- 业务用例编排
- 事务边界管理
- 领域服务协调
- DTO转换

### 3. 领域层 (Domain Layer)

包含核心业务逻辑和规则。

```
internal/domain/
└── user/
    ├── entity/
    │   ├── menu.go                  # 菜单实体
    │   └── menu_permission.go       # 菜单权限实体
    ├── repository/
    │   └── menu_repository.go       # 菜单仓储接口
    ├── service/
    │   └── menu_domain_service.go   # 菜单领域服务
    └── value_object/
        └── menu_type.go             # 菜单类型值对象
```

**职责：**
- 核心业务规则
- 实体行为定义
- 领域事件
- 仓储接口定义

### 4. 基础设施层 (Infrastructure Layer)

包含技术实现和外部系统集成。

```
internal/infrastructure/
├── persistence/
│   ├── menu_repository_impl.go     # 菜单仓储实现
│   └── model/
│       └── menu.go                 # 数据库模型
├── cache/
│   └── menu_cache.go               # 菜单缓存实现
└── config/
    └── menu_config.go              # 菜单配置
```

**职责：**
- 数据持久化
- 缓存实现
- 外部服务集成
- 技术配置

## 核心设计原则

### 1. 依赖倒置原则

高层模块不依赖低层模块，都依赖抽象接口。

```go
// 应用层依赖领域层接口，而不是具体实现
type MenuApplicationService struct {
    menuRepo repository.MenuRepository  // 接口依赖
    userRepo repository.UserRepository  // 接口依赖
}
```

### 2. 单一职责原则

每个类或模块只有一个变化原因。

```go
// 菜单实体只关注菜单本身的行为
type Menu struct {
    // ... 菜单属性
}

// 菜单权限服务专门处理权限逻辑
type MenuPermissionService struct {
    // ... 权限相关逻辑
}
```

### 3. 开闭原则

对扩展开放，对修改关闭。

```go
// 通过接口扩展新的菜单类型
type MenuType interface {
    Validate() error
    GetPermissions() []Permission
}
```

## 关键组件交互

### 1. 菜单创建流程

```mermaid
sequenceDiagram
    participant Client
    participant Handler
    participant AppService
    participant MenuEntity
    participant Repository
    
    Client->>Handler: POST /api/user/menu/create
    Handler->>AppService: CreateMenu(req)
    AppService->>MenuEntity: NewMenu(params)
    MenuEntity-->>AppService: menu entity
    AppService->>Repository: Create(menu)
    Repository-->>AppService: success
    AppService-->>Handler: menu response
    Handler-->>Client: 201 Created
```

### 2. 用户菜单查询流程

```mermaid
sequenceDiagram
    participant Client
    participant Handler
    participant AppService
    participant PermissionService
    participant Repository
    participant Cache
    
    Client->>Handler: POST /api/user/menu/menu-tree
    Handler->>AppService: GetUserMenuTree(userID)
    AppService->>Cache: GetUserMenus(userID)
    
    alt Cache Hit
        Cache-->>AppService: cached menus
    else Cache Miss
        AppService->>PermissionService: GetUserPermissions(userID)
        PermissionService-->>AppService: permissions
        AppService->>Repository: GetMenusByPermissions(permissions)
        Repository-->>AppService: menus
        AppService->>Cache: SetUserMenus(userID, menus)
    end
    
    AppService-->>Handler: menu tree
    Handler-->>Client: 200 OK
```

## 权限验证机制

### 1. 菜单访问权限

```go
// 中间件验证菜单访问权限
func MenuPermissionMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        userID := getUserIDFromToken(c)
        menuCode := getMenuCodeFromPath(c.Request.URL.Path)
        
        hasPermission := menuPermissionService.HasMenuPermission(userID, menuCode)
        if !hasPermission {
            response.Forbidden(c, "无菜单访问权限")
            c.Abort()
            return
        }
        
        c.Next()
    }
}
```

### 2. 按钮权限验证

```go
// 按钮权限验证
func (s *MenuPermissionService) HasButtonPermission(userID int64, menuCode, buttonCode string) bool {
    // 1. 获取用户角色
    roles := s.roleRepo.GetUserRoles(userID)
    
    // 2. 获取角色权限
    permissions := s.permissionRepo.GetRolePermissions(roles)
    
    // 3. 检查按钮权限
    return s.checkButtonPermission(permissions, menuCode, buttonCode)
}
```

## 缓存策略

### 1. 多级缓存

```go
// L1: 内存缓存（应用级）
var memoryCache = sync.Map{}

// L2: Redis缓存（分布式）
var redisCache = redis.NewClient()

// L3: 数据库（持久化）
var database = gorm.DB
```

### 2. 缓存更新策略

- **写入时更新**：菜单变更时主动更新缓存
- **TTL过期**：设置合理的过期时间
- **版本控制**：通过版本号控制缓存一致性

## 性能优化

### 1. 菜单树构建优化

```go
// 使用映射表快速构建树形结构
func BuildMenuTree(menus []Menu) []*MenuTreeNode {
    nodeMap := make(map[int64]*MenuTreeNode)
    
    // 第一遍：创建所有节点
    for _, menu := range menus {
        nodeMap[menu.ID] = &MenuTreeNode{Menu: menu}
    }
    
    // 第二遍：建立父子关系
    var roots []*MenuTreeNode
    for _, node := range nodeMap {
        if node.Menu.ParentID != nil {
            parent := nodeMap[*node.Menu.ParentID]
            parent.Children = append(parent.Children, node)
        } else {
            roots = append(roots, node)
        }
    }
    
    return roots
}
```

### 2. 权限查询优化

```go
// 批量查询权限，减少数据库访问
func (s *MenuPermissionService) GetUserMenusWithPermissions(userID int64) ([]*Menu, error) {
    // 1. 批量获取用户所有权限
    permissions := s.permissionRepo.GetUserPermissions(userID)
    
    // 2. 根据权限批量查询菜单
    menus := s.menuRepo.GetMenusByPermissions(permissions)
    
    // 3. 构建权限映射，减少查询次数
    return s.buildMenuPermissionMap(menus, permissions), nil
}
```

## 扩展性设计

### 1. 菜单类型扩展

```go
// 通过接口支持新的菜单类型
type MenuTypeHandler interface {
    Validate(menu *Menu) error
    GetDefaultPermissions(menu *Menu) []Permission
    BuildComponent(menu *Menu) Component
}

// 注册新的菜单类型处理器
func RegisterMenuTypeHandler(menuType MenuType, handler MenuTypeHandler) {
    menuTypeHandlers[menuType] = handler
}
```

### 2. 权限策略扩展

```go
// 支持不同的权限策略
type PermissionStrategy interface {
    CheckPermission(user *User, menu *Menu) bool
}

// 可配置的权限策略
type MenuPermissionChecker struct {
    strategies []PermissionStrategy
}
```

## 监控和日志

### 1. 关键指标监控

- 菜单查询响应时间
- 权限验证成功率
- 缓存命中率
- 并发访问量

### 2. 审计日志

```go
// 菜单操作审计
type MenuAuditLog struct {
    UserID    int64     `json:"user_id"`
    Action    string    `json:"action"`
    MenuID    int64     `json:"menu_id"`
    Details   string    `json:"details"`
    IP        string    `json:"ip"`
    Timestamp time.Time `json:"timestamp"`
}
```

## 安全考虑

### 1. 数据安全

- 租户数据隔离
- 敏感信息加密
- SQL注入防护

### 2. 访问安全

- JWT令牌验证
- 权限最小化原则
- 操作审计记录

### 3. 系统安全

- 限流保护
- 异常监控
- 故障恢复 