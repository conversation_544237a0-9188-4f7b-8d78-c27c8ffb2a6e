# B端后台菜单权限系统

## 概述

本系统为B端后台管理系统提供完整的菜单权限管理功能，支持层级菜单结构、动态权限控制和按钮级别的权限验证。

## 核心特性

### 🚀 核心功能
- **层级菜单管理**：支持无限级菜单嵌套，灵活的树形结构
- **动态权限控制**：基于RBAC模型的权限验证
- **按钮级权限**：细粒度的页面按钮权限控制
- **菜单类型支持**：目录、菜单、按钮三种类型
- **多租户隔离**：完全的租户数据隔离

### 🛡️ 安全特性
- **权限继承**：子菜单自动继承父菜单权限
- **访问控制**：基于角色的菜单访问控制
- **审计日志**：完整的菜单操作审计记录
- **缓存优化**：高性能的权限缓存机制

### 🎨 用户体验
- **可视化管理**：直观的菜单树形管理界面
- **拖拽排序**：支持菜单拖拽排序
- **图标支持**：丰富的菜单图标库
- **响应式设计**：适配各种屏幕尺寸

## 文档导航

### 📋 设计文档
- [系统架构设计](./architecture.md) - 详细的系统架构和分层设计
- [实体设计](./entity-design.md) - 领域实体和值对象设计
- [权限模型设计](./permission-model.md) - RBAC权限模型和控制机制
- [数据库设计](./database-schema.md) - 完整的数据库表结构和索引设计

### 🔧 开发文档
- [API设计](./api-design.md) - 完整的HTTP API接口设计
- [前端集成指南](./frontend-integration.md) - 前端集成的详细指南

### 🚀 快速开始

1. **了解系统架构**：阅读 [系统架构设计](./architecture.md)
2. **数据库准备**：按照 [数据库设计](./database-schema.md) 创建表结构
3. **后端开发**：参考 [API设计](./api-design.md) 实现接口
4. **前端集成**：按照 [前端集成指南](./frontend-integration.md) 集成权限控制

## 系统架构

本系统基于DDD（领域驱动设计）架构，采用Clean Architecture模式：

```
┌─────────────────────────────────────────────────────────┐
│                   接口层 (Interfaces)                    │
│  ┌─────────────────┐  ┌─────────────────┐               │
│  │   HTTP API      │  │   gRPC API      │               │
│  └─────────────────┘  └─────────────────┘               │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                   应用层 (Application)                   │
│  ┌─────────────────┐  ┌─────────────────┐               │
│  │   菜单服务      │  │   权限服务      │               │
│  └─────────────────┘  └─────────────────┘               │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    领域层 (Domain)                       │
│  ┌─────────────────┐  ┌─────────────────┐               │
│  │   菜单实体      │  │   权限实体      │               │
│  │   菜单仓储      │  │   仓储接口      │               │
│  └─────────────────┘  └─────────────────┘               │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                  基础设施层 (Infrastructure)              │
│  ┌─────────────────┐  ┌─────────────────┐               │
│  │   数据库持久化   │  │   缓存实现      │               │
│  └─────────────────┘  └─────────────────┘               │
└─────────────────────────────────────────────────────────┘
```

## 快速开始

### 1. 数据库初始化
```sql
-- 执行菜单表结构创建
source docs/menu/database-schema.sql
```

### 2. 服务启动
```bash
# 启动用户服务（包含菜单权限模块）
./start.sh
```

### 3. API调用示例
```bash
# 获取用户菜单树
curl -X POST http://localhost:8084/api/user/menu/menu-tree \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{}'
```

## 文档导航

| 文档 | 说明 |
|------|------|
| [系统架构设计](./architecture.md) | 整体架构和设计原则 |
| [实体设计](./entity-design.md) | 领域实体和值对象设计 |
| [仓储设计](./repository-design.md) | 数据访问层接口设计 |
| [服务设计](./service-design.md) | 应用层服务设计 |
| [API设计](./api-design.md) | HTTP API接口设计 |
| [前端集成](./frontend-integration.md) | 前端集成指南 |
| [数据库设计](./database-schema.md) | 数据库表结构设计 |
| [权限模型](./permission-model.md) | 权限控制模型设计 |

## 版本信息

- **当前版本**：v1.0.0
- **Go版本要求**：>= 1.19
- **数据库要求**：MySQL >= 8.0
- **兼容性**：支持现有用户权限系统

## 贡献指南

1. 遵循项目的DDD架构规范
2. 确保所有公共方法都有完整的注释
3. 编写单元测试，覆盖率不低于80%
4. 提交PR前运行完整的测试套件

## 许可证

本项目采用 MIT 许可证，详情请查看 [LICENSE](../../LICENSE) 文件。 