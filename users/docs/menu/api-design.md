# 菜单权限系统API设计

## 概述

本文档详细描述菜单权限系统的HTTP API接口设计，包括菜单管理、权限控制、用户菜单查询等核心功能。

## API基础信息

### 基础路径
- **基础URL**: `/api/user/menu`
- **API版本**: v1.0
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 认证方式
- 使用JWT Bearer Token认证
- 请求头: `Authorization: Bearer <token>`

### 通用响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "meta": {
    "request_id": "req_123456789",
    "timestamp": 1703808000,
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100
    }
  }
}
```

## 菜单管理API

### 1. 创建菜单

**接口信息**
- **路径**: `POST /api/user/menu/create`
- **说明**: 创建新菜单
- **权限**: `system:menu:create`

**请求参数**
```json
{
  "parent_id": 1,
  "name": "user_management",
  "code": "system:user",
  "title": "用户管理",
  "type": "menu",
  "icon": "user",
  "path": "/system/user",
  "component": "system/user/index",
  "redirect": "",
  "sort": 1,
  "is_visible": true,
  "is_cache": false,
  "is_affix": false,
  "is_frame": false,
  "frame_url": "",
  "remark": "用户管理菜单",
  "permission_ids": [101, 102, 103]
}
```

**响应示例**
```json
{
  "code": 200,
  "message": "菜单创建成功",
  "data": {
    "id": 123,
    "parent_id": 1,
    "name": "user_management",
    "code": "system:user",
    "title": "用户管理",
    "type": "menu",
    "icon": "user",
    "path": "/system/user",
    "component": "system/user/index",
    "redirect": "",
    "sort": 1,
    "is_visible": true,
    "is_cache": false,
    "is_affix": false,
    "is_frame": false,
    "frame_url": "",
    "status": "active",
    "remark": "用户管理菜单",
    "created_at": "2024-01-01 12:00:00",
    "updated_at": "2024-01-01 12:00:00",
    "permissions": [
      {
        "id": 101,
        "name": "system:user:create",
        "display_name": "新增用户"
      }
    ]
  }
}
```

### 2. 更新菜单

**接口信息**
- **路径**: `POST /api/user/menu/update`
- **说明**: 更新菜单信息
- **权限**: `system:menu:update`

**请求参数**
```json
{
  "id": 123,
  "title": "用户管理系统",
  "icon": "user-plus",
  "sort": 2,
  "is_visible": false,
  "remark": "更新后的用户管理菜单",
  "permission_ids": [101, 102, 103, 104]
}
```

### 3. 删除菜单

**接口信息**
- **路径**: `POST /api/user/menu/delete`
- **说明**: 删除菜单（软删除）
- **权限**: `system:menu:delete`

**请求参数**
```json
{
  "id": 123
}
```

### 4. 获取菜单详情

**接口信息**
- **路径**: `POST /api/user/menu/detail`
- **说明**: 获取菜单详细信息
- **权限**: `system:menu:view`

**请求参数**
```json
{
  "id": 123
}
```

### 5. 菜单列表查询

**接口信息**
- **路径**: `POST /api/user/menu/list`
- **说明**: 分页查询菜单列表
- **权限**: `system:menu:view`

**请求参数**
```json
{
  "page": 1,
  "size": 20,
  "keyword": "用户",
  "type": "menu",
  "status": "active",
  "parent_id": 1,
  "is_visible": true,
  "order_by": "sort",
  "order_dir": "asc"
}
```

**响应示例**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 123,
      "parent_id": 1,
      "name": "user_management",
      "code": "system:user",
      "title": "用户管理",
      "type": "menu",
      "icon": "user",
      "path": "/system/user",
      "component": "system/user/index",
      "sort": 1,
      "is_visible": true,
      "status": "active",
      "created_at": "2024-01-01 12:00:00",
      "permissions": [
        {
          "id": 101,
          "name": "system:user:create",
          "display_name": "新增用户"
        }
      ]
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 50
    }
  }
}
```

### 6. 获取菜单树

**接口信息**
- **路径**: `POST /api/user/menu/tree`
- **说明**: 获取完整的菜单树结构
- **权限**: `system:menu:view`

**请求参数**
```json
{
  "status": "active",
  "is_visible": true
}
```

**响应示例**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "name": "system",
      "code": "system",
      "title": "系统管理",
      "type": "directory",
      "icon": "system",
      "sort": 1,
      "is_visible": true,
      "status": "active",
      "children": [
        {
          "id": 2,
          "parent_id": 1,
          "name": "user",
          "code": "system:user",
          "title": "用户管理",
          "type": "menu",
          "icon": "user",
          "path": "/system/user",
          "component": "system/user/index",
          "sort": 1,
          "is_visible": true,
          "status": "active",
          "children": []
        }
      ]
    }
  ]
}
```

## 用户菜单API

### 1. 获取用户菜单树

**接口信息**
- **路径**: `POST /api/user/menu/menu-tree`
- **说明**: 获取当前用户有权限的菜单树
- **权限**: 已登录用户

**请求参数**
```json
{}
```

**响应示例**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 1,
      "name": "system",
      "code": "system",
      "title": "系统管理",
      "type": "directory",
      "icon": "system",
      "sort": 1,
      "is_cache": false,
      "is_affix": false,
      "is_frame": false,
      "frame_url": "",
      "children": [
        {
          "id": 2,
          "name": "user",
          "code": "system:user",
          "title": "用户管理",
          "type": "menu",
          "icon": "user",
          "path": "/system/user",
          "component": "system/user/index",
          "sort": 1,
          "is_cache": true,
          "is_affix": false,
          "is_frame": false,
          "buttons": [
            {
              "code": "system:user:create",
              "name": "新增用户"
            },
            {
              "code": "system:user:update",
              "name": "编辑用户"
            }
          ]
        }
      ]
    }
  ]
}
```

### 2. 获取用户按钮权限

**接口信息**
- **路径**: `POST /api/user/menu/buttons`
- **说明**: 获取用户在指定菜单下的按钮权限
- **权限**: 已登录用户

**请求参数**
```json
{
  "menu_code": "system:user"
}
```

**响应示例**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "menu_code": "system:user",
    "buttons": [
      {
        "code": "system:user:create",
        "name": "新增用户"
      },
      {
        "code": "system:user:update",
        "name": "编辑用户"
      },
      {
        "code": "system:user:delete",
        "name": "删除用户"
      }
    ]
  }
}
```

### 3. 验证用户菜单权限

**接口信息**
- **路径**: `POST /api/user/menu/check-permission`
- **说明**: 验证用户是否有指定菜单或按钮权限
- **权限**: 已登录用户

**请求参数**
```json
{
  "menu_code": "system:user",
  "button_code": "system:user:create"
}
```

**响应示例**
```json
{
  "code": 200,
  "message": "验证成功",
  "data": {
    "has_permission": true,
    "permission_type": "button",
    "menu_code": "system:user",
    "button_code": "system:user:create"
  }
}
```

## 菜单权限管理API

### 1. 为菜单分配权限

**接口信息**
- **路径**: `POST /api/user/menu/assign-permissions`
- **说明**: 为菜单分配权限
- **权限**: `system:menu:permission`

**请求参数**
```json
{
  "menu_id": 123,
  "permission_ids": [101, 102, 103, 104]
}
```

### 2. 移除菜单权限

**接口信息**
- **路径**: `POST /api/user/menu/remove-permissions`
- **说明**: 移除菜单权限
- **权限**: `system:menu:permission`

**请求参数**
```json
{
  "menu_id": 123,
  "permission_ids": [102, 103]
}
```

### 3. 获取菜单权限列表

**接口信息**
- **路径**: `POST /api/user/menu/permissions`
- **说明**: 获取菜单关联的权限列表
- **权限**: `system:menu:view`

**请求参数**
```json
{
  "menu_id": 123
}
```

**响应示例**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "id": 101,
      "name": "system:user:create",
      "display_name": "新增用户",
      "description": "创建新用户",
      "resource_type": "button",
      "action": "click",
      "menu_code": "system:user",
      "button_code": "system:user:create",
      "status": "active"
    },
    {
      "id": 102,
      "name": "system:user:update",
      "display_name": "编辑用户",
      "description": "修改用户信息",
      "resource_type": "button",
      "action": "click",
      "menu_code": "system:user",
      "button_code": "system:user:update",
      "status": "active"
    }
  ]
}
```

## 批量操作API

### 1. 批量创建菜单

**接口信息**
- **路径**: `POST /api/user/menu/batch-create`
- **说明**: 批量创建菜单
- **权限**: `system:menu:create`

**请求参数**
```json
{
  "menus": [
    {
      "parent_id": 1,
      "name": "user_list",
      "code": "system:user:list",
      "title": "用户列表",
      "type": "menu",
      "icon": "user-list",
      "path": "/system/user/list",
      "component": "system/user/list/index",
      "sort": 1
    },
    {
      "parent_id": 1,
      "name": "user_detail",
      "code": "system:user:detail",
      "title": "用户详情",
      "type": "menu",
      "icon": "user-detail",
      "path": "/system/user/detail",
      "component": "system/user/detail/index",
      "sort": 2
    }
  ]
}
```

### 2. 批量更新菜单

**接口信息**
- **路径**: `POST /api/user/menu/batch-update`
- **说明**: 批量更新菜单
- **权限**: `system:menu:update`

**请求参数**
```json
{
  "updates": [
    {
      "id": 123,
      "sort": 10,
      "is_visible": false
    },
    {
      "id": 124,
      "sort": 20,
      "is_visible": true
    }
  ]
}
```

### 3. 批量删除菜单

**接口信息**
- **路径**: `POST /api/user/menu/batch-delete`
- **说明**: 批量删除菜单
- **权限**: `system:menu:delete`

**请求参数**
```json
{
  "ids": [123, 124, 125]
}
```

### 4. 批量移动菜单

**接口信息**
- **路径**: `POST /api/user/menu/batch-move`
- **说明**: 批量移动菜单到新的父菜单下
- **权限**: `system:menu:update`

**请求参数**
```json
{
  "menu_ids": [123, 124, 125],
  "target_parent_id": 2
}
```

## 菜单排序API

### 1. 更新菜单排序

**接口信息**
- **路径**: `POST /api/user/menu/update-sort`
- **说明**: 更新菜单排序
- **权限**: `system:menu:update`

**请求参数**
```json
{
  "sort_data": [
    {
      "id": 123,
      "sort": 1
    },
    {
      "id": 124,
      "sort": 2
    },
    {
      "id": 125,
      "sort": 3
    }
  ]
}
```

### 2. 菜单拖拽排序

**接口信息**
- **路径**: `POST /api/user/menu/drag-sort`
- **说明**: 处理前端拖拽排序
- **权限**: `system:menu:update`

**请求参数**
```json
{
  "moved_id": 123,
  "target_id": 124,
  "position": "before"
}
```

## 统计分析API

### 1. 获取菜单统计

**接口信息**
- **路径**: `POST /api/user/menu/stats`
- **说明**: 获取菜单统计信息
- **权限**: `system:menu:view`

**请求参数**
```json
{}
```

**响应示例**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "total_menus": 50,
    "directory_count": 5,
    "menu_count": 25,
    "button_count": 20,
    "active_menus": 45,
    "disabled_menus": 5,
    "visible_menus": 30,
    "hidden_menus": 20
  }
}
```

### 2. 获取菜单使用情况

**接口信息**
- **路径**: `POST /api/user/menu/usage`
- **说明**: 获取菜单访问使用情况
- **权限**: `system:menu:view`

**请求参数**
```json
{
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "menu_id": 123
}
```

**响应示例**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "menu_id": 123,
    "menu_code": "system:user",
    "menu_title": "用户管理",
    "access_count": 1250,
    "unique_users": 85,
    "daily_stats": [
      {
        "date": "2024-01-01",
        "access_count": 45,
        "unique_users": 12
      }
    ]
  }
}
```

## 导入导出API

### 1. 导出菜单配置

**接口信息**
- **路径**: `POST /api/user/menu/export`
- **说明**: 导出菜单配置为JSON格式
- **权限**: `system:menu:export`

**请求参数**
```json
{
  "export_type": "all",
  "include_permissions": true,
  "menu_ids": [1, 2, 3]
}
```

### 2. 导入菜单配置

**接口信息**
- **路径**: `POST /api/user/menu/import`
- **说明**: 从JSON文件导入菜单配置
- **权限**: `system:menu:import`

**请求参数**
```json
{
  "menu_data": [
    {
      "name": "imported_menu",
      "code": "system:imported",
      "title": "导入菜单",
      "type": "menu",
      "icon": "import",
      "path": "/system/imported",
      "component": "system/imported/index"
    }
  ],
  "merge_mode": "update"
}
```

## 工具API

### 1. 验证菜单编码

**接口信息**
- **路径**: `POST /api/user/menu/validate-code`
- **说明**: 验证菜单编码是否可用
- **权限**: `system:menu:create`

**请求参数**
```json
{
  "code": "system:new_menu",
  "exclude_id": 123
}
```

**响应示例**
```json
{
  "code": 200,
  "message": "验证成功",
  "data": {
    "code": "system:new_menu",
    "is_available": true,
    "message": "菜单编码可用"
  }
}
```

### 2. 获取菜单图标列表

**接口信息**
- **路径**: `POST /api/user/menu/icons`
- **说明**: 获取可用的菜单图标列表
- **权限**: `system:menu:view`

**请求参数**
```json
{
  "category": "system",
  "keyword": "user"
}
```

**响应示例**
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "categories": [
      {
        "name": "system",
        "display_name": "系统图标",
        "icons": [
          {
            "name": "user",
            "display_name": "用户",
            "unicode": "\\ue001",
            "svg": "<svg>...</svg>"
          }
        ]
      }
    ]
  }
}
```

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 200000 | 菜单不存在 | 检查菜单ID是否正确 |
| 200001 | 菜单编码已存在 | 使用不同的菜单编码 |
| 200002 | 父菜单不存在 | 检查父菜单ID是否正确 |
| 200003 | 菜单层级过深 | 减少菜单嵌套层级 |
| 200004 | 循环引用 | 检查父子菜单关系 |
| 200005 | 按钮菜单必须有父菜单 | 为按钮类型菜单设置父菜单 |
| 200006 | 目录类型不能设置路由 | 目录类型菜单不应设置path字段 |
| 200007 | 菜单权限不存在 | 检查权限ID是否正确 |
| 200008 | 无菜单操作权限 | 检查用户是否有相应权限 |

## API使用示例

### JavaScript示例

```javascript
// 获取用户菜单树
async function getUserMenuTree() {
  const response = await fetch('/api/user/menu/menu-tree', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({})
  });
  
  const result = await response.json();
  if (result.code === 200) {
    return result.data;
  } else {
    throw new Error(result.message);
  }
}

// 创建菜单
async function createMenu(menuData) {
  const response = await fetch('/api/user/menu/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(menuData)
  });
  
  return await response.json();
}

// 验证按钮权限
async function checkButtonPermission(menuCode, buttonCode) {
  const response = await fetch('/api/user/menu/check-permission', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      menu_code: menuCode,
      button_code: buttonCode
    })
  });
  
  const result = await response.json();
  return result.data?.has_permission || false;
}
```

### Go示例

```go
// MenuClient 菜单API客户端
type MenuClient struct {
    baseURL string
    token   string
    client  *http.Client
}

// GetUserMenuTree 获取用户菜单树
func (c *MenuClient) GetUserMenuTree() ([]UserMenuResponse, error) {
    req := map[string]interface{}{}
    
    var result struct {
        Code int                  `json:"code"`
        Data []UserMenuResponse  `json:"data"`
        Message string           `json:"message"`
    }
    
    err := c.post("/menu-tree", req, &result)
    if err != nil {
        return nil, err
    }
    
    if result.Code != 200 {
        return nil, fmt.Errorf("API error: %s", result.Message)
    }
    
    return result.Data, nil
}

// CreateMenu 创建菜单
func (c *MenuClient) CreateMenu(req CreateMenuRequest) (*MenuResponse, error) {
    var result struct {
        Code int           `json:"code"`
        Data *MenuResponse `json:"data"`
        Message string     `json:"message"`
    }
    
    err := c.post("/create", req, &result)
    if err != nil {
        return nil, err
    }
    
    if result.Code != 200 {
        return nil, fmt.Errorf("API error: %s", result.Message)
    }
    
    return result.Data, nil
}
```

这个API设计文档涵盖了菜单权限系统的所有核心功能，提供了完整的接口定义、参数说明、响应示例和使用示例，为前端开发和系统集成提供了详细的参考。 