# 菜单权限系统前端集成指南

## 概述

本文档详细说明如何在前端项目中集成菜单权限系统，包括菜单渲染、权限验证、路由守卫等核心功能的实现。

## 技术栈要求

### 推荐技术栈
- **前端框架**: Vue 3.x / React 18.x
- **路由**: Vue Router 4.x / React Router 6.x
- **状态管理**: Pinia / Redux Toolkit
- **UI框架**: Element Plus / Ant Design
- **HTTP客户端**: Axios
- **TypeScript**: 支持类型安全

### 浏览器兼容性
- Chrome >= 80
- Firefox >= 75
- Safari >= 13
- Edge >= 80

## 项目结构

### 推荐目录结构

```
src/
├── api/
│   ├── menu.ts              # 菜单API接口
│   ├── permission.ts        # 权限API接口
│   └── types.ts             # API类型定义
├── components/
│   ├── Menu/
│   │   ├── MenuTree.vue     # 菜单树组件
│   │   ├── MenuItem.vue     # 菜单项组件
│   │   └── index.ts         # 导出文件
│   └── Permission/
│       ├── PermissionBtn.vue    # 权限按钮组件
│       ├── PermissionRoute.vue  # 权限路由组件
│       └── index.ts
├── composables/
│   ├── useMenu.ts           # 菜单逻辑组合式函数
│   ├── usePermission.ts     # 权限逻辑组合式函数
│   └── useAuth.ts           # 认证逻辑组合式函数
├── guards/
│   ├── auth.ts              # 认证守卫
│   └── permission.ts        # 权限守卫
├── stores/
│   ├── menu.ts              # 菜单状态管理
│   ├── permission.ts        # 权限状态管理
│   └── user.ts              # 用户状态管理
├── types/
│   ├── menu.ts              # 菜单类型定义
│   ├── permission.ts        # 权限类型定义
│   └── user.ts              # 用户类型定义
└── utils/
    ├── permission.ts        # 权限工具函数
    ├── menu.ts              # 菜单工具函数
    └── storage.ts           # 本地存储工具
```

## 类型定义

### 菜单类型

```typescript
// types/menu.ts

export interface MenuType {
  DIRECTORY: 'directory'
  MENU: 'menu'
  BUTTON: 'button'
}

export interface Menu {
  id: number
  parent_id?: number
  name: string
  code: string
  title: string
  type: 'directory' | 'menu' | 'button'
  icon?: string
  path?: string
  component?: string
  redirect?: string
  sort: number
  is_visible: boolean
  is_cache: boolean
  is_affix: boolean
  is_frame: boolean
  frame_url?: string
  status: 'active' | 'disabled'
  children?: Menu[]
  buttons?: ButtonPermission[]
  created_at: string
  updated_at: string
}

export interface MenuTreeNode extends Menu {
  children: MenuTreeNode[]
  level?: number
  expanded?: boolean
}

export interface ButtonPermission {
  code: string
  name: string
  icon?: string
  type?: 'primary' | 'success' | 'warning' | 'danger'
}

export interface RouteMenu extends Menu {
  meta: {
    title: string
    icon?: string
    cache: boolean
    affix: boolean
    hidden: boolean
    roles?: string[]
    permissions?: string[]
  }
}
```

### 权限类型

```typescript
// types/permission.ts

export interface Permission {
  id: number
  name: string
  display_name: string
  description?: string
  resource_type: 'menu' | 'button' | 'api'
  action: string
  menu_code?: string
  button_code?: string
  status: 'active' | 'disabled'
}

export interface UserPermission {
  menu_permissions: string[]
  button_permissions: string[]
  api_permissions: string[]
}

export interface PermissionCheck {
  hasPermission: (permission: string) => boolean
  hasMenuPermission: (menuCode: string) => boolean
  hasButtonPermission: (menuCode: string, buttonCode: string) => boolean
  hasAnyPermission: (permissions: string[]) => boolean
  hasAllPermissions: (permissions: string[]) => boolean
}
```

## API接口封装

### 菜单API

```typescript
// api/menu.ts

import { http } from '@/utils/http'
import type { Menu, MenuTreeNode } from '@/types/menu'

export interface GetUserMenuTreeResponse {
  code: number
  message: string
  data: MenuTreeNode[]
}

export interface GetUserButtonsResponse {
  code: number
  message: string
  data: {
    menu_code: string
    buttons: ButtonPermission[]
  }
}

export interface CheckPermissionResponse {
  code: number
  message: string
  data: {
    has_permission: boolean
    permission_type: string
    menu_code: string
    button_code?: string
  }
}

// 获取用户菜单树
export const getUserMenuTree = (): Promise<MenuTreeNode[]> => {
  return http.post<GetUserMenuTreeResponse>('/api/user/menu/menu-tree', {})
    .then(response => {
      if (response.data.code === 200) {
        return response.data.data
      }
      throw new Error(response.data.message)
    })
}

// 获取用户按钮权限
export const getUserButtons = (menuCode: string): Promise<ButtonPermission[]> => {
  return http.post<GetUserButtonsResponse>('/api/user/menu/buttons', {
    menu_code: menuCode
  }).then(response => {
    if (response.data.code === 200) {
      return response.data.data.buttons
    }
    throw new Error(response.data.message)
  })
}

// 验证用户权限
export const checkPermission = (menuCode: string, buttonCode?: string): Promise<boolean> => {
  return http.post<CheckPermissionResponse>('/api/user/menu/check-permission', {
    menu_code: menuCode,
    button_code: buttonCode
  }).then(response => {
    if (response.data.code === 200) {
      return response.data.data.has_permission
    }
    return false
  })
}

// 管理员菜单管理API
export const createMenu = (menuData: Partial<Menu>): Promise<Menu> => {
  return http.post('/api/user/menu/create', menuData)
}

export const updateMenu = (menuData: Partial<Menu>): Promise<Menu> => {
  return http.post('/api/user/menu/update', menuData)
}

export const deleteMenu = (id: number): Promise<void> => {
  return http.post('/api/user/menu/delete', { id })
}

export const getMenuList = (params: any): Promise<{ data: Menu[], total: number }> => {
  return http.post('/api/user/menu/list', params)
}

export const getMenuTree = (params?: any): Promise<MenuTreeNode[]> => {
  return http.post('/api/user/menu/tree', params || {})
}
```

## 状态管理

### 菜单Store (Pinia)

```typescript
// stores/menu.ts

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { MenuTreeNode, ButtonPermission } from '@/types/menu'
import { getUserMenuTree, getUserButtons } from '@/api/menu'

export const useMenuStore = defineStore('menu', () => {
  // 状态
  const menus = ref<MenuTreeNode[]>([])
  const buttonPermissions = ref<Map<string, ButtonPermission[]>>(new Map())
  const loading = ref(false)
  const currentMenu = ref<string>('')
  
  // 计算属性
  const visibleMenus = computed(() => {
    return filterVisibleMenus(menus.value)
  })
  
  const flatMenus = computed(() => {
    return flattenMenuTree(menus.value)
  })
  
  const menuMap = computed(() => {
    const map = new Map<string, MenuTreeNode>()
    flatMenus.value.forEach(menu => {
      map.set(menu.code, menu)
    })
    return map
  })
  
  // 方法
  const loadUserMenus = async (): Promise<void> => {
    loading.value = true
    try {
      const data = await getUserMenuTree()
      menus.value = data
      // 缓存到本地存储
      localStorage.setItem('user_menus', JSON.stringify(data))
    } catch (error) {
      console.error('加载用户菜单失败:', error)
      // 尝试从缓存加载
      const cached = localStorage.getItem('user_menus')
      if (cached) {
        menus.value = JSON.parse(cached)
      }
    } finally {
      loading.value = false
    }
  }
  
  const loadButtonPermissions = async (menuCode: string): Promise<ButtonPermission[]> => {
    if (buttonPermissions.value.has(menuCode)) {
      return buttonPermissions.value.get(menuCode)!
    }
    
    try {
      const buttons = await getUserButtons(menuCode)
      buttonPermissions.value.set(menuCode, buttons)
      return buttons
    } catch (error) {
      console.error('加载按钮权限失败:', error)
      return []
    }
  }
  
  const getMenuByCode = (code: string): MenuTreeNode | undefined => {
    return menuMap.value.get(code)
  }
  
  const getMenuButtons = (menuCode: string): ButtonPermission[] => {
    return buttonPermissions.value.get(menuCode) || []
  }
  
  const hasButtonPermission = (menuCode: string, buttonCode: string): boolean => {
    const buttons = getMenuButtons(menuCode)
    return buttons.some(btn => btn.code === buttonCode)
  }
  
  const setCurrentMenu = (menuCode: string): void => {
    currentMenu.value = menuCode
  }
  
  const clearMenus = (): void => {
    menus.value = []
    buttonPermissions.value.clear()
    currentMenu.value = ''
    localStorage.removeItem('user_menus')
  }
  
  return {
    // 状态
    menus,
    buttonPermissions,
    loading,
    currentMenu,
    // 计算属性
    visibleMenus,
    flatMenus,
    menuMap,
    // 方法
    loadUserMenus,
    loadButtonPermissions,
    getMenuByCode,
    getMenuButtons,
    hasButtonPermission,
    setCurrentMenu,
    clearMenus
  }
})

// 工具函数
function filterVisibleMenus(menus: MenuTreeNode[]): MenuTreeNode[] {
  return menus
    .filter(menu => menu.is_visible)
    .map(menu => ({
      ...menu,
      children: filterVisibleMenus(menu.children || [])
    }))
}

function flattenMenuTree(menus: MenuTreeNode[]): MenuTreeNode[] {
  const result: MenuTreeNode[] = []
  
  function traverse(nodes: MenuTreeNode[]) {
    nodes.forEach(node => {
      result.push(node)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  
  traverse(menus)
  return result
}
```

### 权限Store

```typescript
// stores/permission.ts

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Permission, UserPermission } from '@/types/permission'
import { checkPermission } from '@/api/menu'

export const usePermissionStore = defineStore('permission', () => {
  // 状态
  const permissions = ref<UserPermission>({
    menu_permissions: [],
    button_permissions: [],
    api_permissions: []
  })
  
  const permissionCache = ref<Map<string, boolean>>(new Map())
  
  // 计算属性
  const hasAnyPermission = computed(() => {
    return permissions.value.menu_permissions.length > 0 ||
           permissions.value.button_permissions.length > 0 ||
           permissions.value.api_permissions.length > 0
  })
  
  // 方法
  const setPermissions = (userPermissions: UserPermission): void => {
    permissions.value = userPermissions
  }
  
  const hasPermission = (permission: string): boolean => {
    return permissions.value.menu_permissions.includes(permission) ||
           permissions.value.button_permissions.includes(permission) ||
           permissions.value.api_permissions.includes(permission)
  }
  
  const hasMenuPermission = (menuCode: string): boolean => {
    return permissions.value.menu_permissions.includes(menuCode)
  }
  
  const hasButtonPermission = async (menuCode: string, buttonCode: string): Promise<boolean> => {
    const cacheKey = `${menuCode}:${buttonCode}`
    
    if (permissionCache.value.has(cacheKey)) {
      return permissionCache.value.get(cacheKey)!
    }
    
    try {
      const hasPermission = await checkPermission(menuCode, buttonCode)
      permissionCache.value.set(cacheKey, hasPermission)
      return hasPermission
    } catch (error) {
      console.error('权限验证失败:', error)
      return false
    }
  }
  
  const hasAnyPermissions = (permissionList: string[]): boolean => {
    return permissionList.some(permission => hasPermission(permission))
  }
  
  const hasAllPermissions = (permissionList: string[]): boolean => {
    return permissionList.every(permission => hasPermission(permission))
  }
  
  const clearPermissions = (): void => {
    permissions.value = {
      menu_permissions: [],
      button_permissions: [],
      api_permissions: []
    }
    permissionCache.value.clear()
  }
  
  return {
    // 状态
    permissions,
    permissionCache,
    // 计算属性
    hasAnyPermission,
    // 方法
    setPermissions,
    hasPermission,
    hasMenuPermission,
    hasButtonPermission,
    hasAnyPermissions,
    hasAllPermissions,
    clearPermissions
  }
})
```

## 组合式函数

### 菜单逻辑

```typescript
// composables/useMenu.ts

import { ref, computed, onMounted } from 'vue'
import { useMenuStore } from '@/stores/menu'
import { useRouter, useRoute } from 'vue-router'
import type { MenuTreeNode } from '@/types/menu'

export function useMenu() {
  const menuStore = useMenuStore()
  const router = useRouter()
  const route = useRoute()
  
  const activeMenu = ref<string>('')
  const openMenus = ref<string[]>([])
  
  // 计算当前激活的菜单
  const currentActiveMenu = computed(() => {
    if (activeMenu.value) {
      return activeMenu.value
    }
    
    // 根据当前路由匹配菜单
    const currentPath = route.path
    const menu = menuStore.flatMenus.find(m => m.path === currentPath)
    return menu?.code || ''
  })
  
  // 获取面包屑导航
  const breadcrumbs = computed(() => {
    const currentMenu = menuStore.getMenuByCode(currentActiveMenu.value)
    if (!currentMenu) return []
    
    const crumbs: MenuTreeNode[] = []
    let menu: MenuTreeNode | undefined = currentMenu
    
    while (menu) {
      crumbs.unshift(menu)
      menu = menu.parent_id ? menuStore.getMenuByCode(menu.parent_id.toString()) : undefined
    }
    
    return crumbs
  })
  
  // 方法
  const loadMenus = async () => {
    await menuStore.loadUserMenus()
  }
  
  const navigateToMenu = (menuCode: string) => {
    const menu = menuStore.getMenuByCode(menuCode)
    if (menu && menu.path) {
      activeMenu.value = menuCode
      menuStore.setCurrentMenu(menuCode)
      
      if (menu.is_frame && menu.frame_url) {
        // 处理内嵌页面
        window.open(menu.frame_url, '_blank')
      } else {
        router.push(menu.path)
      }
    }
  }
  
  const toggleMenu = (menuCode: string) => {
    const index = openMenus.value.indexOf(menuCode)
    if (index > -1) {
      openMenus.value.splice(index, 1)
    } else {
      openMenus.value.push(menuCode)
    }
  }
  
  const isMenuOpen = (menuCode: string): boolean => {
    return openMenus.value.includes(menuCode)
  }
  
  const expandMenuToActive = () => {
    const currentMenu = menuStore.getMenuByCode(currentActiveMenu.value)
    if (currentMenu) {
      let parentId = currentMenu.parent_id
      while (parentId) {
        const parentMenu = menuStore.getMenuByCode(parentId.toString())
        if (parentMenu && !openMenus.value.includes(parentMenu.code)) {
          openMenus.value.push(parentMenu.code)
        }
        parentId = parentMenu?.parent_id
      }
    }
  }
  
  onMounted(() => {
    loadMenus()
    expandMenuToActive()
  })
  
  return {
    // 状态
    activeMenu,
    openMenus,
    // 计算属性
    currentActiveMenu,
    breadcrumbs,
    menus: computed(() => menuStore.visibleMenus),
    loading: computed(() => menuStore.loading),
    // 方法
    loadMenus,
    navigateToMenu,
    toggleMenu,
    isMenuOpen,
    expandMenuToActive
  }
}
```

### 权限逻辑

```typescript
// composables/usePermission.ts

import { computed } from 'vue'
import { usePermissionStore } from '@/stores/permission'
import { useMenuStore } from '@/stores/menu'

export function usePermission() {
  const permissionStore = usePermissionStore()
  const menuStore = useMenuStore()
  
  // 权限检查方法
  const hasPermission = (permission: string): boolean => {
    return permissionStore.hasPermission(permission)
  }
  
  const hasMenuPermission = (menuCode: string): boolean => {
    return permissionStore.hasMenuPermission(menuCode)
  }
  
  const hasButtonPermission = async (menuCode: string, buttonCode: string): Promise<boolean> => {
    // 首先检查本地权限
    if (menuStore.hasButtonPermission(menuCode, buttonCode)) {
      return true
    }
    
    // 然后检查服务端权限
    return await permissionStore.hasButtonPermission(menuCode, buttonCode)
  }
  
  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissionStore.hasAnyPermissions(permissions)
  }
  
  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissionStore.hasAllPermissions(permissions)
  }
  
  // 权限指令函数
  const checkPermissionDirective = (permission: string | string[]): boolean => {
    if (Array.isArray(permission)) {
      return hasAnyPermission(permission)
    }
    return hasPermission(permission)
  }
  
  return {
    hasPermission,
    hasMenuPermission,
    hasButtonPermission,
    hasAnyPermission,
    hasAllPermissions,
    checkPermissionDirective
  }
}
```

## 组件实现

### 菜单树组件

```vue
<!-- components/Menu/MenuTree.vue -->

<template>
  <div class="menu-tree">
    <el-menu
      :default-active="currentActiveMenu"
      :default-openeds="openMenus"
      :collapse="isCollapse"
      background-color="#304156"
      text-color="#bfcbd9"
      active-text-color="#409EFF"
      @select="handleMenuSelect"
      @open="handleMenuOpen"
      @close="handleMenuClose"
    >
      <menu-item
        v-for="menu in menus"
        :key="menu.code"
        :menu="menu"
        :base-path="menu.path || ''"
      />
    </el-menu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import MenuItem from './MenuItem.vue'
import { useMenu } from '@/composables/useMenu'

interface Props {
  isCollapse?: boolean
}

defineProps<Props>()

const {
  menus,
  currentActiveMenu,
  openMenus,
  navigateToMenu
} = useMenu()

const handleMenuSelect = (menuCode: string) => {
  navigateToMenu(menuCode)
}

const handleMenuOpen = (menuCode: string) => {
  if (!openMenus.value.includes(menuCode)) {
    openMenus.value.push(menuCode)
  }
}

const handleMenuClose = (menuCode: string) => {
  const index = openMenus.value.indexOf(menuCode)
  if (index > -1) {
    openMenus.value.splice(index, 1)
  }
}
</script>
```

### 菜单项组件

```vue
<!-- components/Menu/MenuItem.vue -->

<template>
  <template v-if="hasPermission">
    <!-- 有子菜单的目录或菜单 -->
    <el-sub-menu
      v-if="hasChildren"
      :index="menu.code"
      :popper-append-to-body="true"
    >
      <template #title>
        <el-icon v-if="menu.icon">
          <component :is="menu.icon" />
        </el-icon>
        <span>{{ menu.title }}</span>
      </template>
      
      <menu-item
        v-for="child in menu.children"
        :key="child.code"
        :menu="child"
        :base-path="resolvePath(child.path)"
      />
    </el-sub-menu>
    
    <!-- 叶子菜单项 -->
    <el-menu-item
      v-else
      :index="menu.code"
      :route="{ path: menu.path }"
    >
      <el-icon v-if="menu.icon">
        <component :is="menu.icon" />
      </el-icon>
      <template #title>
        <span>{{ menu.title }}</span>
      </template>
    </el-menu-item>
  </template>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { usePermission } from '@/composables/usePermission'
import type { MenuTreeNode } from '@/types/menu'

interface Props {
  menu: MenuTreeNode
  basePath: string
}

const props = defineProps<Props>()

const { hasMenuPermission } = usePermission()

// 检查是否有菜单权限
const hasPermission = computed(() => {
  return hasMenuPermission(props.menu.code)
})

// 检查是否有子菜单
const hasChildren = computed(() => {
  return props.menu.children && 
         props.menu.children.length > 0 && 
         props.menu.children.some(child => hasMenuPermission(child.code))
})

// 解析路径
const resolvePath = (path?: string): string => {
  if (!path) return ''
  
  if (path.startsWith('/')) {
    return path
  }
  
  return `${props.basePath}/${path}`.replace(/\/+/g, '/')
}
</script>
```

### 权限按钮组件

```vue
<!-- components/Permission/PermissionBtn.vue -->

<template>
  <el-button
    v-if="hasPermission"
    v-bind="$attrs"
    @click="handleClick"
  >
    <slot />
  </el-button>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { usePermission } from '@/composables/usePermission'
import { useMenuStore } from '@/stores/menu'

interface Props {
  permission?: string
  menuCode?: string
  buttonCode?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const { hasPermission: checkPermission, hasButtonPermission } = usePermission()
const menuStore = useMenuStore()

const hasPermission = ref(false)

// 检查权限
const checkButtonPermission = async () => {
  if (props.permission) {
    // 直接权限检查
    hasPermission.value = checkPermission(props.permission)
  } else if (props.menuCode && props.buttonCode) {
    // 菜单+按钮权限检查
    hasPermission.value = await hasButtonPermission(props.menuCode, props.buttonCode)
  } else if (props.buttonCode) {
    // 使用当前菜单+按钮权限检查
    const currentMenu = menuStore.currentMenu
    if (currentMenu) {
      hasPermission.value = await hasButtonPermission(currentMenu, props.buttonCode)
    }
  } else {
    hasPermission.value = true
  }
}

const handleClick = (event: MouseEvent) => {
  if (hasPermission.value) {
    emit('click', event)
  }
}

onMounted(() => {
  checkButtonPermission()
})
</script>
```

## 路由守卫

### 权限守卫

```typescript
// guards/permission.ts

import type { Router } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useMenuStore } from '@/stores/menu'
import { usePermissionStore } from '@/stores/permission'

export function setupPermissionGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const userStore = useUserStore()
    const menuStore = useMenuStore()
    const permissionStore = usePermissionStore()
    
    // 检查是否已登录
    if (!userStore.isLoggedIn) {
      if (to.path !== '/login') {
        next('/login')
        return
      }
      next()
      return
    }
    
    // 如果已经加载过菜单，直接进行权限检查
    if (menuStore.menus.length > 0) {
      const hasPermission = checkRoutePermission(to, menuStore, permissionStore)
      if (hasPermission) {
        next()
      } else {
        next('/403')
      }
      return
    }
    
    // 首次加载，需要获取用户菜单
    try {
      await menuStore.loadUserMenus()
      
      // 生成动态路由
      const routes = generateRoutesFromMenus(menuStore.visibleMenus)
      routes.forEach(route => {
        router.addRoute(route)
      })
      
      // 检查权限
      const hasPermission = checkRoutePermission(to, menuStore, permissionStore)
      if (hasPermission) {
        // 如果当前路由是根路径，重定向到第一个菜单
        if (to.path === '/') {
          const firstMenu = findFirstAccessibleMenu(menuStore.visibleMenus)
          if (firstMenu && firstMenu.path) {
            next(firstMenu.path)
            return
          }
        }
        next()
      } else {
        next('/403')
      }
    } catch (error) {
      console.error('加载用户菜单失败:', error)
      next('/error')
    }
  })
}

// 检查路由权限
function checkRoutePermission(
  route: any,
  menuStore: any,
  permissionStore: any
): boolean {
  // 公共路由直接通过
  const publicRoutes = ['/login', '/403', '/404', '/error']
  if (publicRoutes.includes(route.path)) {
    return true
  }
  
  // 查找对应的菜单
  const menu = menuStore.flatMenus.find((m: any) => m.path === route.path)
  if (!menu) {
    return false
  }
  
  // 检查菜单权限
  return permissionStore.hasMenuPermission(menu.code)
}

// 从菜单生成路由
function generateRoutesFromMenus(menus: any[]): any[] {
  const routes: any[] = []
  
  function traverse(menuList: any[], parentPath = '') {
    menuList.forEach(menu => {
      if (menu.type === 'menu' && menu.path && menu.component) {
        const route = {
          path: menu.path,
          name: menu.code,
          component: () => import(`@/views/${menu.component}.vue`),
          meta: {
            title: menu.title,
            icon: menu.icon,
            cache: menu.is_cache,
            affix: menu.is_affix,
            hidden: !menu.is_visible,
            menuCode: menu.code
          }
        }
        routes.push(route)
      }
      
      if (menu.children && menu.children.length > 0) {
        traverse(menu.children, menu.path || parentPath)
      }
    })
  }
  
  traverse(menus)
  return routes
}

// 查找第一个可访问的菜单
function findFirstAccessibleMenu(menus: any[]): any | null {
  for (const menu of menus) {
    if (menu.type === 'menu' && menu.path && menu.is_visible) {
      return menu
    }
    
    if (menu.children && menu.children.length > 0) {
      const firstChild = findFirstAccessibleMenu(menu.children)
      if (firstChild) {
        return firstChild
      }
    }
  }
  
  return null
}
```

## 权限指令

### Vue权限指令

```typescript
// directives/permission.ts

import type { App, Directive } from 'vue'
import { usePermission } from '@/composables/usePermission'

const permissionDirective: Directive = {
  mounted(el, binding) {
    const { hasPermission, hasAnyPermission } = usePermission()
    const { value } = binding
    
    if (value) {
      let hasAuth = false
      
      if (Array.isArray(value)) {
        hasAuth = hasAnyPermission(value)
      } else {
        hasAuth = hasPermission(value)
      }
      
      if (!hasAuth) {
        el.style.display = 'none'
        // 或者移除元素
        // el.parentNode?.removeChild(el)
      }
    }
  },
  
  updated(el, binding) {
    const { hasPermission, hasAnyPermission } = usePermission()
    const { value } = binding
    
    if (value) {
      let hasAuth = false
      
      if (Array.isArray(value)) {
        hasAuth = hasAnyPermission(value)
      } else {
        hasAuth = hasPermission(value)
      }
      
      el.style.display = hasAuth ? '' : 'none'
    }
  }
}

export function setupPermissionDirective(app: App) {
  app.directive('permission', permissionDirective)
}

// 使用示例：
// <el-button v-permission="'system:user:create'">新增用户</el-button>
// <el-button v-permission="['system:user:create', 'system:user:update']">操作</el-button>
```

## 使用示例

### 页面中使用菜单权限

```vue
<!-- views/system/user/index.vue -->

<template>
  <div class="user-management">
    <!-- 面包屑导航 -->
    <el-breadcrumb separator="/">
      <el-breadcrumb-item
        v-for="crumb in breadcrumbs"
        :key="crumb.code"
        :to="crumb.path"
      >
        {{ crumb.title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
    
    <!-- 操作按钮 -->
    <div class="toolbar">
      <permission-btn
        menu-code="system:user"
        button-code="system:user:create"
        type="primary"
        @click="handleCreate"
      >
        新增用户
      </permission-btn>
      
      <permission-btn
        menu-code="system:user"
        button-code="system:user:export"
        type="success"
        @click="handleExport"
      >
        导出数据
      </permission-btn>
      
      <!-- 使用指令方式 -->
      <el-button
        v-permission="'system:user:import'"
        type="warning"
        @click="handleImport"
      >
        导入数据
      </el-button>
    </div>
    
    <!-- 数据表格 -->
    <el-table :data="users">
      <el-table-column prop="name" label="用户名" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <permission-btn
            menu-code="system:user"
            button-code="system:user:update"
            type="primary"
            size="small"
            @click="handleEdit(row)"
          >
            编辑
          </permission-btn>
          
          <permission-btn
            menu-code="system:user"
            button-code="system:user:delete"
            type="danger"
            size="small"
            @click="handleDelete(row)"
          >
            删除
          </permission-btn>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useMenu } from '@/composables/useMenu'
import PermissionBtn from '@/components/Permission/PermissionBtn.vue'

const { breadcrumbs } = useMenu()

const users = ref([])

// 业务方法
const handleCreate = () => {
  console.log('创建用户')
}

const handleEdit = (user: any) => {
  console.log('编辑用户:', user)
}

const handleDelete = (user: any) => {
  console.log('删除用户:', user)
}

const handleExport = () => {
  console.log('导出数据')
}

const handleImport = () => {
  console.log('导入数据')
}

onMounted(() => {
  // 加载用户数据
})
</script>
```

### 在路由配置中使用权限

```typescript
// router/index.ts

import { createRouter, createWebHistory } from 'vue-router'
import { setupPermissionGuard } from '@/guards/permission'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue')
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403.vue')
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue')
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    children: [
      // 动态路由将在这里添加
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 设置权限守卫
setupPermissionGuard(router)

export default router
```

## 最佳实践

### 1. 权限缓存策略

- 菜单数据本地缓存30分钟
- 按钮权限缓存5分钟
- 用户退出时清空所有缓存

### 2. 错误处理

- 网络异常时使用本地缓存
- 权限验证失败时友好提示
- 提供重试机制

### 3. 性能优化

- 懒加载页面组件
- 权限检查防抖处理
- 使用虚拟滚动处理大量菜单

### 4. 安全考虑

- 前端权限仅用于UI控制
- 重要操作必须后端验证
- 敏感数据不在前端缓存

这个前端集成指南提供了完整的菜单权限系统前端实现方案，包括类型定义、状态管理、组件开发、路由配置等所有关键环节。 