# 租户安全增强 - 默认严格模式实现

## 修改概述

为了解决原有设计中租户ID和应用ID安全验证的漏洞，重新设计了验证策略，**默认采用严格模式**，只有在特殊场景下才允许参数传递。

## 核心设计变更

### 1. 接口重新设计

**原接口 (已删除):**
```go
type StrictContextAware interface {
    IsStrictContextMode() bool
    SetStrictContextMode(strict bool)
}
```

**新接口:**
```go
// ParameterModeAware 参数模式感知接口，用于标记是否允许使用参数传递的租户和应用ID（特殊场景）
type ParameterModeAware interface {
    IsAllowParameterMode() bool
    SetAllowParameterMode(allow bool)
}
```

### 2. BaseModel 变更

**字段修改:**
```go
type BaseModel struct {
    TenantID              int64     `gorm:"not null;index" json:"tenant_id"`
    InternalAppID         int64     `gorm:"not null;index;comment:应用ID，bigint类型提升性能" json:"internal_app_id"`
    AllowParameterMode    bool      `gorm:"-" json:"-"` // 不持久化，默认false表示严格使用context
    CreatedAt             time.Time `gorm:"autoCreateTime" json:"created_at"`
    UpdatedAt             time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}
```

**新增方法:**
```go
// IsAllowParameterMode 是否允许参数模式（默认false，严格使用context）
func (b *BaseModel) IsAllowParameterMode() bool {
    return b.AllowParameterMode
}

// SetAllowParameterMode 设置参数模式
func (b *BaseModel) SetAllowParameterMode(allow bool) {
    b.AllowParameterMode = allow
}
```

### 3. 核心验证逻辑重写

**新的验证逻辑 (AutoFillTenantAndApp):**
```go
// 默认采用严格模式：强制使用context中的值，只有明确标记AllowParameterMode的才允许参数传递
func AutoFillTenantAndApp(tx *gorm.DB) {
    // ... context 获取逻辑 ...
    
    // 处理租户ID
    if tenantAware, ok := model.(TenantAware); ok {
        if parameterAware, isParameterAware := model.(ParameterModeAware); isParameterAware && parameterAware.IsAllowParameterMode() {
            // 参数模式：允许使用参数传递的值，只在值为0时才从context填充
            if tenantAware.GetTenantID() == 0 && tenantID > 0 {
                tenantAware.SetTenantID(tenantID)
            }
        } else {
            // 严格模式（默认）：强制使用context中的值，忽略参数传递的值
            if tenantID > 0 {
                tenantAware.SetTenantID(tenantID)
            }
        }
    }
    
    // 应用ID处理逻辑类似...
}
```

### 4. 辅助方法

**参数模式管理:**
```go
// NewModelWithParameterMode 创建启用参数模式的模型实例（特殊场景使用）
func NewModelWithParameterMode[T interface {
    ParameterModeAware
    *BaseModel
}]() T

// AllowParameterMode 为已有模型启用参数模式（特殊场景使用）
func AllowParameterMode(model ParameterModeAware)

// DisallowParameterMode 为已有模型禁用参数模式（恢复严格模式）
func DisallowParameterMode(model ParameterModeAware)
```

## 安全优势

### 1. 默认安全
- **严格模式为默认行为**：新代码默认强制使用context中的租户和应用ID
- **防止绕过**：参数传递的值会被context值覆盖，防止恶意绕过

### 2. 特殊场景支持
- **明确标记**：需要参数传递的场景必须明确调用 `AllowParameterMode()`
- **可追踪**：参数模式的使用可通过代码审查轻松识别

### 3. 向后兼容
- **渐进式迁移**：现有代码默认采用严格模式，无需修改
- **可控升级**：团队可以逐步审查和标记特殊场景

## 需要使用参数模式的场景

基于代码审查，以下场景可能需要启用参数模式：

### 1. 系统级操作
- **ID生成器服务**: 在 `simple_id_generator.go` 中使用 `common.SystemTenantID`
- **系统初始化**: 创建系统级数据时
- **跨租户管理**: 超级管理员操作

### 2. 数据迁移
- **批量导入**: 明确指定目标租户
- **数据修复**: 系统维护操作

### 3. 第三方集成
- **API调用**: 某些外部API可能需要指定租户

## 使用示例

### 普通业务代码（默认严格模式）
```go
func CreateUser(ctx context.Context, req *CreateUserRequest) error {
    user := &UserModel{
        Username: req.Username,
        Email:    req.Email,
        // TenantID 和 InternalAppID 将自动从 context 填充，忽略 req 中的值
    }
    return userRepo.Create(ctx, user)
}
```

### 特殊场景（参数模式）
```go
func InitSystemData(ctx context.Context) error {
    systemUser := &UserModel{
        Username:      "system",
        Email:         "<EMAIL>",
        TenantID:      common.SystemTenantID,      // 这个值会被保留
        InternalAppID: common.SystemInternalAppID, // 这个值会被保留
    }
    
    // 明确标记允许参数模式
    AllowParameterMode(systemUser)
    
    return userRepo.Create(ctx, systemUser)
}
```

## 迁移建议

1. **立即应用**: 所有新代码默认获得严格模式保护
2. **审查现有代码**: 识别确实需要参数传递的场景
3. **明确标记**: 为特殊场景添加 `AllowParameterMode()` 调用
4. **安全审查**: 定期审查参数模式的使用情况

## 文件修改清单

- `/Users/<USER>/personal/platforms/users/internal/infrastructure/persistence/model/base_model.go`
  - 添加 `ParameterModeAware` 接口
  - 修改 `BaseModel` 结构体
  - 重写 `AutoFillTenantAndApp` 函数
  - 添加辅助方法

## 待处理任务

- [ ] 识别并修改需要参数模式的具体场景
- [ ] 更新相关文档和使用指南
- [ ] 添加单元测试验证安全性
- [ ] 代码审查确保无遗漏场景