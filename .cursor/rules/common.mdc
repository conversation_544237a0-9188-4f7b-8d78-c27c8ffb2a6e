---
alwaysApply: true
---

### 错误码约定
- 所有涉及错误码定义或者使用的操作，遵守 ERROR_CODES_DESIGN.md中的规范

### 前端web代码约定
- 所有模块的web前端代码必须放在根目录 frontend目录
- 前端项目需要使用errorCodes.ts文件保存所有的errorCode
- 前端项目所有的API必须使用src/constants/api.ts维护地址
- 前端项目不显式传递tenantId,该信息从已登录用户信息中可获取，禁止从request中获取tenantId
### 通用能力使用
- 新增功能时，优先考虑使用pkg模块的公共能力，参考pkg/readme.md文件

### GORM 数据库层代码规范
- 数据库访问、ORM、仓储实现等请遵循《gorm-db-layer-guidelines.mdc》中的规范，详见 .cursor/rules/gorm-db-layer-guidelines.mdc
### 日志
- 使用pkg模块的日志组件
### 其他
- tenantCode和tenantId是系统内部信息，禁止对外输出，可以打印日志
- 如果代码行上有 DO NOT MODIFY 的注释，则不允许修改，必须修改需要确认
- 统一使用go-playground/validator/v10来做验证
- tenantCode和tenantId是系统内部信息，禁止对外输出，可以打印日志
- 如果代码行上有 DO NOT MODIFY 的注释，则不允许修改，必须修改需要确认
- 统一使用go-playground/validator/v10来做验证
- appId 和 tenantId从usercontext 一定可以获取到，业务代码无需做重复判空
### 前端开发 Cursor 规则
- 前端项目开发请遵循 .cursor/rules 目录下的各项规则文件，包括 API 集成、认证、组件模式、开发流程、路由、TypeScript 规范等。每项规则详见对应 .mdc 文件说明。
### 参数验证
- 非应用层使用 userErrors.NewParameterValidationFailedError
- 应用层 使用每个模块的 error_handler.go来处理 如果没有，参考users模块的error_handler.go实现
- 不允许将error直接抛出给外部，例如 emailErrors.NewEmailError(emailErrors.CodeTemplateInvalidContent, err.Error())
### 错误码约定
- 所有涉及错误码定义或者使用的操作，遵守 ERROR_CODES_DESIGN.md中的规范

### 前端web代码约定
- 所有模块的web前端代码必须放在根目录 frontend目录
- 前端项目需要使用errorCodes.ts文件保存所有的errorCode
- 前端项目所有的API必须使用src/constants/api.ts维护地址
- 前端项目不显式传递tenantId,该信息从已登录用户信息中可获取，禁止从request中获取tenantId
### 通用能力使用
- 新增功能时，优先考虑使用pkg模块的公共能力，参考pkg/readme.md文件

### GORM 数据库层代码规范
- 数据库访问、ORM、仓储实现等请遵循《gorm-db-layer-guidelines.mdc》中的规范，详见 .cursor/rules/gorm-db-layer-guidelines.mdc
### 日志
- 使用pkg模块的日志组件
### 其他
- tenantCode和tenantId是系统内部信息，禁止对外输出，可以打印日志
- 如果代码行上有 DO NOT MODIFY 的注释，则不允许修改，必须修改需要确认
- 统一使用go-playground/validator/v10来做验证
- tenantCode和tenantId是系统内部信息，禁止对外输出，可以打印日志
- 如果代码行上有 DO NOT MODIFY 的注释，则不允许修改，必须修改需要确认
- 统一使用go-playground/validator/v10来做验证
- appId 和 tenantId从usercontext 一定可以获取到，业务代码无需做重复判空
- 禁止直接连接数据库进行修改字段或者数据
### 前端开发 Cursor 规则
- 前端项目开发请遵循 .cursor/rules 目录下的各项规则文件，包括 API 集成、认证、组件模式、开发流程、路由、TypeScript 规范等。每项规则详见对应 .mdc 文件说明。
### 参数验证
- 非应用层使用 userErrors.NewParameterValidationFailedError
- 应用层 使用每个模块的 error_handler.go来处理 如果没有，参考users模块的error_handler.go实现
- 不允许将error直接抛出给外部，例如 emailErrors.NewEmailError(emailErrors.CodeTemplateInvalidContent, err.Error())
